# ================================
# Crypto Trader Project .gitignore
# ================================

# =====================================
# Python Backend (FastAPI/Django)
# =====================================

# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# Virtual environments
venv/
env/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/

# =====================================
# Node.js / Frontend (Vue.js + Vite)
# =====================================

# Dependencies
node_modules/
jspm_packages/

# Production builds
dist/
build/

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage
.grunt

# Bower dependency directory
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons
build/Release

# Dependency directories
node_modules/
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Storybook build outputs
.out
.storybook-out
storybook-static

# Vite
.vite

# =====================================
# Environment Variables
# =====================================

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.staging

# Keep example file
!.env.example

# =====================================
# Database & Migrations
# =====================================

# SQLite databases
*.sqlite3
*.sqlite
*.db

# Alembic migration versions (keep structure, ignore generated files)
backend/alembic/versions/*
!backend/alembic/versions/.gitkeep

# Database dumps
*.sql
*.dump

# =====================================
# Development Tools & IDEs
# =====================================

# Visual Studio Code
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
!.vscode/*.code-snippets

# Local History for Visual Studio Code
.history/

# Built Visual Studio Code Extensions
*.vsix

# JetBrains IDEs
.idea/
*.swp
*.swo
*~

# Sublime Text
*.tmlanguage.cache
*.tmPreferences.cache
*.stTheme.cache
*.sublime-workspace
*.sublime-project

# Vim
[._]*.s[a-v][a-z]
[._]*.sw[a-p]
[._]s[a-rt-v][a-z]
[._]ss[a-gi-z]
[._]sw[a-p]

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# =====================================
# Operating System Files
# =====================================

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.stackdump
[Dd]esktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# =====================================
# Logs & Temporary Files
# =====================================

# Log files
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Temporary files
*.tmp
*.temp
tmp/
temp/

# =====================================
# Docker & Deployment
# =====================================

# Docker volumes and runtime data
.docker/
docker-compose.override.yml

# Keep Docker configuration files
!Dockerfile
!docker-compose.yml
!.dockerignore

# =====================================
# Testing & Coverage
# =====================================

# 统一测试临时文件目录
temp/
!temp/.gitkeep
!temp/*/.gitkeep

# 旧的分散测试文件（保留以防遗漏）
test-results/
test-reports/
.pytest_cache/
.coverage
htmlcov/
.tox/
.nox/

# Jest
coverage/

# Playwright specific
playwright-report/
blob-report/
playwright/.cache/

# =====================================
# Documentation
# =====================================

# Sphinx documentation
docs/_build/
docs/build/

# =====================================
# Security & Secrets
# =====================================

# Private keys
*.pem
*.key
*.p12
*.pfx

# Certificate files
*.crt
*.cer

# =====================================
# Backup Files
# =====================================

# Backup files
*.bak
*.backup
*.old
*.orig

# =====================================
# Project Specific
# =====================================

# Trading data cache
cache/
data/cache/

# Model files (if large)
*.model
*.pkl
*.pickle

# API keys and secrets (additional safety)
*secret*
*key*
*token*
!*example*
!*template*
!*sample*