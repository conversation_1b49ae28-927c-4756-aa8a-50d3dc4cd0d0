module.exports = {
  env: {
    browser: true,
    es2021: true,
    node: true
  },
  extends: [
    'eslint:recommended',
    'plugin:vue/vue3-essential'
  ],
  parserOptions: {
    ecmaVersion: 'latest',
    sourceType: 'module'
  },
  plugins: [
    'vue'
  ],
  rules: {
    // 允许console.log用于调试
    'no-console': 'warn',
    // 允许未使用的变量（测试中常见）
    'no-unused-vars': 'warn',
    // Vue特定规则
    'vue/multi-word-component-names': 'off'
  },
  ignorePatterns: [
    'node_modules/',
    'dist/',
    'coverage/',
    'test-results/',
    'playwright-report/'
  ]
}
