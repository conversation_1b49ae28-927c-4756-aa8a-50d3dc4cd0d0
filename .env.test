# 测试环境配置 (Test Environment Configuration)

# 应用基础配置
APP_NAME=AI Crypto Trading Agent
APP_VERSION=1.0.0
DEBUG=true
LOG_LEVEL=DEBUG

# 数据库配置 - 使用PostgreSQL
DATABASE_URL=postgresql+asyncpg://crypto_trader:test_password_123@localhost:5432/crypto_trader_test
DB_POOL_SIZE=5
DB_MAX_OVERFLOW=10
DB_POOL_TIMEOUT=30
DB_POOL_RECYCLE=1800
DB_ECHO=false

# PostgreSQL Docker配置
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres
POSTGRES_DB=crypto_trader_test

# 安全配置
APP_SECRET_KEY=test_secret_key_for_pytest_min_32_chars
JWT_SECRET_KEY=test_jwt_secret_key_for_pytest_min_32
JWT_ALGORITHM=HS256
JWT_EXPIRE_MINUTES=60
PASSWORD_HASH_ROUNDS=4

# API配置
API_HOST=0.0.0.0
API_PORT=8000
API_PREFIX=/api/v1
API_URL=http://localhost:8000
CORS_ORIGINS=http://localhost:5173,http://localhost:3000,http://localhost:8080

# LLM配置
OPENAI_API_KEY=sk-mock-testing
ANTHROPIC_API_KEY=mock-key
DEFAULT_LLM_PROVIDER=openai
DEFAULT_LLM_MODEL=gpt-4
LLM_MAX_RETRIES=3
LLM_REQUEST_TIMEOUT=60
LLM_MAX_TOKENS=4096

# Discord配置
DISCORD_TOKEN=mock_discord_token_for_testing

# Discord过滤配置 - 测试环境使用简化配置
DISCORD_FILTER_CONFIG={"enabled":false,"server_ids":[],"channel_ids":[],"author_ids":[],"allowed_message_types":["text"]}

# 默认用户ID - 测试环境
DEFAULT_USER_ID=708db973-fc1f-4be0-9c52-a9736a10372c

# 交易配置
DEFAULT_EXCHANGE=binance
SIMULATION_MODE=true
PRICE_CHECK_INTERVAL=60

# ========================================
# 前端配置 (Frontend Configuration)
# ========================================

# 前端测试环境配置 (Frontend Test Configuration)
NODE_ENV=test

# 前端URL配置 (Frontend URL Configuration)
FRONTEND_URL=http://localhost:5173
VITE_API_BASE_URL=http://localhost:8000
VITE_WS_URL=ws://localhost:8000

# 前端应用配置 (Frontend Application Configuration)
VITE_APP_TITLE="AI Crypto Trading Agent (Test)"
VITE_APP_VERSION=0.1.0
VITE_DEBUG=true

# 测试用户凭据 (Test User Credentials)
TEST_USERNAME=demo
TEST_PASSWORD=password123

# 测试超时配置 (Test Timeout Configuration)
TEST_TIMEOUT=60000
API_TIMEOUT=30000
NAVIGATION_TIMEOUT=10000

# 测试浏览器配置 (Test Browser Configuration)
HEADLESS=true
SLOW_MO=0

# 测试数据配置 (Test Data Configuration)
USE_MOCK_DATA=false
CLEANUP_TEST_DATA=true

# 调试选项 (Debugging Options)
DEBUG_TESTS=false
SAVE_SCREENSHOTS=true
SAVE_VIDEOS=false
SAVE_TRACES=false
