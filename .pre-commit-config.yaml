repos:
  # 基础代码质量检查
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.5.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-yaml
      - id: check-added-large-files
      - id: check-case-conflict
      - id: check-merge-conflict
      - id: debug-statements
      - id: check-json
      - id: pretty-format-json
        args: ['--autofix']

  # Python代码格式化
  - repo: https://github.com/psf/black
    rev: 23.11.0
    hooks:
      - id: black
        language_version: python3.11
        files: ^(backend/app/|backend/tests/).*\.py$

  # 导入排序
  - repo: https://github.com/pycqa/isort
    rev: 5.12.0
    hooks:
      - id: isort
        args: ["--profile", "black"]
        files: ^(backend/app/|backend/tests/).*\.py$

  # 代码风格检查
  - repo: https://github.com/pycqa/flake8
    rev: 6.1.0
    hooks:
      - id: flake8
        files: ^(backend/app/|backend/tests/).*\.py$
        args: [
          "--max-line-length=88",
          "--extend-ignore=E203,W503",
          "--exclude=backend/alembic/versions/"
        ]

  # 类型检查
  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.7.1
    hooks:
      - id: mypy
        files: ^backend/app/.*\.py$
        additional_dependencies: [
          types-redis,
          types-requests,
          types-python-dateutil,
          sqlalchemy[mypy]
        ]
        args: [
          "--ignore-missing-imports",
          "--disallow-untyped-defs",
          "--no-implicit-optional"
        ]

  # 安全检查
  - repo: https://github.com/pycqa/bandit
    rev: 1.7.5
    hooks:
      - id: bandit
        files: ^backend/app/.*\.py$
        args: ["-c", "backend/bandit.yaml"]

  # 依赖安全检查
  - repo: https://github.com/Lucas-C/pre-commit-hooks-safety
    rev: v1.3.2
    hooks:
      - id: python-safety-dependencies-check
        files: ^backend/requirements\.txt$

  # 文档检查
  - repo: https://github.com/pycqa/pydocstyle
    rev: 6.3.0
    hooks:
      - id: pydocstyle
        files: ^backend/app/.*\.py$
        args: [
          "--convention=google",
          "--add-ignore=D100,D101,D102,D103,D104,D105,D106,D107"
        ]

  # Jupyter Notebook清理
  - repo: https://github.com/nbQA-dev/nbQA
    rev: 1.7.1
    hooks:
      - id: nbqa-black
        files: \.ipynb$
      - id: nbqa-isort
        files: \.ipynb$

  # YAML格式检查
  - repo: https://github.com/adrienverge/yamllint
    rev: v1.33.0
    hooks:
      - id: yamllint
        args: [
          "-d",
          "{extends: default, rules: {line-length: {max: 120}, truthy: disable}}"
        ]

  # 自定义测试钩子
  - repo: local
    hooks:
      # 快速单元测试
      - id: pytest-unit
        name: 运行单元测试
        entry: bash -c 'cd backend && python -m pytest tests/unit/ -m unit --tb=short -q'
        language: system
        files: ^backend/(app/|tests/unit/).*\.py$
        pass_filenames: false

      # 属性测试
      - id: pytest-property
        name: 运行属性测试
        entry: bash -c 'cd backend && python -m pytest tests/unit/test_schemas_properties.py -m property --tb=short -q'
        language: system
        files: ^backend/app/core/schemas\.py$
        pass_filenames: false

      # 检查测试覆盖率
      - id: coverage-check
        name: 检查测试覆盖率
        entry: bash -c 'cd backend && python -m pytest tests/unit/ --cov=app --cov-fail-under=70 --cov-report=term-missing:skip-covered -q'
        language: system
        files: ^backend/app/.*\.py$
        pass_filenames: false

      # 检查数据库迁移
      - id: alembic-check
        name: 检查数据库迁移
        entry: bash -c 'cd backend && alembic check'
        language: system
        files: ^backend/(alembic/|app/core/models\.py)
        pass_filenames: false

      # 检查环境变量
      - id: env-check
        name: 检查环境变量配置
        entry: bash -c 'cd backend && python -c "from app.core.config import settings; print(\"配置检查通过\")"'
        language: system
        files: ^backend/app/core/config\.py$
        pass_filenames: false

# 全局配置
default_stages: [commit]
fail_fast: false
minimum_pre_commit_version: 3.0.0

# CI配置
ci:
  autofix_commit_msg: |
    [pre-commit.ci] auto fixes from pre-commit hooks

    for more information, see https://pre-commit.ci
  autofix_prs: true
  autoupdate_branch: ''
  autoupdate_commit_msg: '[pre-commit.ci] pre-commit autoupdate'
  autoupdate_schedule: weekly
  skip: [pytest-unit, pytest-property, coverage-check, alembic-check, env-check]
  submodules: false
