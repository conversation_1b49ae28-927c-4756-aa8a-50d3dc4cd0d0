#!/usr/bin/env python3
"""
统一测试脚本 - 极简版
遵循《0. 项目规范.md》的极简设计原则

核心功能：
- 纯委托模式：直接调用专门的测试脚本
- 简化的命令行接口
- 保持向后兼容性

版本: 4.0 (极简版)
创建日期: 2025-07-22
"""

import argparse
import subprocess
import sys
from pathlib import Path


def run_delegated_command(cmd: list, verbose: bool = False, timeout: int = None) -> bool:
    """运行委托命令 - 直接调用专门的测试脚本"""
    if verbose:
        print(f"[信息] 执行命令: {' '.join(cmd)}")

    try:
        result = subprocess.run(cmd, cwd=Path(__file__).parent.parent, timeout=timeout)
        return result.returncode == 0
    except subprocess.TimeoutExpired:
        print(f"\n⚠️  命令执行超时 ({timeout}秒)")
        return False
    except KeyboardInterrupt:
        print("\n⚠️  测试被用户中断")
        return False
    except Exception as e:
        print(f"❌ 命令执行异常: {e}")
        return False


def check_port(port: int) -> bool:
    """检查端口是否被占用"""
    import socket
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
            sock.settimeout(1)
            result = sock.connect_ex(('localhost', port))
            return result == 0  # 0表示连接成功，端口被占用
    except Exception:
        return False

def check_environment() -> bool:
    """快速环境检查"""
    print("=== 测试环境检查 ===")

    # 检查Python版本
    python_version = sys.version_info
    if python_version >= (3, 11):
        print(f"✅ Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    else:
        print(f"❌ Python版本过低: {python_version.major}.{python_version.minor}.{python_version.micro} (需要 >= 3.11)")
        return False

    # 检查项目结构
    project_root = Path(__file__).parent.parent
    critical_dirs = [
        (project_root / "backend", "backend"),
        (project_root / "frontend", "frontend"),
        (project_root / "scripts", "scripts")
    ]

    for dir_path, name in critical_dirs:
        if dir_path.exists():
            print(f"✅ 目录存在: {name}")
        else:
            print(f"❌ 目录缺失: {name}")
            return False

    print("✅ 环境检查通过")
    return True


def show_services_status() -> None:
    """显示服务运行状态"""
    print("=== 服务运行状态 ===")

    services = [
        ('PostgreSQL', 5432),
        ('后端服务', 8000),
        ('前端服务', 5173)
    ]

    for service_name, port in services:
        if check_port(port):
            print(f"✅ {service_name} 运行正常 (端口 {port})")
        else:
            print(f"⚠️  {service_name} 未运行 (端口 {port})")

    print()
    print("📋 测试依赖说明:")
    print("   🔴 后端测试: 强依赖PostgreSQL数据库 (端口5432)")
    print("   🟡 前端API测试: 依赖后端服务(端口8000+5173)")
    print("   🟡 前端E2E测试: 依赖前后端服务 (端口8000+5173)")
    print("   🟢 前端单元测试: 无外部依赖")


def show_test_commands_list() -> None:
    """显示完整的测试命令清单"""
    print("=== 完整测试命令清单 ===")
    print("以下是所有可用的测试命令，可以独立执行：")
    print()

    print("📋 环境准备:")
    print("  Step 0.1: 检查环境依赖")
    print("    python scripts/test.py --check-env")
    print("  Step 0.2: 重启测试服务")
    print("    docker-compose -f docker-compose.test.yml up -d")
    print("  Step 0.3: 检查服务状态")
    print("    python scripts/test.py --status")
    print()

    print("🧪 核心测试命令:")
    print("  Step 1: 后端单元测试")
    print("    python scripts/test/backend/run_backend_tests.py unit")
    print("    依赖: PostgreSQL数据库 (端口5432)")
    print()

    print("  Step 2: 后端集成测试")
    print("    python scripts/test/backend/run_backend_tests.py integration")
    print("    依赖: PostgreSQL数据库 (端口5432)")
    print()

    print("  Step 3: 前端单元测试")
    print("    node scripts/test/frontend/run_unit_tests.js unit")
    print("    依赖: 无外部依赖")
    print()

    print("  Step 3.1: 前端组件测试")
    print("    node scripts/test/frontend/run_unit_tests.js components")
    print("    依赖: 无外部依赖")
    print()

    print("  Step 4: 前端API测试")
    print("    python scripts/test/frontend/run_api_tests.py all")
    print("    依赖: 后端服务(端口8000+5173)")
    print()

    print("  Step 5: 前端E2E测试 (并行模式)")
    print("    python scripts/test/frontend/run_e2e_tests.py all")
    print("    或直接使用: cd frontend && npm run test:parallel:standard")
    print("    依赖: 前后端服务 (端口8000+5173)")
    print()

    print("🔍 代码质量检查:")
    print("  Step 5.1: 代码格式检查 (强制性)")
    print("    /opt/anaconda3/envs/crypto-trader/bin/python -m black --check app/")
    print("  Step 5.2: 类型检查 (强制性)")
    print("    /opt/anaconda3/envs/crypto-trader/bin/python -m mypy app/ --ignore-missing-imports --explicit-package-bases")
    print()

    print("🔧 数据管理:")
    print("  数据清理:")
    print("    python scripts/db/run_sqls.py cleanup_data.sql")
    print("  Mock数据:")
    print("    python scripts/db/run_sqls.py mock_data.sql")
    print()

    print("🧹 环境清理:")
    print("  Step 6: 停止测试服务")
    print("    docker-compose -f docker-compose.test.yml down")
    print("  Step 6.1: 停止开发环境 (如果使用)")
    print("    docker-compose down")
    print("  Step 6.2: 强制清理所有容器和网络")
    print("    docker-compose down --volumes --remove-orphans")
    print()

    print("🚀 快捷执行:")
    print("  执行所有测试:")
    print("    python scripts/test.py --execute-all")
    print("  单独执行某类测试:")
    print("    python scripts/test.py --pytest unit")
    print("    python scripts/test.py --vitest unit")
    print("    python scripts/test.py --playwright api")
    print()

    print("📊 测试覆盖率要求:")
    print("  - 后端单元测试: ≥80%")
    print("  - 后端集成测试: ≥80%")
    print("  - 前端单元测试: ≥80%")
    print("  - E2E测试: 核心流程覆盖")
    print()

    print("⚡ 快速故障排查:")
    print("  环境检查:")
    print("    python scripts/test.py --check-env             # 检查Python版本和项目结构")
    print("    python scripts/test.py --status                # 检查服务运行状态")
    print("    docker-compose ps                              # 检查容器状态")
    print("  数据问题:")
    print("    python scripts/db/run_sqls.py cleanup_data.sql # 清理脏数据")
    print("    python scripts/db/run_sqls.py mock_data.sql    # 重新生成测试数据")
    print("  服务重启:")
    print("    docker-compose restart postgres                # 重启数据库")
    print("    docker-compose restart backend                 # 重启后端服务")
    print()

    print("🔧 原生命令执行 (快速调试):")
    print("  后端测试 (在backend目录下):")
    print("    cd backend && python -m pytest tests/unit/ -v")
    print("    cd backend && python -m pytest tests/integration/ -v")
    print("    cd backend && python -m pytest tests/unit/test_specific_file.py::test_function -v")
    print("  前端测试 (在frontend目录下):")
    print("    cd frontend && npm test -- --run unit")
    print("    cd frontend && npm test -- --run components")
    print("    cd frontend && npx vitest run src/tests/unit/specific-test.test.js")
    print("  E2E测试 (在frontend目录下，推荐并行模式):")
    print("    cd frontend && npm run test:parallel:fast                    # 快速并行测试")
    print("    cd frontend && npm run test:parallel:debug                   # 调试模式")
    print("    cd frontend && npx playwright test tests/e2e/specific-test.spec.js --config playwright-parallel.config.js")
    print()

    print("💡 提示:")
    print("  - 使用 --execute-all 实际执行所有测试")
    print("  - 使用 --status 检查服务依赖状态")
    print("  - 测试前确保相关服务已启动")
    print("  - 快速调试时可直接使用原生命令执行单个测试文件")
    print("  - 服务启动后，优先使用原生命令进行单个测试文件的快速验证")




def create_argument_parser() -> argparse.ArgumentParser:
    """创建命令行参数解析器 - 极简版本，保持向后兼容性"""
    parser = argparse.ArgumentParser(
        description="统一测试脚本 - 极简版",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
🎯 测试类型说明:
  pytest类型:
    unit        - 单元测试 (依赖PostgreSQL)
    integration - 集成测试 (依赖PostgreSQL)
    e2e         - 端到端测试 (依赖PostgreSQL)
    all         - 所有pytest测试 (依赖PostgreSQL)

  playwright类型:
    api         - API接口测试 (依赖后端服务)
    e2e         - 端到端UI测试 (依赖前后端服务)

  vitest类型:
    unit        - 前端单元测试 (无外部依赖)
    components  - 前端组件测试 (无外部依赖)
    validate    - 验证前端测试环境

  其他类型:
    unified-api - 统一API测试 (依赖后端服务)
    all         - 显示测试命令清单 (不执行)
    execute-all - 执行所有测试 (依赖PostgreSQL+前后端服务)

📋 依赖要求:
  🔴 PostgreSQL数据库 (端口5432) - 后端测试必需
  🟡 后端服务(端口8000+5173) - API/E2E测试需要
  🟡 前端服务 (端口5173) - E2E测试需要

🚀 使用示例:
  python scripts/test.py --status          # 检查服务状态
  python scripts/test.py --pytest unit     # 后端单元测试
  python scripts/test.py --vitest unit     # 前端单元测试
  python scripts/test.py --playwright api  # 前端API测试
  python scripts/test.py --all            # 显示测试命令清单
  python scripts/test.py --execute-all    # 实际执行所有测试
        """
    )

    # 测试类型选项 - 保持向后兼容性
    test_group = parser.add_argument_group('测试类型选项')
    test_group.add_argument("--pytest", choices=["unit", "integration", "e2e", "all"],
                           help="运行pytest测试")
    test_group.add_argument("--playwright", choices=["api", "e2e"],
                           help="运行Playwright测试")
    test_group.add_argument("--vitest", choices=["unit", "components", "validate"],
                           help="运行Vitest前端测试")
    test_group.add_argument("--unified-api", action="store_true",
                           help="运行统一的API测试")
    test_group.add_argument("--all", action="store_true",
                           help="显示完整测试命令清单（不执行）")
    test_group.add_argument("--list", action="store_true",
                           help="显示完整测试命令清单（--all的别名）")
    test_group.add_argument("--execute-all", action="store_true",
                           help="实际执行所有测试")

    # 环境检查选项
    env_group = parser.add_argument_group('环境检查选项')
    env_group.add_argument("--check-env", action="store_true",
                          help="检查测试环境")
    env_group.add_argument("--status", action="store_true",
                          help="显示服务运行状态")



    # 输出选项
    output_group = parser.add_argument_group('输出选项')
    output_group.add_argument("--verbose", "-v", action="store_true",
                             help="显示详细输出")

    return parser


def main():
    """主函数 - 极简版本，纯委托模式"""
    parser = create_argument_parser()
    args = parser.parse_args()

    print("=== 统一测试脚本 - 极简版 ===")
    print("纯委托模式：直接调用专门的测试脚本")
    print()

    success = True

    try:
        # 处理环境检查命令
        if args.check_env:
            success = check_environment()

        # 处理服务状态命令
        elif args.status:
            show_services_status()

        # 处理测试命令 - 纯委托模式
        elif args.pytest:
            cmd = ['python', 'scripts/test/backend/run_backend_tests.py', args.pytest]
            if args.verbose:
                cmd.append('--verbose')
            success = run_delegated_command(cmd, args.verbose)

        elif args.playwright == "api":
            cmd = ['python', 'scripts/test/frontend/run_api_tests.py', 'all']
            success = run_delegated_command(cmd, args.verbose)

        elif args.playwright == "e2e":
            cmd = ['python', 'scripts/test/frontend/run_e2e_tests.py', 'all']
            success = run_delegated_command(cmd, args.verbose)

        elif args.vitest:
            cmd = ['node', 'scripts/test/frontend/run_unit_tests.js', args.vitest]
            success = run_delegated_command(cmd, args.verbose)

        elif args.unified_api:
            cmd = ['python', 'scripts/test/frontend/run_api_tests.py', 'all']
            success = run_delegated_command(cmd, args.verbose)

        elif args.all or args.list:
            # 显示测试命令清单，不执行
            show_test_commands_list()

        elif args.execute_all:
            print("🚀 运行完整测试套件...")

            # 重启测试服务
            print("  重启测试服务")
            restart_cmd = ['docker-compose', '-f', 'docker-compose.test.yml', 'up', '-d']
            if not run_delegated_command(restart_cmd, args.verbose):
                print("❌ Docker 服务重启失败")
                sys.exit(1)

            test_commands = [
                (['python', 'scripts/test/backend/run_backend_tests.py', 'unit'], 600),
                (['python', 'scripts/test/backend/run_backend_tests.py', 'integration'], 600),
                (['node', 'scripts/test/frontend/run_unit_tests.js', 'unit'], 120),
                (['node', 'scripts/test/frontend/run_unit_tests.js', 'components'], 120),
                (['python', 'scripts/test/frontend/run_api_tests.py', 'all'], 300),
                (['python', 'scripts/test/frontend/run_e2e_tests.py', 'all'], 600)
            ]

            success_count = 0
            for i, (cmd, timeout) in enumerate(test_commands, 1):
                print(f"\n📊 进度: {i}/{len(test_commands)} - 执行: {' '.join(cmd)} (超时: {timeout}秒)")
                if run_delegated_command(cmd, args.verbose, timeout):
                    success_count += 1
                    print(f"✅ 测试 {i} 通过")
                else:
                    print(f"❌ 测试 {i} 失败")

            success = success_count == len(test_commands)
            print(f"\n📊 测试结果汇总: {success_count}/{len(test_commands)} 通过")

            # 测试结束后停止Docker服务
            print("  停止docker服务")
            stop_cmd = ['docker-compose', '-f', 'docker-compose.test.yml', 'down']
            run_delegated_command(stop_cmd, args.verbose)

        else:
            # 没有指定任何操作，显示帮助
            parser.print_help()
            return

    except KeyboardInterrupt:
        print("\n⚠️  操作被用户中断")
        success = False
    except Exception as e:
        print(f"❌ 执行过程中发生异常: {e}")
        if args.verbose:
            import traceback
            print(f"详细错误信息:\n{traceback.format_exc()}")
        success = False

    # 输出最终结果
    if success:
        print("\n✅ 操作完成")
    else:
        print("\n❌ 操作失败")

    sys.exit(0 if success else 1)


if __name__ == '__main__':
    # 检查Python版本
    if sys.version_info < (3, 11):
        print("❌ 错误: 需要Python 3.11或更高版本")
        sys.exit(1)

    main()
