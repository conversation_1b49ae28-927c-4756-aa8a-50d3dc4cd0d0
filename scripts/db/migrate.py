#!/usr/bin/env python3
"""
数据库迁移脚本 - 简化重构版
根据《0. 项目规范.md》创建，遵循测试脚本简化原则

核心功能：
- 数据库迁移管理
- 数据库重置和种子数据
- 数据库状态检查
- 支持Alembic迁移

简化改进：
- 移除过度抽象层和复杂的类结构
- 内联配置管理，移除复杂的Config类依赖
- 直接使用subprocess，减少封装
- 专注于核心功能，避免过度工程化

版本: 3.0 (简化重构版)
创建日期: 2025-07-22
"""

import argparse
import asyncio
import subprocess
import sys
from pathlib import Path
from typing import List, Tuple


class SimpleDatabaseManager:
    """简化的数据库管理器 - 专注于核心功能"""
    
    def __init__(self, verbose: bool = False):
        """
        初始化数据库管理器
        
        Args:
            verbose: 是否显示详细输出
        """
        self.verbose = verbose
        self.project_root = Path(__file__).parent.parent.parent
        self.backend_dir = self.project_root / "backend"
        self.alembic_dir = self.backend_dir / "alembic"
        
        # 内联配置 - 避免复杂的配置类
        self.db_config = {
            'database_url': 'postgresql://postgres:password@localhost:5432/crypto_trader_test',
            'alembic_ini': self.backend_dir / "alembic.ini",
            'migrations_dir': self.alembic_dir / "versions"
        }
        
        # 验证项目结构
        if not self.backend_dir.exists():
            self.log_error(f"后端目录不存在: {self.backend_dir}")
            sys.exit(1)
    
    def log_info(self, message: str) -> None:
        """记录信息日志"""
        print(f"[信息] {message}")
    
    def log_success(self, message: str) -> None:
        """记录成功日志"""
        print(f"\033[92m[成功] {message}\033[0m")
    
    def log_error(self, message: str) -> None:
        """记录错误日志"""
        print(f"\033[91m[错误] {message}\033[0m")
    
    def log_warning(self, message: str) -> None:
        """记录警告日志"""
        print(f"\033[93m[警告] {message}\033[0m")
    
    def print_header(self, title: str) -> None:
        """打印标题头部"""
        print(f"\n{'='*60}")
        print(f"\033[95m{title}\033[0m")
        print(f"{'='*60}")
    
    async def run_command(self, cmd: List[str], timeout: int = 300) -> Tuple[bool, str]:
        """
        运行命令 - 简化版本，直接使用subprocess
        
        Args:
            cmd: 命令列表
            timeout: 超时时间
            
        Returns:
            (是否成功, 输出内容)
        """
        if self.verbose:
            self.log_info(f"执行命令: {' '.join(cmd)}")
        
        try:
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                cwd=self.backend_dir
            )
            
            try:
                stdout, _ = await asyncio.wait_for(process.communicate(), timeout=timeout)
                output = stdout.decode('utf-8', errors='ignore').strip()
                success = process.returncode == 0
                
                if not success and self.verbose:
                    self.log_error(f"命令执行失败 (返回码: {process.returncode})")
                    if output:
                        print(output)
                
                return success, output
                
            except asyncio.TimeoutError:
                process.kill()
                await process.wait()
                self.log_error(f"命令执行超时 ({timeout}秒)")
                return False, ""
                
        except Exception as e:
            self.log_error(f"命令执行异常: {e}")
            return False, ""
    
    async def check_alembic_setup(self) -> bool:
        """检查Alembic配置"""
        self.log_info("🔍 检查Alembic配置...")
        
        # 检查alembic.ini文件
        if not self.db_config['alembic_ini'].exists():
            self.log_error(f"Alembic配置文件不存在: {self.db_config['alembic_ini']}")
            return False
        
        # 检查migrations目录
        if not self.alembic_dir.exists():
            self.log_error(f"Alembic目录不存在: {self.alembic_dir}")
            return False
        
        self.log_success("✅ Alembic配置检查通过")
        return True
    
    async def run_migrations(self) -> bool:
        """运行数据库迁移"""
        self.log_info("🔄 运行数据库迁移...")
        
        # 检查Alembic配置
        if not await self.check_alembic_setup():
            return False
        
        # 运行迁移
        success, output = await self.run_command(["alembic", "upgrade", "head"])
        
        if success:
            self.log_success("✅ 数据库迁移完成")
            if self.verbose and output:
                print(output)
            return True
        else:
            self.log_error("❌ 数据库迁移失败")
            if output:
                print(output)
            return False
    
    async def check_migration_status(self) -> bool:
        """检查迁移状态"""
        self.log_info("📊 检查迁移状态...")
        
        # 检查Alembic配置
        if not await self.check_alembic_setup():
            return False
        
        # 获取当前版本
        success, output = await self.run_command(["alembic", "current"])
        
        if success:
            self.log_success("✅ 迁移状态检查完成")
            if output:
                self.log_info(f"当前版本: {output}")
            else:
                self.log_warning("⚠️ 数据库可能未初始化")
            return True
        else:
            self.log_error("❌ 迁移状态检查失败")
            if output:
                print(output)
            return False
    
    async def reset_database(self) -> bool:
        """重置数据库"""
        self.log_info("🔄 重置数据库...")
        
        # 检查Alembic配置
        if not await self.check_alembic_setup():
            return False
        
        # 降级到base
        self.log_info("降级到base版本...")
        success, output = await self.run_command(["alembic", "downgrade", "base"])
        
        if not success:
            self.log_error("❌ 数据库降级失败")
            if output:
                print(output)
            return False
        
        # 升级到head
        self.log_info("升级到最新版本...")
        success, output = await self.run_command(["alembic", "upgrade", "head"])
        
        if success:
            self.log_success("✅ 数据库重置完成")
            return True
        else:
            self.log_error("❌ 数据库重置失败")
            if output:
                print(output)
            return False
    
    async def seed_database(self) -> bool:
        """添加种子数据"""
        self.log_info("🌱 添加种子数据...")
        
        # 检查种子数据脚本
        seed_script = self.project_root / "scripts" / "db" / "run_sqls.py"
        if not seed_script.exists():
            self.log_error(f"种子数据脚本不存在: {seed_script}")
            return False
        
        # 运行种子数据脚本
        cmd = ["python", str(seed_script), "--all"]
        success, output = await self.run_command(cmd)
        
        if success:
            self.log_success("✅ 种子数据添加完成")
            return True
        else:
            self.log_error("❌ 种子数据添加失败")
            if output:
                print(output)
            return False
    
    async def show_migration_history(self) -> bool:
        """显示迁移历史"""
        self.log_info("📜 显示迁移历史...")
        
        # 检查Alembic配置
        if not await self.check_alembic_setup():
            return False
        
        # 获取迁移历史
        success, output = await self.run_command(["alembic", "history", "--verbose"])
        
        if success:
            self.log_success("✅ 迁移历史获取完成")
            if output:
                print(output)
            else:
                self.log_info("没有迁移历史")
            return True
        else:
            self.log_error("❌ 迁移历史获取失败")
            if output:
                print(output)
            return False


def create_argument_parser() -> argparse.ArgumentParser:
    """创建命令行参数解析器"""
    parser = argparse.ArgumentParser(
        description="数据库迁移脚本 - 简化重构版",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
🗄️ 数据库管理命令:
  migrate    - 运行数据库迁移到最新版本
  reset      - 重置数据库（删除所有数据并重新迁移）
  seed       - 添加种子数据
  status     - 检查当前数据库迁移状态
  history    - 显示迁移历史

🚀 使用示例:
  python scripts/db/migrate.py migrate         # 运行迁移
  python scripts/db/migrate.py reset           # 重置数据库
  python scripts/db/migrate.py seed            # 添加种子数据
  python scripts/db/migrate.py status          # 检查状态
  python scripts/db/migrate.py history         # 显示历史
  python scripts/db/migrate.py --verbose       # 显示详细日志
        """
    )

    # 位置参数
    parser.add_argument("command", choices=["migrate", "reset", "seed", "status", "history"],
                       help="要执行的数据库操作")

    # 可选参数
    parser.add_argument("--verbose", "-v", action="store_true", help="显示详细日志")

    return parser


async def main():
    """主函数"""
    parser = create_argument_parser()
    args = parser.parse_args()

    # 创建数据库管理器
    manager = SimpleDatabaseManager(verbose=args.verbose)

    success = True

    try:
        manager.print_header("🗄️ 数据库迁移脚本 - 简化重构版")

        # 根据命令执行相应操作
        if args.command == "migrate":
            success = await manager.run_migrations()
        elif args.command == "reset":
            success = await manager.reset_database()
        elif args.command == "seed":
            success = await manager.seed_database()
        elif args.command == "status":
            success = await manager.check_migration_status()
        elif args.command == "history":
            success = await manager.show_migration_history()

    except KeyboardInterrupt:
        manager.log_warning("操作被用户中断")
        success = False
    except Exception as e:
        manager.log_error(f"执行过程中发生异常: {e}")
        if args.verbose:
            import traceback
            manager.log_error(f"详细错误信息:\n{traceback.format_exc()}")
        success = False

    # 输出最终结果
    if success:
        manager.log_success("\n✅ 操作完成")
    else:
        manager.log_error("\n❌ 操作失败")

    sys.exit(0 if success else 1)


if __name__ == '__main__':
    # 检查Python版本
    if sys.version_info < (3, 11):
        print("❌ 错误: 需要Python 3.11或更高版本")
        sys.exit(1)

    # 运行主函数
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⚠️  操作被用户中断")
        sys.exit(1)
