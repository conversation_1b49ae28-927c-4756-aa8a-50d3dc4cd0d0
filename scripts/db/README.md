# 数据库管理工具

本目录包含所有数据库相关的管理工具和SQL脚本，采用SQL DDL脚本管理模式。

## 🔄 架构重新设计亮点

- **🗃️ SQL DDL脚本管理**: 从Python业务逻辑转换为纯SQL DDL管理
- **🔧 统一执行器**: 提供统一的SQL脚本执行器，便于维护和版本控制
- **📊 事务管理**: 完整的事务管理、错误处理、执行日志
- **🛡️ 干运行模式**: 支持干运行模式验证SQL内容
- **🇨🇳 中文优先**: 所有注释、日志、错误消息使用中文

## 📁 目录结构

```
scripts/db/
├── README.md                # 本文档
├── run_sqls.py             # 🔧 SQL脚本执行器 + PostgreSQL健康检查 (整合版)
├── migrate.py              # 🔄 数据库迁移脚本 (重构版)
├── init.sql                # 数据库初始化脚本
├── create_user.sql         # ✅ 创建用户账户SQL脚本（演示+管理员+测试用户）
├── mock_data.sql           # 🆕 生成测试数据SQL脚本
└── cleanup_data.sql        # 🆕 清理测试数据SQL脚本 (增强版)
```

## 🚀 快速开始

### 主要工具 (推荐使用)

```bash
# 使用SQL脚本执行器 (统一入口)
python scripts/db/run_sqls.py --create-user     # 创建演示用户
python scripts/db/run_sqls.py --mock-data      # 生成测试数据
python scripts/db/run_sqls.py --cleanup        # 清理测试数据
python scripts/db/run_sqls.py --all            # 执行完整流程
python scripts/db/run_sqls.py --check-health   # PostgreSQL健康检查
python scripts/db/run_sqls.py --dry-run --all  # 干运行模式验证
```

### 独立工具使用

```bash
# 数据库迁移和管理
python scripts/db/migrate.py --help                   # 数据库迁移工具

# 直接执行SQL脚本 (高级用户)
psql -h localhost -U crypto_trader -d crypto_trader_test -f scripts/db/create_user.sql
psql -h localhost -U crypto_trader -d crypto_trader_test -f scripts/db/mock_data.sql
psql -h localhost -U crypto_trader -d crypto_trader_test -f scripts/db/cleanup_data.sql
```

## 📊 SQL脚本详解

### 1. create_user.sql
- **功能**: 创建测试和生产环境所需的用户账户
- **用户**: demo (普通用户), admin (管理员), testuser (测试用户)
- **角色系统**: user, admin, super_admin 枚举类型
- **权限管理**: 完整的用户权限表和权限分配
- **密码**: demo/admin使用password123，testuser使用test (bcrypt哈希)
- **特性**: 角色权限系统、用户表、权限表、触发器、验证插入结果

### 2. mock_data.sql
- **功能**: 生成完整的测试环境数据
- **依赖**: 需要先执行 create_user.sql
- **数据**: 订单、风险配置、代理检查点、待处理操作
- **特性**: 完整的表结构创建、关联数据生成、统计报告

### 3. cleanup_data.sql
- **功能**: 清理所有测试环境数据
- **范围**: 删除所有测试数据，重置序列
- **安全**: 按外键依赖顺序删除，避免约束冲突
- **特性**: 清理前后统计、序列重置、验证结果

## 🔧 SQL脚本执行器功能

### 核心特性
- **事务管理**: 每个SQL脚本在独立事务中执行
- **错误处理**: 详细的错误报告和回滚机制
- **执行日志**: 完整的操作日志和统计信息
- **干运行模式**: 验证SQL内容而不实际执行

### 命令行参数
```bash
# 操作参数 (互斥)
--create-user    # 创建演示用户
--mock-data      # 生成测试数据
--cleanup        # 清理测试数据
--all            # 执行完整流程

# 可选参数
--verbose        # 显示详细日志
--dry-run        # 干运行模式
```

### 使用示例
```bash
# 基本使用
python scripts/database/run_sqls.py --create-user

# 详细日志
python scripts/database/run_sqls.py --mock-data --verbose

# 干运行验证
python scripts/database/run_sqls.py --all --dry-run

# 完整流程
python scripts/database/run_sqls.py --all --verbose
```

## 🗃️ 数据库配置

### 连接参数
```python
{
    'host': 'localhost',
    'port': 5432,
    'database': 'crypto_trader_test',
    'user': 'crypto_trader',
    'password': 'test_password_123'
}
```

### 环境要求
- PostgreSQL 12+ 
- Python 3.11+
- asyncpg库

## ⚠️ 注意事项

### 安全提醒
- **仅用于测试环境**: 所有脚本仅适用于测试数据库
- **数据不可恢复**: cleanup操作将永久删除数据
- **权限要求**: 需要数据库创建表和序列的权限

### 最佳实践
1. **先验证后执行**: 使用 `--dry-run` 模式验证SQL内容
2. **备份重要数据**: 在清理前备份重要的测试数据
3. **检查连接**: 确保PostgreSQL服务正在运行
4. **查看日志**: 使用 `--verbose` 参数获取详细信息

## 🔄 与旧架构的对比

| 特性 | 旧架构 (Python脚本) | 新架构 (SQL DDL) |
|------|-------------------|------------------|
| **数据管理** | Python业务逻辑 | 纯SQL DDL脚本 |
| **版本控制** | 复杂Python代码 | 简洁SQL脚本 |
| **维护性** | 需要Python环境 | 标准SQL，易维护 |
| **执行效率** | Python解释执行 | 数据库原生执行 |
| **错误处理** | 分散在各脚本 | 统一执行器管理 |
| **事务管理** | 手动管理 | 自动事务管理 |

## 📈 未来扩展

### 计划功能
- **数据迁移脚本**: 支持数据库版本升级
- **性能测试数据**: 生成大量测试数据
- **数据验证脚本**: 验证数据完整性
- **备份恢复脚本**: 自动化备份和恢复

### 扩展方式
1. 在相应目录添加新的SQL脚本
2. 在 `run_sqls.py` 中添加新的执行选项
3. 更新文档说明新功能的使用方法

---

**数据库管理架构重新设计完成！** 🎉 现在拥有了基于SQL DDL的现代化数据库管理工具。
