#!/usr/bin/env python3
"""
SQL脚本执行器 - 极简版
遵循《0. 项目规范.md》的极简设计原则

核心功能：
- 直接执行SQL脚本
- 数据库健康检查
- 简化的事务管理

版本: 4.0 (极简版)
创建日期: 2025-07-22
"""

import sys
from pathlib import Path

try:
    import asyncpg
except ImportError:
    print("❌ 错误: 需要安装asyncpg库")
    print("运行: pip install asyncpg")
    sys.exit(1)


# 数据库配置 - 与docker-compose.dev.yml保持一致
DB_CONFIG = {
    'host': 'localhost',
    'port': 5433,
    'database': 'crypto_trader_dev',
    'user': 'crypto_trader',
    'password': 'dev_password_123'
}


async def connect_db():
    """连接数据库"""
    try:
        connection = await asyncpg.connect(
            host=DB_CONFIG['host'],
            port=DB_CONFIG['port'],
            database=DB_CONFIG['database'],
            user=DB_CONFIG['user'],
            password=DB_CONFIG['password']
        )
        return connection
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return None


async def execute_sql_file(sql_file: Path) -> bool:
    """执行SQL文件"""
    if not sql_file.exists():
        print(f"❌ SQL文件不存在: {sql_file}")
        return False

    connection = await connect_db()
    if not connection:
        return False

    try:
        # 读取SQL文件
        sql_content = sql_file.read_text(encoding='utf-8')
        print(f"📝 执行SQL文件: {sql_file.name}")

        # 执行SQL
        await connection.execute(sql_content)
        print(f"✅ SQL文件执行成功: {sql_file.name}")
        return True

    except Exception as e:
        print(f"❌ SQL文件执行失败 {sql_file.name}: {e}")
        return False
    finally:
        await connection.close()


async def check_health() -> bool:
    """检查数据库健康状态"""
    connection = await connect_db()
    if not connection:
        return False

    try:
        # 执行简单查询
        result = await connection.fetchval("SELECT 1")
        if result == 1:
            print("✅ 数据库健康检查通过")
            return True
        else:
            print("❌ 数据库健康检查失败")
            return False
    except Exception as e:
        print(f"❌ 数据库健康检查异常: {e}")
        return False
    finally:
        await connection.close()


def main():
    """主函数 - 极简实现，类似bash脚本"""
    # 获取数据库脚本目录
    script_dir = Path(__file__).parent

    # SQL文件映射
    sql_files = {
        'init': script_dir / 'init.sql',
        'create-user': script_dir / 'create_user.sql',
        'mock-data': script_dir / 'mock_data.sql',
        'cleanup': script_dir / 'cleanup_data.sql'
    }

    # 获取操作类型参数
    operation = sys.argv[1] if len(sys.argv) > 1 else "help"

    print("=== SQL脚本执行器 ===")
    print(f"数据库: {DB_CONFIG['database']}@{DB_CONFIG['host']}:{DB_CONFIG['port']}")
    print()

    # 直接的参数映射 - 类似bash的case语句
    if operation == "init":
        print("🔧 初始化数据库...")
        import asyncio
        success = asyncio.run(execute_sql_file(sql_files['init']))

    elif operation == "create-user":
        print("👤 创建用户...")
        import asyncio
        success = asyncio.run(execute_sql_file(sql_files['create-user']))

    elif operation == "mock-data":
        print("📊 生成测试数据...")
        import asyncio
        success = asyncio.run(execute_sql_file(sql_files['mock-data']))

    elif operation == "cleanup":
        print("🧹 清理数据...")
        import asyncio
        success = asyncio.run(execute_sql_file(sql_files['cleanup']))

    elif operation == "check-health":
        print("🔍 检查数据库健康状态...")
        import asyncio
        success = asyncio.run(check_health())

    elif operation == "all":
        print("🚀 执行完整流程...")
        import asyncio

        # 按顺序执行所有SQL文件
        operations = [
            ('初始化数据库', sql_files['init']),
            ('创建用户', sql_files['create-user']),
            ('生成测试数据', sql_files['mock-data'])
        ]

        success = True
        for desc, sql_file in operations:
            print(f"📝 {desc}...")
            if not asyncio.run(execute_sql_file(sql_file)):
                success = False
                break

    else:
        # 显示所有可用命令 - 透明执行原则
        print("可用的SQL操作：")
        print()
        print("数据库初始化:")
        print("  执行 init.sql")
        print()
        print("用户创建:")
        print("  执行 create_user.sql")
        print()
        print("测试数据:")
        print("  执行 mock_data.sql")
        print()
        print("数据清理:")
        print("  执行 cleanup_data.sql")
        print()
        print("健康检查:")
        print("  SELECT 1 查询测试")
        print()
        print("使用方法:")
        print("  python scripts/db/run_sqls.py init        # 初始化数据库")
        print("  python scripts/db/run_sqls.py create-user # 创建用户")
        print("  python scripts/db/run_sqls.py mock-data   # 生成测试数据")
        print("  python scripts/db/run_sqls.py cleanup     # 清理数据")
        print("  python scripts/db/run_sqls.py check-health # 健康检查")
        print("  python scripts/db/run_sqls.py all         # 执行完整流程")
        return

    # 输出结果
    if success:
        print("\n✅ SQL操作完成")
    else:
        print("\n❌ SQL操作失败")
        sys.exit(1)


if __name__ == '__main__':
    # 检查Python版本
    if sys.version_info < (3, 11):
        print("❌ 错误: 需要Python 3.11或更高版本")
        sys.exit(1)

    main()
