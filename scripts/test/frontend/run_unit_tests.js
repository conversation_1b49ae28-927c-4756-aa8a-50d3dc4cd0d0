#!/usr/bin/env node

/**
 * 前端单元测试和组件测试运行脚本
 *
 * 功能：
 * - 运行前端单元测试（Vitest）
 * - 运行Vue组件测试
 * - 验证测试环境配置
 * - 提供简单的命令行界面
 *
 * 注意：E2E和API测试已迁移到Python脚本
 *
 * 用法:
 *   node scripts/test/frontend/run_unit_tests.js           # 运行所有单元测试
 *   node scripts/test/frontend/run_unit_tests.js components # 组件测试
 *   node scripts/test/frontend/run_unit_tests.js unit      # 单元测试
 *   node scripts/test/frontend/run_unit_tests.js validate  # 验证测试环境
 */

const { spawn, execSync } = require('child_process')
const path = require('path')
const fs = require('fs')

const frontendDir = path.join(__dirname, '..', '..', '..', 'frontend')

// 带超时的进程运行函数
function runWithTimeout(command, args, options, timeout = 30000) {
  return new Promise((resolve, reject) => {
    const child = spawn(command, args, options)

    // 设置超时
    const timeoutId = setTimeout(() => {
      console.log(`\n⏰ 测试超时 (${timeout/1000}秒)，强制终止进程...`)
      child.kill('SIGTERM')
      setTimeout(() => {
        if (!child.killed) {
          child.kill('SIGKILL')
        }
      }, 5000)
      reject(new Error(`测试超时: ${timeout/1000}秒`))
    }, timeout)

    child.on('close', (code) => {
      clearTimeout(timeoutId)
      if (code === 0) {
        console.log('✅ 测试完成')
        resolve(true)
      } else {
        console.log(`❌ 测试失败，退出码: ${code}`)
        resolve(false)
      }
    })

    child.on('error', (error) => {
      clearTimeout(timeoutId)
      console.error(`❌ 进程错误: ${error.message}`)
      reject(error)
    })
  })
}

// 测试环境验证检查项
const VALIDATION_CHECKS = [
  {
    name: 'Node.js版本',
    check: () => {
      const version = process.version
      const major = parseInt(version.slice(1).split('.')[0])
      return major >= 16
    },
    fix: '请升级到Node.js 16或更高版本'
  },
  {
    name: '依赖包安装',
    check: () => {
      const packagePath = path.join(frontendDir, 'package.json')
      if (!fs.existsSync(packagePath)) return false
      
      const pkg = JSON.parse(fs.readFileSync(packagePath, 'utf8'))
      const requiredDeps = ['@playwright/test', 'vitest']
      return requiredDeps.every(dep => 
        pkg.dependencies?.[dep] || pkg.devDependencies?.[dep]
      )
    },
    fix: '运行: npm install'
  },
  {
    name: '测试目录结构',
    check: () => {
      const testDirs = ['tests/api', 'tests/components', 'tests/e2e']
      return testDirs.every(dir => {
        const dirPath = path.join(frontendDir, dir)
        return fs.existsSync(dirPath)
      })
    },
    fix: '确保测试目录结构完整'
  }
]

function runTests() {
  console.log('🧪 运行所有单元测试...')
  return runWithTimeout('npm', ['run', 'test:run'], {
    stdio: 'inherit',
    cwd: frontendDir
  }, 120000) // 120秒超时，使用test:run而非test（避免watch模式）
}

function runUnitTests() {
  console.log('🔬 运行单元测试...')
  return runWithTimeout('npx', ['vitest', 'run', 'tests/unit', '--reporter=verbose'], {
    stdio: 'inherit',
    cwd: frontendDir
  }, 60000) // 60秒超时，使用run模式而非watch模式
}

function runComponentTests() {
  console.log('🧩 运行组件测试...')
  return runWithTimeout('npx', ['vitest', 'run', 'tests/components', '--reporter=verbose'], {
    stdio: 'inherit',
    cwd: frontendDir
  }, 60000) // 60秒超时，使用run模式而非watch模式
}

function runE2ETests() {
  console.log('🌐 运行E2E测试...')
  spawn('npx', ['playwright', 'test', 'tests/e2e'], { 
    stdio: 'inherit', 
    cwd: frontendDir 
  })
}

function runQuickTests() {
  console.log('⚡ 运行快速测试（组件测试）...')
  spawn('npx', ['vitest', 'tests/components'], { 
    stdio: 'inherit', 
    cwd: frontendDir 
  })
}

function validateEnvironment() {
  console.log('🔍 验证测试环境...\n')
  
  let passed = 0
  let failed = 0
  const failures = []
  
  VALIDATION_CHECKS.forEach((check, index) => {
    process.stdout.write(`${index + 1}. ${check.name}... `)
    
    try {
      if (check.check()) {
        console.log('✅ 通过')
        passed++
      } else {
        console.log('❌ 失败')
        failed++
        failures.push(check)
      }
    } catch (error) {
      console.log(`❌ 错误: ${error.message}`)
      failed++
      failures.push({ ...check, error: error.message })
    }
  })
  
  // 显示结果
  console.log(`\n📊 验证结果`)
  console.log(`✅ 通过: ${passed}`)
  console.log(`❌ 失败: ${failed}`)
  
  if (failures.length > 0) {
    console.log(`\n🔧 需要修复的问题:`)
    failures.forEach((failure, index) => {
      console.log(`${index + 1}. ${failure.name}`)
      console.log(`   修复方法: ${failure.fix}`)
      if (failure.error) {
        console.log(`   错误信息: ${failure.error}`)
      }
    })
    
    console.log(`\n⚠️  请修复上述问题后再运行测试`)
    return false
  } else {
    console.log(`\n🎉 所有检查通过！测试环境已就绪。`)
    
    console.log(`\n🚀 后续步骤:`)
    console.log(`1. 单元测试: node scripts/test/frontend/run_unit_tests.js unit`)
    console.log(`2. 组件测试: node scripts/test/frontend/run_unit_tests.js components`)
    console.log(`3. E2E测试: python scripts/test/frontend/run_e2e_tests.py`)
    console.log(`4. API测试: python scripts/test/frontend/run_api_tests.py`)
    return true
  }
}

function showSystemInfo() {
  console.log('💻 系统信息')
  console.log(`Node.js: ${process.version}`)
  console.log(`平台: ${process.platform}`)
  console.log(`架构: ${process.arch}`)
  
  try {
    const npmVersion = execSync('npm --version', { encoding: 'utf8' }).trim()
    console.log(`npm: v${npmVersion}`)
  } catch {
    console.log('npm: 不可用')
  }
  console.log('')
}

async function main() {
  const command = process.argv[2]

  try {
    let success = true

    if (command === 'components') {
      success = await runComponentTests()
    } else if (command === 'unit') {
      success = await runUnitTests()
    } else if (command === 'validate') {
      showSystemInfo()
      success = validateEnvironment()
    } else if (command === 'help') {
      console.log('前端单元测试和组件测试运行脚本')
      console.log('用法: node scripts/test/frontend/run_unit_tests.js [选项]')
      console.log('')
      console.log('选项:')
      console.log('  (无参数)    - 运行所有单元测试')
      console.log('  unit       - 运行单元测试')
      console.log('  components - 运行组件测试')
      console.log('  validate   - 验证测试环境')
      console.log('  help       - 显示此帮助信息')
      console.log('')
      console.log('注意: E2E和API测试请使用Python脚本:')
      console.log('  python scripts/test/frontend/run_e2e_tests.py')
      console.log('  python scripts/test/frontend/run_api_tests.py')
      return
    } else if (command) {
      console.log('用法: node scripts/test/frontend/run_unit_tests.js [unit|components|validate|help]')
      return
    } else {
      success = await runTests()
    }

    // 根据测试结果退出
    process.exit(success ? 0 : 1)

  } catch (error) {
    console.error(`❌ 执行错误: ${error.message}`)
    process.exit(1)
  }
}

if (require.main === module) {
  main()
}

module.exports = { runTests, runUnitTests, runComponentTests, validateEnvironment }
