#!/usr/bin/env python3
"""
前端API测试运行脚本 - 并行测试版
遵循《0. 项目规范.md》的极简设计原则

核心功能：
- 使用并行测试配置中的API测试项目
- 纯命令执行，无复杂逻辑
- 透明的命令映射

版本: 5.0 (并行测试版)
创建日期: 2025-07-27
"""

import subprocess
import sys
from pathlib import Path


def check_backend_service() -> bool:
    """检查后端服务是否运行"""
    import socket
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
            sock.settimeout(1)
            result = sock.connect_ex(('localhost', 8000))
            return result == 0  # 0表示连接成功，端口被占用
    except Exception:
        return False


def main():
    """主函数 - 极简实现，类似bash脚本"""
    # 获取前端目录
    script_dir = Path(__file__).parent
    frontend_dir = script_dir.parent.parent.parent / "frontend"

    if not frontend_dir.exists():
        print(f"❌ 错误: 前端目录不存在: {frontend_dir}")
        sys.exit(1)

    # 获取测试类型参数
    test_type = sys.argv[1] if len(sys.argv) > 1 else "help"

    print("=== 前端API测试命令集合 ===")
    print(f"当前目录: {frontend_dir}")
    print()

    # 直接的参数映射 - 使用并行测试配置
    if test_type == "all":
        print("🧪 运行所有API测试 (并行模式)...")
        cmd = ["npx", "playwright", "test", "--config", "playwright-parallel.config.js", "--project", "api-fast"]

    elif test_type == "auth":
        print("🔐 运行认证API测试...")
        cmd = ["npx", "playwright", "test", "--config", "playwright-parallel.config.js", "--project", "api-fast", "--grep", "auth"]

    elif test_type == "orders":
        print("📋 运行订单API测试...")
        cmd = ["npx", "playwright", "test", "--config", "playwright-parallel.config.js", "--project", "api-fast", "--grep", "order"]

    elif test_type == "fast":
        print("⚡ 运行快速API测试 (并行模式)...")
        cmd = ["npx", "playwright", "test", "--config", "playwright-parallel.config.js", "--project", "api-fast"]

    elif test_type == "validate":
        print("🔍 验证测试环境...")
        if check_backend_service():
            print("✅ 后端服务运行正常 (端口 8000)")
            print("✅ 测试环境验证通过")
            sys.exit(0)
        else:
            print("❌ 后端服务未运行，请启动后端服务")
            print("启动命令: cd backend && python -m uvicorn app.main:app --reload")
            sys.exit(1)

    else:
        # 显示所有可用命令 - 并行测试优先
        print("🚀 可用的API测试命令 (并行模式)：")
        print()
        print("所有API测试:")
        print("  npx playwright test --config playwright-parallel.config.js --project api-fast")
        print()
        print("认证API测试:")
        print("  npx playwright test --config playwright-parallel.config.js --project api-fast --grep auth")
        print()
        print("订单API测试:")
        print("  npx playwright test --config playwright-parallel.config.js --project api-fast --grep order")
        print()
        print("使用方法:")
        print("  python scripts/test/frontend/run_api_tests.py all      # 运行所有API测试")
        print("  python scripts/test/frontend/run_api_tests.py fast     # 快速API测试")
        print("  python scripts/test/frontend/run_api_tests.py auth     # 认证API测试")
        print("  python scripts/test/frontend/run_api_tests.py orders   # 订单API测试")
        print("  python scripts/test/frontend/run_api_tests.py validate # 验证测试环境")
        return

    # 检查后端服务
    if not check_backend_service():
        print("❌ 错误: 后端服务未运行，请先启动后端服务")
        print("启动命令: cd backend && python -m uvicorn app.main:app --reload")
        sys.exit(1)

    # 直接执行命令，立即显示输出
    try:
        result = subprocess.run(cmd, cwd=frontend_dir)
        if result.returncode == 0:
            print("\n✅ API测试完成")
        else:
            print("\n❌ API测试失败")
        sys.exit(result.returncode)
    except KeyboardInterrupt:
        print("\n⚠️  测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 命令执行异常: {e}")
        sys.exit(1)


if __name__ == '__main__':
    # 检查Python版本
    if sys.version_info < (3, 11):
        print("❌ 错误: 需要Python 3.11或更高版本")
        sys.exit(1)

    main()


