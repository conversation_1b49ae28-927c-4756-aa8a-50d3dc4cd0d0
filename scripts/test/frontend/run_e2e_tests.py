#!/usr/bin/env python3
"""
前端E2E测试运行脚本 - 并行测试版
遵循《0. 项目规范.md》的极简设计原则

核心功能：
- 优先使用并行测试配置
- 支持传统playwright命令作为备选
- 透明的命令映射

版本: 5.0 (并行测试版)
创建日期: 2025-07-27
"""

import subprocess
import sys
from pathlib import Path


def check_port(port: int) -> bool:
    """检查端口是否被占用"""
    import socket
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
            sock.settimeout(1)
            result = sock.connect_ex(('localhost', port))
            return result == 0  # 0表示连接成功，端口被占用
    except Exception:
        return False


def check_services() -> bool:
    """检查前后端服务是否运行"""
    # 检查前端服务 (端口5173)
    if not check_port(5173):
        print("❌ 前端服务未运行，请启动前端服务")
        print("启动命令: cd frontend && npm run dev")
        return False

    # 检查后端服务(端口8000+5173)
    if not check_port(8000):
        print("❌ 后端服务未运行，请启动后端服务")
        print("启动命令: cd backend && python -m uvicorn app.main:app --reload")
        return False

    return True


def main():
    """主函数 - 极简实现，类似bash脚本"""
    # 获取前端目录
    script_dir = Path(__file__).parent
    frontend_dir = script_dir.parent.parent.parent / "frontend"

    if not frontend_dir.exists():
        print(f"❌ 错误: 前端目录不存在: {frontend_dir}")
        sys.exit(1)

    # 获取测试类型参数
    test_type = sys.argv[1] if len(sys.argv) > 1 else "help"

    print("=== 前端E2E测试命令集合 ===")
    print(f"当前目录: {frontend_dir}")
    print()

    # 直接的参数映射 - 优先使用并行测试
    if test_type == "all":
        print("🧪 运行所有E2E测试 (并行模式)...")
        cmd = ["npm", "run", "test:parallel:standard"]

    elif test_type == "fast":
        print("⚡ 运行快速E2E测试 (并行模式)...")
        cmd = ["npm", "run", "test:parallel:fast"]

    elif test_type == "full":
        print("🔥 运行完整E2E测试 (并行模式)...")
        cmd = ["npm", "run", "test:parallel:full"]

    elif test_type == "smoke":
        print("💨 运行冒烟测试 (并行快速模式)...")
        cmd = ["npm", "run", "test:parallel:fast"]

    elif test_type == "critical":
        print("🔥 运行关键路径测试 (并行标准模式)...")
        cmd = ["npm", "run", "test:parallel:standard"]

    elif test_type == "legacy":
        print("🔧 运行传统E2E测试 (非并行模式)...")
        cmd = ["npx", "playwright", "test", "--config", "playwright-parallel.config.js", "--reporter=line,json"]

    elif test_type == "validate":
        print("🔍 验证测试环境...")
        if check_services():
            print("✅ 前端服务运行正常 (端口 5173)")
            print("✅ 后端服务运行正常 (端口 8000)")
            print("✅ 测试环境验证通过")
            sys.exit(0)
        else:
            print("❌ 测试环境验证失败")
            sys.exit(1)

    else:
        # 显示所有可用命令 - 并行测试优先
        print("🚀 可用的并行测试命令 (推荐)：")
        print()
        print("快速测试:")
        print("  npm run test:parallel:fast")
        print()
        print("标准测试:")
        print("  npm run test:parallel:standard")
        print()
        print("完整测试:")
        print("  npm run test:parallel:full")
        print()
        print("🔧 传统playwright命令 (仅用于调试):")
        print("  npx playwright test --config playwright-parallel.config.js")
        print()
        print("使用方法:")
        print("  python scripts/test/frontend/run_e2e_tests.py fast     # 快速并行测试")
        print("  python scripts/test/frontend/run_e2e_tests.py all      # 标准并行测试")
        print("  python scripts/test/frontend/run_e2e_tests.py full     # 完整并行测试")
        print("  python scripts/test/frontend/run_e2e_tests.py smoke    # 冒烟测试")
        print("  python scripts/test/frontend/run_e2e_tests.py critical # 关键路径测试")
        print("  python scripts/test/frontend/run_e2e_tests.py legacy   # 传统模式")
        print("  python scripts/test/frontend/run_e2e_tests.py validate # 验证测试环境")
        return

    # 检查服务状态
    if not check_services():
        print("❌ 错误: 服务未运行，请先启动前后端服务")
        sys.exit(1)

    # 直接执行命令，立即显示输出
    try:
        result = subprocess.run(cmd, cwd=frontend_dir)
        if result.returncode == 0:
            print("\n✅ E2E测试完成")
            print(f"测试报告目录: {frontend_dir}/test-results/")
        else:
            print("\n❌ E2E测试失败")
        sys.exit(result.returncode)
    except KeyboardInterrupt:
        print("\n⚠️  测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 命令执行异常: {e}")
        sys.exit(1)


if __name__ == '__main__':
    # 检查Python版本
    if sys.version_info < (3, 11):
        print("❌ 错误: 需要Python 3.11或更高版本")
        sys.exit(1)

    main()


