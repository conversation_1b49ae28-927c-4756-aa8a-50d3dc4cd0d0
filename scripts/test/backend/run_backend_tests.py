#!/usr/bin/env python3
"""
后端测试运行脚本 - 极简版
遵循《0. 项目规范.md》的极简设计原则

核心功能：
- 直接执行pytest命令，类似run_backend_tests.sh
- 纯命令执行，无复杂逻辑
- 透明的命令映射

版本: 5.0 (极简版)
创建日期: 2025-07-22
"""

import subprocess
import sys
from pathlib import Path


def check_postgresql_connection() -> bool:
    """检查PostgreSQL数据库连接"""
    import socket
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
            sock.settimeout(2)
            result = sock.connect_ex(('localhost', 5432))
            return result == 0  # 0表示连接成功
    except Exception:
        return False


def main():
    """主函数 - 极简实现，类似bash脚本"""
    # 获取后端目录
    script_dir = Path(__file__).parent
    backend_dir = script_dir.parent.parent.parent / "backend"

    if not backend_dir.exists():
        print(f"❌ 错误: 后端目录不存在: {backend_dir}")
        sys.exit(1)

    # 获取测试类型参数
    test_type = sys.argv[1] if len(sys.argv) > 1 else "help"

    print("=== 后端测试命令集合 ===")
    print(f"当前目录: {backend_dir}")
    print()

    # 检查PostgreSQL依赖
    if not check_postgresql_connection():
        print("❌ 错误: PostgreSQL数据库未运行或不可访问")
        print("📋 后端测试依赖PostgreSQL数据库，请确保:")
        print("   1. PostgreSQL服务正在运行 (端口 5432)")
        print("   2. 测试数据库 'crypto_trader_test' 已创建")
        print("   3. 用户 'crypto_trader' 有访问权限")
        print()
        print("🚀 快速启动PostgreSQL (Docker方式):")
        print("   docker-compose -f docker-compose.test.yml up -d postgres-test")
        print()
        print("🔧 或手动启动PostgreSQL服务:")
        print("   brew services start postgresql  # macOS")
        print("   sudo systemctl start postgresql  # Linux")
        sys.exit(1)
    else:
        print("✅ PostgreSQL数据库连接正常")

    # 直接的参数映射 - 类似bash的case语句
    if test_type == "unit":
        print("🧪 运行单元测试...")
        cmd = ["python", "-m", "pytest", "tests/unit/", "-v", "--tb=short", "--strict-markers", "--disable-warnings"]

    elif test_type == "integration":
        print("🔗 运行集成测试...")
        cmd = ["python", "-m", "pytest", "tests/integration/", "-v", "--tb=short", "--strict-markers", "--disable-warnings"]

    elif test_type == "e2e":
        print("🌐 运行E2E测试...")
        # "参考《前后端测试设计文档》，E2E测试统一放在前端目录下"
        print("❌ E2E测试已迁移至前端目录，请在frontend/run_e2e_tests.py中运行")
        return False  # 直接返回失败，不执行命令

        # cmd = ["python", "-m", "pytest", "tests/e2e/", "-v", "--tb=short", "--strict-markers", "--disable-warnings"]

    elif test_type == "all":
        print("📋 运行所有测试...")
        cmd = ["python", "-m", "pytest", "tests/", "-v", "--tb=short", "--strict-markers", "--disable-warnings"]

    else:
        # 显示所有可用命令 - 透明执行原则
        print("📋 后端测试依赖说明:")
        print("   ⚠️  所有后端测试都依赖PostgreSQL数据库")
        print("   📊 测试数据库: crypto_trader_test")
        print("   🔌 连接地址: localhost:5432")
        print()
        print("可用的pytest命令：")
        print()
        print("单元测试:")
        print("  python -m pytest tests/unit/ -v --tb=short --strict-markers --disable-warnings")
        print()
        print("集成测试:")
        print("  python -m pytest tests/integration/ -v --tb=short --strict-markers --disable-warnings")
        print()
        print("E2E测试:")
        print("  python -m pytest tests/e2e/ -v --tb=short --strict-markers --disable-warnings")
        print()
        print("所有测试:")
        print("  python -m pytest tests/ -v --tb=short --strict-markers --disable-warnings")
        print()
        print("使用方法:")
        print("  python scripts/test/backend/run_backend_tests.py unit        # 运行单元测试")
        print("  python scripts/test/backend/run_backend_tests.py integration # 运行集成测试")
        print("  python scripts/test/backend/run_backend_tests.py e2e         # 运行E2E测试")
        print("  python scripts/test/backend/run_backend_tests.py all         # 运行所有测试")
        print()
        print("🚀 启动PostgreSQL:")
        print("  docker-compose -f docker-compose.test.yml up -d postgres-test")
        return

    # 直接执行命令，立即显示输出
    try:
        result = subprocess.run(cmd, cwd=backend_dir)
        if result.returncode == 0:
            print("\n✅ 后端测试完成")
        else:
            print("\n❌ 后端测试失败")
        sys.exit(result.returncode)
    except KeyboardInterrupt:
        print("\n⚠️  测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 命令执行异常: {e}")
        sys.exit(1)


if __name__ == '__main__':
    # 检查Python版本
    if sys.version_info < (3, 11):
        print("❌ 错误: 需要Python 3.11或更高版本")
        sys.exit(1)

    main()
