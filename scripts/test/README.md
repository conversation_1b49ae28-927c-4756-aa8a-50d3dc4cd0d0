# 🧪 测试脚本使用指南

本目录包含所有测试相关的脚本和工具，提供统一的测试执行接口和完整的测试环境管理。

## 📋 快速导航

### 🚀 立即开始
- [🚀 快速开始](#-快速开始) - 一键执行命令
- [📋 测试依赖要求](#-测试依赖要求) - 环境准备
- [🔧 故障排除](#-故障排除) - 常见问题解决

### 📖 详细指南
- [📁 目录结构](#-目录结构) - 脚本组织架构
- [📖 脚本详细说明](#-脚本详细说明) - 各脚本功能说明
- [💡 最佳实践](#-最佳实践) - 使用建议

## 🎯 核心特性

- **🏗️ 统一委托模式**: 所有测试类型都通过专门脚本执行，架构一致
- **⚡ 直接命令执行**: 使用原生subprocess，简单高效
- **🔧 智能依赖检查**: 自动检测服务状态和环境配置
- **🛡️ 完全向后兼容**: 现有命令行接口完全兼容
- **🇨🇳 中文优先**: 所有输出信息使用中文，便于理解
- **📊 详细日志记录**: 完整的测试执行日志和错误信息

## 📁 目录结构 (重新设计后)

```
scripts/test/
├── README.md                    # 本文档
├── backend/                     # 后端测试脚本 (分层架构)
│   └── run_backend_tests.py    # ✅ 后端测试运行器 (简化重构版)
└── frontend/                    # 前端测试脚本
    ├── run_unit_tests.js       # 单元/组件测试 (JavaScript)
    ├── run_api_tests.py        # ✅ API接口测试 (简化重构版)
    └── run_e2e_tests.py        # ✅ E2E UI测试 (简化重构版)
```

**架构说明**:
- `scripts/test.py` 作为**简化调度器**，专注于核心功能
- 每个测试类型都有专门的入口脚本，职责明确
- 主脚本使用简化实现，专门脚本保持tools模块架构（遗留）
- 数据库管理功能已移至 `scripts/db/` 目录

## 📋 测试依赖要求

### 🔴 强依赖 - PostgreSQL数据库
**所有后端测试都强依赖PostgreSQL数据库**
- **数据库名**: `crypto_trader_test`
- **连接地址**: `localhost:5432`
- **用户**: `crypto_trader`
- **密码**: `test_password_123`

### 🟡 可选依赖 - 服务运行状态
- **后端服务**(端口8000+5173): API测试和E2E测试需要
- **前端服务** (端口5173): E2E测试需要
- **前端单元测试**: 无外部依赖

### 🚀 启动PostgreSQL数据库
```bash
# 方式1: 使用Docker Compose (推荐)
docker-compose -f docker-compose.test.yml up -d postgres-test

# 方式2: 手动启动PostgreSQL
brew services start postgresql  # macOS
sudo systemctl start postgresql  # Linux

# 检查连接状态
python scripts/test.py --status
```

## 🚀 快速开始

### 📋 一键复制执行命令

#### 🎯 完整测试流程 (推荐新手)
```bash
# 1. 启动测试环境
docker-compose -f docker-compose.test.yml up -d

# 2. 检查环境状态
python scripts/test.py --status

# 3. 执行完整测试套件
python scripts/test.py --execute-all

# 4. 停止测试环境
docker-compose -f docker-compose.test.yml down
```

#### ⚡ 快速检查命令
```bash
python scripts/test.py --status          # 检查服务运行状态
python scripts/test.py --check-env       # 检查环境依赖配置
python scripts/test.py --all             # 显示所有可用测试命令
```

#### 🔧 单独测试执行 (开发调试)
```bash
# 后端测试 (需要PostgreSQL)
python scripts/test.py --pytest unit            # 后端单元测试
python scripts/test.py --pytest integration     # 后端集成测试

# 前端测试 (无外部依赖)
python scripts/test.py --vitest unit            # 前端单元测试
python scripts/test.py --vitest components      # 前端组件测试

# 集成测试 (推荐并行测试)
cd frontend && npm run test:parallel:fast       # 快速并行测试 (推荐)
cd frontend && npm run test:parallel:standard   # 标准并行测试
python scripts/test.py --playwright api         # 前端API测试 (传统方式)
python scripts/test.py --playwright e2e         # 前端E2E测试 (传统方式)
```

#### 📊 服务依赖关系
```
PostgreSQL (5432) ← 后端测试 (强依赖)
    ↓
后端服务 (8000) ← API测试 (依赖)
    ↓
前端服务 (5173) ← E2E测试 (依赖)

前端单元/组件测试 ← 无外部依赖 (独立运行)
```

### 主要测试入口 (简化调度器)
```bash
# ⚠️  注意: 后端测试需要PostgreSQL数据库运行在端口5432
# 使用简化调度器 (位于scripts/test.py) - 73.7%代码量减少，专注核心功能

# 1. 首先检查依赖状态
python scripts/test.py --status                 # 查看服务状态 (包括PostgreSQL)

# 2. 运行测试 (按依赖程度排序) - 统一委托模式
python scripts/test.py --pytest unit            # 后端单元测试 (需要PostgreSQL)
python scripts/test.py --pytest integration     # 后端集成测试 (需要PostgreSQL)
python scripts/test.py --vitest unit            # 前端单元测试 (无外部依赖)
python scripts/test.py --vitest components      # 前端组件测试 (无外部依赖)
cd frontend && npm run test:parallel:fast       # 快速并行测试 (推荐)
cd frontend && npm run test:parallel:standard   # 标准并行测试 (推荐)
python scripts/test.py --playwright api         # 前端API测试 (传统方式)
python scripts/test.py --playwright e2e         # 前端E2E测试 (传统方式)
python scripts/test.py --all                    # 显示测试命令清单 (不执行)
python scripts/test.py --execute-all            # 实际运行所有测试 (需要所有依赖)

# 3. 其他命令
python scripts/test.py --vitest validate        # 验证前端测试环境
python scripts/test.py --unified-api            # 统一API测试
python scripts/test.py --check-env              # 检查测试环境
```

### 统一委托模式测试命令
```bash
# 后端测试 (委托给Python脚本)
python scripts/test.py --pytest unit                      # 单元测试
python scripts/test.py --pytest integration               # 集成测试
python scripts/test.py --pytest e2e                       # 端到端测试
python scripts/test.py --pytest all                       # 所有后端测试

# 前端单元/组件测试 (委托给JavaScript脚本)
python scripts/test.py --vitest unit                      # 前端单元测试
python scripts/test.py --vitest components                # 前端组件测试
python scripts/test.py --vitest validate                  # 验证前端测试环境

# 前端集成测试 (推荐并行测试)
cd frontend && npm run test:parallel:fast                 # 快速并行测试 (推荐)
cd frontend && npm run test:parallel:standard             # 标准并行测试 (推荐)
python scripts/test.py --playwright api                   # API接口测试 (传统方式)
python scripts/test.py --playwright e2e                   # E2E UI测试 (传统方式)

# 专门脚本直接调用 (高级用法)
node scripts/test/frontend/run_unit_tests.js unit         # 直接调用JS脚本
python scripts/test/frontend/run_api_tests.py all         # 直接调用API测试
python scripts/test/frontend/run_e2e_tests.py all         # 直接调用E2E测试

# 环境检查 (集成到统一测试脚本)
python scripts/test.py --check-env-quick                  # 快速检查
python scripts/test.py --check-env                        # 完整检查

# 前端单元测试 (JavaScript)
node scripts/test/frontend/run_unit_tests.js unit          # 单元测试
node scripts/test/frontend/run_unit_tests.js components    # 组件测试

# E2E和API测试（推荐并行测试）
cd frontend && npm run test:parallel:fast                  # 快速并行测试 (推荐)
cd frontend && npm run test:parallel:standard              # 标准并行测试 (推荐)
python scripts/test/frontend/run_e2e_tests.py fast         # 快速E2E测试
python scripts/test/frontend/run_e2e_tests.py smoke        # 冒烟测试
python scripts/test/frontend/run_api_tests.py              # API测试
python scripts/test/frontend/run_api_tests.py auth         # 认证测试
```

## 🎯 模拟数据管理

### 统一模拟数据管理
```bash
# 创建演示用户
python scripts/test/mock/unified_mock_data.py users

# 生成模拟数据
python scripts/test/mock/unified_mock_data.py data

# 创建用户并生成数据
python scripts/test/mock/unified_mock_data.py all

# 清理所有测试数据
python scripts/test/mock/unified_mock_data.py clean
```

## 🔧 测试环境管理

### 检查测试环境
```bash
# 检查测试环境配置 (集成到统一测试脚本)
python scripts/test.py --check-env
```

### 启动/停止测试服务
```bash
# 启动测试所需的服务
docker-compose -f docker-compose.test.yml up -d

# 停止测试服务
docker-compose -f docker-compose.test.yml down
```

### 数据库相关
```bash
# 检查PostgreSQL连接
python scripts/test/database/check_postgres.py
```

## 📖 脚本详细说明

### 统一测试脚本 (test.py) - 架构一致性版
- **功能**: 提供统一的测试运行接口，100%委托模式
- **支持的测试工具**: pytest (后端)、vitest (前端单元)、playwright (前端集成)
- **架构特点**: 所有测试类型都通过专门脚本执行，确保架构一致性
- **推荐使用**: 这是运行测试的首选方式

### 后端测试脚本 (backend/run_backend_tests.py)
- **功能**: 专门的后端测试运行器，支持分层架构
- **支持的测试类型**: unit、integration、e2e、all
- **直接使用**: `python scripts/test/backend/run_backend_tests.py [类型]`
- **统一入口**: `python scripts/test.py --pytest [类型]` (推荐)

### 环境检查功能 (集成到统一测试脚本)
- **功能**: 检查Python环境、系统依赖、项目结构、服务状态
- **快速检查**: `python scripts/test.py --check-env-quick`
- **完整检查**: `python scripts/test.py --check-env`

### 前端单元测试脚本 (frontend/run_unit_tests.js) - 已集成
- **功能**: 专门用于前端单元测试和组件测试的JavaScript脚本
- **支持的测试类型**: unit、components、validate
- **委托调用**: `python scripts/test.py --vitest [类型]` (推荐)
- **直接调用**: `node scripts/test/frontend/run_unit_tests.js [类型]`
- **适用场景**: 运行Vitest单元测试和Vue组件测试

### 前端E2E测试脚本 (frontend/run_e2e_tests.py)
- **功能**: 使用Python Playwright进行E2E测试
- **支持的测试类型**: smoke、critical、validate
- **适用场景**: 端到端功能测试

### 前端API测试脚本 (frontend/run_api_tests.py)
- **功能**: 使用Python Playwright进行API测试
- **支持的测试类型**: auth、orders、configs、validate
- **适用场景**: API接口功能测试

## 🏗️ 架构一致性验证

### ✅ 委托模式统一性
| 测试类型 | 参数 | 委托目标 | 状态 |
|---------|------|----------|------|
| 后端单元测试 | `--pytest unit` | `run_backend_tests.py` | ✅ 一致 |
| 后端集成测试 | `--pytest integration` | `run_backend_tests.py` | ✅ 一致 |
| 前端单元测试 | `--vitest unit` | `run_unit_tests.js` | ✅ 一致 |
| 前端组件测试 | `--vitest components` | `run_unit_tests.js` | ✅ 一致 |
| 前端API测试 | `--playwright api` | `run_api_tests.py` | ✅ 一致 |
| 前端E2E测试 | `--playwright e2e` | `run_e2e_tests.py` | ✅ 一致 |

### 🎯 测试工具分工
- **pytest**: 后端测试 (Python) - 依赖PostgreSQL
- **vitest**: 前端单元/组件测试 (JavaScript) - 无外部依赖
- **playwright**: 前端API/E2E测试 (Python) - 依赖后端/前端服务

## 🔧 故障排除

### 常见问题快速解决

#### 🔴 PostgreSQL连接失败
```bash
# 检查数据库状态
docker-compose ps postgres
docker-compose logs postgres

# 重启数据库
docker-compose restart postgres

# 使用测试配置启动
docker-compose -f docker-compose.test.yml up -d postgres
```

#### 🟡 后端服务无法启动
```bash
# 检查后端服务
docker-compose logs backend
curl http://localhost:8000/health

# 重启后端服务
docker-compose restart backend
```

#### 🟢 前端测试失败
```bash
# 重新安装依赖
cd frontend && npm install

# 清理缓存
rm -rf frontend/node_modules frontend/package-lock.json
cd frontend && npm install
```

#### 🧪 测试数据问题
```bash
# 清理测试数据
python scripts/db/run_sqls.py cleanup_data.sql

# 重新生成测试数据
python scripts/db/run_sqls.py mock_data.sql
```

#### 🚨 完全重置环境
```bash
# 停止所有服务
docker-compose -f docker-compose.test.yml down --volumes

# 重新启动
docker-compose -f docker-compose.test.yml up -d

# 验证环境
python scripts/test.py --check-env
```

## 💡 最佳实践

1. **优先使用统一入口**: `python scripts/test.py --all` 查看所有命令
2. **开发时快速测试**: `python scripts/test.py --vitest unit` (无外部依赖)
3. **部署前完整验证**: `python scripts/test.py --execute-all` (完整测试)
4. **环境检查优先**: 测试前先运行 `python scripts/test.py --status`
5. **分层测试策略**: 单元测试 → 组件测试 → API测试 → E2E测试
6. **定期数据清理**: 使用数据库脚本清理测试数据
7. **日志查看习惯**: 失败时查看 `temp/test_logs/` 目录下的日志

## 🔗 相关文档

- [项目根目录README](../../README.md) - 项目总体说明
- [脚本使用指南](../README.md) - 所有脚本的使用说明
- [测试设计文档](../../docs/5.%20前后端测试设计文档.md) - 详细的测试策略和设计

---

**注意**: 本目录结构已经过优化，消除了重复文件，统一了测试脚本的组织方式。如有问题请参考相关文档或联系开发团队。
