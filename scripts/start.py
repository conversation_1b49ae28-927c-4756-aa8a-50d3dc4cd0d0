#!/usr/bin/env python3
"""
开发服务器启动脚本 - 简化重构版
根据《0. 项目规范.md》创建，遵循测试脚本简化原则

核心功能：
- 启动前后端开发服务器
- 进程管理和信号处理
- 健康检查和状态监控
- 支持单独启动前端或后端

简化改进：
- 移除过度抽象层和复杂的类结构
- 内联配置管理，移除复杂的Config类依赖
- 直接使用subprocess，减少封装
- 专注于核心功能，避免过度工程化

版本: 3.0 (简化重构版)
创建日期: 2025-07-22
"""

import argparse
import asyncio
import signal
import subprocess
import sys
import time
from pathlib import Path
from typing import List, Optional


class SimpleServerManager:
    """简化的开发服务器管理器 - 专注于核心功能"""
    
    def __init__(self, verbose: bool = False):
        """
        初始化服务器管理器
        
        Args:
            verbose: 是否显示详细输出
        """
        self.verbose = verbose
        self.project_root = Path(__file__).parent.parent
        self.backend_dir = self.project_root / "backend"
        self.frontend_dir = self.project_root / "frontend"
        
        # 内联配置 - 避免复杂的配置类
        self.server_config = {
            'backend_port': 8000,
            'frontend_port': 5173,
            'backend_host': '127.0.0.1',
            'frontend_host': '127.0.0.1'
        }
        
        # 进程管理
        self.backend_process = None
        self.frontend_process = None
        self.running = True
        
        # 验证项目结构
        if not self.backend_dir.exists():
            self.log_error(f"后端目录不存在: {self.backend_dir}")
            sys.exit(1)
        
        if not self.frontend_dir.exists():
            self.log_error(f"前端目录不存在: {self.frontend_dir}")
            sys.exit(1)
    
    def log_info(self, message: str) -> None:
        """记录信息日志"""
        print(f"[信息] {message}")
    
    def log_success(self, message: str) -> None:
        """记录成功日志"""
        print(f"\033[92m[成功] {message}\033[0m")
    
    def log_error(self, message: str) -> None:
        """记录错误日志"""
        print(f"\033[91m[错误] {message}\033[0m")
    
    def log_warning(self, message: str) -> None:
        """记录警告日志"""
        print(f"\033[93m[警告] {message}\033[0m")
    
    def print_header(self, title: str) -> None:
        """打印标题头部"""
        print(f"\n{'='*60}")
        print(f"\033[95m{title}\033[0m")
        print(f"{'='*60}")
    
    def setup_signal_handlers(self) -> None:
        """设置信号处理器"""
        def signal_handler(signum, frame):
            self.log_info("收到停止信号，正在关闭服务器...")
            self.running = False
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    async def start_backend(self) -> bool:
        """启动后端服务器"""
        self.log_info("🚀 启动后端服务器...")
        
        try:
            # 检查requirements.txt
            requirements_file = self.backend_dir / "requirements.txt"
            if not requirements_file.exists():
                self.log_error(f"找不到requirements.txt: {requirements_file}")
                return False
            
            # 启动FastAPI服务器
            cmd = [
                "python", "-m", "uvicorn", 
                "main:app", 
                "--host", self.server_config['backend_host'],
                "--port", str(self.server_config['backend_port']),
                "--reload"
            ]
            
            if self.verbose:
                self.log_info(f"执行命令: {' '.join(cmd)}")
            
            self.backend_process = await asyncio.create_subprocess_exec(
                *cmd,
                cwd=self.backend_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT
            )
            
            # 等待一下让服务器启动
            await asyncio.sleep(2)
            
            # 检查进程是否还在运行
            if self.backend_process.returncode is None:
                self.log_success(f"✅ 后端服务器启动成功 (端口: {self.server_config['backend_port']})")
                return True
            else:
                self.log_error("❌ 后端服务器启动失败")
                return False
                
        except Exception as e:
            self.log_error(f"启动后端服务器异常: {e}")
            return False
    
    async def start_frontend(self) -> bool:
        """启动前端服务器"""
        self.log_info("🌐 启动前端服务器...")
        
        try:
            # 检查package.json
            package_json = self.frontend_dir / "package.json"
            if not package_json.exists():
                self.log_error(f"找不到package.json: {package_json}")
                return False
            
            # 启动Vite开发服务器
            cmd = ["npm", "run", "dev"]
            
            if self.verbose:
                self.log_info(f"执行命令: {' '.join(cmd)}")
            
            self.frontend_process = await asyncio.create_subprocess_exec(
                *cmd,
                cwd=self.frontend_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT
            )
            
            # 等待一下让服务器启动
            await asyncio.sleep(3)
            
            # 检查进程是否还在运行
            if self.frontend_process.returncode is None:
                self.log_success(f"✅ 前端服务器启动成功 (端口: {self.server_config['frontend_port']})")
                return True
            else:
                self.log_error("❌ 前端服务器启动失败")
                return False
                
        except Exception as e:
            self.log_error(f"启动前端服务器异常: {e}")
            return False
    
    async def stop_servers(self) -> None:
        """停止所有服务器"""
        self.log_info("🛑 停止服务器...")
        
        # 停止后端服务器
        if self.backend_process and self.backend_process.returncode is None:
            self.log_info("停止后端服务器...")
            self.backend_process.terminate()
            try:
                await asyncio.wait_for(self.backend_process.wait(), timeout=5)
                self.log_success("✅ 后端服务器已停止")
            except asyncio.TimeoutError:
                self.log_warning("⚠️ 后端服务器强制终止")
                self.backend_process.kill()
                await self.backend_process.wait()
        
        # 停止前端服务器
        if self.frontend_process and self.frontend_process.returncode is None:
            self.log_info("停止前端服务器...")
            self.frontend_process.terminate()
            try:
                await asyncio.wait_for(self.frontend_process.wait(), timeout=5)
                self.log_success("✅ 前端服务器已停止")
            except asyncio.TimeoutError:
                self.log_warning("⚠️ 前端服务器强制终止")
                self.frontend_process.kill()
                await self.frontend_process.wait()
    
    async def monitor_servers(self) -> None:
        """监控服务器状态"""
        while self.running:
            # 检查后端进程
            if self.backend_process and self.backend_process.returncode is not None:
                self.log_error("❌ 后端服务器意外退出")
                self.running = False
                break
            
            # 检查前端进程
            if self.frontend_process and self.frontend_process.returncode is not None:
                self.log_error("❌ 前端服务器意外退出")
                self.running = False
                break
            
            await asyncio.sleep(1)
    
    async def start_both_servers(self) -> bool:
        """启动前后端服务器"""
        self.print_header("🚀 启动开发服务器 - 简化重构版")
        
        # 设置信号处理
        self.setup_signal_handlers()
        
        # 启动后端
        if not await self.start_backend():
            return False
        
        # 启动前端
        if not await self.start_frontend():
            await self.stop_servers()
            return False
        
        # 显示访问信息
        self.print_access_info()
        
        # 监控服务器
        try:
            await self.monitor_servers()
        except KeyboardInterrupt:
            self.log_info("收到中断信号")
        finally:
            await self.stop_servers()
        
        return True
    
    async def start_backend_only(self) -> bool:
        """只启动后端服务器"""
        self.print_header("🚀 启动后端服务器 - 简化重构版")
        
        # 设置信号处理
        self.setup_signal_handlers()
        
        # 启动后端
        if not await self.start_backend():
            return False
        
        # 显示访问信息
        self.log_info(f"后端服务器地址: http://{self.server_config['backend_host']}:{self.server_config['backend_port']}")
        self.log_info("按 Ctrl+C 停止服务器")
        
        # 监控服务器
        try:
            while self.running and self.backend_process.returncode is None:
                await asyncio.sleep(1)
        except KeyboardInterrupt:
            self.log_info("收到中断信号")
        finally:
            await self.stop_servers()
        
        return True
    
    async def start_frontend_only(self) -> bool:
        """只启动前端服务器"""
        self.print_header("🌐 启动前端服务器 - 简化重构版")
        
        # 设置信号处理
        self.setup_signal_handlers()
        
        # 启动前端
        if not await self.start_frontend():
            return False
        
        # 显示访问信息
        self.log_info(f"前端服务器地址: http://{self.server_config['frontend_host']}:{self.server_config['frontend_port']}")
        self.log_info("按 Ctrl+C 停止服务器")
        
        # 监控服务器
        try:
            while self.running and self.frontend_process.returncode is None:
                await asyncio.sleep(1)
        except KeyboardInterrupt:
            self.log_info("收到中断信号")
        finally:
            await self.stop_servers()
        
        return True
    
    def print_access_info(self) -> None:
        """显示访问信息"""
        self.print_header("📖 服务器访问信息")
        self.log_info("服务器已启动，可以通过以下地址访问：")
        print(f"  前端: \033[96mhttp://{self.server_config['frontend_host']}:{self.server_config['frontend_port']}\033[0m")
        print(f"  后端: \033[96mhttp://{self.server_config['backend_host']}:{self.server_config['backend_port']}\033[0m")
        print(f"  API文档: \033[96mhttp://{self.server_config['backend_host']}:{self.server_config['backend_port']}/docs\033[0m")
        self.log_info("按 Ctrl+C 停止所有服务器")


def create_argument_parser() -> argparse.ArgumentParser:
    """创建命令行参数解析器"""
    parser = argparse.ArgumentParser(
        description="开发服务器启动脚本 - 简化重构版",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
🚀 服务器启动选项:
  both       - 启动前后端服务器 (默认)
  backend    - 只启动后端服务器
  frontend   - 只启动前端服务器

🚀 使用示例:
  python scripts/start.py                # 启动前后端
  python scripts/start.py backend        # 只启动后端
  python scripts/start.py frontend       # 只启动前端
  python scripts/start.py --verbose      # 显示详细日志

📋 服务器信息:
  后端服务器: http://127.0.0.1:8000
  前端服务器: http://127.0.0.1:5173
  API文档: http://127.0.0.1:8000/docs
        """
    )

    # 位置参数
    parser.add_argument("target", nargs='?', default="both",
                       choices=["both", "backend", "frontend"],
                       help="要启动的服务器类型 (默认: both)")

    # 可选参数
    parser.add_argument("--verbose", "-v", action="store_true", help="显示详细日志")

    return parser


async def main():
    """主函数"""
    parser = create_argument_parser()
    args = parser.parse_args()

    # 创建服务器管理器
    manager = SimpleServerManager(verbose=args.verbose)

    success = True

    try:
        # 根据参数启动相应服务器
        if args.target == "backend":
            success = await manager.start_backend_only()
        elif args.target == "frontend":
            success = await manager.start_frontend_only()
        else:  # both
            success = await manager.start_both_servers()

    except KeyboardInterrupt:
        manager.log_warning("操作被用户中断")
        success = False
    except Exception as e:
        manager.log_error(f"执行过程中发生异常: {e}")
        if args.verbose:
            import traceback
            manager.log_error(f"详细错误信息:\n{traceback.format_exc()}")
        success = False

    # 输出最终结果
    if success:
        manager.log_success("\n✅ 操作完成")
    else:
        manager.log_error("\n❌ 操作失败")

    sys.exit(0 if success else 1)


if __name__ == '__main__':
    # 检查Python版本
    if sys.version_info < (3, 11):
        print("❌ 错误: 需要Python 3.11或更高版本")
        sys.exit(1)

    # 运行主函数
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⚠️  操作被用户中断")
        sys.exit(1)
