#!/usr/bin/env python3
"""
LLM配置功能测试运行脚本

运行LLM配置相关的所有测试，包括单元测试、集成测试和前端组件测试
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path


def run_command(command: str, cwd: str = None) -> int:
    """运行命令并返回退出码"""
    print(f"运行命令: {command}")
    if cwd:
        print(f"工作目录: {cwd}")
    
    result = subprocess.run(
        command,
        shell=True,
        cwd=cwd,
        capture_output=False
    )
    return result.returncode


def run_backend_tests(test_type: str = "all") -> int:
    """运行后端测试"""
    print("\n" + "="*50)
    print("运行后端测试")
    print("="*50)
    
    backend_dir = Path(__file__).parent.parent / "backend"
    
    if test_type == "unit":
        command = "python -m pytest tests/unit/test_llm_config_api.py -v"
    elif test_type == "integration":
        command = "python -m pytest tests/integration/test_llm_config_integration.py -v"
    elif test_type == "all":
        command = "python -m pytest tests/unit/test_llm_config_api.py tests/integration/test_llm_config_integration.py -v"
    else:
        print(f"未知的测试类型: {test_type}")
        return 1
    
    return run_command(command, str(backend_dir))


def run_frontend_tests() -> int:
    """运行前端测试"""
    print("\n" + "="*50)
    print("运行前端测试")
    print("="*50)
    
    frontend_dir = Path(__file__).parent.parent / "frontend"
    
    # 检查是否安装了依赖
    if not (frontend_dir / "node_modules").exists():
        print("安装前端依赖...")
        if run_command("npm install", str(frontend_dir)) != 0:
            print("前端依赖安装失败")
            return 1
    
    # 运行LLM组件测试
    command = "npm run test -- src/components/llm/__tests__/"
    return run_command(command, str(frontend_dir))


def run_docker_tests() -> int:
    """在Docker环境中运行测试"""
    print("\n" + "="*50)
    print("在Docker环境中运行测试")
    print("="*50)
    
    project_root = Path(__file__).parent.parent
    
    # 启动测试环境
    print("启动Docker测试环境...")
    if run_command("docker-compose -f docker-compose.test.yml up -d", str(project_root)) != 0:
        print("Docker测试环境启动失败")
        return 1
    
    try:
        # 等待服务启动
        print("等待服务启动...")
        run_command("sleep 10")
        
        # 运行后端测试
        backend_result = run_command(
            "docker-compose -f docker-compose.test.yml exec -T backend python -m pytest tests/unit/test_llm_config_api.py tests/integration/test_llm_config_integration.py -v",
            str(project_root)
        )
        
        # 运行前端测试
        frontend_result = run_command(
            "docker-compose -f docker-compose.test.yml exec -T frontend npm run test -- src/components/llm/__tests__/",
            str(project_root)
        )
        
        return max(backend_result, frontend_result)
    
    finally:
        # 清理测试环境
        print("清理Docker测试环境...")
        run_command("docker-compose -f docker-compose.test.yml down", str(project_root))


def run_coverage_report() -> int:
    """生成测试覆盖率报告"""
    print("\n" + "="*50)
    print("生成测试覆盖率报告")
    print("="*50)
    
    backend_dir = Path(__file__).parent.parent / "backend"
    
    # 运行带覆盖率的测试
    command = "python -m pytest tests/unit/test_llm_config_api.py tests/integration/test_llm_config_integration.py --cov=app.api.v1.llm_configs --cov=app.core.models --cov-report=html --cov-report=term"
    
    result = run_command(command, str(backend_dir))
    
    if result == 0:
        print(f"\n覆盖率报告已生成: {backend_dir}/htmlcov/index.html")
    
    return result


def validate_environment() -> bool:
    """验证测试环境"""
    print("验证测试环境...")
    
    # 检查Python环境
    try:
        import pytest
        import fastapi
        import sqlalchemy
        print("✓ Python依赖检查通过")
    except ImportError as e:
        print(f"✗ Python依赖缺失: {e}")
        return False
    
    # 检查项目结构
    project_root = Path(__file__).parent.parent
    required_paths = [
        project_root / "backend" / "app",
        project_root / "frontend" / "src",
        project_root / "backend" / "tests",
    ]
    
    for path in required_paths:
        if not path.exists():
            print(f"✗ 缺少必要路径: {path}")
            return False
    
    print("✓ 项目结构检查通过")
    
    # 检查Docker环境（如果需要）
    if subprocess.run(["which", "docker"], capture_output=True).returncode == 0:
        print("✓ Docker环境可用")
    else:
        print("⚠ Docker环境不可用（跳过Docker测试）")
    
    return True


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="LLM配置功能测试运行器")
    parser.add_argument(
        "--type",
        choices=["unit", "integration", "frontend", "docker", "coverage", "all"],
        default="all",
        help="测试类型"
    )
    parser.add_argument(
        "--skip-validation",
        action="store_true",
        help="跳过环境验证"
    )
    
    args = parser.parse_args()
    
    # 验证环境
    if not args.skip_validation and not validate_environment():
        print("环境验证失败，退出")
        return 1
    
    print(f"\n开始运行LLM配置功能测试 (类型: {args.type})")
    
    exit_code = 0
    
    if args.type in ["unit", "all"]:
        exit_code = max(exit_code, run_backend_tests("unit"))
    
    if args.type in ["integration", "all"]:
        exit_code = max(exit_code, run_backend_tests("integration"))
    
    if args.type in ["frontend", "all"]:
        exit_code = max(exit_code, run_frontend_tests())
    
    if args.type == "docker":
        exit_code = run_docker_tests()
    
    if args.type == "coverage":
        exit_code = run_coverage_report()
    
    print("\n" + "="*50)
    if exit_code == 0:
        print("✓ 所有测试通过")
    else:
        print("✗ 部分测试失败")
    print("="*50)
    
    return exit_code


if __name__ == "__main__":
    sys.exit(main())
