#!/usr/bin/env python3
"""
环境配置脚本 - 简化重构版
根据《0. 项目规范.md》创建，遵循测试脚本简化原则

核心功能：
- 支持多环境配置（test/dev/prod）
- 自动创建和配置conda环境
- 安装Node.js和Playwright依赖
- 生成环境特定的配置文件
- 验证环境配置

简化改进：
- 移除过度抽象层和复杂的类结构
- 内联配置管理，移除复杂的枚举类型
- 直接使用subprocess，减少封装
- 专注于核心功能，避免过度工程化

版本: 3.0 (简化重构版)
创建日期: 2025-07-22
"""

import argparse
import asyncio
import subprocess
import sys
import os
from pathlib import Path
from typing import Dict, List, Optional, Tuple


class SimpleEnvironmentManager:
    """简化的环境配置管理器 - 专注于核心功能"""
    
    def __init__(self, env_type: str = "test", verbose: bool = False):
        """
        初始化环境配置管理器
        
        Args:
            env_type: 环境类型 (test/dev/prod)
            verbose: 是否显示详细日志
        """
        self.env_type = env_type
        self.verbose = verbose
        self.project_root = Path(__file__).parent.parent
        
        # 内联配置 - 避免复杂的配置类
        self.env_config = {
            'test': {
                'conda_env': 'crypto-trader-test',
                'python_version': '3.11',
                'node_version': '18',
                'description': '测试环境',
                'database_url': 'postgresql://postgres:password@localhost:5432/crypto_trader_test',
                'backend_port': 8000,
                'frontend_port': 5173
            },
            'dev': {
                'conda_env': 'crypto-trader-dev',
                'python_version': '3.11',
                'node_version': '18',
                'description': '开发环境',
                'database_url': 'postgresql://postgres:password@localhost:5432/crypto_trader_dev',
                'backend_port': 8001,
                'frontend_port': 5174
            },
            'prod': {
                'conda_env': 'crypto-trader-prod',
                'python_version': '3.11',
                'node_version': '18',
                'description': '生产环境',
                'database_url': 'postgresql://postgres:secure_password@localhost:5432/crypto_trader',
                'backend_port': 8080,
                'frontend_port': 3000
            }
        }
        
        # 验证环境类型
        if env_type not in self.env_config:
            self.log_error(f"不支持的环境类型: {env_type}")
            sys.exit(1)
        
        self.current_config = self.env_config[env_type]
        self.env_name = self.current_config['conda_env']
    
    def log_info(self, message: str) -> None:
        """记录信息日志"""
        print(f"[信息] {message}")
    
    def log_success(self, message: str) -> None:
        """记录成功日志"""
        print(f"\033[92m[成功] {message}\033[0m")
    
    def log_error(self, message: str) -> None:
        """记录错误日志"""
        print(f"\033[91m[错误] {message}\033[0m")
    
    def log_warning(self, message: str) -> None:
        """记录警告日志"""
        print(f"\033[93m[警告] {message}\033[0m")
    
    def print_header(self, title: str) -> None:
        """打印标题头部"""
        print(f"\n{'='*60}")
        print(f"\033[95m{title}\033[0m")
        print(f"{'='*60}")
    
    async def run_command(self, cmd: List[str], timeout: int = 300) -> Tuple[bool, str]:
        """
        运行命令 - 简化版本，直接使用subprocess
        
        Args:
            cmd: 命令列表
            timeout: 超时时间
            
        Returns:
            (是否成功, 输出内容)
        """
        if self.verbose:
            self.log_info(f"执行命令: {' '.join(cmd)}")
        
        try:
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                cwd=self.project_root
            )
            
            try:
                stdout, _ = await asyncio.wait_for(process.communicate(), timeout=timeout)
                output = stdout.decode('utf-8', errors='ignore').strip()
                success = process.returncode == 0
                
                if not success and self.verbose:
                    self.log_error(f"命令执行失败 (返回码: {process.returncode})")
                    if output:
                        print(output)
                
                return success, output
                
            except asyncio.TimeoutError:
                process.kill()
                await process.wait()
                self.log_error(f"命令执行超时 ({timeout}秒)")
                return False, ""
                
        except Exception as e:
            self.log_error(f"命令执行异常: {e}")
            return False, ""
    
    async def check_conda_installed(self) -> bool:
        """检查conda是否已安装"""
        success, output = await self.run_command(["conda", "--version"])
        if success:
            self.log_success(f"✅ Conda已安装: {output}")
            return True
        else:
            self.log_error("❌ Conda未安装，请先安装Miniconda或Anaconda")
            return False
    
    async def create_conda_environment(self) -> bool:
        """创建conda环境"""
        self.log_info(f"🔧 创建conda环境: {self.env_name}")
        
        # 检查环境是否已存在
        success, output = await self.run_command(["conda", "env", "list"])
        if success and self.env_name in output:
            self.log_warning(f"⚠️ 环境 {self.env_name} 已存在，将重新创建")
            # 删除现有环境
            await self.run_command(["conda", "env", "remove", "-n", self.env_name, "-y"])
        
        # 创建新环境
        python_version = self.current_config['python_version']
        cmd = ["conda", "create", "-n", self.env_name, f"python={python_version}", "-y"]
        success, output = await self.run_command(cmd, timeout=600)
        
        if success:
            self.log_success(f"✅ 环境 {self.env_name} 创建成功")
            return True
        else:
            self.log_error(f"❌ 环境 {self.env_name} 创建失败")
            return False
    
    async def install_python_dependencies(self) -> bool:
        """安装Python依赖"""
        self.log_info("📦 安装Python依赖...")
        
        # 检查requirements.txt文件
        requirements_file = self.project_root / "backend" / "requirements.txt"
        if not requirements_file.exists():
            self.log_error(f"❌ 找不到requirements.txt文件: {requirements_file}")
            return False
        
        # 在conda环境中安装依赖
        cmd = ["conda", "run", "-n", self.env_name, "pip", "install", "-r", str(requirements_file)]
        success, output = await self.run_command(cmd, timeout=600)
        
        if success:
            self.log_success("✅ Python依赖安装成功")
            return True
        else:
            self.log_error("❌ Python依赖安装失败")
            return False
    
    async def install_nodejs_dependencies(self) -> bool:
        """安装Node.js和前端依赖"""
        self.log_info("🌐 安装Node.js和前端依赖...")
        
        # 在conda环境中安装Node.js
        node_version = self.current_config['node_version']
        cmd = ["conda", "install", "-n", self.env_name, f"nodejs={node_version}", "-c", "conda-forge", "-y"]
        success, output = await self.run_command(cmd, timeout=600)
        
        if not success:
            self.log_error("❌ Node.js安装失败")
            return False
        
        self.log_success(f"✅ Node.js {node_version} 安装成功")
        
        # 安装前端依赖
        frontend_dir = self.project_root / "frontend"
        if frontend_dir.exists() and (frontend_dir / "package.json").exists():
            self.log_info("📦 安装前端依赖...")
            cmd = ["conda", "run", "-n", self.env_name, "npm", "install"]
            process = await asyncio.create_subprocess_exec(
                *cmd,
                cwd=frontend_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT
            )
            
            stdout, _ = await process.communicate()
            if process.returncode == 0:
                self.log_success("✅ 前端依赖安装成功")
                return True
            else:
                self.log_error("❌ 前端依赖安装失败")
                return False
        else:
            self.log_warning("⚠️ 未找到前端项目，跳过前端依赖安装")
            return True
    
    async def install_playwright(self) -> bool:
        """安装Playwright浏览器"""
        self.log_info("🎭 安装Playwright浏览器...")
        
        # 安装playwright
        cmd = ["conda", "run", "-n", self.env_name, "pip", "install", "playwright"]
        success, output = await self.run_command(cmd, timeout=300)
        
        if not success:
            self.log_error("❌ Playwright安装失败")
            return False
        
        # 安装浏览器
        cmd = ["conda", "run", "-n", self.env_name, "playwright", "install"]
        success, output = await self.run_command(cmd, timeout=600)
        
        if success:
            self.log_success("✅ Playwright浏览器安装成功")
            return True
        else:
            self.log_error("❌ Playwright浏览器安装失败")
            return False

    async def create_config_files(self) -> bool:
        """创建环境配置文件"""
        self.log_info(f"📝 创建 {self.env_type} 环境配置文件...")

        # 创建.env文件
        env_file = self.project_root / f".env.{self.env_type}"
        env_content = self.generate_env_content()

        try:
            with open(env_file, 'w', encoding='utf-8') as f:
                f.write(env_content)
            self.log_success(f"✅ 创建 .env.{self.env_type} 配置文件")

            # 如果是test环境，也创建默认.env文件
            if self.env_type == 'test':
                default_env = self.project_root / ".env"
                with open(default_env, 'w', encoding='utf-8') as f:
                    f.write(env_content)
                self.log_success("✅ 创建默认 .env 配置文件")

            return True
        except Exception as e:
            self.log_error(f"❌ 配置文件创建失败: {e}")
            return False

    def generate_env_content(self) -> str:
        """生成环境配置文件内容"""
        config = self.current_config
        return f"""# crypto_trader {self.env_type.upper()} 环境配置
# 自动生成，请勿手动编辑

# 环境信息
ENVIRONMENT={self.env_type}
APP_NAME=AI Crypto Trading Agent

# 数据库配置
DATABASE_URL={config['database_url']}

# API配置
SECRET_KEY={'test-secret-key' if self.env_type == 'test' else 'change-in-production'}
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# 服务端口
BACKEND_PORT={config['backend_port']}
FRONTEND_PORT={config['frontend_port']}

# 日志配置
LOG_LEVEL={'DEBUG' if self.env_type != 'prod' else 'WARNING'}
DEBUG={'true' if self.env_type != 'prod' else 'false'}
"""

    async def verify_environment(self) -> bool:
        """验证环境配置"""
        self.log_info("✅ 验证环境配置...")

        # 基础验证
        verifications = [
            ("Python", ["conda", "run", "-n", self.env_name, "python", "--version"]),
            ("Node.js", ["conda", "run", "-n", self.env_name, "node", "--version"]),
            ("npm", ["conda", "run", "-n", self.env_name, "npm", "--version"]),
        ]

        all_success = True
        for name, cmd in verifications:
            success, output = await self.run_command(cmd)
            if success:
                self.log_success(f"✅ {name}: {output}")
            else:
                self.log_error(f"❌ {name}: 验证失败")
                all_success = False

        return all_success

    async def setup_environment(self) -> bool:
        """设置完整环境"""
        self.print_header(f"🚀 设置 {self.current_config['description']}")

        steps = [
            ("检查Conda", self.check_conda_installed),
            ("创建环境", self.create_conda_environment),
            ("安装Python依赖", self.install_python_dependencies),
            ("安装Node.js依赖", self.install_nodejs_dependencies),
            ("安装Playwright", self.install_playwright),
            ("创建配置文件", self.create_config_files),
            ("验证环境", self.verify_environment)
        ]

        for i, (step_name, step_func) in enumerate(steps, 1):
            self.log_info(f"📊 步骤 {i}/{len(steps)}: {step_name}")

            try:
                success = await step_func()
                if not success:
                    self.log_error(f"❌ {step_name} 失败")
                    return False
                self.log_success(f"✅ {step_name} 完成")
            except Exception as e:
                self.log_error(f"❌ {step_name} 异常: {e}")
                return False

        self.print_header("🎉 环境配置完成")
        self.show_usage_info()
        return True

    async def validate_only(self) -> bool:
        """仅验证环境，不安装"""
        self.print_header(f"🔍 验证 {self.current_config['description']}")

        # 检查conda环境是否存在
        success, output = await self.run_command(["conda", "env", "list"])
        if not success or self.env_name not in output:
            self.log_error(f"❌ conda环境 {self.env_name} 不存在")
            return False

        self.log_success(f"✅ conda环境 {self.env_name} 存在")

        # 验证环境
        return await self.verify_environment()

    def show_usage_info(self) -> None:
        """显示使用说明"""
        self.print_header("📖 使用说明")
        config = self.current_config

        self.log_info("环境激活命令:")
        print(f"  \033[92mconda activate {self.env_name}\033[0m")

        self.log_info(f"\n{self.env_type.upper()} 环境配置:")
        print(f"  数据库: {config['database_url']}")
        print(f"  后端端口: {config['backend_port']}")
        print(f"  前端端口: {config['frontend_port']}")

        self.log_info("\n常用命令:")
        commands = [
            ("运行所有测试", "python scripts/test.py --all"),
            ("启动开发服务", "python scripts/start.py"),
            ("构建项目", "python scripts/build.py build"),
            ("验证环境", f"python scripts/env.py {self.env_type} --validate-only")
        ]

        for desc, cmd in commands:
            print(f"  {desc}: \033[96m{cmd}\033[0m")


def create_argument_parser() -> argparse.ArgumentParser:
    """创建命令行参数解析器"""
    parser = argparse.ArgumentParser(
        description="环境配置脚本 - 简化重构版",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
🎯 使用示例:
  python scripts/env.py test                    # 配置测试环境
  python scripts/env.py dev --verbose           # 配置开发环境（详细输出）
  python scripts/env.py prod --validate-only    # 验证生产环境
        """
    )

    parser.add_argument("env_type", nargs='?', default="test",
                       choices=["test", "dev", "prod"],
                       help="环境类型 (默认: test)")
    parser.add_argument("--verbose", "-v", action="store_true",
                       help="显示详细输出")
    parser.add_argument("--validate-only", action="store_true",
                       help="仅验证环境，不进行安装")

    return parser


async def main():
    """主函数"""
    parser = create_argument_parser()
    args = parser.parse_args()

    # 创建环境管理器
    manager = SimpleEnvironmentManager(args.env_type, args.verbose)

    success = True

    try:
        if args.validate_only:
            success = await manager.validate_only()
        else:
            success = await manager.setup_environment()

    except KeyboardInterrupt:
        manager.log_warning("操作被用户中断")
        success = False
    except Exception as e:
        manager.log_error(f"执行过程中发生异常: {e}")
        if args.verbose:
            import traceback
            manager.log_error(f"详细错误信息:\n{traceback.format_exc()}")
        success = False

    # 输出最终结果
    if success:
        manager.log_success("\n✅ 操作完成")
    else:
        manager.log_error("\n❌ 操作失败")

    sys.exit(0 if success else 1)


if __name__ == '__main__':
    # 检查Python版本
    if sys.version_info < (3, 11):
        print("❌ 错误: 需要Python 3.11或更高版本")
        sys.exit(1)

    # 运行主函数
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⚠️  操作被用户中断")
        sys.exit(1)
