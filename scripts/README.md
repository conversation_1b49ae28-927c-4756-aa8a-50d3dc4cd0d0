# 🚀 Scripts 目录使用指南 (极简重构版)

这个目录包含了项目的各种脚本工具，用于简化开发、测试和部署流程。所有脚本都已进行极简化重构，遵循透明执行、快速启动的设计原则。

## 🔄 重构亮点 (v4.0 极简重构版)

- **🎯 极简设计**: 脚本是原生命令的薄包装层，代码量减少55%
- **⚡ 快速启动**: 脚本启动到执行在1秒内，无初始化开销
- **🔍 透明执行**: 帮助信息显示所有可用的原生命令
- **📝 直接映射**: 每个参数直接对应一个具体命令
- **🛡️ 零配置**: 硬编码合理的默认参数，避免配置文件
- **🔄 向后兼容**: 保持现有脚本的命令行接口不变
- **📚 易于理解**: 新手5分钟内可理解脚本逻辑

## 📁 目录结构

```
scripts/
├── README.md                    # 脚本使用指南
├── test.py                      # ✅ 统一测试脚本 - 极简委托版（247行）
├── start.py                     # 🔄 开发服务器启动脚本（保持复杂性）
├── build.py                     # ✅ 项目构建脚本 - 极简版（150行）
├── env.py                       # 🔄 环境配置脚本（保持复杂性）
├── db/                          # 数据库管理工具目录
│   ├── run_sqls.py             # ✅ SQL脚本执行器 - 极简版（207行）
│   ├── create_user.sql         # 创建用户账户SQL脚本
│   ├── mock_data.sql           # 生成测试数据SQL脚本
│   ├── cleanup_data.sql        # 清理测试数据SQL脚本
│   └── init.sql                # 数据库初始化脚本
└── test/                        # 分层测试架构目录
    ├── backend/                # 后端测试脚本
    │   └── run_backend_tests.py # ✅ 后端测试运行器 - 极简版（99行）
    └── frontend/               # 前端测试脚本
        ├── run_e2e_tests.py    # ✅ E2E UI测试 - 极简版（138行）
        └── run_api_tests.py    # ✅ API接口测试 - 极简版（121行）
```

## 📊 重构成果

| 脚本类型 | 重构前总行数 | 重构后总行数 | 减少比例 | 状态 |
|---------|-------------|-------------|----------|------|
| 测试脚本 | 1451行 | 605行 | 58% | ✅ 完成 |
| 工具脚本 | 707行 | 357行 | 49% | ✅ 完成 |
| **总计** | **2158行** | **962行** | **55%** | ✅ 完成 |

## 🚀 快速开始

### 统一测试脚本 (架构一致性版)
```bash
# 🎯 基本测试命令 - 100%委托模式
python scripts/test.py --all                # 显示测试命令清单 (不执行)
python scripts/test.py --execute-all        # 实际运行所有测试

# 🧪 分类测试 - 统一委托架构
python scripts/test.py --pytest unit        # 后端单元测试
python scripts/test.py --pytest integration # 后端集成测试
python scripts/test.py --vitest unit         # 前端单元测试
python scripts/test.py --vitest components   # 前端组件测试
python scripts/test.py --playwright api      # 前端API测试
python scripts/test.py --playwright e2e      # 前端E2E测试

# 🔧 环境检查
python scripts/test.py --vitest validate    # 验证前端测试环境
python scripts/test.py --check-env          # 检查测试环境
python scripts/test.py --status             # 显示服务状态
```

### 专门的测试脚本 (极简版)
```bash
# 🧪 后端测试 - 直接执行pytest命令
python scripts/test/backend/run_backend_tests.py unit        # 单元测试
python scripts/test/backend/run_backend_tests.py integration # 集成测试
python scripts/test/backend/run_backend_tests.py all         # 所有后端测试

# 🌐 前端测试 - 直接执行playwright命令
python scripts/test/frontend/run_api_tests.py all      # API测试
python scripts/test/frontend/run_api_tests.py auth     # 认证测试
python scripts/test/frontend/run_e2e_tests.py all      # E2E测试
python scripts/test/frontend/run_e2e_tests.py smoke    # 冒烟测试
```

### 项目构建 (极简版)
```bash
# 🔨 基本构建命令 - 直接执行npm命令
python scripts/build.py install    # 安装依赖 (npm install)
python scripts/build.py build      # 构建项目 (npm run build)
python scripts/build.py clean      # 清理构建文件
python scripts/build.py clean-all  # 清理所有文件（包括依赖）
python scripts/build.py rebuild    # 重新构建（清理+安装+构建）
```

### 数据库管理 (极简版)
```bash
# 🗄️ SQL脚本执行 - 直接执行SQL文件
python scripts/db/run_sqls.py init        # 初始化数据库
python scripts/db/run_sqls.py create-user # 创建用户
python scripts/db/run_sqls.py mock-data   # 生成测试数据
python scripts/db/run_sqls.py cleanup     # 清理数据
python scripts/db/run_sqls.py check-health # 健康检查
python scripts/db/run_sqls.py all         # 执行完整流程
```

### 开发环境启动 (保持复杂性)
```bash
# 🚀 服务启动脚本 - 保持必要的复杂性
python scripts/start.py                    # 启动前后端服务器
python scripts/start.py backend            # 只启动后端
python scripts/start.py frontend           # 只启动前端
python scripts/start.py --verbose          # 显示详细日志
```

## 🎯 极简设计原则

### 核心特点
- **透明执行**: 所有脚本都显示可用的原生命令
- **直接映射**: 每个参数直接对应一个具体命令
- **快速启动**: 脚本启动到执行在1秒内
- **零配置**: 硬编码合理的默认参数
- **易于理解**: 新手5分钟内可理解逻辑

### 脚本分类
- **测试脚本**: 已极简化，代码量减少58%
- **工具脚本**: 已极简化，代码量减少49%
- **服务脚本**: 保持复杂性，确保可靠性

## 📚 使用示例

### 查看可用命令
```bash
# 所有脚本都支持无参数运行，显示帮助信息
python scripts/test.py                              # 显示统一测试脚本帮助
python scripts/build.py                             # 显示构建脚本帮助
python scripts/db/run_sqls.py                       # 显示数据库脚本帮助
python scripts/test/backend/run_backend_tests.py    # 显示后端测试帮助
python scripts/test/frontend/run_api_tests.py       # 显示API测试帮助
python scripts/test/frontend/run_e2e_tests.py       # 显示E2E测试帮助
```

### 常用测试流程
```bash
# 1. 检查环境
python scripts/test.py --check-env

# 2. 运行后端测试
python scripts/test/backend/run_backend_tests.py unit

# 3. 运行前端测试
python scripts/test/frontend/run_api_tests.py all

# 4. 运行所有测试
python scripts/test.py --all
```

## 📚 相关文档

- [脚本重构总结](../docs/脚本重构总结.md) - 完整的重构成果和价值分析
- [项目规范](../docs/0. 项目规范.md) - 极简设计原则和标准
- [前后端测试设计文档](../docs/5.%20前后端测试设计文档.md) - 测试架构说明

## 💡 使用提示

- 所有脚本都支持无参数运行，显示帮助信息
- 脚本启动到执行在1秒内，无初始化开销
- 每个参数直接对应一个具体的原生命令
- 新手可以在5分钟内理解脚本逻辑
- 保持向后兼容性，现有用法不变

---

**版本**: 4.0 极简重构版 | **重构完成**: 2025-07-22 | **代码减少**: 55%



