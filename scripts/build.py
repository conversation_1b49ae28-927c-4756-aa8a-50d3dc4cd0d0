#!/usr/bin/env python3
"""
项目构建脚本 - 极简版
遵循《0. 项目规范.md》的极简设计原则

核心功能：
- 直接执行npm构建命令
- 纯命令执行，无复杂逻辑
- 透明的命令映射

版本: 4.0 (极简版)
创建日期: 2025-07-22
"""

import shutil
import subprocess
import sys
from pathlib import Path


def run_build_command(cmd: list, cwd: Path) -> bool:
    """运行构建命令 - 直接执行，立即显示输出"""
    try:
        result = subprocess.run(cmd, cwd=cwd)
        return result.returncode == 0
    except KeyboardInterrupt:
        print("\n⚠️  构建被用户中断")
        return False
    except Exception as e:
        print(f"❌ 命令执行异常: {e}")
        return False


def clean_directories(dirs_to_clean: list) -> None:
    """清理指定目录"""
    for dir_path in dirs_to_clean:
        if dir_path.exists():
            print(f"🧹 清理目录: {dir_path.name}")
            shutil.rmtree(dir_path)
        else:
            print(f"⚠️  目录不存在，跳过: {dir_path.name}")


def main():
    """主函数 - 极简实现，类似bash脚本"""
    # 获取前端目录
    script_dir = Path(__file__).parent
    frontend_dir = script_dir.parent / "frontend"

    if not frontend_dir.exists():
        print(f"❌ 错误: 前端目录不存在: {frontend_dir}")
        sys.exit(1)

    if not (frontend_dir / "package.json").exists():
        print(f"❌ 错误: package.json不存在: {frontend_dir}/package.json")
        sys.exit(1)

    # 获取构建类型参数
    build_type = sys.argv[1] if len(sys.argv) > 1 else "help"

    print("=== 前端构建命令集合 ===")
    print(f"当前目录: {frontend_dir}")
    print()

    # 直接的参数映射 - 类似bash的case语句
    if build_type == "install":
        print("📦 安装依赖...")
        cmd = ["npm", "install"]

    elif build_type == "build":
        print("🏗️ 构建项目...")
        cmd = ["npm", "run", "build"]

    elif build_type == "clean":
        print("🧹 清理构建文件...")
        # 清理目录
        dirs_to_clean = [
            frontend_dir / "dist",
            frontend_dir / "build",
            frontend_dir / ".vite",
            frontend_dir / "node_modules" / ".cache"
        ]
        clean_directories(dirs_to_clean)
        print("✅ 清理完成")
        return

    elif build_type == "clean-all":
        print("🧹 清理所有文件（包括依赖）...")
        # 清理所有目录
        dirs_to_clean = [
            frontend_dir / "dist",
            frontend_dir / "build",
            frontend_dir / ".vite",
            frontend_dir / "node_modules"
        ]
        clean_directories(dirs_to_clean)
        print("✅ 清理完成")
        return

    elif build_type == "rebuild":
        print("🔄 重新构建项目...")
        # 先清理，再安装，最后构建
        dirs_to_clean = [
            frontend_dir / "dist",
            frontend_dir / "build",
            frontend_dir / ".vite"
        ]
        clean_directories(dirs_to_clean)

        print("📦 安装依赖...")
        if not run_build_command(["npm", "install"], frontend_dir):
            print("❌ 依赖安装失败")
            sys.exit(1)

        print("🏗️ 构建项目...")
        cmd = ["npm", "run", "build"]

    else:
        # 显示所有可用命令 - 透明执行原则
        print("可用的npm构建命令：")
        print()
        print("安装依赖:")
        print("  npm install")
        print()
        print("构建项目:")
        print("  npm run build")
        print()
        print("使用方法:")
        print("  python scripts/build.py install    # 安装依赖")
        print("  python scripts/build.py build      # 构建项目")
        print("  python scripts/build.py clean      # 清理构建文件")
        print("  python scripts/build.py clean-all  # 清理所有文件（包括依赖）")
        print("  python scripts/build.py rebuild    # 重新构建（清理+安装+构建）")
        return

    # 直接执行命令，立即显示输出
    if run_build_command(cmd, frontend_dir):
        print("\n✅ 构建操作完成")
    else:
        print("\n❌ 构建操作失败")
        sys.exit(1)


if __name__ == '__main__':
    # 检查Python版本
    if sys.version_info < (3, 11):
        print("❌ 错误: 需要Python 3.11或更高版本")
        sys.exit(1)

    main()

