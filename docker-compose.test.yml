# Docker Compose配置文件 - 测试环境
# 根据docs/0. 项目规范.md创建
# 支持PostgreSQL、后端、前端三个服务的统一管理和热加载

services:
  # PostgreSQL数据库服务
  postgres-test:
    image: postgres:15-alpine
    container_name: crypto_trader_postgres_test
    environment:
      # 数据库配置
      POSTGRES_DB: crypto_trader_test
      POSTGRES_USER: crypto_trader
      POSTGRES_PASSWORD: test_password_123
      # PostgreSQL配置
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --locale=C"
    ports:
      - "5432:5432"
    volumes:
      # 数据持久化（测试环境使用临时卷）
      - postgres_test_data:/var/lib/postgresql/data
      # 初始化脚本（注释掉，因为可能导致挂载问题）
      # - ./scripts/test/database/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U crypto_trader -d crypto_trader_test"]
      interval: 5s
      timeout: 5s
      retries: 5
      start_period: 10s
    restart: unless-stopped
    networks:
      - crypto_trader_test

  # 后端API服务
  backend-test:
    build:
      context: ./backend
      dockerfile: Dockerfile.dev
    container_name: crypto_trader_backend_test
    environment:
      # 数据库连接配置
      DATABASE_URL: postgresql+asyncpg://crypto_trader:test_password_123@postgres-test:5432/crypto_trader_test
      # 测试环境配置
      TESTING: "true"
      SIMULATION_MODE: "true"
      # API配置
      API_HOST: "0.0.0.0"
      API_PORT: "8000"
      # 日志配置
      LOG_LEVEL: "INFO"
      # CORS配置
      FRONTEND_URL: "http://localhost:5173"
      # OpenAI API配置（测试环境使用占位符）
      OPENAI_API_KEY: "test-key-placeholder"
      # 其他API密钥（测试环境使用占位符）
      BINANCE_API_KEY: "test-binance-key"
      BINANCE_SECRET_KEY: "test-binance-secret"
    ports:
      - "8000:8000"
    volumes:
      # 代码热加载 - 挂载源代码目录
      - ./backend:/app:cached
      # 排除Python缓存和虚拟环境
      - /app/__pycache__
      - /app/.pytest_cache
    depends_on:
      postgres-test:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/v1/health"]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 30s
    restart: unless-stopped
    networks:
      - crypto_trader_test

  # 前端Web服务
  frontend-test:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    container_name: crypto_trader_frontend_test
    environment:
      # Docker环境标识
      DOCKER_ENV: "true"
      # Vite开发服务器配置
      VITE_HOST: "0.0.0.0"
      VITE_PORT: "5173"
      # API配置 - 使用Docker网络中的服务名
      VITE_API_BASE_URL: "http://backend-test:8000"
      API_BASE_URL: "http://backend-test:8000"
      # 测试配置
      PLAYWRIGHT_DOCKER_COMPOSE: "true"
      # 开发模式配置
      NODE_ENV: "development"
      # 热加载配置
      CHOKIDAR_USEPOLLING: "true"
      CHOKIDAR_INTERVAL: "1000"
    ports:
      - "5173:5173"
    volumes:
      # 代码热加载 - 挂载源代码目录
      - ./frontend:/app:cached
      # 排除node_modules（使用容器内的）
      - /app/node_modules
    depends_on:
      backend-test:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5173"]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 30s
    restart: unless-stopped
    networks:
      - crypto_trader_test

# 网络配置
networks:
  crypto_trader_test:
    driver: bridge
    name: crypto_trader_test_network

# 数据卷配置
volumes:
  postgres_test_data:
    name: crypto_trader_postgres_test_data
    driver: local
