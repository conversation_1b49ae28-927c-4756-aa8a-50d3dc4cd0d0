name: 测试和代码质量检查

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: [3.11]
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: crypto_trader_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
    - uses: actions/checkout@v4
    
    - name: 设置Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
    
    - name: 安装系统依赖
      run: |
        sudo apt-get update
        sudo apt-get install -y postgresql-client
    
    - name: 缓存pip依赖
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-
    
    - name: 安装Python依赖
      run: |
        cd backend
        python -m pip install --upgrade pip
        pip install -r requirements.txt
    
    - name: 设置环境变量
      run: |
        echo "TESTING=true" >> $GITHUB_ENV
        echo "SIMULATION_MODE=true" >> $GITHUB_ENV
        echo "DATABASE_URL=postgresql+asyncpg://postgres:postgres@localhost:5432/crypto_trader_test" >> $GITHUB_ENV
        echo "REDIS_URL=redis://localhost:6379/1" >> $GITHUB_ENV
        echo "OPENAI_API_KEY=sk-mock-testing" >> $GITHUB_ENV
        echo "JWT_SECRET_KEY=test-secret-key-for-ci" >> $GITHUB_ENV
    
    - name: 运行数据库迁移
      run: |
        cd backend
        alembic upgrade head
    
    - name: 代码格式检查
      run: |
        cd backend
        black --check app/ tests/
        isort --check-only app/ tests/
    
    - name: 代码风格检查
      run: |
        cd backend
        flake8 app/ tests/
    
    - name: 类型检查
      run: |
        cd backend
        mypy app/
    
    - name: 运行单元测试
      run: |
        cd backend
        python -m pytest tests/unit/ -m unit --cov=app --cov-report=xml --cov-report=term -v
    
    - name: 运行属性测试
      run: |
        cd backend
        python -m pytest tests/unit/test_schemas_properties.py -m property -v
    
    - name: 运行集成测试
      run: |
        cd backend
        python -m pytest tests/integration/ -m integration -v
    
    - name: 运行端到端测试
      run: |
        cd backend
        python -m pytest tests/e2e/ -m e2e -v
    
    - name: 上传覆盖率报告到Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./backend/coverage.xml
        flags: backend
        name: backend-coverage
        fail_ci_if_error: false

  security-scan:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    
    - name: 设置Python
      uses: actions/setup-python@v4
      with:
        python-version: 3.11
    
    - name: 安装安全扫描工具
      run: |
        pip install bandit safety
    
    - name: 运行Bandit安全扫描
      run: |
        cd backend
        bandit -r app/ -f json -o bandit-report.json || true
    
    - name: 运行Safety依赖安全检查
      run: |
        cd backend
        safety check --json --output safety-report.json || true
    
    - name: 上传安全报告
      uses: actions/upload-artifact@v3
      with:
        name: security-reports
        path: |
          backend/bandit-report.json
          backend/safety-report.json

  performance-test:
    runs-on: ubuntu-latest
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: crypto_trader_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
    
    steps:
    - uses: actions/checkout@v4
    
    - name: 设置Python
      uses: actions/setup-python@v4
      with:
        python-version: 3.11
    
    - name: 安装依赖
      run: |
        cd backend
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pytest-benchmark
    
    - name: 设置环境变量
      run: |
        echo "TESTING=true" >> $GITHUB_ENV
        echo "SIMULATION_MODE=true" >> $GITHUB_ENV
        echo "DATABASE_URL=postgresql+asyncpg://postgres:postgres@localhost:5432/crypto_trader_test" >> $GITHUB_ENV
    
    - name: 运行性能测试
      run: |
        cd backend
        python -m pytest tests/ -m slow --benchmark-only --benchmark-json=benchmark.json
    
    - name: 上传性能报告
      uses: actions/upload-artifact@v3
      with:
        name: performance-report
        path: backend/benchmark.json

  docker-test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    
    - name: 构建Docker镜像
      run: |
        cd backend
        docker build -t crypto-trader-backend .
    
    - name: 运行Docker容器测试
      run: |
        docker run --rm \
          -e TESTING=true \
          -e SIMULATION_MODE=true \
          -e DATABASE_URL=sqlite+aiosqlite:///./test.db \
          crypto-trader-backend \
          python -m pytest tests/unit/ -m unit -v

  notify:
    runs-on: ubuntu-latest
    needs: [test, security-scan]
    if: always()
    steps:
    - name: 通知测试结果
      run: |
        if [ "${{ needs.test.result }}" == "success" ] && [ "${{ needs.security-scan.result }}" == "success" ]; then
          echo "✅ 所有测试通过!"
        else
          echo "❌ 测试失败!"
          exit 1
        fi
