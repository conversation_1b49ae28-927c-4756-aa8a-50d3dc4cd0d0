"""
Agent工作流执行追踪服务

提供简化的Agent节点执行追踪功能，包括：
- 节点执行状态记录
- 性能指标收集
- 错误信息追踪
- 异步写入优化
"""

import asyncio
import json
import time
import uuid
from datetime import datetime, timezone
from functools import wraps
from typing import Any, Dict, Optional, Callable, List
from contextlib import asynccontextmanager

import structlog
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, case

from app.core.models import AgentExecutionTrace
from app.core.schemas import AgentState

logger = structlog.get_logger(__name__)


class AgentTraceService:
    """Agent执行追踪服务"""
    
    def __init__(self):
        self._write_queue: List[Dict[str, Any]] = []
        self._batch_size = 10
        self._flush_interval = 5.0  # 5秒
        self._last_flush = time.time()
    
    async def start_trace(
        self,
        db: AsyncSession,
        task_id: uuid.UUID,
        user_id: uuid.UUID,
        node_name: str,
        execution_order: int,
        signal_id: Optional[uuid.UUID] = None,
        input_data: Optional[Dict[str, Any]] = None
    ) -> uuid.UUID:
        """开始节点执行追踪"""
        
        trace_id = uuid.uuid4()
        
        try:
            trace = AgentExecutionTrace(
                id=trace_id,
                task_id=task_id,
                user_id=user_id,
                signal_id=signal_id,
                node_name=node_name,
                execution_order=execution_order,
                status="running",
                started_at=datetime.now(timezone.utc),
                input_data=self._sanitize_data(input_data)
            )
            
            db.add(trace)
            await db.commit()
            
            logger.info(
                "Agent节点追踪开始",
                trace_id=str(trace_id),
                task_id=str(task_id),
                node_name=node_name,
                execution_order=execution_order
            )
            
            return trace_id
            
        except Exception as e:
            logger.error(
                "开始追踪失败",
                trace_id=str(trace_id),
                error=str(e),
                node_name=node_name
            )
            await db.rollback()
            # 追踪失败不影响主流程，返回空ID
            return uuid.uuid4()
    
    async def complete_trace(
        self,
        db: AsyncSession,
        trace_id: uuid.UUID,
        status: str = "completed",
        output_data: Optional[Dict[str, Any]] = None,
        error_data: Optional[Dict[str, Any]] = None,
        performance_metrics: Optional[Dict[str, Any]] = None
    ) -> bool:
        """完成节点执行追踪"""
        
        try:
            # 查找追踪记录
            result = await db.execute(
                select(AgentExecutionTrace).where(AgentExecutionTrace.id == trace_id)
            )
            trace = result.scalar_one_or_none()
            
            if not trace:
                logger.warning("追踪记录不存在", trace_id=str(trace_id))
                return False
            
            # 计算执行时长
            completed_at = datetime.now(timezone.utc)

            # 简化时间计算 - 转换为时间戳进行计算
            try:
                started_timestamp = trace.started_at.timestamp()
                completed_timestamp = completed_at.timestamp()
                duration_ms = int((completed_timestamp - started_timestamp) * 1000)
            except Exception as e:
                logger.warning("时间计算失败，使用默认值", error=str(e))
                duration_ms = 0
            
            # 更新追踪记录
            trace.status = status
            trace.completed_at = completed_at
            trace.duration_ms = duration_ms
            trace.output_data = self._sanitize_data(output_data)
            trace.error_data = self._sanitize_data(error_data)
            trace.performance_metrics = self._sanitize_data(performance_metrics)
            
            await db.commit()
            
            logger.info(
                "Agent节点追踪完成",
                trace_id=str(trace_id),
                node_name=trace.node_name,
                status=status,
                duration_ms=duration_ms
            )
            
            return True

        except Exception as e:
            logger.error(
                "完成追踪失败",
                trace_id=str(trace_id),
                error=str(e)
            )
            await db.rollback()
            return False

    async def update_llm_monitoring_data(
        self,
        db: AsyncSession,
        trace_id: uuid.UUID,
        llm_request_data: Optional[Dict[str, Any]] = None,
        llm_response_data: Optional[Dict[str, Any]] = None,
        llm_token_usage: Optional[Dict[str, Any]] = None,
        llm_metrics: Optional[Dict[str, Any]] = None
    ) -> bool:
        """更新LLM监控数据"""

        try:
            # 查找追踪记录
            result = await db.execute(
                select(AgentExecutionTrace).where(AgentExecutionTrace.id == trace_id)
            )
            trace = result.scalar_one_or_none()

            if not trace:
                logger.warning("追踪记录不存在，无法更新LLM监控数据", trace_id=str(trace_id))
                return False

            # 更新LLM监控字段
            if llm_request_data is not None:
                trace.llm_request_data = self._sanitize_data(llm_request_data)
            if llm_response_data is not None:
                trace.llm_response_data = self._sanitize_data(llm_response_data)
            if llm_token_usage is not None:
                trace.llm_token_usage = self._sanitize_data(llm_token_usage)
            if llm_metrics is not None:
                trace.llm_metrics = self._sanitize_data(llm_metrics)

            await db.commit()

            logger.info(
                "LLM监控数据更新完成",
                trace_id=str(trace_id),
                node_name=trace.node_name,
                has_request_data=llm_request_data is not None,
                has_response_data=llm_response_data is not None,
                has_token_usage=llm_token_usage is not None,
                has_metrics=llm_metrics is not None
            )

            return True

        except Exception as e:
            logger.error(
                "更新LLM监控数据失败",
                trace_id=str(trace_id),
                error=str(e)
            )
            await db.rollback()
            return False
    
    async def get_task_traces(
        self,
        db: AsyncSession,
        task_id: uuid.UUID
    ) -> List[AgentExecutionTrace]:
        """获取任务的所有执行追踪"""
        
        try:
            result = await db.execute(
                select(AgentExecutionTrace)
                .where(AgentExecutionTrace.task_id == task_id)
                .order_by(AgentExecutionTrace.execution_order)
            )
            return result.scalars().all()
            
        except Exception as e:
            logger.error(
                "获取任务追踪失败",
                task_id=str(task_id),
                error=str(e)
            )
            return []
    
    async def get_user_recent_traces(
        self,
        db: AsyncSession,
        user_id: uuid.UUID,
        limit: int = 10,
        offset: int = 0
    ) -> tuple[List[Dict[str, Any]], int]:
        """获取用户最近的执行记录摘要"""
        
        try:
            # 获取任务级别的摘要
            subquery = (
                select(
                    AgentExecutionTrace.task_id,
                    AgentExecutionTrace.signal_id,
                    func.min(AgentExecutionTrace.started_at).label("started_at"),
                    func.max(AgentExecutionTrace.completed_at).label("completed_at"),
                    func.count().label("nodes_count"),
                    func.sum(
                        case((AgentExecutionTrace.status == "failed", 1), else_=0)
                    ).label("failed_nodes")
                )
                .where(AgentExecutionTrace.user_id == user_id)
                .group_by(AgentExecutionTrace.task_id, AgentExecutionTrace.signal_id)
                .subquery()
            )
            
            # 获取分页结果
            result = await db.execute(
                select(subquery)
                .order_by(subquery.c.started_at.desc())
                .limit(limit)
                .offset(offset)
            )
            
            traces = []
            for row in result:
                total_duration_ms = None
                if row.started_at and row.completed_at:
                    total_duration_ms = int(
                        (row.completed_at - row.started_at).total_seconds() * 1000
                    )
                
                # 确定整体状态
                status = "completed"
                if row.failed_nodes > 0:
                    status = "failed"
                elif row.completed_at is None:
                    status = "running"
                
                traces.append({
                    "task_id": row.task_id,
                    "signal_id": row.signal_id,
                    "started_at": row.started_at,
                    "completed_at": row.completed_at,
                    "total_duration_ms": total_duration_ms,
                    "status": status,
                    "nodes_count": row.nodes_count,
                    "failed_nodes": row.failed_nodes
                })
            
            # 获取总数
            count_result = await db.execute(
                select(func.count(func.distinct(AgentExecutionTrace.task_id)))
                .where(AgentExecutionTrace.user_id == user_id)
            )
            total = count_result.scalar() or 0
            
            return traces, total
            
        except Exception as e:
            logger.error(
                "获取用户最近追踪失败",
                user_id=str(user_id),
                error=str(e)
            )
            return [], 0
    
    def _sanitize_data(self, data: Optional[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """清理数据，确保可以JSON序列化"""
        if data is None:
            return None

        try:
            # 尝试JSON序列化来验证数据
            json.dumps(data, default=str)
            return data
        except (TypeError, ValueError):
            # 如果序列化失败，返回字符串表示
            return {"serialized_data": str(data)}

    def _extract_input_data(self, state: AgentState, node_name: str) -> Dict[str, Any]:
        """统一的输入数据提取方法 - 默认使用完整的增强实现"""
        base_data = {
            "task_id": str(state.task_id),
            "user_id": str(state.user_id),
            "signal_id": str(state.signal_id) if hasattr(state, 'signal_id') and state.signal_id else None,
            "node_name": node_name,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }

        # 节点特定的输入数据提取（完整增强实现）
        if node_name == "Parse":
            return {
                **base_data,
                "raw_input": state.raw_input,
                "input_length": len(state.raw_input) if state.raw_input else 0,
                "language_detected": self._detect_language(state.raw_input),
                "preprocessing_applied": True
            }
        elif node_name == "Context":
            return {
                **base_data,
                "parsed_intents_count": len(state.parsed_intents),
                "context_keys": list(state.context.keys()) if state.context else [],
                "has_risk_config": "risk_config" in (state.context or {}),
                "has_active_orders": "active_orders" in (state.context or {})
            }
        elif node_name == "Plan":
            return {
                **base_data,
                "intents_to_process": len(state.parsed_intents),
                "context_available": bool(state.context),
                "retry_count": state.retry_count
            }
        elif node_name == "Risk":
            return {
                **base_data,
                "execution_plans_count": len(state.execution_plan),
                "risk_config_present": "risk_config" in (state.context or {}),
                "total_usd_amount": self._calculate_total_amount(state.execution_plan)
            }
        elif node_name == "Execute":
            return {
                **base_data,
                "plans_to_execute": len(state.execution_plan),
                "exchange_config_present": "exchange_config" in (state.context or {}),
                "simulation_mode": self._is_simulation_mode(state.context)
            }

        return base_data



    def _extract_output_data(self, state: AgentState, node_name: str, result: Any = None) -> Dict[str, Any]:
        """统一的输出数据提取方法 - 使用最优实现"""
        base_data = {
            "task_id": str(state.task_id) if state else None,
            "node_name": node_name,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "execution_successful": True
        }

        # 节点特定的完整输出数据提取
        if node_name == "Parse":
            return {
                **base_data,
                "parsed_intents": [
                    {
                        "intent_type": intent.intent_type,
                        "symbol": intent.symbol,
                        "side": intent.side,
                        "confidence": float(intent.confidence),
                        "has_clarification": bool(intent.clarification_needed)
                    } for intent in (state.parsed_intents if state else [])
                ],
                "total_intents": len(state.parsed_intents) if state else 0,
                "avg_confidence": self._calculate_avg_confidence(state.parsed_intents) if state else 0.0,
                "ambiguous_intents": len([i for i in (state.parsed_intents if state else []) if i.intent_type == "AMBIGUOUS"])
            }
        elif node_name == "Execute":
            return {
                **base_data,
                "execution_results": self._extract_execution_results(result),
                "orders_created": self._count_successful_orders(result),
                "orders_failed": self._count_failed_orders(result),
                "total_execution_time_ms": getattr(result, 'execution_time_ms', None)
            }
        else:
            return {
                **base_data,
                "state_updated": True,
                "result_type": type(result).__name__ if result else None
            }

    def _detect_language(self, text: str) -> str:
        """简单的语言检测"""
        if not text:
            return "unknown"
        # 简单检测中文字符
        chinese_chars = len([c for c in text if '\u4e00' <= c <= '\u9fff'])
        return "chinese" if chinese_chars > len(text) * 0.3 else "english"

    def _calculate_total_amount(self, execution_plan: list) -> float:
        """计算执行计划总金额"""
        total = 0.0
        for plan in execution_plan:
            if hasattr(plan, 'quantity') and plan.quantity:
                total += float(plan.quantity)
        return total

    def _is_simulation_mode(self, context: dict) -> bool:
        """检查是否为模拟模式"""
        exchange_config = context.get("exchange_config") if context else None
        return exchange_config.get("sandbox_mode", True) if exchange_config else True

    def _calculate_avg_confidence(self, intents: list) -> float:
        """计算平均置信度"""
        if not intents:
            return 0.0
        total_confidence = sum(float(intent.confidence) for intent in intents)
        return total_confidence / len(intents)

    def _extract_execution_results(self, result: Any) -> list:
        """提取执行结果详情"""
        if not result:
            return []

        if hasattr(result, 'orders'):
            return [
                {
                    "order_id": str(order.id) if hasattr(order, 'id') else None,
                    "symbol": getattr(order, 'symbol', None),
                    "side": getattr(order, 'side', None),
                    "status": getattr(order, 'status', None),
                    "quantity": float(getattr(order, 'quantity', 0))
                } for order in result.orders
            ]
        return []

    def _count_successful_orders(self, result: Any) -> int:
        """统计成功订单数量"""
        if not result or not hasattr(result, 'orders'):
            return 0
        return len([o for o in result.orders if getattr(o, 'status', None) == 'filled'])

    def _count_failed_orders(self, result: Any) -> int:
        """统计失败订单数量"""
        if not result or not hasattr(result, 'orders'):
            return 0
        return len([o for o in result.orders if getattr(o, 'status', None) == 'failed'])


# 全局追踪服务实例
trace_service = AgentTraceService()


def trace_agent_node(node_name: str):
    """Agent节点追踪装饰器"""
    
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(state: AgentState, db: AsyncSession, *args, **kwargs):
            try:
                # 添加调试日志
                logger.info(
                    "追踪装饰器被调用",
                    node_name=node_name,
                    task_id=str(state.task_id),
                    signal_id=str(state.signal_id) if state.signal_id else None
                )

                # 确定执行顺序
                execution_order = _get_execution_order(node_name)

                # 开始追踪 - 使用统一的数据提取方法（增强模式）
                trace_id = await trace_service.start_trace(
                    db=db,
                    task_id=state.task_id,
                    user_id=state.user_id,
                    node_name=node_name,
                    execution_order=execution_order,
                    signal_id=getattr(state, 'signal_id', None),
                    input_data=trace_service._extract_input_data(state, node_name)
                )
            except Exception as e:
                logger.error("追踪开始失败", error=str(e), node_name=node_name)
                # 如果追踪失败，仍然执行原始函数
                return await func(state, db, *args, **kwargs)
            
            start_time = time.time()

            try:
                # 将trace_id传递给state，以便函数内部可以访问
                state._current_trace_id = trace_id

                # 执行原始函数
                result = await func(state, db, *args, **kwargs)

                # 清理trace_id
                if hasattr(state, '_current_trace_id'):
                    delattr(state, '_current_trace_id')
                
                # 计算执行指标
                execution_time_ms = int((time.time() - start_time) * 1000)
                metrics = _extract_metrics(node_name, result, execution_time_ms)
                
                # 完成追踪 - 使用统一的数据提取方法（增强模式）
                await trace_service.complete_trace(
                    db=db,
                    trace_id=trace_id,
                    status="completed",
                    output_data=trace_service._extract_output_data(state, node_name, result),
                    performance_metrics=metrics
                )
                
                return result
                
            except Exception as e:
                # 记录错误
                execution_time_ms = int((time.time() - start_time) * 1000)
                
                await trace_service.complete_trace(
                    db=db,
                    trace_id=trace_id,
                    status="failed",
                    error_data={
                        "error_type": type(e).__name__,
                        "error_message": str(e),
                        "execution_time_ms": execution_time_ms
                    }
                )
                
                # 重新抛出异常
                raise
        
        return wrapper
    return decorator


def _get_execution_order(node_name: str) -> int:
    """获取节点执行顺序"""
    order_map = {
        "Preprocess": 1,
        "Parse": 2,
        "Context": 3,
        "Plan": 4,
        "Risk": 5,
        "Execute": 6
    }
    return order_map.get(node_name, 99)





def _extract_metrics(node_name: str, result: Any, execution_time_ms: int) -> Dict[str, Any]:
    """提取节点特定指标"""
    base_metrics = {
        "duration_ms": execution_time_ms,
        "retry_count": 0  # 暂时固定为0，后续可以从状态中获取
    }
    
    # 节点特定指标将在后续实现
    return base_metrics
