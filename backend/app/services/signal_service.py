"""
统一的信号处理服务

提供完整的信号处理功能，包括：
- 信号创建和去重
- Agent工作流触发
- 响应格式转换
- Discord和手动信号处理
"""

import hashlib
import uuid
from typing import Optional, Dict, Any, Tuple
from datetime import datetime, timezone

import structlog
from sqlalchemy import select, and_
from sqlalchemy.ext.asyncio import AsyncSession

from ..core.models import Signal, User
from ..core.schemas import (
    CreateSignalRequest,
    ProcessSignalRequest,
    TaskCreatedResponse,
    SignalResponse,
    APIResponse,
    PlatformType,
    MessageType,
    AIParseStatus,
    MessageTypeAI,
)
from .agent_service import AgentService

logger = structlog.get_logger()


# 工具函数 - 无状态，易于测试
def generate_content_hash(content: str) -> str:
    """
    生成内容哈希
    
    Args:
        content: 信号内容
        
    Returns:
        str: SHA-256哈希值（十六进制）
    """
    return hashlib.sha256(content.encode('utf-8')).hexdigest()


def generate_dedup_key(user_id: uuid.UUID, platform: str, content: str) -> str:
    """
    生成去重键
    
    基于用户ID、平台和内容生成唯一的去重键
    
    Args:
        user_id: 用户ID
        platform: 平台类型
        content: 信号内容
        
    Returns:
        str: 去重键（SHA-256哈希值）
    """
    dedup_string = f"{user_id}|{platform}|{content}"
    return hashlib.sha256(dedup_string.encode('utf-8')).hexdigest()


def create_signal_metadata(
    platform_message_id: Optional[str] = None,
    channel_id: Optional[str] = None,
    author_id: Optional[str] = None,
    metadata: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    创建信号元数据
    
    Args:
        platform_message_id: 平台消息ID
        channel_id: 频道ID
        author_id: 作者ID
        metadata: 额外元数据
        
    Returns:
        Dict[str, Any]: 合并后的元数据
    """
    signal_metadata = metadata or {}
    
    if platform_message_id:
        signal_metadata['platform_message_id'] = platform_message_id
    if channel_id:
        signal_metadata['channel_id'] = channel_id
    if author_id:
        signal_metadata['author_id'] = author_id
        
    return signal_metadata


class SignalService:
    """统一的信号处理服务"""

    def __init__(self):
        self.agent_service = AgentService()

    async def create_signal(
        self,
        request: CreateSignalRequest,
        user: User,
        db: AsyncSession,
        platform_message_id: Optional[str] = None,
        channel_id: Optional[str] = None,
        author_id: Optional[str] = None,
        raw_content: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None,
        auto_trigger_agent: bool = False
    ) -> Tuple[SignalResponse, Optional[TaskCreatedResponse], bool]:
        """
        创建信号（支持去重检查）

        Args:
            request: 创建信号请求
            user: 用户对象
            db: 数据库会话
            platform_message_id: 平台消息ID
            channel_id: 频道ID
            author_id: 作者ID
            raw_content: 原始内容
            metadata: 元数据
            auto_trigger_agent: 是否自动触发Agent处理

        Returns:
            Tuple[SignalResponse, Optional[TaskCreatedResponse], bool]: (信号响应, Agent任务响应, 是否重复)
        """
        # 提前提取用户ID，避免在异常处理中访问数据库对象
        user_id_str = str(user.id)

        try:
            # 1. 检查重复
            is_duplicate, original_signal = await self._check_duplicate(
                user.id, request.platform, request.content, db
            )

            # 2. 创建信号记录
            signal = await self._create_signal_record(
                request=request,
                user=user,
                db=db,
                platform_message_id=platform_message_id,
                channel_id=channel_id,
                author_id=author_id,
                raw_content=raw_content,
                metadata=metadata,
                is_duplicate=is_duplicate,
                original_signal=original_signal
            )

            # 3. 转换为响应模型
            signal_response = self._convert_to_response(signal)

            # 4. 如果是新信号且启用自动触发，则启动Agent工作流程
            agent_task_response = None
            if not is_duplicate and auto_trigger_agent:
                # 确保信号已经提交到数据库，避免与Agent任务的数据库操作冲突
                await db.commit()
                agent_task_response = await self._trigger_agent_processing(
                    signal=signal,
                    user=user,
                    db=db
                )

            await logger.ainfo(
                "信号处理完成",
                user_id=user_id_str,
                signal_id=str(signal.id),
                is_new_signal=not is_duplicate,
                is_duplicate=is_duplicate,
                agent_triggered=agent_task_response is not None,
                platform=request.platform
            )

            return signal_response, agent_task_response, is_duplicate

        except Exception as e:
            await logger.aerror(
                "信号处理失败",
                user_id=user_id_str,
                error=str(e),
                request_data=request.model_dump(),
                exc_info=True
            )
            raise

    async def create_manual_signal(
        self,
        request: CreateSignalRequest,
        user: User,
        db: AsyncSession,
        auto_trigger_agent: bool = False
    ) -> APIResponse[SignalResponse]:
        """
        创建手动信号

        Args:
            request: 创建信号请求
            user: 用户对象
            db: 数据库会话
            auto_trigger_agent: 是否自动触发Agent处理

        Returns:
            APIResponse[SignalResponse]: API响应
        """
        # 提前提取用户ID，避免在异常处理中访问数据库对象
        user_id_str = str(user.id)

        try:
            signal_response, agent_task_response, is_duplicate = await self.create_signal(
                request=request,
                user=user,
                db=db,
                auto_trigger_agent=auto_trigger_agent
            )

            message = "成功创建信号"
            if agent_task_response:
                # 提前提取task_id，避免在字符串格式化中访问对象属性
                task_id = agent_task_response.task_id
                message += f"，已启动Agent处理任务 {task_id}"

            return APIResponse.success_response(
                data=signal_response,
                message=message
            )

        except Exception as e:
            await logger.aerror(
                "创建手动信号失败",
                user_id=user_id_str,
                error=str(e),
                request_data=request.model_dump(),
                exc_info=True
            )
            raise

    async def create_discord_signal(
        self,
        content: str,
        user: User,
        db: AsyncSession,
        platform_message_id: str,
        channel_id: str,
        channel_name: str,
        author_id: str,
        author_name: str,
        raw_content: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None,
        auto_trigger_agent: bool = True
    ) -> Tuple[SignalResponse, Optional[TaskCreatedResponse]]:
        """
        创建Discord信号
        
        Args:
            content: 信号内容
            user: 用户对象
            db: 数据库会话
            platform_message_id: 平台消息ID
            channel_id: 频道ID
            channel_name: 频道名称
            author_id: 作者ID
            author_name: 作者名称
            raw_content: 原始内容
            metadata: 元数据
            auto_trigger_agent: 是否自动触发Agent处理
            
        Returns:
            Tuple[SignalResponse, Optional[TaskCreatedResponse]]: (信号响应, Agent任务响应)
        """
        request = CreateSignalRequest(
            platform=PlatformType.DISCORD,
            content=content,
            raw_content=raw_content,
            channel_name=channel_name,
            author_name=author_name,
            message_type=MessageType.TEXT,
            metadata=metadata or {}
        )

        signal_response, agent_task_response, is_duplicate = await self.create_signal(
            request=request,
            user=user,
            db=db,
            platform_message_id=platform_message_id,
            channel_id=channel_id,
            author_id=author_id,
            raw_content=raw_content,
            metadata=metadata,
            auto_trigger_agent=auto_trigger_agent
        )

        # 返回原有格式，保持向后兼容性
        return signal_response, agent_task_response

    async def process_signal_with_agent(
        self,
        request: CreateSignalRequest,
        user: User,
        db: AsyncSession
    ) -> Dict[str, Any]:
        """
        创建信号并自动触发Agent工作流程
        
        Args:
            request: 创建信号请求
            user: 用户对象
            db: 数据库会话
            
        Returns:
            Dict[str, Any]: 包含信号和Agent任务信息的响应
        """
        signal_response, agent_task_response, is_duplicate = await self.create_signal(
            request=request,
            user=user,
            db=db,
            auto_trigger_agent=True
        )

        # 根据实际状态生成正确的响应消息
        if is_duplicate:
            message = "信号已创建（重复信号跳过Agent处理）"
        elif agent_task_response:
            message = "信号已创建并启动Agent处理"
        else:
            message = "信号已创建（Agent处理未启动）"

        return {
            "signal": signal_response.model_dump(),
            "agent_task": agent_task_response.model_dump() if agent_task_response else None,
            "message": message
        }

    # 内部工具方法

    async def _check_duplicate(
        self,
        user_id: uuid.UUID,
        platform: str,
        content: str,
        db: AsyncSession
    ) -> Tuple[bool, Optional[Signal]]:
        """
        检查信号是否重复

        Args:
            user_id: 用户ID
            platform: 平台类型
            content: 信号内容
            db: 数据库会话

        Returns:
            Tuple[bool, Optional[Signal]]: (是否重复, 原始信号对象)
        """
        dedup_key = generate_dedup_key(user_id, platform, content)

        # 查询是否存在相同的去重键
        stmt = select(Signal).where(
            and_(
                Signal.user_id == user_id,
                Signal.dedup_key == dedup_key,
                Signal.is_duplicate == False
            )
        )

        result = await db.execute(stmt)
        original_signal = result.scalar_one_or_none()

        is_duplicate = original_signal is not None

        if is_duplicate:
            await logger.ainfo(
                "检测到重复信号",
                user_id=str(user_id),
                platform=platform,
                dedup_key=dedup_key,
                original_signal_id=str(original_signal.id),
                content_preview=content[:100]
            )

        return is_duplicate, original_signal

    async def _create_signal_record(
        self,
        request: CreateSignalRequest,
        user: User,
        db: AsyncSession,
        platform_message_id: Optional[str] = None,
        channel_id: Optional[str] = None,
        author_id: Optional[str] = None,
        raw_content: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None,
        is_duplicate: bool = False,
        original_signal: Optional[Signal] = None
    ) -> Signal:
        """
        创建信号数据库记录

        Args:
            request: 创建信号请求
            user: 用户对象
            db: 数据库会话
            platform_message_id: 平台消息ID
            channel_id: 频道ID
            author_id: 作者ID
            raw_content: 原始内容
            metadata: 元数据
            is_duplicate: 是否为重复信号
            original_signal: 原始信号对象（如果是重复信号）

        Returns:
            Signal: 创建的信号对象
        """
        # 生成哈希和去重键
        content_hash = generate_content_hash(request.content)
        dedup_key = generate_dedup_key(user.id, request.platform, request.content)

        # 创建信号元数据
        signal_metadata = create_signal_metadata(
            platform_message_id=platform_message_id,
            channel_id=channel_id,
            author_id=author_id,
            metadata=metadata or request.metadata
        )

        # 创建信号对象
        signal = Signal(
            user_id=user.id,
            platform=request.platform,
            platform_message_id=platform_message_id,
            channel_id=channel_id,
            channel_name=request.channel_name,
            author_id=author_id,
            author_name=request.author_name,
            content=request.content,
            raw_content=raw_content or request.raw_content,
            message_type=request.message_type if hasattr(request, 'message_type') else 'text',
            signal_metadata=signal_metadata,
            content_hash=content_hash,
            dedup_key=dedup_key,
            is_duplicate=is_duplicate,
            original_signal_id=original_signal.id if original_signal else None,
            agent_processing_status="skipped" if is_duplicate else "pending",
            is_processed=False,
            ai_parse_status="pending",
            message_type_ai="normal_message",
            llm_service=None,
        )

        db.add(signal)
        await db.commit()
        await db.refresh(signal)

        log_message = "创建重复信号记录" if is_duplicate else "创建新信号"
        await logger.ainfo(
            log_message,
            user_id=str(user.id),
            signal_id=str(signal.id),
            original_signal_id=str(original_signal.id) if original_signal else None,
            platform=request.platform,
            dedup_key=dedup_key
        )

        return signal

    async def _trigger_agent_processing(
        self,
        signal: Signal,
        user: User,
        db: AsyncSession
    ) -> Optional[TaskCreatedResponse]:
        """
        触发Agent处理

        Args:
            signal: 信号对象
            user: 用户对象
            db: 数据库会话

        Returns:
            Optional[TaskCreatedResponse]: Agent任务响应
        """
        try:
            # 创建Agent处理请求
            agent_request = ProcessSignalRequest(
                raw_input=signal.content,
                context={
                    "signal_id": str(signal.id),
                    "platform": signal.platform,
                    "channel_name": signal.channel_name,
                    "author_name": signal.author_name,
                    "metadata": signal.signal_metadata or {}
                }
            )

            # 启动Agent工作流程
            task_response = await self.agent_service.process_signal(
                request=agent_request,
                user=user,
                db=db
            )

            # 注意：不在这里更新信号状态，让Agent任务自己管理状态
            # Agent任务会在开始时更新状态为"processing"，完成时更新为"completed"或"failed"

            await logger.ainfo(
                "Agent处理已启动",
                signal_id=str(signal.id),
                task_id=task_response.task_id,
                user_id=str(user.id)
            )

            return task_response

        except Exception as e:
            # 更新信号状态为失败
            await self._update_signal_agent_status(
                signal_id=signal.id,
                agent_task_id=uuid.uuid4(),  # 临时ID
                status="failed",
                db=db
            )

            await logger.aerror(
                "Agent处理启动失败",
                signal_id=str(signal.id),
                user_id=str(user.id),
                error=str(e),
                exc_info=True
            )
            return None

    async def _update_signal_agent_status(
        self,
        signal_id: uuid.UUID,
        agent_task_id: uuid.UUID,
        status: str,
        db: AsyncSession
    ) -> bool:
        """
        更新信号的Agent处理状态

        Args:
            signal_id: 信号ID
            agent_task_id: Agent任务ID
            status: 处理状态
            db: 数据库会话

        Returns:
            bool: 是否更新成功
        """
        try:
            stmt = select(Signal).where(Signal.id == signal_id)
            result = await db.execute(stmt)
            signal = result.scalar_one_or_none()

            if not signal:
                await logger.aerror(
                    "信号不存在",
                    signal_id=str(signal_id)
                )
                return False

            signal.agent_task_id = agent_task_id
            signal.agent_processing_status = status
            # updated_at会由模型的onupdate=utc_now自动更新

            await db.commit()

            await logger.ainfo(
                "更新信号Agent状态",
                signal_id=str(signal_id),
                agent_task_id=str(agent_task_id),
                status=status
            )

            return True

        except Exception as e:
            from ..core.utils import safe_rollback_db
            await safe_rollback_db(db)
            await logger.aerror(
                "更新信号Agent状态失败",
                signal_id=str(signal_id),
                error=str(e),
                exc_info=True
            )
            return False

    def _convert_to_response(self, signal: Signal) -> SignalResponse:
        """
        转换为信号响应模型

        Args:
            signal: 信号对象

        Returns:
            SignalResponse: 信号响应
        """
        return SignalResponse(
            id=str(signal.id),
            platform=signal.platform,
            channel_name=signal.channel_name,
            author_name=signal.author_name,
            content=signal.content,
            message_type=signal.message_type,
            confidence=float(signal.confidence) if signal.confidence else None,
            signal_strength=float(signal.confidence) if signal.confidence else None,
            ai_parse_status=AIParseStatus(signal.ai_parse_status)
            if signal.ai_parse_status
            else AIParseStatus.PENDING,
            message_type_ai=MessageTypeAI(signal.message_type_ai)
            if signal.message_type_ai
            else MessageTypeAI.AMBIGUOUS,
            llm_service=signal.llm_service,
            is_processed=signal.is_processed,
            created_at=signal.created_at,
        )


# 全局实例
signal_service = SignalService()
