"""
条件订单服务 - 处理条件订单的创建、监控和触发
"""
import asyncio
import logging
from datetime import datetime, timedelta, timezone
from decimal import Decimal
from typing import Any, Dict, List, Optional
from uuid import UUID

from sqlalchemy import and_, or_, select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from app.core.models import ConditionalOrder, Order, User
from app.core.schemas import (
    ConditionalOrderCreate,
    ConditionalOrderResponse,
    ConditionalOrderUpdate,
)
from app.services.exchange import ExchangeService
from app.core.ws_manager import WebSocketManager

logger = logging.getLogger(__name__)


class ConditionalOrderService:
    """条件订单服务"""

    def __init__(self, db: AsyncSession):
        self.db = db
        self.exchange_service = ExchangeService()
        self.ws_manager = WebSocketManager()
        self._monitoring_tasks: Dict[UUID, asyncio.Task] = {}

    async def create_conditional_order(
        self, user_id: int, order_data: ConditionalOrderCreate
    ) -> ConditionalOrder:
        """创建条件订单"""

        # 验证触发条件
        await self._validate_trigger_condition(order_data.trigger_condition)

        # 验证执行计划
        await self._validate_action_plan(order_data.action_plan)

        conditional_order = ConditionalOrder(
            user_id=user_id,
            symbol=order_data.symbol,
            trigger_condition=order_data.trigger_condition,
            action_plan=order_data.action_plan,
            status="PENDING",
        )

        self.db.add(conditional_order)
        await self.db.commit()
        await self.db.refresh(conditional_order)

        # 启动监控任务
        await self._start_monitoring(conditional_order)

        logger.info(
            f"Created conditional order {conditional_order.id} for user {user_id}"
        )

        return conditional_order

    async def update_conditional_order(
        self, order_id: UUID, user_id: int, update_data: ConditionalOrderUpdate
    ) -> Optional[ConditionalOrder]:
        """更新条件订单"""

        stmt = select(ConditionalOrder).where(
            and_(
                ConditionalOrder.id == order_id,
                ConditionalOrder.user_id == user_id,
                ConditionalOrder.status == "PENDING",
            )
        )
        result = await self.db.execute(stmt)
        conditional_order = result.scalar_one_or_none()

        if not conditional_order:
            return None

        # 停止当前监控
        await self._stop_monitoring(order_id)

        # 更新字段
        if update_data.trigger_condition:
            await self._validate_trigger_condition(update_data.trigger_condition)
            conditional_order.trigger_condition = update_data.trigger_condition.dict()

        if update_data.action_plan:
            await self._validate_action_plan(update_data.action_plan)
            conditional_order.action_plan = update_data.action_plan.dict()

        conditional_order.updated_at = datetime.utcnow()

        await self.db.commit()

        # 重新启动监控
        await self._start_monitoring(conditional_order)

        logger.info(f"Updated conditional order {order_id}")

        return conditional_order

    async def cancel_conditional_order(self, order_id: UUID, user_id: int) -> bool:
        """取消条件订单"""

        stmt = select(ConditionalOrder).where(
            and_(
                ConditionalOrder.id == order_id,
                ConditionalOrder.user_id == user_id,
                ConditionalOrder.status == "PENDING",
            )
        )
        result = await self.db.execute(stmt)
        conditional_order = result.scalar_one_or_none()

        if not conditional_order:
            return False

        # 停止监控
        await self._stop_monitoring(order_id)

        # 更新状态
        conditional_order.status = "CANCELLED"
        conditional_order.updated_at = datetime.utcnow()

        await self.db.commit()

        logger.info(f"Cancelled conditional order {order_id}")

        return True

    async def get_user_conditional_orders(
        self,
        user_id: int,
        status: Optional[str] = None,
        symbol: Optional[str] = None,
        limit: int = 20,
        offset: int = 0,
    ) -> tuple[List[ConditionalOrder], int]:
        """获取用户的条件订单列表"""

        conditions = [ConditionalOrder.user_id == user_id]

        if status:
            conditions.append(ConditionalOrder.status == status)

        if symbol:
            conditions.append(ConditionalOrder.symbol == symbol)

        # 查询总数
        count_stmt = select(ConditionalOrder).where(and_(*conditions))
        count_result = await self.db.execute(count_stmt)
        total = len(count_result.fetchall())

        # 查询数据
        stmt = (
            select(ConditionalOrder)
            .where(and_(*conditions))
            .order_by(ConditionalOrder.created_at.desc())
            .limit(limit)
            .offset(offset)
        )

        result = await self.db.execute(stmt)
        orders = result.scalars().all()

        return list(orders), total

    async def toggle_conditional_order(
        self, order_id: UUID, user_id: int, enabled: bool
    ) -> Optional[ConditionalOrder]:
        """启用/禁用条件订单"""

        stmt = select(ConditionalOrder).where(
            and_(
                ConditionalOrder.id == order_id,
                ConditionalOrder.user_id == user_id,
            )
        )
        result = await self.db.execute(stmt)
        conditional_order = result.scalar_one_or_none()

        if not conditional_order:
            return None

        if enabled and conditional_order.status == "CANCELLED":
            conditional_order.status = "PENDING"
            await self._start_monitoring(conditional_order)
        elif not enabled and conditional_order.status == "PENDING":
            conditional_order.status = "CANCELLED"
            await self._stop_monitoring(order_id)

        conditional_order.updated_at = datetime.utcnow()
        await self.db.commit()

        return conditional_order

    async def _validate_trigger_condition(self, condition: Dict[str, Any]):
        """验证触发条件"""
        condition_type = condition.get("condition_type")
        if condition_type == "price":
            target_price = condition.get("target_price")
            if not target_price or float(target_price) <= 0:
                raise ValueError("Invalid target price")
        elif condition_type == "time":
            target_time = condition.get("target_time")
            if not target_time:
                raise ValueError("Target time is required")
            # 如果是字符串，尝试解析为datetime
            if isinstance(target_time, str):
                try:
                    target_time = datetime.fromisoformat(
                        target_time.replace("Z", "+00:00")
                    )
                except ValueError:
                    raise ValueError("Invalid target time format")
            # 确保时区一致性比较
            current_time = datetime.utcnow()
            if target_time.tzinfo is not None:
                # 如果目标时间有时区信息，将当前时间也转换为UTC时区
                current_time = current_time.replace(tzinfo=timezone.utc)
            if target_time <= current_time:
                raise ValueError("Target time must be in the future")
        # 可以添加更多条件类型的验证

    async def _validate_action_plan(self, plan: Dict[str, Any]):
        """验证执行计划"""
        action_type = plan.get("action_type")
        if action_type == "market_order":
            quantity = plan.get("quantity")
            if not quantity or float(quantity) <= 0:
                raise ValueError("Invalid quantity")
        elif action_type == "limit_order":
            quantity = plan.get("quantity")
            price = plan.get("price")
            if not quantity or float(quantity) <= 0:
                raise ValueError("Invalid quantity")
            if not price or float(price) <= 0:
                raise ValueError("Invalid price")
        # 可以添加更多动作类型的验证

    async def _start_monitoring(self, conditional_order: ConditionalOrder):
        """启动条件监控"""
        if conditional_order.id in self._monitoring_tasks:
            return

        task = asyncio.create_task(self._monitor_condition(conditional_order))
        self._monitoring_tasks[conditional_order.id] = task

        logger.info(f"Started monitoring conditional order {conditional_order.id}")

    async def _stop_monitoring(self, order_id: UUID):
        """停止条件监控"""
        if order_id in self._monitoring_tasks:
            task = self._monitoring_tasks.pop(order_id)
            task.cancel()
            try:
                # 检查是否是真正的asyncio.Task
                if hasattr(task, "__await__") and not hasattr(task, "_mock_name"):
                    await task
            except asyncio.CancelledError:
                pass

            logger.info(f"Stopped monitoring conditional order {order_id}")

    async def _monitor_condition(self, conditional_order: ConditionalOrder):
        """监控条件是否满足"""
        check_interval = 5  # 基础检查间隔（秒）
        error_count = 0
        max_errors = 10

        try:
            logger.info(f"开始监控条件订单 {conditional_order.id}")

            while True:
                try:
                    # 检查订单是否仍然有效
                    if not await self._is_order_still_valid(conditional_order):
                        logger.info(f"条件订单 {conditional_order.id} 已失效，停止监控")
                        break

                    # 检查条件是否满足
                    if await self._check_trigger_condition(conditional_order):
                        logger.info(f"条件订单 {conditional_order.id} 触发条件已满足")
                        await self._trigger_conditional_order(conditional_order)
                        break

                    # 重置错误计数
                    error_count = 0

                    # 动态调整检查间隔
                    adjusted_interval = self._calculate_check_interval(
                        conditional_order
                    )
                    await asyncio.sleep(adjusted_interval)

                except Exception as e:
                    error_count += 1
                    logger.warning(
                        f"监控条件订单 {conditional_order.id} 时发生错误 (错误次数: {error_count}): {str(e)}"
                    )

                    if error_count >= max_errors:
                        logger.error(f"条件订单 {conditional_order.id} 监控错误次数过多，停止监控")
                        await self._handle_monitoring_failure(conditional_order)
                        break

                    # 错误时使用指数退避
                    await asyncio.sleep(min(check_interval * (2**error_count), 60))

        except asyncio.CancelledError:
            logger.info(f"条件订单 {conditional_order.id} 监控已取消")
        except Exception as e:
            logger.error(f"条件订单 {conditional_order.id} 监控发生严重错误: {str(e)}")
            await self._handle_monitoring_failure(conditional_order)

    async def _check_trigger_condition(
        self, conditional_order: ConditionalOrder
    ) -> bool:
        """检查触发条件是否满足"""
        condition = conditional_order.trigger_condition
        condition_type = condition.get("condition_type")

        if condition_type == "price":
            current_price = await self.exchange_service.get_symbol_price(
                conditional_order.symbol
            )
            target_price = Decimal(str(condition.get("target_price")))
            operator = condition.get("operator", ">=")

            if operator == ">=":
                return current_price >= target_price
            elif operator == "<=":
                return current_price <= target_price
            elif operator == "==":
                # 价格相等的容差范围
                tolerance = target_price * Decimal("0.001")  # 0.1%
                return abs(current_price - target_price) <= tolerance

        elif condition_type == "time":
            target_time = condition.get("target_time")
            if isinstance(target_time, str):
                target_time = datetime.fromisoformat(target_time.replace("Z", "+00:00"))
            return datetime.utcnow() >= target_time

        return False

    async def _is_order_still_valid(self, conditional_order: ConditionalOrder) -> bool:
        """检查条件订单是否仍然有效"""
        try:
            # 重新从数据库获取最新状态
            stmt = select(ConditionalOrder).where(
                ConditionalOrder.id == conditional_order.id
            )
            result = await self.db.execute(stmt)
            current_order = result.scalar_one_or_none()

            if not current_order:
                return False

            # 检查状态是否仍为PENDING
            if current_order.status != "PENDING":
                return False

            # 检查是否过期（如果设置了过期时间）
            if hasattr(current_order, "expires_at") and current_order.expires_at:
                if datetime.utcnow() > current_order.expires_at:
                    # 标记为过期
                    current_order.status = "EXPIRED"
                    await self.db.commit()
                    return False

            return True

        except Exception as e:
            logger.error(f"检查条件订单有效性失败 (订单ID: {conditional_order.id}): {str(e)}")
            return False

    def _calculate_check_interval(self, conditional_order: ConditionalOrder) -> float:
        """根据条件类型动态计算检查间隔"""
        condition = conditional_order.trigger_condition
        condition_type = condition.get("condition_type")

        if condition_type == "price":
            # 价格条件：根据目标价格与当前价格的差距调整间隔
            try:
                target_price = Decimal(str(condition.get("target_price")))
                # 这里可以获取当前价格来计算差距，暂时使用固定间隔
                return 5.0  # 价格监控较频繁
            except:
                return 5.0
        elif condition_type == "time":
            # 时间条件：根据剩余时间调整间隔
            try:
                target_time = condition.get("target_time")
                if isinstance(target_time, str):
                    target_time = datetime.fromisoformat(
                        target_time.replace("Z", "+00:00")
                    )

                remaining_seconds = (target_time - datetime.utcnow()).total_seconds()

                if remaining_seconds <= 60:  # 1分钟内
                    return 1.0
                elif remaining_seconds <= 300:  # 5分钟内
                    return 5.0
                elif remaining_seconds <= 3600:  # 1小时内
                    return 30.0
                else:
                    return 60.0  # 超过1小时，每分钟检查一次
            except:
                return 30.0
        else:
            return 10.0  # 默认间隔

    async def _handle_monitoring_failure(self, conditional_order: ConditionalOrder):
        """处理监控失败的情况"""
        try:
            # 更新订单状态为错误
            conditional_order.status = "ERROR"
            conditional_order.updated_at = datetime.utcnow()
            await self.db.commit()

            # 发送通知给用户
            await self._notify_user_monitoring_failure(conditional_order)

            logger.error(f"条件订单 {conditional_order.id} 监控失败，已标记为错误状态")

        except Exception as e:
            logger.error(f"处理监控失败时发生错误", order_id=conditional_order.id, error=str(e))

    async def _notify_user_monitoring_failure(
        self, conditional_order: ConditionalOrder
    ):
        """通知用户监控失败"""
        try:
            # 这里可以集成WebSocket通知或其他通知机制
            from ..core.ws_events import ws_event_manager
            from ..core.ws_schemas import NotificationEvent

            notification = NotificationEvent(
                event_type="NOTIFICATION",
                payload={
                    "type": "conditional_order_error",
                    "title": "条件订单监控失败",
                    "message": f"条件订单 {conditional_order.id} 监控过程中发生错误，请检查订单状态",
                    "order_id": str(conditional_order.id),
                    "symbol": conditional_order.symbol,
                    "timestamp": datetime.utcnow().isoformat(),
                },
            )

            await ws_event_manager.send_to_user(conditional_order.user_id, notification)

        except Exception as e:
            logger.error(f"发送监控失败通知时发生错误", error=str(e))

    async def _trigger_conditional_order(self, conditional_order: ConditionalOrder):
        """触发条件订单"""
        try:
            # 更新状态
            conditional_order.status = "TRIGGERED"
            conditional_order.triggered_at = datetime.utcnow()

            # 执行动作计划
            action_plan = conditional_order.action_plan
            action_type = action_plan.get("action_type")

            if action_type in ["market_order", "limit_order"]:
                # 记录触发信息（实际订单创建需要OrderService）
                logger.info(
                    f"Conditional order {conditional_order.id} triggered, action: {action_type}"
                )

            await self.db.commit()

            # 发送WebSocket通知
            await self.ws_manager.broadcast_to_user(
                conditional_order.user_id,
                {
                    "type": "CONDITIONAL_ORDER_TRIGGERED",
                    "data": {
                        "conditional_order_id": str(conditional_order.id),
                        "symbol": conditional_order.symbol,
                        "triggered_at": conditional_order.triggered_at.isoformat(),
                    },
                },
            )

        except Exception as e:
            logger.error(
                f"Failed to trigger conditional order {conditional_order.id}: {e}"
            )

            # 标记为错误状态
            conditional_order.status = "ERROR"
            await self.db.commit()

        finally:
            # 停止监控
            await self._stop_monitoring(conditional_order.id)

    async def shutdown(self):
        """
        关闭条件订单服务，清理所有监控任务
        """
        logger.info("Shutting down conditional order service...")

        # 取消所有监控任务
        for order_id, task in list(self._monitoring_tasks.items()):
            try:
                task.cancel()
                await task
            except asyncio.CancelledError:
                pass
            except Exception as e:
                logger.warning(
                    f"Error cancelling monitoring task for order {order_id}: {e}"
                )

        # 清理任务字典
        self._monitoring_tasks.clear()

        logger.info("Conditional order service shutdown complete")
