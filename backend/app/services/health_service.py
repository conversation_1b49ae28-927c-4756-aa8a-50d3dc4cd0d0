"""
健康检查服务
提供系统健康状态检查和监控功能
"""

import asyncio
import logging
import psutil
import time
from datetime import datetime, timezone
from typing import Dict, Any, Optional

from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db

logger = logging.getLogger(__name__)


class HealthService:
    """健康检查服务类"""

    def __init__(self):
        self.start_time = time.time()

    async def get_health_status(self) -> Dict[str, Any]:
        """获取系统健康状态"""
        try:
            # 检查各个组件状态
            database_status = await self.check_database()
            cache_status = await self.check_cache()

            # 确定整体健康状态
            if database_status and cache_status:
                overall_status = "healthy"
            elif database_status or cache_status:
                overall_status = "degraded"
            else:
                overall_status = "unhealthy"

            return {
                "status": overall_status,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "components": {
                    "database": "healthy" if database_status else "unhealthy",
                    "cache": "healthy" if cache_status else "unhealthy",
                },
            }
        except Exception as e:
            logger.error(f"Health check failed: {e}")
            return {
                "status": "unhealthy",
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "error": str(e),
            }

    async def check_database(self) -> bool:
        """检查数据库连接状态"""
        try:
            async for session in get_db():
                # 执行简单查询测试连接
                result = await session.execute(text("SELECT 1"))
                return result.scalar() == 1
        except Exception as e:
            logger.warning(f"Database health check failed: {e}")
            return False

    def check_database_sync(self) -> bool:
        """同步检查数据库连接状态（用于测试）"""
        try:
            # 这是一个简化的同步检查，实际实现可能需要更复杂的逻辑
            return True  # 简化实现，实际应该检查数据库连接
        except Exception as e:
            logger.warning(f"Database sync health check failed: {e}")
            return False

    async def check_cache(self) -> bool:
        """检查缓存服务状态"""
        try:
            from app.core.cache import cache_service

            return cache_service.is_connected()
        except Exception as e:
            logger.warning(f"Cache health check failed: {e}")
            return False

    async def get_system_metrics(self) -> Dict[str, Any]:
        """获取系统指标"""
        try:
            # 计算运行时间
            uptime = time.time() - self.start_time

            # 获取内存使用情况
            memory_info = psutil.virtual_memory()
            memory_usage_mb = memory_info.used / (1024 * 1024)

            # 获取CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)

            return {
                "uptime": uptime,
                "memory_usage_mb": memory_usage_mb,
                "memory_total_mb": memory_info.total / (1024 * 1024),
                "memory_percent": memory_info.percent,
                "cpu_percent": cpu_percent,
                "timestamp": datetime.now(timezone.utc).isoformat(),
            }
        except Exception as e:
            logger.error(f"Failed to get system metrics: {e}")
            return {
                "uptime": time.time() - self.start_time,
                "memory_usage_mb": 0,
                "error": str(e),
            }

    async def get_detailed_status(self) -> Dict[str, Any]:
        """获取详细的系统状态"""
        try:
            health_status = await self.get_health_status()
            system_metrics = await self.get_system_metrics()

            return {
                "health": health_status,
                "metrics": system_metrics,
                "version": "1.0.0",  # 应该从配置或环境变量获取
                "environment": "development",  # 应该从环境变量获取
                "services": {
                    "agent_service": await self.check_agent_service(),
                    "exchange_service": await self.check_exchange_service(),
                },
            }
        except Exception as e:
            logger.error(f"Failed to get detailed status: {e}")
            return {
                "error": str(e),
                "timestamp": datetime.now(timezone.utc).isoformat(),
            }

    async def check_agent_service(self) -> str:
        """检查Agent服务状态"""
        try:
            # 这里应该检查Agent服务的实际状态
            # 暂时返回健康状态
            return "healthy"
        except Exception:
            return "unhealthy"

    async def check_exchange_service(self) -> str:
        """检查交易所服务状态"""
        try:
            # 这里应该检查交易所服务的实际状态
            # 暂时返回健康状态
            return "healthy"
        except Exception:
            return "unhealthy"

    async def run_health_checks(self) -> Dict[str, Any]:
        """运行所有健康检查"""
        checks = {
            "database": self.check_database(),
            "cache": self.check_cache(),
            "agent_service": self.check_agent_service(),
            "exchange_service": self.check_exchange_service(),
        }

        # 并发执行所有检查
        results = {}
        for name, check_coro in checks.items():
            try:
                if asyncio.iscoroutine(check_coro):
                    result = await check_coro
                else:
                    result = check_coro
                results[name] = "healthy" if result else "unhealthy"
            except Exception as e:
                logger.error(f"Health check {name} failed: {e}")
                results[name] = "unhealthy"

        return results


# 全局健康服务实例
health_service = HealthService()


async def get_health_status() -> Dict[str, Any]:
    """获取健康状态的便捷函数"""
    return await health_service.get_health_status()


async def get_system_metrics() -> Dict[str, Any]:
    """获取系统指标的便捷函数"""
    return await health_service.get_system_metrics()


async def get_detailed_status() -> Dict[str, Any]:
    """获取详细状态的便捷函数"""
    return await health_service.get_detailed_status()
