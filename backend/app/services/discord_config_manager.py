"""
Discord配置管理器
基于简化设计原则，专注于Discord配置的动态管理
注意：当前版本为简化实现，使用现有的单一监听器架构
"""
import asyncio
import logging
from typing import Dict, List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from sqlalchemy.orm import selectinload

from ..core.models import DiscordConfig, User
from ..core.database import AsyncSessionLocal
from ..core.security import decrypt_sensitive_data
from .discord_listener import DiscordListenerManager, discord_manager

logger = logging.getLogger(__name__)


class DiscordConfigManager:
    """Discord配置管理器 - 简化版本，基于现有单一监听器架构"""

    def __init__(self):
        self.current_config: Optional[DiscordConfig] = None
        self._running = False
    
    async def start(self):
        """启动配置管理器"""
        if self._running:
            return

        self._running = True
        logger.info("Discord配置管理器启动")

        # 加载第一个启用的配置（简化版本）
        await self.reload_config()

    async def stop(self):
        """停止配置管理器"""
        if not self._running:
            return

        self._running = False
        logger.info("Discord配置管理器停止")

        # 停止当前监听器
        await discord_manager.stop()
        self.current_config = None

    async def reload_config(self):
        """重新加载配置（简化版本：只支持一个活跃配置）"""
        try:
            async with AsyncSessionLocal() as session:
                # 获取第一个启用的Discord配置
                result = await session.execute(
                    select(DiscordConfig)
                    .where(DiscordConfig.enabled == True)
                    .options(selectinload(DiscordConfig.user))
                    .limit(1)
                )
                config = result.scalar_one_or_none()

                if config:
                    if not self.current_config or self.current_config.id != config.id:
                        # 配置发生变化，重启监听器
                        await discord_manager.stop()
                        self.current_config = config
                        await discord_manager.start_with_config(config)
                        logger.info(f"Discord配置已更新: {config.source_name}")
                else:
                    # 没有启用的配置，停止监听器
                    if self.current_config:
                        await discord_manager.stop()
                        self.current_config = None
                        logger.info("没有启用的Discord配置，监听器已停止")

        except Exception as e:
            logger.error(f"重新加载配置失败: {e}")
    
    async def restart_config(self, config_id: str):
        """重启指定配置（简化版本）"""
        try:
            async with AsyncSessionLocal() as session:
                result = await session.execute(
                    select(DiscordConfig)
                    .where(DiscordConfig.id == config_id)
                    .options(selectinload(DiscordConfig.user))
                )
                config = result.scalar_one_or_none()

                if not config:
                    logger.error(f"配置不存在: {config_id}")
                    return False

                # 重新加载配置
                await self.reload_config()
                return True

        except Exception as e:
            logger.error(f"重启Discord配置失败 (配置: {config_id}): {e}")
            return False
    
    async def get_status(self) -> Dict:
        """获取配置管理器状态"""
        discord_status = discord_manager.get_status()

        return {
            'manager_running': self._running,
            'current_config': {
                'id': str(self.current_config.id) if self.current_config else None,
                'name': self.current_config.source_name if self.current_config else None,
                'user_id': str(self.current_config.user_id) if self.current_config else None
            } if self.current_config else None,
            'discord_listener': discord_status
        }
    
    def is_running(self) -> bool:
        """检查管理器是否在运行"""
        return self._running


# 全局实例
discord_config_manager = DiscordConfigManager()


async def get_discord_config_manager() -> DiscordConfigManager:
    """获取Discord配置管理器实例"""
    return discord_config_manager


# 配置变更事件处理
async def on_config_created(config: DiscordConfig):
    """配置创建事件处理"""
    await discord_config_manager.reload_config()


async def on_config_updated(config: DiscordConfig):
    """配置更新事件处理"""
    await discord_config_manager.reload_config()


async def on_config_deleted(config_id: str):
    """配置删除事件处理"""
    await discord_config_manager.reload_config()
