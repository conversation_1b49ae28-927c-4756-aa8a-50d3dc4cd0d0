"""
动态风险管理系统 - 实现实时风险评估和自适应风险控制
"""

import asyncio
import json
from datetime import datetime, timedelta
from decimal import Decimal
from enum import Enum
from typing import Any, Dict, List, Optional, Tuple

import structlog
from sqlalchemy import and_, func, select
from sqlalchemy.ext.asyncio import AsyncSession

from ..core.config import settings
from ..core.models import ConditionalOrder, Order, RiskConfig, User
from ..core.schemas import AgentState, TradePlan
from ..services.exchange import ExchangeService

logger = structlog.get_logger()


class RiskLevel(Enum):
    """风险等级"""

    VERY_LOW = "very_low"
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class RiskCategory(Enum):
    """风险类别"""

    POSITION_SIZE = "position_size"
    CONCENTRATION = "concentration"
    LIQUIDITY = "liquidity"
    VOLATILITY = "volatility"
    CORRELATION = "correlation"
    MARKET_CONDITIONS = "market_conditions"
    BEHAVIORAL = "behavioral"
    TECHNICAL = "technical"
    REGULATORY = "regulatory"


class DynamicRiskManager:
    """动态风险管理器"""

    def __init__(self, db: AsyncSession):
        self.db = db
        self.exchange_service = ExchangeService()
        self.risk_cache = {}
        self.market_data_cache = {}
        self.risk_history = []

        # 风险权重配置
        self.risk_weights = {
            RiskCategory.POSITION_SIZE: 0.25,
            RiskCategory.CONCENTRATION: 0.20,
            RiskCategory.LIQUIDITY: 0.15,
            RiskCategory.VOLATILITY: 0.15,
            RiskCategory.CORRELATION: 0.10,
            RiskCategory.MARKET_CONDITIONS: 0.10,
            RiskCategory.BEHAVIORAL: 0.03,
            RiskCategory.TECHNICAL: 0.02,
        }

        # 动态调整因子
        self.adjustment_factors = {
            "market_stress": 1.0,
            "user_performance": 1.0,
            "system_load": 1.0,
            "time_of_day": 1.0,
        }

    async def assess_dynamic_risk(
        self,
        user_id: int,
        trade_plan: TradePlan,
        context: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """执行动态风险评估"""

        logger.info(
            "Starting dynamic risk assessment",
            user_id=user_id,
            symbol=trade_plan.symbol,
            side=trade_plan.side,
            quantity=trade_plan.quantity,
        )

        # 获取用户风险配置
        risk_config = await self._get_user_risk_config(user_id)

        # 获取市场数据
        market_data = await self._get_market_data(trade_plan.symbol)

        # 获取用户当前持仓
        current_positions = await self._get_user_positions(user_id)

        # 获取历史交易数据
        trading_history = await self._get_trading_history(user_id)

        # 执行各类风险评估
        risk_assessments = {}

        # 0. 白名单检查（最高优先级）
        whitelist_check = await self._assess_symbol_whitelist_risk(
            trade_plan, risk_config
        )
        if whitelist_check["risk_level"] == RiskLevel.CRITICAL:
            # 如果不在白名单中，直接返回拒绝
            return {
                "overall_risk": {
                    "overall_score": 100,
                    "overall_level": RiskLevel.CRITICAL,
                    "recommendation": "交易对不在允许列表中",
                    "risk_breakdown": {"symbol_whitelist": whitelist_check},
                },
                "risk_assessments": {"symbol_whitelist": whitelist_check},
                "recommendations": [
                    {
                        "message": whitelist_check["message"],
                        "priority": "critical",
                    }
                ],
                "market_conditions": {},
                "assessment_timestamp": datetime.utcnow().isoformat(),
            }

        risk_assessments["symbol_whitelist"] = whitelist_check

        # 1. 头寸规模风险
        risk_assessments[
            RiskCategory.POSITION_SIZE
        ] = await self._assess_position_size_risk(
            trade_plan, risk_config, current_positions
        )

        # 2. 集中度风险
        risk_assessments[
            RiskCategory.CONCENTRATION
        ] = await self._assess_concentration_risk(
            trade_plan, current_positions, risk_config
        )

        # 3. 流动性风险
        risk_assessments[RiskCategory.LIQUIDITY] = await self._assess_liquidity_risk(
            trade_plan, market_data
        )

        # 4. 波动率风险
        risk_assessments[RiskCategory.VOLATILITY] = await self._assess_volatility_risk(
            trade_plan, market_data, risk_config
        )

        # 5. 相关性风险
        risk_assessments[
            RiskCategory.CORRELATION
        ] = await self._assess_correlation_risk(
            trade_plan, current_positions, market_data
        )

        # 6. 市场条件风险
        risk_assessments[
            RiskCategory.MARKET_CONDITIONS
        ] = await self._assess_market_conditions_risk(trade_plan, market_data)

        # 7. 行为风险
        risk_assessments[RiskCategory.BEHAVIORAL] = await self._assess_behavioral_risk(
            trade_plan, trading_history, user_id
        )

        # 8. 技术风险
        risk_assessments[RiskCategory.TECHNICAL] = await self._assess_technical_risk(
            trade_plan, market_data
        )

        # 计算综合风险评分
        overall_risk = await self._calculate_overall_risk(risk_assessments)

        # 生成风险建议
        recommendations = await self._generate_risk_recommendations(
            risk_assessments, overall_risk, trade_plan
        )

        # 记录风险评估历史
        await self._record_risk_assessment(
            user_id, trade_plan, overall_risk, risk_assessments
        )

        return {
            "overall_risk": overall_risk,
            "risk_assessments": risk_assessments,
            "recommendations": recommendations,
            "market_conditions": market_data.get("conditions", {}),
            "assessment_timestamp": datetime.utcnow().isoformat(),
        }

    async def _get_user_risk_config(self, user_id: int) -> Dict[str, Any]:
        """获取用户风险配置（私有方法）"""
        query = select(RiskConfig).where(RiskConfig.user_id == user_id)
        result = await self.db.execute(query)
        config = result.scalar_one_or_none()

        if not config:
            # 返回默认配置
            return {
                "max_position_size_usd": 1000.0,
                "max_daily_loss_usd": 500.0,
                "max_open_positions": 5,
                "max_volatility_threshold": 0.05,
                "allowed_symbols": ["BTC/USDT", "ETH/USDT"],
            }

        return {
            "max_position_size_usd": float(config.max_position_size_usd),
            "max_daily_loss_usd": float(config.max_daily_loss_usd),
            "max_open_positions": config.max_concurrent_orders,
            "max_volatility_threshold": float(
                getattr(config, "max_volatility_threshold", 0.05)
            ),
            "allowed_symbols": config.allowed_symbols,
        }

    async def get_user_risk_config(self, user_id: int) -> Dict[str, Any]:
        """
        获取用户风险配置（公共方法）

        Args:
            user_id: 用户ID

        Returns:
            用户风险配置字典
        """
        return await self._get_user_risk_config(user_id)

    async def _assess_symbol_whitelist_risk(
        self, trade_plan: TradePlan, risk_config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """评估交易对白名单风险"""

        allowed_symbols = risk_config.get("allowed_symbols", [])
        symbol = trade_plan.symbol

        if not allowed_symbols:
            # 如果没有配置白名单，则允许所有交易对
            return {
                "risk_level": RiskLevel.VERY_LOW,
                "risk_score": 0,
                "message": "未配置交易对白名单，允许所有交易对",
                "details": {
                    "symbol": symbol,
                    "allowed_symbols": [],
                    "whitelist_enabled": False,
                },
            }

        if symbol in allowed_symbols:
            return {
                "risk_level": RiskLevel.VERY_LOW,
                "risk_score": 0,
                "message": f"交易对 {symbol} 在允许列表中",
                "details": {
                    "symbol": symbol,
                    "allowed_symbols": allowed_symbols,
                    "whitelist_enabled": True,
                },
            }
        else:
            return {
                "risk_level": RiskLevel.CRITICAL,
                "risk_score": 100,
                "message": f"交易对 {symbol} 不在允许列表中: {allowed_symbols}",
                "details": {
                    "symbol": symbol,
                    "allowed_symbols": allowed_symbols,
                    "whitelist_enabled": True,
                },
            }

    async def _get_market_data(self, symbol: str) -> Dict[str, Any]:
        """获取市场数据"""
        # 检查缓存
        cache_key = f"market_data_{symbol}"
        if cache_key in self.market_data_cache:
            cached_data = self.market_data_cache[cache_key]
            if datetime.utcnow() - cached_data["timestamp"] < timedelta(minutes=1):
                return cached_data["data"]

        try:
            # 获取实时市场数据
            ticker = await self.exchange_service.get_ticker(symbol)
            orderbook = await self.exchange_service.get_orderbook(symbol, limit=20)

            # 计算市场指标
            spread = float(ticker.get("ask", 0)) - float(ticker.get("bid", 0))
            spread_pct = (
                spread / float(ticker.get("last", 1)) * 100 if ticker.get("last") else 0
            )

            market_data = {
                "price": float(ticker.get("last", 0)),
                "volume_24h": float(ticker.get("quoteVolume", 0)),
                "spread": spread,
                "spread_percentage": spread_pct,
                "bid_depth": sum(
                    float(bid[1]) for bid in orderbook.get("bids", [])[:5]
                ),
                "ask_depth": sum(
                    float(ask[1]) for ask in orderbook.get("asks", [])[:5]
                ),
                "volatility_24h": float(ticker.get("percentage", 0)) / 100,
                "conditions": self._analyze_market_conditions(ticker, orderbook),
            }

            # 更新缓存
            self.market_data_cache[cache_key] = {
                "data": market_data,
                "timestamp": datetime.utcnow(),
            }

            return market_data

        except Exception as e:
            logger.error(f"Failed to get market data for {symbol}: {e}")
            return {
                "price": 0,
                "volume_24h": 0,
                "spread": 0,
                "spread_percentage": 0,
                "bid_depth": 0,
                "ask_depth": 0,
                "volatility_24h": 0,
                "conditions": {"status": "unknown"},
            }

    async def _get_user_positions(self, user_id: int) -> List[Dict[str, Any]]:
        """获取用户当前持仓"""
        query = select(Order).where(
            and_(
                Order.user_id == user_id,
                Order.status.in_(["filled", "partially_filled"]),
            )
        )
        result = await self.db.execute(query)
        orders = result.scalars().all()

        # 计算净持仓
        positions = {}
        for order in orders:
            symbol = order.symbol
            if symbol not in positions:
                positions[symbol] = {
                    "symbol": symbol,
                    "net_quantity": 0,
                    "avg_price": 0,
                    "total_value": 0,
                    "unrealized_pnl": 0,
                }

            quantity = float(order.filled_quantity or order.quantity)
            if order.side == "sell":
                quantity = -quantity

            positions[symbol]["net_quantity"] += quantity
            positions[symbol]["total_value"] += quantity * float(order.price)

        return list(positions.values())

    async def _get_trading_history(
        self, user_id: int, days: int = 30
    ) -> List[Dict[str, Any]]:
        """获取用户交易历史"""
        cutoff_date = datetime.utcnow() - timedelta(days=days)
        query = (
            select(Order)
            .where(
                and_(
                    Order.user_id == user_id,
                    Order.created_at >= cutoff_date,
                    Order.status == "filled",
                )
            )
            .order_by(Order.created_at.desc())
        )

        result = await self.db.execute(query)
        orders = result.scalars().all()

        return [
            {
                "symbol": order.symbol,
                "side": order.side,
                "quantity": float(order.quantity),
                "price": float(order.price),
                "timestamp": order.created_at,
                "pnl": float(getattr(order, "realized_pnl", 0)),
            }
            for order in orders
        ]

    async def _assess_position_size_risk(
        self,
        trade_plan: TradePlan,
        risk_config: Dict[str, Any],
        current_positions: List[Dict[str, Any]],
    ) -> Dict[str, Any]:
        """评估头寸规模风险"""

        position_value = float(trade_plan.quantity) * float(trade_plan.price or 0)
        max_position_size = risk_config["max_position_size_usd"]

        # 计算当前总持仓价值
        total_position_value = sum(abs(pos["total_value"]) for pos in current_positions)

        # 计算风险评分
        size_ratio = position_value / max_position_size
        total_ratio = (total_position_value + position_value) / (
            max_position_size * 5
        )  # 假设最多5个持仓

        risk_score = max(size_ratio, total_ratio) * 100

        if risk_score > 100:
            risk_level = RiskLevel.CRITICAL
            message = f"头寸规模超出限制: {position_value:.2f} > {max_position_size:.2f}"
        elif risk_score > 80:
            risk_level = RiskLevel.HIGH
            message = f"头寸规模接近限制: {position_value:.2f} / {max_position_size:.2f}"
        elif risk_score > 60:
            risk_level = RiskLevel.MEDIUM
            message = f"头寸规模适中: {position_value:.2f} / {max_position_size:.2f}"
        else:
            risk_level = RiskLevel.LOW
            message = f"头寸规模安全: {position_value:.2f} / {max_position_size:.2f}"

        return {
            "risk_level": risk_level,
            "risk_score": min(risk_score, 100),
            "message": message,
            "details": {
                "position_value": position_value,
                "max_allowed": max_position_size,
                "utilization_ratio": size_ratio,
                "total_position_value": total_position_value,
            },
        }

    async def _assess_concentration_risk(
        self,
        trade_plan: TradePlan,
        current_positions: List[Dict[str, Any]],
        risk_config: Dict[str, Any],
    ) -> Dict[str, Any]:
        """评估集中度风险"""

        symbol = trade_plan.symbol
        new_position_value = float(trade_plan.quantity) * float(trade_plan.price or 0)

        # 计算同一交易对的集中度
        existing_position = next(
            (pos for pos in current_positions if pos["symbol"] == symbol), None
        )
        existing_value = (
            abs(existing_position["total_value"]) if existing_position else 0
        )
        total_symbol_value = existing_value + new_position_value

        # 计算总持仓价值
        total_portfolio_value = (
            sum(abs(pos["total_value"]) for pos in current_positions)
            + new_position_value
        )

        # 集中度比例
        concentration_ratio = (
            total_symbol_value / total_portfolio_value
            if total_portfolio_value > 0
            else 0
        )

        # 计算风险评分
        risk_score = concentration_ratio * 100

        if concentration_ratio > 0.5:
            risk_level = RiskLevel.CRITICAL
            message = f"单一资产集中度过高: {concentration_ratio:.1%}"
        elif concentration_ratio > 0.3:
            risk_level = RiskLevel.HIGH
            message = f"单一资产集中度较高: {concentration_ratio:.1%}"
        elif concentration_ratio > 0.2:
            risk_level = RiskLevel.MEDIUM
            message = f"单一资产集中度适中: {concentration_ratio:.1%}"
        else:
            risk_level = RiskLevel.LOW
            message = f"单一资产集中度安全: {concentration_ratio:.1%}"

        return {
            "risk_level": risk_level,
            "risk_score": risk_score,
            "message": message,
            "details": {
                "concentration_ratio": concentration_ratio,
                "symbol_value": total_symbol_value,
                "portfolio_value": total_portfolio_value,
                "diversification_score": 100 - risk_score,
            },
        }

    async def _assess_liquidity_risk(
        self, trade_plan: TradePlan, market_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """评估流动性风险"""

        volume_24h = market_data.get("volume_24h", 0)
        spread_pct = market_data.get("spread_percentage", 0)
        bid_depth = market_data.get("bid_depth", 0)
        ask_depth = market_data.get("ask_depth", 0)

        position_value = float(trade_plan.quantity) * float(
            trade_plan.price or market_data.get("price", 0)
        )

        # 计算流动性指标
        volume_ratio = position_value / volume_24h if volume_24h > 0 else 1
        depth_ratio = (
            position_value / (bid_depth + ask_depth)
            if (bid_depth + ask_depth) > 0
            else 1
        )

        # 综合流动性风险评分
        liquidity_score = volume_ratio * 50 + spread_pct * 10 + depth_ratio * 40

        if liquidity_score > 20 or spread_pct > 1.0:
            risk_level = RiskLevel.HIGH
            message = f"流动性不足，价差: {spread_pct:.2f}%"
        elif liquidity_score > 10 or spread_pct > 0.5:
            risk_level = RiskLevel.MEDIUM
            message = f"流动性一般，价差: {spread_pct:.2f}%"
        else:
            risk_level = RiskLevel.LOW
            message = f"流动性充足，价差: {spread_pct:.2f}%"

        return {
            "risk_level": risk_level,
            "risk_score": min(liquidity_score, 100),
            "message": message,
            "details": {
                "volume_24h": volume_24h,
                "spread_percentage": spread_pct,
                "bid_depth": bid_depth,
                "ask_depth": ask_depth,
                "volume_ratio": volume_ratio,
                "depth_ratio": depth_ratio,
            },
        }

    async def _assess_volatility_risk(
        self,
        trade_plan: TradePlan,
        market_data: Dict[str, Any],
        risk_config: Dict[str, Any],
    ) -> Dict[str, Any]:
        """评估波动率风险"""

        volatility_24h = abs(market_data.get("volatility_24h", 0))
        max_volatility = risk_config.get("max_volatility_threshold", 0.05)

        # 计算波动率风险评分
        volatility_ratio = volatility_24h / max_volatility
        risk_score = volatility_ratio * 100

        if volatility_24h > max_volatility * 2:
            risk_level = RiskLevel.CRITICAL
            message = f"极高波动率: {volatility_24h:.1%}"
        elif volatility_24h > max_volatility:
            risk_level = RiskLevel.HIGH
            message = f"高波动率: {volatility_24h:.1%}"
        elif volatility_24h > max_volatility * 0.5:
            risk_level = RiskLevel.MEDIUM
            message = f"中等波动率: {volatility_24h:.1%}"
        else:
            risk_level = RiskLevel.LOW
            message = f"低波动率: {volatility_24h:.1%}"

        return {
            "risk_level": risk_level,
            "risk_score": min(risk_score, 100),
            "message": message,
            "details": {
                "volatility_24h": volatility_24h,
                "max_allowed": max_volatility,
                "volatility_ratio": volatility_ratio,
            },
        }

    async def _assess_correlation_risk(
        self,
        trade_plan: TradePlan,
        current_positions: List[Dict[str, Any]],
        market_data: Dict[str, Any],
    ) -> Dict[str, Any]:
        """评估相关性风险"""

        symbol = trade_plan.symbol

        # 定义高相关性资产组
        correlation_groups = {
            "major_crypto": ["BTC/USDT", "ETH/USDT"],
            "defi_tokens": ["UNI/USDT", "SUSHI/USDT", "AAVE/USDT"],
            "layer1": ["SOL/USDT", "ADA/USDT", "DOT/USDT"],
            "meme_coins": ["DOGE/USDT", "SHIB/USDT"],
        }

        # 找到当前交易对所属的组
        current_group = None
        for group_name, symbols in correlation_groups.items():
            if symbol in symbols:
                current_group = group_name
                break

        if not current_group:
            return {
                "risk_level": RiskLevel.LOW,
                "risk_score": 0,
                "message": "未识别的资产类别，相关性风险未知",
                "details": {"correlation_group": "unknown"},
            }

        # 计算同组资产的持仓比例
        group_symbols = correlation_groups[current_group]
        group_positions = [
            pos for pos in current_positions if pos["symbol"] in group_symbols
        ]
        group_value = sum(abs(pos["total_value"]) for pos in group_positions)

        total_value = sum(abs(pos["total_value"]) for pos in current_positions)
        new_position_value = float(trade_plan.quantity) * float(
            trade_plan.price or market_data.get("price", 0)
        )

        group_concentration = (
            (group_value + new_position_value) / (total_value + new_position_value)
            if (total_value + new_position_value) > 0
            else 0
        )

        risk_score = group_concentration * 100

        if group_concentration > 0.7:
            risk_level = RiskLevel.HIGH
            message = f"高相关性资产集中度过高: {group_concentration:.1%} ({current_group})"
        elif group_concentration > 0.5:
            risk_level = RiskLevel.MEDIUM
            message = f"高相关性资产集中度较高: {group_concentration:.1%} ({current_group})"
        else:
            risk_level = RiskLevel.LOW
            message = f"相关性风险可控: {group_concentration:.1%} ({current_group})"

        return {
            "risk_level": risk_level,
            "risk_score": risk_score,
            "message": message,
            "details": {
                "correlation_group": current_group,
                "group_concentration": group_concentration,
                "group_symbols": group_symbols,
                "group_positions": len(group_positions),
            },
        }

    async def _assess_market_conditions_risk(
        self, trade_plan: TradePlan, market_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """评估市场条件风险"""

        conditions = market_data.get("conditions", {})
        volatility = abs(market_data.get("volatility_24h", 0))
        volume = market_data.get("volume_24h", 0)

        # 市场条件评分
        risk_factors = []
        risk_score = 0

        # 波动率因子
        if volatility > 0.1:  # 10%
            risk_factors.append("极高波动率")
            risk_score += 30
        elif volatility > 0.05:  # 5%
            risk_factors.append("高波动率")
            risk_score += 15

        # 成交量因子
        if volume < 1000000:  # 100万USDT
            risk_factors.append("低成交量")
            risk_score += 20

        # 时间因子（周末、节假日等）
        current_hour = datetime.utcnow().hour
        if current_hour < 6 or current_hour > 22:  # UTC时间
            risk_factors.append("非主要交易时间")
            risk_score += 10

        if risk_score > 50:
            risk_level = RiskLevel.HIGH
            message = f"市场条件不利: {', '.join(risk_factors)}"
        elif risk_score > 25:
            risk_level = RiskLevel.MEDIUM
            message = f"市场条件一般: {', '.join(risk_factors)}"
        else:
            risk_level = RiskLevel.LOW
            message = "市场条件良好"

        return {
            "risk_level": risk_level,
            "risk_score": risk_score,
            "message": message,
            "details": {
                "risk_factors": risk_factors,
                "volatility_24h": volatility,
                "volume_24h": volume,
                "trading_hour": current_hour,
            },
        }

    async def _assess_behavioral_risk(
        self,
        trade_plan: TradePlan,
        trading_history: List[Dict[str, Any]],
        user_id: int,
    ) -> Dict[str, Any]:
        """评估行为风险"""

        if not trading_history:
            return {
                "risk_level": RiskLevel.LOW,
                "risk_score": 0,
                "message": "无交易历史，行为风险未知",
                "details": {"trades_count": 0},
            }

        # 分析交易行为模式
        recent_trades = [
            t for t in trading_history if (datetime.utcnow() - t["timestamp"]).days <= 7
        ]

        risk_score = 0
        risk_factors = []

        # 过度交易检查
        if len(recent_trades) > 50:  # 一周超过50笔交易
            risk_factors.append("过度交易")
            risk_score += 25

        # 亏损交易比例
        losing_trades = [t for t in recent_trades if t.get("pnl", 0) < 0]
        if recent_trades:
            loss_ratio = len(losing_trades) / len(recent_trades)
            if loss_ratio > 0.7:
                risk_factors.append("高亏损率")
                risk_score += 30
            elif loss_ratio > 0.5:
                risk_factors.append("亏损率偏高")
                risk_score += 15

        # 连续亏损检查
        consecutive_losses = 0
        for trade in recent_trades[:10]:  # 检查最近10笔交易
            if trade.get("pnl", 0) < 0:
                consecutive_losses += 1
            else:
                break

        if consecutive_losses >= 5:
            risk_factors.append("连续亏损")
            risk_score += 20

        if risk_score > 40:
            risk_level = RiskLevel.HIGH
            message = f"行为风险较高: {', '.join(risk_factors)}"
        elif risk_score > 20:
            risk_level = RiskLevel.MEDIUM
            message = f"行为风险一般: {', '.join(risk_factors)}"
        else:
            risk_level = RiskLevel.LOW
            message = "行为风险较低"

        return {
            "risk_level": risk_level,
            "risk_score": risk_score,
            "message": message,
            "details": {
                "recent_trades_count": len(recent_trades),
                "loss_ratio": len(losing_trades) / len(recent_trades)
                if recent_trades
                else 0,
                "consecutive_losses": consecutive_losses,
                "risk_factors": risk_factors,
            },
        }

    async def _assess_technical_risk(
        self, trade_plan: TradePlan, market_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """评估技术风险"""

        # 简化的技术分析风险评估
        price = market_data.get("price", 0)
        volatility = abs(market_data.get("volatility_24h", 0))

        risk_score = 0
        risk_factors = []

        # 价格异常检查
        if price <= 0:
            risk_factors.append("价格数据异常")
            risk_score += 50

        # 系统负载检查（简化）
        current_time = datetime.utcnow()
        if current_time.second % 10 == 0:  # 模拟系统负载检查
            risk_factors.append("系统负载较高")
            risk_score += 10

        if risk_score > 30:
            risk_level = RiskLevel.MEDIUM
            message = f"技术风险: {', '.join(risk_factors)}"
        elif risk_score > 0:
            risk_level = RiskLevel.LOW
            message = f"轻微技术风险: {', '.join(risk_factors)}"
        else:
            risk_level = RiskLevel.VERY_LOW
            message = "技术风险很低"

        return {
            "risk_level": risk_level,
            "risk_score": risk_score,
            "message": message,
            "details": {
                "risk_factors": risk_factors,
                "price_valid": price > 0,
                "data_timestamp": current_time.isoformat(),
            },
        }

    async def _calculate_overall_risk(
        self, risk_assessments: Dict[RiskCategory, Dict[str, Any]]
    ) -> Dict[str, Any]:
        """计算综合风险评分"""

        weighted_score = 0
        total_weight = 0
        risk_breakdown = {}

        for category, assessment in risk_assessments.items():
            weight = self.risk_weights.get(category, 0.1)
            score = assessment.get("risk_score", 0)

            weighted_score += score * weight
            total_weight += weight

            risk_breakdown[
                category.value if hasattr(category, "value") else str(category)
            ] = {
                "score": score,
                "weight": weight,
                "level": assessment.get("risk_level", RiskLevel.LOW).value,
                "message": assessment.get("message", ""),
            }

        # 应用动态调整因子
        for factor, multiplier in self.adjustment_factors.items():
            weighted_score *= multiplier

        overall_score = min(weighted_score, 100)

        # 确定整体风险等级
        if overall_score >= 80:
            overall_level = RiskLevel.CRITICAL
            recommendation = "强烈建议拒绝交易"
        elif overall_score >= 60:
            overall_level = RiskLevel.HIGH
            recommendation = "建议降低仓位或等待更好时机"
        elif overall_score >= 40:
            overall_level = RiskLevel.MEDIUM
            recommendation = "可以交易但需要谨慎"
        elif overall_score >= 20:
            overall_level = RiskLevel.LOW
            recommendation = "风险可控，可以正常交易"
        else:
            overall_level = RiskLevel.VERY_LOW
            recommendation = "风险很低，适合交易"

        return {
            "overall_score": overall_score,
            "overall_level": overall_level,
            "recommendation": recommendation,
            "risk_breakdown": risk_breakdown,
            "adjustment_factors": self.adjustment_factors.copy(),
        }

    async def _generate_risk_recommendations(
        self,
        risk_assessments: Dict[RiskCategory, Dict[str, Any]],
        overall_risk: Dict[str, Any],
        trade_plan: TradePlan,
    ) -> List[Dict[str, Any]]:
        """生成风险建议"""

        recommendations = []

        # 基于整体风险等级的建议
        overall_level = overall_risk["overall_level"]

        if overall_level in [RiskLevel.CRITICAL, RiskLevel.HIGH]:
            recommendations.append(
                {
                    "type": "position_adjustment",
                    "priority": "high",
                    "message": "建议减少仓位规模至原计划的50%",
                    "action": "reduce_position_size",
                    "parameters": {"reduction_factor": 0.5},
                }
            )

        # 基于具体风险类别的建议
        for category, assessment in risk_assessments.items():
            risk_level = assessment.get("risk_level")

            if category == RiskCategory.LIQUIDITY and risk_level in [
                RiskLevel.HIGH,
                RiskLevel.CRITICAL,
            ]:
                recommendations.append(
                    {
                        "type": "order_type_adjustment",
                        "priority": "medium",
                        "message": "建议使用限价单而非市价单",
                        "action": "use_limit_order",
                        "parameters": {"order_type": "limit"},
                    }
                )

            elif category == RiskCategory.VOLATILITY and risk_level == RiskLevel.HIGH:
                recommendations.append(
                    {
                        "type": "stop_loss_adjustment",
                        "priority": "medium",
                        "message": "建议设置更严格的止损",
                        "action": "tighten_stop_loss",
                        "parameters": {"stop_loss_percentage": 3.0},
                    }
                )

            elif (
                category == RiskCategory.CONCENTRATION and risk_level == RiskLevel.HIGH
            ):
                recommendations.append(
                    {
                        "type": "diversification",
                        "priority": "low",
                        "message": "建议分散投资到其他资产",
                        "action": "diversify_portfolio",
                        "parameters": {"max_single_asset_ratio": 0.3},
                    }
                )

        return recommendations

    async def _record_risk_assessment(
        self,
        user_id: int,
        trade_plan: TradePlan,
        overall_risk: Dict[str, Any],
        risk_assessments: Dict[RiskCategory, Dict[str, Any]],
    ):
        """记录风险评估历史"""

        assessment_record = {
            "user_id": user_id,
            "symbol": trade_plan.symbol,
            "side": trade_plan.side,
            "quantity": float(trade_plan.quantity),
            "price": float(trade_plan.price or 0),
            "overall_score": overall_risk["overall_score"],
            "overall_level": overall_risk["overall_level"].value,
            "risk_breakdown": {
                k.value if hasattr(k, "value") else str(k): v
                for k, v in risk_assessments.items()
            },
            "timestamp": datetime.utcnow(),
        }

        self.risk_history.append(assessment_record)

        # 保持历史记录在合理范围内
        if len(self.risk_history) > 1000:
            self.risk_history = self.risk_history[-500:]

        logger.info(
            "Risk assessment recorded",
            user_id=user_id,
            symbol=trade_plan.symbol,
            overall_score=overall_risk["overall_score"],
            overall_level=overall_risk["overall_level"].value,
        )

    def _analyze_market_conditions(
        self, ticker: Dict[str, Any], orderbook: Dict[str, Any]
    ) -> Dict[str, Any]:
        """分析市场条件"""

        volume = float(ticker.get("quoteVolume", 0))
        change_24h = float(ticker.get("percentage", 0))

        conditions = {
            "status": "normal",
            "volume_level": "normal",
            "volatility_level": "normal",
            "trend": "sideways",
        }

        # 成交量分析
        if volume > 10000000:  # 1000万USDT
            conditions["volume_level"] = "high"
        elif volume < 1000000:  # 100万USDT
            conditions["volume_level"] = "low"

        # 波动率分析
        if abs(change_24h) > 10:
            conditions["volatility_level"] = "high"
            conditions["status"] = "volatile"
        elif abs(change_24h) > 5:
            conditions["volatility_level"] = "medium"

        # 趋势分析
        if change_24h > 5:
            conditions["trend"] = "bullish"
        elif change_24h < -5:
            conditions["trend"] = "bearish"

        return conditions
