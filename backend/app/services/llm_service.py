"""
LLM服务层

提供统一的LLM服务接口，支持多个LLM提供商的API调用。
包括DeepSeek、ChatGPT、<PERSON>、<PERSON>等服务的客户端实现。
"""

import asyncio
import time
from typing import Optional, Dict, Any, Union
from abc import ABC, abstractmethod
import httpx
import structlog

from ..core.schemas import LLMConfigTestResponse
from ..core.models import LLMConfig

# 配置结构化日志
logger = structlog.get_logger()


class LLMClientBase(ABC):
    """LLM客户端基类"""

    # 统一的默认测试消息
    DEFAULT_TEST_MESSAGE = "请简单介绍一下你自己（模型与版本），限制50字以内。"

    def __init__(self, config: LLMConfig):
        self.config = config
        self.api_key = config.api_key
        self.api_base_url = config.api_base_url
        self.model_name = config.model_name
        self.max_tokens = config.max_tokens
        self.temperature = float(config.temperature)
        self.timeout_seconds = config.timeout_seconds
        self.max_retries = config.max_retries

    @abstractmethod
    async def test_connection(self) -> LLMConfigTestResponse:
        """测试连接到LLM服务"""
        pass
    
    @abstractmethod
    async def chat_completion(self, messages: list, **kwargs) -> Dict[str, Any]:
        """发送聊天完成请求"""
        pass


class OpenAIClient(LLMClientBase):
    """OpenAI/ChatGPT客户端实现"""
    
    def __init__(self, config: LLMConfig):
        super().__init__(config)
        self.api_base_url = self.api_base_url or "https://api.openai.com/v1"
    
    async def test_connection(self) -> LLMConfigTestResponse:
        """测试OpenAI API连接"""
        start_time = time.time()
        test_message = self.DEFAULT_TEST_MESSAGE

        # 调试日志：记录实际发送的消息
        logger.info(
            "OpenAI测试连接 - 发送消息",
            config_id=str(self.config.id),
            test_message=test_message,
            model_name=self.model_name
        )

        try:
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }

            payload = {
                "model": self.model_name,
                "messages": [
                    {"role": "user", "content": test_message}
                ],
                "max_tokens": min(100, self.max_tokens),  # 测试时使用较少的token
                "temperature": self.temperature
            }
            
            async with httpx.AsyncClient(timeout=self.timeout_seconds) as client:
                response = await client.post(
                    f"{self.api_base_url}/chat/completions",
                    headers=headers,
                    json=payload
                )
                
                response_time_ms = int((time.time() - start_time) * 1000)
                
                if response.status_code == 200:
                    data = response.json()
                    response_text = data.get("choices", [{}])[0].get("message", {}).get("content", "")
                    
                    return LLMConfigTestResponse(
                        success=True,
                        response_text=response_text,
                        error_message=None,
                        response_time_ms=response_time_ms
                    )
                else:
                    error_data = response.json() if response.headers.get("content-type", "").startswith("application/json") else {}
                    error_message = error_data.get("error", {}).get("message", f"HTTP {response.status_code}")

                    return LLMConfigTestResponse(
                        success=False,
                        response_text=None,
                        error_message=f"OpenAI API错误: {error_message}",
                        response_time_ms=response_time_ms
                    )
                    
        except httpx.TimeoutException:
            response_time_ms = int((time.time() - start_time) * 1000)
            return LLMConfigTestResponse(
                success=False,
                response_text=None,
                error_message="请求超时",
                response_time_ms=response_time_ms
            )
        except Exception as e:
            response_time_ms = int((time.time() - start_time) * 1000)
            return LLMConfigTestResponse(
                success=False,
                response_text=None,
                error_message=f"连接错误: {str(e)}",
                response_time_ms=response_time_ms
            )
    
    async def chat_completion(self, messages: list, **kwargs) -> Dict[str, Any]:
        """发送聊天完成请求"""
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "model": self.model_name,
            "messages": messages,
            "max_tokens": self.max_tokens,
            "temperature": self.temperature,
            **kwargs
        }
        
        async with httpx.AsyncClient(timeout=self.timeout_seconds) as client:
            response = await client.post(
                f"{self.api_base_url}/chat/completions",
                headers=headers,
                json=payload
            )
            response.raise_for_status()
            return response.json()


class DeepSeekClient(LLMClientBase):
    """DeepSeek客户端实现"""
    
    def __init__(self, config: LLMConfig):
        super().__init__(config)
        self.api_base_url = self.api_base_url or "https://api.deepseek.com/v1"
    
    async def test_connection(self) -> LLMConfigTestResponse:
        """测试DeepSeek API连接"""
        start_time = time.time()
        test_message = self.DEFAULT_TEST_MESSAGE
        
        try:
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            payload = {
                "model": self.model_name,
                "messages": [
                    {"role": "user", "content": test_message}
                ],
                "max_tokens": min(100, self.max_tokens),
                "temperature": self.temperature
            }
            
            async with httpx.AsyncClient(timeout=self.timeout_seconds) as client:
                response = await client.post(
                    f"{self.api_base_url}/chat/completions",
                    headers=headers,
                    json=payload
                )
                
                response_time_ms = int((time.time() - start_time) * 1000)
                
                if response.status_code == 200:
                    data = response.json()
                    response_text = data.get("choices", [{}])[0].get("message", {}).get("content", "")
                    
                    return LLMConfigTestResponse(
                        success=True,
                        response_text=response_text,
                        error_message=None,
                        response_time_ms=response_time_ms
                    )
                else:
                    error_data = response.json() if response.headers.get("content-type", "").startswith("application/json") else {}
                    error_message = error_data.get("error", {}).get("message", f"HTTP {response.status_code}")

                    return LLMConfigTestResponse(
                        success=False,
                        response_text=None,
                        error_message=f"DeepSeek API错误: {error_message}",
                        response_time_ms=response_time_ms
                    )
                    
        except httpx.TimeoutException:
            response_time_ms = int((time.time() - start_time) * 1000)
            return LLMConfigTestResponse(
                success=False,
                response_text=None,
                error_message="请求超时",
                response_time_ms=response_time_ms
            )
        except Exception as e:
            response_time_ms = int((time.time() - start_time) * 1000)
            return LLMConfigTestResponse(
                success=False,
                response_text=None,
                error_message=f"连接错误: {str(e)}",
                response_time_ms=response_time_ms
            )
    
    async def chat_completion(self, messages: list, **kwargs) -> Dict[str, Any]:
        """发送聊天完成请求"""
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "model": self.model_name,
            "messages": messages,
            "max_tokens": self.max_tokens,
            "temperature": self.temperature,
            **kwargs
        }
        
        async with httpx.AsyncClient(timeout=self.timeout_seconds) as client:
            response = await client.post(
                f"{self.api_base_url}/chat/completions",
                headers=headers,
                json=payload
            )
            response.raise_for_status()
            return response.json()


class GeminiClient(LLMClientBase):
    """Google Gemini客户端实现"""
    
    def __init__(self, config: LLMConfig):
        super().__init__(config)
        self.api_base_url = self.api_base_url or "https://generativelanguage.googleapis.com/v1"
    
    async def test_connection(self) -> LLMConfigTestResponse:
        """测试Gemini API连接"""
        start_time = time.time()
        test_message = self.DEFAULT_TEST_MESSAGE
        
        try:
            # Gemini使用不同的API格式
            url = f"{self.api_base_url}/models/{self.model_name}:generateContent"
            params = {"key": self.api_key}
            
            payload = {
                "contents": [
                    {
                        "parts": [
                            {"text": test_message}
                        ]
                    }
                ],
                "generationConfig": {
                    "maxOutputTokens": min(100, self.max_tokens),
                    "temperature": self.temperature
                }
            }
            
            async with httpx.AsyncClient(timeout=self.timeout_seconds) as client:
                response = await client.post(
                    url,
                    params=params,
                    json=payload
                )
                
                response_time_ms = int((time.time() - start_time) * 1000)
                
                if response.status_code == 200:
                    data = response.json()
                    candidates = data.get("candidates", [])
                    if candidates and candidates[0].get("content", {}).get("parts"):
                        response_text = candidates[0]["content"]["parts"][0].get("text", "")
                    else:
                        response_text = "Gemini响应格式异常"
                    
                    return LLMConfigTestResponse(
                        success=True,
                        response_text=response_text,
                        error_message=None,
                        response_time_ms=response_time_ms
                    )
                else:
                    error_data = response.json() if response.headers.get("content-type", "").startswith("application/json") else {}
                    error_message = error_data.get("error", {}).get("message", f"HTTP {response.status_code}")

                    # 提供更友好的错误信息
                    friendly_error = self._get_friendly_error_message(response.status_code, error_message, "Gemini")

                    return LLMConfigTestResponse(
                        success=False,
                        response_text=None,
                        error_message=friendly_error,
                        response_time_ms=response_time_ms
                    )
                    
        except httpx.TimeoutException:
            response_time_ms = int((time.time() - start_time) * 1000)
            return LLMConfigTestResponse(
                success=False,
                response_text=None,
                error_message="请求超时",
                response_time_ms=response_time_ms
            )
        except Exception as e:
            response_time_ms = int((time.time() - start_time) * 1000)
            return LLMConfigTestResponse(
                success=False,
                response_text=None,
                error_message=f"连接错误: {str(e)}",
                response_time_ms=response_time_ms
            )
    
    async def chat_completion(self, messages: list, **kwargs) -> Dict[str, Any]:
        """发送聊天完成请求"""
        # 转换消息格式为Gemini格式
        contents = []
        for msg in messages:
            contents.append({
                "parts": [{"text": msg["content"]}]
            })
        
        url = f"{self.api_base_url}/models/{self.model_name}:generateContent"
        params = {"key": self.api_key}
        
        payload = {
            "contents": contents,
            "generationConfig": {
                "maxOutputTokens": self.max_tokens,
                "temperature": self.temperature
            }
        }
        
        async with httpx.AsyncClient(timeout=self.timeout_seconds) as client:
            response = await client.post(
                url,
                params=params,
                json=payload
            )
            response.raise_for_status()
            return response.json()


class ClaudeClient(LLMClientBase):
    """Anthropic Claude客户端实现"""

    def __init__(self, config: LLMConfig):
        super().__init__(config)
        self.api_base_url = self.api_base_url or "https://api.anthropic.com/v1"

    async def test_connection(self) -> LLMConfigTestResponse:
        """测试Claude API连接"""
        start_time = time.time()
        test_message = self.DEFAULT_TEST_MESSAGE

        try:
            headers = {
                "x-api-key": self.api_key,
                "Content-Type": "application/json",
                "anthropic-version": "2023-06-01"
            }

            payload = {
                "model": self.model_name,
                "max_tokens": min(100, self.max_tokens),
                "messages": [
                    {"role": "user", "content": test_message}
                ],
                "temperature": self.temperature
            }

            async with httpx.AsyncClient(timeout=self.timeout_seconds) as client:
                response = await client.post(
                    f"{self.api_base_url}/messages",
                    headers=headers,
                    json=payload
                )

                response_time_ms = int((time.time() - start_time) * 1000)

                if response.status_code == 200:
                    data = response.json()
                    content = data.get("content", [])
                    if content and content[0].get("text"):
                        response_text = content[0]["text"]
                    else:
                        response_text = "Claude响应格式异常"

                    return LLMConfigTestResponse(
                        success=True,
                        response_text=response_text,
                        error_message=None,
                        response_time_ms=response_time_ms
                    )
                else:
                    error_data = response.json() if response.headers.get("content-type", "").startswith("application/json") else {}
                    error_message = error_data.get("error", {}).get("message", f"HTTP {response.status_code}")

                    return LLMConfigTestResponse(
                        success=False,
                        response_text=None,
                        error_message=f"Claude API错误: {error_message}",
                        response_time_ms=response_time_ms
                    )

        except httpx.TimeoutException:
            response_time_ms = int((time.time() - start_time) * 1000)
            return LLMConfigTestResponse(
                success=False,
                response_text=None,
                error_message="请求超时",
                response_time_ms=response_time_ms
            )
        except Exception as e:
            response_time_ms = int((time.time() - start_time) * 1000)
            return LLMConfigTestResponse(
                success=False,
                response_text=None,
                error_message=f"连接错误: {str(e)}",
                response_time_ms=response_time_ms
            )

    async def chat_completion(self, messages: list, **kwargs) -> Dict[str, Any]:
        """发送聊天完成请求"""
        headers = {
            "x-api-key": self.api_key,
            "Content-Type": "application/json",
            "anthropic-version": "2023-06-01"
        }

        payload = {
            "model": self.model_name,
            "max_tokens": self.max_tokens,
            "messages": messages,
            "temperature": self.temperature,
            **kwargs
        }

        async with httpx.AsyncClient(timeout=self.timeout_seconds) as client:
            response = await client.post(
                f"{self.api_base_url}/messages",
                headers=headers,
                json=payload
            )
            response.raise_for_status()
            return response.json()


class LLMService:
    """LLM服务主类，提供统一的LLM服务接口"""

    # LLM客户端映射
    CLIENT_MAPPING = {
        "chatgpt": OpenAIClient,
        "openai": OpenAIClient,  # 兼容性别名
        "deepseek": DeepSeekClient,
        "gemini": GeminiClient,
        "claude": ClaudeClient,
    }

    @classmethod
    def create_client(cls, config: LLMConfig) -> LLMClientBase:
        """根据配置创建对应的LLM客户端"""
        provider = config.provider.lower()

        if provider not in cls.CLIENT_MAPPING:
            raise ValueError(f"不支持的LLM提供商: {provider}")

        client_class = cls.CLIENT_MAPPING[provider]
        return client_class(config)

    @classmethod
    async def test_llm_config(cls, config: LLMConfig) -> LLMConfigTestResponse:
        """测试LLM配置连接"""
        # 记录LLM服务测试
        logger.info(
            "LLM服务测试连接",
            config_id=str(config.id),
            provider=config.provider,
            test_message=LLMClientBase.DEFAULT_TEST_MESSAGE
        )

        try:
            client = cls.create_client(config)
            return await client.test_connection()
        except ValueError as e:
            return LLMConfigTestResponse(
                success=False,
                response_text=None,
                error_message=str(e),
                response_time_ms=None
            )
        except Exception as e:
            logger.error(
                "LLM配置测试失败",
                config_id=str(config.id),
                provider=config.provider,
                error=str(e),
                exc_info=True
            )
            return LLMConfigTestResponse(
                success=False,
                response_text=None,
                error_message=f"测试失败: {str(e)}",
                response_time_ms=None
            )

    @classmethod
    async def chat_completion(cls, config: LLMConfig, messages: list, **kwargs) -> Dict[str, Any]:
        """发送聊天完成请求"""
        client = cls.create_client(config)
        return await client.chat_completion(messages, **kwargs)
