"""
Discord监听器服务 - 监控Discord频道的交易信号
"""

import asyncio
import hashlib
import json
import re
import time
import urllib.parse
from collections import defaultdict, deque
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Set

import aiohttp
import discord
import structlog

from ..core.config import settings

# 配置结构化日志
logger = structlog.get_logger()


class MessageDeduplicator:
    """消息去重器"""

    def __init__(self, window_minutes: int = 5, max_cache_size: int = 1000):
        self.window_minutes = window_minutes
        self.max_cache_size = max_cache_size
        self.message_hashes: deque = deque(maxlen=max_cache_size)
        self.hash_timestamps: Dict[str, datetime] = {}

    def _generate_message_hash(
        self, content: str, author_id: int, channel_id: int
    ) -> str:
        """生成消息的唯一哈希"""
        # 标准化内容（去除多余空格、转小写）
        normalized_content = re.sub(r"\s+", " ", content.lower().strip())

        # 生成哈希
        hash_input = f"{normalized_content}:{author_id}:{channel_id}"
        return hashlib.md5(hash_input.encode()).hexdigest()

    def is_duplicate(self, content: str, author_id: int, channel_id: int) -> bool:
        """检查消息是否重复"""
        message_hash = self._generate_message_hash(content, author_id, channel_id)
        current_time = datetime.utcnow()

        # 清理过期的哈希
        self._cleanup_expired_hashes(current_time)

        # 检查是否重复
        if message_hash in self.hash_timestamps:
            last_time = self.hash_timestamps[message_hash]
            if current_time - last_time < timedelta(minutes=self.window_minutes):
                return True

        # 记录新消息
        self.hash_timestamps[message_hash] = current_time
        self.message_hashes.append(message_hash)

        return False

    def _cleanup_expired_hashes(self, current_time: datetime):
        """清理过期的哈希记录"""
        expired_hashes = []
        cutoff_time = current_time - timedelta(minutes=self.window_minutes)

        for message_hash, timestamp in self.hash_timestamps.items():
            if timestamp < cutoff_time:
                expired_hashes.append(message_hash)

        for message_hash in expired_hashes:
            del self.hash_timestamps[message_hash]


class SignalProcessor:
    """增强的信号处理器"""

    def __init__(self):
        self.processing_stats = defaultdict(int)
        self.last_reset_time = time.time()

        # 扩展的交易相关关键词
        self.trading_actions = {
            "buy",
            "sell",
            "long",
            "short",
            "enter",
            "exit",
            "close",
            "open",
            "买入",
            "卖出",
            "做多",
            "做空",
            "开仓",
            "平仓",
            "入场",
            "出场",
        }

        self.crypto_symbols = {
            "btc",
            "eth",
            "ada",
            "dot",
            "bnb",
            "sol",
            "avax",
            "matic",
            "link",
            "uni",
            "aave",
            "sushi",
            "comp",
            "mkr",
            "snx",
            "yfi",
            "crv",
            "bal",
            "ltc",
            "bch",
            "xrp",
            "xlm",
            "trx",
            "eos",
            "atom",
            "luna",
            "near",
            "ftm",
            "one",
            "algo",
            "icp",
            "fil",
            "theta",
            "vet",
            "hbar",
            "egld",
        }

        self.trading_terms = {
            "target",
            "tp",
            "sl",
            "stop",
            "loss",
            "profit",
            "entry",
            "exit",
            "support",
            "resistance",
            "breakout",
            "pump",
            "dump",
            "moon",
            "dip",
            "目标",
            "止盈",
            "止损",
            "支撑",
            "阻力",
            "突破",
            "拉盘",
            "砸盘",
        }

        self.trading_emojis = {
            "📈",
            "📉",
            "⬆️",
            "⬇️",
            "🚀",
            "💎",
            "🔥",
            "💰",
            "🎯",
            "⚡",
        }

    def should_process_message(
        self, content: str, channel_filters: List[str] = None
    ) -> bool:
        """
        判断消息是否应该被处理为交易信号

        Args:
            content: 消息内容
            channel_filters: 频道特定的过滤关键词

        Returns:
            bool: 是否应该处理
        """
        if not content or len(content.strip()) < 3:
            return False

        content_lower = content.lower().strip()

        # 应用频道特定过滤器
        if channel_filters:
            for filter_word in channel_filters:
                if filter_word.lower() in content_lower:
                    return True

        # 基础排除条件
        if self._is_excluded_message(content_lower):
            return False

        # 信号识别逻辑
        return self._contains_trading_signal(content_lower)

    def _is_excluded_message(self, content_lower: str) -> bool:
        """检查是否为应排除的消息类型"""
        exclude_patterns = [
            r"^(hi|hello|hey|good morning|good evening|gm|gn)",  # 问候语
            r"^(thanks|thank you|thx|ty)",  # 感谢语
            r"^(lol|haha|😂|🤣|lmao|rofl)",  # 笑声
            r"^\?+$",  # 纯问号
            r"^(yes|no|ok|okay|sure|nope)$",  # 简单回复
            r"^(first|second|third|\d+st|\d+nd|\d+rd|\d+th)$",  # 序数词
            r"^(nice|cool|awesome|great|amazing)$",  # 简单评价
            r"^\w{1,3}$",  # 过短的单词
        ]

        for pattern in exclude_patterns:
            if re.match(pattern, content_lower):
                return True

        # 排除纯数字或纯符号
        if re.match(r"^[\d\s\.\,\!\?\-\+\=\@\#\$\%\&\*\(\)]+$", content_lower):
            return True

        return False

    def _contains_trading_signal(self, content_lower: str) -> bool:
        """检查是否包含交易信号"""
        # 检查交易动作词
        for action in self.trading_actions:
            if re.search(rf"\b{re.escape(action)}\b", content_lower):
                return True

        # 检查加密货币符号
        for symbol in self.crypto_symbols:
            if re.search(rf"\b{re.escape(symbol)}\b", content_lower):
                return True

        # 检查交易术语
        for term in self.trading_terms:
            if re.search(rf"\b{re.escape(term)}\b", content_lower):
                return True

        # 检查价格表达式
        price_patterns = [
            r"\$\d+(\.\d+)?",  # $100, $1.5
            r"\d+(\.\d+)?\s*(usdt|usd|btc|eth|k|m)",  # 100 USDT, 1.5k
            r"\d+(\.\d+)?%",  # 10%, 1.5%
            r"x\d+(\.\d+)?",  # x10, x1.5
        ]

        for pattern in price_patterns:
            if re.search(pattern, content_lower):
                return True

        # 检查交易相关emoji
        for emoji in self.trading_emojis:
            if emoji in content_lower:
                return True

        return False

    def extract_signal_metadata(self, message: discord.Message) -> Dict[str, Any]:
        """提取增强的信号元数据"""
        metadata = {
            "message_id": str(message.id),
            "channel_id": str(message.channel.id),
            "channel_name": getattr(message.channel, "name", "Unknown"),
            "author_id": str(message.author.id),
            "author_name": message.author.display_name,
            "author_username": str(message.author),
            "timestamp": message.created_at.isoformat(),
            "content_length": len(message.content),
            "has_attachments": len(message.attachments) > 0,
            "has_embeds": len(message.embeds) > 0,
            "reference_message_id": str(message.reference.message_id)
            if message.reference
            else None,
            "is_reply": message.reference is not None,
            "mention_count": len(message.mentions),
            "role_mention_count": len(message.role_mentions),
            "channel_mention_count": len(message.channel_mentions),
        }

        # 提取附件信息
        if message.attachments:
            metadata["attachments"] = [
                {
                    "filename": att.filename,
                    "size": att.size,
                    "content_type": att.content_type,
                    "url": att.url,
                }
                for att in message.attachments
            ]

        # 提取嵌入信息
        if message.embeds:
            metadata["embeds"] = [
                {
                    "title": embed.title,
                    "description": embed.description,
                    "url": embed.url,
                    "color": embed.color.value if embed.color else None,
                    "field_count": len(embed.fields),
                }
                for embed in message.embeds
            ]

        # 信号强度评估
        metadata["signal_strength"] = self._calculate_signal_strength(message.content)

        return metadata

    def _calculate_signal_strength(self, content: str) -> float:
        """
        计算信号强度（0.0-1.0）

        Args:
            content: 消息内容

        Returns:
            float: 信号强度分数
        """
        if not content:
            return 0.0

        content_lower = content.lower()
        strength = 0.0

        # 交易动作词权重
        action_count = sum(
            1
            for action in self.trading_actions
            if re.search(rf"\b{re.escape(action)}\b", content_lower)
        )
        strength += min(action_count * 0.3, 0.6)

        # 加密货币符号权重
        symbol_count = sum(
            1
            for symbol in self.crypto_symbols
            if re.search(rf"\b{re.escape(symbol)}\b", content_lower)
        )
        strength += min(symbol_count * 0.2, 0.4)

        # 交易术语权重
        term_count = sum(
            1
            for term in self.trading_terms
            if re.search(rf"\b{re.escape(term)}\b", content_lower)
        )
        strength += min(term_count * 0.1, 0.3)

        # 价格信息权重
        if re.search(r"\$\d+|\d+\s*(usdt|usd|k|m)", content_lower):
            strength += 0.2

        # 百分比权重
        if re.search(r"\d+(\.\d+)?%", content_lower):
            strength += 0.1

        # emoji权重
        emoji_count = sum(1 for emoji in self.trading_emojis if emoji in content)
        strength += min(emoji_count * 0.05, 0.15)

        return min(strength, 1.0)

    def get_processing_stats(self) -> Dict[str, Any]:
        """获取处理统计信息"""
        current_time = time.time()
        uptime = current_time - self.last_reset_time

        return {
            "uptime_seconds": uptime,
            "messages_processed": self.processing_stats["processed"],
            "messages_filtered": self.processing_stats["filtered"],
            "messages_duplicated": self.processing_stats["duplicated"],
            "api_calls_success": self.processing_stats["api_success"],
            "api_calls_failed": self.processing_stats["api_failed"],
            "processing_rate": self.processing_stats["processed"] / uptime
            if uptime > 0
            else 0,
        }


class TradingSignalClient(discord.Client):
    """
    用于监听交易信号的Discord客户端
    """

    def __init__(self, filter_config=None, user_id=None, *args, **kwargs):
        # 设置代理连接器
        self._setup_proxy()

        # 如果有代理配置，添加到kwargs中
        if self.proxy_connector and self.proxy_url:
            kwargs['connector'] = self.proxy_connector
            kwargs['proxy'] = self.proxy_url

        # discord.py-self 不需要 intents 参数
        super().__init__(*args, **kwargs)

        # 过滤配置 - 优先使用传入的参数，否则使用settings中的配置
        self.filter_config = filter_config or getattr(settings, 'discord_filter_config', None)
        
        # 如果filter_config为None，创建一个默认的空配置
        if self.filter_config is None:
            from ..core.config import DiscordFilterConfig
            self.filter_config = DiscordFilterConfig()
        
        self.default_user_id = user_id or getattr(settings, 'default_user_id', None)

        # 初始化核心组件
        self.monitored_servers = self._get_monitored_servers()
        self.deduplicator = MessageDeduplicator(
            window_minutes=settings.discord.deduplication_window,
            max_cache_size=settings.discord.message_cache_size,
        )
        
        # 初始化信号处理器
        self.signal_processor = SignalProcessor()

        # 统计信息
        self.processing_stats = {
            "processed": 0,
            "filtered": 0,
            "errors": 0
        }

        # 连接管理
        self.reconnect_attempts = 0
        self.max_reconnect_attempts = settings.discord.reconnect_attempts
        self.reconnect_delay = settings.discord.reconnect_delay
        self.last_heartbeat = time.time()
        self.connection_start_time = None

    def _setup_proxy(self):
        """设置代理连接器"""
        if settings.discord.proxy:
            try:
                # 解析代理URL
                parsed = urllib.parse.urlparse(settings.discord.proxy)

                if parsed.scheme in ['http', 'https']:
                    # 创建带代理的连接器
                    self.proxy_connector = aiohttp.TCPConnector()
                    self.proxy_url = settings.discord.proxy
                    logger.info(f"代理连接器已设置: {settings.discord.proxy}")
                elif parsed.scheme == 'socks5':
                    # 对于socks5代理，也使用TCPConnector，discord.py-self会处理
                    self.proxy_connector = aiohttp.TCPConnector()
                    self.proxy_url = settings.discord.proxy
                    logger.info(f"SOCKS5代理连接器已设置: {settings.discord.proxy}")
                else:
                    logger.error(f"不支持的代理协议: {parsed.scheme}")
                    self.proxy_connector = None
                    self.proxy_url = None
            except Exception as e:
                logger.error(f"代理设置失败: {e}")
                self.proxy_connector = None
                self.proxy_url = None
        else:
            self.proxy_connector = None
            self.proxy_url = None

    def _get_monitored_servers(self) -> Set[str]:
        """
        获取监控的服务器ID集合 - 简化版本，从统一配置中提取

        Returns:
            Set[str]: 监控的服务器ID集合，如果配置为空则监控所有服务器
        """
        try:
            # 从统一的过滤配置中获取服务器ID
            if self.filter_config and hasattr(self.filter_config, 'server_ids') and self.filter_config.server_ids:
                return self.filter_config.server_ids

            # 如果没有配置特定服务器，返回空集合（监控所有服务器）
            return set()

        except Exception as e:
            logger.error(f"获取监控服务器配置时发生异常: {e}")
            return set()

    def _should_process_message(self, message: discord.Message) -> bool:
        """
        优化的消息过滤逻辑 - 支持服务器ID、频道ID、用户ID和消息类型过滤

        Args:
            message: Discord消息对象

        Returns:
            bool: 是否应该处理此消息
        """
        try:
            # 如果过滤功能未启用，处理所有消息
            if not self.filter_config or not getattr(self.filter_config, 'enabled', True):
                return True

            # 如果没有配置任何过滤条件，处理所有消息
            if hasattr(self.filter_config, 'is_empty') and callable(getattr(self.filter_config, 'is_empty')):
                if self.filter_config.is_empty():
                    return True

            # 检查服务器ID（处理私聊消息的情况）
            # 如果没有配置服务器ID，则不进行服务器过滤（测试模式）
            server_ids = getattr(self.filter_config, 'server_ids', None)
            if server_ids and len(server_ids) > 0:
                # 私聊消息没有服务器，使用特殊标识"DM"
                server_id = str(message.guild.id) if message.guild else "DM"
                if server_id not in server_ids:
                    return False

            # 检查频道ID（使用set进行O(1)查找）
            # 如果没有配置频道ID，则不进行频道过滤（测试模式）
            channel_ids = getattr(self.filter_config, 'channel_ids', None)
            if channel_ids and len(channel_ids) > 0:
                try:
                    channel_id = str(message.channel.id)
                    if channel_id not in channel_ids:
                        return False
                except (AttributeError, TypeError):
                    logger.warning("无法获取频道ID，跳过频道过滤")
                    return False

            # 检查作者ID（使用set进行O(1)查找）
            # 如果没有配置作者ID，则不进行作者过滤（测试模式）
            author_ids = getattr(self.filter_config, 'author_ids', None)
            if author_ids and len(author_ids) > 0:
                try:
                    author_id = str(message.author.id)
                    if author_id not in author_ids:
                        return False
                except (AttributeError, TypeError):
                    logger.warning("无法获取作者ID，跳过作者过滤")
                    return False

            # 检查消息类型过滤
            if hasattr(self.filter_config, 'should_filter_by_type') and callable(getattr(self.filter_config, 'should_filter_by_type')):
                if self.filter_config.should_filter_by_type():
                    message_type = self._get_message_type(message)
                    allowed_types = getattr(self.filter_config, 'allowed_message_types', None)
                    if allowed_types and message_type not in allowed_types:
                        return False

            return True

        except Exception as e:
            logger.error(
                "消息过滤时发生异常",
                error=str(e),
                channel_id=getattr(message.channel, 'id', None),
                exc_info=True
            )
            # 异常时默认处理消息，避免丢失重要信号
            return True

    def _get_message_type(self, message: discord.Message) -> str:
        """
        判断Discord消息类型

        Args:
            message: Discord消息对象

        Returns:
            str: 消息类型（text/embed/attachment）
        """
        try:
            # 检查是否有附件（图片、文件等）
            if message.attachments:
                return "attachment"

            # 检查是否有嵌入内容（富文本消息）
            if message.embeds:
                return "embed"

            # 默认为文本消息
            return "text"

        except Exception as e:
            logger.warning(f"判断消息类型时发生异常: {e}")
            return "text"  # 异常时默认为文本消息

    def _get_user_id_for_message(self, message: discord.Message) -> str:
        """
        获取消息对应的用户ID - 简化版本，使用统一的默认用户ID

        Args:
            message: Discord消息对象

        Returns:
            str: 用户ID，保证不返回None
        """
        try:
            # 使用默认用户ID（简化配置后不再有频道特定的用户ID）
            if self.default_user_id and self.default_user_id.strip():
                return self.default_user_id.strip()

            # 最后的fallback，确保不返回None
            fallback_user_id = "708db973-fc1f-4be0-9c52-a9736a10372c"
            logger.warning(
                "无法获取有效的用户ID，使用fallback用户ID",
                channel_id=getattr(message.channel, 'id', None),
                fallback_user_id=fallback_user_id
            )
            return fallback_user_id

        except (AttributeError, TypeError) as e:
            logger.error(f"获取用户ID时发生异常: {e}")
            # 异常时返回fallback用户ID
            return "708db973-fc1f-4be0-9c52-a9736a10372c"



    async def on_ready(self):
        """
        当客户端连接成功时调用
        """
        logger.info(f"Discord client ready - logged in as {self.user}")
        channel_count = len(getattr(self.filter_config, 'channel_ids', [])) if self.filter_config else 0
        logger.info(f"Monitoring {channel_count} channels from {len(self.monitored_servers)} servers")

        # 输出优化后的过滤配置信息
        if self.filter_config:
            logger.info(
                "Discord消息过滤配置已加载",
                enabled=getattr(self.filter_config, 'enabled', True),
                server_ids_count=len(getattr(self.filter_config, 'server_ids', [])),
                channel_ids_count=len(getattr(self.filter_config, 'channel_ids', [])),
                author_ids_count=len(getattr(self.filter_config, 'author_ids', [])),
                allowed_message_types=list(getattr(self.filter_config, 'allowed_message_types', [])),
                message_type_filtering=getattr(self.filter_config, 'should_filter_by_type', lambda: False)() if callable(getattr(self.filter_config, 'should_filter_by_type', None)) else False,
                is_empty=getattr(self.filter_config, 'is_empty', lambda: True)() if callable(getattr(self.filter_config, 'is_empty', None)) else True
            )

    async def on_message(self, message: discord.Message):
        """
        当收到消息时调用 - 简洁的消息处理流程

        Args:
            message: Discord消息对象
        """
        try:
            # 忽略自己的消息
            if message.author == self.user:
                return

            # 应用过滤逻辑
            if not self._should_process_message(message):
                self.processing_stats["filtered"] += 1
                return

            # 获取用户ID（现在保证不返回None）
            user_id = self._get_user_id_for_message(message)
            # 由于_get_user_id_for_message现在保证返回有效值，不再需要None检查

            # 检查消息是否重复
            if self.deduplicator.is_duplicate(
                message.content, message.author.id, message.channel.id
            ):
                logger.debug(
                    "重复消息已忽略",
                    content_preview=message.content[:50] if message.content else "无内容",
                    channel_id=message.channel.id,
                    author_id=message.author.id
                )
                return

            # 处理消息：存储到数据库
            await self.process_discord_message(message, user_id)
            self.processing_stats["processed"] += 1

        except Exception as e:
            logger.error(
                "处理Discord消息时发生错误",
                error=str(e),
                channel_id=message.channel.id if message else None,
                server_id=message.guild.id if message and message.guild else None,
                exc_info=True
            )
            self.processing_stats["errors"] += 1

    async def process_discord_message(self, message: discord.Message, user_id: str):
        """
        处理Discord消息：直接存储到数据库并可选地进行AI处理

        Args:
            message: Discord消息
            user_id: 关联的用户ID (UUID字符串)
        """
        content = message.content.strip()

        # 提取信号元数据
        metadata = self.signal_processor.extract_signal_metadata(message)

        # 记录消息
        logger.info(
            "Processing Discord message",
            channel_id=message.channel.id,
            channel_name=getattr(message.channel, 'name', 'Unknown'),
            user_id=user_id,
            content_preview=content[:100],
            metadata=metadata,
        )

        try:
            # 直接创建Signal记录
            await self._create_signal_record(message, user_id, metadata)

            # 可选：如果需要AI处理，调用Agent服务
            # 这里可以根据配置决定是否进行AI处理
            # await self._process_with_agent(message, user_id, metadata)

        except Exception as e:
            logger.error(
                "Failed to process Discord message",
                error=str(e),
                message_id=message.id,
                user_id=user_id
            )
            raise

    async def _create_signal_record(self, message: discord.Message, user_id: str, metadata: dict):
        """
        直接创建Signal记录到数据库

        Args:
            message: Discord消息
            user_id: 用户ID (UUID字符串)
            metadata: 消息元数据
        """
        from ..core.models import User
        from ..core.schemas import CreateSignalRequest, PlatformType, MessageType
        from ..core.database import get_db
        from .signal_service import signal_service
        import uuid

        # 获取数据库会话
        async for db in get_db():
            try:
                # 获取用户对象
                from sqlalchemy import select
                stmt = select(User).where(User.id == uuid.UUID(user_id))
                result = await db.execute(stmt)
                user = result.scalar_one_or_none()

                if not user:
                    logger.error("用户不存在", user_id=user_id)
                    return

                # 创建信号请求
                signal_request = CreateSignalRequest(
                    platform=PlatformType.DISCORD,
                    content=message.content,
                    raw_content=message.content,
                    channel_name=getattr(message.channel, "name", "Unknown"),
                    author_name=message.author.display_name,
                    message_type=MessageType.TEXT,
                    metadata=metadata or {}
                )

                # 使用统一的信号服务处理信号
                signal_response, agent_task_response = await signal_service.create_discord_signal(
                    content=message.content,
                    user=user,
                    db=db,
                    platform_message_id=str(message.id),
                    channel_id=str(message.channel.id),
                    channel_name=getattr(message.channel, "name", "Unknown"),
                    author_id=str(message.author.id),
                    author_name=message.author.display_name,
                    raw_content=message.content,
                    metadata=metadata,
                    auto_trigger_agent=True  # Discord信号自动触发Agent处理
                )

                logger.info(
                    "Discord信号处理完成",
                    signal_id=signal_response.id,
                    user_id=user_id,
                    platform_message_id=message.id,
                    channel_id=message.channel.id,
                    agent_task_id=agent_task_response.task_id if agent_task_response else None,
                    is_new_signal=agent_task_response is not None
                )

                return signal_response

            except Exception as e:
                await db.rollback()
                logger.error(
                    "Failed to create signal record",
                    error=str(e),
                    user_id=user_id,
                    message_id=message.id
                )
                raise
            finally:
                await db.close()

    async def process_trading_signal(self, message: discord.Message, user_id: str):
        """
        处理交易信号（已更新为使用SignalService）

        Args:
            message: Discord消息
            user_id: 关联的用户ID (UUID字符串)
        """
        content = message.content.strip()

        # 提取信号元数据
        metadata = self.signal_processor.extract_signal_metadata(message)

        # 记录消息
        logger.info(
            "Processing trading signal via SignalService",
            channel_id=message.channel.id,
            channel_name=message.channel.name,
            user_id=user_id,
            content_preview=content[:100],
            metadata=metadata,
        )

        # 使用SignalService处理信号
        try:
            from ..services.signal_service import signal_service
            from ..core.models import User
            from ..core.database import get_async_session
            from sqlalchemy import select
            import uuid

            # 获取数据库会话
            async for db in get_async_session():
                try:
                    # 查找用户
                    user_uuid = uuid.UUID(user_id)
                    stmt = select(User).where(User.id == user_uuid)
                    result = await db.execute(stmt)
                    user = result.scalar_one_or_none()

                    if not user:
                        logger.error("User not found for Discord signal", user_id=user_id)
                        return

                    # 通过SignalService创建Discord信号并触发Agent处理
                    signal_response, agent_task_response = await signal_service.create_discord_signal(
                        content=content,
                        user=user,
                        db=db,
                        platform_message_id=str(message.id),
                        channel_id=str(message.channel.id),
                        author_id=str(message.author.id),
                        raw_content=content,
                        channel_name=message.channel.name,
                        author_name=str(message.author),
                        metadata=metadata,
                        auto_trigger_agent=True
                    )

                    # 更新统计
                    self.signal_processor.processing_stats["api_success"] += 1

                    logger.info(
                        "Discord signal processed successfully via SignalService",
                        signal_id=signal_response.id,
                        agent_task_id=agent_task_response.task_id if agent_task_response else None,
                        user_id=user_id,
                        message_id=message.id
                    )

                    return

                except Exception as e:
                    logger.error(
                        "Failed to process Discord signal via SignalService",
                        error=str(e),
                        user_id=user_id,
                        message_id=message.id
                    )
                    # 继续执行原有的API调用作为回退
                    break
                finally:
                    await db.close()
                    break

        except Exception as e:
            logger.error(
                "SignalService import or setup failed, falling back to API call",
                error=str(e)
            )

        # 构建API请求数据
        request_data = {
            "text": content,
            "source": "discord",
            "priority": "normal",
            "metadata": metadata,
        }

        # 发送到后端API处理，带重试机制
        max_retries = 3
        retry_delay = 2

        for attempt in range(max_retries):
            try:
                timeout = aiohttp.ClientTimeout(total=30)
                async with aiohttp.ClientSession(timeout=timeout) as session:
                    # 构建认证头（如果需要）
                    headers = {
                        "Content-Type": "application/json",
                        "User-Agent": "Discord-Signal-Listener/1.0",
                    }

                    # 如果配置了API密钥，添加认证头
                    if hasattr(settings, 'api') and hasattr(settings.api, 'api_key') and settings.api.api_key:
                        headers["Authorization"] = f"Bearer {settings.api.api_key}"

                    async with session.post(
                        f"{settings.api.url}/api/v1/agent/process",
                        headers=headers,
                        json=request_data,
                    ) as response:
                        if response.status == 200:
                            result = await response.json()
                            self.signal_processor.processing_stats["api_success"] += 1
                            logger.info(
                                "Signal processed successfully",
                                task_id=result.get("data", {}).get("task_id"),
                                status=result.get("data", {}).get("status"),
                            )
                            return

                        elif response.status == 429:  # 限流
                            retry_after = int(
                                response.headers.get("Retry-After", retry_delay)
                            )
                            logger.warning(
                                "Rate limited, retrying",
                                retry_after=retry_after,
                                attempt=attempt + 1,
                            )
                            await asyncio.sleep(retry_after)
                            continue

                        else:
                            error_text = await response.text()
                            logger.error(
                                "API request failed",
                                status=response.status,
                                error=error_text,
                                attempt=attempt + 1,
                            )

                            if attempt < max_retries - 1:
                                await asyncio.sleep(retry_delay * (attempt + 1))
                                continue

            except asyncio.TimeoutError:
                logger.error("API request timeout", attempt=attempt + 1)
                if attempt < max_retries - 1:
                    await asyncio.sleep(retry_delay * (attempt + 1))
                    continue

            except Exception as e:
                logger.error(
                    "Error sending signal to API",
                    error=str(e),
                    attempt=attempt + 1,
                )
                if attempt < max_retries - 1:
                    await asyncio.sleep(retry_delay * (attempt + 1))
                    continue

        # 所有重试都失败了
        self.signal_processor.processing_stats["api_failed"] += 1
        logger.error(
            "Failed to process signal after all retries", message_id=message.id
        )

    async def on_disconnect(self):
        """当连接断开时调用"""
        logger.warning(
            "Discord connection lost",
            reconnect_attempts=self.reconnect_attempts,
            max_attempts=self.max_reconnect_attempts,
        )

        # 发送系统状态通知
        await self._send_system_status_notification("disconnected", "Discord连接已断开")

    async def on_resumed(self):
        """当连接恢复时调用"""
        logger.info("Discord connection resumed")
        self.reconnect_attempts = 0

        # 发送系统状态通知
        await self._send_system_status_notification("connected", "Discord连接已恢复")

    async def on_error(self, event, *args, **kwargs):
        """当发生错误时调用"""
        error_info = {
            "event": event,
            "args": str(args) if args else None,
            "kwargs": str(kwargs) if kwargs else None,
            "reconnect_attempts": self.reconnect_attempts,
        }

        logger.error("Discord client error", **error_info)

        # 根据错误类型决定是否需要重连
        if self._should_reconnect_on_error(event):
            self.reconnect_attempts += 1

    async def on_connect(self):
        """当连接建立时调用"""
        logger.info("Discord client connected")

    async def on_ready(self):
        """当客户端准备就绪时调用"""
        logger.info(f"Discord client ready - logged in as {self.user}")
        channel_count = len(getattr(self.filter_config, 'channel_ids', [])) if self.filter_config else 0
        logger.info(f"Monitoring {channel_count} channels from {len(self.monitored_servers)} servers")

        # 发送系统状态通知
        await self._send_system_status_notification(
            "online", f"Discord监听器已启动，监控{channel_count}个频道"
        )

        # 重置重连计数
        self.reconnect_attempts = 0

    def _should_reconnect_on_error(self, event: str) -> bool:
        """判断是否应该在特定错误后重连"""
        reconnect_events = {
            "on_socket_response",
            "on_socket_raw_receive",
            "on_socket_raw_send",
            "gateway_error",
            "connection_lost",
        }
        return event in reconnect_events

    async def _send_system_status_notification(self, status: str, message: str):
        """发送系统状态通知"""
        try:
            # 这里可以通过WebSocket或其他方式发送系统状态通知
            # 暂时只记录日志
            logger.info("System status notification", status=status, message=message)
        except Exception as e:
            logger.error("Failed to send system status notification", error=str(e))

    def get_status(self) -> Dict[str, Any]:
        """获取详细的客户端状态"""
        current_time = time.time()

        status = {
            "connected": not self.is_closed(),
            "user": str(self.user) if self.user else None,
            "user_id": str(self.user.id) if self.user else None,
            "monitored_channels": len(getattr(self.filter_config, 'channel_ids', [])) if self.filter_config else 0,
            "monitored_servers": len(self.monitored_servers),
            "reconnect_attempts": self.reconnect_attempts,
            "max_reconnect_attempts": self.max_reconnect_attempts,
            "reconnect_delay": self.reconnect_delay,
            "last_heartbeat": self.last_heartbeat,
            "time_since_heartbeat": current_time - self.last_heartbeat,
            "connection_uptime": current_time - self.connection_start_time
            if self.connection_start_time
            else 0,
            "processing_stats": self.signal_processor.get_processing_stats(),
            "deduplicator_stats": {
                "cache_size": len(self.deduplicator.message_hashes),
                "window_minutes": self.deduplicator.window_minutes,
                "max_cache_size": self.deduplicator.max_cache_size,
            },
            "proxy_config": {
                "enabled": hasattr(self, 'proxy_url') and self.proxy_url is not None,
                "proxy_url": getattr(self, 'proxy_url', None),
            },
        }

        # 添加监控配置详细信息
        channel_ids = getattr(self.filter_config, 'channel_ids', []) if self.filter_config else []
        if channel_ids:
            status["channel_details"] = [
                {"channel_id": channel_id, "monitored": True}
                for channel_id in channel_ids
            ]

        return status


async def start_discord_listener_with_config(discord_config):
    """
    使用数据库配置启动Discord监听器
    """
    from ..core.security import decrypt_sensitive_data
    
    if not discord_config or not discord_config.enabled:
        logger.warning("Discord config is not enabled")
        return

    # 解密token
    try:
        token = decrypt_sensitive_data(discord_config.encrypted_token)
    except Exception as e:
        logger.error(f"Failed to decrypt Discord token: {e}")
        return

    if not token:
        logger.warning("Discord token not configured, listener will not start")
        return

    # 构建过滤配置
    from ..core.config import DiscordFilterConfig
    filter_config = DiscordFilterConfig(
        enabled=discord_config.enabled,
        server_ids=discord_config.server_ids,
        channel_ids=discord_config.channel_ids,
        author_ids=discord_config.author_ids,
        allowed_message_types=discord_config.allowed_message_types
    )

    # 在测试模式下，允许没有配置频道ID的情况（接受所有消息）
    if not filter_config.channel_ids:
        logger.info("No specific channels configured - will monitor all channels (test mode)")
    else:
        logger.info(f"Monitoring {len(filter_config.channel_ids)} specific channels")

    client = None
    max_reconnect_attempts = settings.discord.reconnect_attempts
    reconnect_delay = settings.discord.reconnect_delay

    while True:
        try:
            # 创建客户端并启动
            client = TradingSignalClient(
                filter_config=filter_config,
                user_id=str(discord_config.user_id)
            )

            # 记录代理状态
            if settings.discord.proxy:
                logger.info(f"使用代理连接Discord: {settings.discord.proxy}")
            else:
                logger.info("使用直连模式连接Discord")

            logger.info(f"Starting Discord listener with config: {discord_config.source_name}")
            await client.start(token)

        except discord.LoginFailure:
            logger.error("Discord login failed - invalid token")
            break

        except discord.ConnectionClosed:
            logger.warning("Discord connection closed")
            if client and client.reconnect_attempts < max_reconnect_attempts:
                client.reconnect_attempts += 1
                logger.info(
                    "Attempting to reconnect",
                    attempt=client.reconnect_attempts,
                    max_attempts=max_reconnect_attempts,
                )
                await asyncio.sleep(reconnect_delay)
                continue
            else:
                logger.error("Max reconnection attempts reached")
                break

        except Exception as e:
            logger.error("Discord listener failed", error=str(e))
            if client and client.reconnect_attempts < max_reconnect_attempts:
                client.reconnect_attempts += 1
                logger.info(
                    "Attempting to restart after error",
                    attempt=client.reconnect_attempts,
                    max_attempts=max_reconnect_attempts,
                )
                await asyncio.sleep(reconnect_delay)
                continue
            else:
                logger.error("Max restart attempts reached")
                break

        finally:
            if client and not client.is_closed():
                await client.close()


async def start_discord_listener():
    """
    启动Discord监听器，带自动重连功能（使用配置文件）
    """
    if not settings.discord.token:
        logger.warning("Discord token not configured, listener will not start")
        return

    # 检查是否有配置的频道ID（从统一配置中获取）
    # 在测试模式下，允许没有配置频道ID的情况（接受所有消息）
    filter_config = getattr(settings, 'discord_filter_config', None)
    if filter_config and getattr(filter_config, 'channel_ids', None):
        logger.info(f"Monitoring {len(filter_config.channel_ids)} specific channels")
    else:
        logger.info("No specific channels configured - will monitor all channels (test mode)")

    client = None
    max_reconnect_attempts = settings.discord.reconnect_attempts
    reconnect_delay = settings.discord.reconnect_delay

    while True:
        try:
            # 创建客户端并启动
            client = TradingSignalClient()

            # 记录代理状态
            if settings.discord.proxy:
                logger.info(f"使用代理连接Discord: {settings.discord.proxy}")
            else:
                logger.info("使用直连模式连接Discord")

            logger.info("Starting Discord listener...")
            await client.start(settings.discord.token)

        except discord.LoginFailure:
            logger.error("Discord login failed - invalid token")
            break

        except discord.ConnectionClosed:
            logger.warning("Discord connection closed")
            if client and client.reconnect_attempts < max_reconnect_attempts:
                client.reconnect_attempts += 1
                logger.info(
                    "Attempting to reconnect",
                    attempt=client.reconnect_attempts,
                    max_attempts=max_reconnect_attempts,
                )
                await asyncio.sleep(reconnect_delay)
                continue
            else:
                logger.error("Max reconnection attempts reached")
                break

        except Exception as e:
            logger.error("Discord listener failed", error=str(e))
            if client and client.reconnect_attempts < max_reconnect_attempts:
                client.reconnect_attempts += 1
                logger.info(
                    "Attempting to restart after error",
                    attempt=client.reconnect_attempts,
                    max_attempts=max_reconnect_attempts,
                )
                await asyncio.sleep(reconnect_delay)
                continue
            else:
                logger.error("Max restart attempts reached")
                break

        finally:
            if client and not client.is_closed():
                await client.close()


class DiscordListenerManager:
    """Discord监听器管理器"""

    def __init__(self):
        self.client: Optional[TradingSignalClient] = None
        self.listener_task: Optional[asyncio.Task] = None
        self.is_running = False
        self.current_config = None

    async def start_with_config(self, discord_config):
        """使用指定配置启动监听器"""
        if self.is_running:
            logger.warning("Discord listener is already running")
            return

        self.current_config = discord_config
        self.listener_task = asyncio.create_task(start_discord_listener_with_config(discord_config))
        self.is_running = True
        logger.info(f"Discord listener manager started with config: {discord_config.source_name}")

    async def start(self):
        """启动监听器（使用配置文件）"""
        if self.is_running:
            logger.warning("Discord listener is already running")
            return

        self.listener_task = asyncio.create_task(start_discord_listener())
        self.is_running = True
        logger.info("Discord listener manager started")

    async def stop(self):
        """停止监听器"""
        if not self.is_running:
            return

        if self.listener_task:
            self.listener_task.cancel()
            try:
                await self.listener_task
            except asyncio.CancelledError:
                pass

        if self.client and not self.client.is_closed():
            await self.client.close()

        self.is_running = False
        self.current_config = None
        logger.info("Discord listener manager stopped")

    def get_status(self) -> Dict[str, Any]:
        """获取监听器状态"""
        return {
            "is_running": self.is_running,
            "task_done": self.listener_task.done() if self.listener_task else True,
            "client_status": self.client.get_status() if self.client else None,
            "current_config": {
                "id": str(self.current_config.id) if self.current_config else None,
                "name": self.current_config.source_name if self.current_config else None
            } if self.current_config else None
        }


# 全局监听器管理器实例
discord_manager = DiscordListenerManager()


def run_discord_listener():
    """
    运行Discord监听器（阻塞调用）
    """
    asyncio.run(start_discord_listener())
