"""
价格监控服务 - 监控触发条件订单
"""

import asyncio
import json
from datetime import datetime, timedelta
from decimal import Decimal
from typing import Any, Dict, List, Optional, Set, Tuple

import aiohttp
import structlog
from sqlalchemy import select, update
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker

# from ..api.v1.orders import create_order  # 暂时注释，未使用
from ..core.config import settings
from ..core.models import ConditionalOrder
from ..core.schemas import (
    OrderCreate,
    TradeSide,
    OrderType,
    ConditionalOrderStatus,
    OrderSide,
)
from ..core.ws_manager import WebSocketManager
from ..core.ws_schemas import (
    ConditionalOrderTriggerEvent,
    ConditionalOrderTriggerPayload,
    NotificationEvent,
    NotificationPayload,
    RiskAlertEvent,
    RiskAlertPayload,
)
from ..services.exchange import ExchangeService

# 配置结构化日志
logger = structlog.get_logger()

# 创建数据库会话工厂
engine = create_async_engine(settings.db.url)
AsyncSessionLocal = sessionmaker(engine, class_=AsyncSession, expire_on_commit=False)


class PriceWatcher:
    """
    价格观察者服务，用于监控条件订单的触发条件
    """

    def __init__(self):
        """
        初始化价格观察者
        """
        self.exchange_service = ExchangeService()
        self.current_prices: Dict[str, Decimal] = {}
        self.symbols_to_watch: Set[str] = set()
        self.is_running = False
        self._task = None
        self.ws_manager = WebSocketManager()

        # 监控配置
        self.check_interval = 5  # 检查间隔（秒）
        self.price_update_interval = 2  # 价格更新间隔（秒）
        self.max_retries = 3  # 最大重试次数
        self.retry_delay = 1  # 重试延迟（秒）

        # 性能监控
        self.last_check_time: Optional[datetime] = None
        self.check_count = 0
        self.trigger_count = 0
        self.error_count = 0

        # 风险控制
        self.max_triggers_per_minute = 10
        self.trigger_timestamps: List[datetime] = []

    async def start(self):
        """
        启动价格监控服务
        """
        if self.is_running:
            logger.warning("PriceWatcher is already running")
            return

        self.is_running = True
        self._task = asyncio.create_task(self._periodic_check())
        logger.info("PriceWatcher started with check_interval=%ds", self.check_interval)

        # 发送系统状态通知
        await self._send_system_status_notification("online", "价格监控服务已启动")

    async def stop(self):
        """
        停止价格监控服务
        """
        self.is_running = False
        if self._task:
            self._task.cancel()
            try:
                await self._task
            except asyncio.CancelledError:
                pass
        logger.info("PriceWatcher stopped")

        # 发送系统状态通知
        await self._send_system_status_notification("offline", "价格监控服务已停止")

    async def _periodic_check(self):
        """
        定期检查价格并处理条件订单
        """
        while self.is_running:
            try:
                start_time = datetime.utcnow()
                self.check_count += 1

                # 获取要监控的交易对
                await self._refresh_symbols_to_watch()

                if self.symbols_to_watch:
                    # 获取当前价格
                    await self._update_current_prices()

                    # 检查条件订单
                    triggered_orders = await self._check_conditional_orders()

                # 记录性能指标
                self.last_check_time = datetime.utcnow()
                check_duration = (self.last_check_time - start_time).total_seconds()

                if check_duration > self.check_interval * 0.8:  # 如果检查时间超过间隔的80%
                    logger.warning(
                        f"Price check took {check_duration:.2f}s, approaching interval limit"
                    )

                # 清理过期的触发时间戳
                self._cleanup_trigger_timestamps()

                # 等待下次检查
                await asyncio.sleep(max(0, self.check_interval - check_duration))

            except Exception as e:
                self.error_count += 1
                logger.error("Error in price watcher periodic check", error=str(e))

                # 发送错误通知
                await self._send_error_notification(f"价格监控检查失败: {str(e)}")

                # 如果错误过多，降低检查频率
                if self.error_count > 5:
                    await asyncio.sleep(self.check_interval * 2)
                else:
                    await asyncio.sleep(self.check_interval)

    async def _refresh_symbols_to_watch(self):
        """
        从数据库中获取需要监控的交易对
        """
        try:
            async with AsyncSessionLocal() as session:
                # 查询所有待处理的条件订单
                query = select(ConditionalOrder).where(
                    ConditionalOrder.status == "PENDING"
                )
                result = await session.execute(query)
                orders = result.scalars().all()

                # 收集所有需要监控的交易对
                symbols = set()
                for order in orders:
                    # 获取条件订单中的交易对
                    symbol = order.trigger_condition.get("symbol")
                    if symbol:
                        symbols.add(symbol)

                self.symbols_to_watch = symbols
                logger.info(
                    f"Refreshed symbols to watch: {len(self.symbols_to_watch)} symbols"
                )

        except Exception as e:
            logger.error("Failed to refresh symbols to watch", error=str(e))

    async def _update_current_prices(self):
        """
        更新当前价格
        """
        if not self.symbols_to_watch:
            return

        try:
            async with aiohttp.ClientSession() as session:
                # 假设我们使用某个API获取价格
                # 这里示例使用Binance API
                binance_url = "https://api.binance.com/api/v3/ticker/price"

                async with session.get(binance_url) as response:
                    if response.status == 200:
                        data = await response.json()

                        # 更新价格
                        for ticker in data:
                            symbol = ticker["symbol"].replace("USDT", "/USDT")  # 转换格式
                            if symbol in self.symbols_to_watch:
                                self.current_prices[symbol] = float(ticker["price"])

                        logger.debug(
                            f"Updated prices for {len(self.current_prices)} symbols"
                        )
                    else:
                        logger.error(f"Failed to get prices: {response.status}")

        except Exception as e:
            logger.error("Failed to update current prices", error=str(e))

    async def _check_conditional_orders(self) -> List[ConditionalOrder]:
        """
        检查条件订单是否触发
        """
        triggered_orders = []

        if not self.current_prices:
            return triggered_orders

        try:
            # 检查触发频率限制
            if not self._can_trigger_more_orders():
                logger.warning("触发频率超限，跳过本次检查")
                return triggered_orders

            async with AsyncSessionLocal() as session:
                # 查询所有待处理的条件订单
                query = select(ConditionalOrder).where(
                    ConditionalOrder.status == "PENDING"
                )
                result = await session.execute(query)
                orders = result.scalars().all()

                logger.debug(f"检查 {len(orders)} 个待处理条件订单")

                # 检查每个条件订单
                for order in orders:
                    try:
                        # 获取触发条件
                        condition = order.trigger_condition
                        symbol = condition.get("symbol")
                        price_threshold = float(condition.get("price", 0))
                        condition_type = condition.get("type")

                        # 如果没有当前价格，跳过
                        if symbol not in self.current_prices:
                            logger.warning(f"交易对 {symbol} 无当前价格数据")
                            continue

                        current_price = self.current_prices[symbol]

                        # 检查触发条件
                        trigger_result = self._evaluate_trigger_condition(
                            order,
                            current_price,
                            condition_type,
                            price_threshold,
                        )

                        if trigger_result["should_trigger"]:
                            success = await self._trigger_order(
                                order, session, trigger_result
                            )
                            if success:
                                triggered_orders.append(order)
                                self.trigger_count += 1
                                self.trigger_timestamps.append(datetime.utcnow())

                                logger.info(
                                    f"订单 {order.id} 已触发: {symbol} {condition_type} {price_threshold}, 当前价格: {current_price}"
                                )

                                # 检查是否需要暂停触发
                                if (
                                    len(self.trigger_timestamps)
                                    >= self.max_triggers_per_minute
                                ):
                                    logger.warning("达到每分钟最大触发次数，暂停触发")
                                    break

                    except Exception as e:
                        logger.error(f"处理条件订单 {order.id} 时出错: {e}")
                        continue

                # 提交所有更改
                await session.commit()

                if triggered_orders:
                    logger.info(f"本次检查触发了 {len(triggered_orders)} 个条件订单")

        except Exception as e:
            logger.error("检查条件订单时出错", error=str(e))
            await self._send_error_notification(f"条件订单检查失败: {str(e)}")

        return triggered_orders

    def _evaluate_trigger_condition(
        self,
        order: ConditionalOrder,
        current_price: Decimal,
        condition_type: str = None,
        price_threshold: float = None,
    ) -> Dict[str, Any]:
        """评估触发条件"""
        try:
            # 如果没有传入条件参数，从订单中获取
            if condition_type is None or price_threshold is None:
                condition = order.trigger_condition
                condition_type = condition.get("type", order.condition_type)
                price_threshold = float(condition.get("price", order.trigger_price))

            trigger_price = Decimal(str(price_threshold))
            should_trigger = False
            trigger_reason = ""

            # 检查基本触发条件
            if condition_type in ["above", "price_above"]:
                should_trigger = current_price >= trigger_price
                trigger_reason = f"价格 {current_price} >= 触发价格 {trigger_price}"
            elif condition_type in ["below", "price_below"]:
                should_trigger = current_price <= trigger_price
                trigger_reason = f"价格 {current_price} <= 触发价格 {trigger_price}"
            elif condition_type == "cross_above":
                # 需要历史价格数据来判断是否穿越
                should_trigger = current_price >= trigger_price
                trigger_reason = f"价格穿越上方: {current_price} >= {trigger_price}"
            elif condition_type == "cross_below":
                should_trigger = current_price <= trigger_price
                trigger_reason = f"价格穿越下方: {current_price} <= {trigger_price}"

            # 计算价格偏差百分比
            price_deviation = abs(current_price - trigger_price) / trigger_price * 100

            return {
                "should_trigger": should_trigger,
                "trigger_reason": trigger_reason,
                "current_price": current_price,
                "trigger_price": trigger_price,
                "price_deviation": price_deviation,
                "condition_type": condition_type,
            }

        except Exception as e:
            logger.error(f"评估触发条件时出错: {e}")
            return {
                "should_trigger": False,
                "trigger_reason": f"评估失败: {str(e)}",
                "current_price": current_price,
                "trigger_price": Decimal("0"),
                "price_deviation": 0,
                "condition_type": condition_type,
            }

    def _can_trigger_more_orders(self) -> bool:
        """检查是否可以触发更多订单（频率限制）"""
        now = datetime.utcnow()
        one_minute_ago = now - timedelta(minutes=1)

        # 清理过期的时间戳
        self.trigger_timestamps = [
            ts for ts in self.trigger_timestamps if ts > one_minute_ago
        ]

        return len(self.trigger_timestamps) < self.max_triggers_per_minute

    def _cleanup_trigger_timestamps(self):
        """清理过期的触发时间戳"""
        one_minute_ago = datetime.utcnow() - timedelta(minutes=1)
        self.trigger_timestamps = [
            ts for ts in self.trigger_timestamps if ts > one_minute_ago
        ]

    async def _trigger_order(
        self,
        order: ConditionalOrder,
        session: AsyncSession,
        trigger_result: Dict[str, Any],
    ) -> bool:
        """
        触发条件订单
        """
        try:
            # 发送触发事件通知
            await self._send_trigger_notification(order, trigger_result)

            # 更新订单状态为已触发
            order.status = ConditionalOrderStatus.TRIGGERED
            order.triggered_at = datetime.utcnow()
            order.execution_price = trigger_result["current_price"]

            # 获取订单计划
            action_plan = order.action_plan

            # 创建实际订单
            order_data = OrderCreate(
                symbol=action_plan["symbol"],
                side=OrderSide(action_plan["side"]),
                type=OrderType(action_plan["type"]),
                quantity=action_plan["quantity"],
                price=action_plan.get("price"),
            )

            # 调用订单创建API
            # 注意：这里需要传递正确的用户ID
            # created_order = await create_order(order_data, user_id=order.user_id)

            logger.info(f"条件订单 {order.id} 已触发并执行: {trigger_result['trigger_reason']}")

            # 发送成功通知
            await self._send_success_notification(order, trigger_result)

            return True

        except Exception as e:
            # 如果执行失败，更新状态为失败
            order.status = ConditionalOrderStatus.FAILED
            order.error_message = str(e)
            logger.error(f"触发订单 {order.id} 失败: {e}")

            # 发送失败通知
            await self._send_failure_notification(order, str(e))

            return False

    async def _send_trigger_notification(
        self, order: ConditionalOrder, trigger_result: Dict[str, Any]
    ):
        """发送触发通知"""
        try:
            event = ConditionalOrderTriggerEvent(
                event_type="CONDITIONAL_ORDER_TRIGGER",
                payload=ConditionalOrderTriggerPayload(
                    conditional_order_id=order.id,
                    trigger_condition=order.trigger_condition,
                    current_price=trigger_result["current_price"],
                    trigger_price=trigger_result["trigger_price"],
                    symbol=order.action_plan["symbol"],
                    action_plan=order.action_plan,
                    execution_status="executing",
                ),
            )

            await self.ws_manager.broadcast_to_user(order.user_id, event.dict())

        except Exception as e:
            logger.error(f"发送触发通知失败: {e}")

    async def _send_success_notification(
        self, order: ConditionalOrder, trigger_result: Dict[str, Any]
    ):
        """发送成功通知"""
        try:
            event = NotificationEvent(
                event_type="NOTIFICATION",
                payload=NotificationPayload(
                    title="条件订单已触发",
                    message=f"订单 {order.id} 已成功触发: {trigger_result['trigger_reason']}",
                    level="success",
                    category="trading",
                    related_order_id=order.id,
                ),
            )

            await self.ws_manager.broadcast_to_user(order.user_id, event.dict())

        except Exception as e:
            logger.error(f"发送成功通知失败: {e}")

    async def _send_failure_notification(
        self, order: ConditionalOrder, error_message: str
    ):
        """发送失败通知"""
        try:
            event = NotificationEvent(
                event_type="NOTIFICATION",
                payload=NotificationPayload(
                    title="条件订单触发失败",
                    message=f"订单 {order.id} 触发失败: {error_message}",
                    level="error",
                    category="trading",
                    action_required=True,
                    related_order_id=order.id,
                ),
            )

            await self.ws_manager.broadcast_to_user(order.user_id, event.dict())

        except Exception as e:
            logger.error(f"发送失败通知失败: {e}")

    async def _send_error_notification(self, error_message: str):
        """发送系统错误通知"""
        try:
            event = RiskAlertEvent(
                event_type="RISK_ALERT",
                payload=RiskAlertPayload(
                    alert_type="exposure_warning",
                    severity="high",
                    message=f"价格监控服务错误: {error_message}",
                    recommended_actions=["检查网络连接", "重启价格监控服务"],
                ),
            )

            await self.ws_manager.broadcast_system(event.dict())

        except Exception as e:
            logger.error(f"发送错误通知失败: {e}")

    async def _send_system_status_notification(self, status: str, message: str):
        """发送系统状态通知"""
        try:
            event = NotificationEvent(
                event_type="NOTIFICATION",
                payload=NotificationPayload(
                    title="价格监控服务状态",
                    message=message,
                    level="info",
                    category="system",
                ),
            )

            await self.ws_manager.broadcast_system(event.dict())

        except Exception as e:
            logger.error(f"发送系统状态通知失败: {e}")


# 创建全局实例
price_watcher = PriceWatcher()


async def start_price_watcher():
    """
    启动价格监控服务的入口点
    """
    await price_watcher.start()


async def stop_price_watcher():
    """
    停止价格监控服务的入口点
    """
    await price_watcher.stop()
