"""统一交易所服务层

提供统一的交易所接口，支持多个交易所的交易操作。
使用CCXT库作为底层实现。
"""

import asyncio
import uuid
from datetime import datetime, timezone
from decimal import Decimal
from typing import Any, Dict, List, Optional, Tuple

import ccxt
import structlog

from ..core.config import settings
from ..core.schemas import TradePlan, TradeResult
from ..core.security import decrypt_api_credentials

# 配置结构化日志
logger = structlog.get_logger()


class ExchangeService:
    """
    统一交易所服务类

    提供统一的交易所接口，支持多个交易所的交易操作。
    """

    def __init__(self):
        self._exchanges: Dict[str, ccxt.Exchange] = {}
        self._supported_exchanges = {
            "binance": ccxt.binance,
            "okx": ccxt.okx,
            "bybit": ccxt.bybit,
            "huobi": ccxt.huobi,
            "kucoin": ccxt.kucoin,
        }

    def get_exchange(
        self,
        exchange_name: str,
        api_key: str,
        api_secret: str,
        sandbox: bool = False,
    ) -> ccxt.Exchange:
        """
        获取交易所实例

        Args:
            exchange_name: 交易所名称
            api_key: API密钥
            api_secret: API密钥
            sandbox: 是否使用沙盒环境

        Returns:
            ccxt.Exchange: 交易所实例
        """
        if exchange_name not in self._supported_exchanges:
            raise ValueError(f"不支持的交易所: {exchange_name}")

        # 创建交易所实例的唯一标识
        exchange_key = f"{exchange_name}_{api_key[:8]}_{sandbox}"

        if exchange_key not in self._exchanges:
            exchange_class = self._supported_exchanges[exchange_name]

            config = {
                "apiKey": api_key,
                "secret": api_secret,
                "sandbox": sandbox,
                "enableRateLimit": True,
                "options": {"defaultType": "spot"},  # 默认现货交易
            }

            # 特定交易所的配置调整
            if exchange_name == "binance":
                config["options"]["defaultType"] = "spot"
            elif exchange_name == "okx":
                config["password"] = ""  # OKX需要passphrase，这里简化处理

            self._exchanges[exchange_key] = exchange_class(config)

            logger.info(
                "创建交易所实例",
                exchange=exchange_name,
                sandbox=sandbox,
                key_prefix=api_key[:8],
            )

        return self._exchanges[exchange_key]

    async def get_ticker(self, symbol: str) -> Dict[str, Any]:
        """
        获取ticker数据

        Args:
            symbol: 交易对符号 (e.g., 'BTC/USDT')

        Returns:
            Dict[str, Any]: ticker数据
        """
        from ..core.config import settings

        # 检查是否为仿真模式
        if settings.simulation_mode:
            logger.info("Using simulation mode for ticker data", symbol=symbol)
            return self._get_simulated_ticker(symbol)

        try:
            # 标准化交易对符号
            normalized_symbol = self._normalize_symbol(symbol)

            # 使用默认交易所（Binance）获取ticker数据
            exchange = ccxt.binance({"enableRateLimit": True})

            # 获取ticker数据
            ticker = await asyncio.get_event_loop().run_in_executor(
                None, exchange.fetch_ticker, normalized_symbol
            )

            logger.info(
                "获取ticker数据成功",
                symbol=normalized_symbol,
                price=ticker.get("last"),
                exchange="binance",
            )

            return ticker

        except Exception as e:
            logger.error("获取ticker数据失败", symbol=symbol, error=str(e))
            raise ValueError(f"获取 {symbol} ticker数据失败: {str(e)}")

    def _get_simulated_ticker(self, symbol: str) -> Dict[str, Any]:
        """
        获取模拟ticker数据

        Args:
            symbol: 交易对符号

        Returns:
            Dict[str, Any]: 模拟ticker数据
        """
        # 模拟价格数据
        simulated_prices = {
            "BTC/USDT": 68000.0,
            "ETH/USDT": 3500.0,
            "SOL/USDT": 150.0,
            "ADA/USDT": 0.45,
            "DOT/USDT": 7.5,
        }

        base_price = simulated_prices.get(symbol, 100.0)

        # 添加一些随机波动
        import random

        price_variation = random.uniform(-0.02, 0.02)  # ±2%
        current_price = base_price * (1 + price_variation)

        # 构造ticker数据结构
        ticker = {
            "symbol": symbol,
            "last": current_price,
            "close": current_price,
            "bid": current_price * 0.9995,  # 0.05% spread
            "ask": current_price * 1.0005,
            "high": current_price * 1.05,
            "low": current_price * 0.95,
            "volume": 1000000.0,  # 模拟成交量
            "quoteVolume": current_price * 1000000.0,
            "percentage": price_variation * 100,  # 24h变化百分比
            "timestamp": None,
            "datetime": None,
            "info": {},
        }

        return ticker

    async def get_orderbook(self, symbol: str, limit: int = 20) -> Dict[str, Any]:
        """
        获取订单簿数据

        Args:
            symbol: 交易对符号 (e.g., 'BTC/USDT')
            limit: 订单簿深度限制

        Returns:
            Dict[str, Any]: 订单簿数据
        """
        from ..core.config import settings

        # 检查是否为仿真模式
        if settings.simulation_mode:
            logger.info("Using simulation mode for orderbook data", symbol=symbol)
            return self._get_simulated_orderbook(symbol, limit)

        try:
            # 标准化交易对符号
            normalized_symbol = self._normalize_symbol(symbol)

            # 使用默认交易所（Binance）获取订单簿数据
            exchange = ccxt.binance({"enableRateLimit": True})

            # 获取订单簿数据
            orderbook = await asyncio.get_event_loop().run_in_executor(
                None, exchange.fetch_order_book, normalized_symbol, limit
            )

            logger.info(
                "获取订单簿数据成功",
                symbol=normalized_symbol,
                bids_count=len(orderbook.get("bids", [])),
                asks_count=len(orderbook.get("asks", [])),
                exchange="binance",
            )

            return orderbook

        except Exception as e:
            logger.error("获取订单簿数据失败", symbol=symbol, error=str(e))
            raise ValueError(f"获取 {symbol} 订单簿数据失败: {str(e)}")

    def _get_simulated_orderbook(self, symbol: str, limit: int = 20) -> Dict[str, Any]:
        """
        获取模拟订单簿数据

        Args:
            symbol: 交易对符号
            limit: 订单簿深度限制

        Returns:
            Dict[str, Any]: 模拟订单簿数据
        """
        # 获取当前价格
        ticker = self._get_simulated_ticker(symbol)
        current_price = ticker["last"]

        # 生成模拟订单簿
        bids = []
        asks = []

        # 生成买单（价格递减）
        for i in range(limit):
            price = current_price * (1 - (i + 1) * 0.001)  # 每层0.1%价差
            quantity = 10.0 + i * 2.0  # 递增数量
            bids.append([price, quantity])

        # 生成卖单（价格递增）
        for i in range(limit):
            price = current_price * (1 + (i + 1) * 0.001)  # 每层0.1%价差
            quantity = 10.0 + i * 2.0  # 递增数量
            asks.append([price, quantity])

        return {
            "symbol": symbol,
            "bids": bids,
            "asks": asks,
            "timestamp": None,
            "datetime": None,
            "nonce": None,
        }

    async def get_market_price(self, exchange: ccxt.Exchange, symbol: str) -> float:
        """
        获取市场价格

        Args:
            exchange: 交易所实例
            symbol: 交易对符号 (e.g., 'BTC/USDT')

        Returns:
            float: 当前市场价格
        """
        try:
            # 标准化交易对符号
            normalized_symbol = self._normalize_symbol(symbol)

            # 获取ticker数据
            ticker = await asyncio.get_event_loop().run_in_executor(
                None, exchange.fetch_ticker, normalized_symbol
            )

            price = float(ticker["last"] or ticker["close"])

            logger.info(
                "获取市场价格成功",
                symbol=normalized_symbol,
                price=price,
                exchange=exchange.id,
            )

            return price

        except Exception as e:
            logger.error("获取市场价格失败", symbol=symbol, exchange=exchange.id, error=str(e))
            raise ValueError(f"获取 {symbol} 价格失败: {str(e)}")

    async def execute_trade(
        self, exchange: ccxt.Exchange, trade_plan: TradePlan
    ) -> TradeResult:
        """
        执行交易计划

        Args:
            exchange: 交易所实例
            trade_plan: 交易计划

        Returns:
            TradeResult: 交易结果
        """
        # 检查是否为仿真模式
        if settings.trading.simulation_mode:
            logger.info(
                "Using simulation mode for trade execution",
                symbol=trade_plan.symbol,
                side=trade_plan.side,
            )
            return await self._execute_simulated_trade(trade_plan)

        try:
            # 标准化交易对符号
            normalized_symbol = self._normalize_symbol(trade_plan.symbol)

            # 预检查：验证交易所连接
            await self._validate_exchange_connection(exchange)

            # 获取交易对信息以验证最小交易量等限制
            market = await asyncio.get_event_loop().run_in_executor(
                None, exchange.load_markets
            )

            if normalized_symbol not in market:
                raise ValueError(f"交易对 {normalized_symbol} 在 {exchange.id} 上不存在")

            market_info = market[normalized_symbol]

            # 预检查：验证账户余额
            await self._validate_account_balance(exchange, trade_plan, market_info)

            # 调整交易数量以符合交易所要求
            adjusted_quantity = self._adjust_quantity(trade_plan.quantity, market_info)

            # 执行订单（带重试机制）
            order = await self._execute_order_with_retry(
                exchange, normalized_symbol, trade_plan, adjusted_quantity
            )

            # 验证订单执行结果
            validated_order = await self._validate_order_execution(exchange, order)

            # 构造成功结果
            result = TradeResult(
                status="success",
                exchange_order_id=validated_order["id"],
                client_order_id=str(trade_plan.plan_id),
                symbol=normalized_symbol,
                side=trade_plan.side,
                quantity=float(validated_order["amount"]),
                price=float(
                    validated_order["price"] or validated_order.get("average", 0)
                ),
                timestamp=datetime.now(timezone.utc),
                raw_response=validated_order,
                fees=self._extract_fees(validated_order),
                slippage=self._calculate_slippage(trade_plan, validated_order),
            )

            logger.info(
                "交易执行成功",
                order_id=validated_order["id"],
                symbol=normalized_symbol,
                side=trade_plan.side,
                quantity=adjusted_quantity,
                price=result.price,
                exchange=exchange.id,
                fees=result.fees,
                slippage=result.slippage,
            )

            return result

        except ccxt.NetworkError as e:
            logger.error("网络错误", error=str(e), exchange=exchange.id)
            return TradeResult(
                status="failure",
                error_message=f"网络错误: {str(e)}",
                client_order_id=str(trade_plan.plan_id),
                symbol=trade_plan.symbol,
                side=trade_plan.side,
                quantity=float(trade_plan.quantity),
                timestamp=datetime.now(timezone.utc),
            )
        except ccxt.ExchangeError as e:
            logger.error("交易所错误", error=str(e), exchange=exchange.id)
            return TradeResult(
                status="failure",
                error_message=f"交易所错误: {str(e)}",
                client_order_id=str(trade_plan.plan_id),
                symbol=trade_plan.symbol,
                side=trade_plan.side,
                quantity=float(trade_plan.quantity),
                timestamp=datetime.now(timezone.utc),
            )
        except ccxt.InsufficientFunds as e:
            logger.error("余额不足", error=str(e), exchange=exchange.id)
            return TradeResult(
                status="failure",
                error_message=f"余额不足: {str(e)}",
                client_order_id=str(trade_plan.plan_id),
                symbol=trade_plan.symbol,
                side=trade_plan.side,
                quantity=float(trade_plan.quantity),
                timestamp=datetime.now(timezone.utc),
            )
        except Exception as e:
            logger.error(
                "交易执行失败",
                symbol=trade_plan.symbol,
                side=trade_plan.side,
                quantity=trade_plan.quantity,
                exchange=exchange.id,
                error=str(e),
            )

            return TradeResult(
                status="failure",
                error_message=str(e),
                client_order_id=str(trade_plan.plan_id),
                symbol=trade_plan.symbol,
                side=trade_plan.side,
                quantity=float(trade_plan.quantity),
                timestamp=datetime.now(timezone.utc),
            )

    async def _execute_simulated_trade(self, trade_plan: TradePlan) -> TradeResult:
        """执行模拟交易"""
        # 模拟交易延迟
        await asyncio.sleep(0.1)

        # 获取模拟价格
        ticker = self._get_simulated_ticker(trade_plan.symbol)
        execution_price = ticker["last"]

        # 模拟滑点
        slippage = 0.001 if trade_plan.order_type.lower() == "market" else 0.0
        if trade_plan.side.lower() == "buy":
            execution_price *= 1 + slippage
        else:
            execution_price *= 1 - slippage

        # 生成模拟订单ID
        order_id = f"sim_{uuid.uuid4().hex[:12]}"

        return TradeResult(
            status="success",
            exchange_order_id=order_id,
            client_order_id=str(trade_plan.plan_id),
            symbol=trade_plan.symbol,
            side=trade_plan.side,
            quantity=float(trade_plan.quantity),
            price=execution_price,
            timestamp=datetime.now(timezone.utc),
            raw_response={"simulated": True},
            fees={"trading": execution_price * float(trade_plan.quantity) * 0.001},
            slippage=slippage,
        )

    async def _validate_exchange_connection(self, exchange: ccxt.Exchange):
        """验证交易所连接"""
        try:
            # 测试连接
            await asyncio.get_event_loop().run_in_executor(None, exchange.load_markets)
        except Exception as e:
            raise ccxt.NetworkError(f"交易所连接失败: {str(e)}")

    async def _validate_account_balance(
        self, exchange: ccxt.Exchange, trade_plan: TradePlan, market_info: Dict
    ):
        """验证账户余额"""
        try:
            balance = await asyncio.get_event_loop().run_in_executor(
                None, exchange.fetch_balance
            )

            # 确定需要检查的货币
            if trade_plan.side.lower() == "buy":
                # 买入需要检查报价货币（如USDT）
                quote_currency = market_info["quote"]
                required_amount = float(trade_plan.quantity) * (trade_plan.price or 0)
                available = balance.get(quote_currency, {}).get("free", 0)

                if available < required_amount:
                    raise ccxt.InsufficientFunds(
                        f"余额不足: 需要 {required_amount} {quote_currency}, 可用 {available}"
                    )
            else:
                # 卖出需要检查基础货币（如BTC）
                base_currency = market_info["base"]
                required_amount = float(trade_plan.quantity)
                available = balance.get(base_currency, {}).get("free", 0)

                if available < required_amount:
                    raise ccxt.InsufficientFunds(
                        f"余额不足: 需要 {required_amount} {base_currency}, 可用 {available}"
                    )

        except ccxt.InsufficientFunds:
            raise
        except Exception as e:
            logger.warning("余额检查失败", error=str(e))
            # 余额检查失败不阻止交易，让交易所自己处理

    async def _execute_order_with_retry(
        self,
        exchange: ccxt.Exchange,
        symbol: str,
        trade_plan: TradePlan,
        quantity: float,
    ) -> Dict[str, Any]:
        """带重试机制的订单执行"""
        max_retries = 3
        retry_delay = 1.0

        for attempt in range(max_retries):
            try:
                if trade_plan.order_type.lower() == "market":
                    order = await asyncio.get_event_loop().run_in_executor(
                        None,
                        exchange.create_market_order,
                        symbol,
                        trade_plan.side,
                        quantity,
                    )
                elif trade_plan.order_type.lower() == "limit":
                    if not trade_plan.price:
                        raise ValueError("限价订单必须指定价格")

                    order = await asyncio.get_event_loop().run_in_executor(
                        None,
                        exchange.create_limit_order,
                        symbol,
                        trade_plan.side,
                        quantity,
                        trade_plan.price,
                    )
                else:
                    raise ValueError(f"不支持的订单类型: {trade_plan.order_type}")

                return order

            except ccxt.NetworkError as e:
                if attempt < max_retries - 1:
                    logger.warning(
                        f"网络错误，重试中 (尝试 {attempt + 1}/{max_retries})",
                        error=str(e),
                    )
                    await asyncio.sleep(retry_delay * (attempt + 1))
                    continue
                raise
            except (ccxt.ExchangeError, ccxt.InsufficientFunds):
                # 这些错误不重试
                raise
            except Exception as e:
                if attempt < max_retries - 1:
                    logger.warning(
                        f"订单执行失败，重试中 (尝试 {attempt + 1}/{max_retries})",
                        error=str(e),
                    )
                    await asyncio.sleep(retry_delay * (attempt + 1))
                    continue
                raise

    async def _validate_order_execution(
        self, exchange: ccxt.Exchange, order: Dict[str, Any]
    ) -> Dict[str, Any]:
        """验证订单执行结果"""
        try:
            # 获取最新的订单状态
            order_id = order["id"]
            updated_order = await asyncio.get_event_loop().run_in_executor(
                None, exchange.fetch_order, order_id
            )

            # 检查订单状态
            if updated_order["status"] in ["closed", "filled"]:
                return updated_order
            elif updated_order["status"] in ["canceled", "rejected"]:
                raise ccxt.ExchangeError(f"订单被拒绝或取消: {updated_order.get('info', {})}")
            else:
                # 订单仍在处理中，返回当前状态
                logger.info(f"订单状态: {updated_order['status']}", order_id=order_id)
                return updated_order

        except Exception as e:
            logger.warning("无法验证订单状态", order_id=order.get("id"), error=str(e))
            # 返回原始订单信息
            return order

    def _extract_fees(self, order: Dict[str, Any]) -> Dict[str, float]:
        """提取手续费信息"""
        fees = order.get("fees", [])
        if not fees:
            fee_info = order.get("fee", {})
            if fee_info:
                return {
                    "currency": fee_info.get("currency", "USDT"),
                    "cost": float(fee_info.get("cost", 0)),
                }

        total_fees = {}
        for fee in fees:
            currency = fee.get("currency", "USDT")
            cost = float(fee.get("cost", 0))
            total_fees[currency] = total_fees.get(currency, 0) + cost

        return total_fees

    def _calculate_slippage(
        self, trade_plan: TradePlan, order: Dict[str, Any]
    ) -> float:
        """计算滑点"""
        if not trade_plan.price or trade_plan.order_type.lower() != "market":
            return 0.0

        executed_price = float(order.get("average") or order.get("price", 0))
        expected_price = float(trade_plan.price)

        if expected_price == 0:
            return 0.0

        slippage = abs(executed_price - expected_price) / expected_price
        return slippage

    def _normalize_symbol(self, symbol: str) -> str:
        """
        标准化交易对符号

        Args:
            symbol: 原始交易对符号

        Returns:
            str: 标准化后的交易对符号
        """
        # 移除空格并转换为大写
        symbol = symbol.replace(" ", "").upper()

        # 如果没有分隔符，尝试添加常见的分隔符
        if "/" not in symbol:
            # 常见的基础货币
            base_currencies = ["USDT", "USDC", "BTC", "ETH", "BNB"]

            for base in base_currencies:
                if symbol.endswith(base):
                    quote = symbol[: -len(base)]
                    symbol = f"{quote}/{base}"
                    break
            else:
                # 默认添加USDT
                symbol = f"{symbol}/USDT"

        return symbol

    def _adjust_quantity(self, quantity: float, market_info: Dict[str, Any]) -> float:
        """
        调整交易数量以符合交易所要求

        Args:
            quantity: 原始数量
            market_info: 市场信息

        Returns:
            float: 调整后的数量
        """
        limits = market_info.get("limits", {})
        amount_limits = limits.get("amount", {})

        min_amount = amount_limits.get("min", 0)
        max_amount = amount_limits.get("max", float("inf"))

        # 确保数量在允许范围内
        adjusted_quantity = max(min_amount, min(quantity, max_amount))

        # 根据精度要求调整
        precision = market_info.get("precision", {}).get("amount", 8)
        adjusted_quantity = round(adjusted_quantity, precision)

        return adjusted_quantity

    async def get_account_balance(
        self, exchange: ccxt.Exchange, currency: str = None
    ) -> Dict[str, float]:
        """
        获取账户余额

        Args:
            exchange: 交易所实例
            currency: 指定货币，如果为None则返回所有余额

        Returns:
            Dict[str, float]: 余额信息
        """
        try:
            balance = await asyncio.get_event_loop().run_in_executor(
                None, exchange.fetch_balance
            )

            if currency:
                return {
                    "free": float(balance.get(currency, {}).get("free", 0)),
                    "used": float(balance.get(currency, {}).get("used", 0)),
                    "total": float(balance.get(currency, {}).get("total", 0)),
                }
            else:
                result = {}
                for curr, info in balance.items():
                    if isinstance(info, dict) and info.get("total", 0) > 0:
                        result[curr] = {
                            "free": float(info.get("free", 0)),
                            "used": float(info.get("used", 0)),
                            "total": float(info.get("total", 0)),
                        }
                return result

        except Exception as e:
            logger.error(
                "获取账户余额失败",
                currency=currency,
                exchange=exchange.id,
                error=str(e),
            )
            raise ValueError(f"获取余额失败: {str(e)}")


# 全局交易所服务实例
exchange_service = ExchangeService()


# 便捷函数
async def get_exchange_instance(
    user_id: int, exchange_name: str = None
) -> ccxt.Exchange:
    """
    获取用户的交易所实例

    Args:
        user_id: 用户ID
        exchange_name: 交易所名称，如果为None则使用默认交易所

    Returns:
        ccxt.Exchange: 交易所实例
    """
    from sqlalchemy import select

    from ..core.database import get_db
    from ..core.models import ExchangeConfig

    # 这里需要数据库会话，实际使用时需要传入
    # 为了简化，这里抛出NotImplementedError
    raise NotImplementedError("需要在具体使用场景中实现数据库查询逻辑")
