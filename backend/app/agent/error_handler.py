"""统一的错误处理和超时机制模块

本模块提供了统一的异步错误处理装饰器和超时控制机制，
用于包装 Agent 节点函数，确保错误的一致性处理和超时控制。
增强版本包含智能错误恢复和自我修正能力。
"""

import asyncio
import functools
from datetime import datetime, timedelta, timezone
from enum import Enum
from typing import Any, Callable, Dict, List, Optional, TypeVar, Union

import structlog

from ..core.config import settings
from ..core.schemas import AgentState

# 配置结构化日志
logger = structlog.get_logger()

# 类型变量
F = TypeVar("F", bound=Callable[..., Any])


class RecoveryStrategy(Enum):
    """错误恢复策略枚举"""

    RETRY = "retry"  # 简单重试
    FALLBACK = "fallback"  # 降级处理
    SKIP = "skip"  # 跳过当前步骤
    RESTART = "restart"  # 重新开始
    USER_INTERVENTION = "user_intervention"  # 需要用户干预
    ADAPTIVE_RETRY = "adaptive_retry"  # 自适应重试


class ErrorSeverity(Enum):
    """错误严重程度"""

    LOW = "low"  # 轻微错误，可以忽略或简单重试
    MEDIUM = "medium"  # 中等错误，需要恢复策略
    HIGH = "high"  # 严重错误，需要用户干预
    CRITICAL = "critical"  # 致命错误，必须停止执行


class AgentError(Exception):
    """Agent 执行过程中的基础异常类"""

    def __init__(
        self,
        message: str,
        error_code: str = "AGENT_ERROR",
        node_name: Optional[str] = None,
        recoverable: bool = True,
        severity: ErrorSeverity = ErrorSeverity.MEDIUM,
        recovery_strategy: RecoveryStrategy = RecoveryStrategy.RETRY,
        context: Optional[Dict[str, Any]] = None,
    ):
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.node_name = node_name
        self.recoverable = recoverable
        self.severity = severity
        self.recovery_strategy = recovery_strategy
        self.context = context or {}
        self.timestamp = datetime.now(timezone.utc)
        self.retry_count = 0


class AgentTimeoutError(AgentError):
    """Agent 节点执行超时异常"""

    def __init__(self, node_name: str, timeout_seconds: float):
        super().__init__(
            f"Node '{node_name}' execution timed out after {timeout_seconds} seconds",
            error_code="TIMEOUT_ERROR",
            node_name=node_name,
            recoverable=True,
        )
        self.timeout_seconds = timeout_seconds


class AgentNetworkError(AgentError):
    """Agent 网络相关异常"""

    def __init__(self, message: str, node_name: Optional[str] = None):
        super().__init__(
            message,
            error_code="NETWORK_ERROR",
            node_name=node_name,
            recoverable=True,
        )


class AgentValidationError(AgentError):
    """Agent 数据验证异常"""

    def __init__(self, message: str, node_name: Optional[str] = None):
        super().__init__(
            message,
            error_code="VALIDATION_ERROR",
            node_name=node_name,
            recoverable=False,
        )


class AgentConfigError(AgentError):
    """Agent 配置相关异常"""

    def __init__(self, message: str, node_name: Optional[str] = None):
        super().__init__(
            message,
            error_code="CONFIG_ERROR",
            node_name=node_name,
            recoverable=False,
            severity=ErrorSeverity.HIGH,
            recovery_strategy=RecoveryStrategy.USER_INTERVENTION,
        )


class SmartRecoveryManager:
    """智能错误恢复管理器"""

    def __init__(self):
        self.error_patterns = {}
        self.recovery_history = []
        self.success_patterns = {}

    def analyze_error(self, error: AgentError, state: AgentState) -> Dict[str, Any]:
        """分析错误并确定最佳恢复策略"""
        error_signature = self._create_error_signature(error, state)

        # 检查历史恢复记录
        historical_strategy = self._get_historical_strategy(error_signature)

        # 分析错误上下文
        context_analysis = self._analyze_error_context(error, state)

        # 确定恢复策略
        recovery_plan = self._determine_recovery_strategy(
            error, state, historical_strategy, context_analysis
        )

        return {
            "error_signature": error_signature,
            "recovery_plan": recovery_plan,
            "context_analysis": context_analysis,
            "confidence": recovery_plan.get("confidence", 0.5),
        }

    def _create_error_signature(self, error: AgentError, state: AgentState) -> str:
        """创建错误签名用于模式识别"""
        components = [
            error.error_code,
            error.node_name or "unknown",
            str(error.severity.value),
            state.current_node or "unknown",
        ]
        return "|".join(components)

    def _get_historical_strategy(
        self, error_signature: str
    ) -> Optional[Dict[str, Any]]:
        """获取历史成功的恢复策略"""
        if error_signature in self.success_patterns:
            pattern = self.success_patterns[error_signature]
            return {
                "strategy": pattern["strategy"],
                "success_rate": pattern["success_count"] / pattern["total_count"],
                "last_success": pattern["last_success"],
            }
        return None

    def _analyze_error_context(
        self, error: AgentError, state: AgentState
    ) -> Dict[str, Any]:
        """分析错误上下文"""
        analysis = {
            "error_frequency": self._calculate_error_frequency(error, state),
            "state_health": self._assess_state_health(state),
            "resource_availability": self._check_resource_availability(state),
            "user_context": self._analyze_user_context(state),
        }
        return analysis

    def _determine_recovery_strategy(
        self,
        error: AgentError,
        state: AgentState,
        historical_strategy: Optional[Dict[str, Any]],
        context_analysis: Dict[str, Any],
    ) -> Dict[str, Any]:
        """确定最佳恢复策略"""

        # 基于错误严重程度的基础策略
        base_strategy = self._get_base_strategy_by_severity(error.severity)

        # 如果有历史成功策略且成功率高，优先使用
        if historical_strategy and historical_strategy["success_rate"] > 0.7:
            return {
                "strategy": historical_strategy["strategy"],
                "confidence": historical_strategy["success_rate"],
                "reason": "historical_success",
                "parameters": self._get_strategy_parameters(
                    historical_strategy["strategy"], error, state
                ),
            }

        # 基于上下文分析调整策略
        adjusted_strategy = self._adjust_strategy_by_context(
            base_strategy, context_analysis, error, state
        )

        return adjusted_strategy

    def _get_base_strategy_by_severity(
        self, severity: ErrorSeverity
    ) -> RecoveryStrategy:
        """根据错误严重程度获取基础策略"""
        strategy_map = {
            ErrorSeverity.LOW: RecoveryStrategy.RETRY,
            ErrorSeverity.MEDIUM: RecoveryStrategy.ADAPTIVE_RETRY,
            ErrorSeverity.HIGH: RecoveryStrategy.FALLBACK,
            ErrorSeverity.CRITICAL: RecoveryStrategy.USER_INTERVENTION,
        }
        return strategy_map.get(severity, RecoveryStrategy.RETRY)

    def _adjust_strategy_by_context(
        self,
        base_strategy: RecoveryStrategy,
        context: Dict[str, Any],
        error: AgentError,
        state: AgentState,
    ) -> Dict[str, Any]:
        """根据上下文调整恢复策略"""

        # 如果错误频率过高，升级策略
        if context["error_frequency"] > 3:
            if base_strategy == RecoveryStrategy.RETRY:
                base_strategy = RecoveryStrategy.FALLBACK
            elif base_strategy == RecoveryStrategy.ADAPTIVE_RETRY:
                base_strategy = RecoveryStrategy.USER_INTERVENTION

        # 如果状态健康度低，使用更保守的策略
        if context["state_health"] < 0.5:
            if base_strategy in [
                RecoveryStrategy.RETRY,
                RecoveryStrategy.ADAPTIVE_RETRY,
            ]:
                base_strategy = RecoveryStrategy.RESTART

        # 如果资源不可用，等待或降级
        if not context["resource_availability"]:
            base_strategy = RecoveryStrategy.FALLBACK

        return {
            "strategy": base_strategy,
            "confidence": self._calculate_confidence(base_strategy, context),
            "reason": "context_adjusted",
            "parameters": self._get_strategy_parameters(base_strategy, error, state),
        }

    def _calculate_error_frequency(self, error: AgentError, state: AgentState) -> int:
        """计算错误频率"""
        if not state.errors:
            return 0

        recent_errors = [
            e
            for e in state.errors
            if e.get("error_code") == error.error_code
            and e.get("node_name") == error.node_name
        ]
        return len(recent_errors)

    def _assess_state_health(self, state: AgentState) -> float:
        """评估状态健康度"""
        health_score = 1.0

        # 错误数量影响
        if state.errors:
            error_penalty = min(len(state.errors) * 0.1, 0.5)
            health_score -= error_penalty

        # 执行时间影响
        if hasattr(state, "execution_time") and state.execution_time:
            if state.execution_time > 300:  # 5分钟
                health_score -= 0.2

        # 重试次数影响
        if hasattr(state, "retry_count") and state.retry_count:
            retry_penalty = min(state.retry_count * 0.15, 0.3)
            health_score -= retry_penalty

        return max(health_score, 0.0)

    def _check_resource_availability(self, state: AgentState) -> bool:
        """检查资源可用性"""
        # 这里可以检查数据库连接、API限制、网络状态等
        # 简化实现，实际应该检查具体资源
        return True

    def _analyze_user_context(self, state: AgentState) -> Dict[str, Any]:
        """分析用户上下文"""
        return {
            "user_id": state.user_id,
            "task_priority": getattr(state, "priority", "normal"),
            "user_preferences": getattr(state, "user_preferences", {}),
        }

    def _calculate_confidence(
        self, strategy: RecoveryStrategy, context: Dict[str, Any]
    ) -> float:
        """计算策略置信度"""
        base_confidence = {
            RecoveryStrategy.RETRY: 0.6,
            RecoveryStrategy.ADAPTIVE_RETRY: 0.7,
            RecoveryStrategy.FALLBACK: 0.8,
            RecoveryStrategy.SKIP: 0.5,
            RecoveryStrategy.RESTART: 0.6,
            RecoveryStrategy.USER_INTERVENTION: 0.9,
        }

        confidence = base_confidence.get(strategy, 0.5)

        # 根据状态健康度调整
        confidence *= context["state_health"]

        return min(confidence, 1.0)

    def _get_strategy_parameters(
        self, strategy: RecoveryStrategy, error: AgentError, state: AgentState
    ) -> Dict[str, Any]:
        """获取策略参数"""
        parameters = {}

        if strategy == RecoveryStrategy.RETRY:
            parameters = {
                "max_retries": 3,
                "retry_delay": 1.0,
                "exponential_backoff": True,
            }
        elif strategy == RecoveryStrategy.ADAPTIVE_RETRY:
            parameters = {
                "max_retries": 5,
                "initial_delay": 1.0,
                "max_delay": 30.0,
                "backoff_factor": 2.0,
                "jitter": True,
            }
        elif strategy == RecoveryStrategy.FALLBACK:
            parameters = {
                "fallback_node": self._determine_fallback_node(error.node_name),
                "preserve_state": True,
            }
        elif strategy == RecoveryStrategy.RESTART:
            parameters = {
                "restart_from": "Preprocess",
                "preserve_context": True,
                "reset_errors": True,
            }
        elif strategy == RecoveryStrategy.USER_INTERVENTION:
            parameters = {
                "notification_type": "error_intervention_required",
                "timeout_seconds": 300,
                "auto_fallback": True,
            }

        return parameters

    def _determine_fallback_node(self, current_node: Optional[str]) -> str:
        """确定降级节点"""
        fallback_map = {
            "Parse": "Preprocess",
            "Context": "Parse",
            "Plan": "Context",
            "Risk": "Plan",
            "Execute": "Risk",
            "AnalyzeError": "Plan",
        }
        return fallback_map.get(current_node, "Preprocess")

    def record_recovery_attempt(
        self,
        error_signature: str,
        strategy: RecoveryStrategy,
        success: bool,
        execution_time: float,
    ):
        """记录恢复尝试结果"""
        record = {
            "error_signature": error_signature,
            "strategy": strategy,
            "success": success,
            "execution_time": execution_time,
            "timestamp": datetime.now(timezone.utc),
        }

        self.recovery_history.append(record)

        # 更新成功模式
        if success:
            if error_signature not in self.success_patterns:
                self.success_patterns[error_signature] = {
                    "strategy": strategy,
                    "success_count": 0,
                    "total_count": 0,
                    "last_success": None,
                }

            pattern = self.success_patterns[error_signature]
            pattern["success_count"] += 1
            pattern["total_count"] += 1
            pattern["last_success"] = datetime.now(timezone.utc)
        else:
            if error_signature in self.success_patterns:
                self.success_patterns[error_signature]["total_count"] += 1


# 全局恢复管理器实例
recovery_manager = SmartRecoveryManager()


def with_smart_error_handling(
    node_name: str,
    timeout_seconds: Optional[float] = None,
    enable_smart_recovery: bool = True,
    fallback_strategy: Optional[RecoveryStrategy] = None,
) -> Callable[[F], F]:
    """智能错误处理和恢复装饰器

    Args:
        node_name: 节点名称，用于日志和错误追踪
        timeout_seconds: 超时时间（秒），默认使用配置中的值
        enable_smart_recovery: 是否启用智能恢复
        fallback_strategy: 备用恢复策略

    Returns:
        装饰后的函数
    """

    def decorator(func: F) -> F:
        @functools.wraps(func)
        async def wrapper(state: AgentState, *args, **kwargs) -> AgentState:
            # 使用默认超时时间
            actual_timeout = timeout_seconds or settings.llm.request_timeout
            start_time = datetime.now(timezone.utc)

            # 记录节点开始执行
            logger.info(
                "Smart node execution started",
                node_name=node_name,
                task_id=str(state.task_id),
                timeout_seconds=actual_timeout,
                smart_recovery=enable_smart_recovery,
            )

            last_exception = None
            recovery_attempts = 0
            max_recovery_attempts = 3

            while recovery_attempts <= max_recovery_attempts:
                try:
                    # 使用 asyncio.wait_for 实现超时控制
                    result = await asyncio.wait_for(
                        func(state, *args, **kwargs), timeout=actual_timeout
                    )

                    # 记录成功执行
                    execution_time = (
                        datetime.now(timezone.utc) - start_time
                    ).total_seconds()
                    logger.info(
                        "Smart node execution completed successfully",
                        node_name=node_name,
                        task_id=str(state.task_id),
                        recovery_attempts=recovery_attempts,
                        execution_time=execution_time,
                    )

                    # 如果之前有错误但现在成功了，记录恢复成功
                    if last_exception and enable_smart_recovery:
                        error_signature = recovery_manager._create_error_signature(
                            last_exception, state
                        )
                        recovery_manager.record_recovery_attempt(
                            error_signature,
                            last_exception.recovery_strategy,
                            True,
                            execution_time,
                        )

                    return result

                except asyncio.TimeoutError:
                    last_exception = AgentTimeoutError(node_name, actual_timeout)

                    logger.error(
                        "Smart node execution timed out",
                        node_name=node_name,
                        task_id=str(state.task_id),
                        timeout_seconds=actual_timeout,
                        recovery_attempt=recovery_attempts + 1,
                    )

                except Exception as e:
                    # 将普通异常转换为 AgentError
                    if isinstance(e, AgentError):
                        last_exception = e
                    else:
                        # 根据异常类型进行分类并设置恢复策略
                        error_message = str(e)
                        if any(
                            keyword in error_message.lower()
                            for keyword in [
                                "network",
                                "connection",
                                "timeout",
                                "dns",
                            ]
                        ):
                            last_exception = AgentNetworkError(error_message, node_name)
                            last_exception.recovery_strategy = (
                                RecoveryStrategy.ADAPTIVE_RETRY
                            )
                            last_exception.severity = ErrorSeverity.MEDIUM
                        elif any(
                            keyword in error_message.lower()
                            for keyword in ["validation", "invalid", "format"]
                        ):
                            last_exception = AgentValidationError(
                                error_message, node_name
                            )
                            last_exception.recovery_strategy = RecoveryStrategy.FALLBACK
                            last_exception.severity = ErrorSeverity.HIGH
                        elif any(
                            keyword in error_message.lower()
                            for keyword in ["config", "setting", "key"]
                        ):
                            last_exception = AgentConfigError(error_message, node_name)
                            last_exception.recovery_strategy = (
                                RecoveryStrategy.USER_INTERVENTION
                            )
                            last_exception.severity = ErrorSeverity.CRITICAL
                        else:
                            last_exception = AgentError(
                                error_message,
                                error_code="UNKNOWN_ERROR",
                                node_name=node_name,
                                recovery_strategy=RecoveryStrategy.RETRY,
                                severity=ErrorSeverity.MEDIUM,
                            )

                # 智能恢复决策
                if enable_smart_recovery and last_exception:
                    recovery_analysis = recovery_manager.analyze_error(
                        last_exception, state
                    )
                    recovery_plan = recovery_analysis["recovery_plan"]

                    logger.info(
                        "Smart recovery analysis",
                        node_name=node_name,
                        task_id=str(state.task_id),
                        error_code=last_exception.error_code,
                        recovery_strategy=recovery_plan["strategy"].value,
                        confidence=recovery_plan["confidence"],
                        recovery_attempt=recovery_attempts + 1,
                    )

                    # 执行恢复策略
                    recovery_result = await _execute_recovery_strategy(
                        recovery_plan,
                        last_exception,
                        state,
                        func,
                        args,
                        kwargs,
                        actual_timeout,
                    )

                    if recovery_result["success"]:
                        return recovery_result["result"]
                    elif recovery_result["should_continue"]:
                        recovery_attempts += 1
                        continue
                    else:
                        break
                else:
                    # 传统错误处理
                    logger.error(
                        "Node execution failed",
                        node_name=node_name,
                        task_id=str(state.task_id),
                        error_code=last_exception.error_code,
                        error_message=last_exception.message,
                        recovery_attempt=recovery_attempts + 1,
                        recoverable=last_exception.recoverable,
                    )

                    # 如果错误不可恢复，停止尝试
                    if not last_exception.recoverable:
                        break

                    recovery_attempts += 1

            # 更新状态中的错误信息
            if last_exception:
                # 将错误信息添加到状态中
                error_info = {
                    "error_code": last_exception.error_code,
                    "error_message": last_exception.message,
                    "node_name": node_name,
                    "timestamp": last_exception.timestamp.isoformat(),
                    "recoverable": last_exception.recoverable,
                }

                # 更新状态
                state.errors = state.errors or []
                state.errors.append(error_info)
                state.current_node = node_name
                state.status = "error"

                # 抛出异常以便上层处理
                raise last_exception

            # 这里不应该到达，但为了类型安全
            return state

        return wrapper

    return decorator


def create_error_state(state: AgentState, error: AgentError) -> AgentState:
    """创建包含错误信息的状态

    Args:
        state: 原始状态
        error: 错误对象

    Returns:
        包含错误信息的新状态
    """
    error_info = {
        "error_code": error.error_code,
        "error_message": error.message,
        "node_name": error.node_name,
        "timestamp": error.timestamp.isoformat(),
        "recoverable": error.recoverable,
    }

    # 创建新状态
    new_state = state.copy(deep=True)
    new_state.errors = new_state.errors or []
    new_state.errors.append(error_info)
    new_state.current_node = error.node_name
    new_state.status = "error"

    return new_state


async def handle_node_error(
    state: AgentState, error: Exception, node_name: str
) -> AgentState:
    """处理节点执行错误的通用函数

    Args:
        state: 当前状态
        error: 捕获的异常
        node_name: 节点名称

    Returns:
        更新后的状态
    """
    # 转换为 AgentError
    if isinstance(error, AgentError):
        agent_error = error
    else:
        agent_error = AgentError(
            str(error), error_code="UNKNOWN_ERROR", node_name=node_name
        )

    # 记录错误
    logger.error(
        "Node error handled",
        node_name=node_name,
        task_id=str(state.task_id),
        error_code=agent_error.error_code,
        error_message=agent_error.message,
    )

    # 返回包含错误信息的状态
    return create_error_state(state, agent_error)


async def _execute_recovery_strategy(
    recovery_plan: Dict[str, Any],
    error: AgentError,
    state: AgentState,
    func: Callable,
    args: tuple,
    kwargs: dict,
    timeout: float,
) -> Dict[str, Any]:
    """执行恢复策略"""
    strategy = recovery_plan["strategy"]
    parameters = recovery_plan.get("parameters", {})

    logger.info(
        "Executing recovery strategy",
        strategy=strategy.value,
        error_code=error.error_code,
        task_id=str(state.task_id),
    )

    try:
        if strategy == RecoveryStrategy.RETRY:
            return await _execute_retry_strategy(
                func, state, args, kwargs, timeout, parameters
            )

        elif strategy == RecoveryStrategy.ADAPTIVE_RETRY:
            return await _execute_adaptive_retry_strategy(
                func, state, args, kwargs, timeout, parameters
            )

        elif strategy == RecoveryStrategy.FALLBACK:
            return await _execute_fallback_strategy(state, parameters)

        elif strategy == RecoveryStrategy.SKIP:
            return await _execute_skip_strategy(state, parameters)

        elif strategy == RecoveryStrategy.RESTART:
            return await _execute_restart_strategy(state, parameters)

        elif strategy == RecoveryStrategy.USER_INTERVENTION:
            return await _execute_user_intervention_strategy(state, error, parameters)

        else:
            logger.warning(f"Unknown recovery strategy: {strategy}")
            return {"success": False, "should_continue": False}

    except Exception as e:
        logger.error(
            "Recovery strategy execution failed",
            strategy=strategy.value,
            error=str(e),
            task_id=str(state.task_id),
        )
        return {"success": False, "should_continue": False}


async def _execute_retry_strategy(
    func: Callable,
    state: AgentState,
    args: tuple,
    kwargs: dict,
    timeout: float,
    parameters: Dict[str, Any],
) -> Dict[str, Any]:
    """执行重试策略"""
    max_retries = parameters.get("max_retries", 3)
    retry_delay = parameters.get("retry_delay", 1.0)
    exponential_backoff = parameters.get("exponential_backoff", True)

    for retry in range(max_retries):
        try:
            await asyncio.sleep(retry_delay)
            result = await asyncio.wait_for(
                func(state, *args, **kwargs), timeout=timeout
            )
            return {
                "success": True,
                "result": result,
                "should_continue": False,
            }
        except Exception as e:
            if retry == max_retries - 1:
                break
            if exponential_backoff:
                retry_delay *= 2

    return {"success": False, "should_continue": True}


async def _execute_adaptive_retry_strategy(
    func: Callable,
    state: AgentState,
    args: tuple,
    kwargs: dict,
    timeout: float,
    parameters: Dict[str, Any],
) -> Dict[str, Any]:
    """执行自适应重试策略"""
    max_retries = parameters.get("max_retries", 5)
    initial_delay = parameters.get("initial_delay", 1.0)
    max_delay = parameters.get("max_delay", 30.0)
    backoff_factor = parameters.get("backoff_factor", 2.0)
    jitter = parameters.get("jitter", True)

    delay = initial_delay

    for retry in range(max_retries):
        try:
            # 添加抖动以避免雷群效应
            actual_delay = delay
            if jitter:
                import random

                actual_delay = delay * (0.5 + random.random() * 0.5)

            await asyncio.sleep(actual_delay)
            result = await asyncio.wait_for(
                func(state, *args, **kwargs), timeout=timeout
            )
            return {
                "success": True,
                "result": result,
                "should_continue": False,
            }
        except Exception as e:
            if retry == max_retries - 1:
                break
            delay = min(delay * backoff_factor, max_delay)

    return {"success": False, "should_continue": True}


async def _execute_fallback_strategy(
    state: AgentState, parameters: Dict[str, Any]
) -> Dict[str, Any]:
    """执行降级策略"""
    fallback_node = parameters.get("fallback_node", "Preprocess")
    preserve_state = parameters.get("preserve_state", True)

    # 创建降级状态
    if preserve_state:
        fallback_state = state.copy(deep=True)
    else:
        fallback_state = AgentState(
            task_id=state.task_id,
            user_id=state.user_id,
            text=state.text,
            current_node=fallback_node,
            status="fallback",
        )

    fallback_state.current_node = fallback_node
    fallback_state.status = "fallback"

    logger.info(
        "Fallback strategy executed",
        fallback_node=fallback_node,
        task_id=str(state.task_id),
    )

    return {
        "success": True,
        "result": fallback_state,
        "should_continue": False,
    }


async def _execute_skip_strategy(
    state: AgentState, parameters: Dict[str, Any]
) -> Dict[str, Any]:
    """执行跳过策略"""
    # 简单地返回当前状态，标记为已跳过
    skip_state = state.copy(deep=True)
    skip_state.status = "skipped"

    logger.info("Skip strategy executed", task_id=str(state.task_id))

    return {"success": True, "result": skip_state, "should_continue": False}


async def _execute_restart_strategy(
    state: AgentState, parameters: Dict[str, Any]
) -> Dict[str, Any]:
    """执行重启策略"""
    restart_from = parameters.get("restart_from", "Preprocess")
    preserve_context = parameters.get("preserve_context", True)
    reset_errors = parameters.get("reset_errors", True)

    # 创建重启状态
    restart_state = AgentState(
        task_id=state.task_id,
        user_id=state.user_id,
        text=state.text,
        current_node=restart_from,
        status="restarted",
    )

    if preserve_context:
        restart_state.context = state.context
        restart_state.parsed_intent = state.parsed_intent

    if not reset_errors:
        restart_state.errors = state.errors

    logger.info(
        "Restart strategy executed",
        restart_from=restart_from,
        task_id=str(state.task_id),
    )

    return {"success": True, "result": restart_state, "should_continue": False}


async def _execute_user_intervention_strategy(
    state: AgentState, error: AgentError, parameters: Dict[str, Any]
) -> Dict[str, Any]:
    """执行用户干预策略"""
    notification_type = parameters.get(
        "notification_type", "error_intervention_required"
    )
    timeout_seconds = parameters.get("timeout_seconds", 300)
    auto_fallback = parameters.get("auto_fallback", True)

    # 创建需要用户干预的状态
    intervention_state = state.copy(deep=True)
    intervention_state.status = "user_intervention_required"
    intervention_state.current_node = "UserConfirm"

    # 添加干预信息
    intervention_info = {
        "type": notification_type,
        "error": {
            "code": error.error_code,
            "message": error.message,
            "node": error.node_name,
        },
        "timeout_seconds": timeout_seconds,
        "auto_fallback": auto_fallback,
    }

    if not hasattr(intervention_state, "intervention_requests"):
        intervention_state.intervention_requests = []
    intervention_state.intervention_requests.append(intervention_info)

    logger.info(
        "User intervention strategy executed",
        notification_type=notification_type,
        task_id=str(state.task_id),
    )

    return {
        "success": True,
        "result": intervention_state,
        "should_continue": False,
    }


# 保持向后兼容的原始装饰器
def with_error_handling(
    node_name: str,
    timeout_seconds: Optional[float] = None,
    retry_count: int = 0,
    retry_delay: float = 1.0,
) -> Callable[[F], F]:
    """原始的错误处理装饰器（保持向后兼容）"""
    return with_smart_error_handling(
        node_name=node_name,
        timeout_seconds=timeout_seconds,
        enable_smart_recovery=False,
        fallback_strategy=RecoveryStrategy.RETRY,
    )
