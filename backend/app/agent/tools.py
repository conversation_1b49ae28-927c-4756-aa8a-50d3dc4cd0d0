"""
Agent工具函数模块 - 定义LangGraph智能体可调用的所有工具函数
"""
import uuid
from datetime import datetime
from decimal import Decimal
from typing import Any, Dict, List, Optional, Tuple

import ccxt.async_support as ccxt
import structlog
from sqlalchemy import and_, select
from sqlalchemy.ext.asyncio import AsyncSession

from ..core.config import settings
from ..core.models import ExchangeConfig
from ..core.models import Order as OrderModel
from ..core.schemas import Order as OrderSchema
from ..core.schemas import OrderStatus, TradePlan, TradeResult
from ..core.security import decrypt_api_credentials

# 配置结构化日志
logger = structlog.get_logger()


async def get_active_orders(user_id: uuid.UUID, db: AsyncSession) -> List[OrderSchema]:
    """
    获取用户的活跃订单

    Args:
        user_id: 用户ID
        db: 数据库会话

    Returns:
        List[OrderSchema]: 活跃订单列表
    """
    from ..core.models import Order as OrderModel

    # 查询数据库中的活跃订单
    query = select(OrderModel).where(
        and_(
            OrderModel.user_id == user_id,
            OrderModel.status == OrderStatus.ACTIVE.value,
        )
    )

    result = await db.execute(query)
    orders = result.scalars().all()

    # 转换为OrderSchema
    return [
        OrderSchema(
            id=order.id,
            user_id=order.user_id,
            symbol=order.symbol,
            side=order.side,
            quantity=order.quantity,
            entry_price=order.entry_price,
            pnl=order.pnl,
            status=order.status,
            created_at=order.created_at,
        )
        for order in orders
    ]


async def find_orders_by_criteria(
    user_id: uuid.UUID, criteria: str, db: AsyncSession
) -> List[OrderSchema]:
    """
    根据自然语言条件查找订单

    Args:
        user_id: 用户ID
        criteria: 自然语言条件
        db: 数据库会话

    Returns:
        List[OrderSchema]: 匹配的订单列表
    """
    logger.info("Finding orders by criteria", user_id=user_id, criteria=criteria)

    # 获取所有活跃订单作为起点
    query = select(OrderModel).where(OrderModel.user_id == user_id)
    result = await db.execute(query)
    orders = result.scalars().all()

    # 解析自然语言条件并应用筛选
    criteria_lower = criteria.lower()
    filtered_orders = []

    # 按交易对筛选
    if "btc" in criteria_lower:
        filtered_orders = [o for o in orders if "BTC" in o.symbol]
    elif "eth" in criteria_lower:
        filtered_orders = [o for o in orders if "ETH" in o.symbol]
    elif "sol" in criteria_lower:
        filtered_orders = [o for o in orders if "SOL" in o.symbol]
    else:
        filtered_orders = orders

    # 按状态筛选
    if "活跃" in criteria_lower or "active" in criteria_lower:
        filtered_orders = [
            o for o in filtered_orders if o.status == OrderStatus.ACTIVE.value
        ]
    elif "已关闭" in criteria_lower or "closed" in criteria_lower:
        filtered_orders = [
            o for o in filtered_orders if o.status == OrderStatus.CLOSED.value
        ]

    # 按盈亏筛选
    if (
        "盈利" in criteria_lower
        or "profitable" in criteria_lower
        or "profit" in criteria_lower
    ):
        filtered_orders = [o for o in filtered_orders if o.pnl and o.pnl > 0]
    elif (
        "亏损" in criteria_lower or "losing" in criteria_lower or "loss" in criteria_lower
    ):
        filtered_orders = [o for o in filtered_orders if o.pnl and o.pnl < 0]

    # 按时间筛选
    if (
        "最新" in criteria_lower
        or "latest" in criteria_lower
        or "recent" in criteria_lower
    ):
        filtered_orders.sort(key=lambda o: o.created_at, reverse=True)
        if filtered_orders:
            filtered_orders = [filtered_orders[0]]

    # 如果没有匹配的订单，尝试采用最宽松的解释
    if not filtered_orders and orders:
        logger.info("No orders matched specific criteria, returning all orders")
        filtered_orders = orders

    # 转换为OrderSchema
    return [
        OrderSchema(
            id=order.id,
            user_id=order.user_id,
            symbol=order.symbol,
            side=order.side,
            quantity=order.quantity,
            entry_price=order.entry_price,
            pnl=order.pnl,
            status=order.status,
            created_at=order.created_at,
        )
        for order in filtered_orders
    ]


async def get_market_price(symbol: str) -> Decimal:
    """
    获取市场价格

    Args:
        symbol: 交易对，如 BTC/USDT

    Returns:
        Decimal: 当前市场价格
    """
    logger.info(f"Getting market price for {symbol}")

    # 检查是否使用模拟模式
    if settings.trading.simulation_mode or settings.llm.openai_api_key.startswith(
        "sk-mock-"
    ):
        logger.info(f"Using simulation mode for {symbol}")
        try:
            price = await simulation_exchange.get_price(symbol)
            logger.info(f"Simulated price for {symbol}: {price}")
            return price
        except Exception as e:
            logger.error(f"Error getting simulated price for {symbol}: {e}")
            raise

    try:
        # 使用ccxt获取市场价格
        # 创建交易所实例
        exchange = ccxt.binance()

        # 获取ticker
        ticker = await exchange.fetch_ticker(symbol)

        # 关闭交易所连接
        await exchange.close()

        # 返回最新价格
        return Decimal(str(ticker["last"]))
    except Exception as e:
        logger.error(f"Error getting market price for {symbol}: {e}")
        # 返回模拟价格作为备用
        if symbol == "BTC/USDT":
            return Decimal("68000.0")
        elif symbol == "ETH/USDT":
            return Decimal("3500.0")
        elif symbol == "SOL/USDT":
            return Decimal("150.0")
        else:
            return Decimal("0.0")


async def execute_trade(
    trade_plan: TradePlan, user_id: uuid.UUID, db: AsyncSession
) -> TradeResult:
    """
    执行交易计划

    Args:
        trade_plan: 交易计划
        user_id: 用户ID
        db: 数据库会话

    Returns:
        TradeResult: 交易结果
    """
    logger.info(
        "Executing trade plan",
        user_id=user_id,
        symbol=trade_plan.symbol,
        side=trade_plan.side,
        quantity=trade_plan.quantity,
    )

    # 生成客户端订单ID
    client_order_id = str(uuid.uuid4())

    try:
        # 检查是否使用模拟模式
        if settings.trading.simulation_mode or settings.llm.openai_api_key.startswith(
            "sk-mock-"
        ):
            logger.info("Using simulation mode for trade execution")
            # 使用模拟交易所
            try:
                order = await simulation_exchange.create_order(
                    symbol=trade_plan.symbol,
                    side=trade_plan.side,
                    quantity=trade_plan.quantity,
                    client_order_id=client_order_id,
                )

                # 序列化agent_log，确保所有字段都可以JSON序列化
                def serialize_for_json(obj):
                    """递归序列化对象为JSON兼容格式"""
                    if isinstance(obj, uuid.UUID):
                        return str(obj)
                    elif isinstance(obj, Decimal):
                        return float(obj)
                    elif hasattr(obj, "value"):  # 枚举类型
                        return obj.value
                    elif isinstance(obj, dict):
                        return {k: serialize_for_json(v) for k, v in obj.items()}
                    elif isinstance(obj, list):
                        return [serialize_for_json(item) for item in obj]
                    else:
                        return obj

                serialized_trade_plan = serialize_for_json(trade_plan.model_dump())

                # 创建订单记录
                db_order = OrderModel(
                    id=uuid.uuid4(),
                    user_id=user_id,
                    client_order_id=client_order_id,
                    exchange_order_id=order["id"],
                    symbol=trade_plan.symbol,
                    side=trade_plan.side.value
                    if hasattr(trade_plan.side, "value")
                    else trade_plan.side,
                    quantity=trade_plan.quantity,
                    entry_price=Decimal(str(order["price"])),
                    status=OrderStatus.ACTIVE.value,
                    agent_log={
                        "trade_plan": serialized_trade_plan,
                        "simulation": True,
                    },
                )

                # 保存到数据库
                db.add(db_order)
                await db.commit()
                await db.refresh(db_order)

                logger.info(
                    "Order saved to database",
                    order_id=str(db_order.id),
                    client_order_id=client_order_id,
                    exchange_order_id=order["id"],
                )

                db.add(db_order)
                await db.commit()
                logger.info(
                    "Simulation order created successfully",
                    order_id=str(db_order.id),
                )

                return TradeResult(
                    status="success",
                    order_id=order["id"],
                    client_order_id=client_order_id,
                )
            except Exception as e:
                logger.error("Simulation order creation failed", error=str(e))
                return TradeResult(
                    status="failure",
                    client_order_id=client_order_id,
                    error_message=f"模拟订单创建失败: {str(e)}",
                )

        else:
            # 真实交易所交互
            try:
                # 获取用户交易所配置
                exchange_config_query = select(ExchangeConfig).where(
                    ExchangeConfig.user_id == user_id
                )
                exchange_config_result = await db.execute(exchange_config_query)
                exchange_config = exchange_config_result.scalar_one_or_none()

                if not exchange_config:
                    raise ValueError(f"找不到用户 {user_id} 的交易所配置")

                # 解密API凭证
                api_key, api_secret = decrypt_api_credentials(
                    exchange_config.encrypted_api_key,
                    exchange_config.encrypted_api_secret,
                )

                # 创建交易所实例
                exchange_class = getattr(ccxt, exchange_config.exchange_name)
                exchange = exchange_class(
                    {
                        "apiKey": api_key,
                        "secret": api_secret,
                        "enableRateLimit": True,
                    }
                )

                # 准备订单参数
                params = {"clientOrderId": client_order_id}

                logger.info(
                    "Placing order on exchange",
                    exchange=exchange_config.exchange_name,
                )

                # 执行订单
                order = await exchange.create_order(
                    symbol=trade_plan.symbol,
                    type=trade_plan.order_type.lower(),
                    side=trade_plan.side.lower(),
                    amount=float(trade_plan.quantity),
                    price=float(trade_plan.price) if trade_plan.price else None,
                    params=params,
                )

                # 关闭交易所连接
                await exchange.close()

                # 序列化agent_log，确保所有字段都可以JSON序列化
                def serialize_for_json(obj):
                    """递归序列化对象为JSON兼容格式"""
                    if isinstance(obj, uuid.UUID):
                        return str(obj)
                    elif isinstance(obj, Decimal):
                        return float(obj)
                    elif hasattr(obj, "value"):  # 枚举类型
                        return obj.value
                    elif isinstance(obj, dict):
                        return {k: serialize_for_json(v) for k, v in obj.items()}
                    elif isinstance(obj, list):
                        return [serialize_for_json(item) for item in obj]
                    else:
                        return obj

                serialized_trade_plan = serialize_for_json(trade_plan.model_dump())

                # 记录订单
                db_order = OrderModel(
                    id=uuid.uuid4(),
                    user_id=user_id,
                    client_order_id=client_order_id,
                    exchange_order_id=order["id"],
                    symbol=trade_plan.symbol,
                    side=trade_plan.side.value
                    if hasattr(trade_plan.side, "value")
                    else trade_plan.side,
                    quantity=trade_plan.quantity,
                    entry_price=Decimal(str(order["price"]))
                    if "price" in order
                    else None,
                    status=OrderStatus.ACTIVE.value,
                    agent_log={"trade_plan": serialized_trade_plan},
                )

                db.add(db_order)
                await db.commit()
                await db.refresh(db_order)
                logger.info(
                    "Real order created successfully",
                    order_id=str(db_order.id),
                    exchange_order_id=order["id"],
                )

                return TradeResult(
                    status="success",
                    order_id=order["id"],
                    client_order_id=client_order_id,
                )
            except Exception as e:
                logger.exception(
                    "Real order creation failed",
                    error=str(e),
                    error_type=type(e).__name__,
                    user_id=user_id,
                    symbol=trade_plan.symbol,
                )
                return TradeResult(
                    status="failure",
                    client_order_id=client_order_id,
                    error_message=f"订单创建失败: {str(e)}",
                )
    except Exception as e:
        logger.exception(
            "Unexpected error in execute_trade",
            error=str(e),
            error_type=type(e).__name__,
            user_id=user_id,
        )
        return TradeResult(
            status="failure",
            client_order_id=client_order_id,
            error_message=f"意外错误: {str(e)}",
        )


async def create_conditional_order(
    user_id: uuid.UUID,
    trigger_condition: Dict[str, Any],
    action_plan: Dict[str, Any],
    db: AsyncSession,
) -> Dict[str, Any]:
    """
    创建条件订单

    Args:
        user_id: 用户ID
        trigger_condition: 触发条件，包含symbol, operator, price等
        action_plan: 执行计划，包含side, quantity_usd等
        db: 数据库会话

    Returns:
        Dict[str, Any]: 创建的条件订单信息
    """
    from ..core.models import ConditionalOrder

    logger.info(
        "Creating conditional order",
        user_id=user_id,
        trigger_condition=trigger_condition,
        action_plan=action_plan,
    )

    try:
        # 创建条件订单记录
        conditional_order = ConditionalOrder(
            id=uuid.uuid4(),
            user_id=user_id,
            trigger_condition=trigger_condition,
            action_plan=action_plan,
            status="PENDING",
            created_at=datetime.now(),
        )

        db.add(conditional_order)
        await db.commit()
        await db.refresh(conditional_order)

        logger.info(
            "Conditional order created successfully",
            order_id=str(conditional_order.id),
        )

        return {
            "id": str(conditional_order.id),
            "user_id": conditional_order.user_id,
            "trigger_condition": conditional_order.trigger_condition,
            "action_plan": conditional_order.action_plan,
            "status": conditional_order.status,
            "created_at": conditional_order.created_at.isoformat(),
        }

    except Exception as e:
        logger.error("Failed to create conditional order", error=str(e))
        await db.rollback()
        raise


# 注意：实际应用中需要将SimulationExchange也更新为使用settings配置
# 模拟交易所类
class SimulationExchange:
    """模拟交易所，用于测试和开发环境"""

    def __init__(self):
        self.orders = {}
        self.prices = {
            "BTC/USDT": Decimal("68000.0"),
            "ETH/USDT": Decimal("3500.0"),
            "SOL/USDT": Decimal("150.0"),
        }

    async def get_price(self, symbol: str) -> Decimal:
        """获取模拟价格"""
        if symbol in self.prices:
            return self.prices[symbol]
        raise ValueError(f"Unknown symbol: {symbol}")

    async def create_order(
        self, symbol: str, side: str, quantity: Decimal, client_order_id: str
    ) -> dict:
        """创建模拟订单"""
        price = await self.get_price(symbol)
        order = {
            "id": f"sim_{client_order_id[:8]}",
            "client_order_id": client_order_id,
            "symbol": symbol,
            "side": side,
            "quantity": quantity,
            "price": price,
            "status": "FILLED",
        }
        self.orders[client_order_id] = order
        return order


# 模拟交易所实例，用于测试和开发环境
simulation_exchange = SimulationExchange()
