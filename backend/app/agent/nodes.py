import asyncio
import json
import uuid
from datetime import datetime, timed<PERSON>ta
from decimal import Decimal
from typing import Any, Dict, List, Optional, Union

import structlog
from langgraph.graph import END, StateGraph
from sqlalchemy import insert, select
from sqlalchemy.ext.asyncio import AsyncSession

from ..core.config import settings
from ..core.models import Agent<PERSON>he<PERSON><PERSON>, PendingAction
from ..core.schemas import (
    AgentState,
    ErrorAnalysis,
    IntentType,
    OrderType,
    ParsedIntent,
    TradePlan,
    TradeSide,
)
from ..services.dynamic_risk_manager import DynamicRiskManager
from ..services.agent_trace_service import trace_agent_node
from .error_handler import (
    AgentError,
    AgentNetworkError,
    AgentTimeoutError,
    AgentValidationError,
    handle_node_error,
    with_error_handling,
)
from .prompts import (
    ERROR_ANALYSIS_PROMPT,
    GENERATE_PLAN_PROMPT,
    PARSE_INTENTS_PROMPT,
)
from .tools import (
    TradeResult,
    create_conditional_order,
    execute_trade,
    find_orders_by_criteria,
    get_active_orders,
    get_market_price,
)

# 配置结构化日志
logger = structlog.get_logger()

# LLM服务架构说明：
# 本模块使用统一的LLM服务架构 (app.services.llm_service.LLMService)
# 支持多个LLM提供商：OpenAI、DeepSeek、Gemini、Claude
# 通过数据库中的LLMConfig表进行配置管理，支持用户级别的个性化配置


# 导入追踪装饰器
# 追踪装饰器已移至agent_trace_service

# 节点A: 文本预处理
@trace_agent_node("Preprocess")
@with_error_handling(node_name="preprocess_text", timeout_seconds=10.0)
async def preprocess_text(state: AgentState, db: AsyncSession = None) -> AgentState:
    """
    预处理原始输入文本，执行规范化操作以提高解析的稳定性。
    """
    logger.info("Preprocessing text", task_id=str(state.task_id))

    # 获取原始输入
    raw_input = state.raw_input

    # 规范化处理
    normalized = raw_input.lower()  # 转换为小写
    normalized = " ".join(normalized.split())  # 移除多余空格

    # 可以添加其他规范化步骤，如规范化符号等

    # 更新状态
    state.raw_input = normalized
    state.log.append(f"原始文本: '{raw_input}' 已规范化为: '{normalized}'")

    return state


# 节点B: 解析指令
@with_error_handling(
    node_name="parse_intents",
    timeout_seconds=30.0,
    retry_count=2,
    retry_delay=2.0,
)
@trace_agent_node("Parse")
async def parse_intents(state: AgentState, db: AsyncSession) -> AgentState:
    """
    使用可配置的LLM服务解析规范化后的文本，识别其中的交易意图。

    该函数使用新的LLM服务架构：
    1. 从数据库获取用户的默认LLM配置
    2. 支持多个LLM提供商（OpenAI、DeepSeek、Gemini、Claude）
    3. 自动处理不同提供商的响应格式
    4. 在LLM服务不可用时自动回退到模拟模式

    Args:
        state: Agent状态对象
        db: 数据库会话

    Returns:
        更新后的Agent状态，包含解析的交易意图
    """
    logger.info("Parsing intents", task_id=str(state.task_id))

    try:
        # 使用新的LLM配置系统进行解析
        logger.info("Using configurable LLM for intent parsing", task_id=str(state.task_id))

        # 构建上下文信息
        context_info = ""
        if state.context and "active_orders" in state.context:
            active_count = len(state.context["active_orders"])
            context_info = f"\n\n当前用户有 {active_count} 个活跃订单。"

        # 获取用户的默认LLM配置
        from ..core.models import LLMConfig
        from sqlalchemy import select, and_

        logger.info(
            "Searching for LLM configuration",
            task_id=str(state.task_id),
            user_id=str(state.user_id)
        )

        llm_stmt = select(LLMConfig).where(
            and_(
                LLMConfig.user_id == state.user_id,
                LLMConfig.enabled == True,
                LLMConfig.is_default == True
            )
        )
        llm_result = await db.execute(llm_stmt)
        llm_config = llm_result.scalar_one_or_none()

        if not llm_config:
            # 如果没有LLM配置，回退到模拟模式
            logger.warning(
                "No LLM configuration found, falling back to mock mode",
                task_id=str(state.task_id),
                user_id=str(state.user_id)
            )
            state.parsed_intents = await _mock_parse_intents(state.raw_input)
            state.log.append("未找到LLM配置，使用模拟模式解析")
            return state

        logger.info(
            "Found LLM configuration",
            task_id=str(state.task_id),
            config_id=str(llm_config.id),
            provider=llm_config.provider,
            model_name=llm_config.model_name
        )

        # 使用LLM服务进行解析
        from ..services.llm_service import LLMService
        import time

        # 准备LLM请求数据
        llm_request_messages = [
            {"role": "system", "content": PARSE_INTENTS_PROMPT},
            {
                "role": "user",
                "content": f"User Input: {state.raw_input}{context_info}\n\nParse this into trading intents.",
            },
        ]

        llm_request_data = {
            "provider": llm_config.provider,
            "model": llm_config.model_name,
            "messages": llm_request_messages,
            "temperature": 0.1,
            "max_tokens": llm_config.max_tokens,
            "timestamp": time.time()
        }

        try:
            # 记录开始时间
            llm_start_time = time.time()

            response = await LLMService.chat_completion(
                llm_config,
                messages=llm_request_messages,
                temperature=0.1
            )

            # 记录结束时间和计算响应时间
            llm_end_time = time.time()
            llm_response_time = (llm_end_time - llm_start_time) * 1000  # 转换为毫秒

            # 提取Token使用统计
            llm_token_usage = {}
            if llm_config.provider == "deepseek":
                usage = response.get("usage", {})
                llm_token_usage = {
                    "input_tokens": usage.get("prompt_tokens", 0),
                    "output_tokens": usage.get("completion_tokens", 0),
                    "total_tokens": usage.get("total_tokens", 0)
                }
            elif llm_config.provider == "gemini":
                usage = response.get("usageMetadata", {})
                llm_token_usage = {
                    "input_tokens": usage.get("promptTokenCount", 0),
                    "output_tokens": usage.get("candidatesTokenCount", 0),
                    "total_tokens": usage.get("totalTokenCount", 0)
                }
            elif llm_config.provider == "claude":
                usage = response.get("usage", {})
                llm_token_usage = {
                    "input_tokens": usage.get("input_tokens", 0),
                    "output_tokens": usage.get("output_tokens", 0),
                    "total_tokens": usage.get("input_tokens", 0) + usage.get("output_tokens", 0)
                }
            else:  # OpenAI/ChatGPT
                usage = response.get("usage", {})
                llm_token_usage = {
                    "input_tokens": usage.get("prompt_tokens", 0),
                    "output_tokens": usage.get("completion_tokens", 0),
                    "total_tokens": usage.get("total_tokens", 0)
                }

            # 准备LLM监控指标
            llm_metrics = {
                "response_time_ms": llm_response_time,
                "success": True,
                "provider": llm_config.provider,
                "model": llm_config.model_name,
                "temperature": 0.1,
                "timestamp": llm_end_time
            }

            # 解析响应内容
            import json

            try:
                # 根据不同的LLM提供商提取响应内容
                if llm_config.provider == "deepseek":
                    content = response.get("choices", [{}])[0].get("message", {}).get("content", "")
                elif llm_config.provider == "gemini":
                    candidates = response.get("candidates", [])
                    if candidates and candidates[0].get("content", {}).get("parts"):
                        content = candidates[0]["content"]["parts"][0].get("text", "")
                    else:
                        content = ""
                elif llm_config.provider == "claude":
                    content_list = response.get("content", [])
                    if content_list and content_list[0].get("text"):
                        content = content_list[0]["text"]
                    else:
                        content = ""
                else:  # OpenAI/ChatGPT
                    content = response.get("choices", [{}])[0].get("message", {}).get("content", "")

                # 清理和解析JSON响应
                # 处理可能包含代码块标记的响应
                content = content.strip()
                if content.startswith("```json"):
                    content = content[7:]  # 移除 ```json
                if content.endswith("```"):
                    content = content[:-3]  # 移除 ```
                content = content.strip()

                parsed_data = json.loads(content)
                if isinstance(parsed_data, list):
                    parsed_intents = [ParsedIntent(**item) for item in parsed_data]
                else:
                    parsed_intents = [ParsedIntent(**parsed_data)]

                logger.info(
                    "LLM intent parsing successful",
                    task_id=str(state.task_id),
                    llm_provider=llm_config.provider,
                    intent_count=len(parsed_intents),
                    input_tokens=llm_token_usage.get("input_tokens", 0),
                    output_tokens=llm_token_usage.get("output_tokens", 0),
                    response_time_ms=llm_response_time
                )

                # 保存LLM监控数据到追踪记录
                # 注意：这里我们需要获取当前的trace_id，暂时通过state传递
                if hasattr(state, '_current_trace_id'):
                    from ..services.agent_trace_service import trace_service

                    # 准备LLM响应数据（去除敏感信息）
                    llm_response_data = {
                        "provider": llm_config.provider,
                        "model": llm_config.model_name,
                        "content_length": len(content),
                        "parsed_intents_count": len(parsed_intents),
                        "timestamp": llm_end_time
                    }

                    await trace_service.update_llm_monitoring_data(
                        db=db,
                        trace_id=state._current_trace_id,
                        llm_request_data=llm_request_data,
                        llm_response_data=llm_response_data,
                        llm_token_usage=llm_token_usage,
                        llm_metrics=llm_metrics
                    )

            except (json.JSONDecodeError, TypeError, ValueError) as e:
                logger.warning(
                    "Failed to parse LLM response",
                    error=str(e),
                    content=content if 'content' in locals() else "No content",
                    llm_provider=llm_config.provider
                )
                parsed_intents = []

        except Exception as llm_error:
            # LLM调用失败，记录错误监控数据
            llm_end_time = time.time()
            llm_response_time = (llm_end_time - llm_start_time) * 1000 if 'llm_start_time' in locals() else 0

            # 准备错误监控指标
            llm_metrics = {
                "response_time_ms": llm_response_time,
                "success": False,
                "error": str(llm_error),
                "provider": llm_config.provider,
                "model": llm_config.model_name,
                "timestamp": llm_end_time
            }

            # 保存错误监控数据
            if hasattr(state, '_current_trace_id'):
                from ..services.agent_trace_service import trace_service

                llm_response_data = {
                    "provider": llm_config.provider,
                    "model": llm_config.model_name,
                    "error": str(llm_error),
                    "fallback_to_mock": True,
                    "timestamp": llm_end_time
                }

                await trace_service.update_llm_monitoring_data(
                    db=db,
                    trace_id=state._current_trace_id,
                    llm_request_data=llm_request_data if 'llm_request_data' in locals() else None,
                    llm_response_data=llm_response_data,
                    llm_metrics=llm_metrics
                )

            logger.error(
                "LLM service call failed, falling back to mock mode",
                task_id=str(state.task_id),
                llm_provider=llm_config.provider,
                error=str(llm_error),
                response_time_ms=llm_response_time
            )
            state.parsed_intents = await _mock_parse_intents(state.raw_input)
            state.log.append(f"LLM服务调用失败({llm_config.provider})，使用模拟模式解析")
            return state

        # 验证解析结果
        validated_intents = []
        for intent in parsed_intents:
            if _validate_parsed_intent(intent):
                validated_intents.append(intent)
            else:
                logger.warning("Invalid parsed intent", intent=intent.dict())

        state.parsed_intents = validated_intents
        state.log.append(f"LLM成功解析出 {len(validated_intents)} 个有效交易意图")

        # 记录解析结果的详细信息
        for i, intent in enumerate(validated_intents):
            state.log.append(
                f"意图 {i+1}: {intent.intent_type.value} - {intent.symbol or 'N/A'} - {intent.side.value if intent.side else 'N/A'}"
            )

        # 记录使用的LLM服务
        state.log.append(f"使用LLM服务: {llm_config.provider} ({llm_config.model_name})")

    except Exception as e:
        logger.error("Intent parsing failed", task_id=str(state.task_id), error=str(e))

        # 详细的错误分类和恢复策略
        error_type = _classify_parsing_error(str(e))
        recovery_strategy = _get_recovery_strategy(error_type)

        state.log.append(f"解析意图失败: {str(e)} (错误类型: {error_type})")

        # 根据错误类型执行不同的恢复策略
        if recovery_strategy == "retry_with_simplified_prompt":
            # 尝试使用简化的提示词重新解析
            try:
                simplified_intents = await _retry_with_simplified_parsing(
                    state.raw_input
                )
                state.parsed_intents = simplified_intents
                state.log.append("使用简化解析策略成功恢复")
                return state
            except Exception as retry_e:
                logger.warning("Simplified parsing also failed", error=str(retry_e))
                state.log.append(f"简化解析也失败: {str(retry_e)}")

        elif recovery_strategy == "fallback_to_mock":
            # 降级到模拟解析
            try:
                state.parsed_intents = await _mock_parse_intents(state.raw_input)
                state.log.append("降级到模拟解析模式")
                return state
            except Exception as mock_e:
                logger.warning("Mock parsing failed", error=str(mock_e))
                state.log.append(f"模拟解析失败: {str(mock_e)}")

        # 最终的错误恢复意图
        state.parsed_intents = [
            ParsedIntent(
                intent_type=IntentType.AMBIGUOUS,
                raw_text=state.raw_input,
                side=None,
                symbol=None,
                quantity_usd=None,
                target_criteria=None,
                confidence=Decimal("0.0"),
                clarification_needed=f"系统解析失败，请重新描述您的交易意图。错误类型: {error_type}，建议: {_get_user_guidance(error_type)}",
            )
        ]

    return state


def _classify_parsing_error(error_message: str) -> str:
    """分类解析错误类型"""
    error_lower = error_message.lower()

    if any(keyword in error_lower for keyword in ["rate limit", "quota", "429"]):
        return "rate_limit"
    elif any(
        keyword in error_lower for keyword in ["timeout", "connection", "network"]
    ):
        return "network_error"
    elif any(keyword in error_lower for keyword in ["invalid", "validation", "schema"]):
        return "validation_error"
    elif any(keyword in error_lower for keyword in ["unauthorized", "401", "403"]):
        return "auth_error"
    elif any(keyword in error_lower for keyword in ["model", "openai", "api"]):
        return "llm_error"
    else:
        return "unknown_error"


def _get_recovery_strategy(error_type: str) -> str:
    """根据错误类型获取恢复策略"""
    strategies = {
        "rate_limit": "retry_with_delay",
        "network_error": "retry_with_simplified_prompt",
        "validation_error": "fallback_to_mock",
        "auth_error": "fallback_to_mock",
        "llm_error": "retry_with_simplified_prompt",
        "unknown_error": "fallback_to_mock",
    }
    return strategies.get(error_type, "fallback_to_mock")


def _get_user_guidance(error_type: str) -> str:
    """根据错误类型提供用户指导"""
    guidance = {
        "rate_limit": "请稍后重试，系统正在处理大量请求",
        "network_error": "网络连接不稳定，请检查网络后重试",
        "validation_error": "请使用更清晰的语言描述您的交易意图",
        "auth_error": "系统配置问题，请联系管理员",
        "llm_error": "AI服务暂时不可用，请稍后重试",
        "unknown_error": "系统遇到未知错误，请重新描述您的需求",
    }
    return guidance.get(error_type, "请重新描述您的交易需求")


async def _retry_with_simplified_parsing(raw_input: str) -> List[ParsedIntent]:
    """使用简化的提示词重新解析"""
    # 简化的解析逻辑，专注于基本的买卖意图识别
    simplified_prompt = """
    你是一个交易意图解析器。请从用户输入中识别基本的交易意图。
    只需要识别：买入(BUY)或卖出(SELL)，交易对象，数量。
    如果无法确定，返回AMBIGUOUS类型。
    """

    try:
        # 这里可以调用简化的LLM解析或使用规则解析
        return await _mock_parse_intents(raw_input)  # 暂时使用模拟解析
    except Exception as e:
        logger.error("Simplified parsing failed", error=str(e))
        raise


async def _mock_parse_intents(raw_input: str) -> List[ParsedIntent]:
    """模拟意图解析，用于测试和开发"""
    input_lower = raw_input.lower()

    # 检查是否是多意图（包含"同时"、"和"、"还有"等关键词）
    is_multi_intent = any(word in input_lower for word in ["同时", "和", "还有", "以及"])

    if is_multi_intent:
        # 解析多意图
        intents = []
        import re

        # 查找所有交易对和数量的组合
        # 匹配模式：做多/做空 + 交易对 + 数量
        patterns = [
            r"做多\s*(btc/usdt|eth/usdt|sol/usdt)\s*(\d+)u",
            r"做空\s*(btc/usdt|eth/usdt|sol/usdt)\s*(\d+)u",
        ]

        for pattern in patterns:
            matches = re.findall(pattern, input_lower)
            for match in matches:
                symbol = match[0].upper()
                quantity = float(match[1])
                side = TradeSide.BUY if "做多" in pattern else TradeSide.SELL

                intents.append(
                    ParsedIntent(
                        intent_type=IntentType.CREATE_ORDER,
                        raw_text=raw_input,
                        side=side,
                        symbol=symbol,
                        quantity_usd=Decimal(str(quantity)),
                        confidence=Decimal("0.95"),
                        target_criteria=None,
                        clarification_needed=None,
                    )
                )

        if intents:
            return intents

    # 单意图解析（原有逻辑）
    # 提取交易对
    symbol = "BTC/USDT"
    if "eth" in input_lower:
        symbol = "ETH/USDT"
    elif "sol" in input_lower:
        symbol = "SOL/USDT"

    # 提取数量
    quantity = None
    import re

    quantity_match = re.search(r"(\d+)u", input_lower)
    if quantity_match:
        quantity = float(quantity_match.group(1))

    # 判断意图类型和方向
    if any(word in input_lower for word in ["多", "buy", "long", "做多"]):
        return [
            ParsedIntent(
                intent_type=IntentType.CREATE_ORDER,
                raw_text=raw_input,
                side=TradeSide.BUY,
                symbol=symbol,
                quantity_usd=Decimal(str(quantity)) if quantity else None,
                confidence=Decimal("0.95"),
                target_criteria=None,
                clarification_needed=None,
            )
        ]
    elif any(word in input_lower for word in ["空", "sell", "short", "做空"]):
        return [
            ParsedIntent(
                intent_type=IntentType.CREATE_ORDER,
                raw_text=raw_input,
                side=TradeSide.SELL,
                symbol=symbol,
                quantity_usd=Decimal(str(quantity)) if quantity else None,
                confidence=Decimal("0.95"),
                target_criteria=None,
                clarification_needed=None,
            )
        ]
    elif any(word in input_lower for word in ["平", "close", "tp", "平仓"]):
        # 检查是否是模糊表达
        if any(phrase in input_lower for phrase in ["那个", "这个", "盈利的", "亏损的"]):
            return [
                ParsedIntent(
                    intent_type=IntentType.AMBIGUOUS,
                    raw_text=raw_input,
                    side=None,
                    symbol=None,
                    quantity_usd=None,
                    target_criteria=None,
                    confidence=Decimal("0.6"),
                    clarification_needed="请明确指出要平仓的具体交易对和订单。",
                )
            ]
        else:
            return [
                ParsedIntent(
                    intent_type=IntentType.CLOSE_ORDER,
                    raw_text=raw_input,
                    side=TradeSide.SELL,  # 假设平多仓
                    symbol=symbol,
                    quantity_usd=None,
                    target_criteria="all active positions",
                    confidence=Decimal("0.9"),
                    clarification_needed=None,
                )
            ]
    elif any(word in input_lower for word in ["查看", "看", "显示", "状态", "订单", "持仓", "查询"]):
        return [
            ParsedIntent(
                intent_type=IntentType.QUERY_STATUS,
                raw_text=raw_input,
                side=None,
                symbol=None,
                quantity_usd=None,
                target_criteria="user orders and positions",
                confidence=Decimal("0.9"),
                clarification_needed=None,
            )
        ]
    else:
        return [
            ParsedIntent(
                intent_type=IntentType.AMBIGUOUS,
                raw_text=raw_input,
                side=None,
                symbol=None,
                quantity_usd=None,
                target_criteria=None,
                confidence=Decimal("0.5"),
                clarification_needed="无法确定交易方向，请明确指出是做多还是做空。",
            )
        ]


def _validate_parsed_intent(intent: ParsedIntent) -> bool:
    """验证解析出的意图是否有效"""
    # 基本字段验证
    if not intent.raw_text or not intent.intent_type:
        return False

    # 交易意图必须有方向（除了AMBIGUOUS）
    if intent.intent_type in [IntentType.CREATE_ORDER, IntentType.CLOSE_ORDER]:
        if intent.intent_type != IntentType.AMBIGUOUS and not intent.side:
            return False

    # 置信度范围验证
    if intent.confidence < 0 or intent.confidence > 1:
        return False

    # 交易对格式验证
    if intent.symbol and "/" not in intent.symbol:
        return False

    return True


# 节点B的路由函数
def route_after_parsing(state: AgentState) -> str:
    """
    根据解析结果决定下一步路由

    实现基于置信度的分层决策逻辑：
    1. 自动执行路径: confidence >= auto_approve_threshold (默认0.95)
    2. 人机协作路径: confidence_threshold <= confidence < auto_approve_threshold
    3. 强制确认路径: confidence < confidence_threshold (默认0.8)
    4. 拒绝路径: confidence < 0.5
    """
    # 检查是否有意图
    if not state.parsed_intents:
        return "Failure"

    # 从风控配置中获取置信度阈值，使用设计文档中的默认值
    confidence_threshold = Decimal("0.8")  # 默认置信度阈值
    auto_approve_threshold = Decimal("0.95")  # 默认自动批准阈值

    if (
        state.context
        and "risk_config" in state.context
    ):
        risk_config = state.context["risk_config"]
        if "confidence_threshold" in risk_config:
            confidence_threshold = Decimal(str(risk_config["confidence_threshold"]))
        if "auto_approve_threshold" in risk_config:
            auto_approve_threshold = Decimal(str(risk_config["auto_approve_threshold"]))

    logger.info(
        "路由决策参数",
        task_id=str(state.task_id),
        confidence_threshold=float(confidence_threshold),
        auto_approve_threshold=float(auto_approve_threshold),
        intents_count=len(state.parsed_intents)
    )

    # 分析所有意图，实现分层决策
    has_auto_approve_intent = False
    needs_confirmation = False
    should_reject = False

    for intent in state.parsed_intents:
        intent_confidence = intent.confidence

        logger.info(
            "分析意图",
            task_id=str(state.task_id),
            intent_type=intent.intent_type.value,
            confidence=float(intent_confidence),
            side=intent.side.value if intent.side else None,
            symbol=intent.symbol
        )

        # 1. 拒绝路径: 置信度过低 (< 0.5)
        if intent_confidence < Decimal("0.5"):
            logger.info(
                "意图置信度过低，拒绝执行",
                task_id=str(state.task_id),
                confidence=float(intent_confidence)
            )
            should_reject = True
            break

        # 2. 强制确认路径: 满足以下任一条件
        if (
            intent.intent_type == IntentType.AMBIGUOUS
            or (
                intent.intent_type in [IntentType.CREATE_ORDER, IntentType.CLOSE_ORDER]
                and intent.side is None
            )
            or intent_confidence < confidence_threshold
        ):
            logger.info(
                "意图需要用户确认",
                task_id=str(state.task_id),
                reason="AMBIGUOUS" if intent.intent_type == IntentType.AMBIGUOUS
                       else "missing_side" if intent.side is None
                       else "low_confidence",
                confidence=float(intent_confidence)
            )
            needs_confirmation = True
            continue

        # 3. 自动执行路径: 高置信度且条件满足
        if (
            intent_confidence >= auto_approve_threshold
            and intent.intent_type != IntentType.AMBIGUOUS
            and (
                intent.intent_type not in [IntentType.CREATE_ORDER, IntentType.CLOSE_ORDER]
                or intent.side is not None
            )
        ):
            logger.info(
                "意图满足自动执行条件",
                task_id=str(state.task_id),
                confidence=float(intent_confidence),
                intent_type=intent.intent_type.value
            )
            has_auto_approve_intent = True

    # 路由决策逻辑
    if should_reject:
        logger.info("路由决策: 拒绝执行", task_id=str(state.task_id))
        return "Failure"

    if needs_confirmation:
        logger.info("路由决策: 需要用户确认", task_id=str(state.task_id))
        return "UserConfirm"

    if has_auto_approve_intent:
        logger.info("路由决策: 自动执行", task_id=str(state.task_id))
        return "Context"

    # 4. 人机协作路径: 中等置信度
    logger.info("路由决策: 人机协作", task_id=str(state.task_id))
    return "UserConfirm"


# 节点C: 获取上下文
@with_error_handling(node_name="get_context", timeout_seconds=20.0, retry_count=1)
@trace_agent_node("Context")
async def get_context(state: AgentState, db: AsyncSession) -> AgentState:
    """
    获取执行计划所需的环境信息
    """
    logger.info("Getting context", task_id=str(state.task_id))

    # 初始化上下文字典
    state.context = {}

    try:
        # 1. 获取用户的风控配置
        from ..core.models import RiskConfig

        query = select(RiskConfig).where(RiskConfig.user_id == state.user_id)
        result = await db.execute(query)
        risk_config = result.scalar_one_or_none()

        if not risk_config:
            # 使用默认风控配置（包含置信度阈值）
            state.context["risk_config"] = {
                "max_concurrent_orders": 5,
                "max_total_position_value_usd": 1000.0,
                "default_position_size_usd": 100.0,
                "max_position_size_usd": 500.0,
                "allowed_symbols": ["BTC/USDT", "ETH/USDT"],
                "confidence_threshold": 0.8,  # 默认置信度阈值
                "auto_approve_threshold": 0.95,  # 默认自动批准阈值
            }
        else:
            state.context["risk_config"] = {
                "max_concurrent_orders": risk_config.max_concurrent_orders,
                "max_total_position_value_usd": float(risk_config.max_total_position_value_usd),
                "default_position_size_usd": float(risk_config.default_position_size_usd),
                "max_position_size_usd": float(risk_config.max_position_size_usd),
                "allowed_symbols": risk_config.allowed_symbols,
                "confidence_threshold": float(risk_config.confidence_threshold),
                "auto_approve_threshold": float(risk_config.auto_approve_threshold),
            }

        # 2. 获取活跃订单
        active_orders = await get_active_orders(state.user_id, db)
        state.context["active_orders"] = [order.model_dump() for order in active_orders]

        # 3. 获取相关交易对的市场价格
        market_prices = {}
        # 获取所有涉及的交易对
        symbols = set()
        for intent in state.parsed_intents:
            if intent.symbol:
                symbols.add(intent.symbol)

        # 查询每个交易对的市场价格
        for symbol in symbols:
            try:
                price = await get_market_price(symbol)
                market_prices[symbol] = price
            except Exception as e:
                logger.error(f"Failed to get market price for {symbol}", error=str(e))
                market_prices[symbol] = None

        state.context["market_prices"] = market_prices

        state.log.append(
            f"已加载上下文信息: 风控配置、{len(active_orders)}个活跃订单、{len(market_prices)}个市场价格"
        )

    except Exception as e:
        logger.error("Context loading failed", task_id=str(state.task_id), error=str(e))
        state.log.append(f"加载上下文失败: {str(e)}")

    return state


# 节点D: 制定初步计划
@with_error_handling(node_name="generate_plan", timeout_seconds=25.0, retry_count=1)
@trace_agent_node("Plan")
async def generate_plan(state: AgentState, db: AsyncSession = None) -> AgentState:
    """
    根据解析的意图和上下文信息生成执行计划

    Args:
        state: 当前状态

    Returns:
        AgentState: 更新后的状态
    """
    logger.info("Generating execution plan", task_id=str(state.task_id))

    state.log.append("正在生成执行计划...")
    plans = []

    # 获取风控配置和市场价格
    risk_config = state.context.get("risk_config", {})
    market_prices = state.context.get("market_prices", {})
    active_orders = state.context.get("active_orders", [])

    for intent in state.parsed_intents:
        # 跳过所有非交易意图
        if intent.intent_type not in [
            IntentType.CREATE_ORDER,
            IntentType.CLOSE_ORDER,
            IntentType.MODIFY_ORDER,
        ]:
            state.log.append(f"跳过非交易意图: {intent.intent_type}")
            continue

        # 对于创建订单，需要检查交易方向；对于平仓和修改订单，不需要
        if intent.intent_type == IntentType.CREATE_ORDER and not intent.side:
            state.log.append(f"跳过缺少交易方向的意图: '{intent.raw_text}'")
            continue

        if intent.intent_type == IntentType.CREATE_ORDER:
            market_price_raw = market_prices.get(intent.symbol)

            if not market_price_raw:
                state.log.append(f"无法获取 {intent.symbol} 的市场价格，跳过")
                continue

            # 确保市场价格是Decimal类型
            market_price = Decimal(str(market_price_raw))

            # 计算交易数量 - 确保使用Decimal类型
            quantity_usd = intent.quantity_usd or Decimal(str(risk_config.get(
                "default_position_size_usd", 100.0
            )))
            max_position_size_usd = Decimal(str(risk_config.get(
                "max_position_size_usd", 500.0
            )))

            # 确保不超过最大仓位限制
            quantity_usd = min(quantity_usd, max_position_size_usd)

            # 根据市场价格计算实际数量（基础货币）
            if market_price > Decimal("0"):
                quantity = quantity_usd / market_price

                # 创建交易计划
                plan = TradePlan(
                    symbol=intent.symbol,
                    side=intent.side,  # 直接使用AI判断的结果
                    quantity=quantity,
                    order_type=OrderType.MARKET,
                )
                plans.append(plan)
                state.log.append(
                    f"为意图 '{intent.raw_text}' 创建交易计划: {intent.side} {quantity} {intent.symbol}"
                )
            else:
                state.log.append(f"市场价格无效: {market_price}")

        elif intent.intent_type == IntentType.CLOSE_ORDER:
            # 根据条件查找要关闭的订单
            target_orders = []

            if intent.target_criteria:
                # 使用目标条件过滤活跃订单
                target_orders = [
                    order
                    for order in active_orders
                    if (intent.symbol and intent.symbol in order.get("symbol", ""))
                    or (not intent.symbol)
                ]

                if not target_orders:
                    state.log.append(f"未找到符合条件的订单: '{intent.target_criteria}'")
                    continue

            # 如果没有明确的条件，且指定了交易对，则关闭该交易对的所有活跃订单
            elif intent.symbol:
                target_orders = [
                    order
                    for order in active_orders
                    if intent.symbol in order.get("symbol", "")
                ]

                if not target_orders:
                    state.log.append(f"未找到交易对为 {intent.symbol} 的活跃订单")
                    continue

            # 为每个目标订单创建平仓计划
            for order in target_orders:
                # 对于平仓，方向是反向操作
                # 优先使用持仓的反向
                order_side = order.get("side", "buy")
                close_side = TradeSide.SELL if order_side == "buy" else TradeSide.BUY

                # 创建平仓计划
                plan = TradePlan(
                    symbol=order.get("symbol", ""),
                    side=close_side,
                    quantity=Decimal(str(order.get("quantity", 0))),
                    order_type=OrderType.MARKET,
                )
                plans.append(plan)
                state.log.append(
                    f"为订单 {order.get('id', 'unknown')} 创建平仓计划: {close_side} {order.get('quantity', 0)} {order.get('symbol', '')}"
                )

        elif intent.intent_type == IntentType.MODIFY_ORDER:
            # 修改订单逻辑（如修改止损止盈等）
            # 当前版本暂不实现
            state.log.append("修改订单功能尚未实现")
            continue

    # 更新状态
    state.execution_plan = plans

    if not plans:
        state.log.append("未生成任何执行计划")
    else:
        state.log.append(f"成功生成 {len(plans)} 个执行计划")

    return state


# 节点E: 风险评估（增强版）
@with_error_handling(node_name="assess_risk", timeout_seconds=30.0, retry_count=1)
@trace_agent_node("Risk")
async def assess_risk(state: AgentState, db: AsyncSession = None) -> AgentState:
    """
    增强的风险评估节点 - 使用动态风险管理器进行全面风险评估
    """
    logger.info("Starting enhanced risk assessment", task_id=str(state.task_id))

    try:
        # 获取交易计划
        execution_plan = state.execution_plan

        if not execution_plan or not execution_plan:
            state.risk_assessment = {
                "passed": False,
                "reason": "无交易执行计划",
                "details": [],
                "risk_score": 100,
                "recommendations": [],
            }
            state.log.append("没有执行计划，风险评估跳过")
            return state

        # 初始化动态风险管理器
        if not db:
            # 如果没有传入db，尝试从状态中获取或创建临时会话
            logger.warning("No database session provided for risk assessment")
            state.risk_assessment = {
                "passed": False,
                "reason": "无数据库连接",
                "details": [],
                "risk_score": 100,
                "recommendations": [],
            }
            state.log.append("缺少数据库连接，风险评估失败")
            return state

        dynamic_risk_manager = DynamicRiskManager(db)

        # 对每个交易计划进行动态风险评估
        all_assessments = []
        overall_passed = True
        max_risk_score = 0
        all_recommendations = []

        for trade_plan in execution_plan:
            # 执行动态风险评估
            assessment = await dynamic_risk_manager.assess_dynamic_risk(
                user_id=state.user_id,
                trade_plan=trade_plan,
                context=state.context,
            )

            all_assessments.append(
                {
                    "symbol": trade_plan.symbol,
                    "side": trade_plan.side,
                    "assessment": assessment,
                }
            )

            # 更新整体评估结果
            overall_risk = assessment["overall_risk"]
            risk_score = overall_risk["overall_score"]
            max_risk_score = max(max_risk_score, risk_score)

            # 如果任何一个交易的风险过高，整体评估失败
            if overall_risk["overall_level"].value in ["high", "critical"]:
                overall_passed = False

            # 收集所有建议
            all_recommendations.extend(assessment.get("recommendations", []))

        # 生成综合评估结果
        state.risk_assessment = {
            "passed": overall_passed,
            "reason": "动态风险评估完成" if overall_passed else "风险等级过高",
            "risk_score": max_risk_score,
            "details": all_assessments,
            "recommendations": all_recommendations,
            "assessment_type": "dynamic",
            "timestamp": datetime.utcnow().isoformat(),
        }

        # 记录评估结果到日志
        if overall_passed:
            state.log.append(f"动态风险评估通过，最高风险评分: {max_risk_score:.1f}")
        else:
            state.log.append(f"动态风险评估失败，最高风险评分: {max_risk_score:.1f}")
            # 设置错误消息
            error_details = []
            for assessment in all_assessments:
                overall_risk = assessment["assessment"]["overall_risk"]
                if overall_risk["overall_level"].value in ["high", "critical"]:
                    error_details.append(
                        f"{assessment['symbol']}: {overall_risk['recommendation']}"
                    )

            state.error_message = f"风险评估失败: {'; '.join(error_details)}"

        # 记录具体的风险建议
        for rec in all_recommendations[:3]:  # 只记录前3个最重要的建议
            state.log.append(f"  建议: {rec.get('message', '')}")

        logger.info(
            "Enhanced risk assessment completed",
            task_id=str(state.task_id),
            passed=overall_passed,
            max_risk_score=max_risk_score,
            recommendations_count=len(all_recommendations),
        )

    except Exception as e:
        logger.error(
            "Enhanced risk assessment failed",
            task_id=str(state.task_id),
            error=str(e),
        )
        state.risk_assessment = {
            "passed": False,
            "reason": f"动态风险评估过程出错: {str(e)}",
            "details": [],
            "risk_score": 100,
            "recommendations": [],
        }
        state.log.append(f"动态风险评估过程中出错: {str(e)}")

    return state


class RiskAssessmentEngine:
    """
    风险评估引擎 - 实现多层次风险控制策略
    """

    def __init__(self, risk_config, active_orders, market_prices):
        self.risk_config = risk_config
        self.active_orders = active_orders
        self.market_prices = market_prices
        self.risk_checks = []
        self.risk_score = 0

    async def assess_execution_plan(self, execution_plan) -> Dict[str, Any]:
        """评估交易执行计划的风险"""

        # 1. 基础合规性检查
        await self._check_basic_compliance(execution_plan)

        # 2. 交易对风险检查
        await self._check_symbol_risks(execution_plan)

        # 3. 头寸规模风险检查
        await self._check_position_size_risks(execution_plan)

        # 4. 流动性风险检查
        await self._check_liquidity_risks(execution_plan)

        # 5. 集中度风险检查
        await self._check_concentration_risks(execution_plan)

        # 6. 市场风险检查
        await self._check_market_risks(execution_plan)

        # 7. 时间风险检查
        await self._check_temporal_risks(execution_plan)

        # 8. 相关性风险检查
        await self._check_correlation_risks(execution_plan)

        # 9. 波动率风险检查
        await self._check_volatility_risks(execution_plan)

        # 10. 技术指标风险检查
        await self._check_technical_risks(execution_plan)

        # 11. 资金管理风险检查
        await self._check_capital_management_risks(execution_plan)

        # 12. 交易频率风险检查
        await self._check_trading_frequency_risks(execution_plan)

        # 计算综合风险评分
        self._calculate_risk_score()

        # 生成最终评估结果
        return self._generate_assessment_result()

    async def _check_basic_compliance(self, execution_plan):
        """基础合规性检查"""
        # 获取允许的交易对列表，支持对象或字典访问
        allowed_symbols = (
            self.risk_config.allowed_symbols
            if hasattr(self.risk_config, "allowed_symbols")
            else self.risk_config.get("allowed_symbols", [])
        )

        for plan in execution_plan:
            # 检查交易对白名单
            if plan.symbol not in allowed_symbols:
                self._add_risk_check(
                    {
                        "category": "合规性",
                        "check": "交易对白名单",
                        "passed": False,
                        "reason": f"交易对 {plan.symbol} 不在允许列表中",
                        "severity": "critical",
                        "risk_weight": 25,
                    }
                )
            else:
                self._add_risk_check(
                    {
                        "category": "合规性",
                        "check": "交易对白名单",
                        "passed": True,
                        "reason": f"交易对 {plan.symbol} 在允许列表中",
                        "severity": "low",
                        "risk_weight": 0,
                    }
                )

            # 检查交易参数有效性
            if not plan.quantity or plan.quantity <= 0:
                self._add_risk_check(
                    {
                        "category": "合规性",
                        "check": "交易数量有效性",
                        "passed": False,
                        "reason": f"交易数量无效: {plan.quantity}",
                        "severity": "critical",
                        "risk_weight": 20,
                    }
                )

            if not plan.side or plan.side not in ["BUY", "SELL"]:
                self._add_risk_check(
                    {
                        "category": "合规性",
                        "check": "交易方向有效性",
                        "passed": False,
                        "reason": f"交易方向无效: {plan.side}",
                        "severity": "critical",
                        "risk_weight": 20,
                    }
                )

    async def _check_symbol_risks(self, execution_plan):
        """交易对特定风险检查"""
        for plan in execution_plan:
            symbol = plan.symbol

            # 检查价格数据可用性
            if symbol not in self.market_prices:
                self._add_risk_check(
                    {
                        "category": "市场数据",
                        "check": "价格数据可用性",
                        "passed": False,
                        "reason": f"无法获取 {symbol} 的当前价格",
                        "severity": "high",
                        "risk_weight": 15,
                    }
                )
                continue

            current_price = self.market_prices[symbol]

            # 检查价格合理性（避免异常价格）
            if current_price <= 0:
                self._add_risk_check(
                    {
                        "category": "市场数据",
                        "check": "价格合理性",
                        "passed": False,
                        "reason": f"{symbol} 价格异常: {current_price}",
                        "severity": "high",
                        "risk_weight": 15,
                    }
                )

    async def _check_position_size_risks(self, execution_plan):
        """头寸规模风险检查"""
        # 获取最大单笔订单限额
        max_position_size_usd = (
            self.risk_config.max_position_size_usd
            if hasattr(self.risk_config, "max_position_size_usd")
            else self.risk_config.get("max_position_size_usd", 500.0)
        )

        # 获取最大并发订单数
        max_concurrent_orders = (
            self.risk_config.max_concurrent_orders
            if hasattr(self.risk_config, "max_concurrent_orders")
            else self.risk_config.get("max_concurrent_orders", 5)
        )

        for plan in execution_plan:
            symbol = plan.symbol
            current_price = self.market_prices.get(symbol, 0)

            if current_price > 0:
                order_value_usd = float(plan.quantity) * current_price

                # 单笔订单规模检查
                if order_value_usd > max_position_size_usd:
                    self._add_risk_check(
                        {
                            "category": "头寸规模",
                            "check": "单笔订单规模",
                            "passed": False,
                            "reason": f"订单价值 ${order_value_usd:.2f} 超过限制 ${max_position_size_usd}",
                            "severity": "high",
                            "risk_weight": 20,
                        }
                    )
                else:
                    # 计算规模风险评分
                    size_ratio = order_value_usd / max_position_size_usd
                    risk_weight = min(10, size_ratio * 10)  # 最高10分

                    self._add_risk_check(
                        {
                            "category": "头寸规模",
                            "check": "单笔订单规模",
                            "passed": True,
                            "reason": f"订单价值 ${order_value_usd:.2f} 在限制内 ({size_ratio:.1%})",
                            "severity": "low" if size_ratio < 0.5 else "medium",
                            "risk_weight": risk_weight,
                        }
                    )

        # 检查并发订单数
        total_orders = len(self.active_orders) + len(execution_plan)
        if total_orders > max_concurrent_orders:
            self._add_risk_check(
                {
                    "category": "头寸规模",
                    "check": "并发订单数",
                    "passed": False,
                    "reason": f"总订单数 {total_orders} 超过限制 {max_concurrent_orders}",
                    "severity": "medium",
                    "risk_weight": 15,
                }
            )
        else:
            order_ratio = total_orders / max_concurrent_orders
            risk_weight = min(5, order_ratio * 5)

            self._add_risk_check(
                {
                    "category": "头寸规模",
                    "check": "并发订单数",
                    "passed": True,
                    "reason": f"总订单数 {total_orders} 在限制内 ({order_ratio:.1%})",
                    "severity": "low" if order_ratio < 0.7 else "medium",
                    "risk_weight": risk_weight,
                }
            )

    async def _check_liquidity_risks(self, execution_plan):
        """流动性风险检查"""
        for plan in execution_plan:
            symbol = plan.symbol

            # 简化版本：基于交易对类型评估流动性
            if (
                symbol.endswith("USDT")
                or symbol.endswith("BTC")
                or symbol.endswith("ETH")
            ):
                self._add_risk_check(
                    {
                        "category": "流动性",
                        "check": f"{symbol}流动性",
                        "passed": True,
                        "reason": f"{symbol} 为主流交易对，流动性良好",
                        "severity": "low",
                        "risk_weight": 2,
                    }
                )
            else:
                self._add_risk_check(
                    {
                        "category": "流动性",
                        "check": f"{symbol}流动性",
                        "passed": True,
                        "reason": f"{symbol} 流动性需要关注",
                        "severity": "medium",
                        "risk_weight": 5,
                    }
                )

    async def _check_concentration_risks(self, execution_plan):
        """集中度风险检查"""
        # 获取最大总持仓价值
        max_total_position_value_usd = (
            self.risk_config.max_total_position_value_usd
            if hasattr(self.risk_config, "max_total_position_value_usd")
            else self.risk_config.get("max_total_position_value_usd", 1000.0)
        )

        # 计算各交易对的总暴露
        symbol_exposure = {}

        # 计算现有订单暴露
        for order in self.active_orders:
            symbol = None
            quantity = 0

            if isinstance(order, dict):
                symbol = order.get("symbol")
                quantity = order.get("quantity", 0)
            elif hasattr(order, "symbol"):
                symbol = order.symbol
                quantity = order.quantity

            if symbol and symbol in self.market_prices:
                current_price = self.market_prices.get(symbol, 0)
                if current_price > 0:
                    order_value = float(quantity) * current_price
                    symbol_exposure[symbol] = (
                        symbol_exposure.get(symbol, 0) + order_value
                    )

        # 计算计划订单暴露
        for plan in execution_plan:
            symbol = plan.symbol
            current_price = self.market_prices.get(symbol, 0)
            if current_price > 0:
                plan_value = float(plan.quantity) * current_price
                symbol_exposure[symbol] = symbol_exposure.get(symbol, 0) + plan_value

        # 检查总暴露
        total_exposure = sum(symbol_exposure.values())
        if total_exposure > max_total_position_value_usd:
            self._add_risk_check(
                {
                    "category": "集中度",
                    "check": "总风险暴露",
                    "passed": False,
                    "reason": f"总暴露 ${total_exposure:.2f} 超过限制 ${max_total_position_value_usd}",
                    "severity": "high",
                    "risk_weight": 25,
                }
            )
        else:
            exposure_ratio = total_exposure / max_total_position_value_usd
            risk_weight = min(15, exposure_ratio * 15)

            self._add_risk_check(
                {
                    "category": "集中度",
                    "check": "总风险暴露",
                    "passed": True,
                    "reason": f"总暴露 ${total_exposure:.2f} 在限制内 ({exposure_ratio:.1%})",
                    "severity": "low" if exposure_ratio < 0.7 else "medium",
                    "risk_weight": risk_weight,
                }
            )

        # 检查单个交易对集中度
        max_symbol_concentration = max_total_position_value_usd * 0.3  # 30%集中度限制
        for symbol, exposure in symbol_exposure.items():
            if exposure > max_symbol_concentration:
                self._add_risk_check(
                    {
                        "category": "集中度",
                        "check": f"{symbol}持仓集中度",
                        "passed": False,
                        "reason": f"{symbol} 暴露 ${exposure:.2f} 超过集中度限制 ${max_symbol_concentration:.2f}",
                        "severity": "medium",
                        "risk_weight": 15,
                    }
                )
            else:
                concentration_ratio = exposure / max_symbol_concentration
                risk_weight = min(8, concentration_ratio * 8)

                self._add_risk_check(
                    {
                        "category": "集中度",
                        "check": f"{symbol}持仓集中度",
                        "passed": True,
                        "reason": f"{symbol} 暴露 ${exposure:.2f} 在集中度限制内 ({concentration_ratio:.1%})",
                        "severity": "low" if concentration_ratio < 0.5 else "medium",
                        "risk_weight": risk_weight,
                    }
                )

    async def _check_market_risks(self, execution_plan):
        """市场风险检查"""
        for plan in execution_plan:
            symbol = plan.symbol

            # 简化版本：基于交易方向和市场条件的基本风险评估
            self._add_risk_check(
                {
                    "category": "市场风险",
                    "check": f"{symbol}市场风险",
                    "passed": True,
                    "reason": f"{symbol} {plan.side} 订单市场风险可控",
                    "severity": "medium",
                    "risk_weight": 5,
                }
            )

    async def _check_temporal_risks(self, execution_plan):
        """时间风险检查"""
        current_time = datetime.utcnow()

        # 检查是否在交易时间内
        self._add_risk_check(
            {
                "category": "时间风险",
                "check": "交易时间",
                "passed": True,
                "reason": "当前时间适合交易",
                "severity": "low",
                "risk_weight": 2,
            }
        )

    async def _check_correlation_risks(self, execution_plan):
        """相关性风险检查"""
        # 检查交易对之间的相关性
        symbols = [plan.symbol for plan in execution_plan]
        if len(set(symbols)) < len(symbols):
            self._add_risk_check(
                {
                    "category": "相关性风险",
                    "check": "重复交易对",
                    "passed": False,
                    "reason": "存在重复的交易对",
                    "severity": "medium",
                    "risk_weight": 10,
                }
            )
        else:
            self._add_risk_check(
                {
                    "category": "相关性风险",
                    "check": "交易对多样性",
                    "passed": True,
                    "reason": "交易对多样性良好",
                    "severity": "low",
                    "risk_weight": 0,
                }
            )

        # 检查高相关性资产组
        correlation_groups = [
            ["BTCUSDT", "ETHUSDT"],  # 主流加密货币
            ["ADAUSDT", "DOTUSDT"],  # 智能合约平台
        ]

        for group in correlation_groups:
            overlapping_symbols = set(symbols) & set(group)
            if len(overlapping_symbols) > 1:
                self._add_risk_check(
                    {
                        "category": "相关性风险",
                        "check": "高相关资产检查",
                        "passed": False,
                        "reason": f"同时交易高相关资产: {list(overlapping_symbols)}",
                        "severity": "medium",
                        "risk_weight": 12,
                    }
                )

    async def _check_volatility_risks(self, execution_plan):
        """波动率风险检查"""
        max_volatility_threshold = (
            self.risk_config.max_volatility_threshold
            if hasattr(self.risk_config, "max_volatility_threshold")
            else self.risk_config.get("max_volatility_threshold", 0.05)
        )

        for plan in execution_plan:
            symbol = plan.symbol
            if symbol in self.market_prices:
                # 简化的波动率估算
                estimated_volatility = 0.03  # 3%日波动率

                if estimated_volatility > max_volatility_threshold:
                    self._add_risk_check(
                        {
                            "category": "波动率风险",
                            "check": "高波动率检查",
                            "passed": False,
                            "reason": f"{symbol} 波动率过高: {estimated_volatility:.2%}",
                            "severity": "high",
                            "risk_weight": 15,
                        }
                    )

    async def _check_technical_risks(self, execution_plan):
        """技术指标风险检查"""
        for plan in execution_plan:
            symbol = plan.symbol
            if symbol in self.market_prices:
                current_price = self.market_prices[symbol]

                # 检查心理阻力位
                if self._is_near_psychological_level(current_price):
                    self._add_risk_check(
                        {
                            "category": "技术风险",
                            "check": "心理阻力位检查",
                            "passed": False,
                            "reason": f"{symbol} 接近心理阻力位: {current_price}",
                            "severity": "medium",
                            "risk_weight": 8,
                        }
                    )

    async def _check_capital_management_risks(self, execution_plan):
        """资金管理风险检查"""
        max_total_exposure = (
            self.risk_config.max_total_exposure_usd
            if hasattr(self.risk_config, "max_total_exposure_usd")
            else self.risk_config.get("max_total_exposure_usd", 2000.0)
        )

        # 计算总敞口
        current_exposure = sum(
            float(order.quantity) * float(self.market_prices.get(order.symbol, 0))
            for order in self.active_orders
            if hasattr(order, "quantity") and hasattr(order, "symbol")
        )

        new_exposure = sum(
            float(plan.quantity) * float(self.market_prices.get(plan.symbol, 0))
            for plan in execution_plan
        )

        total_exposure = current_exposure + new_exposure

        if total_exposure > max_total_exposure:
            self._add_risk_check(
                {
                    "category": "资金管理",
                    "check": "总敞口限制",
                    "passed": False,
                    "reason": f"总敞口将超限: {total_exposure:.2f} > {max_total_exposure:.2f}",
                    "severity": "critical",
                    "risk_weight": 25,
                }
            )

    async def _check_trading_frequency_risks(self, execution_plan):
        """交易频率风险检查"""
        max_trades_per_hour = (
            self.risk_config.max_trades_per_hour
            if hasattr(self.risk_config, "max_trades_per_hour")
            else self.risk_config.get("max_trades_per_hour", 10)
        )

        if len(execution_plan) > max_trades_per_hour:
            self._add_risk_check(
                {
                    "category": "交易频率",
                    "check": "交易频率限制",
                    "passed": False,
                    "reason": f"交易频率过高: {len(execution_plan)} > {max_trades_per_hour}",
                    "severity": "medium",
                    "risk_weight": 12,
                }
            )

    def _is_near_psychological_level(self, price: float) -> bool:
        """检查价格是否接近心理阻力位"""
        # 检查整数价位
        if abs(price - round(price)) < 0.01:
            return True

        # 检查重要整数位
        for magnitude in [10, 100, 1000, 10000]:
            if abs(price % magnitude) < magnitude * 0.02:
                return True

        return False

    def _add_risk_check(self, check):
        """添加风险检查结果"""
        self.risk_checks.append(check)

    def _calculate_risk_score(self):
        """计算综合风险评分"""
        total_weight = 0
        failed_critical = 0

        for check in self.risk_checks:
            if not check["passed"]:
                if check["severity"] == "critical":
                    failed_critical += 1
                total_weight += check.get("risk_weight", 0)
            else:
                # 即使通过的检查也可能有风险权重
                total_weight += check.get("risk_weight", 0)

        # 如果有关键失败，风险评分直接设为100
        if failed_critical > 0:
            self.risk_score = 100
        else:
            # 否则基于权重计算，最高100分
            self.risk_score = min(100, total_weight)

    def _generate_assessment_result(self) -> Dict[str, Any]:
        """生成最终评估结果"""
        failed_checks = [check for check in self.risk_checks if not check["passed"]]
        critical_failures = [
            check for check in failed_checks if check["severity"] == "critical"
        ]
        high_severity_failures = [
            check for check in failed_checks if check["severity"] == "high"
        ]

        # 决定是否通过
        passed = len(critical_failures) == 0 and self.risk_score < 80

        # 生成风险等级
        if self.risk_score >= 80:
            risk_level = "critical"
        elif self.risk_score >= 60:
            risk_level = "high"
        elif self.risk_score >= 40:
            risk_level = "medium"
        else:
            risk_level = "low"

        return {
            "passed": passed,
            "risk_score": self.risk_score,
            "risk_level": risk_level,
            "reason": "风险评估通过" if passed else "风险评估失败",
            "details": self.risk_checks,
            "failed_checks": failed_checks,
            "critical_failures": critical_failures,
            "high_severity_failures": high_severity_failures,
            "summary": {
                "total_checks": len(self.risk_checks),
                "passed_checks": len(self.risk_checks) - len(failed_checks),
                "failed_checks": len(failed_checks),
                "critical_failures": len(critical_failures),
                "risk_categories": list(
                    set(check["category"] for check in self.risk_checks)
                ),
            },
        }


# 节点E的路由函数
def route_after_risk_assessment(state: AgentState) -> str:
    """
    根据风险评估结果决定下一步路由
    """
    # 检查风险评估结果
    if hasattr(state, "risk_assessment") and state.risk_assessment:
        if not state.risk_assessment.get("passed", False):
            logger.info(
                "Risk assessment failed, routing to Failure",
                task_id=str(state.task_id),
                reason=state.risk_assessment.get("reason", "Unknown"),
            )
            return "Failure"

    # 如果日志中包含"风险评估失败"字样，也拒绝执行（向后兼容）
    for log_entry in state.log:
        if "风险评估失败" in log_entry:
            logger.info(
                "Risk assessment failed (from log), routing to Failure",
                task_id=str(state.task_id),
            )
            return "Failure"

    logger.info(
        "Risk assessment passed, routing to Execute",
        task_id=str(state.task_id),
    )
    return "Execute"


# 节点F: 执行计划
@with_error_handling(node_name="execute_plan", timeout_seconds=60.0, retry_count=1)
@trace_agent_node("Execute")
async def execute_plan(state: AgentState, db: AsyncSession) -> AgentState:
    """
    执行交易计划

    采用"尽力而为，完整记录"的策略执行计划。即使某个计划执行失败，
    也会继续尝试执行后续的计划，并记录所有执行结果。

    Args:
        state: 当前Agent状态
        db: 数据库会话

    Returns:
        更新后的Agent状态
    """
    logger.info(
        "Executing trade plans",
        task_id=str(state.task_id),
        plan_count=len(state.execution_plan),
    )

    if not state.execution_plan:
        state.log.append("无执行计划，跳过执行阶段")
        return state

    # 使用传入的数据库会话

    # 初始化执行引擎
    execution_engine = TradeExecutionEngine(
        user_id=state.user_id,
        task_id=str(state.task_id),
        simulation_mode=getattr(settings, "SIMULATION_MODE", True),
    )

    try:
        # 执行交易计划
        execution_results = await execution_engine.execute_plans(
            state.execution_plan, db
        )

        # 保存执行结果到状态 - 转换为字典格式
        state.execution_results = [
            {
                "status": result.status,
                "order_id": getattr(result, "exchange_order_id", None)
                or getattr(result, "order_id", None),
                "client_order_id": result.client_order_id,
                "error_message": getattr(result, "error_message", None),
            }
            for result in execution_results
        ]

        # 更新日志
        successful_executions = [
            r for r in execution_results if getattr(r, "status", None) == "success"
        ]
        failed_executions = [
            r for r in execution_results if getattr(r, "status", None) != "success"
        ]

        state.log.append(
            f"执行完成: {len(successful_executions)}个成功, {len(failed_executions)}个失败"
        )

        # 记录详细的执行结果
        for result in execution_results:
            if getattr(result, "status", None) == "success":
                state.log.append(f"✓ 执行成功: {getattr(result, 'order_id', 'unknown')}")
            else:
                state.log.append(
                    f"✗ 执行失败: {getattr(result, 'error_message', 'unknown error')}"
                )
                if hasattr(result, "error_message"):
                    state.error_message = result.error_message

        logger.info(
            f"交易计划执行完成: 成功 {len(successful_executions)}/{len(execution_results)}"
        )

        # 设置最终结果
        state.final_result = {
            "execution_summary": {
                "total_plans": len(execution_results),
                "successful": len(successful_executions),
                "failed": len(failed_executions),
                "success_rate": len(successful_executions) / len(execution_results)
                if execution_results
                else 0,
            },
            "execution_results": state.execution_results,  # 使用已转换的字典格式
        }

    except Exception as e:
        logger.error(f"执行交易计划时出错: {e}")
        state.execution_results = [
            {"success": False, "error": str(e), "message": f"执行引擎异常: {str(e)}"}
        ]
        state.error_message = str(e)
        state.log.append(f"执行过程中出错: {str(e)}")

    return state


class TradeExecutionEngine:
    """交易执行引擎 - 负责执行交易计划"""

    def __init__(self, user_id: int, task_id: str, simulation_mode: bool = False):
        self.user_id = user_id
        self.task_id = task_id
        self.simulation_mode = simulation_mode

    async def execute_plans(self, execution_plans, db: AsyncSession):
        """执行交易计划列表"""
        results = []

        logger.info(f"开始执行 {len(execution_plans)} 个交易计划")

        for i, plan in enumerate(execution_plans):
            try:
                logger.info(
                    f"执行第 {i+1}/{len(execution_plans)} 个计划: {plan.symbol} {plan.side} {plan.quantity}"
                )

                # 根据订单类型选择执行方法
                if hasattr(plan, "order_type") and plan.order_type in [
                    "STOP_LOSS",
                    "TAKE_PROFIT",
                    "CONDITIONAL",
                ]:
                    result = await self._execute_conditional_order(plan, db)
                elif hasattr(plan, "action") and plan.action == "CLOSE":
                    result = await self._execute_close_order(plan, db)
                elif hasattr(plan, "action") and plan.action == "MODIFY":
                    result = await self._execute_modify_order(plan, db)
                else:
                    result = await self._execute_regular_order(plan, db)

                results.append(result)

                # 记录执行结果
                if getattr(result, "status", None) == "success":
                    logger.info(
                        f"计划 {i+1} 执行成功: {getattr(result, 'order_id', 'unknown')}"
                    )
                else:
                    logger.warning(
                        f"计划 {i+1} 执行失败: {getattr(result, 'error_message', 'unknown error')}"
                    )

                # 在计划之间添加短暂延迟，避免API限制
                if i < len(execution_plans) - 1:
                    import asyncio

                    await asyncio.sleep(0.1)

            except Exception as e:
                logger.error(f"执行计划 {i+1} 时出现异常: {e}")
                result = TradeResult(
                    status="failure",
                    client_order_id=str(uuid.uuid4()),
                    error_message=f"执行异常: {str(e)}",
                )
                results.append(result)

        return results

    async def _execute_regular_order(self, plan, db: AsyncSession):
        """执行普通订单"""
        try:
            # 无论是模拟模式还是真实模式，都调用execute_trade工具
            # 该工具内部会根据配置决定是否使用模拟交易所
            from ..agent.tools import execute_trade

            return await execute_trade(trade_plan=plan, user_id=self.user_id, db=db)
        except Exception as e:
            return TradeResult(
                status="failure",
                client_order_id=str(uuid.uuid4()),
                error_message=f"普通订单执行失败: {str(e)}",
            )

    async def _execute_conditional_order(self, plan, db: AsyncSession):
        """执行条件订单"""
        try:
            if self.simulation_mode:
                # 模拟模式 - 创建条件订单记录
                return TradeResult(
                    status="success",
                    order_id=f"COND_{uuid.uuid4().hex[:8]}",
                    client_order_id=str(uuid.uuid4()),
                    symbol=plan.symbol,
                    side=plan.side,
                    quantity=plan.quantity,
                    price=getattr(plan, "trigger_price", None),
                    order_type=getattr(plan, "order_type", "CONDITIONAL"),
                    message="模拟条件订单创建成功，等待触发",
                )
            else:
                # 真实模式 - 创建条件订单记录到数据库
                from datetime import datetime, timedelta

                from ..models.orders import ConditionalOrder

                conditional_order = ConditionalOrder(
                    user_id=self.user_id,
                    symbol=plan.symbol,
                    side=plan.side.value if hasattr(plan.side, "value") else plan.side,
                    quantity=plan.quantity,
                    order_type=getattr(plan, "order_type", "CONDITIONAL"),
                    trigger_price=getattr(plan, "trigger_price", None),
                    trigger_condition=getattr(
                        plan, "trigger_condition", "GREATER_THAN"
                    ),
                    status="PENDING",
                    created_at=datetime.utcnow(),
                    expires_at=datetime.utcnow() + timedelta(days=30),  # 30天后过期
                )

                db.add(conditional_order)
                await db.commit()
                await db.refresh(conditional_order)

                return TradeResult(
                    status="success",
                    order_id=str(conditional_order.id),
                    client_order_id=str(uuid.uuid4()),
                    symbol=plan.symbol,
                    side=plan.side,
                    quantity=plan.quantity,
                    price=getattr(plan, "trigger_price", None),
                    order_type=getattr(plan, "order_type", "CONDITIONAL"),
                    message="条件订单创建成功，等待触发",
                )
        except Exception as e:
            return TradeResult(
                status="failure",
                client_order_id=str(uuid.uuid4()),
                error_message=f"条件订单执行失败: {str(e)}",
            )

    async def _execute_close_order(self, plan, db: AsyncSession):
        """执行平仓订单"""
        try:
            if self.simulation_mode:
                return TradeResult(
                    status="success",
                    order_id=f"CLOSE_{uuid.uuid4().hex[:8]}",
                    client_order_id=str(uuid.uuid4()),
                    symbol=plan.symbol,
                    side="SELL"
                    if getattr(plan, "position_side", "LONG") == "LONG"
                    else "BUY",
                    quantity=getattr(plan, "quantity", 0),
                    order_type="MARKET",
                    message="模拟平仓订单执行成功",
                )
            else:
                # 真实模式 - 调用平仓逻辑
                from ..services.trading import close_position

                return await close_position(
                    symbol=plan.symbol,
                    user_id=self.user_id,
                    quantity=getattr(plan, "quantity", None),
                    db=db,
                )
        except Exception as e:
            return TradeResult(
                status="failure",
                client_order_id=str(uuid.uuid4()),
                error_message=f"平仓订单执行失败: {str(e)}",
            )

    async def _execute_modify_order(self, plan, db: AsyncSession):
        """执行修改订单"""
        try:
            # 目前仅支持模拟模式
            return TradeResult(
                status="success" if self.simulation_mode else "failure",
                order_id=getattr(plan, "order_id", f"MOD_{uuid.uuid4().hex[:8]}"),
                client_order_id=str(uuid.uuid4()),
                symbol=getattr(plan, "symbol", "UNKNOWN"),
                message="模拟订单修改成功" if self.simulation_mode else "订单修改功能暂未实现",
            )
        except Exception as e:
            return TradeResult(
                status="failure",
                client_order_id=str(uuid.uuid4()),
                error_message=f"修改订单执行失败: {str(e)}",
            )


# 节点F的路由函数
def route_after_execution(state: AgentState) -> str:
    """
    根据执行结果决定下一步路由
    """
    # 如果有错误消息，需要分析错误
    if state.error_message:
        return "AnalyzeError"

    return "Success"


# 节点I: 分析错误
@with_error_handling(node_name="analyze_error", timeout_seconds=20.0, retry_count=1)
async def analyze_error(state: AgentState) -> AgentState:
    """
    分析错误信息，确定是否可重试，并尝试自我修正
    """
    logger.info(
        "Analyzing error",
        task_id=str(state.task_id),
        error=state.error_message,
    )

    try:
        # 创建错误分析器
        error_analyzer = ErrorAnalyzer(state)

        # 执行多层次错误分析
        analysis_result = await error_analyzer.analyze()

        # 应用分析结果到状态
        state.log.extend(analysis_result.get("logs", []))

        # 如果有自我修正建议，应用它们
        if analysis_result.get("self_correction"):
            await _apply_self_correction(state, analysis_result["self_correction"])

        # 设置重试标志
        if (
            analysis_result.get("should_retry", False)
            and state.retry_count < settings.llm.max_retries
        ):
            state.retry_count += 1
            state.log.append(f"错误分析完成，将进行第 {state.retry_count} 次重试")
        else:
            state.log.append("错误分析完成，无法重试或已达最大重试次数")

    except Exception as e:
        logger.error("Error analysis failed", task_id=str(state.task_id), error=str(e))
        state.log.append(f"错误分析失败: {str(e)}")
        state.log.append("默认视为不可重试的错误")

    return state


class ErrorAnalyzer:
    """错误分析器 - 提供多层次的错误分析和自我修正能力"""

    def __init__(self, state: AgentState):
        self.state = state
        self.error_message = state.error_message or ""
        self.retry_count = state.retry_count
        self.logs = []

    async def analyze(self) -> Dict[str, Any]:
        """执行完整的错误分析流程"""
        # 1. 基础错误分类
        error_category = self._classify_error()
        self.logs.append(f"错误分类: {error_category}")

        # 2. 错误严重性评估
        severity = self._assess_severity()
        self.logs.append(f"错误严重性: {severity}")

        # 3. 可重试性分析
        is_retryable = self._is_retryable(error_category, severity)
        self.logs.append(f"可重试性: {'是' if is_retryable else '否'}")

        # 4. 自我修正建议
        self_correction = await self._generate_self_correction(error_category)
        if self_correction:
            self.logs.append(f"自我修正建议: {self_correction.get('description', '无')}")

        # 5. 使用LLM进行深度分析（如果可用）
        if not settings.trading.simulation_mode and settings.llm.openai_api_key:
            llm_analysis = await self._llm_analysis()
            if llm_analysis:
                self.logs.append(f"LLM分析: {llm_analysis.reason}")
                # LLM分析可以覆盖基础分析结果
                is_retryable = llm_analysis.is_correctable

        return {
            "category": error_category,
            "severity": severity,
            "should_retry": is_retryable
            and self.retry_count < settings.llm.max_retries,
            "self_correction": self_correction,
            "logs": self.logs,
        }

    def _classify_error(self) -> str:
        """错误分类"""
        error_lower = self.error_message.lower()

        # 网络相关错误
        if any(
            keyword in error_lower
            for keyword in [
                "network",
                "timeout",
                "connection",
                "dns",
                "unreachable",
                "502",
                "503",
                "504",
            ]
        ):
            return "network"

        # 认证相关错误（不可重试）
        elif any(
            keyword in error_lower
            for keyword in [
                "invalid api key",
                "api key",
                "unauthorized",
                "forbidden",
                "401",
                "403",
            ]
        ):
            return "auth"

        # API相关错误（可重试）
        elif any(
            keyword in error_lower
            for keyword in [
                "api",
                "rate limit",
                "quota",
                "429",
            ]
        ):
            return "api"

        # 交易所相关错误
        elif any(
            keyword in error_lower
            for keyword in [
                "insufficient",
                "balance",
                "funds",
                "margin",
                "position",
                "order",
            ]
        ):
            return "exchange"

        # 数据验证错误
        elif any(
            keyword in error_lower
            for keyword in [
                "invalid",
                "validation",
                "format",
                "parse",
                "schema",
            ]
        ):
            return "validation"

        # 系统错误
        elif any(
            keyword in error_lower
            for keyword in ["internal", "server", "database", "500", "system"]
        ):
            return "system"

        else:
            return "unknown"

    def _assess_severity(self) -> str:
        """评估错误严重性"""
        error_lower = self.error_message.lower()

        # 关键错误
        if any(
            keyword in error_lower
            for keyword in [
                "critical",
                "fatal",
                "crash",
                "corruption",
                "security",
            ]
        ):
            return "critical"

        # 高严重性错误
        elif any(
            keyword in error_lower
            for keyword in [
                "insufficient funds",
                "unauthorized",
                "forbidden",
                "invalid credentials",
            ]
        ):
            return "high"

        # 中等严重性错误
        elif any(
            keyword in error_lower
            for keyword in ["timeout", "rate limit", "temporary", "busy"]
        ):
            return "medium"

        # 低严重性错误
        else:
            return "low"

    def _is_retryable(self, category: str, severity: str) -> bool:
        """判断错误是否可重试"""
        # 关键错误不重试
        if severity == "critical":
            return False

        # 检查重试次数限制
        max_retries = getattr(settings.llm, "max_retries", 3)
        if self.retry_count >= max_retries:
            return False

        # 根据错误类别判断
        retryable_categories = {
            "network": True,
            "api": True,  # API错误可能是临时的
            "auth": False,  # 认证错误需要修正API密钥
            "exchange": False,  # 交易所错误通常需要人工干预
            "validation": False,  # 验证错误需要修正输入
            "system": True,  # 系统错误可能是临时的
            "unknown": False,  # 未知错误谨慎处理
        }

        return retryable_categories.get(category, False)

    async def _generate_self_correction(
        self, error_category: str
    ) -> Optional[Dict[str, Any]]:
        """生成自我修正建议"""
        corrections = {
            "network": {
                "description": "网络重连和重试",
                "actions": ["增加重试延迟", "检查网络连接", "切换备用端点"],
            },
            "api": {
                "description": "API调用优化",
                "actions": ["降低请求频率", "检查API密钥", "使用备用API"],
            },
            "validation": {
                "description": "数据格式修正",
                "actions": ["重新验证输入", "标准化数据格式", "使用默认值"],
            },
            "system": {
                "description": "系统状态检查",
                "actions": ["检查系统资源", "重启相关服务", "清理临时数据"],
            },
        }

        return corrections.get(error_category)

    async def _llm_analysis(self) -> Optional[Any]:
        """
        使用可配置的LLM服务进行深度错误分析

        该方法使用新的LLM服务架构：
        1. 从数据库获取用户的默认LLM配置
        2. 支持多个LLM提供商的错误分析
        3. 自动处理不同提供商的响应格式
        4. 在LLM配置不可用时优雅降级

        Returns:
            ErrorAnalysis对象或None（如果分析失败）
        """
        try:
            from ..core.schemas import ErrorAnalysis
            from ..core.models import LLMConfig
            from sqlalchemy import select, and_

            # 构建上下文信息
            context = f"""
            错误信息: {self.error_message}
            重试次数: {self.retry_count}
            执行计划: {len(self.state.execution_plan)} 个交易计划
            """

            # 获取用户的默认LLM配置
            llm_stmt = select(LLMConfig).where(
                and_(
                    LLMConfig.user_id == self.state.user_id,
                    LLMConfig.enabled == True,
                    LLMConfig.is_default == True
                )
            )
            llm_result = await self.db.execute(llm_stmt)
            llm_config = llm_result.scalar_one_or_none()

            if not llm_config:
                logger.warning("No LLM configuration found for error analysis")
                return None

            # 使用LLM服务进行错误分析
            from ..services.llm_service import LLMService

            response = await LLMService.chat_completion(
                llm_config,
                messages=[
                    {"role": "system", "content": ERROR_ANALYSIS_PROMPT},
                    {
                        "role": "user",
                        "content": f"Context: {context}\n\nAnalyze this error and provide correction suggestions.",
                    },
                ],
                temperature=0.1
            )

            # 解析响应内容
            import json

            try:
                # 根据不同的LLM提供商提取响应内容
                if llm_config.provider == "deepseek":
                    content = response.get("choices", [{}])[0].get("message", {}).get("content", "")
                elif llm_config.provider == "gemini":
                    candidates = response.get("candidates", [])
                    if candidates and candidates[0].get("content", {}).get("parts"):
                        content = candidates[0]["content"]["parts"][0].get("text", "")
                    else:
                        content = ""
                elif llm_config.provider == "claude":
                    content_list = response.get("content", [])
                    if content_list and content_list[0].get("text"):
                        content = content_list[0]["text"]
                    else:
                        content = ""
                else:  # OpenAI/ChatGPT
                    content = response.get("choices", [{}])[0].get("message", {}).get("content", "")

                parsed_data = json.loads(content)
                analysis = ErrorAnalysis(**parsed_data)
            except (json.JSONDecodeError, TypeError, ValueError) as e:
                logger.warning(
                    "Failed to parse LLM error analysis response",
                    error=str(e),
                    content=content[:200] if 'content' in locals() else "No content",
                )
                analysis = None

            return analysis

        except Exception as e:
            logger.warning("LLM error analysis failed", error=str(e))
            return None


async def _apply_self_correction(state: AgentState, correction: Dict[str, Any]):
    """应用自我修正建议"""
    actions = correction.get("actions", [])

    for action in actions:
        if action == "增加重试延迟":
            # 增加延迟时间
            await asyncio.sleep(min(2**state.retry_count, 10))  # 指数退避，最大10秒
            state.log.append("应用自我修正: 增加重试延迟")

        elif action == "重新验证输入":
            # 重新验证解析的意图
            for intent in state.parsed_intents:
                if not _validate_parsed_intent(intent):
                    intent.confidence = max(0, intent.confidence - Decimal("0.1"))
            state.log.append("应用自我修正: 重新验证输入数据")

        elif action == "使用默认值":
            # 为缺失的字段使用默认值
            for plan in state.execution_plan:
                if not plan.quantity and state.context.get("risk_config"):
                    default_size = state.context["risk_config"].get(
                        "default_position_size_usd", 100
                    )
                    plan.quantity = Decimal(str(default_size))
            state.log.append("应用自我修正: 使用默认值填充缺失字段")


# 节点I的路由函数
def route_after_error_analysis(state: AgentState) -> str:
    """
    根据错误分析结果决定下一步路由
    """
    # 首先检查重试次数限制，防止无限循环
    max_retries = getattr(settings.llm, "max_retries", 3)
    if state.retry_count >= max_retries:
        state.log.append(f"已达最大重试次数限制 ({max_retries})，停止重试")
        return "Failure"

    # 检查错误分析次数，防止在错误分析和执行之间无限循环
    error_analysis_count = sum(1 for log in state.log if "错误分析完成" in log)
    if error_analysis_count >= max_retries:
        state.log.append(f"错误分析次数已达限制 ({max_retries})，停止重试")
        return "Failure"

    # 检查是否应该重试
    should_retry = False

    # 检查日志中的重试指示
    for log_entry in state.log:
        if "将进行第" in log_entry and "次重试" in log_entry:
            should_retry = True
            break
        elif "无法重试" in log_entry or "已达最大重试次数" in log_entry:
            should_retry = False
            break

    # 如果没有明确的重试指示，默认不重试以避免无限循环
    if not should_retry:
        state.log.append("错误分析未建议重试，停止执行")
        return "Failure"

    return "Execute" if should_retry else "Failure"


# 节点J: 请求用户确认
@with_error_handling(node_name="request_user_confirmation", timeout_seconds=15.0)
async def request_user_confirmation(state: AgentState, db: AsyncSession) -> AgentState:
    """
    创建待处理动作，并暂停工作流等待用户确认
    """
    logger.info("Requesting user confirmation", task_id=str(state.task_id))

    # 找到需要用户确认的第一个意图
    ambiguous_intent = None
    for intent in state.parsed_intents:
        if intent.intent_type == IntentType.AMBIGUOUS or (
            intent.intent_type in [IntentType.CREATE_ORDER, IntentType.CLOSE_ORDER]
            and intent.side is None
        ):
            ambiguous_intent = intent
            break

    if not ambiguous_intent:
        state.log.append("没有需要确认的意图，但进入了确认节点，这是一个逻辑错误")
        return state

    # 创建待处理动作
    try:
        # 准备详情数据
        details = {
            "raw_input": state.raw_input,
            "clarification_needed": ambiguous_intent.clarification_needed
            or f"无法确定'{ambiguous_intent.raw_text}'的交易方向，请选择买入或卖出",
        }

        # 如果是CREATE_ORDER，可以提供建议的执行计划
        if (
            ambiguous_intent.intent_type == IntentType.CREATE_ORDER
            and ambiguous_intent.symbol
        ):
            # 获取风控配置的默认订单大小
            default_size = Decimal(str(state.context.get("risk_config", {}).get(
                "default_position_size_usd", 100.0
            )))

            # 计算买入和卖出的两种可能方案
            market_price = Decimal(str(state.context.get("market_prices", {}).get(
                ambiguous_intent.symbol, 0
            )))
            if market_price > Decimal("0"):
                quantity = default_size / market_price

                details["proposed_plan"] = [
                    TradePlan(
                        symbol=ambiguous_intent.symbol,
                        side=TradeSide.BUY,
                        quantity=quantity,
                        order_type=OrderType.MARKET,
                    ).dict(),
                    TradePlan(
                        symbol=ambiguous_intent.symbol,
                        side=TradeSide.SELL,
                        quantity=quantity,
                        order_type=OrderType.MARKET,
                    ).dict(),
                ]

        # 创建待处理动作记录
        action_id = uuid.uuid4()
        expires_at = datetime.utcnow() + timedelta(minutes=15)  # 15分钟后过期

        pending_action = PendingAction(
            id=action_id,
            task_id=str(state.task_id),
            user_id=state.user_id,
            action_type="USER_CONFIRMATION",
            details=details,
            status="PENDING",
            expires_at=expires_at,
        )

        db.add(pending_action)
        await db.commit()

        # 保存动作ID到状态
        state.pending_action_id = action_id

        # 序列化检查点
        checkpoint = {
            "task_id": str(state.task_id),
            "user_id": state.user_id,
            "raw_input": state.raw_input,
            "parsed_intents": [intent.model_dump() for intent in state.parsed_intents],
            "context": state.context,
            "execution_plan": [plan.model_dump() for plan in state.execution_plan],
            "error_message": state.error_message,
            "retry_count": state.retry_count,
            "pending_action_id": str(action_id),
            "log": state.log,
        }

        # 使用检查点管理器保存检查点
        from .checkpoint import CheckpointManager

        checkpoint_manager = CheckpointManager(db)
        await checkpoint_manager.save_checkpoint(
            state, "UserConfirm", force=True  # 用户确认是关键节点，强制保存
        )

        state.log.append(f"已创建待处理动作，ID: {action_id}，等待用户确认")

        # 在这里，状态图会暂停执行，直到用户响应

    except Exception as e:
        logger.error(
            "Failed to create pending action",
            task_id=str(state.task_id),
            error=str(e),
        )
        state.log.append(f"创建待处理动作失败: {str(e)}")

    return state


# 节点J的路由函数
def route_after_user_confirmation(state: AgentState) -> str:
    """
    根据用户的确认结果决定下一步路由
    """
    # 如果用户拒绝，则直接结束任务
    if state.user_response and not state.user_response.get("approved", False):
        return "Failure"

    # 如果用户批准，则继续流程
    if state.user_response and state.user_response.get("approved", False):
        # 如果用户提供了修改后的交易方向
        if "side" in state.user_response:
            # 更新意图中的方向
            for intent in state.parsed_intents:
                if (
                    intent.intent_type
                    in [IntentType.CREATE_ORDER, IntentType.CLOSE_ORDER]
                    and intent.side is None
                ):
                    intent.side = TradeSide(state.user_response["side"])

        return "Context"

    # 默认情况（不应该发生）
    return "Failure"
