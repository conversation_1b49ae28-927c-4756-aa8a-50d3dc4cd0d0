"""
Agent检查点管理系统 - 实现状态持久化和恢复机制
"""
import json
import uuid
from datetime import datetime, timedelta, timezone
from typing import Any, Dict, List, Optional

import structlog
from sqlalchemy import and_, delete, select
from sqlalchemy.ext.asyncio import AsyncSession

from ..core.config import settings
from ..core.models import AgentCheckpoint
from ..core.schemas import AgentState, ParsedIntent, TradePlan

logger = structlog.get_logger()


class CheckpointManager:
    """检查点管理器 - 负责Agent状态的持久化和恢复"""

    def __init__(self, db: AsyncSession):
        self.db = db

    async def save_checkpoint(
        self, state: AgentState, node_name: str, force: bool = False
    ) -> str:
        """
        保存Agent状态检查点

        Args:
            state: Agent状态
            node_name: 当前节点名称
            force: 是否强制保存（忽略保存策略）

        Returns:
            str: 检查点ID
        """
        try:
            # 调试日志：检查点保存开始
            logger.info(
                "调试：检查点保存开始",
                task_id=str(state.task_id),
                node_name=node_name,
                force=force
            )

            # 检查是否需要保存检查点
            should_save = self._should_save_checkpoint(node_name)
            logger.info(
                "调试：检查点保存策略",
                node_name=node_name,
                should_save=should_save,
                force=force
            )

            if not force and not should_save:
                logger.debug("Skipping checkpoint save", node_name=node_name)
                return None

            # 序列化状态数据
            state_data = self._serialize_state(state)

            # 创建检查点记录
            checkpoint = AgentCheckpoint(
                task_id=state.task_id,
                user_id=state.user_id,
                node_name=node_name,
                state_data=state_data,
                created_at=datetime.now(timezone.utc).replace(
                    tzinfo=None
                ),  # 修复：移除时区信息以匹配数据库
            )

            self.db.add(checkpoint)
            await self.db.commit()

            logger.info(
                "Checkpoint saved",
                task_id=str(state.task_id),
                node_name=node_name,
                checkpoint_id=str(checkpoint.id),
            )

            # 清理旧检查点
            await self._cleanup_old_checkpoints(state.task_id, state.user_id)

            return str(checkpoint.id)

        except Exception as e:
            logger.error(
                "Failed to save checkpoint",
                task_id=str(state.task_id),
                node_name=node_name,
                error=str(e),
            )
            await self.db.rollback()
            raise

    async def load_checkpoint(
        self, task_id: uuid.UUID, user_id: int
    ) -> Optional[Dict[str, Any]]:
        """
        加载最新的检查点

        Args:
            task_id: 任务ID
            user_id: 用户ID

        Returns:
            Dict[str, Any]: 检查点数据，包含state和node_name
        """
        try:
            # 查询最新检查点
            query = (
                select(AgentCheckpoint)
                .where(
                    and_(
                        AgentCheckpoint.task_id == task_id,
                        AgentCheckpoint.user_id == user_id,
                    )
                )
                .order_by(AgentCheckpoint.created_at.desc())
                .limit(1)
            )

            result = await self.db.execute(query)
            checkpoint = result.scalar_one_or_none()

            if not checkpoint:
                logger.warning(
                    "No checkpoint found",
                    task_id=str(task_id),
                    user_id=user_id,
                )
                return None

            # 反序列化状态数据
            state = self._deserialize_state(checkpoint.state_data)

            logger.info(
                "Checkpoint loaded",
                task_id=str(task_id),
                node_name=checkpoint.node_name,
                checkpoint_id=str(checkpoint.id),
            )

            return {
                "state": state,
                "node_name": checkpoint.node_name,
                "checkpoint_id": str(checkpoint.id),
                "created_at": checkpoint.created_at,
            }

        except Exception as e:
            logger.error(
                "Failed to load checkpoint",
                task_id=str(task_id),
                user_id=user_id,
                error=str(e),
            )
            return None

    async def list_checkpoints(
        self, task_id: uuid.UUID, user_id: int, limit: int = 10
    ) -> List[Dict[str, Any]]:
        """
        列出任务的所有检查点

        Args:
            task_id: 任务ID
            user_id: 用户ID
            limit: 返回数量限制

        Returns:
            List[Dict[str, Any]]: 检查点列表
        """
        try:
            query = (
                select(AgentCheckpoint)
                .where(
                    and_(
                        AgentCheckpoint.task_id == task_id,
                        AgentCheckpoint.user_id == user_id,
                    )
                )
                .order_by(AgentCheckpoint.created_at.desc())
                .limit(limit)
            )

            result = await self.db.execute(query)
            checkpoints = result.scalars().all()

            return [
                {
                    "id": str(cp.id),
                    "node_name": cp.node_name,
                    "created_at": cp.created_at,
                    "has_state_data": bool(cp.state_data),
                }
                for cp in checkpoints
            ]

        except Exception as e:
            logger.error(
                "Failed to list checkpoints",
                task_id=str(task_id),
                user_id=user_id,
                error=str(e),
            )
            return []

    async def delete_checkpoints(self, task_id: uuid.UUID, user_id: int) -> int:
        """
        删除任务的所有检查点

        Args:
            task_id: 任务ID
            user_id: 用户ID

        Returns:
            int: 删除的检查点数量
        """
        try:
            query = delete(AgentCheckpoint).where(
                and_(
                    AgentCheckpoint.task_id == task_id,
                    AgentCheckpoint.user_id == user_id,
                )
            )

            result = await self.db.execute(query)
            await self.db.commit()

            deleted_count = result.rowcount
            logger.info(
                "Checkpoints deleted",
                task_id=str(task_id),
                user_id=user_id,
                count=deleted_count,
            )

            return deleted_count

        except Exception as e:
            logger.error(
                "Failed to delete checkpoints",
                task_id=str(task_id),
                user_id=user_id,
                error=str(e),
            )
            await self.db.rollback()
            return 0

    def _should_save_checkpoint(self, node_name: str) -> bool:
        """
        判断是否应该保存检查点

        Args:
            node_name: 节点名称

        Returns:
            bool: 是否应该保存
        """
        # 关键决策点必须保存检查点
        critical_nodes = {
            "Parse",  # 解析完成
            "UserConfirm",  # 用户确认
            "Risk",  # 风险评估
            "Execute",  # 执行完成
            "Success",  # 成功结束
            "Failure",  # 失败结束
            "Completed",  # 完成
        }

        return node_name in critical_nodes

    def _serialize_state(self, state: AgentState) -> Dict[str, Any]:
        """
        序列化Agent状态为JSON格式

        Args:
            state: Agent状态

        Returns:
            Dict[str, Any]: 序列化后的状态数据
        """
        # 调试日志：检查state.signal_id
        logger.info(
            "调试：序列化状态",
            task_id=str(state.task_id),
            has_signal_id=hasattr(state, 'signal_id'),
            signal_id_value=getattr(state, 'signal_id', None),
            signal_id_type=type(getattr(state, 'signal_id', None)).__name__
        )

        # 深度序列化，确保所有嵌套对象都能正确序列化
        def serialize_value(value):
            """递归序列化值，处理UUID、Decimal等特殊类型"""
            from decimal import Decimal

            if isinstance(value, uuid.UUID):
                return str(value)
            elif isinstance(value, Decimal):
                return float(value)
            elif isinstance(value, datetime):
                return value.isoformat()
            elif hasattr(value, "value"):  # 枚举类型
                return value.value
            elif hasattr(value, "model_dump"):  # Pydantic模型
                return serialize_value(value.model_dump())
            elif isinstance(value, dict):
                # 处理字典，确保键也被序列化
                serialized_dict = {}
                for k, v in value.items():
                    # 序列化键
                    if hasattr(k, "value"):  # 枚举类型作为键
                        key = k.value
                    elif isinstance(k, uuid.UUID):
                        key = str(k)
                    else:
                        key = k
                    serialized_dict[key] = serialize_value(v)
                return serialized_dict
            elif isinstance(value, list):
                return [serialize_value(item) for item in value]
            elif hasattr(value, "__dict__"):  # 其他对象
                return serialize_value(value.__dict__)
            else:
                return value

        return {
            "task_id": str(state.task_id),
            "user_id": str(state.user_id),  # 修复：将UUID转换为字符串
            "signal_id": str(state.signal_id) if state.signal_id else None,  # 添加signal_id序列化
            "raw_input": state.raw_input,
            "parsed_intents": [
                serialize_value(intent) for intent in state.parsed_intents
            ],
            "context": serialize_value(state.context),
            "execution_plan": [serialize_value(plan) for plan in state.execution_plan],
            "error_message": state.error_message,
            "retry_count": state.retry_count,
            "pending_action_id": str(state.pending_action_id)
            if state.pending_action_id
            else None,
            "user_response": serialize_value(state.user_response),
            "final_result": serialize_value(state.final_result),
            "log": state.log,
            "errors": serialize_value(state.errors),
            "current_node": state.current_node,
            "status": state.status,
            "risk_assessment": serialize_value(state.risk_assessment),
            "execution_results": serialize_value(state.execution_results),
            "created_at": datetime.now(timezone.utc)
            .replace(tzinfo=None)
            .isoformat(),  # 修复：移除时区信息以匹配数据库
        }

    def _deserialize_state(self, state_data: Dict[str, Any]) -> AgentState:
        """
        从JSON数据反序列化Agent状态

        Args:
            state_data: 序列化的状态数据

        Returns:
            AgentState: 反序列化后的状态
        """
        # 重建ParsedIntent对象
        parsed_intents = []
        for intent_data in state_data.get("parsed_intents", []):
            parsed_intents.append(ParsedIntent(**intent_data))

        # 重建TradePlan对象
        execution_plan = []
        for plan_data in state_data.get("execution_plan", []):
            execution_plan.append(TradePlan(**plan_data))

        # 重建AgentState对象
        return AgentState(
            task_id=uuid.UUID(state_data["task_id"]),
            user_id=uuid.UUID(state_data["user_id"])
            if isinstance(state_data["user_id"], str)
            else state_data["user_id"],  # 修复：处理字符串形式的UUID
            signal_id=uuid.UUID(state_data["signal_id"])
            if state_data.get("signal_id")
            else None,  # 添加signal_id反序列化
            raw_input=state_data["raw_input"],
            parsed_intents=parsed_intents,
            context=state_data.get("context", {}),
            execution_plan=execution_plan,
            error_message=state_data.get("error_message"),
            retry_count=state_data.get("retry_count", 0),
            pending_action_id=uuid.UUID(state_data["pending_action_id"])
            if state_data.get("pending_action_id")
            else None,
            user_response=state_data.get("user_response"),
            final_result=state_data.get("final_result"),
            log=state_data.get("log", []),
            errors=state_data.get("errors", []),
            current_node=state_data.get("current_node"),
            status=state_data.get("status", "initialized"),
            risk_assessment=state_data.get("risk_assessment"),
            execution_results=state_data.get("execution_results", []),
        )

    async def _cleanup_old_checkpoints(self, task_id: uuid.UUID, user_id: int):
        """
        清理旧的检查点，保留最近的几个

        Args:
            task_id: 任务ID
            user_id: 用户ID
        """
        try:
            # 保留最近的5个检查点
            keep_count = 5

            # 查询需要删除的检查点
            subquery = (
                select(AgentCheckpoint.id)
                .where(
                    and_(
                        AgentCheckpoint.task_id == task_id,
                        AgentCheckpoint.user_id == user_id,
                    )
                )
                .order_by(AgentCheckpoint.created_at.desc())
                .offset(keep_count)
            )

            # 删除旧检查点
            delete_query = delete(AgentCheckpoint).where(
                AgentCheckpoint.id.in_(subquery)
            )

            result = await self.db.execute(delete_query)

            if result.rowcount > 0:
                logger.debug(
                    "Old checkpoints cleaned up",
                    task_id=str(task_id),
                    deleted_count=result.rowcount,
                )

        except Exception as e:
            logger.warning(
                "Failed to cleanup old checkpoints",
                task_id=str(task_id),
                error=str(e),
            )
