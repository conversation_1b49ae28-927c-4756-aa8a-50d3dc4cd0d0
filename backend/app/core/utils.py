"""
核心工具函数
"""
import hashlib
import uuid
from typing import Any, Dict, Optional
from uuid import UUID

import structlog
from fastapi import HTTPException, status

logger = structlog.get_logger()


def generate_content_hash(content: str) -> str:
    """
    生成内容的哈希值

    Args:
        content: 要哈希的内容

    Returns:
        str: 哈希值
    """
    return hashlib.sha256(content.encode()).hexdigest()


def safe_dict_get(data: Dict[str, Any], key: str, default: Any = None) -> Any:
    """
    安全地从字典中获取值

    Args:
        data: 字典
        key: 键
        default: 默认值

    Returns:
        Any: 值或默认值
    """
    try:
        return data.get(key, default)
    except (AttributeError, TypeError):
        return default


def format_decimal(value: Any, precision: int = 8) -> Optional[str]:
    """
    格式化小数

    Args:
        value: 要格式化的值
        precision: 精度

    Returns:
        Optional[str]: 格式化后的字符串或None
    """
    if value is None:
        return None

    try:
        from decimal import Decimal

        if isinstance(value, str):
            decimal_value = Decimal(value)
        elif isinstance(value, (int, float)):
            decimal_value = Decimal(str(value))
        elif isinstance(value, Decimal):
            decimal_value = value
        else:
            return None

        # 格式化为指定精度
        return f"{decimal_value:.{precision}f}".rstrip("0").rstrip(".")

    except Exception:
        return None


def validate_uuid(uuid_str: str, field_name: str = "ID") -> UUID:
    """
    验证并转换UUID字符串
    
    Args:
        uuid_str: UUID字符串
        field_name: 字段名称，用于错误消息
        
    Returns:
        UUID: 验证后的UUID对象
        
    Raises:
        HTTPException: 当UUID格式无效时
    """
    try:
        return UUID(uuid_str)
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=f"Invalid {field_name} format"
        )


def safe_str_to_uuid(uuid_str: Optional[str]) -> Optional[UUID]:
    """
    安全地将字符串转换为UUID，不抛出异常

    Args:
        uuid_str: UUID字符串

    Returns:
        Optional[UUID]: UUID对象或None
    """
    if not uuid_str:
        return None
    try:
        return UUID(uuid_str)
    except (ValueError, TypeError):
        return None


def safe_rollback_db(db, operation_name: str = "operation"):
    """
    安全地回滚数据库事务，避免MissingGreenlet错误

    Args:
        db: 数据库会话
        operation_name: 操作名称，用于日志
    """
    try:
        import asyncio
        # 检查是否在异步上下文中
        try:
            asyncio.get_running_loop()
            # 在异步上下文中，使用异步回滚
            return asyncio.create_task(db.rollback())
        except RuntimeError:
            # 不在异步上下文中，记录警告
            logger.warning(f"Cannot rollback database in non-async context for {operation_name}")
            return None
    except Exception as e:
        # 忽略回滚失败，因为可能是会话已经过期
        logger.warning(f"Database rollback failed for {operation_name}", error=str(e))
        return None


async def safe_get_user_resource(db, model_class, resource_id: UUID, user_id: UUID, resource_name: str = "resource"):
    """
    安全地获取用户拥有的资源

    Args:
        db: 数据库会话
        model_class: 模型类
        resource_id: 资源ID
        user_id: 用户ID
        resource_name: 资源名称，用于错误消息

    Returns:
        资源对象

    Raises:
        HTTPException: 当资源不存在或用户无权限时
    """
    from sqlalchemy import select

    query = select(model_class).where(
        model_class.id == resource_id,
        model_class.user_id == user_id
    )

    result = await db.execute(query)
    resource = result.scalar_one_or_none()

    if not resource:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"{resource_name.capitalize()} not found"
        )

    return resource
