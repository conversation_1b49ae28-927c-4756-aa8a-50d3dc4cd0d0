"""
JWT认证和授权系统
"""
from datetime import datetime, timedelta, timezone
from typing import Any, Dict, Optional

import bcrypt
import jwt
import structlog
from fastapi import Depends, HTTPException, status
from fastapi.security import HTTPAuthorizationCredentials, HTTPBearer
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from .config import settings
from .database import get_db
from .models import User

logger = structlog.get_logger()

# JWT配置
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30
REFRESH_TOKEN_EXPIRE_DAYS = 7

# HTTP Bearer认证
security = HTTPBearer()


class AuthManager:
    """认证管理器"""

    @staticmethod
    def hash_password(password: str) -> str:
        """哈希密码"""
        salt = bcrypt.gensalt()
        return bcrypt.hashpw(password.encode("utf-8"), salt).decode("utf-8")

    @staticmethod
    def verify_password(password: str, password_hash: str) -> bool:
        """验证密码"""
        return bcrypt.checkpw(password.encode("utf-8"), password_hash.encode("utf-8"))

    @staticmethod
    def create_access_token(
        data: Dict[str, Any], expires_delta: Optional[timedelta] = None
    ) -> str:
        """创建访问令牌"""
        to_encode = data.copy()

        if expires_delta:
            expire = datetime.now(timezone.utc) + expires_delta
        else:
            expire = datetime.now(timezone.utc) + timedelta(
                minutes=ACCESS_TOKEN_EXPIRE_MINUTES
            )

        to_encode.update({"exp": expire, "type": "access"})

        return jwt.encode(to_encode, settings.jwt.secret_key, algorithm=ALGORITHM)

    @staticmethod
    def create_refresh_token(data: Dict[str, Any]) -> str:
        """创建刷新令牌"""
        to_encode = data.copy()
        expire = datetime.now(timezone.utc) + timedelta(days=REFRESH_TOKEN_EXPIRE_DAYS)
        to_encode.update({"exp": expire, "type": "refresh"})

        return jwt.encode(to_encode, settings.jwt.secret_key, algorithm=ALGORITHM)

    @staticmethod
    def verify_token(token: str, token_type: str = "access") -> Dict[str, Any]:
        """验证令牌"""
        try:
            payload = jwt.decode(token, settings.jwt.secret_key, algorithms=[ALGORITHM])

            # 检查令牌类型
            if payload.get("type") != token_type:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail=f"Invalid token type. Expected {token_type}",
                    headers={"WWW-Authenticate": "Bearer"},
                )

            # 检查过期时间
            exp = payload.get("exp")
            if exp is None:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Token missing expiration",
                    headers={"WWW-Authenticate": "Bearer"},
                )

            if datetime.fromtimestamp(exp, tz=timezone.utc) < datetime.now(
                timezone.utc
            ):
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Token expired",
                    headers={"WWW-Authenticate": "Bearer"},
                )

            return payload

        except jwt.InvalidTokenError as e:
            logger.warning("Invalid JWT token", error=str(e))
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Could not validate credentials",
                headers={"WWW-Authenticate": "Bearer"},
            )

    @staticmethod
    async def authenticate_user(
        username: str, password: str, db: AsyncSession
    ) -> Optional[User]:
        """认证用户"""
        try:
            # 查询用户
            query = select(User).where(User.username == username)
            result = await db.execute(query)
            user = result.scalar_one_or_none()

            if not user:
                logger.warning("User not found", username=username)
                return None

            # 验证密码
            if not AuthManager.verify_password(password, user.password_hash):
                logger.warning("Invalid password", username=username)
                return None

            logger.info(
                "User authenticated successfully",
                username=username,
                user_id=user.id,
            )
            return user

        except Exception as e:
            logger.error("Authentication error", username=username, error=str(e))
            return None


# 依赖注入函数
async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: AsyncSession = Depends(get_db),
) -> User:
    """获取当前用户（依赖注入）"""

    # 验证令牌
    payload = AuthManager.verify_token(credentials.credentials, "access")

    # 获取用户ID
    user_id = payload.get("sub")
    if user_id is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Token missing user ID",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # 查询用户
    try:
        # 将user_id转换为UUID
        from uuid import UUID

        user_uuid = UUID(user_id)
        query = select(User).where(User.id == user_uuid)
        result = await db.execute(query)
        user = result.scalar_one_or_none()

        if user is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="User not found",
                headers={"WWW-Authenticate": "Bearer"},
            )

        return user

    except ValueError as e:
        logger.error("Invalid user ID in token", user_id=user_id, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid user ID in token",
            headers={"WWW-Authenticate": "Bearer"},
        )
    except HTTPException:
        # 重新抛出HTTPException，不要被通用异常处理器捕获
        raise
    except Exception as e:
        logger.error("Error getting current user", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


async def get_current_user_optional(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security),
    db: AsyncSession = Depends(get_db),
) -> Optional[User]:
    """获取当前用户（可选，用于可选认证的端点）"""
    if not credentials:
        return None

    try:
        return await get_current_user(credentials, db)
    except HTTPException:
        return None


# 权限检查装饰器
def require_permissions(*permissions: str):
    """权限检查装饰器（预留，当前系统暂不实现复杂权限）"""

    def decorator(func):
        async def wrapper(*args, **kwargs):
            # 当前版本暂时跳过权限检查
            # 未来可以在这里实现基于角色的访问控制(RBAC)
            return await func(*args, **kwargs)

        return wrapper

    return decorator


# 令牌刷新
async def refresh_access_token(refresh_token: str, db: AsyncSession) -> Dict[str, str]:
    """刷新访问令牌"""

    # 验证刷新令牌
    payload = AuthManager.verify_token(refresh_token, "refresh")

    # 获取用户ID
    user_id = payload.get("sub")
    if user_id is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid refresh token",
        )

    # 验证用户是否存在
    try:
        # 将user_id转换为UUID
        from uuid import UUID

        user_uuid = UUID(user_id)
        query = select(User).where(User.id == user_uuid)
        result = await db.execute(query)
        user = result.scalar_one_or_none()

        if user is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="User not found",
            )

        # 创建新的访问令牌
        access_token = AuthManager.create_access_token(
            data={"sub": str(user.id), "username": user.username}
        )

        return {"access_token": access_token, "token_type": "bearer"}

    except ValueError as e:
        logger.error("Invalid user ID in refresh token", user_id=user_id, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid user ID in refresh token",
        )
    except HTTPException:
        # 重新抛出HTTPException，不要被通用异常处理器捕获
        raise
    except Exception as e:
        logger.error("Error refreshing token", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )
