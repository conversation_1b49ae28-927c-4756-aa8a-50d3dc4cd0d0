"""日志配置模块

提供统一的日志配置和初始化功能，支持文件存储和轮转机制。
"""

import logging
import logging.handlers
import os
import sys
from pathlib import Path
from typing import Optional

import structlog
from structlog.typing import FilteringBoundLogger


def setup_logging(
    log_level: str = "INFO",
    environment: str = "development",
    log_to_file: bool = True,
    log_file_path: str = "logs",
    log_file_max_size: int = 10,  # MB
    log_file_backup_count: int = 5,
    log_retention_days: int = 7,
) -> FilteringBoundLogger:
    """配置结构化日志系统

    Args:
        log_level: 日志级别 (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        environment: 环境类型 (development, production)
        log_to_file: 是否启用文件日志
        log_file_path: 日志文件目录路径
        log_file_max_size: 单个日志文件最大大小(MB)
        log_file_backup_count: 备份文件数量
        log_retention_days: 日志保留天数

    Returns:
        配置好的structlog logger实例
    """

    # 确保日志目录存在
    if log_to_file:
        log_dir = Path(log_file_path)
        log_dir.mkdir(parents=True, exist_ok=True)

    # 配置标准库logging
    root_logger = logging.getLogger()
    root_logger.handlers.clear()  # 清除现有处理器

    # 设置日志级别
    numeric_level = getattr(logging, log_level.upper(), logging.INFO)
    root_logger.setLevel(numeric_level)

    # 创建格式化器
    formatter = logging.Formatter(
        fmt="%(message)s",
        datefmt="%Y-%m-%d %H:%M:%S"
    )

    # 控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(numeric_level)
    console_handler.setFormatter(formatter)
    root_logger.addHandler(console_handler)

    # 文件处理器
    if log_to_file:
        # 主日志文件 (所有级别)
        app_log_file = log_dir / "app.log"
        file_handler = logging.handlers.RotatingFileHandler(
            filename=str(app_log_file),
            maxBytes=log_file_max_size * 1024 * 1024,  # 转换为字节
            backupCount=log_file_backup_count,
            encoding='utf-8'
        )
        file_handler.setLevel(numeric_level)
        file_handler.setFormatter(formatter)
        root_logger.addHandler(file_handler)

        # 错误日志文件 (ERROR及以上级别)
        error_log_file = log_dir / "error.log"
        error_handler = logging.handlers.RotatingFileHandler(
            filename=str(error_log_file),
            maxBytes=log_file_max_size * 1024 * 1024,
            backupCount=log_file_backup_count,
            encoding='utf-8'
        )
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(formatter)
        root_logger.addHandler(error_handler)

    # 配置structlog处理器链
    processors = [
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
    ]

    # 根据环境选择渲染器
    if environment == "development":
        # 开发环境使用彩色输出
        processors.append(structlog.dev.ConsoleRenderer())
    else:
        # 生产环境使用JSON格式
        processors.append(structlog.processors.JSONRenderer())

    # 配置structlog
    structlog.configure(
        processors=processors,
        wrapper_class=structlog.stdlib.BoundLogger,
        logger_factory=structlog.stdlib.LoggerFactory(),
        cache_logger_on_first_use=True,
    )

    # 创建并返回logger实例
    logger = structlog.get_logger()

    # 记录日志系统初始化信息
    logger.info(
        "日志系统初始化完成",
        log_level=log_level,
        environment=environment,
        log_to_file=log_to_file,
        log_file_path=str(log_file_path) if log_to_file else None,
        file_max_size_mb=log_file_max_size if log_to_file else None,
        backup_count=log_file_backup_count if log_to_file else None,
    )

    return logger


def cleanup_old_logs(log_file_path: str, retention_days: int) -> None:
    """清理过期的日志文件

    Args:
        log_file_path: 日志文件目录路径
        retention_days: 保留天数
    """
    try:
        import time
        from pathlib import Path

        log_dir = Path(log_file_path)
        if not log_dir.exists():
            return

        current_time = time.time()
        cutoff_time = current_time - (retention_days * 24 * 60 * 60)

        # 查找并删除过期的日志文件
        for log_file in log_dir.glob("*.log*"):
            if log_file.stat().st_mtime < cutoff_time:
                try:
                    log_file.unlink()
                    print(f"已删除过期日志文件: {log_file}")
                except OSError as e:
                    print(f"删除日志文件失败 {log_file}: {e}")

    except Exception as e:
        print(f"清理日志文件时发生错误: {e}")


def get_logger(name: Optional[str] = None) -> FilteringBoundLogger:
    """获取logger实例

    Args:
        name: logger名称，如果为None则使用调用模块名

    Returns:
        structlog logger实例
    """
    if name is None:
        # 自动获取调用模块名
        import inspect
        frame = inspect.currentframe()
        if frame and frame.f_back:
            name = frame.f_back.f_globals.get('__name__', 'unknown')

    return structlog.get_logger(name)


# 便捷函数，用于向后兼容
def configure_logging_from_settings(settings) -> FilteringBoundLogger:
    """从设置对象配置日志系统

    Args:
        settings: 应用设置对象

    Returns:
        配置好的logger实例
    """
    # 根据环境调整保留天数
    retention_days = settings.log_retention_days
    if settings.environment == "production":
        retention_days = max(retention_days, 30)  # 生产环境至少保留30天

    logger = setup_logging(
        log_level=settings.log_level.value,
        environment=settings.environment,
        log_to_file=settings.log_to_file,
        log_file_path=settings.log_file_path,
        log_file_max_size=settings.log_file_max_size,
        log_file_backup_count=settings.log_file_backup_count,
        log_retention_days=retention_days,
    )

    # 启动时清理过期日志
    if settings.log_to_file:
        cleanup_old_logs(settings.log_file_path, retention_days)

    return logger