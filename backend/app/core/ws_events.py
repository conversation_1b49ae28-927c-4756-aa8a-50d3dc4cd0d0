"""
WebSocket事件系统 - 统一的事件发布和订阅机制
"""
import asyncio
import json
from datetime import datetime, timezone
from enum import Enum
from typing import Any, Callable, Dict, List, Optional, Set

import structlog

from .ws_manager import MessagePriority, ws_manager
from .ws_schemas import (
    AgentStateTransitionEvent,
    ConditionalOrderTriggerEvent,
    MarketDataUpdateEvent,
    NotificationEvent,
    OrderUpdateEvent,
    PendingActionRequiredEvent,
    PerformanceMetricsEvent,
    RiskAlertEvent,
    SystemStatusEvent,
    WebSocketMessage,
)

logger = structlog.get_logger()


class EventType(Enum):
    """事件类型枚举"""

    ORDER_UPDATE = "ORDER_UPDATE"
    AGENT_STATE_TRANSITION = "AGENT_STATE_TRANSITION"
    PENDING_ACTION_REQUIRED = "PENDING_ACTION_REQUIRED"
    NOTIFICATION = "NOTIFICATION"
    MARKET_DATA_UPDATE = "MARKET_DATA_UPDATE"
    RISK_ALERT = "RISK_ALERT"
    SYSTEM_STATUS = "SYSTEM_STATUS"
    CONDITIONAL_ORDER_TRIGGER = "CONDITIONAL_ORDER_TRIGGER"
    PERFORMANCE_METRICS = "PERFORMANCE_METRICS"
    USER_MESSAGE = "USER_MESSAGE"
    SYSTEM_BROADCAST = "SYSTEM_BROADCAST"


class EventBus:
    """事件总线 - 管理事件的发布和订阅"""

    def __init__(self):
        # 事件监听器：event_type -> List[callback]
        self.listeners: Dict[EventType, List[Callable]] = {}
        # 用户订阅：user_id -> Set[EventType]
        self.user_subscriptions: Dict[int, Set[EventType]] = {}
        # 事件统计
        self.event_stats: Dict[EventType, int] = {}

    def subscribe(self, event_type: EventType, callback: Callable):
        """
        订阅事件

        Args:
            event_type: 事件类型
            callback: 回调函数
        """
        if event_type not in self.listeners:
            self.listeners[event_type] = []

        self.listeners[event_type].append(callback)

        logger.debug(
            "Event listener registered",
            event_type=event_type.value,
            callback=callback.__name__,
        )

    def unsubscribe(self, event_type: EventType, callback: Callable):
        """
        取消订阅事件

        Args:
            event_type: 事件类型
            callback: 回调函数
        """
        if event_type in self.listeners:
            try:
                self.listeners[event_type].remove(callback)
                logger.debug(
                    "Event listener unregistered",
                    event_type=event_type.value,
                    callback=callback.__name__,
                )
            except ValueError:
                pass

    async def publish(
        self,
        event_type: EventType,
        data: Any,
        target_user_id: Optional[int] = None,
        priority: MessagePriority = MessagePriority.NORMAL,
    ):
        """
        发布事件

        Args:
            event_type: 事件类型
            data: 事件数据
            target_user_id: 目标用户ID（None表示广播）
            priority: 消息优先级
        """
        # 更新统计
        if event_type not in self.event_stats:
            self.event_stats[event_type] = 0
        self.event_stats[event_type] += 1

        # 调用监听器
        if event_type in self.listeners:
            for callback in self.listeners[event_type]:
                try:
                    if asyncio.iscoroutinefunction(callback):
                        await callback(data)
                    else:
                        callback(data)
                except Exception as e:
                    logger.error(
                        "Event listener error",
                        event_type=event_type.value,
                        callback=callback.__name__,
                        error=str(e),
                    )

        # 发送WebSocket消息
        message = {
            "event_type": event_type.value,
            "payload": data
            if isinstance(data, dict)
            else data.dict()
            if hasattr(data, "dict")
            else str(data),
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }

        if target_user_id:
            # 检查目标用户是否订阅了此事件类型
            if (
                target_user_id in self.user_subscriptions
                and event_type in self.user_subscriptions[target_user_id]
            ):
                await self.ws_manager.send_to_user(target_user_id, message, priority)
        else:
            # 广播给所有订阅了此事件类型的用户
            for user_id, subscriptions in self.user_subscriptions.items():
                if event_type in subscriptions:
                    await self.ws_manager.send_to_user(user_id, message, priority)

        logger.debug(
            "Event published",
            event_type=event_type.value,
            target_user_id=target_user_id,
            priority=priority.name,
        )

    def subscribe_user(self, user_id: int, event_types: List[EventType]):
        """
        用户订阅事件类型

        Args:
            user_id: 用户ID
            event_types: 事件类型列表
        """
        if user_id not in self.user_subscriptions:
            self.user_subscriptions[user_id] = set()

        self.user_subscriptions[user_id].update(event_types)

        logger.info(
            "User subscribed to events",
            user_id=user_id,
            event_types=[et.value for et in event_types],
        )

    def unsubscribe_user(self, user_id: int, event_types: List[EventType]):
        """
        用户取消订阅事件类型

        Args:
            user_id: 用户ID
            event_types: 事件类型列表
        """
        if user_id in self.user_subscriptions:
            self.user_subscriptions[user_id].difference_update(event_types)

            # 如果没有订阅任何事件，删除记录
            if not self.user_subscriptions[user_id]:
                del self.user_subscriptions[user_id]

        logger.info(
            "User unsubscribed from events",
            user_id=user_id,
            event_types=[et.value for et in event_types],
        )

    def get_stats(self) -> Dict[str, Any]:
        """
        获取事件统计信息

        Returns:
            Dict[str, Any]: 统计信息
        """
        return {
            "event_counts": {et.value: count for et, count in self.event_stats.items()},
            "total_events": sum(self.event_stats.values()),
            "active_listeners": {
                et.value: len(listeners) for et, listeners in self.listeners.items()
            },
            "user_subscriptions": len(self.user_subscriptions),
        }


class WebSocketEventManager:
    """WebSocket事件管理器 - 高级事件处理"""

    def __init__(self, event_bus: EventBus):
        self.event_bus = event_bus
        self._setup_default_handlers()

    def _setup_default_handlers(self):
        """设置默认事件处理器"""
        # 订单更新事件处理
        self.event_bus.subscribe(EventType.ORDER_UPDATE, self._handle_order_update)

        # Agent状态转换事件处理
        self.event_bus.subscribe(
            EventType.AGENT_STATE_TRANSITION,
            self._handle_agent_state_transition,
        )

        # 风险警报事件处理
        self.event_bus.subscribe(EventType.RISK_ALERT, self._handle_risk_alert)

        # 系统状态事件处理
        self.event_bus.subscribe(EventType.SYSTEM_STATUS, self._handle_system_status)

    async def _handle_order_update(self, order_data: Dict[str, Any]):
        """处理订单更新事件"""
        logger.info(
            "Order update processed",
            order_id=order_data.get("id"),
            status=order_data.get("status"),
            user_id=order_data.get("user_id"),
        )

    async def _handle_agent_state_transition(self, transition_data: Dict[str, Any]):
        """处理Agent状态转换事件"""
        logger.info(
            "Agent state transition processed",
            from_node=transition_data.get("from_node"),
            to_node=transition_data.get("to_node"),
            task_id=transition_data.get("task_id"),
        )

    async def _handle_risk_alert(self, alert_data: Dict[str, Any]):
        """处理风险警报事件"""
        severity = alert_data.get("severity", "medium")

        # 高严重性警报需要立即通知
        if severity in ["high", "critical"]:
            logger.warning(
                "High severity risk alert",
                alert_type=alert_data.get("alert_type"),
                severity=severity,
                message=alert_data.get("message"),
            )

    async def _handle_system_status(self, status_data: Dict[str, Any]):
        """处理系统状态事件"""
        component = status_data.get("component")
        status = status_data.get("status")

        if status in ["offline", "degraded"]:
            logger.error(
                "System component issue",
                component=component,
                status=status,
                message=status_data.get("message"),
            )

    async def send_order_update(self, order_data: Dict[str, Any], user_id: int):
        """发送订单更新事件"""
        await self.event_bus.publish(
            EventType.ORDER_UPDATE,
            order_data,
            target_user_id=user_id,
            priority=MessagePriority.HIGH,
        )

    async def send_agent_state_transition(
        self, transition_data: Dict[str, Any], user_id: int
    ):
        """发送Agent状态转换事件"""
        await self.event_bus.publish(
            EventType.AGENT_STATE_TRANSITION,
            transition_data,
            target_user_id=user_id,
            priority=MessagePriority.NORMAL,
        )

    async def send_notification(
        self, notification_data: Dict[str, Any], user_id: Optional[int] = None
    ):
        """发送通知事件"""
        priority = (
            MessagePriority.HIGH
            if notification_data.get("level") == "error"
            else MessagePriority.NORMAL
        )

        await self.event_bus.publish(
            EventType.NOTIFICATION,
            notification_data,
            target_user_id=user_id,
            priority=priority,
        )

    async def send_risk_alert(self, alert_data: Dict[str, Any], user_id: int):
        """发送风险警报事件"""
        severity = alert_data.get("severity", "medium")
        priority = (
            MessagePriority.CRITICAL if severity == "critical" else MessagePriority.HIGH
        )

        await self.event_bus.publish(
            EventType.RISK_ALERT,
            alert_data,
            target_user_id=user_id,
            priority=priority,
        )

    async def send_system_broadcast(self, message: str, level: str = "info"):
        """发送系统广播"""
        broadcast_data = {
            "title": "系统通知",
            "message": message,
            "level": level,
            "category": "system",
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }

        priority = (
            MessagePriority.HIGH
            if level in ["warning", "error"]
            else MessagePriority.NORMAL
        )

        await self.event_bus.publish(
            EventType.SYSTEM_BROADCAST, broadcast_data, priority=priority
        )


# 全局实例
event_bus = EventBus()
ws_event_manager = WebSocketEventManager(event_bus)
