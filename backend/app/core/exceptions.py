"""
统一异常处理和错误响应
"""
import traceback
from typing import Any, Dict, Optional

import structlog
from fastapi import HTT<PERSON><PERSON>xception, Request, status
from fastapi.exceptions import RequestValidationError
from fastapi.responses import J<PERSON>NResponse
from starlette.exceptions import HTTPException as StarletteHTTPException

logger = structlog.get_logger()


# 自定义异常类
class BusinessException(Exception):
    """业务逻辑异常"""

    def __init__(
        self,
        message: str,
        code: str = "BUSINESS_ERROR",
        details: Optional[Dict[str, Any]] = None,
    ):
        self.message = message
        self.code = code
        self.details = details or {}
        super().__init__(self.message)


class ValidationException(Exception):
    """数据验证异常"""

    def __init__(
        self,
        message: str,
        field: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
    ):
        self.message = message
        self.field = field
        self.details = details or {}
        super().__init__(self.message)


class AuthenticationException(Exception):
    """认证异常"""

    def __init__(
        self,
        message: str = "Authentication failed",
        details: Optional[Dict[str, Any]] = None,
    ):
        self.message = message
        self.details = details or {}
        super().__init__(self.message)


class AuthorizationException(Exception):
    """授权异常"""

    def __init__(
        self,
        message: str = "Access denied",
        details: Optional[Dict[str, Any]] = None,
    ):
        self.message = message
        self.details = details or {}
        super().__init__(self.message)


class ResourceNotFoundException(Exception):
    """资源未找到异常"""

    def __init__(self, resource: str, identifier: Optional[str] = None):
        self.resource = resource
        self.identifier = identifier
        message = f"{resource} not found"
        if identifier:
            message += f": {identifier}"
        super().__init__(message)


class RateLimitException(Exception):
    """频率限制异常"""

    def __init__(
        self, message: str = "Rate limit exceeded", retry_after: Optional[int] = None
    ):
        self.message = message
        self.retry_after = retry_after
        super().__init__(self.message)


class ExternalServiceException(Exception):
    """外部服务异常"""

    def __init__(self, service: str, message: str, status_code: Optional[int] = None):
        self.service = service
        self.message = message
        self.status_code = status_code
        super().__init__(f"{service}: {message}")


# 错误响应格式化
def format_error_response(
    error_code: str,
    message: str,
    details: Optional[Dict[str, Any]] = None,
    request_id: Optional[str] = None,
) -> Dict[str, Any]:
    """格式化错误响应"""
    response = {
        "success": False,
        "error": {
            "code": error_code,
            "message": message,
        },
    }

    if details:
        response["error"]["details"] = details

    if request_id:
        response["request_id"] = request_id

    return response


# 异常处理器
async def business_exception_handler(
    request: Request, exc: BusinessException
) -> JSONResponse:
    """业务异常处理器"""
    logger.warning(
        "Business exception",
        error_code=exc.code,
        message=exc.message,
        details=exc.details,
        path=request.url.path,
    )

    return JSONResponse(
        status_code=status.HTTP_400_BAD_REQUEST,
        content=format_error_response(
            error_code=exc.code,
            message=exc.message,
            details=exc.details,
            request_id=getattr(request.state, "request_id", None),
        ),
    )


async def validation_exception_handler(
    request: Request, exc: ValidationException
) -> JSONResponse:
    """验证异常处理器"""
    logger.warning(
        "Validation exception",
        message=exc.message,
        field=exc.field,
        details=exc.details,
        path=request.url.path,
    )

    details = exc.details.copy()
    if exc.field:
        details["field"] = exc.field

    return JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content=format_error_response(
            error_code="VALIDATION_ERROR",
            message=exc.message,
            details=details,
            request_id=getattr(request.state, "request_id", None),
        ),
    )


async def authentication_exception_handler(
    request: Request, exc: AuthenticationException
) -> JSONResponse:
    """认证异常处理器"""
    logger.warning(
        "Authentication exception",
        message=exc.message,
        details=exc.details,
        path=request.url.path,
    )

    return JSONResponse(
        status_code=status.HTTP_401_UNAUTHORIZED,
        content=format_error_response(
            error_code="AUTHENTICATION_ERROR",
            message=exc.message,
            details=exc.details,
            request_id=getattr(request.state, "request_id", None),
        ),
        headers={"WWW-Authenticate": "Bearer"},
    )


async def authorization_exception_handler(
    request: Request, exc: AuthorizationException
) -> JSONResponse:
    """授权异常处理器"""
    logger.warning(
        "Authorization exception",
        message=exc.message,
        details=exc.details,
        path=request.url.path,
    )

    return JSONResponse(
        status_code=status.HTTP_403_FORBIDDEN,
        content=format_error_response(
            error_code="AUTHORIZATION_ERROR",
            message=exc.message,
            details=exc.details,
            request_id=getattr(request.state, "request_id", None),
        ),
    )


async def resource_not_found_exception_handler(
    request: Request, exc: ResourceNotFoundException
) -> JSONResponse:
    """资源未找到异常处理器"""
    logger.warning(
        "Resource not found",
        resource=exc.resource,
        identifier=exc.identifier,
        path=request.url.path,
    )

    details = {"resource": exc.resource}
    if exc.identifier:
        details["identifier"] = exc.identifier

    return JSONResponse(
        status_code=status.HTTP_404_NOT_FOUND,
        content=format_error_response(
            error_code="RESOURCE_NOT_FOUND",
            message=str(exc),
            details=details,
            request_id=getattr(request.state, "request_id", None),
        ),
    )


async def rate_limit_exception_handler(
    request: Request, exc: RateLimitException
) -> JSONResponse:
    """频率限制异常处理器"""
    logger.warning(
        "Rate limit exceeded",
        message=exc.message,
        retry_after=exc.retry_after,
        path=request.url.path,
    )

    headers = {}
    if exc.retry_after:
        headers["Retry-After"] = str(exc.retry_after)

    return JSONResponse(
        status_code=status.HTTP_429_TOO_MANY_REQUESTS,
        content=format_error_response(
            error_code="RATE_LIMIT_EXCEEDED",
            message=exc.message,
            details={"retry_after": exc.retry_after} if exc.retry_after else None,
            request_id=getattr(request.state, "request_id", None),
        ),
        headers=headers,
    )


async def external_service_exception_handler(
    request: Request, exc: ExternalServiceException
) -> JSONResponse:
    """外部服务异常处理器"""
    logger.error(
        "External service error",
        service=exc.service,
        message=exc.message,
        status_code=exc.status_code,
        path=request.url.path,
    )

    return JSONResponse(
        status_code=status.HTTP_502_BAD_GATEWAY,
        content=format_error_response(
            error_code="EXTERNAL_SERVICE_ERROR",
            message=f"External service error: {exc.message}",
            details={"service": exc.service, "status_code": exc.status_code},
            request_id=getattr(request.state, "request_id", None),
        ),
    )


async def http_exception_handler(request: Request, exc: HTTPException) -> JSONResponse:
    """HTTP异常处理器"""
    logger.warning(
        "HTTP exception",
        status_code=exc.status_code,
        detail=exc.detail,
        path=request.url.path,
    )

    # 根据状态码确定错误代码
    error_codes = {
        400: "BAD_REQUEST",
        401: "UNAUTHORIZED",
        403: "FORBIDDEN",
        404: "NOT_FOUND",
        405: "METHOD_NOT_ALLOWED",
        422: "UNPROCESSABLE_ENTITY",
        429: "TOO_MANY_REQUESTS",
        500: "INTERNAL_SERVER_ERROR",
        502: "BAD_GATEWAY",
        503: "SERVICE_UNAVAILABLE",
    }

    error_code = error_codes.get(exc.status_code, "HTTP_ERROR")

    return JSONResponse(
        status_code=exc.status_code,
        content=format_error_response(
            error_code=error_code,
            message=exc.detail,
            request_id=getattr(request.state, "request_id", None),
        ),
    )


async def validation_error_handler(
    request: Request, exc: RequestValidationError
) -> JSONResponse:
    """请求验证错误处理器"""
    logger.warning(
        "Request validation error", errors=exc.errors(), path=request.url.path
    )

    # 格式化验证错误
    formatted_errors = []
    for error in exc.errors():
        formatted_errors.append(
            {
                "field": ".".join(str(loc) for loc in error["loc"]),
                "message": error["msg"],
                "type": error["type"],
            }
        )

    return JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content=format_error_response(
            error_code="VALIDATION_ERROR",
            message="Request validation failed",
            details={"errors": formatted_errors},
            request_id=getattr(request.state, "request_id", None),
        ),
    )


async def general_exception_handler(request: Request, exc: Exception) -> JSONResponse:
    """通用异常处理器"""
    logger.error(
        "Unhandled exception",
        error=str(exc),
        traceback=traceback.format_exc(),
        path=request.url.path,
    )

    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content=format_error_response(
            error_code="INTERNAL_SERVER_ERROR",
            message="An internal server error occurred",
            request_id=getattr(request.state, "request_id", None),
        ),
    )
