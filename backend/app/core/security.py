"""  
安全模块 - 用于API密钥的加密解密、用户认证和JWT处理
"""
import base64
import os
from typing import <PERSON>ple
from uuid import UUID

import jwt
import structlog
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from fastapi import Depends, HTTPException, status
from fastapi.security import OAuth2PasswordBearer
from passlib.context import CryptContext
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from .config import get_settings, settings
from .database import get_db
from .models import User

# 配置结构化日志
logger = structlog.get_logger()

# 密码哈希上下文配置
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


def get_encryption_key() -> bytes:
    """
    从配置中的secret_key生成加密密钥

    Returns:
        bytes: 生成的加密密钥
    """
    if not settings.security.secret_key:
        raise ValueError("配置中缺少APP_SECRET_KEY，无法进行加密/解密操作")

    # 使用PBKDF2创建密钥
    kdf = PBKDF2HMAC(
        algorithm=hashes.SHA256(),
        length=32,
        salt=settings.security.salt,
        iterations=100000,
    )
    key = base64.urlsafe_b64encode(kdf.derive(settings.security.secret_key.encode()))
    return key


def encrypt_api_credentials(api_key: str, api_secret: str) -> Tuple[str, str]:
    """
    加密API凭证

    Args:
        api_key: 交易所API Key
        api_secret: 交易所API Secret

    Returns:
        Tuple[str, str]: 加密后的API Key和Secret
    """
    try:
        encryption_key = get_encryption_key()
        fernet = Fernet(encryption_key)

        encrypted_key = fernet.encrypt(api_key.encode()).decode()
        encrypted_secret = fernet.encrypt(api_secret.encode()).decode()

        return encrypted_key, encrypted_secret

    except Exception as e:
        logger.error("加密API凭证失败", error=str(e))
        raise ValueError(f"加密失败: {str(e)}")


def decrypt_api_credentials(
    encrypted_key: str, encrypted_secret: str
) -> Tuple[str, str]:
    """
    解密API凭证

    Args:
        encrypted_key: 加密的API Key
        encrypted_secret: 加密的API Secret

    Returns:
        Tuple[str, str]: 解密后的API Key和Secret
    """
    try:
        encryption_key = get_encryption_key()
        fernet = Fernet(encryption_key)

        api_key = fernet.decrypt(encrypted_key.encode()).decode()
        api_secret = fernet.decrypt(encrypted_secret.encode()).decode()

        return api_key, api_secret

    except Exception as e:
        logger.error("解密API凭证失败", error=str(e))
        raise ValueError(f"解密失败: {str(e)}")


def verify_password(plain_password: str, password_hash: str) -> bool:
    """
    验证密码

    Args:
        plain_password: 明文密码
        password_hash: 哈希密码

    Returns:
        bool: 密码是否匹配
    """
    try:
        return pwd_context.verify(plain_password, password_hash)
    except Exception as e:
        logger.error("密码验证失败", error=str(e))
        return False


def get_password_hash(password: str) -> str:
    """
    生成密码哈希

    Args:
        password: 明文密码

    Returns:
        str: 哈希后的密码
    """
    try:
        return pwd_context.hash(password)
    except Exception as e:
        logger.error("密码哈希生成失败", error=str(e))
        raise ValueError(f"密码哈希失败: {str(e)}")


def encrypt_api_key(api_key: str) -> str:
    """
    加密API Key

    Args:
        api_key: 交易所API Key

    Returns:
        str: 加密后的API Key
    """
    try:
        encryption_key = get_encryption_key()
        fernet = Fernet(encryption_key)
        return fernet.encrypt(api_key.encode()).decode()
    except Exception as e:
        logger.error("加密API Key失败", error=str(e))
        raise ValueError(f"加密失败: {str(e)}")


def encrypt_api_secret(api_secret: str) -> str:
    """
    加密API Secret

    Args:
        api_secret: 交易所API Secret

    Returns:
        str: 加密后的API Secret
    """
    try:
        encryption_key = get_encryption_key()
        fernet = Fernet(encryption_key)
        return fernet.encrypt(api_secret.encode()).decode()
    except Exception as e:
        logger.error("加密API Secret失败", error=str(e))
        raise ValueError(f"加密失败: {str(e)}")


def decrypt_api_key(encrypted_key: str) -> str:
    """
    解密API Key

    Args:
        encrypted_key: 加密的API Key

    Returns:
        str: 解密后的API Key
    """
    try:
        encryption_key = get_encryption_key()
        fernet = Fernet(encryption_key)
        return fernet.decrypt(encrypted_key.encode()).decode()
    except Exception as e:
        logger.error("解密API Key失败", error=str(e))
        raise ValueError(f"解密失败: {str(e)}")


def decrypt_api_secret(encrypted_secret: str) -> str:
    """
    解密API Secret

    Args:
        encrypted_secret: 加密的API Secret

    Returns:
        str: 解密后的API Secret
    """
    try:
        encryption_key = get_encryption_key()
        fernet = Fernet(encryption_key)
        return fernet.decrypt(encrypted_secret.encode()).decode()
    except Exception as e:
        logger.error("解密API Secret失败", error=str(e))
        raise ValueError(f"解密失败: {str(e)}")


def encrypt_sensitive_data(data: str) -> str:
    """
    加密敏感数据（通用函数）

    Args:
        data: 要加密的敏感数据

    Returns:
        str: 加密后的数据
    """
    try:
        encryption_key = get_encryption_key()
        fernet = Fernet(encryption_key)
        return fernet.encrypt(data.encode()).decode()
    except Exception as e:
        logger.error("加密敏感数据失败", error=str(e))
        raise ValueError(f"加密失败: {str(e)}")


def decrypt_sensitive_data(encrypted_data: str) -> str:
    """
    解密敏感数据（通用函数）

    Args:
        encrypted_data: 加密的敏感数据

    Returns:
        str: 解密后的数据
    """
    try:
        encryption_key = get_encryption_key()
        fernet = Fernet(encryption_key)
        return fernet.decrypt(encrypted_data.encode()).decode()
    except Exception as e:
        logger.error("解密敏感数据失败", error=str(e))
        raise ValueError(f"解密失败: {str(e)}")


# OAuth2密码Bearer
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="/api/v1/auth/login")


async def get_current_user(
    token: str = Depends(oauth2_scheme), db: AsyncSession = Depends(get_db)
) -> int:
    """
    获取当前用户ID

    Args:
        token: JWT令牌
        db: 数据库会话

    Returns:
        当前用户ID (UUID)
    """
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )

    try:
        settings_obj = get_settings()
        payload = jwt.decode(
            token,
            settings_obj.jwt_secret_key,
            algorithms=[settings_obj.jwt_algorithm],
        )
        user_id_str = payload.get("sub")

        if user_id_str is None:
            raise credentials_exception

        # 转换为UUID
        from uuid import UUID
        user_id = UUID(user_id_str)
    except (jwt.PyJWTError, ValueError):
        raise credentials_exception

    # 查询用户是否存在
    query = select(User).where(User.id == user_id)
    result = await db.execute(query)
    user = result.scalar_one_or_none()

    if user is None:
        raise credentials_exception

    return user_id


async def get_current_user_obj(
    token: str = Depends(oauth2_scheme), db: AsyncSession = Depends(get_db)
) -> User:
    """
    获取当前用户对象

    Args:
        token: JWT令牌
        db: 数据库会话

    Returns:
        当前用户对象
    """
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )

    try:
        settings_obj = get_settings()
        payload = jwt.decode(
            token,
            settings_obj.jwt_secret_key,
            algorithms=[settings_obj.jwt_algorithm],
        )
        user_id_str = payload.get("sub")

        if user_id_str is None:
            raise credentials_exception

        # 转换为UUID
        user_id = UUID(user_id_str)
    except (jwt.PyJWTError, ValueError):
        raise credentials_exception

    # 查询用户
    query = select(User).where(User.id == user_id)
    result = await db.execute(query)
    user = result.scalar_one_or_none()

    if user is None:
        raise credentials_exception

    return user
