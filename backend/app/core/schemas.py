from typing import (
    Any,
    Dict,
    Generic,
    List,
    Literal,
    Optional,
    TypedDict,
    TypeVar,
    Union,
)

T = TypeVar("T")
import uuid
from datetime import datetime
from decimal import Decimal
from enum import Enum

from pydantic import (
    BaseModel,
    Field,
    field_validator,
    model_serializer,
    model_validator,
)

# --- 数据库表对应模型 ---


class PlatformType(str, Enum):
    """平台类型枚举"""

    DISCORD = "discord"
    TELEGRAM = "telegram"
    MANUAL = "manual"


class MessageType(str, Enum):
    """消息类型枚举"""

    TEXT = "text"
    EMBED = "embed"
    ATTACHMENT = "attachment"
    REPLY = "reply"


class AIParseStatus(str, Enum):
    """AI解析状态枚举"""

    PENDING = "pending"
    SUCCESS = "success"
    FAILED = "failed"
    PARTIAL = "partial"


class MessageTypeAI(str, Enum):
    """AI识别的消息类型枚举"""

    NORMAL_MESSAGE = "normal_message"
    TRADING_SIGNAL = "trading_signal"
    MARKET_ANALYSIS = "market_analysis"
    PRICE_ALERT = "price_alert"
    AMBIGUOUS = "ambiguous"


class LLMService(str, Enum):
    """LLM服务枚举"""

    DEEPSEEK = "deepseek"
    GEMINI = "gemini"
    CHATGPT = "chatgpt"
    CLAUDE = "claude"


class Signal(BaseModel):
    """信号基础模型"""

    id: uuid.UUID
    user_id: uuid.UUID
    platform: PlatformType
    platform_message_id: Optional[str] = None
    channel_id: Optional[str] = None
    channel_name: Optional[str] = None
    author_id: Optional[str] = None
    author_name: Optional[str] = None
    content: str
    raw_content: Optional[str] = None
    message_type: MessageType = MessageType.TEXT
    metadata: Optional[Dict[str, Any]] = None
    signal_strength: Optional[Decimal] = None
    is_processed: bool = False
    processed_at: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class RiskConfig(BaseModel):
    user_id: uuid.UUID  # 修复：改为UUID类型以匹配数据库
    max_concurrent_orders: int = 5
    max_total_position_value_usd: Decimal = Decimal("1000.0")
    default_position_size_usd: Decimal = Decimal("100.0")
    max_position_size_usd: Decimal = Decimal("500.0")
    allowed_symbols: List[str] = ["BTC/USDT", "ETH/USDT"]
    confidence_threshold: Decimal = Decimal("0.8")


class ExchangeConfig(BaseModel):
    id: uuid.UUID
    user_id: uuid.UUID  # 修复：改为UUID类型以匹配数据库
    exchange_name: str
    encrypted_api_key: str
    encrypted_api_secret: str


# --- 基础与工具函数模型 ---


class OrderStatus(str, Enum):
    """订单状态枚举"""

    ACTIVE = "active"
    CLOSED = "closed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class TradeSide(str, Enum):
    """交易方向枚举"""

    BUY = "buy"
    SELL = "sell"


class Order(BaseModel):
    id: uuid.UUID
    user_id: uuid.UUID  # 修复：改为UUID类型以匹配数据库
    symbol: str
    side: TradeSide
    quantity: Decimal
    status: OrderStatus
    entry_price: Optional[Decimal] = None
    pnl: Optional[Decimal] = None
    created_at: datetime


class TradeResult(BaseModel):
    status: Literal["success", "failure"]
    order_id: Optional[str] = None  # 添加缺失的order_id字段
    exchange_order_id: Optional[str] = None
    client_order_id: str
    symbol: Optional[str] = None
    side: Optional[str] = None
    quantity: Optional[Decimal] = None  # 修复：使用Decimal确保精度
    price: Optional[Decimal] = None     # 修复：使用Decimal确保精度
    timestamp: Optional[datetime] = None
    raw_response: Optional[Dict[str, Any]] = None
    fees: Optional[Dict[str, Decimal]] = None  # 修复：使用Decimal确保精度
    slippage: Optional[Decimal] = None         # 修复：使用Decimal确保精度
    error_message: Optional[str] = None
    error_code: Optional[str] = None


# --- AI 核心 (LangGraph State & Intents) ---


class IntentType(str, Enum):
    """意图类型枚举"""

    CREATE_ORDER = "CREATE_ORDER"
    CLOSE_ORDER = "CLOSE_ORDER"
    MODIFY_ORDER = "MODIFY_ORDER"
    QUERY_STATUS = "QUERY_STATUS"
    AMBIGUOUS = "AMBIGUOUS"
    CONDITIONAL_ORDER = "CONDITIONAL_ORDER"


class ParsedIntent(BaseModel):
    """
    Represents a single, structured trading intent parsed from natural language.
    This is a critical data model for the AI core.

    Enhanced to support complex trading signals with multiple price points,
    risk management parameters, and detailed order execution information.
    """

    intent_type: IntentType
    raw_text: str = Field(
        ...,
        description="The original text snippet from the input that led to this intent. Used for logging and debugging.",
    )

    # The 'side' is determined by the LLM in the parsing stage
    side: Optional[TradeSide] = Field(
        None,
        description="The determined trade direction (buy/long or sell/short). Null if cannot be determined.",
    )

    symbol: Optional[str] = Field(None, description="e.g., 'BTC/USDT'")
    quantity_usd: Optional[Decimal] = Field(None, description="The amount in USD.")

    # 价格相关字段 - 支持复杂交易信号
    entry_price: Optional[Decimal] = Field(
        None,
        description="Entry price for the order. For multiple entries, this represents one specific entry point."
    )
    stop_loss_price: Optional[Decimal] = Field(
        None,
        description="Stop loss price to limit downside risk."
    )
    take_profit_price: Optional[Decimal] = Field(
        None,
        description="Take profit price to secure gains."
    )
    quantity_base: Optional[Decimal] = Field(
        None,
        description="Quantity in base currency (e.g., 2.92 ETH, 0.5 BTC)."
    )

    # 订单执行细节
    order_type: Optional[str] = Field(
        None,
        description="Order type: 'market', 'limit', 'stop', 'stop_limit', etc."
    )
    time_in_force: Optional[str] = Field(
        None,
        description="Time in force: 'GTC' (Good Till Cancelled), 'IOC' (Immediate or Cancel), 'FOK' (Fill or Kill)."
    )

    # 风险管理参数
    risk_amount: Optional[Decimal] = Field(
        None,
        description="Risk amount in USD (e.g., 200 from 'risking 200 for 1600')."
    )
    risk_reward_ratio: Optional[Decimal] = Field(
        None,
        description="Risk to reward ratio (e.g., 8.05 from '8.05R' or calculated from risk/reward amounts)."
    )

    # 多入场点支持
    entry_sequence: Optional[int] = Field(
        None,
        description="Entry sequence number for multiple entry strategies (1, 2, 3, etc.)."
    )

    target_criteria: Optional[str] = Field(
        None,
        description="Criteria to select an order, e.g., 'the profitable one', 'the latest ETH order'.",
    )
    confidence: Decimal = Field(
        Decimal("1.0"),
        description="Confidence score of the parsing, from 0.0 to 1.0.",
        ge=0,
        le=1,
    )
    clarification_needed: Optional[str] = Field(
        None,
        description="If intent is AMBIGUOUS or side is null, explain why and ask a clear question.",
    )

    @field_validator("symbol")
    @classmethod
    def symbol_format_validation(cls, v):
        if v and "/" not in v:
            raise ValueError("交易对格式应为 BASE/QUOTE，如 BTC/USDT")
        return v


class OrderType(str, Enum):
    """订单类型枚举"""

    MARKET = "market"
    LIMIT = "limit"


class ActionType(str, Enum):
    """动作类型枚举"""

    USER_CONFIRMATION = "USER_CONFIRMATION"
    RISK_OVERRIDE = "RISK_OVERRIDE"
    MANUAL_INTERVENTION = "MANUAL_INTERVENTION"


class TradePlan(BaseModel):
    plan_id: uuid.UUID = Field(default_factory=uuid.uuid4)
    symbol: str
    side: TradeSide
    order_type: OrderType = OrderType.MARKET
    quantity: Decimal
    price: Optional[Decimal] = None  # For limit orders

    @field_validator("quantity")
    @classmethod
    def quantity_must_be_positive(cls, v):
        if v <= 0:
            raise ValueError("交易数量必须大于0")
        return v


class ErrorAnalysis(BaseModel):
    is_correctable: bool = Field(
        ...,
        description="True if the error is temporary and can be retried, False otherwise.",
    )
    reason: str = Field(
        ..., description="A brief, human-readable explanation of the error."
    )
    suggestion: str = Field(..., description="A suggestion for the system or user.")


# TypedDict版本的AgentState，用于LangGraph
class AgentStateDict(TypedDict):
    # 必需字段
    task_id: uuid.UUID
    user_id: uuid.UUID  # 修复：改为UUID类型以匹配数据库
    signal_id: Optional[uuid.UUID]  # 关联的信号ID
    raw_input: str
    # 可选字段（有默认值）
    parsed_intents: List[ParsedIntent]
    context: Dict[str, Any]
    execution_plan: List[TradePlan]
    error_message: Optional[str]
    retry_count: int
    pending_action_id: Optional[uuid.UUID]
    user_response: Optional[Dict[str, Any]]
    final_result: Optional[Any]
    log: List[str]
    errors: List[Dict[str, Any]]
    current_node: Optional[str]
    status: str
    risk_assessment: Optional[Dict[str, Any]]
    execution_results: List[Dict[str, Any]]


# Pydantic版本的AgentState，用于类型验证和序列化
class AgentState(BaseModel):
    task_id: uuid.UUID = Field(default_factory=uuid.uuid4)
    user_id: uuid.UUID  # 修复：改为UUID类型以匹配数据库
    signal_id: Optional[uuid.UUID] = None  # 关联的信号ID
    raw_input: str
    parsed_intents: List[ParsedIntent] = []
    context: Dict[
        str, Any
    ] = {}  # e.g., {'risk_config': RiskConfig, 'active_orders': List[Order], ...}
    execution_plan: List[TradePlan] = []
    error_message: Optional[str] = None
    retry_count: int = 0
    pending_action_id: Optional[uuid.UUID] = None
    user_response: Optional[Dict[str, Any]] = None  # To inject user's response
    final_result: Optional[Any] = None
    log: List[str] = []
    # 添加错误处理相关字段
    errors: List[Dict[str, Any]] = []  # 存储错误信息列表
    current_node: Optional[str] = None  # 当前执行的节点
    status: str = "initialized"  # 状态: initialized, processing, completed, error, etc.
    risk_assessment: Optional[Dict[str, Any]] = None  # 风险评估结果
    execution_results: List[Dict[str, Any]] = []  # 执行结果列表

    def to_dict(self) -> AgentStateDict:
        """转换为TypedDict格式"""
        data = self.model_dump()
        # 确保所有字段都有值
        return AgentStateDict(
            task_id=data["task_id"],
            user_id=data["user_id"],
            signal_id=data.get("signal_id"),
            raw_input=data["raw_input"],
            parsed_intents=data.get("parsed_intents", []),
            context=data.get("context", {}),
            execution_plan=data.get("execution_plan", []),
            error_message=data.get("error_message"),
            retry_count=data.get("retry_count", 0),
            pending_action_id=data.get("pending_action_id"),
            user_response=data.get("user_response"),
            final_result=data.get("final_result"),
            log=data.get("log", []),
            errors=data.get("errors", []),
            current_node=data.get("current_node"),
            status=data.get("status", "initialized"),
            risk_assessment=data.get("risk_assessment"),
            execution_results=data.get("execution_results", []),
        )

    @classmethod
    def from_dict(cls, data: AgentStateDict) -> "AgentState":
        """从TypedDict创建AgentState"""
        return cls(
            task_id=data["task_id"],
            user_id=data["user_id"],
            signal_id=data.get("signal_id"),
            raw_input=data["raw_input"],
            parsed_intents=data.get("parsed_intents", []),
            context=data.get("context", {}),
            execution_plan=data.get("execution_plan", []),
            error_message=data.get("error_message"),
            retry_count=data.get("retry_count", 0),
            pending_action_id=data.get("pending_action_id"),
            user_response=data.get("user_response"),
            final_result=data.get("final_result"),
            log=data.get("log", []),
            errors=data.get("errors", []),
            current_node=data.get("current_node"),
            status=data.get("status", "initialized"),
            risk_assessment=data.get("risk_assessment"),
            execution_results=data.get("execution_results", []),
        )


# --- API 请求/响应模型 ---


class APIResponse(BaseModel, Generic[T]):
    """通用API响应模型"""

    success: bool = Field(..., description="请求是否成功")
    message: Optional[str] = Field(None, description="响应消息")
    data: Optional[T] = Field(None, description="响应数据")
    error_code: Optional[str] = Field(None, description="错误代码")

    @classmethod
    def success_response(
        cls, data: Optional[T] = None, message: str = "操作成功"
    ) -> "APIResponse[T]":
        """创建成功响应"""
        return cls(success=True, message=message, data=data, error_code=None)

    @classmethod
    def error_response(
        cls, message: str = "操作失败", error_code: Optional[str] = None
    ) -> "APIResponse[Any]":
        """创建错误响应"""
        return cls(success=False, message=message, data=None, error_code=error_code)

    class Config:
        """Pydantic配置"""

        json_encoders = {
            # 确保datetime对象正确序列化
            datetime: lambda v: v.isoformat()
            if v
            else None
        }


class PaginatedResponse(BaseModel, Generic[T]):
    """分页响应模型"""

    items: List[T]
    total: int
    page: int
    size: int
    has_next: bool
    has_prev: bool


class OrderResponse(BaseModel):
    """订单响应模型"""

    id: uuid.UUID
    user_id: uuid.UUID
    client_order_id: str  # 必需字段，数据库已确保非空
    exchange_order_id: Optional[str] = None
    source_message_id: Optional[str] = None
    symbol: str
    side: str
    quantity: Decimal
    entry_price: Optional[Decimal] = None
    close_price: Optional[Decimal] = None
    pnl: Optional[Decimal] = None
    status: str
    agent_log: Optional[Dict[str, Any]] = None
    created_at: datetime
    closed_at: Optional[datetime] = None

    @model_serializer
    def serialize_model(self):
        """自定义序列化器，将Decimal转换为float，统一字段格式"""
        data = {}
        for field_name, field_value in self.__dict__.items():
            if isinstance(field_value, Decimal):
                data[field_name] = (
                    float(field_value) if field_value is not None else None
                )
            elif field_name == "side" and field_value:
                # 统一side字段为大写
                data[field_name] = field_value.upper()
            elif field_name == "status" and field_value:
                # 统一status字段为大写
                data[field_name] = field_value.upper()
            else:
                data[field_name] = field_value

        # 添加order_type字段，从agent_log中提取
        if self.agent_log and isinstance(self.agent_log, dict):
            data["order_type"] = self.agent_log.get("order_type", "MARKET")
        else:
            data["order_type"] = "MARKET"

        # 添加price字段，使用entry_price
        data["price"] = data.get("entry_price")

        return data

    class Config:
        from_attributes = True


class OrderCreate(BaseModel):
    """创建订单请求模型"""

    symbol: str = Field(..., description="交易对，如 BTC/USDT")
    side: str = Field(..., description="买卖方向: BUY 或 SELL")
    quantity: Decimal = Field(..., description="数量", gt=0)
    order_type: str = Field(default="MARKET", description="订单类型: MARKET 或 LIMIT")
    price: Optional[Decimal] = Field(None, description="价格（限价单必填）", gt=0)

    @field_validator("symbol")
    @classmethod
    def validate_symbol(cls, v):
        if v and "/" not in v:
            raise ValueError("交易对格式应为 BASE/QUOTE，如 BTC/USDT")
        return v

    @field_validator("side")
    @classmethod
    def validate_side(cls, v):
        if v.upper() not in ["BUY", "SELL"]:
            raise ValueError("买卖方向必须是 BUY 或 SELL")
        return v.upper()

    @field_validator("order_type")
    @classmethod
    def validate_order_type(cls, v):
        if v.upper() not in ["MARKET", "LIMIT"]:
            raise ValueError("订单类型必须是 MARKET 或 LIMIT")
        return v.upper()


class OrderListResponse(BaseModel):
    """订单列表响应模型"""

    orders: List[OrderResponse]
    total: int
    limit: int
    offset: int


class ExchangeConfigCreate(BaseModel):
    """创建交易所配置请求模型"""

    exchange_name: str = Field(..., description="交易所名称，如 'binance'")
    api_key: str = Field(..., description="API密钥")
    api_secret: str = Field(..., description="API密钥")


class ExchangeConfigUpdate(BaseModel):
    """更新交易所配置请求模型"""

    api_key: Optional[str] = None
    api_secret: Optional[str] = None
    sandbox_mode: Optional[bool] = None
    is_active: Optional[bool] = None


class ExchangeConfigResponse(BaseModel):
    """交易所配置响应模型"""

    id: uuid.UUID
    user_id: int
    exchange_name: str
    sandbox_mode: bool
    is_active: bool
    created_at: datetime
    # 注意：不返回加密的密钥信息

    class Config:
        from_attributes = True


class RiskConfigUpdate(BaseModel):
    """风控配置更新请求模型"""

    max_concurrent_orders: Optional[int] = None
    max_total_position_value_usd: Optional[Decimal] = None
    default_position_size_usd: Optional[Decimal] = None
    max_position_size_usd: Optional[Decimal] = None
    allowed_symbols: Optional[List[str]] = None
    confidence_threshold: Optional[Decimal] = None


class RiskConfigResponse(BaseModel):
    """风控配置响应模型"""

    user_id: int
    max_concurrent_orders: int
    max_total_position_value_usd: Decimal
    default_position_size_usd: Decimal
    max_position_size_usd: Decimal
    allowed_symbols: List[str]
    confidence_threshold: Decimal

    class Config:
        from_attributes = True


# --- Agent相关模型 ---


class ProcessSignalRequest(BaseModel):
    """处理信号请求模型"""

    raw_input: str = Field(..., description="原始输入文本")
    priority: Optional[str] = Field(
        default="normal", description="优先级: low, normal, high"
    )
    context: Optional[Dict[str, Any]] = Field(default_factory=dict, description="额外上下文")


class TaskCreatedResponse(BaseModel):
    """任务创建响应模型"""

    task_id: str
    status: str
    message: str


class AgentTask(BaseModel):
    """Agent任务模型"""

    id: str
    user_id: uuid.UUID  # 修复：改为UUID类型以匹配数据库
    status: str
    raw_input: str
    created_at: datetime
    updated_at: datetime
    result: Optional[Dict[str, Any]] = None


class AgentCheckpoint(BaseModel):
    """Agent检查点模型"""

    task_id: str
    node_name: str
    state_data: Dict[str, Any]
    created_at: datetime


# --- 系统相关模型 ---


class HealthStatus(BaseModel):
    """健康状态模型"""

    status: str = Field(..., description="系统状态: healthy, degraded, unhealthy")
    timestamp: datetime
    checks: Dict[str, Any] = Field(default_factory=dict)


class SystemMetrics(BaseModel):
    """系统指标模型"""

    cpu_usage: float
    memory_usage: float
    disk_usage: float
    active_connections: int
    running_tasks: int
    timestamp: datetime


class VersionInfo(BaseModel):
    """版本信息模型"""

    version: str
    build_date: str
    environment: str
    git_commit: Optional[str] = None


# --- 认证相关模型 ---


class TokenResponse(BaseModel):
    """JWT令牌响应模型"""

    access_token: str
    token_type: str = "bearer"
    expires_in: int  # 秒数


class UserCreate(BaseModel):
    """用户创建请求模型"""

    username: str = Field(..., min_length=3, max_length=50)
    password: str = Field(..., min_length=6)


class UserResponse(BaseModel):
    """用户响应模型"""

    id: uuid.UUID
    username: str
    email: str
    is_active: bool
    is_first_time: bool
    created_at: datetime

    class Config:
        from_attributes = True


class PendingAction(BaseModel):
    """待确认动作模型"""

    id: uuid.UUID = Field(default_factory=uuid.uuid4)
    user_id: uuid.UUID  # 修复：改为UUID类型以匹配数据库
    action_type: str  # 动作类型，如 'trade', 'cancel_order' 等
    action_data: Dict[str, Any]  # 动作的具体数据
    description: str  # 动作描述，用于向用户展示
    status: str = "pending"  # pending, approved, rejected
    created_at: datetime = Field(default_factory=datetime.utcnow)
    responded_at: Optional[datetime] = None

    class Config:
        from_attributes = True


# --- 条件订单相关模型 ---


class ConditionalOrderCreate(BaseModel):
    """创建条件订单请求模型"""

    symbol: str = Field(..., description="交易对，如 BTC/USDT")
    trigger_condition: Dict[str, Any] = Field(..., description="触发条件")
    action_plan: Dict[str, Any] = Field(..., description="执行计划")
    status: str = Field(default="PENDING", description="订单状态")


class ConditionalOrderUpdate(BaseModel):
    """更新条件订单请求模型"""

    trigger_condition: Optional[Dict[str, Any]] = None
    action_plan: Optional[Dict[str, Any]] = None
    status: Optional[str] = None


class ConditionalOrderResponse(BaseModel):
    """条件订单响应模型"""

    id: uuid.UUID
    user_id: int
    symbol: str
    trigger_condition: Dict[str, Any]
    action_plan: Dict[str, Any]
    status: str
    created_at: datetime

    class Config:
        from_attributes = True


# --- 信号相关API模型 ---


class SignalQueryParams(BaseModel):
    """信号查询参数"""

    platform: Optional[str] = None
    channel_id: Optional[str] = None
    is_processed: Optional[bool] = None
    confidence_min: Optional[float] = Field(None, ge=0, le=1, description="最小置信度")
    confidence_max: Optional[float] = Field(None, ge=0, le=1, description="最大置信度")
    ai_parse_status: Optional[AIParseStatus] = Field(None, description="AI解析状态")
    message_type_ai: Optional[MessageTypeAI] = Field(None, description="AI识别的消息类型")
    llm_service: Optional[LLMService] = Field(None, description="LLM服务")
    date_from: Optional[datetime] = None
    date_to: Optional[datetime] = None
    page: int = Field(default=1, ge=1)
    size: int = Field(default=20, ge=1, le=100)
    sort_by: str = Field(default="created_at")
    sort_order: str = Field(default="desc", pattern="^(asc|desc)$")

    # 向后兼容
    signal_strength_min: Optional[float] = Field(
        None, ge=0, le=1, description="已废弃，请使用confidence_min"
    )

    @model_validator(mode="after")
    def validate_confidence_range(self):
        """验证置信度范围"""
        if self.confidence_min is not None and self.confidence_max is not None:
            if self.confidence_min > self.confidence_max:
                raise ValueError("confidence_min不能大于confidence_max")

        # 向后兼容处理
        if self.signal_strength_min is not None and self.confidence_min is None:
            self.confidence_min = self.signal_strength_min

        return self


class CreateSignalRequest(BaseModel):
    """创建信号请求"""

    platform: str = Field(..., pattern="^(discord|telegram|manual)$")
    content: str = Field(..., min_length=1, max_length=4000)
    channel_name: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
    raw_content: Optional[str] = None
    author_name: Optional[str] = None


class UpdateSignalRequest(BaseModel):
    """更新信号请求"""

    is_processed: Optional[bool] = None
    confidence: Optional[float] = Field(None, ge=0, le=1, description="AI解析置信度")
    ai_parse_status: Optional[AIParseStatus] = Field(None, description="AI解析状态")
    message_type_ai: Optional[MessageTypeAI] = Field(None, description="AI识别的消息类型")
    llm_service: Optional[LLMService] = Field(None, description="LLM服务")
    metadata: Optional[Dict[str, Any]] = None
    ai_analysis: Optional[Dict[str, Any]] = Field(None, description="AI分析结果")

    # 向后兼容
    signal_strength: Optional[float] = Field(
        None, ge=0, le=1, description="已废弃，请使用confidence"
    )

    @model_validator(mode="after")
    def handle_backward_compatibility(self):
        """处理向后兼容"""
        if self.signal_strength is not None and self.confidence is None:
            self.confidence = self.signal_strength
        return self


class SignalResponse(BaseModel):
    """信号响应模型"""

    id: str
    platform: str
    channel_name: Optional[str]
    author_name: Optional[str]
    content: str
    message_type: str
    confidence: Optional[float] = Field(None, description="AI解析置信度")
    ai_parse_status: AIParseStatus = Field(description="AI解析状态")
    message_type_ai: MessageTypeAI = Field(description="AI识别的消息类型")
    llm_service: Optional[LLMService] = Field(None, description="使用的LLM服务")
    is_processed: bool
    created_at: datetime

    # 向后兼容字段
    signal_strength: Optional[float] = Field(None, description="已废弃，请使用confidence")

    @model_validator(mode="after")
    def ensure_backward_compatibility(self):
        """确保向后兼容"""
        if self.confidence is not None:
            self.signal_strength = self.confidence
        return self

    @model_serializer
    def serialize_model(self):
        """自定义序列化器"""
        data = {}
        for field_name, field_value in self.__dict__.items():
            if isinstance(field_value, Decimal):
                data[field_name] = (
                    float(field_value) if field_value is not None else None
                )
            elif isinstance(field_value, uuid.UUID):
                data[field_name] = str(field_value)
            else:
                data[field_name] = field_value
        return data

    class Config:
        from_attributes = True


class SignalDetailResponse(SignalResponse):
    """信号详情响应模型"""

    raw_content: Optional[str]
    metadata: Optional[Dict[str, Any]]
    processed_at: Optional[datetime]
    platform_message_id: Optional[str]
    channel_id: Optional[str]
    author_id: Optional[str]

    @property
    def ai_analysis(self) -> Optional[Dict[str, Any]]:
        """获取AI分析结果"""
        if self.metadata and "ai_analysis" in self.metadata:
            return self.metadata["ai_analysis"]
        return None


class SignalStatsResponse(BaseModel):
    """信号统计响应模型"""

    total_signals: int
    processed_signals: int
    platform_breakdown: Dict[str, int]
    avg_confidence: Optional[float] = Field(None, description="平均置信度")
    ai_parse_status_breakdown: Dict[str, int] = Field(
        default_factory=dict, description="AI解析状态分布"
    )
    message_type_breakdown: Dict[str, int] = Field(
        default_factory=dict, description="消息类型分布"
    )
    llm_service_breakdown: Dict[str, int] = Field(
        default_factory=dict, description="LLM服务分布"
    )
    recent_activity: List[Dict[str, Any]]

    # 向后兼容字段
    avg_signal_strength: Optional[float] = Field(
        None, description="已废弃，请使用avg_confidence"
    )

    @model_validator(mode="after")
    def ensure_backward_compatibility(self):
        """确保向后兼容"""
        if self.avg_confidence is not None:
            self.avg_signal_strength = self.avg_confidence
        return self


# --- 待处理动作相关模型 ---


class PendingActionUpdate(BaseModel):
    """更新待处理动作请求模型"""

    status: Optional[str] = None
    response: Optional[Dict[str, Any]] = None


class PendingActionResponse(BaseModel):
    """待处理动作响应模型"""

    id: uuid.UUID
    task_id: str
    user_id: uuid.UUID  # 修复：改为UUID类型以匹配数据库
    details: Dict[str, Any]
    status: str
    response: Optional[Dict[str, Any]] = None
    created_at: datetime
    expires_at: datetime
    resolved_at: Optional[datetime] = None

    class Config:
        from_attributes = True


# --- Discord配置相关模型 ---


class DiscordConfigRequest(BaseModel):
    """Discord配置请求模型"""
    source_name: str = Field(..., min_length=1, max_length=100, description="配置名称")
    enabled: bool = Field(default=False, description="是否启用")
    token: str = Field(..., min_length=1, description="Discord Bot Token")
    server_ids: List[str] = Field(default=[], description="服务器ID列表")
    channel_ids: List[str] = Field(default=[], description="频道ID列表")
    author_ids: List[str] = Field(default=[], description="作者ID列表")
    allowed_message_types: List[str] = Field(default=["text"], description="允许的消息类型")

    @field_validator("server_ids", "channel_ids", "author_ids")
    @classmethod
    def validate_id_lists(cls, v):
        """验证ID列表格式"""
        if not isinstance(v, list):
            raise ValueError("必须是列表格式")
        for item in v:
            if not isinstance(item, str) or not item.strip():
                raise ValueError("ID不能为空")
        return [item.strip() for item in v if item.strip()]

    @field_validator("allowed_message_types")
    @classmethod
    def validate_message_types(cls, v):
        """验证消息类型"""
        valid_types = {"text", "embed", "attachment", "reply"}
        if not v:
            return ["text"]
        for msg_type in v:
            if msg_type not in valid_types:
                raise ValueError(f"无效的消息类型: {msg_type}")
        return v


class DiscordConfigUpdateRequest(BaseModel):
    """Discord配置更新请求模型"""
    source_name: str = Field(..., min_length=1, max_length=100, description="配置名称")
    enabled: bool = Field(default=False, description="是否启用")
    token: Optional[str] = Field(None, min_length=1, description="Discord Bot Token（可选，不提供则保持原有token）")
    server_ids: List[str] = Field(default=[], description="服务器ID列表")
    channel_ids: List[str] = Field(default=[], description="频道ID列表")
    author_ids: List[str] = Field(default=[], description="作者ID列表")
    allowed_message_types: List[str] = Field(default=["text"], description="允许的消息类型")

    @field_validator("server_ids", "channel_ids", "author_ids")
    @classmethod
    def validate_id_lists(cls, v):
        """验证ID列表格式"""
        if not isinstance(v, list):
            raise ValueError("必须是列表格式")
        for item in v:
            if not isinstance(item, str) or not item.strip():
                raise ValueError("ID不能为空")
        return [item.strip() for item in v if item.strip()]

    @field_validator("allowed_message_types")
    @classmethod
    def validate_message_types(cls, v):
        """验证消息类型"""
        valid_types = {"text", "embed", "attachment", "reply"}
        if not v:
            return ["text"]
        for msg_type in v:
            if msg_type not in valid_types:
                raise ValueError(f"无效的消息类型: {msg_type}")
        return v


class DiscordConfigResponse(BaseModel):
    """Discord配置响应模型"""
    id: uuid.UUID
    user_id: uuid.UUID
    source_name: str
    enabled: bool
    has_token: bool  # 不返回实际token
    server_ids: List[str]
    channel_ids: List[str]
    author_ids: List[str]
    allowed_message_types: List[str]
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

    @classmethod
    def from_orm_with_token_flag(cls, obj):
        """从ORM对象创建响应，设置token标志"""
        data = {
            "id": obj.id,
            "user_id": obj.user_id,
            "source_name": obj.source_name,
            "enabled": obj.enabled,
            "has_token": bool(obj.encrypted_token),
            "server_ids": obj.server_ids,
            "channel_ids": obj.channel_ids,
            "author_ids": obj.author_ids,
            "allowed_message_types": obj.allowed_message_types,
            "created_at": obj.created_at,
            "updated_at": obj.updated_at,
        }
        return cls(**data)


# --- LLM配置相关模型 ---


class LLMProvider(str, Enum):
    """LLM服务提供商枚举"""
    DEEPSEEK = "deepseek"
    GEMINI = "gemini"
    CHATGPT = "chatgpt"
    CLAUDE = "claude"


class LLMConfigRequest(BaseModel):
    """创建LLM配置请求模型"""

    config_name: str = Field(..., min_length=1, max_length=100, description="配置名称")
    provider: LLMProvider = Field(..., description="LLM服务提供商")
    enabled: bool = Field(default=False, description="是否启用")
    is_default: bool = Field(default=False, description="是否为默认配置")
    api_key: str = Field(..., min_length=1, description="API密钥")
    api_base_url: Optional[str] = Field(None, max_length=255, description="自定义API基础URL")
    model_name: str = Field(..., min_length=1, max_length=100, description="模型名称")
    max_tokens: int = Field(default=4096, ge=1, le=32768, description="最大token数")
    temperature: float = Field(default=0.7, ge=0.0, le=2.0, description="温度参数")
    timeout_seconds: int = Field(default=60, ge=1, le=300, description="请求超时时间（秒）")
    max_retries: int = Field(default=3, ge=0, le=10, description="最大重试次数")


class LLMConfigUpdateRequest(BaseModel):
    """更新LLM配置请求模型"""

    config_name: str = Field(..., min_length=1, max_length=100, description="配置名称")
    provider: LLMProvider = Field(..., description="LLM服务提供商")
    enabled: bool = Field(default=False, description="是否启用")
    is_default: bool = Field(default=False, description="是否为默认配置")
    api_key: Optional[str] = Field(None, description="API密钥（可选，不提供则保持原有密钥）")
    api_base_url: Optional[str] = Field(None, max_length=255, description="自定义API基础URL")
    model_name: str = Field(..., min_length=1, max_length=100, description="模型名称")
    max_tokens: int = Field(default=4096, ge=1, le=32768, description="最大token数")
    temperature: float = Field(default=0.7, ge=0.0, le=2.0, description="温度参数")
    timeout_seconds: int = Field(default=60, ge=1, le=300, description="请求超时时间（秒）")
    max_retries: int = Field(default=3, ge=0, le=10, description="最大重试次数")


class LLMConfigResponse(BaseModel):
    """LLM配置响应模型"""

    id: str = Field(..., description="配置ID")
    config_name: str = Field(..., description="配置名称")
    provider: str = Field(..., description="LLM服务提供商")
    enabled: bool = Field(..., description="是否启用")
    is_default: bool = Field(..., description="是否为默认配置")
    api_base_url: Optional[str] = Field(None, description="自定义API基础URL")
    model_name: str = Field(..., description="模型名称")
    max_tokens: int = Field(..., description="最大token数")
    temperature: float = Field(..., description="温度参数")
    timeout_seconds: int = Field(..., description="请求超时时间（秒）")
    max_retries: int = Field(..., description="最大重试次数")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")

    # 安全字段：不返回完整的API密钥
    api_key_masked: str = Field(..., description="脱敏后的API密钥")

    class Config:
        from_attributes = True

    @classmethod
    def from_orm_with_masked_key(cls, obj):
        """从ORM对象创建响应模型，并对API密钥进行脱敏处理"""
        # 对API密钥进行脱敏处理
        api_key = obj.api_key
        if len(api_key) > 8:
            masked_key = api_key[:4] + "***" + api_key[-4:]
        else:
            masked_key = "***"

        data = {
            "id": str(obj.id),
            "config_name": obj.config_name,
            "provider": obj.provider,
            "enabled": obj.enabled,
            "is_default": obj.is_default,
            "api_base_url": obj.api_base_url,
            "model_name": obj.model_name,
            "max_tokens": obj.max_tokens,
            "temperature": float(obj.temperature),
            "timeout_seconds": obj.timeout_seconds,
            "max_retries": obj.max_retries,
            "created_at": obj.created_at,
            "updated_at": obj.updated_at,
            "api_key_masked": masked_key,
        }
        return cls(**data)


class LLMConfigTestRequest(BaseModel):
    """测试LLM配置请求模型"""

    # 移除test_message字段，使用统一的默认测试消息
    pass


class LLMConfigTestResponse(BaseModel):
    """测试LLM配置响应模型"""

    success: bool = Field(..., description="测试是否成功")
    response_text: Optional[str] = Field(None, description="LLM响应文本")
    error_message: Optional[str] = Field(None, description="错误信息")
    response_time_ms: Optional[int] = Field(None, description="响应时间（毫秒）")


# ==================== Agent执行追踪相关模型 ====================

class AgentExecutionTraceResponse(BaseModel):
    """Agent执行追踪响应模型"""

    id: uuid.UUID = Field(..., description="追踪记录ID")
    task_id: uuid.UUID = Field(..., description="任务ID")
    user_id: uuid.UUID = Field(..., description="用户ID")
    signal_id: Optional[uuid.UUID] = Field(None, description="信号ID")
    node_name: str = Field(..., description="节点名称")
    execution_order: int = Field(..., description="执行顺序")

    # 状态字段
    status: str = Field(..., description="执行状态")
    started_at: datetime = Field(..., description="开始时间")
    completed_at: Optional[datetime] = Field(None, description="完成时间")
    duration_ms: Optional[int] = Field(None, description="执行时长（毫秒）")

    # 数据字段
    input_data: Optional[Dict[str, Any]] = Field(None, description="输入数据")
    output_data: Optional[Dict[str, Any]] = Field(None, description="输出数据")
    error_data: Optional[Dict[str, Any]] = Field(None, description="错误数据")
    performance_metrics: Optional[Dict[str, Any]] = Field(None, description="性能指标")
    metadata: Optional[Dict[str, Any]] = Field(None, description="元数据")

    # 时间戳
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")

    class Config:
        from_attributes = True


class TaskExecutionTracesResponse(BaseModel):
    """任务执行追踪响应模型"""

    task_id: uuid.UUID = Field(..., description="任务ID")
    user_id: uuid.UUID = Field(..., description="用户ID")
    signal_id: Optional[uuid.UUID] = Field(None, description="信号ID")
    total_duration_ms: Optional[int] = Field(None, description="总执行时长（毫秒）")
    status: str = Field(..., description="整体状态")
    traces: List[AgentExecutionTraceResponse] = Field(..., description="执行追踪列表")


class RecentExecutionSummary(BaseModel):
    """最近执行摘要模型"""

    task_id: uuid.UUID = Field(..., description="任务ID")
    signal_id: Optional[uuid.UUID] = Field(None, description="信号ID")
    started_at: datetime = Field(..., description="开始时间")
    completed_at: Optional[datetime] = Field(None, description="完成时间")
    total_duration_ms: Optional[int] = Field(None, description="总执行时长（毫秒）")
    status: str = Field(..., description="整体状态")
    nodes_count: int = Field(..., description="节点总数")
    failed_nodes: int = Field(..., description="失败节点数")


class RecentExecutionsResponse(BaseModel):
    """最近执行记录响应模型"""

    traces: List[RecentExecutionSummary] = Field(..., description="执行摘要列表")
    total: int = Field(..., description="总记录数")
    page: int = Field(..., description="当前页码")
    limit: int = Field(..., description="每页限制")
