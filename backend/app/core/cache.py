"""缓存服务

提供统一的缓存接口，用于缓存市场数据、用户配置等频繁访问的数据。
使用内存缓存实现，简化系统依赖。
"""

import time
from decimal import Decimal
from typing import Any, Dict, Optional

import structlog

# 配置结构化日志
logger = structlog.get_logger()


class CacheService:
    """缓存服务类 - 使用内存缓存实现"""

    def __init__(self):
        self._memory_cache: Dict[str, Any] = {}  # 内存缓存
        self._memory_cache_ttl: Dict[str, float] = {}  # TTL记录
        logger.info("内存缓存服务已初始化")

    def _is_memory_cache_expired(self, key: str) -> bool:
        """检查内存缓存是否过期"""
        if key not in self._memory_cache_ttl:
            return True
        return time.time() > self._memory_cache_ttl[key]

    def _clean_expired_memory_cache(self):
        """清理过期的内存缓存"""
        current_time = time.time()
        expired_keys = [
            key
            for key, expire_time in self._memory_cache_ttl.items()
            if current_time > expire_time
        ]
        for key in expired_keys:
            self._memory_cache.pop(key, None)
            self._memory_cache_ttl.pop(key, None)

    def is_connected(self) -> bool:
        """检查缓存连接状态"""
        return True  # 内存缓存总是可用

    async def get(self, key: str, default: Any = None) -> Any:
        """获取缓存值"""
        self._clean_expired_memory_cache()
        if key in self._memory_cache and not self._is_memory_cache_expired(key):
            return self._memory_cache[key]
        return default

    async def set(
        self,
        key: str,
        value: Any,
        ttl: Optional[int] = None,
        serialize_method: str = "memory",
    ) -> bool:
        """设置缓存值"""
        self._memory_cache[key] = value
        if ttl:
            self._memory_cache_ttl[key] = time.time() + ttl
        else:
            # 无TTL时设置为1小时后过期，避免内存泄漏
            self._memory_cache_ttl[key] = time.time() + 3600
        return True

    async def delete(self, key: str) -> bool:
        """删除缓存"""
        deleted = False
        if key in self._memory_cache:
            del self._memory_cache[key]
            deleted = True
        if key in self._memory_cache_ttl:
            del self._memory_cache_ttl[key]
        return deleted

    async def exists(self, key: str) -> bool:
        """检查缓存是否存在"""
        self._clean_expired_memory_cache()
        return key in self._memory_cache and not self._is_memory_cache_expired(key)

    async def get_market_price(self, symbol: str) -> Optional[Decimal]:
        """获取缓存的市场价格"""
        cache_key = f"market_price:{symbol}"
        cached_price = await self.get(cache_key)

        if cached_price:
            try:
                return Decimal(str(cached_price))
            except:
                return None

        return None

    async def set_market_price(
        self, symbol: str, price: Decimal, ttl: int = 30
    ) -> bool:
        """设置市场价格缓存"""
        cache_key = f"market_price:{symbol}"
        return await self.set(cache_key, str(price), ttl, "json")

    async def get_user_config(self, user_id: int, config_type: str) -> Optional[Dict]:
        """获取用户配置缓存"""
        cache_key = f"user_config:{user_id}:{config_type}"
        return await self.get(cache_key)

    async def set_user_config(
        self, user_id: int, config_type: str, config: Dict, ttl: int = 300
    ) -> bool:
        """设置用户配置缓存"""
        cache_key = f"user_config:{user_id}:{config_type}"
        return await self.set(cache_key, config, ttl)

    async def get_exchange_status(self, exchange_name: str) -> Optional[Dict]:
        """获取交易所状态缓存"""
        cache_key = f"exchange_status:{exchange_name}"
        return await self.get(cache_key)

    async def set_exchange_status(
        self, exchange_name: str, status: Dict, ttl: int = 60
    ) -> bool:
        """设置交易所状态缓存"""
        cache_key = f"exchange_status:{exchange_name}"
        return await self.set(cache_key, status, ttl)

    async def invalidate_pattern(self, pattern: str) -> int:
        """删除匹配模式的所有缓存"""
        # 简单的模式匹配实现
        deleted_count = 0
        keys_to_delete = []

        # 将Redis模式转换为Python模式
        if pattern.endswith("*"):
            prefix = pattern[:-1]
            keys_to_delete = [
                key for key in self._memory_cache.keys() if key.startswith(prefix)
            ]
        elif pattern.startswith("*"):
            suffix = pattern[1:]
            keys_to_delete = [
                key for key in self._memory_cache.keys() if key.endswith(suffix)
            ]
        else:
            # 精确匹配
            if pattern in self._memory_cache:
                keys_to_delete = [pattern]

        for key in keys_to_delete:
            if await self.delete(key):
                deleted_count += 1

        return deleted_count

    async def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        self._clean_expired_memory_cache()
        return {
            "connected": True,
            "cache_type": "memory",
            "total_keys": len(self._memory_cache),
            "memory_usage": "N/A",  # 内存使用情况难以精确计算
        }

    def close(self):
        """清理缓存"""
        self._memory_cache.clear()
        self._memory_cache_ttl.clear()
        logger.info("内存缓存已清理")


# 全局缓存服务实例
cache_service = CacheService()


# 便捷函数
async def get_cached_market_price(symbol: str) -> Optional[Decimal]:
    """获取缓存的市场价格"""
    return await cache_service.get_market_price(symbol)


async def cache_market_price(symbol: str, price: Decimal, ttl: int = 30) -> bool:
    """缓存市场价格"""
    return await cache_service.set_market_price(symbol, price, ttl)


async def get_cached_user_config(user_id: int, config_type: str) -> Optional[Dict]:
    """获取缓存的用户配置"""
    return await cache_service.get_user_config(user_id, config_type)


async def cache_user_config(
    user_id: int, config_type: str, config: Dict, ttl: int = 300
) -> bool:
    """缓存用户配置"""
    return await cache_service.set_user_config(user_id, config_type, config, ttl)


async def invalidate_user_cache(user_id: int) -> int:
    """清除用户相关的所有缓存"""
    pattern = f"user_config:{user_id}:*"
    return await cache_service.invalidate_pattern(pattern)


async def get_cache_health() -> Dict[str, Any]:
    """获取缓存健康状态"""
    return await cache_service.get_cache_stats()
