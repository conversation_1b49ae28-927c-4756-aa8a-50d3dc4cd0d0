# 配置系统使用指南

## 简介

本模块提供了一个集中化的配置管理系统，基于Pydantic Settings实现。它能够从环境变量、.env文件以及默认值中加载配置，并提供类型安全的访问方式。

## 核心特性

- **分组配置**: 将相关配置项组织到逻辑分组中，如数据库配置、安全配置等
- **类型验证**: 所有配置项都有明确的类型定义，提供运行时类型检查
- **缓存机制**: 使用`lru_cache`减少配置加载开销
- **环境变量覆盖**: 可通过环境变量覆盖任何配置项
- **智能解析**: 能够自动将字符串形式的环境变量转换为适当的类型（列表、布尔值等）

## 使用方法

### 导入配置模块

```python
from app.core.config import settings
```

### 访问配置项

配置项按逻辑分组组织，通过点号访问：

```python
# 访问基础配置
app_name = settings.app_name
debug_mode = settings.debug

# 访问分组配置
db_url = settings.db.url
jwt_secret = settings.security.jwt_secret_key
api_host = settings.api.host
```

### 配置分组

当前系统包含以下配置分组：

1. **数据库配置 (db)**
   - `url`: 数据库连接URL
   - `pool_size`: 连接池大小
   - `max_overflow`: 最大溢出连接数
   - `pool_timeout`: 连接池超时
   - `pool_recycle`: 连接回收时间
   - `echo`: 是否输出SQL日志

2. **安全配置 (security)**
   - `secret_key`: 应用主密钥，用于加密
   - `jwt_secret_key`: JWT令牌密钥
   - `jwt_algorithm`: JWT加密算法
   - `jwt_expire_minutes`: JWT过期时间（分钟）
   - `salt`: 加密盐值

3. **API配置 (api)**
   - `host`: API监听主机
   - `port`: API监听端口
   - `prefix`: API前缀
   - `url`: API完整URL
   - `cors_origins`: CORS允许的源

4. **LLM配置 (llm)**
   - `openai_api_key`: OpenAI API密钥
   - `anthropic_api_key`: Anthropic API密钥
   - `default_provider`: 默认LLM提供商
   - `default_model`: 默认模型名称
   - `max_retries`: 最大重试次数
   - `request_timeout`: 请求超时时间（秒）
   - `max_tokens`: 最大生成token数量

5. **Discord配置 (discord)**
   - `token`: Discord Bot令牌
   - `channel_ids`: 监控频道ID列表
   - `monitored_channels`: 监控频道配置JSON

6. **交易配置 (trading)**
   - `default_exchange`: 默认交易所
   - `simulation_mode`: 是否使用模拟模式
   - `price_check_interval`: 价格检查间隔（秒）

## 环境变量示例

以下是一个.env文件的示例，展示了可用的环境变量：

```
# 应用基础配置
APP_NAME=AI Crypto Trading Agent
DEBUG=false
LOG_LEVEL=INFO

# 数据库配置
DATABASE_URL=postgresql+asyncpg://postgres:password@localhost:5432/crypto_trader
DB_POOL_SIZE=10
DB_ECHO=false

# 安全配置
APP_SECRET_KEY=your-very-secret-key-here
JWT_SECRET_KEY=your-jwt-secret-key-here
JWT_EXPIRE_MINUTES=60

# API配置
API_HOST=0.0.0.0
API_PORT=8000
API_PREFIX=/api/v1
API_URL=http://localhost:8000
CORS_ORIGINS=http://localhost:3000,http://localhost:8080

# LLM配置
OPENAI_API_KEY=sk-your-key-here
DEFAULT_LLM_PROVIDER=openai
DEFAULT_LLM_MODEL=gpt-4
LLM_MAX_RETRIES=3

# Discord配置
DISCORD_TOKEN=your-discord-token
DISCORD_CHANNEL_IDS=channel1_id,channel2_id
MONITORED_CHANNELS={"channel1_id":{"name":"Channel1","user_id":1}}

# 交易配置
DEFAULT_EXCHANGE=binance
SIMULATION_MODE=true
PRICE_CHECK_INTERVAL=60
```

## 扩展配置

如需添加新的配置项：

1. 找到相应的配置分组类（如`DatabaseSettings`, `SecuritySettings`等）
2. 添加新的字段，包括类型提示、默认值和环境变量名
3. 如果需要添加新的配置分组，创建新的BaseSettings子类
4. 在Settings类中添加新的分组实例

## 最佳实践

- 在代码中使用`settings`实例访问配置，避免硬编码
- 不要直接使用`os.getenv()`，应通过配置系统统一管理环境变量
- 为敏感配置（如API密钥）提供适当的验证和默认值处理
- 在测试中可以使用环境变量覆盖默认配置 