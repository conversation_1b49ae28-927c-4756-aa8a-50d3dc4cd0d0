import uuid
from datetime import datetime, timezone
from decimal import Decimal
from typing import Any, Dict, List, Literal, Optional, Union

from pydantic import BaseModel

from .schemas import Order, TradePlan

# ============================================================================
# 基础消息模型
# ============================================================================


class WebSocketMessage(BaseModel):
    """WebSocket消息基类"""

    event_type: str
    payload: Any
    timestamp: Optional[str] = None

    def __init__(self, **data):
        if "timestamp" not in data:
            data["timestamp"] = datetime.now(timezone.utc).isoformat()
        super().__init__(**data)


# ============================================================================
# 载荷数据模型
# ============================================================================


class AgentStateTransitionPayload(BaseModel):
    """Agent状态转换载荷"""

    timestamp: str
    from_node: str
    to_node: str
    task_id: uuid.UUID
    progress_percentage: Optional[float] = None
    estimated_completion_time: Optional[str] = None
    current_step_description: Optional[str] = None


class PendingActionDetails(BaseModel):
    """待办事项的详细信息，用于发往前端"""

    raw_input: str
    clarification_needed: str
    proposed_plan: Optional[List[TradePlan]] = None
    confidence_score: Optional[float] = None
    alternative_interpretations: Optional[List[str]] = None


class PendingActionPayload(BaseModel):
    """待处理动作载荷"""

    action_id: uuid.UUID
    details: PendingActionDetails
    expires_at: str
    priority: Optional[str] = "normal"  # low, normal, high, urgent
    action_type: Optional[str] = "user_confirmation"


class NotificationPayload(BaseModel):
    """通知载荷"""

    title: str
    message: str
    level: Literal["info", "warning", "error", "success"] = "info"
    category: Optional[str] = None  # trading, system, risk, etc.
    action_required: bool = False
    related_order_id: Optional[uuid.UUID] = None
    related_task_id: Optional[uuid.UUID] = None


class MarketDataPayload(BaseModel):
    """市场数据载荷"""

    symbol: str
    price: Decimal
    change_24h: Optional[Decimal] = None
    volume_24h: Optional[Decimal] = None
    timestamp: str
    source: Optional[str] = None


class RiskAlertPayload(BaseModel):
    """风险警报载荷"""

    alert_type: Literal[
        "position_limit", "loss_threshold", "exposure_warning", "margin_call"
    ]
    severity: Literal["low", "medium", "high", "critical"]
    message: str
    affected_positions: Optional[List[uuid.UUID]] = None
    recommended_actions: Optional[List[str]] = None
    auto_action_taken: Optional[str] = None


class SystemStatusPayload(BaseModel):
    """系统状态载荷"""

    component: str  # trading_engine, price_feed, risk_manager, etc.
    status: Literal["online", "offline", "degraded", "maintenance"]
    message: Optional[str] = None
    affected_features: Optional[List[str]] = None
    estimated_recovery_time: Optional[str] = None


class ConditionalOrderTriggerPayload(BaseModel):
    """条件订单触发载荷"""

    conditional_order_id: uuid.UUID
    trigger_condition: Dict[str, Any]
    current_price: Decimal
    trigger_price: Decimal
    symbol: str
    action_plan: Dict[str, Any]
    execution_status: Literal["pending", "executing", "completed", "failed"]


class PerformanceMetricsPayload(BaseModel):
    """性能指标载荷"""

    period: str  # 1h, 24h, 7d, 30d
    total_pnl: Decimal
    win_rate: float
    total_trades: int
    avg_trade_duration: Optional[str] = None
    sharpe_ratio: Optional[float] = None
    max_drawdown: Optional[Decimal] = None


# ============================================================================
# 具体事件模型
# ============================================================================


class OrderUpdateEvent(WebSocketMessage):
    """订单更新事件"""

    event_type: Literal["ORDER_UPDATE"] = "ORDER_UPDATE"
    payload: Order


class AgentStateTransitionEvent(WebSocketMessage):
    """Agent状态转换事件"""

    event_type: Literal["AGENT_STATE_TRANSITION"] = "AGENT_STATE_TRANSITION"
    payload: AgentStateTransitionPayload


class PendingActionRequiredEvent(WebSocketMessage):
    """待处理动作事件"""

    event_type: Literal["PENDING_ACTION_REQUIRED"] = "PENDING_ACTION_REQUIRED"
    payload: PendingActionPayload


class NotificationEvent(WebSocketMessage):
    """通知事件"""

    event_type: Literal["NOTIFICATION"] = "NOTIFICATION"
    payload: NotificationPayload


class MarketDataUpdateEvent(WebSocketMessage):
    """市场数据更新事件"""

    event_type: Literal["MARKET_DATA_UPDATE"] = "MARKET_DATA_UPDATE"
    payload: MarketDataPayload


class RiskAlertEvent(WebSocketMessage):
    """风险警报事件"""

    event_type: Literal["RISK_ALERT"] = "RISK_ALERT"
    payload: RiskAlertPayload


class SystemStatusEvent(WebSocketMessage):
    """系统状态事件"""

    event_type: Literal["SYSTEM_STATUS"] = "SYSTEM_STATUS"
    payload: SystemStatusPayload


class ConditionalOrderTriggerEvent(WebSocketMessage):
    """条件订单触发事件"""

    event_type: Literal["CONDITIONAL_ORDER_TRIGGER"] = "CONDITIONAL_ORDER_TRIGGER"
    payload: ConditionalOrderTriggerPayload


class PerformanceMetricsEvent(WebSocketMessage):
    """性能指标事件"""

    event_type: Literal["PERFORMANCE_METRICS"] = "PERFORMANCE_METRICS"
    payload: PerformanceMetricsPayload


class TaskCompletedEvent(WebSocketMessage):
    """任务完成事件"""

    event_type: Literal["TASK_COMPLETED"] = "TASK_COMPLETED"
    payload: Dict[str, Any]


class HeartbeatEvent(WebSocketMessage):
    """心跳事件"""

    event_type: Literal["HEARTBEAT"] = "HEARTBEAT"
    payload: Dict[str, Any] = {}


# ============================================================================
# 消息类型联合
# ============================================================================

WebSocketEventUnion = Union[
    OrderUpdateEvent,
    AgentStateTransitionEvent,
    PendingActionRequiredEvent,
    NotificationEvent,
    MarketDataUpdateEvent,
    RiskAlertEvent,
    SystemStatusEvent,
    ConditionalOrderTriggerEvent,
    PerformanceMetricsEvent,
    TaskCompletedEvent,
    HeartbeatEvent,
]
