import uuid
from datetime import datetime, timezone
from typing import List, Optional
from decimal import Decimal

from sqlalchemy import (
    NUMERIC,
    TIMESTAMP,
    Boolean,
    CheckConstraint,
    Column,
    DateTime,
    Float,
    ForeignKey,
    Index,
    Integer,
    MetaData,
    String,
    Table,
    Text,
    UniqueConstraint,
    DECIMAL,
)
from sqlalchemy.dialects.postgresql import ARRAY, BYTEA, JSONB, UUID
from sqlalchemy.sql import func
from sqlalchemy.orm import Mapped, mapped_column, relationship

from .database import Base


def utc_now():
    """返回UTC时间，替代已弃用的datetime.utcnow()"""
    return datetime.now(timezone.utc).replace(tzinfo=None)


class User(Base):
    __tablename__ = "users"

    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True), primary_key=True, default=uuid.uuid4
    )
    username: Mapped[str] = mapped_column(String(50), unique=True, nullable=False)
    email: Mapped[str] = mapped_column(
        String(100), unique=True, nullable=False, default=""
    )
    password_hash: Mapped[str] = mapped_column(String(255), nullable=False)  # 匹配数据库表字段名
    is_active: Mapped[bool] = mapped_column(Boolean, default=True)
    is_first_time: Mapped[bool] = mapped_column(Boolean, default=True)
    created_at: Mapped[datetime] = mapped_column(
        DateTime, default=utc_now, nullable=False
    )
    updated_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime, default=utc_now, onupdate=utc_now
    )

    # 关系映射
    signals: Mapped[List["Signal"]] = relationship(
        "Signal", back_populates="user", cascade="all, delete-orphan"
    )
    discord_configs: Mapped[List["DiscordConfig"]] = relationship(
        "DiscordConfig", back_populates="user", cascade="all, delete-orphan"
    )
    llm_configs: Mapped[List["LLMConfig"]] = relationship(
        "LLMConfig", back_populates="user", cascade="all, delete-orphan"
    )

    # 约束
    __table_args__ = (
        CheckConstraint("length(username) >= 3", name="username_min_length"),
        CheckConstraint(
            "length(password_hash) >= 60", name="password_hash_min_length"
        ),  # bcrypt哈希长度
        Index("idx_users_username", "username"),
        Index("idx_users_email", "email"),
        Index("idx_users_created_at", "created_at"),
    )


class Signal(Base):
    __tablename__ = "signals"

    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True), primary_key=True, default=uuid.uuid4
    )
    user_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True), ForeignKey("users.id", ondelete="CASCADE"), nullable=False
    )
    platform: Mapped[str] = mapped_column(String(20), nullable=False)
    platform_message_id: Mapped[Optional[str]] = mapped_column(
        String(255), nullable=True
    )
    channel_id: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)
    channel_name: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)
    author_id: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)
    author_name: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)
    content: Mapped[str] = mapped_column(Text, nullable=False)
    raw_content: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    message_type: Mapped[str] = mapped_column(
        String(50), default="text", nullable=False
    )
    signal_metadata: Mapped[Optional[dict]] = mapped_column(
        "metadata", JSONB, nullable=True
    )
    confidence: Mapped[Optional[Decimal]] = mapped_column(
        NUMERIC(3, 2), nullable=True, comment="AI解析置信度"
    )
    ai_parse_status: Mapped[str] = mapped_column(
        String(20), default="pending", nullable=False, comment="AI解析状态"
    )
    message_type_ai: Mapped[str] = mapped_column(
        String(30), default="normal_message", nullable=False, comment="AI识别的消息类型"
    )
    llm_service: Mapped[Optional[str]] = mapped_column(
        String(20), nullable=True, comment="使用的LLM服务"
    )
    is_processed: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False)
    processed_at: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)

    # 去重相关字段
    content_hash: Mapped[Optional[str]] = mapped_column(
        String(64), nullable=True, comment="内容哈希，用于去重"
    )
    dedup_key: Mapped[Optional[str]] = mapped_column(
        String(255), nullable=True, comment="去重键，基于用户+平台+内容"
    )
    is_duplicate: Mapped[bool] = mapped_column(
        Boolean, default=False, nullable=False, comment="是否为重复信号"
    )
    original_signal_id: Mapped[Optional[uuid.UUID]] = mapped_column(
        UUID(as_uuid=True), ForeignKey("signals.id"), nullable=True, comment="原始信号ID（如果是重复信号）"
    )

    # Agent处理相关字段
    agent_task_id: Mapped[Optional[uuid.UUID]] = mapped_column(
        UUID(as_uuid=True), nullable=True, comment="关联的Agent任务ID"
    )
    agent_processing_status: Mapped[str] = mapped_column(
        String(20), default="pending", nullable=False, comment="Agent处理状态"
    )

    created_at: Mapped[datetime] = mapped_column(
        DateTime, default=utc_now, nullable=False
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime, default=utc_now, onupdate=utc_now, nullable=False
    )

    # 约束和索引
    __table_args__ = (
        CheckConstraint(
            "platform IN ('discord', 'telegram', 'manual')",
            name="valid_platform",
        ),
        CheckConstraint(
            "message_type IN ('text', 'embed', 'attachment', 'reply')",
            name="valid_message_type",
        ),
        CheckConstraint(
            "confidence IS NULL OR (confidence >= 0 AND confidence <= 1)",
            name="valid_confidence",
        ),
        CheckConstraint(
            "ai_parse_status IN ('pending', 'success', 'failed', 'partial')",
            name="valid_ai_parse_status",
        ),
        CheckConstraint(
            "message_type_ai IN ('normal_message', 'trading_signal', 'market_analysis', 'price_alert', 'ambiguous')",
            name="valid_message_type_ai",
        ),
        CheckConstraint(
            "llm_service IS NULL OR llm_service IN ('deepseek', 'gemini', 'chatgpt', 'claude')",
            name="valid_llm_service",
        ),
        CheckConstraint(
            "processed_at IS NULL OR processed_at >= created_at",
            name="valid_processed_time",
        ),
        CheckConstraint(
            "agent_processing_status IN ('pending', 'processing', 'completed', 'failed', 'skipped')",
            name="valid_agent_processing_status",
        ),
        # 唯一约束：同一平台的同一消息在同一频道中只能存在一次
        Index(
            "unique_platform_message",
            "platform",
            "platform_message_id",
            "channel_id",
            unique=True,
            postgresql_where="platform_message_id IS NOT NULL AND channel_id IS NOT NULL",
        ),
        # 基础索引
        Index("idx_signals_user_id", "user_id"),
        Index("idx_signals_platform", "platform"),
        Index("idx_signals_channel_id", "channel_id"),
        Index("idx_signals_created_at", "created_at"),
        Index("idx_signals_is_processed", "is_processed"),
        Index("idx_signals_confidence", "confidence"),
        Index("idx_signals_ai_parse_status", "ai_parse_status"),
        Index("idx_signals_message_type_ai", "message_type_ai"),
        Index("idx_signals_llm_service", "llm_service"),
        # 去重和Agent处理相关索引
        Index("idx_signals_content_hash", "content_hash"),
        Index("idx_signals_dedup_key", "dedup_key"),
        Index("idx_signals_is_duplicate", "is_duplicate"),
        Index("idx_signals_agent_task_id", "agent_task_id"),
        Index("idx_signals_agent_status", "agent_processing_status"),
        Index(
            "idx_signals_unique_dedup",
            "user_id",
            "dedup_key",
            unique=True,
            postgresql_where="is_duplicate = FALSE",
        ),
        # 复合索引用于常见查询
        Index("idx_signals_user_platform", "user_id", "platform"),
        Index("idx_signals_user_created", "user_id", "created_at"),
        Index("idx_signals_platform_created", "platform", "created_at"),
        Index("idx_signals_processed_created", "is_processed", "created_at"),
        Index("idx_signals_user_ai_status", "user_id", "ai_parse_status"),
        Index("idx_signals_user_msg_type", "user_id", "message_type_ai"),
        # GIN索引用于JSONB字段
        Index("idx_signals_metadata_gin", "metadata", postgresql_using="gin"),
    )

    # 关系映射
    user: Mapped["User"] = relationship("User", back_populates="signals")

    def __repr__(self) -> str:
        return f"Signal(id={self.id}, platform={self.platform}, content='{self.content[:50]}...')"


class Order(Base):
    __tablename__ = "orders"

    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True), primary_key=True, default=uuid.uuid4
    )
    user_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True), ForeignKey("users.id"), nullable=False
    )
    client_order_id: Mapped[str] = mapped_column(
        String(100), nullable=False, unique=True
    )
    exchange_order_id: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    source_message_id: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    symbol: Mapped[str] = mapped_column(String(20), nullable=False)
    side: Mapped[str] = mapped_column(String(10), nullable=False)
    quantity: Mapped[Decimal] = mapped_column(NUMERIC(20, 8), nullable=False)
    entry_price: Mapped[Optional[Decimal]] = mapped_column(
        NUMERIC(20, 8), nullable=True
    )
    close_price: Mapped[Optional[Decimal]] = mapped_column(
        NUMERIC(20, 8), nullable=True
    )  # 平仓价格
    pnl: Mapped[Optional[Decimal]] = mapped_column(NUMERIC(20, 8), nullable=True)  # 盈亏
    status: Mapped[str] = mapped_column(String(20), nullable=False)
    agent_log: Mapped[Optional[dict]] = mapped_column(JSONB, nullable=True)
    created_at: Mapped[datetime] = mapped_column(
        DateTime, default=utc_now, nullable=False
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime, default=utc_now, onupdate=utc_now, nullable=False
    )
    closed_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime, nullable=True
    )  # 平仓时间

    # 约束和索引
    __table_args__ = (
        CheckConstraint("side IN ('buy', 'sell')", name="valid_side"),
        CheckConstraint(
            "status IN ('active', 'closed', 'failed', 'cancelled')",
            name="valid_status",
        ),
        CheckConstraint("quantity > 0", name="positive_quantity"),
        CheckConstraint(
            "entry_price IS NULL OR entry_price > 0",
            name="positive_entry_price",
        ),
        CheckConstraint(
            "close_price IS NULL OR close_price > 0",
            name="positive_close_price",
        ),
        CheckConstraint(
            "closed_at IS NULL OR closed_at >= created_at",
            name="valid_close_time",
        ),
        # 复合索引用于常见查询
        Index("idx_orders_user_status", "user_id", "status"),
        Index("idx_orders_user_symbol", "user_id", "symbol"),
        Index("idx_orders_user_created", "user_id", "created_at"),
        Index("idx_orders_status_created", "status", "created_at"),
        Index("idx_orders_symbol_created", "symbol", "created_at"),
        # GIN索引用于JSONB字段
        Index("idx_orders_agent_log_gin", "agent_log", postgresql_using="gin"),
    )


class ExchangeConfig(Base):
    __tablename__ = "exchange_configs"

    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True), primary_key=True, default=uuid.uuid4
    )
    user_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True), ForeignKey("users.id"), nullable=False
    )
    exchange_name: Mapped[str] = mapped_column(String(50), nullable=False)
    encrypted_api_key: Mapped[str] = mapped_column(Text, nullable=False)
    encrypted_api_secret: Mapped[str] = mapped_column(Text, nullable=False)
    encrypted_passphrase: Mapped[Optional[str]] = mapped_column(
        Text, nullable=True
    )  # For OKX
    sandbox_mode: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)
    created_at: Mapped[datetime] = mapped_column(
        DateTime, default=utc_now, nullable=False
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime, default=utc_now, onupdate=utc_now, nullable=False
    )

    # 约束和索引
    __table_args__ = (
        CheckConstraint(
            "exchange_name IN ('binance', 'okx', 'bybit', 'coinbase')",
            name="valid_exchange_name",
        ),
        CheckConstraint("length(encrypted_api_key) > 0", name="non_empty_api_key"),
        CheckConstraint(
            "length(encrypted_api_secret) > 0", name="non_empty_api_secret"
        ),
        # 索引
        Index("idx_exchange_configs_user_id", "user_id"),
        Index("idx_exchange_configs_user_active", "user_id", "is_active"),
        Index(
            "idx_exchange_configs_exchange_active",
            "exchange_name",
            "is_active",
        ),
    )


class ConditionalOrder(Base):
    __tablename__ = "conditional_orders"

    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True), primary_key=True, default=uuid.uuid4
    )
    user_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True), ForeignKey("users.id"), nullable=False
    )
    symbol: Mapped[str] = mapped_column(String(20), nullable=False)
    trigger_condition: Mapped[dict] = mapped_column(JSONB, nullable=False)
    action_plan: Mapped[dict] = mapped_column(JSONB, nullable=False)
    status: Mapped[str] = mapped_column(String(20), nullable=False)
    created_at: Mapped[datetime] = mapped_column(
        DateTime, default=utc_now, nullable=False
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime, default=utc_now, onupdate=utc_now, nullable=False
    )
    triggered_at: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)

    # 约束和索引
    __table_args__ = (
        CheckConstraint(
            "status IN ('PENDING', 'TRIGGERED', 'CANCELLED', 'EXPIRED')",
            name="valid_conditional_status",
        ),
        CheckConstraint(
            "triggered_at IS NULL OR triggered_at >= created_at",
            name="valid_trigger_time",
        ),
        # 索引
        Index("idx_conditional_orders_user_status", "user_id", "status"),
        Index("idx_conditional_orders_user_symbol", "user_id", "symbol"),
        Index("idx_conditional_orders_status_created", "status", "created_at"),
        Index("idx_conditional_orders_symbol_status", "symbol", "status"),
        # GIN索引用于JSONB字段
        Index(
            "idx_conditional_orders_trigger_gin",
            "trigger_condition",
            postgresql_using="gin",
        ),
        Index(
            "idx_conditional_orders_action_gin",
            "action_plan",
            postgresql_using="gin",
        ),
    )


class RiskConfig(Base):
    __tablename__ = "risk_configs"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    user_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True), ForeignKey("users.id"), nullable=False, unique=True
    )

    # 基础风控参数
    max_concurrent_orders: Mapped[int] = mapped_column(
        Integer, default=5, nullable=False
    )
    max_total_position_value_usd: Mapped[Decimal] = mapped_column(
        NUMERIC(15, 2), default=Decimal("1000.0"), nullable=False
    )
    default_position_size_usd: Mapped[Decimal] = mapped_column(
        NUMERIC(15, 2), default=Decimal("100.0"), nullable=False
    )
    max_position_size_usd: Mapped[Decimal] = mapped_column(
        NUMERIC(15, 2), default=Decimal("500.0"), nullable=False
    )

    # 损失控制
    max_daily_loss_usd: Mapped[Decimal] = mapped_column(
        NUMERIC(15, 2), default=Decimal("500.0"), nullable=False
    )
    max_drawdown_percent: Mapped[Decimal] = mapped_column(
        NUMERIC(5, 2), default=Decimal("20.0"), nullable=False
    )
    stop_loss_percent: Mapped[Decimal] = mapped_column(
        NUMERIC(5, 2), default=Decimal("5.0"), nullable=False
    )

    # 交易对和时间限制
    allowed_symbols: Mapped[List[str]] = mapped_column(
        ARRAY(String), default=["BTC/USDT", "ETH/USDT"], nullable=False
    )
    trading_hours_start: Mapped[str] = mapped_column(
        String(5), default="00:00", nullable=False
    )  # HH:MM格式
    trading_hours_end: Mapped[str] = mapped_column(
        String(5), default="23:59", nullable=False
    )  # HH:MM格式

    # AI相关配置
    confidence_threshold: Mapped[Decimal] = mapped_column(
        NUMERIC(3, 2), default=Decimal("0.8"), nullable=False
    )
    auto_approve_threshold: Mapped[Decimal] = mapped_column(
        NUMERIC(3, 2), default=Decimal("0.95"), nullable=False
    )  # 自动批准阈值

    # 高级风控
    max_symbol_concentration_percent: Mapped[Decimal] = mapped_column(
        NUMERIC(5, 2), default=Decimal("50.0"), nullable=False
    )  # 单个交易对最大占比
    correlation_check_enabled: Mapped[bool] = mapped_column(
        Boolean, default=True, nullable=False
    )
    volatility_check_enabled: Mapped[bool] = mapped_column(
        Boolean, default=True, nullable=False
    )

    # 时间戳
    created_at: Mapped[datetime] = mapped_column(
        DateTime, default=utc_now, nullable=False
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime, default=utc_now, onupdate=utc_now, nullable=False
    )

    # 约束和索引
    __table_args__ = (
        # 数值范围约束
        CheckConstraint(
            "max_concurrent_orders > 0 AND max_concurrent_orders <= 100",
            name="valid_concurrent_orders",
        ),
        CheckConstraint(
            "max_total_position_value_usd > 0",
            name="positive_total_position_value",
        ),
        CheckConstraint(
            "default_position_size_usd > 0",
            name="positive_default_position_size",
        ),
        CheckConstraint("max_position_size_usd > 0", name="positive_max_position_size"),
        CheckConstraint("max_daily_loss_usd > 0", name="positive_max_daily_loss"),
        CheckConstraint(
            "max_drawdown_percent > 0 AND max_drawdown_percent <= 100",
            name="valid_drawdown_percent",
        ),
        CheckConstraint(
            "stop_loss_percent > 0 AND stop_loss_percent <= 100",
            name="valid_stop_loss_percent",
        ),
        CheckConstraint(
            "max_symbol_concentration_percent > 0 AND max_symbol_concentration_percent <= 100",
            name="valid_concentration_percent",
        ),
        # 置信度约束
        CheckConstraint(
            "confidence_threshold >= 0 AND confidence_threshold <= 1",
            name="valid_confidence_threshold",
        ),
        CheckConstraint(
            "auto_approve_threshold >= 0 AND auto_approve_threshold <= 1",
            name="valid_auto_approve_threshold",
        ),
        CheckConstraint(
            "auto_approve_threshold >= confidence_threshold",
            name="auto_approve_gte_confidence",
        ),
        # 时间格式约束
        CheckConstraint(
            "trading_hours_start ~ '^[0-2][0-9]:[0-5][0-9]$'",
            name="valid_start_time_format",
        ),
        CheckConstraint(
            "trading_hours_end ~ '^[0-2][0-9]:[0-5][0-9]$'",
            name="valid_end_time_format",
        ),
        # 逻辑约束
        CheckConstraint(
            "max_position_size_usd <= max_total_position_value_usd",
            name="max_position_lte_total",
        ),
        CheckConstraint(
            "default_position_size_usd <= max_position_size_usd",
            name="default_lte_max_position",
        ),
        # 索引
        Index("idx_risk_configs_user_id", "user_id"),
        Index("idx_risk_configs_updated_at", "updated_at"),
    )


class PendingAction(Base):
    __tablename__ = "pending_actions"

    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True), primary_key=True, default=uuid.uuid4
    )
    task_id: Mapped[str] = mapped_column(String(100), nullable=False)
    user_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True), ForeignKey("users.id"), nullable=False
    )
    action_type: Mapped[str] = mapped_column(String(50), nullable=False)
    details: Mapped[dict] = mapped_column(JSONB, nullable=False)
    status: Mapped[str] = mapped_column(String(20), nullable=False)
    response: Mapped[Optional[dict]] = mapped_column(JSONB, nullable=True)
    created_at: Mapped[datetime] = mapped_column(
        DateTime, default=utc_now, nullable=False
    )
    expires_at: Mapped[datetime] = mapped_column(DateTime, nullable=False)
    resolved_at: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)

    # 约束和索引
    __table_args__ = (
        CheckConstraint(
            "status IN ('PENDING', 'APPROVED', 'REJECTED', 'EXPIRED')",
            name="valid_action_status",
        ),
        CheckConstraint(
            "action_type IN ('USER_CONFIRMATION', 'RISK_OVERRIDE', 'MANUAL_INTERVENTION')",
            name="valid_action_type",
        ),
        CheckConstraint("expires_at > created_at", name="valid_expiry_time"),
        CheckConstraint(
            "resolved_at IS NULL OR resolved_at >= created_at",
            name="valid_resolve_time",
        ),
        # 索引
        Index("idx_pending_actions_user_status", "user_id", "status"),
        Index("idx_pending_actions_task_id", "task_id"),
        Index("idx_pending_actions_expires_at", "expires_at"),
        Index("idx_pending_actions_created_at", "created_at"),
        # GIN索引用于JSONB字段
        Index(
            "idx_pending_actions_details_gin",
            "details",
            postgresql_using="gin",
        ),
        Index(
            "idx_pending_actions_response_gin",
            "response",
            postgresql_using="gin",
        ),
    )


class AgentCheckpoint(Base):
    __tablename__ = "agent_checkpoints"

    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True), primary_key=True, default=uuid.uuid4
    )
    task_id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), nullable=False)
    user_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True), ForeignKey("users.id"), nullable=False
    )
    node_name: Mapped[str] = mapped_column(String(50), nullable=False)
    state_data: Mapped[dict] = mapped_column(JSONB, nullable=False)
    created_at: Mapped[datetime] = mapped_column(
        DateTime, default=utc_now, nullable=False
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime, default=utc_now, onupdate=utc_now, nullable=False
    )

    # 约束和索引
    __table_args__ = (
        CheckConstraint(
            "node_name IN ('Preprocess', 'Parse', 'Context', 'Plan', 'Risk', 'Execute', 'AnalyzeError', 'UserConfirm', 'Success', 'Failure', 'Completed')",
            name="valid_node_name",
        ),
        # 复合索引用于常见查询
        Index("idx_agent_checkpoints_task_user", "task_id", "user_id"),
        Index("idx_agent_checkpoints_user_created", "user_id", "created_at"),
        Index("idx_agent_checkpoints_task_created", "task_id", "created_at"),
        Index("idx_agent_checkpoints_node_created", "node_name", "created_at"),
        # GIN索引用于JSONB字段
        Index(
            "idx_agent_checkpoints_state_gin",
            "state_data",
            postgresql_using="gin",
        ),
    )


class AgentExecutionTrace(Base):
    """Agent工作流执行追踪模型"""
    __tablename__ = "agent_execution_traces"

    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True), primary_key=True, default=uuid.uuid4
    )
    task_id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), nullable=False)
    user_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True), ForeignKey("users.id"), nullable=False
    )
    signal_id: Mapped[Optional[uuid.UUID]] = mapped_column(
        UUID(as_uuid=True), ForeignKey("signals.id"), nullable=True
    )
    node_name: Mapped[str] = mapped_column(String(50), nullable=False)
    execution_order: Mapped[int] = mapped_column(Integer, nullable=False)

    # 状态字段
    status: Mapped[str] = mapped_column(String(20), default="running", nullable=False)
    started_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), default=utc_now, nullable=False
    )
    completed_at: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)
    duration_ms: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)

    # 数据字段
    input_data: Mapped[Optional[dict]] = mapped_column(JSONB, nullable=True)
    output_data: Mapped[Optional[dict]] = mapped_column(JSONB, nullable=True)
    error_data: Mapped[Optional[dict]] = mapped_column(JSONB, nullable=True)
    performance_metrics: Mapped[Optional[dict]] = mapped_column(JSONB, nullable=True)
    trace_metadata: Mapped[Optional[dict]] = mapped_column("metadata", JSONB, nullable=True)

    # LLM监控字段
    llm_request_data: Mapped[Optional[dict]] = mapped_column(JSONB, nullable=True, comment="LLM原始请求数据")
    llm_response_data: Mapped[Optional[dict]] = mapped_column(JSONB, nullable=True, comment="LLM原始响应数据")
    llm_token_usage: Mapped[Optional[dict]] = mapped_column(JSONB, nullable=True, comment="LLM Token使用统计")
    llm_metrics: Mapped[Optional[dict]] = mapped_column(JSONB, nullable=True, comment="LLM调用监控指标")

    # 时间戳
    created_at: Mapped[datetime] = mapped_column(
        DateTime, default=utc_now, nullable=False
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime, default=utc_now, onupdate=utc_now, nullable=False
    )

    # 约束和索引
    __table_args__ = (
        CheckConstraint(
            "status IN ('started', 'completed', 'failed', 'timeout', 'cancelled')",
            name="valid_trace_status",
        ),
        CheckConstraint(
            "node_name IN ('Preprocess', 'Parse', 'Context', 'Plan', 'Risk', 'Execute')",
            name="valid_trace_node_name",
        ),
        CheckConstraint(
            "execution_order > 0",
            name="positive_execution_order",
        ),
        # 复合索引用于常见查询
        Index("idx_agent_traces_task_order", "task_id", "execution_order"),
        Index("idx_agent_traces_user_created", "user_id", "created_at"),
        Index("idx_agent_traces_node_status", "node_name", "status"),
        Index("idx_agent_traces_signal_created", "signal_id", "created_at"),
        # GIN索引用于JSONB字段
        Index(
            "idx_agent_traces_input_gin",
            "input_data",
            postgresql_using="gin",
        ),
        Index(
            "idx_agent_traces_output_gin",
            "output_data",
            postgresql_using="gin",
        ),
        Index(
            "idx_agent_traces_performance_gin",
            "performance_metrics",
            postgresql_using="gin",
        ),
        Index(
            "idx_agent_traces_metadata_gin",
            "metadata",
            postgresql_using="gin",
        ),
    )


class DiscordConfig(Base):
    """Discord信号源配置模型"""
    __tablename__ = "signal_source_configs"

    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True), primary_key=True, default=uuid.uuid4
    )
    user_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True), ForeignKey("users.id", ondelete="CASCADE"), nullable=False
    )

    # 基础配置
    source_type: Mapped[str] = mapped_column(String(50), default="discord", nullable=False)
    source_name: Mapped[str] = mapped_column(String(100), nullable=False)
    enabled: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False)

    # Discord认证
    encrypted_token: Mapped[str] = mapped_column(Text, nullable=False)

    # Discord过滤配置
    server_ids: Mapped[List[str]] = mapped_column(ARRAY(String), default=[], nullable=False)
    channel_ids: Mapped[List[str]] = mapped_column(ARRAY(String), default=[], nullable=False)
    author_ids: Mapped[List[str]] = mapped_column(ARRAY(String), default=[], nullable=False)
    allowed_message_types: Mapped[List[str]] = mapped_column(
        ARRAY(String), default=["text"], nullable=False
    )

    # 时间戳
    created_at: Mapped[datetime] = mapped_column(
        DateTime, default=utc_now, nullable=False
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime, default=utc_now, onupdate=utc_now, nullable=False
    )

    # 约束和索引
    __table_args__ = (
        CheckConstraint("source_type = 'discord'", name="valid_source_type"),
        CheckConstraint("length(encrypted_token) > 0", name="non_empty_token"),
        CheckConstraint(
            "allowed_message_types <@ ARRAY['text', 'embed', 'attachment', 'reply']",
            name="valid_message_types"
        ),
        UniqueConstraint("user_id", "source_name", name="unique_user_source_name"),
        Index("idx_signal_configs_user_id", "user_id"),
        Index("idx_signal_configs_user_enabled", "user_id", "enabled"),
        Index("idx_signal_configs_updated", "updated_at"),
        Index("idx_signal_configs_server_ids", "server_ids", postgresql_using="gin"),
        Index("idx_signal_configs_channel_ids", "channel_ids", postgresql_using="gin"),
        Index("idx_signal_configs_author_ids", "author_ids", postgresql_using="gin"),
    )

    # 关系映射
    user: Mapped["User"] = relationship("User", back_populates="discord_configs")


class LLMConfig(Base):
    """LLM配置模型"""
    __tablename__ = "llm_configs"

    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True), primary_key=True, default=uuid.uuid4
    )
    user_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True), ForeignKey("users.id", ondelete="CASCADE"), nullable=False
    )

    # 基础配置
    config_name: Mapped[str] = mapped_column(String(100), nullable=False)
    provider: Mapped[str] = mapped_column(String(20), nullable=False)
    enabled: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False)
    is_default: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False)

    # API配置
    api_key: Mapped[str] = mapped_column(Text, nullable=False)  # 加密存储
    api_base_url: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)
    model_name: Mapped[str] = mapped_column(String(100), nullable=False)

    # 请求参数配置
    max_tokens: Mapped[int] = mapped_column(Integer, default=4096, nullable=False)
    temperature: Mapped[Decimal] = mapped_column(DECIMAL(3, 2), default=Decimal('0.7'), nullable=False)
    timeout_seconds: Mapped[int] = mapped_column(Integer, default=60, nullable=False)
    max_retries: Mapped[int] = mapped_column(Integer, default=3, nullable=False)

    # 时间戳
    created_at: Mapped[datetime] = mapped_column(
        DateTime, default=utc_now, nullable=False
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime, default=utc_now, onupdate=utc_now, nullable=False
    )

    # 约束和索引
    __table_args__ = (
        CheckConstraint(
            "provider IN ('deepseek', 'gemini', 'chatgpt', 'claude')",
            name="valid_llm_provider"
        ),
        CheckConstraint(
            "temperature >= 0.0 AND temperature <= 2.0",
            name="valid_temperature"
        ),
        CheckConstraint(
            "max_tokens > 0 AND max_tokens <= 32768",
            name="valid_max_tokens"
        ),
        CheckConstraint(
            "timeout_seconds > 0 AND timeout_seconds <= 300",
            name="valid_timeout"
        ),
        CheckConstraint(
            "max_retries >= 0 AND max_retries <= 10",
            name="valid_max_retries"
        ),
        CheckConstraint("length(api_key) > 0", name="non_empty_api_key"),
        CheckConstraint("length(config_name) > 0", name="non_empty_config_name"),
        UniqueConstraint("user_id", "config_name", name="unique_user_llm_config_name"),
        # 注意：PostgreSQL 中的部分唯一约束需要在迁移脚本中单独创建
        Index("idx_llm_configs_user_id", "user_id"),
        Index("idx_llm_configs_user_enabled", "user_id", "enabled"),
        Index("idx_llm_configs_provider", "provider"),
        Index("idx_llm_configs_updated", "updated_at"),
    )

    # 关系映射
    user: Mapped["User"] = relationship("User", back_populates="llm_configs")

    def __repr__(self) -> str:
        return f"DiscordConfig(id={self.id}, source_name='{self.source_name}', enabled={self.enabled})"
