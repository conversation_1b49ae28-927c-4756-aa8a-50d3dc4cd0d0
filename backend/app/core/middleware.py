"""统一错误处理中间件

本模块提供全局的错误处理中间件，确保API响应的一致性和错误信息的标准化。
"""

import time
import traceback
from typing import Any, Dict, Optional
from uuid import uuid4

import structlog
from fastapi import HTTPException, Request, Response, status
from fastapi.responses import JSONResponse
from pydantic import ValidationError
from sqlalchemy.exc import IntegrityError, OperationalError
from starlette.middleware.base import BaseHTTPMiddleware

from ..core.config import settings

# 配置结构化日志
logger = structlog.get_logger()


class ErrorHandlingMiddleware(BaseHTTPMiddleware):
    """统一错误处理中间件

    处理所有未捕获的异常，提供标准化的错误响应格式。
    """

    async def dispatch(self, request: Request, call_next) -> Response:
        """处理请求并捕获异常"""
        # 生成请求ID用于追踪
        request_id = str(uuid4())
        start_time = time.time()

        # 记录请求开始
        logger.info(
            "Request started",
            request_id=request_id,
            method=request.method,
            url=str(request.url),
            client_ip=request.client.host if request.client else None,
        )

        try:
            # 将请求ID添加到请求状态中
            request.state.request_id = request_id

            # 执行请求
            response = await call_next(request)

            # 记录成功响应
            duration = time.time() - start_time
            logger.info(
                "Request completed",
                request_id=request_id,
                status_code=response.status_code,
                duration=duration,
            )

            return response

        except ValidationError as e:
            # Pydantic验证错误
            return await self._handle_validation_error(e, request_id)

        except HTTPException as e:
            # FastAPI HTTP异常
            return await self._handle_http_exception(e, request_id)

        except IntegrityError as e:
            # 数据库完整性错误
            return await self._handle_database_error(e, request_id, "integrity_error")

        except OperationalError as e:
            # 数据库操作错误
            return await self._handle_database_error(e, request_id, "operational_error")

        except Exception as e:
            # 未知错误
            return await self._handle_unknown_error(e, request_id)

    async def _handle_validation_error(
        self, error: ValidationError, request_id: str
    ) -> JSONResponse:
        """处理验证错误"""
        logger.warning(
            "Validation error occurred",
            request_id=request_id,
            error_details=error.errors(),
        )

        # 格式化验证错误信息
        formatted_errors = []
        for err in error.errors():
            formatted_errors.append(
                {
                    "field": ".".join(str(x) for x in err["loc"]),
                    "message": err["msg"],
                    "type": err["type"],
                }
            )

        return get_error_response(
            error_code="VALIDATION_ERROR",
            message="请求数据验证失败",
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            details={"errors": formatted_errors},
            request_id=request_id,
        )

    async def _handle_http_exception(
        self, error: HTTPException, request_id: str
    ) -> JSONResponse:
        """处理HTTP异常"""
        logger.warning(
            "HTTP exception occurred",
            request_id=request_id,
            status_code=error.status_code,
            detail=error.detail,
        )

        return get_error_response(
            error_code="HTTP_ERROR",
            message=error.detail,
            status_code=error.status_code,
            request_id=request_id,
        )

    async def _handle_database_error(
        self, error: Exception, request_id: str, error_type: str
    ) -> JSONResponse:
        """处理数据库错误"""
        logger.error(
            "Database error occurred",
            request_id=request_id,
            error_type=error_type,
            error_message=str(error),
        )

        # 根据错误类型提供不同的用户友好消息和状态码
        error_type_lower = error_type.lower()
        if error_type_lower == "integrity_error" or error_type == "INTEGRITY_ERROR":
            message = "数据完整性错误，可能是重复数据或违反约束"
            status_code = status.HTTP_409_CONFLICT
            error_code = "INTEGRITY_ERROR"
        elif (
            error_type_lower == "operational_error" or error_type == "OPERATIONAL_ERROR"
        ):
            message = "数据库操作失败，请稍后重试"
            status_code = status.HTTP_503_SERVICE_UNAVAILABLE
            error_code = "OPERATIONAL_ERROR"
        else:
            message = "数据库错误"
            status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
            error_code = "DATABASE_ERROR"

        return get_error_response(
            error_code=error_code,
            message=message,
            status_code=status_code,
            request_id=request_id,
        )

    async def _handle_unknown_error(
        self, error: Exception, request_id: str
    ) -> JSONResponse:
        """处理未知错误"""
        # 记录完整的错误信息
        logger.error(
            "Unknown error occurred",
            request_id=request_id,
            error_type=type(error).__name__,
            error_message=str(error),
            traceback=traceback.format_exc() if settings.debug else None,
        )

        # 在开发模式下提供详细错误信息
        if settings.debug:
            details = {
                "error_type": type(error).__name__,
                "traceback": traceback.format_exc(),
            }
            message = f"内部服务器错误: {str(error)}"
        else:
            details = None
            message = "内部服务器错误，请稍后重试"

        return get_error_response(
            error_code="INTERNAL_ERROR",
            message=message,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            details=details,
            request_id=request_id,
        )


class RequestLoggingMiddleware(BaseHTTPMiddleware):
    """请求日志中间件

    记录所有API请求的详细信息，用于监控和调试。
    """

    async def dispatch(self, request: Request, call_next) -> Response:
        """记录请求和响应信息"""
        start_time = time.time()

        # 记录请求详情
        request_info = {
            "method": request.method,
            "url": str(request.url),
            "headers": dict(request.headers),
            "client_ip": request.client.host if request.client else None,
        }

        # 过滤敏感信息
        if "authorization" in request_info["headers"]:
            request_info["headers"]["authorization"] = "***"

        logger.info("API request received", **request_info)

        try:
            response = await call_next(request)

            # 记录响应信息
            duration = time.time() - start_time
            logger.info(
                "API request completed",
                status_code=response.status_code,
                duration=duration,
                method=request.method,
                url=str(request.url),
            )

            return response

        except Exception as e:
            duration = time.time() - start_time
            logger.error(
                "API request failed",
                error=str(e),
                duration=duration,
                method=request.method,
                url=str(request.url),
            )
            raise


def get_error_response(
    error_code: str,
    message: str,
    status_code: int = 500,
    details: Optional[Dict[str, Any]] = None,
    request_id: Optional[str] = None,
) -> JSONResponse:
    """创建标准化的错误响应

    Args:
        error_code: 错误代码
        message: 错误消息
        status_code: HTTP状态码
        details: 详细错误信息
        request_id: 请求ID

    Returns:
        标准化的JSON错误响应
    """
    content = {
        "success": False,
        "error": error_code,  # 向后兼容
        "error_code": error_code,  # 新格式
        "message": message,
    }

    if details:
        content["details"] = details

    if request_id:
        content["request_id"] = request_id

    return JSONResponse(status_code=status_code, content=content)


def create_success_response(
    data: Any,
    message: str = "操作成功",
    request_id: Optional[str] = None,
) -> Dict[str, Any]:
    """创建标准化的成功响应

    Args:
        data: 响应数据
        message: 成功消息
        request_id: 请求ID

    Returns:
        标准化的成功响应
    """
    response = {
        "success": True,
        "message": message,
        "data": data,
    }

    if request_id:
        response["request_id"] = request_id

    return response
