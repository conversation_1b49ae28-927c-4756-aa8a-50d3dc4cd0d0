"""
WebSocket连接管理器 - 支持连接恢复和消息重发
"""
import asyncio
import json
import uuid
from collections import deque
from datetime import datetime, timedelta, timezone
from enum import Enum
from typing import Any, Dict, List, Optional, Set

import structlog
from fastapi import WebSocket, WebSocketDisconnect

from .models import User

logger = structlog.get_logger()


class ConnectionState(Enum):
    """连接状态"""

    CONNECTING = "connecting"
    CONNECTED = "connected"
    DISCONNECTED = "disconnected"
    RECONNECTING = "reconnecting"


class MessagePriority(Enum):
    """消息优先级"""

    LOW = 1
    NORMAL = 2
    HIGH = 3
    CRITICAL = 4


class PendingMessage:
    """待发送消息"""

    def __init__(
        self,
        message: Dict[str, Any],
        priority: MessagePriority = MessagePriority.NORMAL,
        retry_count: int = 0,
        max_retries: int = 3,
    ):
        self.id = str(uuid.uuid4())
        self.message = message
        self.priority = priority
        self.retry_count = retry_count
        self.max_retries = max_retries
        self.created_at = datetime.now(timezone.utc)
        self.last_attempt = None


class ConnectionInfo:
    """连接信息"""

    def __init__(self, websocket: WebSocket, user: User):
        self.websocket = websocket
        self.user = user
        self.connected_at = datetime.now(timezone.utc)
        self.last_ping = datetime.now(timezone.utc)
        self.last_pong = datetime.now(timezone.utc)
        self.connection_id = str(uuid.uuid4())
        self.state = ConnectionState.CONNECTED
        self.reconnect_attempts = 0
        self.max_reconnect_attempts = 5

        # 消息队列和重发机制
        self.pending_messages: deque = deque(maxlen=1000)  # 限制队列大小
        self.sent_messages: Dict[str, PendingMessage] = {}  # 已发送但未确认的消息
        self.message_sequence = 0

        # 心跳机制
        self.heartbeat_interval = 30  # 秒
        self.heartbeat_timeout = 60  # 秒
        self.heartbeat_task: Optional[asyncio.Task] = None


class WebSocketManager:
    """WebSocket连接管理器"""

    def __init__(self):
        # 活跃连接：user_id -> List[ConnectionInfo]
        self.active_connections: Dict[int, List[ConnectionInfo]] = {}
        # 连接ID映射：connection_id -> ConnectionInfo
        self.connection_map: Dict[str, ConnectionInfo] = {}
        # 断开连接的用户（用于重连）
        self.disconnected_users: Dict[int, datetime] = {}
        # 全局消息队列（用于离线用户）
        self.offline_messages: Dict[int, deque] = {}

        # 清理任务（延迟创建）
        self.cleanup_task: Optional[asyncio.Task] = None

    async def connect(
        self,
        websocket: WebSocket,
        user: User,
        reconnect_token: Optional[str] = None,
    ) -> str:
        """
        建立WebSocket连接

        Args:
            websocket: WebSocket连接
            user: 用户对象
            reconnect_token: 重连令牌（用于恢复会话）

        Returns:
            str: 连接ID
        """
        # 启动清理任务（如果还没有启动）
        if self.cleanup_task is None:
            self.cleanup_task = asyncio.create_task(self._cleanup_loop())

        await websocket.accept()

        connection_info = ConnectionInfo(websocket, user)

        # 处理重连逻辑
        if reconnect_token and user.id in self.disconnected_users:
            await self._handle_reconnection(connection_info, reconnect_token)

        # 添加到用户连接列表
        if user.id not in self.active_connections:
            self.active_connections[user.id] = []
        self.active_connections[user.id].append(connection_info)

        # 添加到连接映射
        self.connection_map[connection_info.connection_id] = connection_info

        # 清理断开连接记录
        if user.id in self.disconnected_users:
            del self.disconnected_users[user.id]

        # 启动心跳
        connection_info.heartbeat_task = asyncio.create_task(
            self._heartbeat_loop(connection_info)
        )

        # 发送离线消息
        await self._send_offline_messages(connection_info)

        logger.info(
            "WebSocket connected",
            user_id=user.id,
            username=user.username,
            connection_id=connection_info.connection_id,
            total_connections=len(self.connection_map),
            is_reconnect=reconnect_token is not None,
        )

        return connection_info.connection_id

    async def disconnect(self, connection_id: str):
        """
        断开WebSocket连接

        Args:
            connection_id: 连接ID
        """
        if connection_id not in self.connection_map:
            return

        connection_info = self.connection_map[connection_id]
        user_id = connection_info.user.id

        # 停止心跳任务
        if connection_info.heartbeat_task:
            connection_info.heartbeat_task.cancel()

        # 从连接映射中移除
        del self.connection_map[connection_id]

        # 从用户连接列表中移除
        if user_id in self.active_connections:
            self.active_connections[user_id] = [
                conn
                for conn in self.active_connections[user_id]
                if conn.connection_id != connection_id
            ]

            # 如果用户没有其他连接，记录断开时间
            if not self.active_connections[user_id]:
                del self.active_connections[user_id]
                self.disconnected_users[user_id] = datetime.now(timezone.utc)

        logger.info(
            "WebSocket disconnected",
            user_id=user_id,
            connection_id=connection_id,
            total_connections=len(self.connection_map),
        )

    async def send_to_user(
        self,
        user_id: int,
        message: Dict[str, Any],
        priority: MessagePriority = MessagePriority.NORMAL,
    ) -> bool:
        """
        发送消息给指定用户

        Args:
            user_id: 用户ID
            message: 消息内容
            priority: 消息优先级

        Returns:
            bool: 是否成功发送
        """
        if user_id not in self.active_connections:
            # 用户离线，存储消息
            await self._store_offline_message(user_id, message, priority)
            return False

        success_count = 0
        connections = self.active_connections[user_id].copy()

        for connection_info in connections:
            try:
                await self._send_message_to_connection(
                    connection_info, message, priority
                )
                success_count += 1
            except Exception as e:
                logger.error(
                    "Failed to send message to connection",
                    user_id=user_id,
                    connection_id=connection_info.connection_id,
                    error=str(e),
                )
                # 连接可能已断开，尝试清理
                await self.disconnect(connection_info.connection_id)

        return success_count > 0

    async def send_to_all(
        self,
        message: Dict[str, Any],
        priority: MessagePriority = MessagePriority.NORMAL,
    ):
        """
        广播消息给所有连接的用户

        Args:
            message: 消息内容
            priority: 消息优先级
        """
        if not self.connection_map:
            return

        connections = list(self.connection_map.values())

        for connection_info in connections:
            try:
                await self._send_message_to_connection(
                    connection_info, message, priority
                )
            except Exception as e:
                logger.error(
                    "Failed to broadcast message",
                    connection_id=connection_info.connection_id,
                    user_id=connection_info.user.id,
                    error=str(e),
                )

    async def _send_message_to_connection(
        self,
        connection_info: ConnectionInfo,
        message: Dict[str, Any],
        priority: MessagePriority = MessagePriority.NORMAL,
    ):
        """
        发送消息到特定连接

        Args:
            connection_info: 连接信息
            message: 消息内容
            priority: 消息优先级
        """
        # 添加消息序列号和时间戳
        enhanced_message = {
            **message,
            "sequence": connection_info.message_sequence,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "connection_id": connection_info.connection_id,
        }

        connection_info.message_sequence += 1

        # 创建待发送消息
        pending_msg = PendingMessage(enhanced_message, priority)

        try:
            # 发送消息
            await connection_info.websocket.send_text(json.dumps(enhanced_message))

            # 如果是高优先级消息，等待确认
            if priority in [MessagePriority.HIGH, MessagePriority.CRITICAL]:
                connection_info.sent_messages[pending_msg.id] = pending_msg

        except Exception as e:
            # 发送失败，添加到重试队列
            connection_info.pending_messages.append(pending_msg)
            raise e

    async def _handle_reconnection(
        self, connection_info: ConnectionInfo, reconnect_token: str
    ):
        """
        处理重连逻辑

        Args:
            connection_info: 新的连接信息
            reconnect_token: 重连令牌
        """
        # 这里可以实现重连令牌验证逻辑
        # 当前简化实现
        logger.info(
            "Handling reconnection",
            user_id=connection_info.user.id,
            reconnect_token=reconnect_token,
        )

    async def _send_offline_messages(self, connection_info: ConnectionInfo):
        """
        发送离线消息

        Args:
            connection_info: 连接信息
        """
        user_id = connection_info.user.id

        if user_id not in self.offline_messages:
            return

        messages = self.offline_messages[user_id]

        while messages:
            try:
                message = messages.popleft()
                await connection_info.websocket.send_text(json.dumps(message))
            except Exception as e:
                logger.error(
                    "Failed to send offline message",
                    user_id=user_id,
                    error=str(e),
                )
                break

        # 清理空队列
        if not messages:
            del self.offline_messages[user_id]

    async def _store_offline_message(
        self,
        user_id: int,
        message: Dict[str, Any],
        priority: MessagePriority = MessagePriority.NORMAL,
    ):
        """
        存储离线消息

        Args:
            user_id: 用户ID
            message: 消息内容
            priority: 消息优先级
        """
        if user_id not in self.offline_messages:
            self.offline_messages[user_id] = deque(maxlen=100)  # 限制离线消息数量

        offline_message = {
            **message,
            "offline": True,
            "stored_at": datetime.now(timezone.utc).isoformat(),
            "priority": priority.value,
        }

        # 根据优先级插入消息
        if priority == MessagePriority.CRITICAL:
            self.offline_messages[user_id].appendleft(offline_message)
        else:
            self.offline_messages[user_id].append(offline_message)

        logger.debug(
            "Offline message stored",
            user_id=user_id,
            priority=priority.name,
            queue_size=len(self.offline_messages[user_id]),
        )

    async def _heartbeat_loop(self, connection_info: ConnectionInfo):
        """
        心跳循环

        Args:
            connection_info: 连接信息
        """
        try:
            while connection_info.state == ConnectionState.CONNECTED:
                await asyncio.sleep(connection_info.heartbeat_interval)

                # 发送心跳
                ping_message = {
                    "event_type": "PING",
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                }

                try:
                    await connection_info.websocket.send_text(json.dumps(ping_message))
                    connection_info.last_ping = datetime.now(timezone.utc)

                    # 检查心跳超时
                    if (
                        connection_info.last_ping - connection_info.last_pong
                    ).total_seconds() > connection_info.heartbeat_timeout:
                        logger.warning(
                            "Heartbeat timeout",
                            connection_id=connection_info.connection_id,
                            user_id=connection_info.user.id,
                        )
                        break

                except Exception as e:
                    logger.error(
                        "Heartbeat failed",
                        connection_id=connection_info.connection_id,
                        error=str(e),
                    )
                    break

        except asyncio.CancelledError:
            logger.debug(
                "Heartbeat task cancelled",
                connection_id=connection_info.connection_id,
            )
        except Exception as e:
            logger.error(
                "Heartbeat loop error",
                connection_id=connection_info.connection_id,
                error=str(e),
            )

    async def _cleanup_loop(self):
        """
        清理循环 - 定期清理过期的连接和消息
        """
        try:
            while True:
                await asyncio.sleep(300)  # 每5分钟清理一次

                current_time = datetime.now(timezone.utc)

                # 清理过期的断开连接记录（24小时后）
                expired_users = [
                    user_id
                    for user_id, disconnect_time in self.disconnected_users.items()
                    if (current_time - disconnect_time).total_seconds() > 86400  # 24小时
                ]

                for user_id in expired_users:
                    del self.disconnected_users[user_id]
                    # 同时清理离线消息
                    if user_id in self.offline_messages:
                        del self.offline_messages[user_id]

                # 清理过期的离线消息（7天后）
                for user_id, messages in list(self.offline_messages.items()):
                    filtered_messages = deque()
                    for msg in messages:
                        stored_at = datetime.fromisoformat(
                            msg.get("stored_at", current_time.isoformat())
                        )
                        if (current_time - stored_at).total_seconds() < 604800:  # 7天
                            filtered_messages.append(msg)

                    if filtered_messages:
                        self.offline_messages[user_id] = filtered_messages
                    else:
                        del self.offline_messages[user_id]

                logger.debug(
                    "Cleanup completed",
                    active_connections=len(self.connection_map),
                    disconnected_users=len(self.disconnected_users),
                    offline_message_queues=len(self.offline_messages),
                )

        except asyncio.CancelledError:
            logger.debug("Cleanup task cancelled")
        except Exception as e:
            logger.error("Cleanup loop error", error=str(e))

    async def handle_message_ack(self, connection_id: str, message_id: str):
        """
        处理消息确认

        Args:
            connection_id: 连接ID
            message_id: 消息ID
        """
        if connection_id not in self.connection_map:
            return

        connection_info = self.connection_map[connection_id]

        if message_id in connection_info.sent_messages:
            del connection_info.sent_messages[message_id]
            logger.debug(
                "Message acknowledged",
                connection_id=connection_id,
                message_id=message_id,
            )

    async def handle_pong(self, connection_id: str):
        """
        处理心跳响应

        Args:
            connection_id: 连接ID
        """
        if connection_id not in self.connection_map:
            return

        connection_info = self.connection_map[connection_id]
        connection_info.last_pong = datetime.now(timezone.utc)

        logger.debug(
            "Pong received",
            connection_id=connection_id,
            user_id=connection_info.user.id,
        )

    def get_connection_stats(self) -> Dict[str, Any]:
        """
        获取连接统计信息

        Returns:
            Dict[str, Any]: 统计信息
        """
        return {
            "total_connections": len(self.connection_map),
            "active_users": len(self.active_connections),
            "disconnected_users": len(self.disconnected_users),
            "offline_message_queues": len(self.offline_messages),
            "total_offline_messages": sum(
                len(queue) for queue in self.offline_messages.values()
            ),
        }

    async def shutdown(self):
        """
        关闭WebSocket管理器，清理所有资源
        """
        logger.info("Shutting down WebSocket manager...")

        # 取消所有心跳任务
        for connection_info in self.connection_map.values():
            if connection_info.heartbeat_task:
                connection_info.heartbeat_task.cancel()
                try:
                    await connection_info.heartbeat_task
                except asyncio.CancelledError:
                    pass
                except Exception as e:
                    logger.warning(f"Error cancelling heartbeat task: {e}")

        # 取消清理任务
        if self.cleanup_task:
            self.cleanup_task.cancel()
            try:
                await self.cleanup_task
            except asyncio.CancelledError:
                pass
            except Exception as e:
                logger.warning(f"Error cancelling cleanup task: {e}")
            self.cleanup_task = None

        # 关闭所有WebSocket连接
        for connection_id in list(self.connection_map.keys()):
            try:
                connection_info = self.connection_map[connection_id]
                await connection_info.websocket.close()
            except Exception as e:
                logger.warning(f"Error closing websocket {connection_id}: {e}")

        # 清理所有数据结构
        self.connection_map.clear()
        self.active_connections.clear()
        self.disconnected_users.clear()
        self.offline_messages.clear()

        logger.info("WebSocket manager shutdown complete")


# 全局实例
ws_manager = WebSocketManager()
