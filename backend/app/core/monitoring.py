"""监控和指标收集服务

提供业务指标收集、性能监控和健康检查功能。
"""

import time
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional

import structlog

# 配置结构化日志
logger = structlog.get_logger()

# Prometheus client removed - using logging-only metrics
PROMETHEUS_AVAILABLE = False


class MetricsCollector:
    """指标收集器 - 简化版本，仅使用日志记录"""

    def __init__(self, registry=None):
        self._metrics_data = {}

    def record_order(self, status: str, symbol: str, side: str):
        """记录订单指标"""
        key = f"orders_{status}_{symbol}_{side}"
        self._metrics_data[key] = self._metrics_data.get(key, 0) + 1
        logger.info("Order metric recorded", status=status, symbol=symbol, side=side)

    def record_agent_processing_time(
        self, node_name: str, duration: float, status: str
    ):
        """记录Agent处理时间"""
        key = f"agent_processing_{node_name}_{status}"
        if key not in self._metrics_data:
            self._metrics_data[key] = {
                "count": 0,
                "total_time": 0,
                "avg_time": 0,
            }

        self._metrics_data[key]["count"] += 1
        self._metrics_data[key]["total_time"] += duration
        self._metrics_data[key]["avg_time"] = (
            self._metrics_data[key]["total_time"] / self._metrics_data[key]["count"]
        )

        logger.info(
            "Agent processing time recorded",
            node_name=node_name,
            duration=duration,
            status=status,
        )

    def set_active_websocket_connections(self, count: int):
        """设置活跃WebSocket连接数"""
        self._metrics_data["active_websocket_connections"] = count

    def record_api_request(
        self, method: str, endpoint: str, status_code: int, duration: float
    ):
        """记录API请求指标"""
        key = f"api_requests_{method}_{endpoint}_{status_code}"
        self._metrics_data[key] = self._metrics_data.get(key, 0) + 1

    def record_exchange_api_call(self, exchange: str, operation: str, status: str):
        """记录交易所API调用"""
        key = f"exchange_api_{exchange}_{operation}_{status}"
        self._metrics_data[key] = self._metrics_data.get(key, 0) + 1

    def record_cache_operation(self, operation: str, status: str):
        """记录缓存操作"""
        key = f"cache_{operation}_{status}"
        self._metrics_data[key] = self._metrics_data.get(key, 0) + 1

    def set_conditional_orders_active(self, count: int):
        """设置活跃条件订单数"""
        self._metrics_data["conditional_orders_active"] = count

    def set_system_health_score(self, score: float):
        """设置系统健康分数"""
        self._metrics_data["system_health_score"] = score

    def set_app_info(self, version: str, environment: str):
        """设置应用信息"""
        self._metrics_data["app_info"] = {
            "version": version,
            "environment": environment,
            "build_time": datetime.now(timezone.utc).isoformat(),
        }

    def get_metrics_summary(self) -> Dict[str, Any]:
        """获取指标摘要"""
        return {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "metrics": self._metrics_data.copy(),
            "prometheus_available": PROMETHEUS_AVAILABLE,
        }

    def get_prometheus_metrics(self) -> str:
        """获取Prometheus格式的指标 - 已禁用"""
        return "# Prometheus client not available\n"


class HealthChecker:
    """健康检查器"""

    def __init__(self):
        self.checks = {}

    def register_check(self, name: str, check_func):
        """注册健康检查"""
        self.checks[name] = check_func

    async def run_health_checks(self) -> Dict[str, Any]:
        """运行所有健康检查"""
        results = {}
        overall_healthy = True

        for name, check_func in self.checks.items():
            try:
                start_time = time.time()
                result = (
                    await check_func()
                    if hasattr(check_func, "__call__")
                    else check_func
                )
                duration = time.time() - start_time

                if isinstance(result, bool):
                    results[name] = {
                        "healthy": result,
                        "duration": duration,
                        "message": "OK" if result else "Failed",
                    }
                else:
                    results[name] = {
                        "healthy": result.get("healthy", False),
                        "duration": duration,
                        "message": result.get("message", "Unknown"),
                        "details": result.get("details", {}),
                    }

                if not results[name]["healthy"]:
                    overall_healthy = False

            except Exception as e:
                results[name] = {
                    "healthy": False,
                    "duration": 0,
                    "message": f"Check failed: {str(e)}",
                }
                overall_healthy = False

        return {
            "healthy": overall_healthy,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "checks": results,
        }


class PerformanceMonitor:
    """性能监控器"""

    def __init__(self):
        self.active_requests = {}
        self.request_history = []
        self.max_history = 1000

    def start_request(self, request_id: str, endpoint: str, method: str):
        """开始请求监控"""
        self.active_requests[request_id] = {
            "endpoint": endpoint,
            "method": method,
            "start_time": time.time(),
            "timestamp": datetime.now(timezone.utc),
        }

    def end_request(
        self, request_id: str, status_code: int
    ) -> Optional[Dict[str, Any]]:
        """结束请求监控"""
        if request_id not in self.active_requests:
            return None

        request_info = self.active_requests.pop(request_id)
        duration = time.time() - request_info["start_time"]

        result = {
            "request_id": request_id,
            "endpoint": request_info["endpoint"],
            "method": request_info["method"],
            "duration": duration,
            "status_code": status_code,
            "timestamp": request_info["timestamp"],
        }

        # 添加到历史记录
        self.request_history.append(result)
        if len(self.request_history) > self.max_history:
            self.request_history.pop(0)

        return result

    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        if not self.request_history:
            return {"message": "No request history available"}

        durations = [req["duration"] for req in self.request_history]

        return {
            "total_requests": len(self.request_history),
            "active_requests": len(self.active_requests),
            "avg_duration": sum(durations) / len(durations),
            "min_duration": min(durations),
            "max_duration": max(durations),
            "recent_requests": self.request_history[-10:],  # 最近10个请求
        }


# 全局实例
metrics_collector = MetricsCollector()
health_checker = HealthChecker()
performance_monitor = PerformanceMonitor()


# 便捷函数
def record_order_metric(status: str, symbol: str, side: str):
    """记录订单指标"""
    metrics_collector.record_order(status, symbol, side)


def record_agent_metric(node_name: str, duration: float, status: str = "success"):
    """记录Agent指标"""
    metrics_collector.record_agent_processing_time(node_name, duration, status)


def record_api_metric(method: str, endpoint: str, status_code: int, duration: float):
    """记录API指标"""
    metrics_collector.record_api_request(method, endpoint, status_code, duration)


async def get_system_health() -> Dict[str, Any]:
    """获取系统健康状态"""
    return await health_checker.run_health_checks()


def get_metrics_data() -> Dict[str, Any]:
    """获取指标数据"""
    return metrics_collector.get_metrics_summary()
