from typing import AsyncGenerator

from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import declarative_base, sessionmaker

from .config import settings

# 声明基础模型类
Base = declarative_base()

# 创建异步引擎
engine_kwargs = {
    "echo": settings.db.echo,
}

# 只有当使用PostgreSQL时，才添加连接池参数
if "postgresql" in settings.db.url:
    engine_kwargs.update(
        {
            "pool_size": settings.db.pool_size,
            "max_overflow": settings.db.max_overflow,
            "pool_timeout": settings.db.pool_timeout,
            "pool_recycle": settings.db.pool_recycle,
        }
    )

engine = create_async_engine(settings.db.url, **engine_kwargs)

# 创建会话工厂
AsyncSessionLocal = sessionmaker(
    autocommit=False,
    autoflush=False,
    bind=engine,
    class_=AsyncSession,
    expire_on_commit=False,
)


# 创建数据库依赖
async def get_db() -> AsyncGenerator[AsyncSession, None]:
    """
    获取数据库会话的异步生成器

    Yields:
        AsyncSession: SQLAlchemy异步会话
    """
    session = AsyncSessionLocal()
    try:
        yield session
        await session.commit()
    except:
        await session.rollback()
        raise
    finally:
        await session.close()


async def get_async_session() -> AsyncSession:
    """
    获取异步数据库会话（用于测试和工厂类）

    Returns:
        AsyncSession: SQLAlchemy异步会话
    """
    return AsyncSessionLocal()
