from typing import List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from ..core.auth import get_current_user
from ..core.database import get_db
from ..core.models import ConditionalOrder, User
from ..core.schemas import (
    ConditionalOrderCreate,
    ConditionalOrderResponse,
    ConditionalOrderUpdate,
)

router = APIRouter(prefix="/conditional-orders", tags=["conditional-orders"])


@router.get("/", response_model=List[ConditionalOrderResponse])
async def get_conditional_orders(
    current_user: User = Depends(get_current_user),
    symbol: Optional[str] = Query(None, description="过滤特定交易对"),
    status: Optional[str] = Query(None, description="过滤特定状态"),
    limit: int = Query(50, ge=1, le=100, description="返回结果数量限制"),
    offset: int = Query(0, ge=0, description="分页偏移量"),
    db: AsyncSession = Depends(get_db),
):
    """
    获取用户的条件订单列表
    """
    query = select(ConditionalOrder).where(ConditionalOrder.user_id == current_user.id)

    # 应用过滤条件
    if symbol:
        query = query.where(ConditionalOrder.symbol == symbol)
    if status:
        query = query.where(ConditionalOrder.status == status)

    # 应用分页
    query = query.offset(offset).limit(limit)

    result = await db.execute(query)
    conditional_orders = result.scalars().all()

    return conditional_orders


@router.get("/{order_id}", response_model=ConditionalOrderResponse)
async def get_conditional_order(
    order_id: UUID,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """
    获取单个条件订单详情
    """
    query = select(ConditionalOrder).where(
        ConditionalOrder.id == order_id,
        ConditionalOrder.user_id == current_user.id,
    )

    result = await db.execute(query)
    conditional_order = result.scalar_one_or_none()

    if not conditional_order:
        raise HTTPException(status_code=404, detail="条件订单未找到")

    return conditional_order


@router.post("/", response_model=ConditionalOrderResponse)
async def create_conditional_order(
    order_data: ConditionalOrderCreate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """
    创建新的条件订单
    """
    conditional_order = ConditionalOrder(user_id=current_user.id, **order_data.dict())

    db.add(conditional_order)
    await db.commit()
    await db.refresh(conditional_order)

    return conditional_order


@router.put("/{order_id}", response_model=ConditionalOrderResponse)
async def update_conditional_order(
    order_id: UUID,
    order_data: ConditionalOrderUpdate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """
    更新条件订单
    """
    query = select(ConditionalOrder).where(
        ConditionalOrder.id == order_id, ConditionalOrder.user_id == current_user.id
    )

    result = await db.execute(query)
    conditional_order = result.scalar_one_or_none()

    if not conditional_order:
        raise HTTPException(status_code=404, detail="条件订单未找到")

    # 更新字段
    update_data = order_data.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(conditional_order, field, value)

    await db.commit()
    await db.refresh(conditional_order)

    return conditional_order


@router.delete("/{order_id}")
async def delete_conditional_order(
    order_id: UUID,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """
    删除条件订单
    """
    query = select(ConditionalOrder).where(
        ConditionalOrder.id == order_id, ConditionalOrder.user_id == current_user.id
    )

    result = await db.execute(query)
    conditional_order = result.scalar_one_or_none()

    if not conditional_order:
        raise HTTPException(status_code=404, detail="条件订单未找到")

    await db.delete(conditional_order)
    await db.commit()

    return {"message": "条件订单已删除"}
