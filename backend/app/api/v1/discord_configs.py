"""
Discord配置管理API端点
基于简化设计原则，专注于Discord配置的CRUD操作
"""
import uuid
from typing import List

import structlog
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from ...core.auth import get_current_user
from ...core.database import get_db
from ...core.models import DiscordConfig, User
from ...core.schemas import DiscordConfigRequest, DiscordConfigUpdateRequest, DiscordConfigResponse
from ...core.security import encrypt_sensitive_data, decrypt_sensitive_data
from ...services.discord_config_manager import (
    get_discord_config_manager,
    on_config_created,
    on_config_updated,
    on_config_deleted
)

logger = structlog.get_logger()

router = APIRouter(prefix="/discord-configs", tags=["Discord配置"])


@router.get("", response_model=List[DiscordConfigResponse])
async def list_discord_configs(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """获取用户的Discord配置列表"""
    query = select(DiscordConfig).where(DiscordConfig.user_id == current_user.id)
    result = await db.execute(query)
    configs = result.scalars().all()

    return [DiscordConfigResponse.from_orm_with_token_flag(config) for config in configs]


@router.post("", response_model=DiscordConfigResponse)
async def create_discord_config(
    request: DiscordConfigRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """创建Discord配置"""
    # 检查配置名称是否重复
    existing = await db.execute(
        select(DiscordConfig).where(
            DiscordConfig.user_id == current_user.id,
            DiscordConfig.source_name == request.source_name
        )
    )
    if existing.scalar_one_or_none():
        raise HTTPException(status_code=400, detail="配置名称已存在")

    # 加密token
    encrypted_token = encrypt_sensitive_data(request.token)

    # 创建配置
    config = DiscordConfig(
        user_id=current_user.id,
        source_name=request.source_name,
        enabled=request.enabled,
        encrypted_token=encrypted_token,
        server_ids=request.server_ids,
        channel_ids=request.channel_ids,
        author_ids=request.author_ids,
        allowed_message_types=request.allowed_message_types,
    )

    db.add(config)
    await db.commit()
    await db.refresh(config)

    # 通知配置管理器
    try:
        await on_config_created(config)
    except Exception as e:
        logger.warning(f"配置管理器事件处理失败: {e}")

    logger.info("Discord配置创建成功", config_id=str(config.id), user_id=str(current_user.id))
    return DiscordConfigResponse.from_orm_with_token_flag(config)


@router.get("/{config_id}", response_model=DiscordConfigResponse)
async def get_discord_config(
    config_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """获取特定Discord配置"""
    config = await get_user_discord_config(db, config_id, current_user.id)
    return DiscordConfigResponse.from_orm_with_token_flag(config)


@router.put("/{config_id}", response_model=DiscordConfigResponse)
async def update_discord_config(
    config_id: uuid.UUID,
    request: DiscordConfigUpdateRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """更新Discord配置"""
    config = await get_user_discord_config(db, config_id, current_user.id)

    # 检查名称冲突（排除当前配置）
    if request.source_name != config.source_name:
        existing = await db.execute(
            select(DiscordConfig).where(
                DiscordConfig.user_id == current_user.id,
                DiscordConfig.source_name == request.source_name,
                DiscordConfig.id != config_id
            )
        )
        if existing.scalar_one_or_none():
            raise HTTPException(status_code=400, detail="配置名称已存在")

    # 更新配置
    config.source_name = request.source_name
    config.enabled = request.enabled
    # 只有提供了新token时才更新token
    if request.token:
        config.encrypted_token = encrypt_sensitive_data(request.token)
    config.server_ids = request.server_ids
    config.channel_ids = request.channel_ids
    config.author_ids = request.author_ids
    config.allowed_message_types = request.allowed_message_types

    await db.commit()
    await db.refresh(config)

    # 通知配置管理器
    try:
        await on_config_updated(config)
    except Exception as e:
        logger.warning(f"配置管理器事件处理失败: {e}")

    logger.info("Discord配置更新成功", config_id=str(config.id), user_id=str(current_user.id))
    return DiscordConfigResponse.from_orm_with_token_flag(config)


@router.patch("/{config_id}/toggle", response_model=DiscordConfigResponse)
async def toggle_discord_config(
    config_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """切换Discord配置的启用/禁用状态"""
    config = await get_user_discord_config(db, config_id, current_user.id)
    
    # 切换启用状态
    config.enabled = not config.enabled
    
    await db.commit()
    await db.refresh(config)

    # 通知配置管理器
    try:
        await on_config_updated(config)
    except Exception as e:
        logger.warning(f"配置管理器事件处理失败: {e}")

    action = "启用" if config.enabled else "禁用"
    logger.info(f"Discord配置{action}成功", config_id=str(config.id), user_id=str(current_user.id))
    return DiscordConfigResponse.from_orm_with_token_flag(config)


@router.delete("/{config_id}")
async def delete_discord_config(
    config_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """删除Discord配置"""
    config = await get_user_discord_config(db, config_id, current_user.id)
    config_id_str = str(config.id)

    await db.delete(config)
    await db.commit()

    # 通知配置管理器
    try:
        await on_config_deleted(config_id_str)
    except Exception as e:
        logger.warning(f"配置管理器事件处理失败: {e}")

    logger.info("Discord配置删除成功", config_id=config_id_str, user_id=str(current_user.id))
    return {"success": True, "message": "配置已删除"}


# 辅助函数
async def get_user_discord_config(db: AsyncSession, config_id: uuid.UUID, user_id: uuid.UUID):
    """获取用户的Discord配置"""
    query = select(DiscordConfig).where(
        DiscordConfig.id == config_id,
        DiscordConfig.user_id == user_id
    )
    result = await db.execute(query)
    config = result.scalar_one_or_none()

    if not config:
        raise HTTPException(status_code=404, detail="配置不存在")

    return config
