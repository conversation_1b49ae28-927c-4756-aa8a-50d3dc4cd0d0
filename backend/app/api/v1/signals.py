"""
信号管理API接口

提供信号的CRUD操作，包括：
- 获取信号列表（分页、筛选）
- 获取单个信号详情
- 创建新信号（手动）
- 更新信号状态
- 删除信号
- 获取信号统计信息
"""

from typing import List, Optional, Dict, Any
import uuid
from datetime import datetime, timezone, timedelta
from decimal import Decimal

import structlog
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy import and_, desc, func, select, update, delete, or_
from sqlalchemy.exc import IntegrityError
from sqlalchemy.ext.asyncio import AsyncSession

from ...core.auth import get_current_user
from ...core.database import get_db
from ...core.models import Signal, User
from ...core.utils import validate_uuid, safe_get_user_resource
from ...core.schemas import (
    APIResponse,
    PaginatedResponse,
    SignalQueryParams,
    CreateSignalRequest,
    UpdateSignalRequest,
    SignalResponse,
    SignalDetailResponse,
    SignalStatsResponse,
    PlatformType,
    MessageType,
    AIParseStatus,
    MessageTypeAI,
    LLMService,
)
from ...services.signal_service import signal_service

# 配置结构化日志
logger = structlog.get_logger()

router = APIRouter(prefix="/signals", tags=["signals"])


@router.get("", response_model=APIResponse[PaginatedResponse[SignalResponse]])
async def get_signals(
    current_user: User = Depends(get_current_user),
    platform: Optional[str] = Query(None, description="平台筛选"),
    channel_id: Optional[str] = Query(None, description="频道ID筛选"),
    is_processed: Optional[bool] = Query(None, description="处理状态筛选"),
    confidence_min: Optional[float] = Query(None, ge=0, le=1, description="最小置信度"),
    confidence_max: Optional[float] = Query(None, ge=0, le=1, description="最大置信度"),
    ai_parse_status: Optional[str] = Query(None, description="AI解析状态"),
    message_type_ai: Optional[str] = Query(None, description="AI识别的消息类型"),
    llm_service: Optional[str] = Query(None, description="LLM服务"),
    search: Optional[str] = Query(None, description="搜索关键词（在信号内容中搜索）"),
    # 向后兼容
    signal_strength_min: Optional[float] = Query(
        None, ge=0, le=1, description="最小信号强度（已废弃，请使用confidence_min）"
    ),
    date_from: Optional[datetime] = Query(None, description="开始日期"),
    date_to: Optional[datetime] = Query(None, description="结束日期"),
    page: int = Query(default=1, ge=1, description="页码"),
    size: int = Query(default=20, ge=1, le=100, description="每页大小"),
    sort_by: str = Query(default="created_at", description="排序字段"),
    sort_order: str = Query(default="desc", regex="^(asc|desc)$", description="排序方向"),
    db: AsyncSession = Depends(get_db),
):
    """
    获取信号列表（分页）

    支持多种筛选条件和排序选项
    """
    try:
        # 构建查询条件
        conditions = [Signal.user_id == current_user.id]

        if platform:
            conditions.append(Signal.platform == platform)
        if channel_id:
            conditions.append(Signal.channel_id == channel_id)
        if is_processed is not None:
            conditions.append(Signal.is_processed == is_processed)
        # 处理置信度筛选（向后兼容）
        confidence_min_value = confidence_min or signal_strength_min
        if confidence_min_value is not None:
            conditions.append(Signal.confidence >= confidence_min_value)
        if confidence_max is not None:
            conditions.append(Signal.confidence <= confidence_max)

        # 处理AI解析相关筛选
        if ai_parse_status:
            conditions.append(Signal.ai_parse_status == ai_parse_status)
        if message_type_ai:
            conditions.append(Signal.message_type_ai == message_type_ai)
        if llm_service:
            conditions.append(Signal.llm_service == llm_service)
        if date_from:
            conditions.append(Signal.created_at >= date_from)
        if date_to:
            conditions.append(Signal.created_at <= date_to)
        
        # 处理搜索关键词
        if search:
            search_condition = or_(
                Signal.content.ilike(f"%{search}%"),
                Signal.channel_name.ilike(f"%{search}%"),
                Signal.author_name.ilike(f"%{search}%")
            )
            conditions.append(search_condition)

        # 构建基础查询
        query = select(Signal).where(and_(*conditions))

        # 添加排序
        # 映射API字段名到模型字段名
        sort_field_mapping = {
            "signal_strength": "confidence",
            "created_at": "created_at",
            "updated_at": "updated_at",
            "platform": "platform",
            "is_processed": "is_processed",
        }

        actual_sort_field = sort_field_mapping.get(sort_by, sort_by)

        if sort_order == "desc":
            query = query.order_by(desc(getattr(Signal, actual_sort_field)))
        else:
            query = query.order_by(getattr(Signal, actual_sort_field))

        # 获取总数
        count_query = select(func.count()).select_from(Signal).where(and_(*conditions))
        total_result = await db.execute(count_query)
        total = total_result.scalar()

        # 分页
        offset = (page - 1) * size
        query = query.offset(offset).limit(size)

        # 执行查询
        result = await db.execute(query)
        signals = result.scalars().all()

        # 转换为响应模型
        signal_responses = []
        for signal in signals:
            signal_responses.append(
                SignalResponse(
                    id=str(signal.id),
                    platform=signal.platform,
                    channel_name=signal.channel_name,
                    author_name=signal.author_name,
                    content=signal.content,
                    message_type=signal.message_type,
                    confidence=float(signal.confidence) if signal.confidence else None,
                    signal_strength=float(signal.confidence)
                    if signal.confidence
                    else None,
                    ai_parse_status=AIParseStatus(signal.ai_parse_status)
                    if signal.ai_parse_status
                    else AIParseStatus.PENDING,
                    message_type_ai=MessageTypeAI(signal.message_type_ai)
                    if signal.message_type_ai
                    else MessageTypeAI.AMBIGUOUS,
                    llm_service=LLMService(signal.llm_service)
                    if signal.llm_service
                    else None,
                    is_processed=signal.is_processed,
                    created_at=signal.created_at,
                )
            )

        # 构建分页响应
        paginated_response = PaginatedResponse(
            items=signal_responses,
            total=total or 0,
            page=page,
            size=size,
            has_next=offset + size < (total or 0),
            has_prev=page > 1,
        )

        await logger.ainfo(
            "获取信号列表成功",
            user_id=str(current_user.id),
            total=total,
            page=page,
            size=size,
            filters={
                "platform": platform,
                "channel_id": channel_id,
                "is_processed": is_processed,
                "confidence_min": confidence_min,
                "confidence_max": confidence_max,
                "ai_parse_status": ai_parse_status,
                "message_type_ai": message_type_ai,
                "llm_service": llm_service,
                "search": search,
                "signal_strength_min": signal_strength_min,  # 向后兼容
            },
        )

        return APIResponse.success_response(
            data=paginated_response, message=f"成功获取 {len(signal_responses)} 条信号记录"
        )

    except Exception as e:
        await logger.aerror(
            "获取信号列表失败", user_id=str(current_user.id), error=str(e), exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="获取信号列表失败"
        )


@router.get("/stats", response_model=APIResponse[SignalStatsResponse])
async def get_signal_stats(
    current_user: User = Depends(get_current_user),
    days: int = Query(default=30, ge=1, le=365, description="统计天数"),
    db: AsyncSession = Depends(get_db),
):
    """
    获取信号统计信息
    """
    try:
        # 计算时间范围
        end_date = datetime.now(timezone.utc).replace(tzinfo=None)
        start_date = end_date.replace(hour=0, minute=0, second=0, microsecond=0)
        start_date = start_date - timedelta(days=days - 1)

        # 基础条件
        base_conditions = [
            Signal.user_id == current_user.id,
            Signal.created_at >= start_date,
        ]

        # 总信号数
        total_query = (
            select(func.count()).select_from(Signal).where(and_(*base_conditions))
        )
        total_result = await db.execute(total_query)
        total_signals = total_result.scalar()

        # 已处理信号数
        processed_conditions = base_conditions + [Signal.is_processed == True]
        processed_query = (
            select(func.count()).select_from(Signal).where(and_(*processed_conditions))
        )
        processed_result = await db.execute(processed_query)
        processed_signals = processed_result.scalar()

        # 平台分布
        platform_query = (
            select(Signal.platform, func.count())
            .where(and_(*base_conditions))
            .group_by(Signal.platform)
        )
        platform_result = await db.execute(platform_query)
        platform_breakdown = {row[0]: row[1] for row in platform_result.fetchall()}

        # 平均置信度
        avg_confidence_query = select(func.avg(Signal.confidence)).where(
            and_(*base_conditions + [Signal.confidence.isnot(None)])
        )
        avg_result = await db.execute(avg_confidence_query)
        avg_confidence = avg_result.scalar()
        if avg_confidence:
            avg_confidence = float(avg_confidence)

        # AI解析状态分布
        ai_status_query = (
            select(Signal.ai_parse_status, func.count())
            .where(and_(*base_conditions))
            .group_by(Signal.ai_parse_status)
        )
        ai_status_result = await db.execute(ai_status_query)
        ai_parse_status_breakdown = {
            row[0]: row[1] for row in ai_status_result.fetchall()
        }

        # 消息类型分布
        msg_type_query = (
            select(Signal.message_type_ai, func.count())
            .where(and_(*base_conditions))
            .group_by(Signal.message_type_ai)
        )
        msg_type_result = await db.execute(msg_type_query)
        message_type_breakdown = {row[0]: row[1] for row in msg_type_result.fetchall()}

        # LLM服务分布
        llm_service_query = (
            select(Signal.llm_service, func.count())
            .where(and_(*base_conditions + [Signal.llm_service.isnot(None)]))
            .group_by(Signal.llm_service)
        )
        llm_service_result = await db.execute(llm_service_query)
        llm_service_breakdown = {
            row[0]: row[1] for row in llm_service_result.fetchall()
        }

        # 最近活动（最近7天每天的信号数量）
        recent_activity = []
        for i in range(7):
            day_start = end_date.replace(hour=0, minute=0, second=0, microsecond=0)
            day_start = day_start - timedelta(days=i)
            day_end = day_start.replace(
                hour=23, minute=59, second=59, microsecond=999999
            )

            day_query = (
                select(func.count())
                .select_from(Signal)
                .where(
                    and_(
                        Signal.user_id == current_user.id,
                        Signal.created_at >= day_start,
                        Signal.created_at <= day_end,
                    )
                )
            )
            day_result = await db.execute(day_query)
            day_count = day_result.scalar()

            recent_activity.append(
                {"date": day_start.strftime("%Y-%m-%d"), "count": day_count}
            )

        # 构建响应
        stats_response = SignalStatsResponse(
            total_signals=total_signals or 0,
            processed_signals=processed_signals or 0,
            platform_breakdown=platform_breakdown,
            avg_confidence=avg_confidence,
            avg_signal_strength=avg_confidence,  # 向后兼容
            ai_parse_status_breakdown=ai_parse_status_breakdown,
            message_type_breakdown=message_type_breakdown,
            llm_service_breakdown=llm_service_breakdown,
            recent_activity=recent_activity,
        )

        await logger.ainfo(
            "获取信号统计成功",
            user_id=str(current_user.id),
            days=days,
            total_signals=total_signals,
            processed_signals=processed_signals,
        )

        return APIResponse.success_response(data=stats_response, message="成功获取信号统计信息")

    except Exception as e:
        await logger.aerror(
            "获取信号统计失败",
            user_id=str(current_user.id),
            days=days,
            error=str(e),
            exc_info=True,
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="获取信号统计失败"
        )


@router.get("/{signal_id}", response_model=APIResponse[SignalDetailResponse])
async def get_signal_detail(
    signal_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """
    获取单个信号详情
    """
    try:
        # 验证信号ID格式并获取信号
        signal_uuid = validate_uuid(signal_id, "signal ID")
        signal = await safe_get_user_resource(db, Signal, signal_uuid, current_user.id, "signal")

        # 转换为详情响应模型
        signal_detail = SignalDetailResponse(
            id=str(signal.id),
            platform=signal.platform,
            channel_name=signal.channel_name,
            author_name=signal.author_name,
            content=signal.content,
            message_type=signal.message_type,
            confidence=float(signal.confidence) if signal.confidence else None,
            signal_strength=float(signal.confidence) if signal.confidence else None,
            ai_parse_status=AIParseStatus(signal.ai_parse_status)
            if signal.ai_parse_status
            else AIParseStatus.PENDING,
            message_type_ai=MessageTypeAI(signal.message_type_ai)
            if signal.message_type_ai
            else MessageTypeAI.AMBIGUOUS,
            llm_service=LLMService(signal.llm_service) if signal.llm_service else None,
            is_processed=signal.is_processed,
            created_at=signal.created_at,
            raw_content=signal.raw_content,
            metadata=signal.signal_metadata,
            processed_at=signal.processed_at,
            platform_message_id=signal.platform_message_id,
            channel_id=signal.channel_id,
            author_id=signal.author_id,
        )

        await logger.ainfo(
            "获取信号详情成功", user_id=str(current_user.id), signal_id=signal_id
        )

        return APIResponse.success_response(data=signal_detail, message="成功获取信号详情")

    except HTTPException:
        raise
    except Exception as e:
        await logger.aerror(
            "获取信号详情失败",
            user_id=str(current_user.id),
            signal_id=signal_id,
            error=str(e),
            exc_info=True,
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="获取信号详情失败"
        )


@router.post("", response_model=APIResponse[SignalResponse])
async def create_signal(
    request: CreateSignalRequest,
    auto_trigger_agent: bool = Query(False, description="是否自动触发Agent处理"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """
    创建新信号（手动）

    支持去重检查和可选的Agent自动处理
    """
    # 提前提取用户ID，避免在异常处理中访问数据库对象
    user_id_str = str(current_user.id)

    try:
        # 使用统一的信号服务创建信号
        response = await signal_service.create_manual_signal(
            request=request,
            user=current_user,
            db=db,
            auto_trigger_agent=auto_trigger_agent
        )

        return response

    except IntegrityError as e:
        from app.core.utils import safe_rollback_db
        await safe_rollback_db(db)
        await logger.aerror(
            "创建信号失败 - 数据完整性错误",
            user_id=user_id_str,
            error=str(e),
            request_data=request.model_dump(),
        )
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="信号数据不符合要求")
    except Exception as e:
        from app.core.utils import safe_rollback_db
        await safe_rollback_db(db)
        await logger.aerror(
            "创建信号失败",
            user_id=user_id_str,
            error=str(e),
            request_data=request.model_dump(),
            exc_info=True,
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="创建信号失败"
        )


@router.get("/{signal_id}/traces", response_model=APIResponse[list])
async def get_signal_traces(
    signal_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """获取信号关联的Agent追踪记录"""
    from app.core.models import AgentExecutionTrace, Signal
    from sqlalchemy import select, and_
    import uuid

    # 提前提取用户ID，避免在异常处理中访问数据库对象
    user_id_str = str(current_user.id)

    try:
        signal_uuid = uuid.UUID(signal_id)

        # 验证信号存在且属于当前用户
        signal_stmt = select(Signal).where(
            and_(
                Signal.id == signal_uuid,
                Signal.user_id == current_user.id
            )
        )
        signal_result = await db.execute(signal_stmt)
        signal = signal_result.scalar_one_or_none()

        if not signal:
            raise HTTPException(status_code=404, detail="Signal not found")

        # 查询追踪记录
        traces_stmt = select(AgentExecutionTrace).where(
            and_(
                AgentExecutionTrace.signal_id == signal_uuid,
                AgentExecutionTrace.user_id == current_user.id
            )
        ).order_by(AgentExecutionTrace.execution_order)

        traces_result = await db.execute(traces_stmt)
        traces = traces_result.scalars().all()

        # 转换为响应格式
        traces_data = []
        for trace in traces:
            traces_data.append({
                "id": str(trace.id),
                "task_id": str(trace.task_id),
                "signal_id": str(trace.signal_id),
                "node_name": trace.node_name,
                "execution_order": trace.execution_order,
                "status": trace.status,
                "started_at": trace.started_at.isoformat() if trace.started_at else None,
                "completed_at": trace.completed_at.isoformat() if trace.completed_at else None,
                "duration_ms": trace.duration_ms,
                "input_data": trace.input_data,
                "output_data": trace.output_data,
                "error_data": trace.error_data,
                "performance_metrics": trace.performance_metrics,
                "created_at": trace.created_at.isoformat(),
                "updated_at": trace.updated_at.isoformat()
            })

        # 包含信号基本信息
        response_data = {
            "signal": {
                "id": str(signal.id),
                "content": signal.content,
                "platform": signal.platform,
                "channel_name": signal.channel_name,
                "author_name": signal.author_name,
                "agent_task_id": str(signal.agent_task_id) if signal.agent_task_id else None,
                "agent_processing_status": signal.agent_processing_status,
                "created_at": signal.created_at.isoformat()
            },
            "traces": traces_data,
            "total_traces": len(traces_data)
        }

        return APIResponse.success_response(data=response_data)

    except ValueError:
        raise HTTPException(status_code=400, detail="Invalid signal ID format")
    except HTTPException:
        raise
    except Exception as e:
        await logger.aerror(
            "获取信号追踪记录失败",
            user_id=user_id_str,
            signal_id=signal_id,
            error=str(e),
            exc_info=True,
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="获取追踪记录失败"
        )


@router.post("/process", response_model=APIResponse[Dict[str, Any]])
async def process_signal_with_agent(
    request: CreateSignalRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """
    处理信号并自动触发Agent工作流程

    完整的信号处理流程：
    1. 信号接收与入库
    2. 去重检查
    3. 如果是新信号，自动启动Agent工作流程
    4. 返回信号信息和Agent任务信息
    """
    try:
        # 处理信号并自动触发Agent
        response_data = await signal_service.process_signal_with_agent(
            request=request,
            user=current_user,
            db=db
        )

        return APIResponse.success_response(
            data=response_data,
            message=response_data.get("message", "信号处理完成")
        )

    except Exception as e:
        # 尝试回滚数据库，但如果失败也不要抛出异常
        try:
            await db.rollback()
        except Exception:
            # 忽略回滚失败，因为可能是会话已经过期
            pass

        await logger.aerror(
            "信号处理失败",
            error=str(e),
            exc_info=True,
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="信号处理失败"
        )


@router.put("/{signal_id}", response_model=APIResponse[SignalResponse])
async def update_signal(
    signal_id: str,
    request: UpdateSignalRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """
    更新信号状态
    """
    try:
        # 验证UUID格式
        try:
            signal_uuid = uuid.UUID(signal_id)
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="无效的信号ID格式"
            )

        # 查询信号
        query = select(Signal).where(
            and_(Signal.id == signal_uuid, Signal.user_id == current_user.id)
        )
        result = await db.execute(query)
        signal = result.scalar_one_or_none()

        if not signal:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="信号不存在")

        # 更新字段
        update_data: Dict[str, Any] = {}
        if request.is_processed is not None:
            update_data["is_processed"] = request.is_processed
            if request.is_processed:
                update_data["processed_at"] = datetime.now(timezone.utc).replace(
                    tzinfo=None
                )

        # 处理置信度更新（向后兼容）
        confidence_value = request.confidence or request.signal_strength
        if confidence_value is not None:
            update_data["confidence"] = Decimal(str(confidence_value))

        # 处理AI解析相关字段更新
        if request.ai_parse_status is not None:
            update_data["ai_parse_status"] = request.ai_parse_status

        if request.message_type_ai is not None:
            update_data["message_type_ai"] = request.message_type_ai

        if request.llm_service is not None:
            update_data["llm_service"] = request.llm_service

        if request.metadata is not None:
            update_data["signal_metadata"] = request.metadata

        # 处理AI分析结果
        if request.ai_analysis is not None:
            current_metadata = signal.signal_metadata or {}
            current_metadata["ai_analysis"] = request.ai_analysis
            update_data["signal_metadata"] = current_metadata

        if update_data:
            # 执行更新
            update_query = (
                update(Signal).where(Signal.id == signal_uuid).values(**update_data)
            )
            await db.execute(update_query)
            await db.commit()

            # 重新查询更新后的数据
            result = await db.execute(query)
            signal = result.scalar_one()

        # 转换为响应模型
        signal_response = SignalResponse(
            id=str(signal.id),
            platform=signal.platform,
            channel_name=signal.channel_name,
            author_name=signal.author_name,
            content=signal.content,
            message_type=signal.message_type,
            confidence=float(signal.confidence) if signal.confidence else None,
            signal_strength=float(signal.confidence) if signal.confidence else None,
            ai_parse_status=AIParseStatus(signal.ai_parse_status)
            if signal.ai_parse_status
            else AIParseStatus.PENDING,
            message_type_ai=MessageTypeAI(signal.message_type_ai)
            if signal.message_type_ai
            else MessageTypeAI.AMBIGUOUS,
            llm_service=LLMService(signal.llm_service) if signal.llm_service else None,
            is_processed=signal.is_processed,
            created_at=signal.created_at,
        )

        await logger.ainfo(
            "更新信号成功",
            user_id=str(current_user.id),
            signal_id=signal_id,
            update_data=update_data,
        )

        return APIResponse.success_response(data=signal_response, message="成功更新信号")

    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        await logger.aerror(
            "更新信号失败",
            user_id=str(current_user.id),
            signal_id=signal_id,
            error=str(e),
            request_data=request.model_dump(),
            exc_info=True,
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="更新信号失败"
        )


@router.delete("/{signal_id}", response_model=APIResponse[None])
async def delete_signal(
    signal_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """
    删除信号
    """
    try:
        # 验证UUID格式
        try:
            signal_uuid = uuid.UUID(signal_id)
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="无效的信号ID格式"
            )

        # 检查信号是否存在
        query = select(Signal).where(
            and_(Signal.id == signal_uuid, Signal.user_id == current_user.id)
        )
        result = await db.execute(query)
        signal = result.scalar_one_or_none()

        if not signal:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="信号不存在")

        # 删除信号
        delete_query = delete(Signal).where(Signal.id == signal_uuid)
        await db.execute(delete_query)
        await db.commit()

        await logger.ainfo("删除信号成功", user_id=str(current_user.id), signal_id=signal_id)

        return APIResponse.success_response(data=None, message="成功删除信号")

    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        await logger.aerror(
            "删除信号失败",
            user_id=str(current_user.id),
            signal_id=signal_id,
            error=str(e),
            exc_info=True,
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="删除信号失败"
        )
