"""
Agent执行追踪API路由

提供Agent工作流执行追踪的查询接口：
- 获取任务执行追踪详情
- 获取用户最近执行记录
- 支持分页和过滤
"""

import uuid
from typing import List, Optional

import structlog
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.core.schemas import (
    AgentExecutionTraceResponse,
    TaskExecutionTracesResponse,
    RecentExecutionsResponse,
    RecentExecutionSummary,
)
from app.services.agent_trace_service import trace_service
from app.core.auth import get_current_user
from app.core.models import User

logger = structlog.get_logger(__name__)

router = APIRouter(prefix="/traces", tags=["agent-traces"])


@router.get("/task/{task_id}", response_model=TaskExecutionTracesResponse)
async def get_task_execution_traces(
    task_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """
    获取指定任务的执行追踪详情
    
    返回任务的完整执行时间线，包括每个节点的执行状态、时长和数据。
    """
    try:
        logger.info(
            "获取任务执行追踪",
            task_id=str(task_id),
            user_id=str(current_user.id)
        )
        
        # 获取任务的所有追踪记录
        traces = await trace_service.get_task_traces(db, task_id)
        
        if not traces:
            raise HTTPException(
                status_code=404,
                detail=f"未找到任务 {task_id} 的执行追踪记录"
            )
        
        # 验证用户权限 - 只能查看自己的任务
        if traces[0].user_id != current_user.id:
            raise HTTPException(
                status_code=403,
                detail="无权限查看此任务的执行追踪"
            )
        
        # 计算总执行时长
        total_duration_ms = None
        if traces:
            started_at = min(trace.started_at for trace in traces)
            completed_traces = [t for t in traces if t.completed_at]
            if completed_traces:
                completed_at = max(trace.completed_at for trace in completed_traces)
                total_duration_ms = int((completed_at - started_at).total_seconds() * 1000)
        
        # 确定整体状态
        failed_traces = [t for t in traces if t.status == "failed"]
        running_traces = [t for t in traces if t.status == "running"]
        
        if failed_traces:
            overall_status = "failed"
        elif running_traces:
            overall_status = "running"
        else:
            overall_status = "completed"
        
        # 转换为响应模型
        trace_responses = []
        for trace in traces:
            trace_response = AgentExecutionTraceResponse(
                id=trace.id,
                task_id=trace.task_id,
                user_id=trace.user_id,
                signal_id=trace.signal_id,
                node_name=trace.node_name,
                execution_order=trace.execution_order,
                status=trace.status,
                started_at=trace.started_at,
                completed_at=trace.completed_at,
                duration_ms=trace.duration_ms,
                input_data=trace.input_data,
                output_data=trace.output_data,
                error_data=trace.error_data,
                performance_metrics=trace.performance_metrics,
                metadata=trace.trace_metadata,
                created_at=trace.created_at,
                updated_at=trace.updated_at
            )
            trace_responses.append(trace_response)
        
        response = TaskExecutionTracesResponse(
            task_id=task_id,
            user_id=current_user.id,
            signal_id=traces[0].signal_id if traces else None,
            total_duration_ms=total_duration_ms,
            status=overall_status,
            traces=trace_responses
        )
        
        logger.info(
            "任务执行追踪获取成功",
            task_id=str(task_id),
            traces_count=len(traces),
            total_duration_ms=total_duration_ms,
            status=overall_status
        )
        
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "获取任务执行追踪失败",
            task_id=str(task_id),
            user_id=str(current_user.id),
            error=str(e)
        )
        raise HTTPException(
            status_code=500,
            detail="获取任务执行追踪失败"
        )


@router.get("/user/me/recent", response_model=RecentExecutionsResponse)
async def get_my_recent_executions(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
    limit: int = Query(10, ge=1, le=100, description="每页记录数"),
    page: int = Query(1, ge=1, description="页码"),
):
    """
    获取当前用户最近的Agent执行记录摘要

    便捷接口，自动使用当前用户ID查询最近的执行记录。
    """
    logger.info(
        "获取当前用户最近执行记录",
        user_id=str(current_user.id),
        limit=limit,
        page=page
    )

    # 计算偏移量
    offset = (page - 1) * limit

    # 获取用户最近的执行记录
    traces, total = await trace_service.get_user_recent_traces(
        db, current_user.id, limit, offset
    )

    # 转换为响应模型
    summaries = []
    for trace_data in traces:
        summary = RecentExecutionSummary(
            task_id=trace_data['task_id'],
            signal_id=trace_data['signal_id'],
            started_at=trace_data['started_at'],
            completed_at=trace_data['completed_at'],
            total_duration_ms=trace_data['total_duration_ms'],
            status=trace_data['status'],
            nodes_count=trace_data['nodes_count'],
            failed_nodes=trace_data['failed_nodes']
        )
        summaries.append(summary)

    logger.info(
        "当前用户最近执行记录获取成功",
        user_id=str(current_user.id),
        traces_count=len(summaries),
        total=total,
        page=page
    )

    return RecentExecutionsResponse(
        traces=summaries,
        total=total,
        page=page,
        limit=limit
    )


@router.get("/user/{user_id}/recent", response_model=RecentExecutionsResponse)
async def get_user_recent_executions(
    user_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
    limit: int = Query(10, ge=1, le=100, description="每页记录数"),
    page: int = Query(1, ge=1, description="页码"),
):
    """
    获取用户最近的Agent执行记录摘要

    返回用户最近的任务执行摘要，包括执行状态、时长和节点统计。
    支持分页查询。
    """
    try:
        # 验证用户权限 - 只能查看自己的记录
        if user_id != current_user.id:
            raise HTTPException(
                status_code=403,
                detail="无权限查看其他用户的执行记录"
            )

        logger.info(
            "获取用户最近执行记录",
            user_id=str(user_id),
            limit=limit,
            page=page
        )

        # 计算偏移量
        offset = (page - 1) * limit

        # 获取用户最近的执行记录
        traces, total = await trace_service.get_user_recent_traces(
            db, user_id, limit, offset
        )

        # 转换为响应模型
        summaries = []
        for trace_data in traces:
            summary = RecentExecutionSummary(
                task_id=trace_data["task_id"],
                signal_id=trace_data["signal_id"],
                started_at=trace_data["started_at"],
                completed_at=trace_data["completed_at"],
                total_duration_ms=trace_data["total_duration_ms"],
                status=trace_data["status"],
                nodes_count=trace_data["nodes_count"],
                failed_nodes=trace_data["failed_nodes"]
            )
            summaries.append(summary)

        response = RecentExecutionsResponse(
            traces=summaries,
            total=total,
            page=page,
            limit=limit
        )

        logger.info(
            "用户最近执行记录获取成功",
            user_id=str(user_id),
            traces_count=len(summaries),
            total=total,
            page=page
        )

        return response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "获取用户最近执行记录失败",
            user_id=str(user_id),
            error=str(e)
        )
        raise HTTPException(
            status_code=500,
            detail="获取用户最近执行记录失败"
        )


@router.get("/stats/summary")
async def get_execution_stats_summary(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """
    获取执行统计摘要
    
    返回用户的基础执行统计信息，如总执行次数、成功率等。
    """
    try:
        logger.info(
            "获取执行统计摘要",
            user_id=str(current_user.id)
        )
        
        # 获取最近30天的执行记录
        traces, total = await trace_service.get_user_recent_traces(
            db, current_user.id, limit=1000, offset=0
        )
        
        # 计算统计信息
        total_executions = len(traces)
        successful_executions = len([t for t in traces if t["status"] == "completed"])
        failed_executions = len([t for t in traces if t["status"] == "failed"])
        
        success_rate = 0.0
        if total_executions > 0:
            success_rate = (successful_executions / total_executions) * 100
        
        # 计算平均执行时长
        completed_traces = [t for t in traces if t["total_duration_ms"]]
        avg_duration_ms = 0
        if completed_traces:
            avg_duration_ms = sum(t["total_duration_ms"] for t in completed_traces) // len(completed_traces)
        
        stats = {
            "total_executions": total_executions,
            "successful_executions": successful_executions,
            "failed_executions": failed_executions,
            "success_rate": round(success_rate, 2),
            "avg_duration_ms": avg_duration_ms,
            "period_days": 30
        }
        
        logger.info(
            "执行统计摘要获取成功",
            user_id=str(current_user.id),
            stats=stats
        )
        
        return stats
        
    except Exception as e:
        logger.error(
            "获取执行统计摘要失败",
            user_id=str(current_user.id),
            error=str(e)
        )
        raise HTTPException(
            status_code=500,
            detail="获取执行统计摘要失败"
        )
