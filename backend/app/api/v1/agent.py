"""
Agent API endpoints for task management and processing
"""
from typing import List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.auth import get_current_user
from app.core.database import get_db
from app.core.models import User
from app.core.schemas import (
    AgentCheckpoint,
    AgentTask,
    APIResponse,
    PaginatedResponse,
    ProcessSignalRequest,
    TaskCreatedResponse,
)
from app.services.agent_service import AgentService

router = APIRouter(prefix="/agent", tags=["agent"])


@router.get("/status/{task_id}", response_model=APIResponse[dict])
async def get_task_status(
    task_id: str,  # 改为str类型
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """获取指定任务的状态或最新检查点"""
    from app.services.agent_service import agent_service

    try:
        status = await agent_service.get_task_status(task_id, db)
        if not status:
            raise HTTPException(status_code=404, detail="Task not found")

        return APIResponse.success_response(data=status, message="获取任务状态成功")
    except HTTPException:
        # 重新抛出HTTP异常，保持原始状态码
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/test-process", response_model=APIResponse[dict])
async def test_process_signal(
    request: ProcessSignalRequest,
    user_id: str = Query(..., description="用户ID"),
    db: AsyncSession = Depends(get_db),
):
    """测试Agent处理 - 绕过认证"""
    from app.services.agent_service import agent_service
    from app.core.models import User
    from sqlalchemy import select
    import uuid

    try:
        # 直接查找用户
        user_uuid = uuid.UUID(user_id)
        stmt = select(User).where(User.id == user_uuid)
        result = await db.execute(stmt)
        user = result.scalar_one_or_none()

        if not user:
            raise HTTPException(status_code=404, detail="User not found")

        # 处理信号
        response = await agent_service.process_signal(request, user, db)
        return APIResponse.success_response(data=response.dict(), message="任务创建成功，正在处理中")
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/process", response_model=APIResponse[dict])
async def process_signal(
    request: ProcessSignalRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """
    提交新的交易指令处理

    注意：此端点已弃用，建议使用 /api/v1/signals 端点创建信号并自动触发Agent处理
    """
    from app.services.signal_service import signal_service
    from app.core.schemas import CreateSignalRequest, PlatformType, MessageType
    import structlog

    logger = structlog.get_logger()

    try:
        # 记录直接调用警告
        logger.warning(
            "直接调用Agent API端点 - 建议使用SignalService",
            user_id=current_user.id,
            raw_input=request.raw_input[:100],
            context=request.context
        )

        # 转换为CreateSignalRequest并通过SignalService处理
        signal_request = CreateSignalRequest(
            platform=PlatformType.MANUAL,
            content=request.raw_input,
            raw_content=request.raw_input,
            channel_name="api_direct",
            author_name=current_user.username,
            message_type=MessageType.TEXT,
            metadata=request.context or {}
        )

        # 通过SignalService创建信号并触发Agent处理
        result = await signal_service.process_signal_with_agent(
            request=signal_request,
            user=current_user,
            db=db
        )

        return APIResponse(success=True, message="任务创建成功，正在处理中", data=result, error_code=None)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/task/{task_id}", response_model=APIResponse[dict])
async def get_task_details(
    task_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """获取Agent任务详细信息"""
    from app.services.agent_service import agent_service
    from app.core.models import Signal
    from sqlalchemy import select
    import uuid

    try:
        # 获取任务状态
        task_status = await agent_service.get_task_status(task_id, db)
        if not task_status:
            raise HTTPException(status_code=404, detail="Task not found")

        # 查找关联的信号
        task_uuid = uuid.UUID(task_id)
        stmt = select(Signal).where(Signal.agent_task_id == task_uuid)
        result = await db.execute(stmt)
        signal = result.scalar_one_or_none()

        # 构建响应
        response_data = {
            "task_id": task_id,
            "status": task_status,
            "signal": {
                "id": str(signal.id),
                "content": signal.content,
                "platform": signal.platform,
                "channel_name": signal.channel_name,
                "author_name": signal.author_name,
                "created_at": signal.created_at.isoformat(),
                "agent_processing_status": signal.agent_processing_status
            } if signal else None
        }

        return APIResponse.success_response(data=response_data)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/task/{task_id}/traces", response_model=APIResponse[list])
async def get_task_traces(
    task_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """获取Agent任务的追踪记录"""
    from app.core.models import AgentExecutionTrace
    from sqlalchemy import select, and_
    import uuid

    try:
        task_uuid = uuid.UUID(task_id)

        # 查询追踪记录
        stmt = select(AgentExecutionTrace).where(
            and_(
                AgentExecutionTrace.task_id == task_uuid,
                AgentExecutionTrace.user_id == current_user.id
            )
        ).order_by(AgentExecutionTrace.execution_order)

        result = await db.execute(stmt)
        traces = result.scalars().all()

        # 转换为响应格式
        traces_data = []
        for trace in traces:
            traces_data.append({
                "id": str(trace.id),
                "task_id": str(trace.task_id),
                "signal_id": str(trace.signal_id) if trace.signal_id else None,
                "node_name": trace.node_name,
                "execution_order": trace.execution_order,
                "status": trace.status,
                "started_at": trace.started_at.isoformat() if trace.started_at else None,
                "completed_at": trace.completed_at.isoformat() if trace.completed_at else None,
                "duration_ms": trace.duration_ms,
                "input_data": trace.input_data,
                "output_data": trace.output_data,
                "error_data": trace.error_data,
                "performance_metrics": trace.performance_metrics
            })

        return APIResponse.success_response(data=traces_data)
    except ValueError:
        raise HTTPException(status_code=400, detail="Invalid task ID format")
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/cancel/{task_id}", response_model=APIResponse[None])
async def cancel_task(
    task_id: str,  # 改为str类型
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """取消正在执行的任务"""
    from app.services.agent_service import agent_service

    try:
        success = await agent_service.cancel_task(task_id)
        if not success:
            raise HTTPException(
                status_code=404, detail="Task not found or cannot be cancelled"
            )

        return APIResponse.success_response(
            data=None, message="Task cancelled successfully"
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/tasks", response_model=APIResponse[List[dict]])
async def get_user_tasks(
    status: Optional[str] = Query(None, description="Filter by task status"),
    limit: int = Query(20, ge=1, le=100, description="Number of tasks per page"),
    offset: int = Query(0, ge=0, description="Number of tasks to skip"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """获取用户的任务历史"""
    from app.services.agent_service import agent_service

    try:
        tasks = await agent_service.list_user_tasks(
            user_id=current_user.id, db=db, limit=limit, offset=offset
        )

        return APIResponse.success_response(data=tasks, message="获取任务列表成功")
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/statistics", response_model=APIResponse[dict])
async def get_agent_statistics(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """获取用户的Agent使用统计"""
    from app.services.agent_service import agent_service

    try:
        # 简化统计信息
        stats = {
            "running_tasks": agent_service.get_running_tasks_count(),
            "user_id": current_user.id,
        }
        return APIResponse.success_response(data=stats, message="获取统计信息成功")
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
