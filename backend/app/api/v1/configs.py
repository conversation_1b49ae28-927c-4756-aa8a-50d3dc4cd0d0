"""
配置管理API端点
"""
import uuid
from typing import Any, Dict, List, Optional

import structlog
from fastapi import APIRouter, Depends, HTTPException, status
from pydantic import BaseModel, Field
from sqlalchemy import delete, select, update
from sqlalchemy.ext.asyncio import AsyncSession

from ...core.auth import get_current_user
from ...core.database import get_db
from ...core.exceptions import (
    BusinessException,
    ResourceNotFoundException,
    ValidationException,
)
from ...core.models import ExchangeConfig, RiskConfig, User
from ...core.schemas import (
    ExchangeConfigCreate,
    ExchangeConfigResponse,
    ExchangeConfigUpdate,
    RiskConfigResponse,
    RiskConfigUpdate,
)

logger = structlog.get_logger()

router = APIRouter(prefix="/configs", tags=["配置管理"])


# 请求/响应模型
class ExchangeConfigRequest(BaseModel):
    exchange_name: Optional[str] = Field(None, description="交易所名称")
    exchange: Optional[str] = Field(None, description="交易所名称（别名）")
    api_key: str = Field(..., description="API密钥")
    api_secret: str = Field(..., description="API密钥")
    passphrase: Optional[str] = Field(None, description="API密码（OKX需要）")
    sandbox_mode: Optional[bool] = Field(None, description="是否为沙盒模式")
    testnet: Optional[bool] = Field(None, description="是否为测试网（别名）")
    enabled: Optional[bool] = Field(None, description="是否启用")

    @property
    def resolved_exchange_name(self) -> str:
        """解析交易所名称"""
        return self.exchange_name or self.exchange

    @property
    def resolved_sandbox_mode(self) -> bool:
        """解析沙盒模式"""
        if self.sandbox_mode is not None:
            return self.sandbox_mode
        if self.testnet is not None:
            return self.testnet
        return True  # 默认为沙盒模式


class ExchangeConfigResponse(BaseModel):
    id: str = Field(..., description="配置ID")
    exchange_name: str = Field(..., description="交易所名称")
    sandbox_mode: bool = Field(..., description="是否为沙盒模式")
    is_active: bool = Field(..., description="是否激活")
    created_at: str = Field(..., description="创建时间")
    updated_at: str = Field(..., description="更新时间")


class RiskConfigRequest(BaseModel):
    max_concurrent_orders: int = Field(5, ge=1, le=100, description="最大并发订单数")
    max_total_position_value_usd: float = Field(
        1000.0, gt=0, description="最大总持仓价值（USD）"
    )
    default_position_size_usd: float = Field(100.0, gt=0, description="默认持仓大小（USD）")
    max_position_size_usd: float = Field(500.0, gt=0, description="最大持仓大小（USD）")
    max_daily_loss_usd: float = Field(500.0, gt=0, description="最大日损失（USD）")
    max_drawdown_percent: float = Field(20.0, gt=0, le=100, description="最大回撤百分比")
    stop_loss_percent: float = Field(5.0, gt=0, le=100, description="止损百分比")
    allowed_symbols: List[str] = Field(["BTC/USDT", "ETH/USDT"], description="允许的交易对")
    trading_hours_start: str = Field("00:00", description="交易时间开始")
    trading_hours_end: str = Field("23:59", description="交易时间结束")
    confidence_threshold: float = Field(0.8, ge=0, le=1, description="AI置信度阈值")
    auto_approve_threshold: float = Field(0.95, ge=0, le=1, description="自动批准阈值")
    max_symbol_concentration_percent: float = Field(
        50.0, gt=0, le=100, description="单个交易对最大占比"
    )
    correlation_check_enabled: bool = Field(True, description="是否启用相关性检查")
    volatility_check_enabled: bool = Field(True, description="是否启用波动性检查")


class RiskConfigResponse(BaseModel):
    id: int = Field(..., description="配置ID")
    max_concurrent_orders: int = Field(..., description="最大并发订单数")
    max_total_position_value_usd: float = Field(..., description="最大总持仓价值（USD）")
    default_position_size_usd: float = Field(..., description="默认持仓大小（USD）")
    max_position_size_usd: float = Field(..., description="最大持仓大小（USD）")
    max_daily_loss_usd: float = Field(..., description="最大日损失（USD）")
    max_drawdown_percent: float = Field(..., description="最大回撤百分比")
    stop_loss_percent: float = Field(..., description="止损百分比")
    allowed_symbols: List[str] = Field(..., description="允许的交易对")
    trading_hours_start: str = Field(..., description="交易时间开始")
    trading_hours_end: str = Field(..., description="交易时间结束")
    confidence_threshold: float = Field(..., description="AI置信度阈值")
    auto_approve_threshold: float = Field(..., description="自动批准阈值")
    max_symbol_concentration_percent: float = Field(..., description="单个交易对最大占比")
    correlation_check_enabled: bool = Field(..., description="是否启用相关性检查")
    volatility_check_enabled: bool = Field(..., description="是否启用波动性检查")
    created_at: str = Field(..., description="创建时间")
    updated_at: str = Field(..., description="更新时间")


# 交易所配置API
@router.get("/exchange", response_model=Dict[str, Any], summary="获取交易所配置列表")
async def get_exchange_configs(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
) -> Dict[str, Any]:
    """
    获取用户的交易所配置列表
    """
    try:
        query = select(ExchangeConfig).where(
            ExchangeConfig.user_id == current_user.id,
            ExchangeConfig.is_active == True,
        )
        result = await db.execute(query)
        configs = result.scalars().all()

        config_list = []
        for config in configs:
            config_list.append(
                {
                    "id": str(config.id),
                    "exchange_name": config.exchange_name,
                    "exchange": config.exchange_name,  # 别名
                    "sandbox_mode": config.sandbox_mode,
                    "testnet": config.sandbox_mode,  # 别名
                    "is_active": config.is_active,
                    "enabled": config.is_active,  # 别名
                    "created_at": config.created_at.isoformat(),
                    "updated_at": config.updated_at.isoformat(),
                }
            )

        return {
            "success": True,
            "data": config_list,
        }

    except Exception as e:
        logger.error(
            "Failed to get exchange configs",
            user_id=current_user.id,
            error=str(e),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取交易所配置失败",
        )


@router.post("/exchange", response_model=Dict[str, Any], summary="创建交易所配置")
async def create_exchange_config(
    request: ExchangeConfigRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
) -> Dict[str, Any]:
    """
    创建交易所配置

    - **exchange_name**: 交易所名称（binance, okx, bybit, coinbase）
    - **api_key**: API密钥
    - **api_secret**: API密钥
    - **passphrase**: API密码（OKX需要）
    - **sandbox_mode**: 是否为沙盒模式
    """
    try:
        # 解析字段
        exchange_name = request.resolved_exchange_name
        sandbox_mode = request.resolved_sandbox_mode

        if not exchange_name:
            raise HTTPException(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                detail="exchange_name or exchange field is required",
            )

        # 检查是否已存在相同交易所的配置
        query = select(ExchangeConfig).where(
            ExchangeConfig.user_id == current_user.id,
            ExchangeConfig.exchange_name == exchange_name,
            ExchangeConfig.is_active == True,
        )
        result = await db.execute(query)
        existing_config = result.scalar_one_or_none()

        if existing_config:
            raise BusinessException(
                message=f"交易所 {exchange_name} 的配置已存在",
                code="EXCHANGE_CONFIG_EXISTS",
            )

        # TODO: 实现API密钥加密
        # 当前为简化实现，实际应用中需要加密存储
        new_config = ExchangeConfig(
            user_id=current_user.id,
            exchange_name=exchange_name,
            encrypted_api_key=request.api_key,  # 应该加密
            encrypted_api_secret=request.api_secret,  # 应该加密
            encrypted_passphrase=request.passphrase,  # 应该加密
            sandbox_mode=sandbox_mode,
            is_active=request.enabled if request.enabled is not None else True,
        )

        db.add(new_config)
        await db.commit()
        await db.refresh(new_config)

        logger.info(
            "Exchange config created",
            user_id=current_user.id,
            exchange=exchange_name,
            config_id=str(new_config.id),
        )

        return {
            "success": True,
            "message": "交易所配置创建成功",
            "data": {
                "id": str(new_config.id),
                "config_id": str(new_config.id),
                "exchange_name": new_config.exchange_name,
                "exchange": new_config.exchange_name,  # 别名
                "sandbox_mode": new_config.sandbox_mode,
                "testnet": new_config.sandbox_mode,  # 别名
                "is_active": new_config.is_active,
                "enabled": new_config.is_active,  # 别名
                "created_at": new_config.created_at.isoformat(),
                "updated_at": new_config.updated_at.isoformat(),
            },
        }

    except BusinessException:
        raise
    except Exception as e:
        logger.error(
            "Failed to create exchange config",
            user_id=current_user.id,
            error=str(e),
        )
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="创建交易所配置失败",
        )


# 获取单个交易所配置
@router.get("/exchange/{config_id}", response_model=Dict[str, Any], summary="获取单个交易所配置")
async def get_exchange_config(
    config_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
) -> Dict[str, Any]:
    """
    获取单个交易所配置

    - **config_id**: 配置ID
    """
    try:
        query = select(ExchangeConfig).where(
            ExchangeConfig.id == uuid.UUID(config_id),
            ExchangeConfig.user_id == current_user.id,
            ExchangeConfig.is_active == True,
        )
        result = await db.execute(query)
        config = result.scalar_one_or_none()

        if not config:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Exchange config with ID {config_id} not found",
            )

        return {
            "success": True,
            "data": {
                "id": str(config.id),
                "exchange_name": config.exchange_name,
                "exchange": config.exchange_name,  # 别名
                "sandbox_mode": config.sandbox_mode,
                "testnet": config.sandbox_mode,  # 别名
                "is_active": config.is_active,
                "enabled": config.is_active,  # 别名
                "created_at": config.created_at.isoformat(),
                "updated_at": config.updated_at.isoformat(),
            },
        }

    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid config ID format",
        )
    except Exception as e:
        logger.error(
            "Failed to get exchange config",
            user_id=current_user.id,
            error=str(e),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取交易所配置失败",
        )


@router.delete(
    "/exchange/{config_id}", response_model=Dict[str, Any], summary="删除交易所配置"
)
async def delete_exchange_config(
    config_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
) -> Dict[str, Any]:
    """
    删除交易所配置

    - **config_id**: 配置ID
    """
    try:
        query = select(ExchangeConfig).where(
            ExchangeConfig.id == uuid.UUID(config_id),
            ExchangeConfig.user_id == current_user.id,
        )
        result = await db.execute(query)
        config = result.scalar_one_or_none()

        if not config:
            raise ResourceNotFoundException("交易所配置", config_id)

        # 软删除：设置为非激活状态
        config.is_active = False
        await db.commit()

        logger.info(
            "Exchange config deleted",
            user_id=current_user.id,
            config_id=config_id,
        )

        return {"success": True, "message": "交易所配置删除成功"}

    except ResourceNotFoundException:
        raise
    except ValueError:
        raise ValidationException("无效的配置ID格式", "config_id")
    except Exception as e:
        logger.error(
            "Failed to delete exchange config",
            user_id=current_user.id,
            error=str(e),
        )
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="删除交易所配置失败",
        )


@router.put("/exchange/{config_id}", response_model=Dict[str, Any], summary="更新交易所配置")
async def update_exchange_config(
    config_id: str,
    request: ExchangeConfigRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
) -> Dict[str, Any]:
    """
    更新交易所配置

    - **config_id**: 配置ID
    - **exchange_name**: 交易所名称（binance, okx, bybit, coinbase）
    - **api_key**: API密钥
    - **api_secret**: API密钥
    - **passphrase**: API密码（OKX需要）
    - **sandbox_mode**: 是否为沙盒模式
    """
    try:
        query = select(ExchangeConfig).where(
            ExchangeConfig.id == uuid.UUID(config_id),
            ExchangeConfig.user_id == current_user.id,
        )
        result = await db.execute(query)
        config = result.scalar_one_or_none()

        if not config:
            raise ResourceNotFoundException("交易所配置", config_id)

        # 解析字段
        exchange_name = request.resolved_exchange_name
        sandbox_mode = request.resolved_sandbox_mode

        # 更新配置
        if exchange_name:
            config.exchange_name = exchange_name
        if request.api_key:
            config.encrypted_api_key = request.api_key  # 应该加密
        if request.api_secret:
            config.encrypted_api_secret = request.api_secret  # 应该加密
        if request.passphrase is not None:
            config.encrypted_passphrase = request.passphrase  # 应该加密
        if request.sandbox_mode is not None or request.testnet is not None:
            config.sandbox_mode = sandbox_mode
        if request.enabled is not None:
            config.is_active = request.enabled

        await db.commit()

        logger.info(
            "Exchange config updated",
            user_id=current_user.id,
            config_id=config_id,
            exchange=request.exchange_name,
        )

        return {
            "success": True,
            "message": "交易所配置更新成功",
            "data": {
                "id": str(config.id),
                "config_id": str(config.id),
                "exchange_name": config.exchange_name,
                "exchange": config.exchange_name,
                "sandbox_mode": config.sandbox_mode,
                "testnet": config.sandbox_mode,
                "is_active": config.is_active,
                "enabled": config.is_active,
                "created_at": config.created_at.isoformat()
                if config.created_at
                else None,
            },
        }

    except ResourceNotFoundException:
        raise
    except ValueError:
        raise ValidationException("无效的配置ID格式", "config_id")
    except Exception as e:
        logger.error(
            "Failed to update exchange config",
            user_id=current_user.id,
            error=str(e),
        )
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="更新交易所配置失败",
        )


# 风控配置API
@router.post("/risk", response_model=Dict[str, Any], summary="创建或更新风控配置")
async def create_or_update_risk_config(
    request: RiskConfigRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
) -> Dict[str, Any]:
    """
    创建或更新风控配置

    如果用户已有风控配置，则更新；否则创建新的配置
    """
    try:
        # 检查是否已存在风控配置
        query = select(RiskConfig).where(RiskConfig.user_id == current_user.id)
        result = await db.execute(query)
        existing_config = result.scalar_one_or_none()

        if existing_config:
            # 更新现有配置
            for field, value in request.dict().items():
                setattr(existing_config, field, value)

            await db.commit()
            await db.refresh(existing_config)

            logger.info("Risk config updated", user_id=current_user.id)
            message = "风控配置更新成功"
            config_id = existing_config.id
        else:
            # 创建新配置
            new_config = RiskConfig(user_id=current_user.id, **request.dict())

            db.add(new_config)
            await db.commit()
            await db.refresh(new_config)

            logger.info("Risk config created", user_id=current_user.id)
            message = "风控配置创建成功"
            config_id = new_config.id

        return {
            "success": True,
            "message": message,
            "data": {"config_id": config_id},
        }

    except Exception as e:
        logger.error(
            "Failed to create/update risk config",
            user_id=current_user.id,
            error=str(e),
        )
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="风控配置操作失败",
        )

    await db.execute(delete(ExchangeConfig).where(ExchangeConfig.id == config_id))
    await db.commit()

    return None


# 风控配置路由
@router.get("/risk", response_model=Dict[str, Any], summary="获取风控配置")
async def get_risk_config(
    current_user: User = Depends(get_current_user), db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    获取用户的风控配置
    """
    try:
        query = select(RiskConfig).where(RiskConfig.user_id == current_user.id)
        result = await db.execute(query)
        config = result.scalar_one_or_none()

        if not config:
            # 如果没有配置，返回默认配置
            return {
                "success": True,
                "data": {
                    "max_concurrent_orders": 5,
                    "max_total_position_value_usd": 1000.0,
                    "default_position_size_usd": 100.0,
                    "max_position_size_usd": 500.0,
                    "max_daily_loss_usd": 200.0,
                    "stop_loss_percent": 2.0,
                    "allowed_symbols": ["BTC/USDT", "ETH/USDT"],
                    "confidence_threshold": 0.8,
                },
            }

        return {
            "success": True,
            "data": {
                "max_concurrent_orders": config.max_concurrent_orders,
                "max_total_position_value_usd": float(
                    config.max_total_position_value_usd
                ),
                "default_position_size_usd": float(config.default_position_size_usd),
                "max_position_size_usd": float(config.max_position_size_usd),
                "max_daily_loss_usd": float(config.max_daily_loss_usd),
                "stop_loss_percent": float(config.stop_loss_percent),
                "allowed_symbols": config.allowed_symbols,
                "confidence_threshold": float(config.confidence_threshold),
            },
        }

    except Exception as e:
        logger.error(
            "Failed to get risk config",
            user_id=current_user.id,
            error=str(e),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取风控配置失败",
        )


@router.put("/risk", response_model=Dict[str, Any], summary="更新风控配置")
async def update_risk_config(
    request: RiskConfigRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
) -> Dict[str, Any]:
    """
    更新风控配置
    """
    try:
        # 检查是否已存在配置
        query = select(RiskConfig).where(RiskConfig.user_id == current_user.id)
        result = await db.execute(query)
        existing_config = result.scalar_one_or_none()

        if existing_config:
            # 更新现有配置
            for field, value in request.dict().items():
                setattr(existing_config, field, value)

            await db.commit()
            await db.refresh(existing_config)

            logger.info("Risk config updated", user_id=current_user.id)
            message = "风控配置更新成功"
        else:
            # 创建新配置
            new_config = RiskConfig(user_id=current_user.id, **request.dict())

            db.add(new_config)
            await db.commit()
            await db.refresh(new_config)

            logger.info("Risk config created", user_id=current_user.id)
            message = "风控配置创建成功"

        return {
            "success": True,
            "message": message,
            "data": {
                "max_concurrent_orders": request.max_concurrent_orders,
                "max_total_position_value_usd": float(
                    request.max_total_position_value_usd
                ),
                "default_position_size_usd": float(request.default_position_size_usd),
                "max_position_size_usd": float(request.max_position_size_usd),
                "max_daily_loss_usd": float(request.max_daily_loss_usd),
                "stop_loss_percent": float(request.stop_loss_percent),
                "allowed_symbols": request.allowed_symbols,
                "confidence_threshold": float(request.confidence_threshold),
            },
        }

    except Exception as e:
        logger.error(
            "Failed to update risk config",
            user_id=current_user.id,
            error=str(e),
        )
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="风控配置更新失败",
        )


# 系统配置API
@router.get("/system", response_model=Dict[str, Any], summary="获取系统配置")
async def get_system_config(
    current_user: User = Depends(get_current_user),
) -> Dict[str, Any]:
    """
    获取系统配置信息
    """
    return {
        "success": True,
        "data": {
            "supported_exchanges": ["binance", "okx", "bybit", "coinbase"],
            "supported_symbols": [
                "BTC/USDT",
                "ETH/USDT",
                "SOL/USDT",
                "BNB/USDT",
            ],
            "max_concurrent_orders": 100,
            "min_position_size_usd": 10.0,
            "max_position_size_usd": 100000.0,
            "trading_hours": {"start": "00:00", "end": "23:59"},
            "risk_limits": {
                "max_drawdown_percent": 50.0,
                "max_daily_loss_percent": 30.0,
                "min_confidence_threshold": 0.5,
                "max_confidence_threshold": 1.0,
            },
        },
    }
