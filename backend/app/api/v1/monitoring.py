"""
信号处理监控API端点
基于可观测性设计文档实现的监控接口
提供KPI指标、流程状态、告警等监控数据
"""

from datetime import datetime, timedelta, timezone
from typing import Dict, List, Optional, Any
from fastapi import APIRouter, Depends, Query, HTTPException
from sqlalchemy import func, select, and_, text
from sqlalchemy.ext.asyncio import AsyncSession

from ...core.database import get_async_session
from ...core.models import Signal, User
from ...core.auth import get_current_user
from ...core.schemas import APIResponse

router = APIRouter(prefix="/monitoring", tags=["monitoring"])


@router.get("/signals/status")
async def get_signals_status(
    db: AsyncSession = Depends(get_async_session),
    current_user = Depends(get_current_user)
):
    """获取信号处理实时状态"""
    
    # 当前时间
    now = datetime.utcnow()
    five_minutes_ago = now - timedelta(minutes=5)
    
    # 统计各种状态的信号数量
    status_query = select(
        func.count().label('total'),
        func.count().filter(Signal.ai_parse_status == 'pending').label('pending'),
        func.count().filter(Signal.agent_processing_status == 'processing').label('processing'),
        func.count().filter(
            and_(
                Signal.ai_parse_status == 'pending',
                Signal.created_at < five_minutes_ago
            )
        ).label('stuck'),
        func.count().filter(Signal.ai_parse_status == 'success').label('successful'),
        func.count().filter(Signal.ai_parse_status == 'failed').label('failed')
    ).select_from(Signal)
    
    result = await db.execute(status_query)
    stats = result.first()
    
    # 计算成功率
    total_processed = stats.successful + stats.failed
    success_rate = (stats.successful / total_processed * 100) if total_processed > 0 else 0
    
    return {
        "timestamp": now.isoformat(),
        "total_signals": stats.total,
        "pending_count": stats.pending,
        "processing_count": stats.processing,
        "stuck_count": stats.stuck,
        "success_count": stats.successful,
        "failed_count": stats.failed,
        "success_rate": round(success_rate, 2)
    }


@router.get("/signals/{signal_id}/timeline")
async def get_signal_timeline(
    signal_id: str,
    db: AsyncSession = Depends(get_async_session),
    current_user = Depends(get_current_user)
):
    """获取特定信号的处理时间线"""
    
    # 查询信号详情
    signal_query = select(Signal).where(Signal.id == signal_id)
    result = await db.execute(signal_query)
    signal = result.scalar_one_or_none()
    
    if not signal:
        return {"error": "Signal not found"}
    
    # 构建时间线
    timeline = []
    
    # 创建时间
    timeline.append({
        "stage": "created",
        "timestamp": signal.created_at.isoformat(),
        "status": {
            "ai_parse_status": "pending",
            "agent_processing_status": None,
            "is_processed": False
        }
    })
    
    # 如果有更新时间，添加完成时间
    if signal.updated_at and signal.updated_at != signal.created_at:
        processing_time = (signal.updated_at - signal.created_at).total_seconds()
        timeline.append({
            "stage": "completed",
            "timestamp": signal.updated_at.isoformat(),
            "status": {
                "ai_parse_status": signal.ai_parse_status,
                "agent_processing_status": signal.agent_processing_status,
                "is_processed": signal.is_processed
            },
            "processing_time_seconds": processing_time
        })
    
    return {
        "signal_id": signal_id,
        "timeline": timeline,
        "current_status": {
            "ai_parse_status": signal.ai_parse_status,
            "agent_processing_status": signal.agent_processing_status,
            "is_processed": signal.is_processed
        }
    }


@router.get("/metrics/performance")
async def get_performance_metrics(
    hours: int = Query(24, ge=1, le=168),  # 1小时到7天
    db: AsyncSession = Depends(get_async_session),
    current_user = Depends(get_current_user)
):
    """获取性能指标数据"""
    
    # 时间范围
    now = datetime.utcnow()
    start_time = now - timedelta(hours=hours)
    
    # 处理时间统计
    processing_time_query = select(
        func.avg(
            func.extract('epoch', Signal.updated_at - Signal.created_at)
        ).label('avg_seconds'),
        func.min(
            func.extract('epoch', Signal.updated_at - Signal.created_at)
        ).label('min_seconds'),
        func.max(
            func.extract('epoch', Signal.updated_at - Signal.created_at)
        ).label('max_seconds'),
        func.percentile_cont(0.95).within_group(
            func.extract('epoch', Signal.updated_at - Signal.created_at)
        ).label('p95_seconds')
    ).where(
        and_(
            Signal.created_at >= start_time,
            Signal.ai_parse_status.in_(['success', 'failed']),
            Signal.updated_at.isnot(None)
        )
    )
    
    result = await db.execute(processing_time_query)
    time_stats = result.first()
    
    # 成功率统计
    success_rate_query = select(
        func.count().label('total'),
        func.count().filter(Signal.ai_parse_status == 'success').label('successful')
    ).where(
        and_(
            Signal.created_at >= start_time,
            Signal.ai_parse_status.in_(['success', 'failed'])
        )
    )
    
    result = await db.execute(success_rate_query)
    success_stats = result.first()
    
    success_rate = (success_stats.successful / success_stats.total * 100) if success_stats.total > 0 else 0
    
    # 吞吐量统计（每小时处理的信号数）
    throughput = success_stats.total / hours if hours > 0 else 0
    
    return {
        "time_range_hours": hours,
        "processing_time_stats": {
            "avg_seconds": round(time_stats.avg_seconds or 0, 2),
            "min_seconds": round(time_stats.min_seconds or 0, 2),
            "max_seconds": round(time_stats.max_seconds or 0, 2),
            "p95_seconds": round(time_stats.p95_seconds or 0, 2)
        },
        "success_rate": round(success_rate, 2),
        "throughput_per_hour": round(throughput, 2),
        "total_processed": success_stats.total,
        "successful_count": success_stats.successful,
        "failed_count": success_stats.total - success_stats.successful
    }


@router.get("/alerts")
async def get_active_alerts(
    db: AsyncSession = Depends(get_async_session),
    current_user = Depends(get_current_user)
):
    """获取当前活跃的告警"""
    
    alerts = []
    now = datetime.utcnow()
    
    # 检查卡住的信号（超过5分钟仍在pending状态）
    five_minutes_ago = now - timedelta(minutes=5)
    stuck_query = select(func.count()).where(
        and_(
            Signal.ai_parse_status == 'pending',
            Signal.created_at < five_minutes_ago
        )
    )
    result = await db.execute(stuck_query)
    stuck_count = result.scalar()
    
    if stuck_count > 0:
        severity = "critical" if stuck_count >= 10 else "warning"
        alerts.append({
            "type": "stuck_signals",
            "severity": severity,
            "message": f"{stuck_count} signals stuck in pending status for >5 minutes",
            "count": stuck_count,
            "timestamp": now.isoformat()
        })
    
    # 检查最近1小时的成功率
    one_hour_ago = now - timedelta(hours=1)
    success_rate_query = select(
        func.count().label('total'),
        func.count().filter(Signal.ai_parse_status == 'success').label('successful')
    ).where(
        and_(
            Signal.created_at >= one_hour_ago,
            Signal.ai_parse_status.in_(['success', 'failed'])
        )
    )
    
    result = await db.execute(success_rate_query)
    stats = result.first()
    
    if stats.total > 0:
        success_rate = stats.successful / stats.total * 100
        if success_rate < 80:
            severity = "critical" if success_rate < 80 else "warning"
            alerts.append({
                "type": "low_success_rate",
                "severity": severity,
                "message": f"Success rate is {success_rate:.1f}% in the last hour",
                "success_rate": round(success_rate, 2),
                "timestamp": now.isoformat()
            })
    
    return {
        "timestamp": now.isoformat(),
        "alert_count": len(alerts),
        "alerts": alerts
    }


@router.get("/dashboard")
async def get_monitoring_dashboard(
    db: AsyncSession = Depends(get_async_session),
    current_user = Depends(get_current_user)
):
    """获取监控仪表板数据"""
    
    # 获取各个组件的数据
    status_data = await get_signals_status(db, current_user)
    performance_data = await get_performance_metrics(24, db, current_user)
    alerts_data = await get_active_alerts(db, current_user)
    
    return {
        "timestamp": datetime.utcnow().isoformat(),
        "core_metrics": {
            "success_rate": status_data["success_rate"],
            "avg_processing_time": performance_data["processing_time_stats"]["avg_seconds"],
            "processing_count": status_data["processing_count"],
            "stuck_count": status_data["stuck_count"]
        },
        "performance": performance_data,
        "status": status_data,
        "alerts": alerts_data
    }


@router.get("/flow-status", response_model=APIResponse[Dict[str, Any]])
async def get_flow_status(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session),
):
    """获取信号处理流程状态"""
    try:
        # 查询各个阶段的信号数量
        flow_stats_query = text("""
            SELECT
                ai_parse_status,
                agent_processing_status,
                COUNT(*) as count
            FROM signals
            WHERE created_at >= :recent_time
            GROUP BY ai_parse_status, agent_processing_status
        """)

        recent_time = datetime.now(timezone.utc) - timedelta(hours=24)
        result = await db.execute(flow_stats_query, {"recent_time": recent_time})
        flow_stats = result.fetchall()

        # 初始化节点数据
        nodes = [
            {
                "id": "create",
                "type": "create",
                "label": "创建信号",
                "status": "completed",
                "count": 0,
                "x": 100,
                "y": 200,
                "description": "信号从外部平台接收并创建"
            },
            {
                "id": "parse",
                "type": "parse",
                "label": "解析意图",
                "status": "pending",
                "count": 0,
                "x": 250,
                "y": 200,
                "description": "AI解析信号内容，提取交易意图"
            },
            {
                "id": "context",
                "type": "context",
                "label": "上下文分析",
                "status": "pending",
                "count": 0,
                "x": 400,
                "y": 200,
                "description": "分析市场上下文和历史数据"
            },
            {
                "id": "plan",
                "type": "plan",
                "label": "制定策略",
                "status": "pending",
                "count": 0,
                "x": 550,
                "y": 200,
                "description": "制定具体的交易执行策略"
            },
            {
                "id": "execute",
                "type": "execute",
                "label": "执行交易",
                "status": "pending",
                "count": 0,
                "x": 700,
                "y": 200,
                "description": "执行实际的交易操作"
            }
        ]

        # 统计各阶段数量
        total_signals = 0
        for stat in flow_stats:
            ai_status = stat.ai_parse_status
            agent_status = stat.agent_processing_status
            count = stat.count
            total_signals += count

            if ai_status == 'pending':
                nodes[1]["count"] += count  # parse节点
                nodes[1]["status"] = "processing" if count > 0 else "pending"
            elif ai_status == 'success':
                if agent_status == 'processing':
                    nodes[3]["count"] += count  # plan节点
                    nodes[3]["status"] = "processing"
                elif agent_status == 'completed':
                    nodes[4]["count"] += count  # execute节点
                    nodes[4]["status"] = "success"
            elif ai_status == 'failed':
                nodes[1]["status"] = "failed"

        # 更新创建节点的数量
        nodes[0]["count"] = total_signals

        # 连接线数据
        connections = [
            {
                "id": "create-parse",
                "from": "create",
                "to": "parse",
                "status": "active",
                "path": "M 150 200 L 200 200"
            },
            {
                "id": "parse-context",
                "from": "parse",
                "to": "context",
                "status": "active",
                "path": "M 300 200 L 350 200"
            },
            {
                "id": "context-plan",
                "from": "context",
                "to": "plan",
                "status": "active",
                "path": "M 450 200 L 500 200"
            },
            {
                "id": "plan-execute",
                "from": "plan",
                "to": "execute",
                "status": "active",
                "path": "M 600 200 L 650 200"
            }
        ]

        flow_data = {
            "nodes": nodes,
            "connections": connections,
            "total_signals": total_signals,
            "last_updated": datetime.now(timezone.utc).isoformat()
        }

        return APIResponse.success_response(
            data=flow_data,
            message="流程状态获取成功"
        )

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get flow status: {str(e)}"
        )


@router.get("/recent-signals", response_model=APIResponse[List[Dict[str, Any]]])
async def get_recent_signals(
    limit: int = Query(10, description="返回信号数量限制"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session),
):
    """获取最近的信号处理状态"""
    try:
        recent_signals_query = text("""
            SELECT
                id,
                platform,
                content,
                ai_parse_status,
                agent_processing_status,
                confidence,
                is_processed,
                created_at,
                updated_at,
                EXTRACT(EPOCH FROM (COALESCE(updated_at, NOW()) - created_at)) as processing_duration
            FROM signals
            ORDER BY created_at DESC
            LIMIT :limit
        """)

        result = await db.execute(recent_signals_query, {"limit": limit})
        signals = result.fetchall()

        signals_data = []
        for signal in signals:
            # 确定状态和颜色
            if signal.ai_parse_status == 'success' and signal.agent_processing_status == 'completed':
                status = 'completed'
                status_color = 'success'
                status_text = '已完成'
            elif signal.ai_parse_status == 'failed':
                status = 'failed'
                status_color = 'error'
                status_text = '处理失败'
            elif signal.agent_processing_status == 'processing':
                status = 'processing'
                status_color = 'warning'
                status_text = '处理中'
            elif signal.ai_parse_status == 'pending':
                status = 'pending'
                status_color = 'info'
                status_text = '等待处理'
            else:
                status = 'unknown'
                status_color = 'grey'
                status_text = '未知状态'

            # 截断内容
            content = signal.content
            if len(content) > 100:
                content = content[:100] + "..."

            signals_data.append({
                "id": signal.id,
                "platform": signal.platform,
                "content": content,
                "full_content": signal.content,
                "status": status,
                "status_color": status_color,
                "status_text": status_text,
                "confidence": signal.confidence,
                "processing_duration": round(signal.processing_duration, 1) if signal.processing_duration else None,
                "created_at": signal.created_at.isoformat(),
                "updated_at": signal.updated_at.isoformat() if signal.updated_at else None
            })

        return APIResponse.success_response(
            data=signals_data,
            message="最近信号获取成功"
        )

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get recent signals: {str(e)}"
        )


@router.get("/kpi", response_model=APIResponse[Dict[str, Any]])
async def get_kpi_metrics(
    hours: int = Query(24, description="统计时间范围（小时）"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session),
):
    """获取KPI指标数据"""
    try:
        # 计算时间范围
        end_time = datetime.now(timezone.utc)
        start_time = end_time - timedelta(hours=hours)

        # 查询信号处理统计
        signal_stats_query = text("""
            SELECT
                COUNT(*) as total_signals,
                COUNT(CASE WHEN ai_parse_status = 'success' THEN 1 END) as successful_signals,
                COUNT(CASE WHEN ai_parse_status = 'failed' THEN 1 END) as failed_signals,
                COUNT(CASE WHEN ai_parse_status = 'pending' THEN 1 END) as pending_signals,
                COUNT(CASE WHEN agent_processing_status = 'processing' THEN 1 END) as processing_signals,
                AVG(CASE
                    WHEN ai_parse_status IN ('success', 'failed') AND updated_at > created_at
                    THEN EXTRACT(EPOCH FROM (updated_at - created_at))
                END) as avg_processing_time_seconds
            FROM signals
            WHERE created_at >= :start_time AND created_at <= :end_time
        """)

        result = await db.execute(signal_stats_query, {
            "start_time": start_time,
            "end_time": end_time
        })
        stats = result.fetchone()

        # 查询卡住的信号（超过5分钟仍在pending状态）
        stuck_signals_query = text("""
            SELECT COUNT(*) as stuck_count
            FROM signals
            WHERE ai_parse_status = 'pending'
            AND created_at < :stuck_threshold
        """)

        stuck_result = await db.execute(stuck_signals_query, {
            "stuck_threshold": datetime.now(timezone.utc) - timedelta(minutes=5)
        })
        stuck_count = stuck_result.scalar() or 0

        # 计算成功率
        total_signals = stats.total_signals or 0
        successful_signals = stats.successful_signals or 0
        success_rate = (successful_signals / total_signals * 100) if total_signals > 0 else 0

        # 格式化处理时间
        avg_processing_time = stats.avg_processing_time_seconds or 0

        # 计算趋势（与前一个时间段比较）
        prev_start_time = start_time - timedelta(hours=hours)
        prev_stats_query = text("""
            SELECT
                COUNT(*) as total_signals,
                COUNT(CASE WHEN ai_parse_status = 'success' THEN 1 END) as successful_signals,
                AVG(CASE
                    WHEN ai_parse_status IN ('success', 'failed') AND updated_at > created_at
                    THEN EXTRACT(EPOCH FROM (updated_at - created_at))
                END) as avg_processing_time_seconds
            FROM signals
            WHERE created_at >= :prev_start_time AND created_at < :start_time
        """)

        prev_result = await db.execute(prev_stats_query, {
            "prev_start_time": prev_start_time,
            "start_time": start_time
        })
        prev_stats = prev_result.fetchone()

        # 计算趋势
        prev_success_rate = 0
        prev_avg_time = 0
        if prev_stats.total_signals and prev_stats.total_signals > 0:
            prev_success_rate = (prev_stats.successful_signals / prev_stats.total_signals * 100)
            prev_avg_time = prev_stats.avg_processing_time_seconds or 0

        success_rate_trend = success_rate - prev_success_rate
        processing_time_trend = avg_processing_time - prev_avg_time

        kpi_data = {
            "success_rate": {
                "value": round(success_rate, 1),
                "trend": {
                    "direction": "up" if success_rate_trend > 0 else "down" if success_rate_trend < 0 else "stable",
                    "value": abs(round(success_rate_trend, 1)),
                    "text": f"较前{hours}小时{'上升' if success_rate_trend > 0 else '下降' if success_rate_trend < 0 else '持平'} {abs(round(success_rate_trend, 1))}%"
                },
                "format": "percentage",
                "color": "success",
                "icon": "mdi-check-circle"
            },
            "avg_processing_time": {
                "value": round(avg_processing_time, 1),
                "trend": {
                    "direction": "down" if processing_time_trend < 0 else "up" if processing_time_trend > 0 else "stable",
                    "value": abs(round(processing_time_trend, 1)),
                    "text": f"较前{hours}小时{'减少' if processing_time_trend < 0 else '增加' if processing_time_trend > 0 else '持平'} {abs(round(processing_time_trend, 1))}秒"
                },
                "format": "duration",
                "color": "info",
                "icon": "mdi-clock-outline"
            },
            "processing_count": {
                "value": stats.processing_signals or 0,
                "trend": {
                    "direction": "stable",
                    "value": 0,
                    "text": "当前处理中"
                },
                "format": "number",
                "color": "warning",
                "icon": "mdi-cog"
            },
            "stuck_count": {
                "value": stuck_count,
                "trend": {
                    "direction": "stable",
                    "value": 0,
                    "text": "异常信号数量"
                },
                "format": "number",
                "color": "error" if stuck_count > 0 else "success",
                "icon": "mdi-alert-circle"
            },
            "metadata": {
                "total_signals": total_signals,
                "successful_signals": successful_signals,
                "failed_signals": stats.failed_signals or 0,
                "pending_signals": stats.pending_signals or 0,
                "time_range_hours": hours,
                "last_updated": end_time.isoformat()
            }
        }

        return APIResponse.success_response(
            data=kpi_data,
            message="KPI指标获取成功"
        )

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get KPI metrics: {str(e)}"
        )


@router.get("/nodes/{node_id}", response_model=APIResponse[Dict[str, Any]])
async def get_node_details(
    node_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session),
):
    """获取流程节点的详细信息"""
    try:
        node_details = {}

        if node_id == "create":
            # 创建节点详情
            create_stats_query = text("""
                SELECT
                    COUNT(*) as total_created,
                    COUNT(CASE WHEN created_at >= :recent_time THEN 1 END) as recent_created
                FROM signals
            """)

            recent_time = datetime.now(timezone.utc) - timedelta(hours=1)
            result = await db.execute(create_stats_query, {"recent_time": recent_time})
            stats = result.fetchone()

            node_details = {
                "id": node_id,
                "name": "创建信号",
                "description": "信号从外部平台接收并创建",
                "status": "active",
                "metrics": {
                    "total_created": stats.total_created,
                    "recent_created": stats.recent_created,
                    "creation_rate": f"{stats.recent_created}/小时"
                }
            }

        elif node_id == "parse":
            # 解析节点详情
            parse_stats_query = text("""
                SELECT
                    COUNT(CASE WHEN ai_parse_status = 'pending' THEN 1 END) as pending_count,
                    COUNT(CASE WHEN ai_parse_status = 'success' THEN 1 END) as success_count,
                    COUNT(CASE WHEN ai_parse_status = 'failed' THEN 1 END) as failed_count,
                    AVG(CASE
                        WHEN ai_parse_status IN ('success', 'failed') AND updated_at > created_at
                        THEN EXTRACT(EPOCH FROM (updated_at - created_at))
                    END) as avg_parse_time
                FROM signals
                WHERE created_at >= :recent_time
            """)

            recent_time = datetime.now(timezone.utc) - timedelta(hours=24)
            result = await db.execute(parse_stats_query, {"recent_time": recent_time})
            stats = result.fetchone()

            total_parsed = (stats.success_count or 0) + (stats.failed_count or 0)
            parse_success_rate = (stats.success_count / total_parsed * 100) if total_parsed > 0 else 0

            node_details = {
                "id": node_id,
                "name": "解析意图",
                "description": "AI解析信号内容，提取交易意图",
                "status": "processing" if stats.pending_count > 0 else "idle",
                "metrics": {
                    "pending_count": stats.pending_count or 0,
                    "success_count": stats.success_count or 0,
                    "failed_count": stats.failed_count or 0,
                    "success_rate": round(parse_success_rate, 1),
                    "avg_parse_time": round(stats.avg_parse_time or 0, 1)
                }
            }

        # 其他节点的详情可以类似实现...

        return APIResponse.success_response(
            data=node_details,
            message="节点详情获取成功"
        )

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get node details: {str(e)}"
        )
