import uuid
from datetime import datetime, timezone
from typing import List, Optional

import structlog
from fastapi import (
    APIRouter,
    BackgroundTasks,
    Depends,
    HTTPException,
    Query,
    status,
)
from sqlalchemy import select, update
from sqlalchemy.ext.asyncio import AsyncSession

from ...core.auth import get_current_user
from ...core.database import get_db
from ...core.models import PendingAction, User
from ...core.schemas import AgentState, APIResponse

# 配置结构化日志
logger = structlog.get_logger()

router = APIRouter(prefix="/actions", tags=["actions"])


@router.get("/pending", response_model=APIResponse[List[dict]])
async def get_pending_actions(
    current_user: User = Depends(get_current_user),
    status_filter: Optional[str] = Query(None, alias="status", description="过滤特定状态"),
    limit: int = Query(50, ge=1, le=100, description="返回结果数量限制"),
    offset: int = Query(0, ge=0, description="分页偏移量"),
    db: AsyncSession = Depends(get_db),
):
    """
    获取用户的待处理动作列表
    """
    try:
        query = select(PendingAction).where(PendingAction.user_id == current_user.id)

        # 应用过滤条件
        if status_filter:
            query = query.where(PendingAction.status == status_filter)

        # 按创建时间倒序排列
        query = query.order_by(PendingAction.created_at.desc())

        # 应用分页
        query = query.offset(offset).limit(limit)

        result = await db.execute(query)
        pending_actions = result.scalars().all()

        # 转换为字典格式
        actions_data = []
        for action in pending_actions:
            actions_data.append(
                {
                    "id": str(action.id),
                    "task_id": action.task_id,
                    "action_type": action.action_type,
                    "details": action.details,
                    "status": action.status,
                    "created_at": action.created_at.isoformat(),
                    "expires_at": action.expires_at.isoformat()
                    if action.expires_at
                    else None,
                }
            )

        return APIResponse.success_response(data=actions_data, message="获取待处理动作成功")

    except Exception as e:
        logger.error("获取待处理动作失败", user_id=current_user.id, error=str(e))
        raise HTTPException(status_code=500, detail="获取待处理动作失败")


@router.post("/{action_id}/respond", status_code=status.HTTP_202_ACCEPTED)
async def respond_to_action(
    action_id: uuid.UUID,
    response: dict,
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_db),
):
    """
    用户响应一个待确认的动作

    Args:
        action_id: 动作ID
        response: 用户响应，包含 approved 字段
        background_tasks: 后台任务
        db: 数据库会话

    Returns:
        响应状态
    """
    # 查询待处理动作
    query = select(PendingAction).where(PendingAction.id == action_id)
    result = await db.execute(query)
    pending_action = result.scalar_one_or_none()

    if not pending_action:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Action with ID {action_id} not found",
        )

    # 检查动作是否已过期或已处理
    if pending_action.status != "PENDING":
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Action with ID {action_id} is already {pending_action.status}",
        )

    # 检查是否已过期 - use timezone-naive comparison to match expires_at format
    if pending_action.expires_at < datetime.utcnow():
        # 更新状态为过期
        await db.execute(
            update(PendingAction)
            .where(PendingAction.id == action_id)
            .values(status="EXPIRED")
        )
        await db.commit()

        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Action with ID {action_id} has expired",
        )

    # 获取用户响应
    approved = response.get("approved", False)

    # 更新动作状态
    new_status = "APPROVED" if approved else "REJECTED"
    await db.execute(
        update(PendingAction)
        .where(PendingAction.id == action_id)
        .values(status=new_status)
    )
    await db.commit()

    # TODO: Implement agent resumption logic when needed
    # For now, just log that the action was responded to
    logger.info(
        "User responded to pending action",
        action_id=action_id,
        task_id=pending_action.task_id,
        approved=approved,
    )

    return {
        "status": "accepted",
        "message": f"Action {action_id} has been {new_status.lower()}",
    }


async def resume_agent_task(state: AgentState, db: AsyncSession):
    """
    恢复Agent任务

    Args:
        state: Agent状态
        db: 数据库会话
    """
    try:
        # TODO: 实现agent模块后恢复此功能
        # await run_agent(state, db, resume=True)
        logger.info(
            "Agent task resume requested but agent module not implemented yet",
            task_id=str(state.task_id),
        )
    except Exception as e:
        logger.exception(
            "Failed to resume agent task",
            task_id=str(state.task_id),
            error=str(e),
        )
