from datetime import datetime
from typing import List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from ..core.database import get_db
from ..core.models import PendingAction
from ..core.schemas import PendingActionResponse, PendingActionUpdate
from ..core.security import get_current_user_obj

router = APIRouter(prefix="/pending-actions", tags=["pending-actions"])


@router.get("/", response_model=List[PendingActionResponse])
async def get_pending_actions(
    current_user=Depends(get_current_user_obj),
    status: Optional[str] = Query(None, description="过滤特定状态"),
    limit: int = Query(50, ge=1, le=100, description="返回结果数量限制"),
    offset: int = Query(0, ge=0, description="分页偏移量"),
    db: AsyncSession = Depends(get_db),
):
    """
    获取用户的待处理动作列表
    """
    query = select(PendingAction).where(PendingAction.user_id == current_user.id)

    # 应用过滤条件
    if status:
        query = query.where(PendingAction.status == status)

    # 按创建时间倒序排列
    query = query.order_by(PendingAction.created_at.desc())

    # 应用分页
    query = query.offset(offset).limit(limit)

    result = await db.execute(query)
    pending_actions = result.scalars().all()

    return pending_actions


@router.get("/{action_id}", response_model=PendingActionResponse)
async def get_pending_action(
    action_id: UUID,
    current_user=Depends(get_current_user_obj),
    db: AsyncSession = Depends(get_db),
):
    """
    获取单个待处理动作详情
    """
    query = select(PendingAction).where(
        PendingAction.id == action_id, PendingAction.user_id == current_user.id
    )

    result = await db.execute(query)
    pending_action = result.scalar_one_or_none()

    if not pending_action:
        raise HTTPException(status_code=404, detail="待处理动作未找到")

    return pending_action


@router.put("/{action_id}", response_model=PendingActionResponse)
async def update_pending_action(
    action_id: UUID,
    action_data: PendingActionUpdate,
    current_user=Depends(get_current_user_obj),
    db: AsyncSession = Depends(get_db),
):
    """
    更新待处理动作（通常用于用户响应）
    """
    query = select(PendingAction).where(
        PendingAction.id == action_id, PendingAction.user_id == current_user.id
    )

    result = await db.execute(query)
    pending_action = result.scalar_one_or_none()

    if not pending_action:
        raise HTTPException(status_code=404, detail="待处理动作未找到")

    # 检查动作是否已过期
    if pending_action.expires_at and pending_action.expires_at < datetime.utcnow():
        raise HTTPException(status_code=400, detail="待处理动作已过期")

    # 检查动作是否已处理
    if pending_action.status != "PENDING":
        raise HTTPException(status_code=400, detail="待处理动作已被处理")

    # 更新字段
    update_data = action_data.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(pending_action, field, value)

    # 设置处理时间
    pending_action.resolved_at = datetime.utcnow()

    await db.commit()
    await db.refresh(pending_action)

    return pending_action


@router.post("/{action_id}/approve")
async def approve_pending_action(
    action_id: UUID,
    current_user=Depends(get_current_user_obj),
    db: AsyncSession = Depends(get_db),
):
    """
    批准待处理动作
    """
    query = select(PendingAction).where(
        PendingAction.id == action_id, PendingAction.user_id == current_user.id
    )

    result = await db.execute(query)
    pending_action = result.scalar_one_or_none()

    if not pending_action:
        raise HTTPException(status_code=404, detail="待处理动作未找到")

    # 检查动作是否已过期
    if pending_action.expires_at and pending_action.expires_at < datetime.utcnow():
        raise HTTPException(status_code=400, detail="待处理动作已过期")

    # 检查动作是否已处理
    if pending_action.status != "PENDING":
        raise HTTPException(status_code=400, detail="待处理动作已被处理")

    # 更新状态
    pending_action.status = "APPROVED"
    pending_action.resolved_at = datetime.utcnow()
    pending_action.response = {"approved": True}

    await db.commit()
    await db.refresh(pending_action)

    return {"message": "待处理动作已批准", "action_id": action_id}


@router.post("/{action_id}/reject")
async def reject_pending_action(
    action_id: UUID,
    current_user=Depends(get_current_user_obj),
    db: AsyncSession = Depends(get_db),
):
    """
    拒绝待处理动作
    """
    query = select(PendingAction).where(
        PendingAction.id == action_id, PendingAction.user_id == current_user.id
    )

    result = await db.execute(query)
    pending_action = result.scalar_one_or_none()

    if not pending_action:
        raise HTTPException(status_code=404, detail="待处理动作未找到")

    # 检查动作是否已过期
    if pending_action.expires_at and pending_action.expires_at < datetime.utcnow():
        raise HTTPException(status_code=400, detail="待处理动作已过期")

    # 检查动作是否已处理
    if pending_action.status != "PENDING":
        raise HTTPException(status_code=400, detail="待处理动作已被处理")

    # 更新状态
    pending_action.status = "REJECTED"
    pending_action.resolved_at = datetime.utcnow()
    pending_action.response = {"approved": False}

    await db.commit()
    await db.refresh(pending_action)

    return {"message": "待处理动作已拒绝", "action_id": action_id}


@router.delete("/{action_id}")
async def delete_pending_action(
    action_id: UUID,
    current_user=Depends(get_current_user_obj),
    db: AsyncSession = Depends(get_db),
):
    """
    删除待处理动作
    """
    query = select(PendingAction).where(
        PendingAction.id == action_id, PendingAction.user_id == current_user.id
    )

    result = await db.execute(query)
    pending_action = result.scalar_one_or_none()

    if not pending_action:
        raise HTTPException(status_code=404, detail="待处理动作未找到")

    await db.delete(pending_action)
    await db.commit()

    return {"message": "待处理动作已删除"}
