# Bandit安全扫描配置文件

# 要扫描的目录
tests:
  - app/

# 要跳过的目录
skips:
  - tests/
  - alembic/versions/

# 要排除的测试ID
exclude_dirs:
  - /tests/
  - /alembic/versions/

# 严重性级别
severity: medium

# 置信度级别
confidence: medium

# 要跳过的特定测试
skips:
  # B101: 使用assert语句 - 在测试中是正常的
  - B101
  # B601: shell注入 - 我们有控制的shell命令
  - B601
  # B603: subprocess调用 - 我们需要调用外部程序
  - B603

# 自定义规则
tests:
  # 硬编码密码检查
  - B105
  - B106
  - B107
  
  # SQL注入检查
  - B608
  
  # 不安全的随机数生成
  - B311
  
  # 不安全的哈希算法
  - B303
  - B324
  
  # 不安全的序列化
  - B301
  - B302
  - B506
  
  # 路径遍历
  - B202
  
  # 不安全的临时文件
  - B108
  - B109
  
  # 不安全的网络请求
  - B310
  
  # 不安全的加密
  - B304
  - B305
  - B306

# 忽略的文件模式
exclude:
  - "*/tests/*"
  - "*/test_*.py"
  - "*/conftest.py"
  - "*/alembic/versions/*"

# 报告格式
format: json

# 输出文件
output: bandit-report.json
