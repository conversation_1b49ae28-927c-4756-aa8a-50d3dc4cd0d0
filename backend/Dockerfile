FROM python:3.11-slim

WORKDIR /app

# 配置apt使用国内镜像源并安装构建工具(psutil 需要编译)和curl(健康检查需要)
RUN sed -i 's/deb.debian.org/mirrors.tuna.tsinghua.edu.cn/g' /etc/apt/sources.list.d/debian.sources && \
    apt-get update && apt-get install -y \
    gcc \
    python3-dev \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 配置pip使用国内镜像源
RUN pip config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple/ && \
    pip config set global.trusted-host pypi.tuna.tsinghua.edu.cn

# 安装依赖
COPY requirements.txt .
RUN pip install -r requirements.txt

# 复制应用代码
COPY . .

# 设置默认环境变量
ENV HOST=0.0.0.0
ENV PORT=8000
ENV RELOAD=False
ENV LOG_LEVEL=info

# 运行启动脚本
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"] 