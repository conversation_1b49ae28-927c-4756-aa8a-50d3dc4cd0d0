#!/usr/bin/env python3
"""
Docker网络连接测试脚本
测试Docker容器内访问宿主机代理的连接
"""

import socket
import sys
import subprocess
import platform


def test_host_connectivity():
    """测试宿主机连接性"""
    print("🔍 测试Docker容器到宿主机的连接...")
    
    # Docker容器访问宿主机的常见地址
    host_addresses = [
        '127.0.0.1',           # 本地回环（通常在容器内不可用）
        'localhost',           # 本地主机
        'host.docker.internal', # Docker Desktop提供的宿主机地址
        '**********',          # Docker默认网桥网关
        '************',        # Docker Desktop for Mac
        '***********',         # 常见路由器地址
    ]
    
    port = 7897
    working_hosts = []
    
    for host in host_addresses:
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(3)
            result = sock.connect_ex((host, port))
            sock.close()
            
            if result == 0:
                print(f"✅ {host}:{port} - 连接成功")
                working_hosts.append(host)
            else:
                print(f"❌ {host}:{port} - 连接失败 (错误码: {result})")
        except socket.gaierror as e:
            print(f"❌ {host}:{port} - DNS解析失败: {e}")
        except Exception as e:
            print(f"❌ {host}:{port} - 连接异常: {e}")
    
    return working_hosts


def get_network_info():
    """获取网络信息"""
    print("\n🔍 获取网络信息...")
    
    try:
        # 获取路由信息
        if platform.system() == "Linux":
            result = subprocess.run(['ip', 'route'], capture_output=True, text=True)
            if result.returncode == 0:
                print("📋 路由表:")
                for line in result.stdout.strip().split('\n'):
                    if 'default' in line or '172.' in line or '192.168.' in line:
                        print(f"  {line}")
        
        # 获取网络接口信息
        result = subprocess.run(['hostname', '-I'], capture_output=True, text=True)
        if result.returncode == 0:
            ips = result.stdout.strip().split()
            print(f"📍 容器IP地址: {', '.join(ips)}")
        
    except Exception as e:
        print(f"⚠️ 无法获取网络信息: {e}")


def test_dns_resolution():
    """测试DNS解析"""
    print("\n🔍 测试DNS解析...")
    
    hosts_to_test = [
        'host.docker.internal',
        'localhost',
        'discord.com',
        'google.com'
    ]
    
    for host in hosts_to_test:
        try:
            ip = socket.gethostbyname(host)
            print(f"✅ {host} -> {ip}")
        except socket.gaierror as e:
            print(f"❌ {host} - DNS解析失败: {e}")


def check_docker_environment():
    """检查Docker环境"""
    print("\n🔍 检查Docker环境...")
    
    # 检查是否在容器内
    try:
        with open('/proc/1/cgroup', 'r') as f:
            content = f.read()
            if 'docker' in content:
                print("📦 确认运行在Docker容器内")
                return True
            else:
                print("🖥️ 运行在宿主机上")
                return False
    except FileNotFoundError:
        print("🖥️ 运行在宿主机上（非Linux系统）")
        return False


def suggest_solutions(working_hosts):
    """提供解决方案建议"""
    print("\n💡 解决方案建议:")
    
    if working_hosts:
        print(f"✅ 找到可用的宿主机地址: {', '.join(working_hosts)}")
        print("🔧 修复方法:")
        print("1. 更新.env.development文件中的DISCORD_PROXY配置:")
        for host in working_hosts:
            print(f"   DISCORD_PROXY=http://{host}:7897")
        print("\n2. 或者在docker-compose.dev.yml中使用host网络模式:")
        print("   network_mode: host")
        
    else:
        print("❌ 未找到可用的宿主机地址")
        print("🔧 可能的解决方案:")
        print("1. 确保代理软件绑定到0.0.0.0:7897而不是127.0.0.1:7897")
        print("2. 在docker-compose.dev.yml中添加:")
        print("   extra_hosts:")
        print("     - \"host.docker.internal:host-gateway\"")
        print("3. 使用host网络模式:")
        print("   network_mode: host")
        print("4. 或者在宿主机上运行代理，并配置防火墙允许Docker网络访问")


def main():
    """主函数"""
    print("🚀 Docker网络连接测试")
    print("=" * 50)
    
    # 检查Docker环境
    is_docker = check_docker_environment()
    
    # 获取网络信息
    get_network_info()
    
    # 测试DNS解析
    test_dns_resolution()
    
    # 测试宿主机连接
    working_hosts = test_host_connectivity()
    
    # 提供解决方案
    suggest_solutions(working_hosts)
    
    print("\n" + "=" * 50)
    if working_hosts:
        print("🎉 找到可用的连接方式")
        return True
    else:
        print("❌ 无法连接到宿主机代理")
        return False


if __name__ == "__main__":
    try:
        result = main()
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试过程中发生异常: {e}")
        sys.exit(1)
