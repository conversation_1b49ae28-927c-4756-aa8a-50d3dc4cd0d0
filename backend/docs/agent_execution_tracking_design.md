# Agent工作流执行追踪系统设计文档

## 1. 设计概述

### 1.1 设计原则
- **单表设计**：使用1个核心表 `agent_execution_traces` 存储所有追踪数据
- **最小侵入**：对现有Agent工作流代码的修改最小化
- **性能优先**：追踪系统不能影响主业务流程性能
- **实用导向**：专注于解决实际问题，避免过度复杂化

### 1.2 核心功能
- 记录Agent工作流各节点的执行状态和时间
- 存储节点输入/输出数据和错误信息
- 提供简单的查询API获取执行历史
- 实现基础的性能指标收集

## 2. 数据库设计

### 2.1 核心表结构：agent_execution_traces

```sql
CREATE TABLE agent_execution_traces (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    task_id UUID NOT NULL,
    user_id UUID NOT NULL REFERENCES users(id),
    signal_id UUID REFERENCES signals(id),
    node_name VARCHAR(50) NOT NULL,
    execution_order INTEGER NOT NULL,
    
    -- 状态字段
    status VARCHAR(20) NOT NULL DEFAULT 'started',
    started_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE,
    duration_ms INTEGER,
    
    -- 数据字段
    input_data JSONB,
    output_data JSONB,
    error_data JSONB,
    execution_metrics JSONB,
    
    -- 时间戳
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- 索引
CREATE INDEX idx_agent_traces_task_id ON agent_execution_traces(task_id);
CREATE INDEX idx_agent_traces_user_id ON agent_execution_traces(user_id);
CREATE INDEX idx_agent_traces_node_name ON agent_execution_traces(node_name);
CREATE INDEX idx_agent_traces_started_at ON agent_execution_traces(started_at);
CREATE INDEX idx_agent_traces_status ON agent_execution_traces(status);

-- 自动更新updated_at
CREATE OR REPLACE FUNCTION update_agent_traces_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_agent_traces_updated_at
    BEFORE UPDATE ON agent_execution_traces
    FOR EACH ROW
    EXECUTE FUNCTION update_agent_traces_updated_at();
```

### 2.2 状态枚举
- `started`: 节点开始执行
- `completed`: 节点执行成功
- `failed`: 节点执行失败
- `timeout`: 节点执行超时
- `cancelled`: 节点执行被取消

### 2.3 数据保留策略
- 自动清理30天前的追踪记录
- 保留失败记录60天用于问题分析

## 3. 追踪指标设计

### 3.1 通用指标（所有节点）
```json
{
    "duration_ms": 1250,
    "retry_count": 0,
    "memory_usage_mb": 45.2,
    "cpu_time_ms": 890
}
```

### 3.2 节点特定指标

#### Parse节点
```json
{
    "llm_provider": "deepseek",
    "llm_call_duration_ms": 800,
    "input_tokens": 150,
    "output_tokens": 75,
    "parsed_intents_count": 3,
    "parsing_success": true
}
```

#### Context节点
```json
{
    "db_queries_count": 4,
    "db_query_duration_ms": 120,
    "context_items_loaded": 15,
    "cache_hits": 2,
    "cache_misses": 2
}
```

#### Risk节点
```json
{
    "risk_score": 30.5,
    "risk_checks_count": 8,
    "risk_checks_passed": 8,
    "risk_level": "low",
    "approval_required": false
}
```

#### Execute节点
```json
{
    "orders_count": 1,
    "execution_mode": "simulation",
    "orders_successful": 1,
    "orders_failed": 0,
    "total_volume_usdt": 5000.0
}
```

## 4. API接口设计

### 4.1 获取任务执行追踪
```
GET /api/v1/agent/traces/task/{task_id}

Response:
{
    "task_id": "uuid",
    "user_id": "uuid", 
    "signal_id": "uuid",
    "total_duration_ms": 5420,
    "status": "completed",
    "traces": [
        {
            "node_name": "parse",
            "execution_order": 1,
            "status": "completed",
            "started_at": "2025-07-30T12:00:00Z",
            "completed_at": "2025-07-30T12:00:01Z",
            "duration_ms": 1200,
            "metrics": {...}
        }
    ]
}
```

### 4.2 获取用户最近执行记录
```
GET /api/v1/agent/traces/user/{user_id}/recent?limit=10

Response:
{
    "traces": [
        {
            "task_id": "uuid",
            "signal_id": "uuid",
            "started_at": "2025-07-30T12:00:00Z",
            "completed_at": "2025-07-30T12:05:20Z",
            "total_duration_ms": 5420,
            "status": "completed",
            "nodes_count": 6,
            "failed_nodes": 0
        }
    ],
    "total": 25,
    "page": 1,
    "limit": 10
}
```

## 5. 实现策略

### 5.1 第一阶段：数据库表和模型
1. 创建数据库迁移脚本
2. 定义SQLAlchemy模型类
3. 创建Pydantic响应模型

### 5.2 第二阶段：追踪装饰器
1. 实现通用的节点追踪装饰器
2. 支持异步写入，避免阻塞主流程
3. 实现错误处理，确保追踪失败不影响业务

### 5.3 第三阶段：集成验证
1. 集成到Parse、Risk、Execute节点
2. 验证数据写入和查询功能
3. 性能测试确保开销<2%

### 5.4 第四阶段：API和测试
1. 实现查询API接口
2. 添加基础单元测试
3. 集成测试验证完整流程

## 6. 技术实现细节

### 6.1 追踪装饰器设计
```python
@trace_agent_node(node_name="parse")
async def parse_intents(state: AgentState, db: AsyncSession) -> AgentState:
    # 现有业务逻辑
    pass
```

### 6.2 异步写入策略
- 使用后台任务队列写入追踪数据
- 批量写入优化，减少数据库连接
- 写入失败时记录到日志，不影响主流程

### 6.3 性能优化
- 使用连接池复用数据库连接
- 实现简单的内存缓存减少查询
- 定期清理历史数据保持表大小

## 7. 监控和维护

### 7.1 数据清理
- 每日凌晨自动清理30天前的记录
- 保留错误记录用于问题分析
- 监控表大小和查询性能

### 7.2 性能监控
- 追踪系统自身的性能指标
- 监控对主业务流程的影响
- 设置告警阈值

## 8. 未来扩展预留

### 8.1 可视化界面
- 执行时间线图表
- 性能趋势分析
- 错误统计报告

### 8.2 高级功能
- 实时WebSocket推送
- 复杂的性能分析
- 自动化问题诊断

但当前阶段专注于基础功能实现，保持系统简洁性。
