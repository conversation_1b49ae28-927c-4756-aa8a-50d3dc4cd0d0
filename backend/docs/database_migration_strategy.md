# 数据库迁移策略和最佳实践

## 问题背景

在后端设计验证过程中，发现了数据库表结构与代码模型定义不匹配的问题：
- `agent_checkpoints` 表缺少 `updated_at` 和 `status` 字段
- 代码期望的字段与实际表结构不一致

## 根本原因分析

### 1. 缺乏统一的数据库版本管理
- 模型定义更新后，数据库表结构未同步更新
- 缺少自动化的迁移脚本执行机制
- 开发环境与生产环境可能存在结构差异

### 2. 测试策略问题
- 为了通过测试而简化查询，掩盖了真实问题
- 缺少数据库结构一致性验证
- 测试环境与实际需求脱节

## 正确的解决策略

### 1. 建立规范的迁移流程

#### 迁移脚本命名规范
```
migrations/
├── 001_initial_schema.py
├── 002_create_agent_checkpoints.py
├── 003_fix_agent_checkpoints_schema.py
└── 004_add_user_preferences.py
```

#### 迁移脚本模板
```python
#!/usr/bin/env python3
"""
数据迁移脚本：[描述]
版本：XXX
日期：YYYY-MM-DD

问题：[具体问题描述]
解决方案：[解决方案说明]
影响范围：[影响的表和字段]
"""

async def upgrade():
    """升级数据库结构"""
    pass

async def downgrade():
    """回滚数据库结构"""
    pass

async def verify():
    """验证迁移结果"""
    pass
```

### 2. 自动化验证机制

#### 模型与表结构一致性检查
```python
async def validate_schema_consistency():
    """验证数据库表结构与模型定义的一致性"""
    
    # 1. 获取模型定义的字段
    model_fields = get_model_fields(AgentCheckpoint)
    
    # 2. 获取数据库表的实际字段
    db_fields = await get_table_fields("agent_checkpoints")
    
    # 3. 比较差异
    missing_fields = model_fields - db_fields
    extra_fields = db_fields - model_fields
    
    # 4. 报告不一致
    if missing_fields or extra_fields:
        raise SchemaInconsistencyError(
            f"表结构不一致: 缺失{missing_fields}, 多余{extra_fields}"
        )
```

### 3. 测试策略改进

#### 集成测试中的数据库验证
```python
@pytest.fixture(scope="session")
async def verify_database_schema():
    """测试开始前验证数据库结构"""
    await validate_schema_consistency()
    yield
    # 测试后清理
```

#### 端到端测试的完整性
```python
async def test_agent_workflow_with_checkpoints():
    """测试完整的Agent工作流，包括checkpoint保存"""
    
    # 1. 执行Agent工作流
    result = await execute_agent_workflow(test_input)
    
    # 2. 验证checkpoint记录
    checkpoints = await get_checkpoints(result.task_id)
    assert len(checkpoints) > 0
    
    # 3. 验证字段完整性
    for cp in checkpoints:
        assert cp.updated_at is not None
        assert cp.status in VALID_STATUSES
        assert cp.node_name in VALID_NODES
```

### 4. 持续集成中的数据库检查

#### CI/CD 流程集成
```yaml
# .github/workflows/backend-tests.yml
- name: Validate Database Schema
  run: |
    docker-compose -f docker-compose.test.yml up -d postgres
    python backend/scripts/validate_schema.py
    
- name: Run Migration Tests
  run: |
    python backend/scripts/test_migrations.py
```

## 实施建议

### 短期目标（已完成）
- ✅ 修复 `agent_checkpoints` 表结构
- ✅ 添加缺失的 `updated_at` 和 `status` 字段
- ✅ 验证修复结果

### 中期目标
- [ ] 建立自动化的模式验证脚本
- [ ] 创建迁移脚本执行框架
- [ ] 在测试套件中集成数据库结构验证

### 长期目标
- [ ] 实现数据库版本管理系统
- [ ] 建立生产环境迁移流程
- [ ] 创建数据库结构监控和告警机制

## 经验教训

### 1. 永远不要为了通过测试而掩盖问题
- 简化查询只是临时绕过，不是解决方案
- 真正的问题会在生产环境中暴露
- 技术债务会累积并影响系统稳定性

### 2. 数据库结构变更需要严格管理
- 模型定义变更必须伴随迁移脚本
- 开发、测试、生产环境必须保持一致
- 每次变更都需要验证和测试

### 3. 测试应该验证真实场景
- 测试环境应该尽可能接近生产环境
- 集成测试应该覆盖完整的数据流
- 不应该为了测试通过而修改业务逻辑

## 结论

通过这次问题的发现和解决，我们建立了更加健壮的数据库管理策略。
关键是要始终坚持"修复根本问题而非掩盖症状"的原则，
确保系统的长期稳定性和可维护性。
