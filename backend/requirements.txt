# Web 框架
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6

# 数据库
sqlalchemy[asyncio]==2.0.23
alembic==1.12.1
aiosqlite==0.19.0
psycopg2-binary==2.9.9
asyncpg>=0.29.0

# 数据验证和序列化
pydantic==2.5.0
pydantic-settings==2.1.0

# WebSocket 支持
websockets==12.0

# JWT 认证
PyJWT==2.8.0
cryptography==41.0.7
passlib[bcrypt]==1.7.4

# HTTP 客户端
httpx==0.25.2
aiohttp==3.9.1

# 日志
structlog==23.2.0

# 数据处理
pandas==2.1.4
numpy>=1.24.0

# 加密货币交易
ccxt==4.1.64

# 环境变量
python-dotenv==1.0.0

# 测试框架和工具
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-mock==3.12.0
pytest-cov==4.1.0
pytest-xdist==3.5.0
pytest-html==3.2.0
pytest-sugar==0.9.7
pytest-clarity==1.0.1
pytest-randomly==3.15.0
pytest-repeat==0.9.3
pytest-rerunfailures==12.0
pytest-parallel==0.1.1
pytest-timeout==2.2.0
pytest-env==1.1.3
pytest-benchmark==4.0.0
allure-pytest==2.13.2
hypothesis==6.92.1
factory-boy==3.3.0
faker==20.1.0
freezegun==1.2.2
mimesis==11.1.0
responses==0.24.1
httpretty==1.1.4
mock==5.1.0
coverage==7.3.2

# 类型检查
mypy==1.7.1

# 代码格式化
black==23.11.0
isort==5.12.0
pre-commit==3.6.0

# AI/LLM 相关
openai>=1.6.1
anthropic>=0.3.0
instructor>=0.4.0
langchain>=0.2.0
langchain-openai>=0.1.0
langgraph>=0.2.0

# Discord集成
# 注意：discord.py-self 在 Python 3.12 下需要使用最新开发版本
# 安装方法：pip install git+https://github.com/dolfies/discord.py-self.git
# 原因：PyPI 上的版本与 Python 3.12 存在兼容性问题（CachedSlotProperty TypeError）
# 开发版本修复了这个问题，并且不需要 Intents（与标准 discord.py 不同）
# discord.py-self>=2.1.0  # 临时注释掉，使用可用版本
discord.py-self==2.0.1

# 其他工具
click==8.1.7
rich==13.7.0
psutil==5.9.6

# E2E测试和浏览器自动化
playwright==1.40.0
selenium==4.15.2
webdriver-manager==4.0.1

# TODO 精简依赖
# 性能和监控工具
memory-profiler==0.61.0
# line-profiler==4.1.1  # 临时注释：Python 3.12 兼容性问题
py-spy==0.3.14
locust==2.17.0
psutil==5.9.6

# 数据分析和可视化
jupyter==1.0.0
ipython==8.18.1
matplotlib==3.8.2
seaborn==0.13.0
plotly==5.17.0