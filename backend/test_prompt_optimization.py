#!/usr/bin/env python3
"""
测试优化后的Prompt模板
直接调用LLM API验证价格提取功能
"""

import asyncio
import json
import os
from typing import List, Dict, Any
from app.agent.prompts import PARSE_INTENTS_PROMPT
from app.core.schemas import ParsedIntent
from pydantic import ValidationError
import httpx


async def test_llm_parsing():
    """测试LLM解析功能"""
    
    # 测试用例
    test_cases = [
        {
            "name": "复杂交易信号",
            "input": "ETH Entry 1: $3731.786, Entry 2: $3712.76, Stop/loss: $3688.00, Take profit: $3998.00, Position size: 2.92 ETH, risking 200 for 1600"
        },
        {
            "name": "简单买入信号", 
            "input": "买入 BTC 50u"
        },
        {
            "name": "价格信号",
            "input": "BTC 68000 止损 67000"
        }
    ]
    
    print("=== 测试优化后的Prompt模板 ===")
    print(f"Prompt长度: {len(PARSE_INTENTS_PROMPT)} 字符")
    print()
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"测试用例 {i}: {test_case['name']}")
        print(f"输入: {test_case['input']}")
        print("-" * 60)
        
        # 构建LLM请求消息
        messages = [
            {
                "role": "system",
                "content": PARSE_INTENTS_PROMPT
            },
            {
                "role": "user", 
                "content": test_case['input']
            }
        ]
        
        print("发送给LLM的消息:")
        print(f"System: {PARSE_INTENTS_PROMPT[:200]}...")
        print(f"User: {test_case['input']}")
        print()
        
        # 尝试调用真实LLM API
        try:
            response = await call_deepseek_api(messages)
            if response:
                print("LLM响应:")
                print(json.dumps(response, indent=2, ensure_ascii=False))

                # 验证响应格式
                try:
                    if isinstance(response, list):
                        intents = [ParsedIntent(**intent) for intent in response]
                        print(f"\n✅ 成功解析 {len(intents)} 个意图:")
                        for j, intent in enumerate(intents, 1):
                            print(f"  {j}. {intent.intent_type} - {intent.side} - {intent.symbol}")
                            if intent.entry_price:
                                print(f"     入场价格: {intent.entry_price}")
                            if intent.stop_loss_price:
                                print(f"     止损价格: {intent.stop_loss_price}")
                            if intent.take_profit_price:
                                print(f"     止盈价格: {intent.take_profit_price}")
                    else:
                        print("❌ 响应格式不正确，应该是数组")
                except ValidationError as e:
                    print(f"❌ 响应验证失败: {e}")
            else:
                print("❌ LLM API调用失败")
        except Exception as e:
            print(f"❌ API调用异常: {e}")

        print("=" * 80)
        print()


async def call_deepseek_api(messages: List[Dict[str, str]]) -> List[Dict[str, Any]]:
    """调用DeepSeek API"""
    api_key = os.getenv("DEEPSEEK_API_KEY", "sk-test-key")

    if api_key == "sk-test-key":
        print("⚠️  使用测试API密钥，可能无法调用真实API")
        return None

    try:
        async with httpx.AsyncClient() as client:
            response = await client.post(
                "https://api.deepseek.com/v1/chat/completions",
                headers={
                    "Authorization": f"Bearer {api_key}",
                    "Content-Type": "application/json"
                },
                json={
                    "model": "deepseek-chat",
                    "messages": messages,
                    "temperature": 0.1,
                    "max_tokens": 4000
                },
                timeout=30.0
            )

            if response.status_code == 200:
                result = response.json()
                content = result["choices"][0]["message"]["content"]

                # 尝试解析JSON响应
                try:
                    return json.loads(content)
                except json.JSONDecodeError:
                    print(f"❌ 无法解析JSON响应: {content}")
                    return None
            else:
                print(f"❌ API调用失败: {response.status_code} - {response.text}")
                return None

    except Exception as e:
        print(f"❌ API调用异常: {e}")
        return None


async def validate_prompt_structure():
    """验证Prompt结构"""
    print("=== Prompt结构分析 ===")
    
    # 检查关键部分
    required_sections = [
        "You are an expert trading signal parser",
        "**Task**",
        "**Rules**",
        "**Price Extraction**",
        "**Examples**"
    ]
    
    for section in required_sections:
        if section in PARSE_INTENTS_PROMPT:
            print(f"✅ 包含: {section}")
        else:
            print(f"❌ 缺失: {section}")
    
    print()
    print(f"总长度: {len(PARSE_INTENTS_PROMPT)} 字符")
    print(f"行数: {len(PARSE_INTENTS_PROMPT.splitlines())}")
    
    # 检查价格提取规则
    price_keywords = ["entry_price", "stop_loss_price", "take_profit_price", "quantity_base", "risk_amount"]
    print("\n价格字段覆盖:")
    for keyword in price_keywords:
        if keyword in PARSE_INTENTS_PROMPT:
            print(f"✅ {keyword}")
        else:
            print(f"❌ {keyword}")


if __name__ == "__main__":
    print("开始测试优化后的Prompt模板...")
    print()
    
    asyncio.run(validate_prompt_structure())
    print()
    asyncio.run(test_llm_parsing())
