# crypto_trader 项目环境配置文件 (统一版)
#
# 此文件整合了项目的完整环境配置，包含系统依赖和Python包依赖
# 根据《0. 项目规范.md》创建的统一环境配置
#
# 使用方法:
# conda env create -f backend/environment.yml
# conda activate crypto-trader
#
# 更新环境:
# conda env update -f backend/environment.yml --prune
#
# 删除环境:
# conda env remove -n crypto-trader

name: crypto-trader
channels:
  - conda-forge
  - defaults

dependencies:
  # Python环境
  - python=3.11

  # 系统级数据库工具
  # - postgresql
  # - psycopg2

  # 前端Node.js环境
  - nodejs=20.17.0
  - npm

  # 系统工具
  - curl
  - wget
  - git

  # Python包管理
  - pip

  # Python包通过requirements.txt安装
  - pip:
    - -r requirements.txt