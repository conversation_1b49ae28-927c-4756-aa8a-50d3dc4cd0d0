#!/usr/bin/env python3
"""
数据迁移脚本：添加缺失的updated_at字段
根据《0. 项目规范.md》的PostgreSQL优先原则

问题：orders表和其他一些表缺少updated_at字段，导致单元测试失败
解决方案：
1. 为orders表添加updated_at字段
2. 为其他缺失updated_at字段的表添加该字段
3. 创建触发器自动更新updated_at字段
4. 确保与模型定义一致
"""

import asyncio
import asyncpg
from datetime import datetime
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

async def add_updated_at_columns():
    """添加缺失的updated_at字段"""
    
    conn = await asyncpg.connect(
        host="localhost",
        port=5432,
        database="crypto_trader_test",
        user="crypto_trader",
        password="test_password_123"
    )
    
    try:
        print("🚀 开始添加缺失的updated_at字段...")
        
        # 1. 检查orders表是否缺少updated_at字段
        orders_has_updated_at = await conn.fetchval("""
            SELECT COUNT(*) 
            FROM information_schema.columns 
            WHERE table_name = 'orders' AND column_name = 'updated_at'
        """)
        
        if orders_has_updated_at == 0:
            print("📝 为orders表添加updated_at字段...")
            await conn.execute("""
                ALTER TABLE orders 
                ADD COLUMN updated_at TIMESTAMP WITHOUT TIME ZONE 
                DEFAULT CURRENT_TIMESTAMP NOT NULL
            """)
            print("✅ orders表updated_at字段添加成功")
        else:
            print("✅ orders表已有updated_at字段")
        
        # 2. 检查其他可能缺少updated_at字段的表
        tables_to_check = [
            'users', 'exchange_configs', 'conditional_orders', 
            'risk_configs', 'pending_actions'
        ]
        
        for table_name in tables_to_check:
            # 检查表是否存在
            table_exists = await conn.fetchval("""
                SELECT COUNT(*) 
                FROM information_schema.tables 
                WHERE table_name = $1
            """, table_name)
            
            if table_exists == 0:
                print(f"⚠️ 表 {table_name} 不存在，跳过")
                continue
            
            # 检查是否有updated_at字段
            has_updated_at = await conn.fetchval("""
                SELECT COUNT(*) 
                FROM information_schema.columns 
                WHERE table_name = $1 AND column_name = 'updated_at'
            """, table_name)
            
            if has_updated_at == 0:
                print(f"📝 为{table_name}表添加updated_at字段...")
                await conn.execute(f"""
                    ALTER TABLE {table_name} 
                    ADD COLUMN updated_at TIMESTAMP WITHOUT TIME ZONE 
                    DEFAULT CURRENT_TIMESTAMP NOT NULL
                """)
                print(f"✅ {table_name}表updated_at字段添加成功")
            else:
                print(f"✅ {table_name}表已有updated_at字段")
        
        # 3. 创建或更新触发器函数
        print("🔧 创建updated_at触发器函数...")
        await conn.execute("""
            CREATE OR REPLACE FUNCTION update_updated_at_column()
            RETURNS TRIGGER AS $$
            BEGIN
                NEW.updated_at = CURRENT_TIMESTAMP;
                RETURN NEW;
            END;
            $$ language 'plpgsql';
        """)
        
        # 4. 为orders表创建触发器
        print("🔧 为orders表创建updated_at触发器...")
        await conn.execute("""
            DROP TRIGGER IF EXISTS update_orders_updated_at ON orders;
            CREATE TRIGGER update_orders_updated_at
                BEFORE UPDATE ON orders 
                FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
        """)
        
        # 5. 为其他表创建触发器
        for table_name in tables_to_check:
            table_exists = await conn.fetchval("""
                SELECT COUNT(*) 
                FROM information_schema.tables 
                WHERE table_name = $1
            """, table_name)
            
            if table_exists > 0:
                has_updated_at = await conn.fetchval("""
                    SELECT COUNT(*) 
                    FROM information_schema.columns 
                    WHERE table_name = $1 AND column_name = 'updated_at'
                """, table_name)
                
                if has_updated_at > 0:
                    print(f"🔧 为{table_name}表创建updated_at触发器...")
                    await conn.execute(f"""
                        DROP TRIGGER IF EXISTS update_{table_name}_updated_at ON {table_name};
                        CREATE TRIGGER update_{table_name}_updated_at
                            BEFORE UPDATE ON {table_name} 
                            FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
                    """)
        
        print("✅ 所有updated_at字段和触发器创建完成")
        return True
        
    except Exception as e:
        print(f"❌ 迁移失败: {e}")
        return False
    finally:
        await conn.close()

async def verify_migration():
    """验证迁移结果"""
    conn = await asyncpg.connect(
        host="localhost",
        port=5432,
        database="crypto_trader_test",
        user="crypto_trader",
        password="test_password_123"
    )
    
    try:
        print("🔍 验证迁移结果...")
        
        # 检查orders表的updated_at字段
        orders_column = await conn.fetchrow("""
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_name = 'orders' AND column_name = 'updated_at'
        """)
        
        if orders_column:
            print(f"✅ orders表updated_at字段:")
            print(f"  - 类型: {orders_column['data_type']}")
            print(f"  - 可空: {orders_column['is_nullable']}")
            print(f"  - 默认值: {orders_column['column_default'] or '无'}")
        else:
            print("❌ orders表仍然缺少updated_at字段")
            return False
        
        # 检查触发器
        triggers = await conn.fetch("""
            SELECT trigger_name, event_manipulation, action_timing
            FROM information_schema.triggers 
            WHERE trigger_name LIKE '%updated_at%'
            ORDER BY trigger_name
        """)
        
        print(f"🔧 找到 {len(triggers)} 个updated_at触发器:")
        for trigger in triggers:
            print(f"  - {trigger['trigger_name']}: {trigger['action_timing']} {trigger['event_manipulation']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False
    finally:
        await conn.close()

async def main():
    """主函数"""
    print("=" * 60)
    print("添加缺失的updated_at字段迁移")
    print("=" * 60)
    
    # 执行迁移
    success = await add_updated_at_columns()
    
    if success:
        # 验证迁移
        verified = await verify_migration()
        if verified:
            print("\n🎉 迁移成功完成！")
            return True
        else:
            print("\n❌ 迁移验证失败！")
            return False
    else:
        print("\n❌ 迁移执行失败！")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
