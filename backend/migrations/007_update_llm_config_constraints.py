"""
更新LLM配置约束 - 允许API密钥为空（用于更新操作）

修改时间: 2025-07-29
修改内容:
- 删除 non_empty_api_key 约束，允许API密钥为空
- 这样在更新操作时可以不提供API密钥，保持原有密钥
"""

import asyncio
import asyncpg
from typing import Optional
import structlog

# 配置日志
logger = structlog.get_logger()

async def run_migration():
    """运行数据库迁移"""
    
    # 数据库连接配置 (Docker容器内部)
    DATABASE_URL = "*************************************************************/crypto_trader_dev"
    
    conn: Optional[asyncpg.Connection] = None
    
    try:
        # 连接数据库
        conn = await asyncpg.connect(DATABASE_URL)
        await logger.ainfo("数据库连接成功")
        
        # 删除 non_empty_api_key 约束
        await conn.execute("""
            ALTER TABLE llm_configs 
            DROP CONSTRAINT IF EXISTS non_empty_api_key;
        """)
        
        await logger.ainfo("成功删除 non_empty_api_key 约束")
        
        # 验证约束已删除
        constraints = await conn.fetch("""
            SELECT constraint_name, constraint_type 
            FROM information_schema.table_constraints 
            WHERE table_name = 'llm_configs' 
            AND constraint_name = 'non_empty_api_key';
        """)
        
        if not constraints:
            await logger.ainfo("确认 non_empty_api_key 约束已成功删除")
        else:
            await logger.aerror("约束删除失败，仍然存在")
            
        await logger.ainfo("LLM配置约束更新迁移完成")
        
    except Exception as e:
        await logger.aerror("迁移失败", error=str(e), exc_info=True)
        raise
    finally:
        if conn:
            await conn.close()
            await logger.ainfo("数据库连接已关闭")

if __name__ == "__main__":
    asyncio.run(run_migration())
