"""
创建Agent执行追踪表

Migration: 004_create_agent_execution_traces
Created: 2025-07-30
Purpose: 实现简化的Agent工作流执行追踪系统
"""

import asyncio
import asyncpg
from datetime import datetime

async def upgrade():
    """创建agent_execution_traces表和相关索引"""

    # 数据库连接配置 - 使用Docker内部网络
    DATABASE_URL = "*************************************************************/crypto_trader_dev"

    conn = await asyncpg.connect(DATABASE_URL)
    
    try:
        print("🚀 开始创建Agent执行追踪表...")
        
        # 1. 创建主表
        await conn.execute("""
            CREATE TABLE IF NOT EXISTS agent_execution_traces (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                task_id UUID NOT NULL,
                user_id UUID NOT NULL REFERENCES users(id),
                signal_id UUID REFERENCES signals(id),
                node_name VARCHAR(50) NOT NULL,
                execution_order INTEGER NOT NULL,
                
                -- 状态字段
                status VARCHAR(20) NOT NULL DEFAULT 'started',
                started_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
                completed_at TIMESTAMP WITH TIME ZONE,
                duration_ms INTEGER,
                
                -- 数据字段
                input_data JSONB,
                output_data JSONB,
                error_data JSONB,
                execution_metrics JSONB,
                
                -- 时间戳
                created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
                updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
            );
        """)
        print("✅ 创建agent_execution_traces表成功")
        
        # 2. 创建索引
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_agent_traces_task_id ON agent_execution_traces(task_id);",
            "CREATE INDEX IF NOT EXISTS idx_agent_traces_user_id ON agent_execution_traces(user_id);", 
            "CREATE INDEX IF NOT EXISTS idx_agent_traces_node_name ON agent_execution_traces(node_name);",
            "CREATE INDEX IF NOT EXISTS idx_agent_traces_started_at ON agent_execution_traces(started_at);",
            "CREATE INDEX IF NOT EXISTS idx_agent_traces_status ON agent_execution_traces(status);",
            "CREATE INDEX IF NOT EXISTS idx_agent_traces_task_order ON agent_execution_traces(task_id, execution_order);"
        ]
        
        for index_sql in indexes:
            await conn.execute(index_sql)
        print("✅ 创建索引成功")
        
        # 3. 创建更新触发器函数
        await conn.execute("""
            CREATE OR REPLACE FUNCTION update_agent_traces_updated_at()
            RETURNS TRIGGER AS $$
            BEGIN
                NEW.updated_at = NOW();
                RETURN NEW;
            END;
            $$ LANGUAGE plpgsql;
        """)
        print("✅ 创建触发器函数成功")
        
        # 4. 创建触发器
        await conn.execute("""
            DROP TRIGGER IF EXISTS trigger_agent_traces_updated_at ON agent_execution_traces;
            CREATE TRIGGER trigger_agent_traces_updated_at
                BEFORE UPDATE ON agent_execution_traces
                FOR EACH ROW
                EXECUTE FUNCTION update_agent_traces_updated_at();
        """)
        print("✅ 创建触发器成功")
        
        # 5. 创建数据清理函数
        await conn.execute("""
            CREATE OR REPLACE FUNCTION cleanup_old_agent_traces()
            RETURNS INTEGER AS $$
            DECLARE
                deleted_count INTEGER := 0;
                temp_count INTEGER;
            BEGIN
                -- 删除30天前的成功记录
                DELETE FROM agent_execution_traces
                WHERE created_at < NOW() - INTERVAL '30 days'
                AND status = 'completed';

                GET DIAGNOSTICS temp_count = ROW_COUNT;
                deleted_count := deleted_count + temp_count;

                -- 删除60天前的失败记录
                DELETE FROM agent_execution_traces
                WHERE created_at < NOW() - INTERVAL '60 days'
                AND status IN ('failed', 'timeout', 'cancelled');

                GET DIAGNOSTICS temp_count = ROW_COUNT;
                deleted_count := deleted_count + temp_count;

                RETURN deleted_count;
            END;
            $$ LANGUAGE plpgsql;
        """)
        print("✅ 创建数据清理函数成功")
        
        # 6. 验证表结构
        result = await conn.fetch("""
            SELECT column_name, data_type, is_nullable
            FROM information_schema.columns 
            WHERE table_name = 'agent_execution_traces'
            ORDER BY ordinal_position;
        """)
        
        print("\n📋 表结构验证:")
        for row in result:
            nullable = "NULL" if row['is_nullable'] == 'YES' else "NOT NULL"
            print(f"  - {row['column_name']}: {row['data_type']} {nullable}")
        
        # 7. 验证索引
        indexes_result = await conn.fetch("""
            SELECT indexname, indexdef
            FROM pg_indexes 
            WHERE tablename = 'agent_execution_traces'
            ORDER BY indexname;
        """)
        
        print("\n📋 索引验证:")
        for row in indexes_result:
            print(f"  - {row['indexname']}")
        
        print(f"\n✅ Agent执行追踪表创建完成! 共创建 {len(result)} 个字段和 {len(indexes_result)} 个索引")
        
    except Exception as e:
        print(f"❌ 迁移失败: {e}")
        raise
    finally:
        await conn.close()

async def downgrade():
    """回滚迁移"""
    DATABASE_URL = "*************************************************************/crypto_trader_dev"

    conn = await asyncpg.connect(DATABASE_URL)
    
    try:
        print("🔄 开始回滚Agent执行追踪表...")
        
        # 删除触发器
        await conn.execute("DROP TRIGGER IF EXISTS trigger_agent_traces_updated_at ON agent_execution_traces;")
        
        # 删除函数
        await conn.execute("DROP FUNCTION IF EXISTS update_agent_traces_updated_at();")
        await conn.execute("DROP FUNCTION IF EXISTS cleanup_old_agent_traces();")
        
        # 删除表（会自动删除索引）
        await conn.execute("DROP TABLE IF EXISTS agent_execution_traces;")
        
        print("✅ 回滚完成")
        
    except Exception as e:
        print(f"❌ 回滚失败: {e}")
        raise
    finally:
        await conn.close()

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "downgrade":
        asyncio.run(downgrade())
    else:
        asyncio.run(upgrade())
