"""
添加信号去重机制和Agent关联字段

Revision ID: 007
Revises: 006
Create Date: 2025-07-31 03:00:00.000000
"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy import text
from sqlalchemy.dialects import postgresql

# revision identifiers
revision = '007'
down_revision = '006'
branch_labels = None
depends_on = None


async def upgrade():
    """升级数据库：添加信号去重和Agent关联功能"""
    
    # 1. 为signals表添加去重相关字段
    await op.execute(text("""
        ALTER TABLE signals 
        ADD COLUMN content_hash VARCHAR(64),
        ADD COLUMN dedup_key VARCHAR(255),
        ADD COLUMN is_duplicate BOOLEAN DEFAULT FALSE,
        ADD COLUMN original_signal_id UUID REFERENCES signals(id),
        ADD COLUMN agent_task_id UUID,
        ADD COLUMN agent_processing_status VARCHAR(20) DEFAULT 'pending' 
            CHECK (agent_processing_status IN ('pending', 'processing', 'completed', 'failed', 'skipped'));
    """))
    
    # 2. 创建去重相关索引
    await op.execute(text("""
        CREATE INDEX idx_signals_content_hash ON signals(content_hash);
        CREATE INDEX idx_signals_dedup_key ON signals(dedup_key);
        CREATE INDEX idx_signals_is_duplicate ON signals(is_duplicate);
        CREATE INDEX idx_signals_agent_task_id ON signals(agent_task_id);
        CREATE INDEX idx_signals_agent_status ON signals(agent_processing_status);
        CREATE UNIQUE INDEX idx_signals_unique_dedup ON signals(user_id, dedup_key) 
            WHERE is_duplicate = FALSE;
    """))
    
    # 3. 为现有信号生成content_hash和dedup_key
    await op.execute(text("""
        UPDATE signals 
        SET 
            content_hash = encode(sha256(content::bytea), 'hex'),
            dedup_key = encode(sha256((user_id::text || '|' || platform || '|' || content)::bytea), 'hex')
        WHERE content_hash IS NULL;
    """))
    
    # 4. 设置字段为NOT NULL
    await op.execute(text("""
        ALTER TABLE signals 
        ALTER COLUMN content_hash SET NOT NULL,
        ALTER COLUMN dedup_key SET NOT NULL;
    """))


async def downgrade():
    """降级数据库：移除信号去重和Agent关联功能"""
    
    # 移除索引
    await op.execute(text("""
        DROP INDEX IF EXISTS idx_signals_content_hash;
        DROP INDEX IF EXISTS idx_signals_dedup_key;
        DROP INDEX IF EXISTS idx_signals_is_duplicate;
        DROP INDEX IF EXISTS idx_signals_agent_task_id;
        DROP INDEX IF EXISTS idx_signals_agent_status;
        DROP INDEX IF EXISTS idx_signals_unique_dedup;
    """))
    
    # 移除字段
    await op.execute(text("""
        ALTER TABLE signals 
        DROP COLUMN IF EXISTS content_hash,
        DROP COLUMN IF EXISTS dedup_key,
        DROP COLUMN IF EXISTS is_duplicate,
        DROP COLUMN IF EXISTS original_signal_id,
        DROP COLUMN IF EXISTS agent_task_id,
        DROP COLUMN IF EXISTS agent_processing_status;
    """))
