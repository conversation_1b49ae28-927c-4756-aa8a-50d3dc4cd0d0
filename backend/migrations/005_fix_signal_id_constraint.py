"""修复signal_id外键约束

Revision ID: 005
Revises: 004
Create Date: 2025-07-30 14:40:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '005'
down_revision = '004'
branch_labels = None
depends_on = None


def upgrade():
    """修复signal_id外键约束，允许NULL值"""
    
    # 删除现有的外键约束
    op.drop_constraint('agent_execution_traces_signal_id_fkey', 'agent_execution_traces', type_='foreignkey')
    
    # 重新创建外键约束，允许NULL值
    op.create_foreign_key(
        'agent_execution_traces_signal_id_fkey',
        'agent_execution_traces', 
        'signals',
        ['signal_id'], 
        ['id'],
        ondelete='SET NULL'  # 当信号被删除时，设置为NULL
    )
    
    print("✅ 外键约束修复完成 - signal_id现在可以为NULL")


def downgrade():
    """回滚外键约束修改"""
    
    # 删除修改后的外键约束
    op.drop_constraint('agent_execution_traces_signal_id_fkey', 'agent_execution_traces', type_='foreignkey')
    
    # 恢复原始的外键约束
    op.create_foreign_key(
        'agent_execution_traces_signal_id_fkey',
        'agent_execution_traces', 
        'signals',
        ['signal_id'], 
        ['id']
    )
    
    print("✅ 外键约束回滚完成")
