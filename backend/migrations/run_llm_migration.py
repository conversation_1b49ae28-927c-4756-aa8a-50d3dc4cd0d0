"""
独立运行LLM配置表迁移脚本
"""

import asyncio
import asyncpg
import os

async def create_llm_configs_table():
    """创建LLM配置表"""
    # 从环境变量获取数据库URL
    db_url = os.getenv("DATABASE_URL", "postgresql://crypto_trader:dev_password_123@localhost:5433/crypto_trader_dev")
    
    # 移除协议前缀
    if db_url.startswith("postgresql+asyncpg://"):
        db_url = db_url.replace("postgresql+asyncpg://", "postgresql://")
    
    conn = await asyncpg.connect(db_url)
    
    try:
        # 创建LLM配置表
        await conn.execute("""
            CREATE TABLE IF NOT EXISTS llm_configs (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
                
                -- 基础配置
                config_name VARCHAR(100) NOT NULL,
                provider VARCHAR(20) NOT NULL,
                enabled BOOLEAN NOT NULL DEFAULT false,
                is_default BOOLEAN NOT NULL DEFAULT false,
                
                -- API配置
                api_key TEXT NOT NULL,
                api_base_url VARCHAR(255),
                model_name VARCHAR(100) NOT NULL,
                
                -- 请求参数配置
                max_tokens INTEGER NOT NULL DEFAULT 4096,
                temperature DECIMAL(3,2) NOT NULL DEFAULT 0.7,
                timeout_seconds INTEGER NOT NULL DEFAULT 60,
                max_retries INTEGER NOT NULL DEFAULT 3,
                
                -- 时间戳
                created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
                updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
                
                -- 约束
                CONSTRAINT valid_llm_provider CHECK (provider IN ('deepseek', 'gemini', 'chatgpt', 'claude')),
                CONSTRAINT valid_temperature CHECK (temperature >= 0.0 AND temperature <= 2.0),
                CONSTRAINT valid_max_tokens CHECK (max_tokens > 0 AND max_tokens <= 32768),
                CONSTRAINT valid_timeout CHECK (timeout_seconds > 0 AND timeout_seconds <= 300),
                CONSTRAINT valid_max_retries CHECK (max_retries >= 0 AND max_retries <= 10),
                CONSTRAINT non_empty_api_key CHECK (length(api_key) > 0),
                CONSTRAINT non_empty_config_name CHECK (length(config_name) > 0),
                CONSTRAINT unique_user_llm_config_name UNIQUE (user_id, config_name)
            );
        """)
        
        # 创建部分唯一约束（确保每个用户只有一个默认配置）
        await conn.execute("""
            CREATE UNIQUE INDEX IF NOT EXISTS unique_user_default_llm 
            ON llm_configs (user_id) 
            WHERE is_default = true;
        """)
        
        # 创建索引
        await conn.execute("""
            CREATE INDEX IF NOT EXISTS idx_llm_configs_user_id ON llm_configs(user_id);
        """)
        
        await conn.execute("""
            CREATE INDEX IF NOT EXISTS idx_llm_configs_user_enabled ON llm_configs(user_id, enabled);
        """)
        
        await conn.execute("""
            CREATE INDEX IF NOT EXISTS idx_llm_configs_provider ON llm_configs(provider);
        """)
        
        await conn.execute("""
            CREATE INDEX IF NOT EXISTS idx_llm_configs_updated ON llm_configs(updated_at);
        """)
        
        # 创建更新时间戳的触发器
        await conn.execute("""
            CREATE OR REPLACE FUNCTION update_llm_configs_updated_at()
            RETURNS TRIGGER AS $$
            BEGIN
                NEW.updated_at = NOW();
                RETURN NEW;
            END;
            $$ language 'plpgsql';
        """)
        
        await conn.execute("""
            DROP TRIGGER IF EXISTS trigger_update_llm_configs_updated_at ON llm_configs;
            CREATE TRIGGER trigger_update_llm_configs_updated_at
                BEFORE UPDATE ON llm_configs
                FOR EACH ROW
                EXECUTE FUNCTION update_llm_configs_updated_at();
        """)
        
        print("✅ LLM配置表创建成功")
        
    except Exception as e:
        print(f"❌ 创建LLM配置表失败: {e}")
        raise
    finally:
        await conn.close()

if __name__ == "__main__":
    asyncio.run(create_llm_configs_table())
