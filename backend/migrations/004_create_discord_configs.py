#!/usr/bin/env python3
"""
数据库迁移脚本：创建Discord信号源配置表
基于简化设计原则，专注于Discord配置需求

功能：
1. 创建signal_source_configs表
2. 支持Discord配置存储
3. 为未来扩展预留基本架构空间
"""

import asyncio
import sys
from pathlib import Path
from sqlalchemy import text

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.core.database import AsyncSessionLocal


async def upgrade():
    """升级数据库：创建Discord信号源配置表"""
    session = AsyncSessionLocal()
    try:
        print("🚀 开始创建Discord信号源配置表...")

        # 1. 检查表是否已存在
        result = await session.execute(text("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables
                WHERE table_schema = 'public'
                AND table_name = 'signal_source_configs'
            )
        """))
        table_exists = result.scalar()

        if table_exists:
            print("✅ signal_source_configs表已存在，跳过创建")
            return True

        # 2. 创建表结构
        print("📋 创建表结构...")
        await session.execute(text("""
            CREATE TABLE signal_source_configs (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,

                -- 基础配置
                source_type VARCHAR(50) NOT NULL DEFAULT 'discord',
                source_name VARCHAR(100) NOT NULL,
                enabled BOOLEAN NOT NULL DEFAULT false,

                -- Discord认证配置
                encrypted_token TEXT NOT NULL,

                -- Discord过滤配置
                server_ids TEXT[] DEFAULT '{}',
                channel_ids TEXT[] DEFAULT '{}',
                author_ids TEXT[] DEFAULT '{}',
                allowed_message_types TEXT[] DEFAULT '{"text"}',

                -- 时间戳
                created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
                updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),

                -- 约束
                CONSTRAINT valid_source_type CHECK (source_type = 'discord'),
                CONSTRAINT non_empty_token CHECK (length(encrypted_token) > 0),
                CONSTRAINT valid_message_types CHECK (
                    allowed_message_types <@ ARRAY['text', 'embed', 'attachment', 'reply']
                ),
                CONSTRAINT unique_user_source_name UNIQUE(user_id, source_name)
            )
        """))

        # 3. 创建索引
        print("🔍 创建索引...")
        await session.execute(text("""
            CREATE INDEX idx_signal_configs_user_id ON signal_source_configs(user_id)
        """))
        await session.execute(text("""
            CREATE INDEX idx_signal_configs_user_enabled ON signal_source_configs(user_id, enabled)
        """))
        await session.execute(text("""
            CREATE INDEX idx_signal_configs_updated ON signal_source_configs(updated_at)
        """))
        await session.execute(text("""
            CREATE INDEX idx_signal_configs_server_ids ON signal_source_configs USING gin(server_ids)
        """))
        await session.execute(text("""
            CREATE INDEX idx_signal_configs_channel_ids ON signal_source_configs USING gin(channel_ids)
        """))
        await session.execute(text("""
            CREATE INDEX idx_signal_configs_author_ids ON signal_source_configs USING gin(author_ids)
        """))

        # 4. 创建更新时间触发器
        print("⏰ 创建更新时间触发器...")
        await session.execute(text("""
            CREATE OR REPLACE FUNCTION update_signal_source_configs_updated_at()
            RETURNS TRIGGER AS $$
            BEGIN
                NEW.updated_at = NOW();
                RETURN NEW;
            END;
            $$ language 'plpgsql'
        """))

        await session.execute(text("""
            CREATE TRIGGER update_signal_source_configs_updated_at
                BEFORE UPDATE ON signal_source_configs
                FOR EACH ROW EXECUTE FUNCTION update_signal_source_configs_updated_at()
        """))

        await session.commit()
        print("✅ signal_source_configs表创建完成")
        return True
    except Exception as e:
        await session.rollback()
        print(f"❌ 创建表失败: {e}")
        return False
    finally:
        await session.close()


async def downgrade():
    """降级数据库：删除Discord信号源配置表"""
    session = AsyncSessionLocal()
    try:
        print("🗑️ 开始删除Discord信号源配置表...")

        # 删除触发器和函数
        await session.execute(text("""
            DROP TRIGGER IF EXISTS update_signal_source_configs_updated_at ON signal_source_configs
        """))
        await session.execute(text("""
            DROP FUNCTION IF EXISTS update_signal_source_configs_updated_at()
        """))

        # 删除表（会自动删除索引）
        await session.execute(text("DROP TABLE IF EXISTS signal_source_configs CASCADE"))

        await session.commit()
        print("✅ signal_source_configs表删除完成")
        return True
    except Exception as e:
        await session.rollback()
        print(f"❌ 删除表失败: {e}")
        return False
    finally:
        await session.close()


if __name__ == "__main__":
    print("开始执行Discord信号源配置表迁移...")
    asyncio.run(upgrade())
    print("迁移完成！")
