#!/usr/bin/env python3
"""
数据迁移脚本：修复agent_checkpoints表结构
解决数据库表结构与代码模型不匹配的问题

问题分析：
1. 实际表缺少 updated_at 字段
2. 代码期望 current_node 但表中是 node_name
3. 代码期望 status 字段但表中没有

解决方案：
1. 添加 updated_at 字段
2. 保持 node_name 字段（代码中统一使用 node_name）
3. 添加 status 字段用于状态跟踪
4. 创建适当的索引和约束
"""

import asyncio
import asyncpg
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

async def fix_agent_checkpoints_schema():
    """修复agent_checkpoints表结构"""
    
    conn = await asyncpg.connect(
        host="localhost",
        port=5432,
        database="crypto_trader_test",
        user="crypto_trader",
        password="test_password_123"
    )
    
    try:
        print("🚀 开始修复agent_checkpoints表结构...")
        
        # 1. 检查表是否存在
        table_exists = await conn.fetchval("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name = 'agent_checkpoints'
            )
        """)
        
        if not table_exists:
            print("❌ agent_checkpoints表不存在，请先运行002_create_agent_checkpoints.py")
            return False
        
        # 2. 检查当前表结构
        print("📋 检查当前表结构...")
        columns = await conn.fetch("""
            SELECT column_name, data_type, is_nullable
            FROM information_schema.columns 
            WHERE table_name = 'agent_checkpoints'
            ORDER BY ordinal_position
        """)
        
        existing_columns = {col['column_name'] for col in columns}
        print(f"   现有字段: {', '.join(existing_columns)}")
        
        # 3. 添加缺失的字段
        changes_made = False
        
        # 添加 updated_at 字段
        if 'updated_at' not in existing_columns:
            print("📝 添加 updated_at 字段...")
            await conn.execute("""
                ALTER TABLE agent_checkpoints 
                ADD COLUMN updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP
            """)
            
            # 更新现有记录的 updated_at 为 created_at
            await conn.execute("""
                UPDATE agent_checkpoints 
                SET updated_at = created_at 
                WHERE updated_at IS NULL
            """)
            
            # 设置为非空
            await conn.execute("""
                ALTER TABLE agent_checkpoints 
                ALTER COLUMN updated_at SET NOT NULL
            """)
            changes_made = True
        
        # 添加 status 字段
        if 'status' not in existing_columns:
            print("📝 添加 status 字段...")
            await conn.execute("""
                ALTER TABLE agent_checkpoints 
                ADD COLUMN status VARCHAR(20) DEFAULT 'processing'
            """)
            
            # 更新现有记录的状态
            await conn.execute("""
                UPDATE agent_checkpoints 
                SET status = CASE 
                    WHEN node_name IN ('Success', 'Completed') THEN 'completed'
                    WHEN node_name IN ('Failure', 'AnalyzeError') THEN 'failed'
                    ELSE 'processing'
                END
                WHERE status = 'processing'
            """)
            
            # 设置为非空
            await conn.execute("""
                ALTER TABLE agent_checkpoints 
                ALTER COLUMN status SET NOT NULL
            """)
            changes_made = True
        
        # 4. 创建/更新触发器（如果添加了 updated_at）
        if 'updated_at' not in existing_columns:
            print("⏰ 创建/更新 updated_at 触发器...")
            
            # 删除旧触发器（如果存在）
            await conn.execute("""
                DROP TRIGGER IF EXISTS update_agent_checkpoints_updated_at 
                ON agent_checkpoints
            """)
            
            # 确保触发器函数存在
            await conn.execute("""
                CREATE OR REPLACE FUNCTION update_updated_at_column()
                RETURNS TRIGGER AS $$
                BEGIN
                    NEW.updated_at = CURRENT_TIMESTAMP;
                    RETURN NEW;
                END;
                $$ language 'plpgsql'
            """)
            
            # 创建新触发器
            await conn.execute("""
                CREATE TRIGGER update_agent_checkpoints_updated_at 
                BEFORE UPDATE ON agent_checkpoints 
                FOR EACH ROW EXECUTE FUNCTION update_updated_at_column()
            """)
        
        # 5. 更新约束
        print("🔒 更新表约束...")
        
        # 删除旧的节点名称约束（如果存在）
        await conn.execute("""
            ALTER TABLE agent_checkpoints 
            DROP CONSTRAINT IF EXISTS valid_node_name
        """)
        
        # 添加新的节点名称约束
        await conn.execute("""
            ALTER TABLE agent_checkpoints 
            ADD CONSTRAINT valid_node_name 
            CHECK (node_name IN (
                'Preprocess', 'Parse', 'Context', 'Plan', 'Risk', 'Execute', 
                'AnalyzeError', 'UserConfirm', 'Success', 'Failure', 'Completed'
            ))
        """)
        
        # 添加状态约束
        await conn.execute("""
            ALTER TABLE agent_checkpoints 
            DROP CONSTRAINT IF EXISTS valid_status
        """)
        
        await conn.execute("""
            ALTER TABLE agent_checkpoints 
            ADD CONSTRAINT valid_status 
            CHECK (status IN ('initialized', 'processing', 'completed', 'failed', 'cancelled'))
        """)
        
        # 6. 创建新索引
        print("🔍 创建/更新索引...")
        
        # 状态索引
        await conn.execute("""
            CREATE INDEX IF NOT EXISTS idx_agent_checkpoints_status 
            ON agent_checkpoints(status)
        """)
        
        # 复合索引：用户+状态+更新时间
        await conn.execute("""
            CREATE INDEX IF NOT EXISTS idx_agent_checkpoints_user_status_updated 
            ON agent_checkpoints(user_id, status, updated_at DESC)
        """)
        
        # 复合索引：任务+节点+更新时间
        await conn.execute("""
            CREATE INDEX IF NOT EXISTS idx_agent_checkpoints_task_node_updated 
            ON agent_checkpoints(task_id, node_name, updated_at DESC)
        """)
        
        if changes_made:
            print("✅ agent_checkpoints表结构修复完成")
        else:
            print("✅ agent_checkpoints表结构已是最新，无需修复")
        
        return True
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        await conn.close()

async def verify_schema():
    """验证表结构修复结果"""
    
    conn = await asyncpg.connect(
        host="localhost",
        port=5432,
        database="crypto_trader_test",
        user="crypto_trader",
        password="test_password_123"
    )
    
    try:
        print("\n🔍 验证表结构修复结果...")
        
        # 检查字段
        columns = await conn.fetch("""
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_name = 'agent_checkpoints'
            ORDER BY ordinal_position
        """)
        
        print("📋 当前表结构:")
        for col in columns:
            nullable = "可空" if col['is_nullable'] == 'YES' else "非空"
            default = f" (默认: {col['column_default']})" if col['column_default'] else ""
            print(f"   - {col['column_name']}: {col['data_type']} ({nullable}){default}")
        
        # 检查约束
        constraints = await conn.fetch("""
            SELECT constraint_name, constraint_type
            FROM information_schema.table_constraints
            WHERE table_name = 'agent_checkpoints'
            AND constraint_type IN ('CHECK', 'FOREIGN KEY')
        """)
        
        if constraints:
            print("\n🔒 表约束:")
            for constraint in constraints:
                print(f"   - {constraint['constraint_name']}: {constraint['constraint_type']}")
        
        # 检查索引
        indexes = await conn.fetch("""
            SELECT indexname, indexdef
            FROM pg_indexes
            WHERE tablename = 'agent_checkpoints'
            AND schemaname = 'public'
        """)
        
        if indexes:
            print("\n🔍 表索引:")
            for index in indexes:
                print(f"   - {index['indexname']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False
    finally:
        await conn.close()

async def main():
    """主函数"""
    print("=" * 60)
    print("Agent Checkpoints 表结构修复工具")
    print("=" * 60)
    
    # 修复表结构
    success = await fix_agent_checkpoints_schema()
    
    if success:
        # 验证修复结果
        await verify_schema()
        print("\n✅ 表结构修复和验证完成")
    else:
        print("\n❌ 表结构修复失败")
        return False
    
    return True

if __name__ == "__main__":
    asyncio.run(main())
