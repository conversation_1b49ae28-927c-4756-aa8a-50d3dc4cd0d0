"""
创建信号表 (signals)

创建时间: 2025-07-26
描述: 添加信号功能模块的数据表，支持多平台消息存储和管理
"""

import uuid
from datetime import datetime, timezone
from sqlalchemy import text
from app.core.database import get_db_session


def utc_now():
    """返回UTC时间"""
    return datetime.now(timezone.utc).replace(tzinfo=None)


async def upgrade():
    """升级数据库：创建signals表"""
    async with get_db_session() as session:
        # 创建signals表
        await session.execute(text("""
            CREATE TABLE signals (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                user_id UUID NOT NULL REFERENCES users(id),
                platform VARCHAR(20) NOT NULL CHECK (platform IN ('discord', 'telegram', 'manual')),
                platform_message_id VARCHAR(255),
                channel_id VARCHAR(255),
                channel_name VA<PERSON>HA<PERSON>(255),
                author_id VARCHAR(255),
                author_name <PERSON><PERSON><PERSON><PERSON>(255),
                content TEXT NOT NULL,
                raw_content TEXT,
                message_type VARCHAR(50) DEFAULT 'text' CHECK (message_type IN ('text', 'embed', 'attachment', 'reply')),
                metadata JSONB DEFAULT '{}',
                signal_strength NUMERIC(3,2) CHECK (signal_strength IS NULL OR (signal_strength >= 0 AND signal_strength <= 1)),
                is_processed BOOLEAN DEFAULT FALSE NOT NULL,
                processed_at TIMESTAMP,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,

                -- 约束
                CONSTRAINT valid_processed_time CHECK (processed_at IS NULL OR processed_at >= created_at)
            );
        """))

        # 创建唯一索引（条件索引）
        await session.execute(text("""
            CREATE UNIQUE INDEX unique_platform_message
            ON signals (platform, platform_message_id, channel_id)
            WHERE platform_message_id IS NOT NULL AND channel_id IS NOT NULL;
        """))

        # 创建基础索引
        await session.execute(text("""
            CREATE INDEX idx_signals_user_id ON signals(user_id);
            CREATE INDEX idx_signals_platform ON signals(platform);
            CREATE INDEX idx_signals_channel_id ON signals(channel_id);
            CREATE INDEX idx_signals_created_at ON signals(created_at);
            CREATE INDEX idx_signals_is_processed ON signals(is_processed);
            CREATE INDEX idx_signals_signal_strength ON signals(signal_strength);
        """))

        # 创建复合索引
        await session.execute(text("""
            CREATE INDEX idx_signals_user_platform ON signals(user_id, platform);
            CREATE INDEX idx_signals_user_created ON signals(user_id, created_at DESC);
            CREATE INDEX idx_signals_platform_created ON signals(platform, created_at DESC);
            CREATE INDEX idx_signals_processed_created ON signals(is_processed, created_at DESC);
        """))

        # 创建GIN索引用于JSONB字段
        await session.execute(text("""
            CREATE INDEX idx_signals_metadata_gin ON signals USING GIN (metadata);
        """))

        # 创建更新时间触发器
        await session.execute(text("""
            CREATE OR REPLACE FUNCTION update_signals_updated_at()
            RETURNS TRIGGER AS $$
            BEGIN
                NEW.updated_at = CURRENT_TIMESTAMP;
                RETURN NEW;
            END;
            $$ LANGUAGE plpgsql;

            CREATE TRIGGER trigger_signals_updated_at
                BEFORE UPDATE ON signals
                FOR EACH ROW
                EXECUTE FUNCTION update_signals_updated_at();
        """))

        await session.commit()
        print("✅ 成功创建signals表及相关索引")


async def downgrade():
    """降级数据库：删除signals表"""
    async with get_db_session() as session:
        # 删除触发器和函数
        await session.execute(text("""
            DROP TRIGGER IF EXISTS trigger_signals_updated_at ON signals;
            DROP FUNCTION IF EXISTS update_signals_updated_at();
        """))

        # 删除表（会自动删除索引）
        await session.execute(text("DROP TABLE IF EXISTS signals CASCADE;"))

        await session.commit()
        print("✅ 成功删除signals表")


if __name__ == "__main__":
    import asyncio

    print("开始执行signals表迁移...")
    asyncio.run(upgrade())
    print("迁移完成！")