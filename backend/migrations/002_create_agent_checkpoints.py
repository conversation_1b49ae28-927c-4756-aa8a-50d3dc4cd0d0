#!/usr/bin/env python3
"""
数据迁移脚本：创建agent_checkpoints表
根据《0. 项目规范.md》的PostgreSQL优先原则

问题：Agent API测试失败，因为agent_checkpoints表不存在
解决方案：
1. 创建agent_checkpoints表，字段类型与AgentState模型一致
2. 确保user_id字段为UUID类型
3. 添加适当的索引和约束
"""

import asyncio
import asyncpg
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

async def create_agent_checkpoints_table():
    """创建agent_checkpoints表"""
    
    conn = await asyncpg.connect(
        host="localhost",
        port=5432,
        database="crypto_trader_test",
        user="crypto_trader",
        password="test_password_123"
    )
    
    try:
        print("🚀 开始创建agent_checkpoints表...")
        
        # 1. 检查表是否已存在
        table_exists = await conn.fetchval("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name = 'agent_checkpoints'
            )
        """)
        
        if table_exists:
            print("✅ agent_checkpoints表已存在，跳过创建")
            return True
        
        # 2. 创建表结构
        print("📋 创建表结构...")
        await conn.execute("""
            CREATE TABLE agent_checkpoints (
                id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                task_id UUID NOT NULL,
                user_id UUID NOT NULL,
                node_name VARCHAR(255) NOT NULL,
                state_data JSONB NOT NULL,
                created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # 3. 创建索引
        print("🔍 创建索引...")
        
        # 任务ID索引（用于查询特定任务的检查点）
        await conn.execute("""
            CREATE INDEX idx_agent_checkpoints_task_id 
            ON agent_checkpoints(task_id)
        """)
        
        # 用户ID索引（用于查询特定用户的检查点）
        await conn.execute("""
            CREATE INDEX idx_agent_checkpoints_user_id 
            ON agent_checkpoints(user_id)
        """)
        
        # 复合索引（用于查询特定任务的最新检查点）
        await conn.execute("""
            CREATE INDEX idx_agent_checkpoints_task_created 
            ON agent_checkpoints(task_id, created_at DESC)
        """)
        
        # 节点名称索引（用于查询特定节点的检查点）
        await conn.execute("""
            CREATE INDEX idx_agent_checkpoints_node_name 
            ON agent_checkpoints(node_name)
        """)
        
        # 4. 添加外键约束（如果users表存在）
        users_table_exists = await conn.fetchval("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name = 'users'
            )
        """)
        
        if users_table_exists:
            print("🔗 添加外键约束...")
            await conn.execute("""
                ALTER TABLE agent_checkpoints 
                ADD CONSTRAINT fk_agent_checkpoints_user_id 
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
            """)
        else:
            print("⚠️ users表不存在，跳过外键约束")
        
        # 5. 添加更新时间触发器
        print("⏰ 创建更新时间触发器...")
        
        # 创建触发器函数
        await conn.execute("""
            CREATE OR REPLACE FUNCTION update_updated_at_column()
            RETURNS TRIGGER AS $$
            BEGIN
                NEW.updated_at = CURRENT_TIMESTAMP;
                RETURN NEW;
            END;
            $$ language 'plpgsql'
        """)
        
        # 创建触发器
        await conn.execute("""
            CREATE TRIGGER update_agent_checkpoints_updated_at 
            BEFORE UPDATE ON agent_checkpoints 
            FOR EACH ROW EXECUTE FUNCTION update_updated_at_column()
        """)
        
        print("✅ agent_checkpoints表创建完成")
        return True
        
    except Exception as e:
        print(f"❌ 创建表失败: {e}")
        return False
    finally:
        await conn.close()

async def verify_table_creation():
    """验证表创建结果"""
    conn = await asyncpg.connect(
        host="localhost",
        port=5432,
        database="crypto_trader_test",
        user="crypto_trader",
        password="test_password_123"
    )
    
    try:
        # 检查表结构
        columns = await conn.fetch("""
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_name = 'agent_checkpoints'
            ORDER BY ordinal_position
        """)
        
        print("🔍 表结构验证:")
        for col in columns:
            nullable = '可空' if col['is_nullable'] == 'YES' else '非空'
            default = f', 默认值: {col["column_default"]}' if col['column_default'] else ''
            print(f"  - {col['column_name']}: {col['data_type']} ({nullable}{default})")
        
        # 检查索引
        indexes = await conn.fetch("""
            SELECT indexname, indexdef 
            FROM pg_indexes 
            WHERE tablename = 'agent_checkpoints'
            ORDER BY indexname
        """)
        
        print(f"\\n📊 索引验证 ({len(indexes)}个):")
        for idx in indexes:
            print(f"  - {idx['indexname']}")
        
        # 检查约束
        constraints = await conn.fetch("""
            SELECT constraint_name, constraint_type 
            FROM information_schema.table_constraints 
            WHERE table_name = 'agent_checkpoints'
            ORDER BY constraint_name
        """)
        
        print(f"\\n🔒 约束验证 ({len(constraints)}个):")
        for constraint in constraints:
            print(f"  - {constraint['constraint_name']}: {constraint['constraint_type']}")
        
        # 检查触发器
        triggers = await conn.fetch("""
            SELECT trigger_name, event_manipulation, action_timing
            FROM information_schema.triggers 
            WHERE event_object_table = 'agent_checkpoints'
        """)
        
        print(f"\\n⏰ 触发器验证 ({len(triggers)}个):")
        for trigger in triggers:
            print(f"  - {trigger['trigger_name']}: {trigger['action_timing']} {trigger['event_manipulation']}")
        
        return len(columns) > 0
        
    finally:
        await conn.close()

async def test_table_operations():
    """测试表的基本操作"""
    conn = await asyncpg.connect(
        host="localhost",
        port=5432,
        database="crypto_trader_test",
        user="crypto_trader",
        password="test_password_123"
    )
    
    try:
        print("🧪 测试表操作...")
        
        # 插入测试数据
        test_task_id = "00000000-0000-0000-0000-000000000001"

        # 获取一个真实的用户ID
        real_user = await conn.fetchrow("SELECT id FROM users LIMIT 1")
        if not real_user:
            print("  ⚠️ 没有找到用户，跳过外键测试")
            return True
        test_user_id = real_user['id']
        
        import json
        await conn.execute("""
            INSERT INTO agent_checkpoints (task_id, user_id, node_name, state_data)
            VALUES ($1, $2, $3, $4)
        """, test_task_id, test_user_id, "test_node", json.dumps({"test": "data"}))
        
        # 查询测试数据
        result = await conn.fetchrow("""
            SELECT * FROM agent_checkpoints 
            WHERE task_id = $1
        """, test_task_id)
        
        if result:
            print("  ✅ 插入和查询操作正常")
            
            # 更新测试
            await conn.execute("""
                UPDATE agent_checkpoints
                SET state_data = $1
                WHERE task_id = $2
            """, json.dumps({"test": "updated"}), test_task_id)
            
            # 验证更新时间是否自动更新
            updated_result = await conn.fetchrow("""
                SELECT created_at, updated_at 
                FROM agent_checkpoints 
                WHERE task_id = $1
            """, test_task_id)
            
            if updated_result['updated_at'] > updated_result['created_at']:
                print("  ✅ 更新时间触发器正常")
            else:
                print("  ⚠️ 更新时间触发器可能有问题")
            
            # 清理测试数据
            await conn.execute("""
                DELETE FROM agent_checkpoints 
                WHERE task_id = $1
            """, test_task_id)
            
            print("  ✅ 删除操作正常")
            return True
        else:
            print("  ❌ 插入操作失败")
            return False
        
    except Exception as e:
        print(f"  ❌ 表操作测试失败: {e}")
        return False
    finally:
        await conn.close()

async def main():
    """主函数"""
    print("=" * 60)
    print("agent_checkpoints表创建")
    print("=" * 60)
    
    # 创建表
    success = await create_agent_checkpoints_table()
    
    if success:
        # 验证创建结果
        verified = await verify_table_creation()
        if verified:
            # 测试表操作
            tested = await test_table_operations()
            if tested:
                print("\\n🎉 agent_checkpoints表创建和测试成功完成！")
                return True
            else:
                print("\\n❌ 表操作测试失败！")
                return False
        else:
            print("\\n❌ 表创建验证失败！")
            return False
    else:
        print("\\n❌ 表创建失败！")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
