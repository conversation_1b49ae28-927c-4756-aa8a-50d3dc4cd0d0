#!/usr/bin/env python3
"""
数据迁移脚本：修复client_order_id字段
根据《0. 项目规范.md》的PostgreSQL优先原则

问题：client_order_id字段在数据库中为NULL，但业务逻辑期望每个订单都有唯一的客户端订单ID
解决方案：
1. 为现有NULL值生成合理的默认值
2. 将字段设置为非空约束
3. 添加唯一约束确保业务一致性
"""

import asyncio
import asyncpg
import uuid
from datetime import datetime
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

async def migrate_client_order_id():
    """修复client_order_id字段的数据迁移"""
    
    conn = await asyncpg.connect(
        host="localhost",
        port=5432,
        database="crypto_trader_test",
        user="crypto_trader",
        password="test_password_123"
    )
    
    try:
        print("🚀 开始client_order_id字段数据迁移...")
        
        # 1. 检查当前状态
        null_count = await conn.fetchval(
            "SELECT COUNT(*) FROM orders WHERE client_order_id IS NULL"
        )
        total_count = await conn.fetchval("SELECT COUNT(*) FROM orders")
        
        print(f"📊 迁移前状态:")
        print(f"  - 总订单数: {total_count}")
        print(f"  - NULL值数量: {null_count}")
        
        if null_count == 0:
            print("✅ 所有订单都已有client_order_id，无需迁移")
            return True
        
        # 2. 为NULL值生成唯一的client_order_id
        print(f"🔧 为 {null_count} 个订单生成client_order_id...")
        
        # 获取所有NULL值的订单
        null_orders = await conn.fetch("""
            SELECT id, created_at 
            FROM orders 
            WHERE client_order_id IS NULL 
            ORDER BY created_at
        """)
        
        # 为每个订单生成唯一的client_order_id
        for i, order in enumerate(null_orders):
            # 生成格式：order_YYYYMMDD_HHMMSS_序号
            timestamp = order['created_at'].strftime('%Y%m%d_%H%M%S')
            client_order_id = f"order_{timestamp}_{i+1:03d}"
            
            # 确保唯一性
            while await conn.fetchval(
                "SELECT COUNT(*) FROM orders WHERE client_order_id = $1", 
                client_order_id
            ) > 0:
                client_order_id = f"order_{timestamp}_{i+1:03d}_{uuid.uuid4().hex[:6]}"
            
            # 更新订单
            await conn.execute("""
                UPDATE orders 
                SET client_order_id = $1, updated_at = $2
                WHERE id = $3
            """, client_order_id, datetime.now(), order['id'])
            
            print(f"  ✅ 订单 {order['id']} -> {client_order_id}")
        
        # 3. 验证迁移结果
        remaining_null = await conn.fetchval(
            "SELECT COUNT(*) FROM orders WHERE client_order_id IS NULL"
        )
        
        if remaining_null > 0:
            raise Exception(f"迁移失败：仍有 {remaining_null} 个订单的client_order_id为NULL")
        
        # 4. 添加非空约束
        print("🔒 添加非空约束...")
        await conn.execute("""
            ALTER TABLE orders 
            ALTER COLUMN client_order_id SET NOT NULL
        """)
        
        # 5. 添加唯一约束
        print("🔑 添加唯一约束...")
        try:
            await conn.execute("""
                ALTER TABLE orders 
                ADD CONSTRAINT orders_client_order_id_unique 
                UNIQUE (client_order_id)
            """)
        except asyncpg.exceptions.UniqueViolationError:
            print("⚠️ 唯一约束已存在，跳过")
        
        # 6. 最终验证
        final_count = await conn.fetchval("SELECT COUNT(*) FROM orders")
        unique_count = await conn.fetchval(
            "SELECT COUNT(DISTINCT client_order_id) FROM orders"
        )
        
        print(f"✅ 迁移完成:")
        print(f"  - 总订单数: {final_count}")
        print(f"  - 唯一client_order_id数量: {unique_count}")
        print(f"  - 数据一致性: {'✅ 通过' if final_count == unique_count else '❌ 失败'}")
        
        return final_count == unique_count
        
    except Exception as e:
        print(f"❌ 迁移失败: {e}")
        await conn.execute("ROLLBACK")
        return False
    finally:
        await conn.close()

async def verify_migration():
    """验证迁移结果"""
    conn = await asyncpg.connect(
        host="localhost",
        port=5432,
        database="crypto_trader_test",
        user="crypto_trader",
        password="test_password_123"
    )
    
    try:
        # 检查约束
        constraints = await conn.fetch("""
            SELECT constraint_name, constraint_type 
            FROM information_schema.table_constraints 
            WHERE table_name = 'orders' 
            AND constraint_name LIKE '%client_order_id%'
        """)
        
        print("🔍 约束验证:")
        for constraint in constraints:
            print(f"  - {constraint['constraint_name']}: {constraint['constraint_type']}")
        
        # 检查字段属性
        column_info = await conn.fetchrow("""
            SELECT is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_name = 'orders' AND column_name = 'client_order_id'
        """)
        
        print(f"📋 字段属性:")
        print(f"  - 可空: {column_info['is_nullable']}")
        print(f"  - 默认值: {column_info['column_default'] or '无'}")
        
        return column_info['is_nullable'] == 'NO'
        
    finally:
        await conn.close()

async def main():
    """主函数"""
    print("=" * 60)
    print("client_order_id字段数据迁移")
    print("=" * 60)
    
    # 执行迁移
    success = await migrate_client_order_id()
    
    if success:
        # 验证迁移
        verified = await verify_migration()
        if verified:
            print("\n🎉 迁移成功完成！")
            return True
        else:
            print("\n❌ 迁移验证失败！")
            return False
    else:
        print("\n❌ 迁移执行失败！")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
