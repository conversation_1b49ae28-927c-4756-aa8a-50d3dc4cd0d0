# 后端测试指南

## 📋 概述

本目录包含AI加密货币交易系统后端的所有测试代码，采用pytest框架进行测试管理。

## 🏗️ 测试结构

```
tests/
├── unit/                   # 单元测试
│   ├── test_models.py     # 数据模型测试
│   ├── test_security.py   # 安全模块测试
│   ├── test_agent_*.py    # Agent相关测试
│   └── ...
├── integration/           # 集成测试
│   ├── test_agent_workflow.py
│   ├── test_discord_integration.py
│   └── ...
├── mocks/                 # Mock对象
│   └── mock_exchange.py
├── conftest.py           # pytest配置和fixtures
├── factories.py          # 测试数据工厂
└── async_factories.py    # 异步测试数据工厂
```

## 🚀 快速开始

### 环境准备

1. **安装依赖**
```bash
cd backend
pip install -r requirements.txt
```

2. **🔴 启动PostgreSQL数据库 (必需)**
```bash
# 方式1: 使用Docker Compose (推荐)
docker-compose -f docker-compose.test.yml up -d postgres-test

# 方式2: 手动启动PostgreSQL服务
brew services start postgresql  # macOS
sudo systemctl start postgresql  # Linux

# 验证连接
python -c "import socket; sock = socket.socket(); print('✅ PostgreSQL可访问' if sock.connect_ex(('localhost', 5432)) == 0 else '❌ PostgreSQL不可访问'); sock.close()"
```

3. **环境变量设置**
```bash
export TESTING=true
export DATABASE_URL=postgresql+asyncpg://crypto_trader:test_password_123@localhost:5432/crypto_trader_test
export SIMULATION_MODE=true
```

**⚠️  重要说明**:
- 所有后端测试都依赖PostgreSQL数据库
- 测试使用独立的测试数据库 `crypto_trader_test`
- 每个测试在事务中运行，测试结束后自动回滚

### 运行测试

#### 基本命令
```bash
# 运行所有测试
pytest

# 运行特定目录的测试
pytest tests/unit/
pytest tests/integration/

# 运行特定文件的测试
pytest tests/unit/test_security.py

# 运行特定测试函数
pytest tests/unit/test_security.py::test_password_hashing
```

#### 测试分类运行
```bash
# 单元测试
pytest -m unit

# 集成测试
pytest -m integration

# 数据库测试
pytest -m database

# 业务逻辑测试
pytest -m business_logic

# 慢速测试
pytest -m slow
```

#### 覆盖率测试
```bash
# 生成覆盖率报告
pytest --cov=app --cov-report=html --cov-report=term-missing

# 查看HTML覆盖率报告
open htmlcov/index.html
```

#### 并行测试
```bash
# 自动检测CPU核心数
pytest -n auto

# 指定进程数
pytest -n 4
```

## 🎯 测试标记说明

| 标记 | 描述 | 用法 |
|------|------|------|
| `unit` | 单元测试 | `pytest -m unit` |
| `integration` | 集成测试 | `pytest -m integration` |
| `database` | 数据库测试 | `pytest -m database` |
| `business_logic` | 业务逻辑测试 | `pytest -m business_logic` |
| `models` | 模型测试 | `pytest -m models` |
| `services` | 服务测试 | `pytest -m services` |
| `slow` | 慢速测试 | `pytest -m slow` |
| `simulation` | 仿真模式测试 | `pytest -m simulation` |

## 📊 覆盖率目标

- **总体覆盖率**: ≥ 80%
- **核心模块覆盖率**: ≥ 90%
  - `app/core/security.py`
  - `app/core/auth.py`
  - `app/core/models.py`
- **服务模块覆盖率**: ≥ 70%
- **API模块覆盖率**: ≥ 60%

## 🔧 测试配置

### pytest.ini配置
- **测试发现**: 自动发现`test_*.py`文件
- **异步支持**: 启用asyncio模式
- **覆盖率**: 自动生成HTML和终端报告
- **超时**: 单个测试最大300秒
- **并行**: 支持pytest-xdist并行执行

### 环境变量
```bash
TESTING=true                    # 启用测试模式
DATABASE_URL=postgresql+asyncpg://crypto_trader:test_password_123@localhost:5432/crypto_trader_test  # PostgreSQL测试数据库
SIMULATION_MODE=true            # 仿真模式
OPENAI_API_KEY=sk-mock-testing  # Mock API密钥
JWT_SECRET_KEY=test-secret-key  # 测试JWT密钥
```

**数据库配置说明**:
- 🔴 **强依赖PostgreSQL**: 所有测试都需要PostgreSQL数据库运行
- 📊 **测试数据库**: `crypto_trader_test` (独立于开发数据库)
- 🔌 **连接地址**: `localhost:5432`
- 👤 **数据库用户**: `crypto_trader` / `test_password_123`

## 🧪 编写测试

### 测试文件命名
- 单元测试: `test_<module_name>.py`
- 集成测试: `test_<feature>_integration.py`
- 测试类: `Test<ClassName>`
- 测试方法: `test_<functionality>`

### 测试示例
```python
import pytest
from unittest.mock import AsyncMock, MagicMock

@pytest.mark.unit
@pytest.mark.asyncio
async def test_user_authentication():
    """测试用户认证功能"""
    # 测试代码
    pass

@pytest.mark.integration
@pytest.mark.database
async def test_order_creation_workflow():
    """测试订单创建工作流"""
    # 集成测试代码
    pass
```

### Fixtures使用
```python
# 使用预定义的fixtures
def test_with_mock_db(mock_db):
    # 使用mock数据库
    pass

def test_with_test_user(test_user):
    # 使用测试用户
    pass
```

## 🚨 故障排除

### 常见问题

1. **数据库连接错误**
```bash
# 确保测试数据库URL正确
export DATABASE_URL=sqlite+aiosqlite:///./test.db
```

2. **异步测试失败**
```bash
# 确保使用@pytest.mark.asyncio装饰器
@pytest.mark.asyncio
async def test_async_function():
    pass
```

3. **导入错误**
```bash
# 确保在backend目录下运行测试
cd backend
pytest
```

4. **覆盖率不足**
```bash
# 查看未覆盖的代码行
pytest --cov=app --cov-report=term-missing
```

### 调试技巧
```bash
# 详细输出
pytest -v

# 显示print输出
pytest -s

# 在第一个失败处停止
pytest -x

# 显示最慢的10个测试
pytest --durations=10

# 调试模式
pytest --pdb
```

## 📈 性能监控

### 测试执行时间
```bash
# 显示最慢的测试
pytest --durations=0

# 设置超时
pytest --timeout=60
```

### 内存使用
```bash
# 监控内存使用
pytest --memray
```

## 🔄 CI/CD集成

### GitHub Actions
```yaml
- name: Run Backend Tests
  run: |
    cd backend
    pytest --cov=app --cov-report=xml
```

### 本地CI模拟
```bash
# 模拟CI环境运行
CI=true pytest --cov=app --cov-fail-under=80
```

## 📚 相关文档

- [pytest官方文档](https://docs.pytest.org/)
- [pytest-asyncio文档](https://pytest-asyncio.readthedocs.io/)
- [pytest-cov文档](https://pytest-cov.readthedocs.io/)
- [项目测试策略文档](../docs/testing-strategy.md)

---

**维护者**: AI Crypto Trading Team  
**最后更新**: 2025-07-20  
**版本**: 1.0
