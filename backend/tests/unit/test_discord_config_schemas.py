#!/usr/bin/env python3
"""
Discord配置Schemas单元测试
测试DiscordConfigRequest和DiscordConfigResponse的验证和序列化

按照项目测试规范：
- 测试Pydantic模型验证
- 测试字段约束
- 测试序列化和反序列化
- 使用中文注释和错误信息
"""
import uuid
from datetime import datetime
from typing import List

import pytest
from pydantic import ValidationError

from app.core.schemas import DiscordConfigRequest, DiscordConfigResponse


class TestDiscordConfigRequest:
    """Discord配置请求模型测试类"""

    def test_discord_config_request_valid_data(self):
        """测试有效的Discord配置请求数据"""
        valid_data = {
            "source_name": "测试Discord配置",
            "enabled": True,
            "token": "discord_bot_token_123",
            "server_ids": ["123456789", "987654321"],
            "channel_ids": ["111111111", "222222222"],
            "author_ids": ["333333333"],
            "allowed_message_types": ["text"]
        }
        
        # 执行验证
        config_request = DiscordConfigRequest(**valid_data)
        
        # 验证结果
        assert config_request.source_name == "测试Discord配置"
        assert config_request.enabled is True
        assert config_request.token == "discord_bot_token_123"
        assert config_request.server_ids == ["123456789", "987654321"]
        assert config_request.channel_ids == ["111111111", "222222222"]
        assert config_request.author_ids == ["333333333"]
        assert config_request.allowed_message_types == ["text"]

    def test_discord_config_request_minimal_data(self):
        """测试最小有效数据"""
        minimal_data = {
            "source_name": "最小配置",
            "token": "token"
        }
        
        config_request = DiscordConfigRequest(**minimal_data)
        
        # 验证默认值
        assert config_request.source_name == "最小配置"
        assert config_request.enabled is False  # 默认值
        assert config_request.token == "token"
        assert config_request.server_ids == []  # 默认值
        assert config_request.channel_ids == []  # 默认值
        assert config_request.author_ids == []  # 默认值
        assert config_request.allowed_message_types == ["text"]  # 默认值

    def test_discord_config_request_source_name_validation(self):
        """测试source_name字段验证"""
        base_data = {
            "token": "test_token",
            "enabled": True
        }
        
        # 测试空字符串
        with pytest.raises(ValidationError) as exc_info:
            DiscordConfigRequest(source_name="", **base_data)
        
        error = exc_info.value
        assert "at least 1 character" in str(error) or "ensure this value has at least 1 character" in str(error)
        
        # 测试超长字符串
        long_name = "a" * 101  # 超过100字符限制
        with pytest.raises(ValidationError) as exc_info:
            DiscordConfigRequest(source_name=long_name, **base_data)
        
        error = exc_info.value
        assert "at most 100 characters" in str(error) or "ensure this value has at most 100 characters" in str(error)
        
        # 测试边界值
        boundary_name = "a" * 100  # 正好100字符
        config_request = DiscordConfigRequest(source_name=boundary_name, **base_data)
        assert config_request.source_name == boundary_name

    def test_discord_config_request_token_validation(self):
        """测试token字段验证"""
        base_data = {
            "source_name": "测试配置",
            "enabled": True
        }
        
        # 测试空token
        with pytest.raises(ValidationError) as exc_info:
            DiscordConfigRequest(token="", **base_data)
        
        error = exc_info.value
        assert "at least 1 character" in str(error) or "ensure this value has at least 1 character" in str(error)
        
        # 测试缺少token
        with pytest.raises(ValidationError) as exc_info:
            DiscordConfigRequest(**base_data)
        
        error = exc_info.value
        assert "field required" in str(error) or "Field required" in str(error)

    def test_discord_config_request_enabled_field(self):
        """测试enabled字段的布尔值处理"""
        base_data = {
            "source_name": "测试配置",
            "token": "test_token"
        }
        
        # 测试显式True
        config_true = DiscordConfigRequest(enabled=True, **base_data)
        assert config_true.enabled is True
        
        # 测试显式False
        config_false = DiscordConfigRequest(enabled=False, **base_data)
        assert config_false.enabled is False
        
        # 测试字符串转换
        config_str_true = DiscordConfigRequest(enabled="true", **base_data)
        assert config_str_true.enabled is True
        
        config_str_false = DiscordConfigRequest(enabled="false", **base_data)
        assert config_str_false.enabled is False

    def test_discord_config_request_array_fields(self):
        """测试数组字段的验证"""
        base_data = {
            "source_name": "测试配置",
            "token": "test_token",
            "enabled": True
        }
        
        # 测试有效的数组数据
        array_data = {
            "server_ids": ["123456789", "987654321"],
            "channel_ids": ["111111111", "222222222", "333333333"],
            "author_ids": ["444444444"],
            "allowed_message_types": ["text", "embed"]
        }
        
        config_request = DiscordConfigRequest(**base_data, **array_data)
        assert config_request.server_ids == array_data["server_ids"]
        assert config_request.channel_ids == array_data["channel_ids"]
        assert config_request.author_ids == array_data["author_ids"]
        assert config_request.allowed_message_types == array_data["allowed_message_types"]
        
        # 测试空数组
        empty_arrays = {
            "server_ids": [],
            "channel_ids": [],
            "author_ids": [],
            "allowed_message_types": []
        }

        config_empty = DiscordConfigRequest(**base_data, **empty_arrays)
        assert config_empty.server_ids == []
        assert config_empty.channel_ids == []
        assert config_empty.author_ids == []
        # allowed_message_types即使传递空列表也会被validator设置为["text"]
        assert config_empty.allowed_message_types == ["text"]

    def test_discord_config_request_serialization(self):
        """测试序列化功能"""
        config_data = {
            "source_name": "序列化测试配置",
            "enabled": True,
            "token": "test_token_123",
            "server_ids": ["123456789"],
            "channel_ids": ["111111111"],
            "author_ids": ["333333333"],
            "allowed_message_types": ["text"]
        }
        
        config_request = DiscordConfigRequest(**config_data)
        
        # 测试转换为字典
        config_dict = config_request.model_dump()
        assert isinstance(config_dict, dict)
        assert config_dict["source_name"] == config_data["source_name"]
        assert config_dict["enabled"] == config_data["enabled"]
        assert config_dict["token"] == config_data["token"]
        
        # 测试JSON序列化
        config_json = config_request.model_dump_json()
        assert isinstance(config_json, str)
        assert "序列化测试配置" in config_json


class TestDiscordConfigResponse:
    """Discord配置响应模型测试类"""

    def test_discord_config_response_creation(self):
        """测试Discord配置响应模型创建"""
        response_data = {
            "id": uuid.uuid4(),
            "user_id": uuid.uuid4(),
            "source_name": "测试响应配置",
            "enabled": True,
            "has_token": True,
            "server_ids": ["123456789"],
            "channel_ids": ["111111111"],
            "author_ids": ["333333333"],
            "allowed_message_types": ["text"],
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow()
        }
        
        config_response = DiscordConfigResponse(**response_data)
        
        # 验证所有字段
        assert config_response.id == response_data["id"]
        assert config_response.user_id == response_data["user_id"]
        assert config_response.source_name == response_data["source_name"]
        assert config_response.enabled == response_data["enabled"]
        assert config_response.has_token == response_data["has_token"]
        assert config_response.server_ids == response_data["server_ids"]
        assert config_response.channel_ids == response_data["channel_ids"]
        assert config_response.author_ids == response_data["author_ids"]
        assert config_response.allowed_message_types == response_data["allowed_message_types"]
        assert config_response.created_at == response_data["created_at"]
        assert config_response.updated_at == response_data["updated_at"]

    def test_discord_config_response_uuid_validation(self):
        """测试UUID字段验证"""
        base_data = {
            "source_name": "UUID测试配置",
            "enabled": True,
            "has_token": True,
            "server_ids": [],
            "channel_ids": [],
            "author_ids": [],
            "allowed_message_types": ["text"],
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow()
        }
        
        # 测试有效UUID
        valid_id = uuid.uuid4()
        valid_user_id = uuid.uuid4()
        
        config_response = DiscordConfigResponse(
            id=valid_id,
            user_id=valid_user_id,
            **base_data
        )
        
        assert config_response.id == valid_id
        assert config_response.user_id == valid_user_id
        
        # 测试无效UUID字符串
        with pytest.raises(ValidationError) as exc_info:
            DiscordConfigResponse(
                id="invalid-uuid",
                user_id=valid_user_id,
                **base_data
            )
        
        error = exc_info.value
        assert "invalid" in str(error).lower() or "uuid" in str(error).lower()

    def test_discord_config_response_has_token_field(self):
        """测试has_token字段的布尔值处理"""
        base_data = {
            "id": uuid.uuid4(),
            "user_id": uuid.uuid4(),
            "source_name": "Token标志测试配置",
            "enabled": True,
            "server_ids": [],
            "channel_ids": [],
            "author_ids": [],
            "allowed_message_types": ["text"],
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow()
        }
        
        # 测试has_token为True
        config_with_token = DiscordConfigResponse(has_token=True, **base_data)
        assert config_with_token.has_token is True
        
        # 测试has_token为False
        config_without_token = DiscordConfigResponse(has_token=False, **base_data)
        assert config_without_token.has_token is False

    def test_discord_config_response_datetime_validation(self):
        """测试日期时间字段验证"""
        base_data = {
            "id": uuid.uuid4(),
            "user_id": uuid.uuid4(),
            "source_name": "日期时间测试配置",
            "enabled": True,
            "has_token": True,
            "server_ids": [],
            "channel_ids": [],
            "author_ids": [],
            "allowed_message_types": ["text"]
        }
        
        # 测试有效的datetime对象
        now = datetime.utcnow()
        config_response = DiscordConfigResponse(
            created_at=now,
            updated_at=now,
            **base_data
        )
        
        assert config_response.created_at == now
        assert config_response.updated_at == now
        
        # 测试ISO格式字符串
        iso_string = "2023-01-01T12:00:00"
        config_iso = DiscordConfigResponse(
            created_at=iso_string,
            updated_at=iso_string,
            **base_data
        )
        
        assert isinstance(config_iso.created_at, datetime)
        assert isinstance(config_iso.updated_at, datetime)

    def test_discord_config_response_serialization(self):
        """测试响应模型序列化"""
        response_data = {
            "id": uuid.uuid4(),
            "user_id": uuid.uuid4(),
            "source_name": "序列化测试响应",
            "enabled": False,
            "has_token": False,
            "server_ids": ["123456789", "987654321"],
            "channel_ids": ["111111111"],
            "author_ids": [],
            "allowed_message_types": ["text", "embed"],
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow()
        }
        
        config_response = DiscordConfigResponse(**response_data)
        
        # 测试转换为字典
        response_dict = config_response.model_dump()
        assert isinstance(response_dict, dict)
        assert str(response_dict["id"]) == str(response_data["id"])
        assert response_dict["source_name"] == response_data["source_name"]
        
        # 测试JSON序列化
        response_json = config_response.model_dump_json()
        assert isinstance(response_json, str)
        assert "序列化测试响应" in response_json
        
        # 验证UUID在JSON中是字符串格式
        import json
        parsed_json = json.loads(response_json)
        assert isinstance(parsed_json["id"], str)
        assert isinstance(parsed_json["user_id"], str)


class TestDiscordConfigSchemasIntegration:
    """Discord配置Schemas集成测试"""

    def test_request_to_response_workflow(self):
        """测试请求到响应的完整工作流程"""
        # 模拟从请求创建响应的过程
        request_data = {
            "source_name": "工作流程测试配置",
            "enabled": True,
            "token": "secret_token_123",
            "server_ids": ["123456789"],
            "channel_ids": ["111111111"],
            "author_ids": ["333333333"],
            "allowed_message_types": ["text"]
        }

        # 创建请求对象
        config_request = DiscordConfigRequest(**request_data)

        # 模拟服务器处理后创建响应
        response_data = {
            "id": uuid.uuid4(),
            "user_id": uuid.uuid4(),
            "source_name": config_request.source_name,
            "enabled": config_request.enabled,
            "has_token": bool(config_request.token),  # 转换为has_token标志
            "server_ids": config_request.server_ids,
            "channel_ids": config_request.channel_ids,
            "author_ids": config_request.author_ids,
            "allowed_message_types": config_request.allowed_message_types,
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow()
        }

        # 创建响应对象
        config_response = DiscordConfigResponse(**response_data)

        # 验证数据一致性
        assert config_response.source_name == config_request.source_name
        assert config_response.enabled == config_request.enabled
        assert config_response.has_token is True  # 因为提供了token
        assert config_response.server_ids == config_request.server_ids
        assert config_response.channel_ids == config_request.channel_ids
        assert config_response.author_ids == config_request.author_ids
        assert config_response.allowed_message_types == config_request.allowed_message_types
