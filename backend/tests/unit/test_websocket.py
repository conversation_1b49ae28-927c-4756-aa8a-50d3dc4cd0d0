"""
WebSocket连接测试
"""
import json
import uuid
from decimal import Decimal
from unittest.mock import As<PERSON><PERSON><PERSON>, MagicMock, patch

import pytest
import pytest_asyncio
from fastapi.testclient import TestClient
from fastapi import WebSocket

from app.core.models import User
from app.core.schemas import Agent<PERSON><PERSON>, IntentType, ParsedIntent, TradeSide
from app.core.ws_manager import WebSocketManager, ConnectionInfo, MessagePriority
from app.core.ws_events import EventBus, EventType
from app.main import app


@pytest.fixture
def mock_websocket():
    """Mock WebSocket fixture"""
    websocket = AsyncMock(spec=WebSocket)
    websocket.accept = AsyncMock()
    websocket.send_text = AsyncMock()
    websocket.close = AsyncMock()
    return websocket


@pytest.fixture
def mock_user():
    """Mock User fixture"""
    user = MagicMock(spec=User)
    user.id = 1
    user.username = "test_user"
    return user


@pytest.fixture
def ws_manager():
    """WebSocket Manager fixture"""
    return WebSocketManager()


class TestWebSocketConnection:
    """WebSocket连接测试"""

    @pytest.mark.asyncio
    async def test_websocket_connection_success(self, ws_manager, mock_websocket, mock_user):
        """测试WebSocket连接成功"""
        # 建立连接
        connection_id = await ws_manager.connect(mock_websocket, mock_user)

        # 验证连接建立
        assert connection_id is not None
        assert mock_user.id in ws_manager.active_connections
        assert len(ws_manager.active_connections[mock_user.id]) == 1
        assert connection_id in ws_manager.connection_map

        # 验证WebSocket被接受
        mock_websocket.accept.assert_called_once()

    @pytest.mark.asyncio
    async def test_websocket_connection_invalid_token(self, ws_manager, mock_websocket, mock_user):
        """测试无效token的WebSocket连接"""
        # 使用无效的重连token
        invalid_token = "invalid_token"

        # 建立连接（应该忽略无效token）
        connection_id = await ws_manager.connect(mock_websocket, mock_user, invalid_token)

        # 验证连接仍然成功（忽略无效token）
        assert connection_id is not None
        assert mock_user.id in ws_manager.active_connections

    @pytest.mark.asyncio
    async def test_websocket_message_processing(self, ws_manager, mock_websocket, mock_user):
        """测试WebSocket消息处理"""
        # 建立连接
        await ws_manager.connect(mock_websocket, mock_user)

        # 发送消息
        test_message = {"type": "test", "data": "hello"}
        success = await ws_manager.send_to_user(mock_user.id, test_message)

        # 验证消息发送成功
        assert success is True
        mock_websocket.send_text.assert_called()

    @pytest.mark.asyncio
    async def test_websocket_task_status_subscription(self, ws_manager, mock_websocket, mock_user):
        """测试任务状态订阅"""
        # 建立连接
        await ws_manager.connect(mock_websocket, mock_user)

        # 模拟任务状态更新
        task_status = {
            "task_id": "test_task_123",
            "status": "completed",
            "result": "success"
        }

        # 发送任务状态更新
        success = await ws_manager.send_to_user(mock_user.id, task_status, MessagePriority.HIGH)

        # 验证高优先级消息发送
        assert success is True
        mock_websocket.send_text.assert_called()

    @pytest.mark.asyncio
    async def test_websocket_order_updates(self, ws_manager, mock_websocket, mock_user):
        """测试订单更新推送"""
        # 建立连接
        await ws_manager.connect(mock_websocket, mock_user)

        # 模拟订单更新
        order_update = {
            "order_id": "order_123",
            "status": "filled",
            "filled_quantity": "1.5",
            "price": "50000.00"
        }

        # 发送订单更新
        success = await ws_manager.send_to_user(mock_user.id, order_update, MessagePriority.HIGH)

        # 验证订单更新发送
        assert success is True
        mock_websocket.send_text.assert_called()

    @pytest.mark.asyncio
    async def test_websocket_error_handling(self, ws_manager, mock_websocket, mock_user):
        """测试WebSocket错误处理"""
        # 建立连接
        await ws_manager.connect(mock_websocket, mock_user)

        # 模拟发送消息时出错
        mock_websocket.send_text.side_effect = Exception("Connection lost")

        # 尝试发送消息
        test_message = {"type": "test", "data": "error_test"}
        success = await ws_manager.send_to_user(mock_user.id, test_message)

        # 验证错误处理（连接应该被清理）
        # 注意：实际的错误处理逻辑可能需要根据实现调整
        assert mock_websocket.send_text.called

    @pytest.mark.asyncio
    async def test_websocket_ping_pong(self, ws_manager, mock_websocket, mock_user):
        """测试WebSocket心跳"""
        # 建立连接
        connection_id = await ws_manager.connect(mock_websocket, mock_user)

        # 获取连接信息
        connection_info = ws_manager.connection_map[connection_id]

        # 验证心跳任务已启动
        assert connection_info.heartbeat_task is not None
        assert not connection_info.heartbeat_task.done()

    @pytest.mark.asyncio
    async def test_websocket_multiple_clients(self, ws_manager, mock_user):
        """测试多客户端连接"""
        # 创建多个WebSocket连接
        websocket1 = AsyncMock(spec=WebSocket)
        websocket1.accept = AsyncMock()
        websocket1.send_text = AsyncMock()

        websocket2 = AsyncMock(spec=WebSocket)
        websocket2.accept = AsyncMock()
        websocket2.send_text = AsyncMock()

        # 建立多个连接
        connection_id1 = await ws_manager.connect(websocket1, mock_user)
        connection_id2 = await ws_manager.connect(websocket2, mock_user)

        # 验证多连接
        assert connection_id1 != connection_id2
        assert len(ws_manager.active_connections[mock_user.id]) == 2
        assert len(ws_manager.connection_map) == 2

    @pytest.mark.asyncio
    async def test_websocket_connection_cleanup(self, ws_manager, mock_websocket, mock_user):
        """测试WebSocket连接清理"""
        # 建立连接
        connection_id = await ws_manager.connect(mock_websocket, mock_user)

        # 验证连接存在
        assert connection_id in ws_manager.connection_map
        assert mock_user.id in ws_manager.active_connections

        # 断开连接
        await ws_manager.disconnect(connection_id)

        # 验证连接清理
        assert connection_id not in ws_manager.connection_map
        # 用户可能仍在断开连接列表中，但活跃连接应该被清理
        if mock_user.id in ws_manager.active_connections:
            assert len(ws_manager.active_connections[mock_user.id]) == 0


class TestWebSocketBroadcast:
    """WebSocket广播测试"""

    @pytest.mark.asyncio
    async def test_broadcast_order_update(self, ws_manager):
        """测试订单更新广播"""
        # 创建多个用户和连接
        user1 = MagicMock(spec=User)
        user1.id = 1
        user1.username = "user1"

        user2 = MagicMock(spec=User)
        user2.id = 2
        user2.username = "user2"

        websocket1 = AsyncMock(spec=WebSocket)
        websocket1.accept = AsyncMock()
        websocket1.send_text = AsyncMock()

        websocket2 = AsyncMock(spec=WebSocket)
        websocket2.accept = AsyncMock()
        websocket2.send_text = AsyncMock()

        # 建立连接
        await ws_manager.connect(websocket1, user1)
        await ws_manager.connect(websocket2, user2)

        # 广播订单更新
        order_update = {
            "type": "order_update",
            "order_id": "order_123",
            "status": "filled"
        }

        await ws_manager.send_to_all(order_update, MessagePriority.HIGH)

        # 验证所有连接都收到消息
        websocket1.send_text.assert_called()
        websocket2.send_text.assert_called()

    @pytest.mark.asyncio
    async def test_broadcast_agent_status_update(self, ws_manager):
        """测试Agent状态更新广播"""
        # 创建用户和连接
        user = MagicMock(spec=User)
        user.id = 1
        user.username = "test_user"

        websocket = AsyncMock(spec=WebSocket)
        websocket.accept = AsyncMock()
        websocket.send_text = AsyncMock()

        # 建立连接
        await ws_manager.connect(websocket, user)

        # 广播Agent状态更新
        agent_status = {
            "type": "agent_status",
            "agent_id": "agent_123",
            "status": "active",
            "current_task": "processing_order"
        }

        await ws_manager.send_to_all(agent_status, MessagePriority.NORMAL)

        # 验证消息发送
        websocket.send_text.assert_called()

    @pytest.mark.asyncio
    async def test_broadcast_to_all_users(self, ws_manager):
        """测试全用户广播"""
        # 创建多个用户连接
        users_and_websockets = []
        for i in range(3):
            user = MagicMock(spec=User)
            user.id = i + 1
            user.username = f"user{i + 1}"

            websocket = AsyncMock(spec=WebSocket)
            websocket.accept = AsyncMock()
            websocket.send_text = AsyncMock()

            users_and_websockets.append((user, websocket))
            await ws_manager.connect(websocket, user)

        # 广播系统消息
        system_message = {
            "type": "system_announcement",
            "message": "System maintenance in 10 minutes",
            "priority": "high"
        }

        await ws_manager.send_to_all(system_message, MessagePriority.CRITICAL)

        # 验证所有用户都收到消息
        for user, websocket in users_and_websockets:
            websocket.send_text.assert_called()


class TestWebSocketSecurity:
    """WebSocket安全测试"""

    @pytest.mark.asyncio
    async def test_websocket_authentication_required(self, ws_manager, mock_websocket):
        """测试WebSocket需要认证"""
        # 创建未认证用户（None）
        unauthenticated_user = None

        # 尝试建立连接应该需要有效用户
        with pytest.raises((AttributeError, TypeError)):
            await ws_manager.connect(mock_websocket, unauthenticated_user)

    @pytest.mark.asyncio
    async def test_websocket_token_validation(self, ws_manager, mock_websocket, mock_user):
        """测试WebSocket token验证"""
        # 测试有效用户连接
        connection_id = await ws_manager.connect(mock_websocket, mock_user)
        assert connection_id is not None

        # 验证用户信息正确存储
        connection_info = ws_manager.connection_map[connection_id]
        assert connection_info.user.id == mock_user.id
        assert connection_info.user.username == mock_user.username

    @pytest.mark.asyncio
    async def test_websocket_rate_limiting(self, ws_manager, mock_websocket, mock_user):
        """测试WebSocket速率限制"""
        # 建立连接
        await ws_manager.connect(mock_websocket, mock_user)

        # 快速发送多条消息（模拟速率限制测试）
        messages_sent = 0
        for i in range(10):
            test_message = {"type": "test", "data": f"message_{i}"}
            success = await ws_manager.send_to_user(mock_user.id, test_message)
            if success:
                messages_sent += 1

        # 验证消息发送（实际的速率限制逻辑需要根据实现调整）
        assert messages_sent > 0
        assert mock_websocket.send_text.call_count == messages_sent

    @pytest.mark.asyncio
    async def test_websocket_message_size_limit(self, ws_manager, mock_websocket, mock_user):
        """测试WebSocket消息大小限制"""
        # 建立连接
        await ws_manager.connect(mock_websocket, mock_user)

        # 发送正常大小的消息
        normal_message = {"type": "test", "data": "normal message"}
        success = await ws_manager.send_to_user(mock_user.id, normal_message)
        assert success is True

        # 发送大消息（模拟大小限制测试）
        large_data = "x" * 10000  # 10KB数据
        large_message = {"type": "test", "data": large_data}
        success = await ws_manager.send_to_user(mock_user.id, large_message)

        # 验证大消息处理（实际的大小限制逻辑需要根据实现调整）
        assert mock_websocket.send_text.called
