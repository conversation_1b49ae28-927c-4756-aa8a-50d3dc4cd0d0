"""
Tests for monitoring functionality
"""
import pytest
import asyncio
from unittest.mock import Mo<PERSON>, patch, AsyncMock, MagicMock
from datetime import datetime, timedelta
import json
import time

from app.core.monitoring import (
    MetricsCollector,
    HealthChecker,
    PerformanceMonitor,
    metrics_collector,
    health_checker,
    performance_monitor,
    record_order_metric,
    record_agent_metric,
    record_api_metric,
    get_system_health,
    get_metrics_data
)


class TestMetricsCollector:
    """Test metrics collection functionality"""

    @pytest.fixture
    def metrics_collector(self):
        collector = MetricsCollector()
        return collector

    def test_metrics_collector_initialization(self, metrics_collector):
        """Test metrics collector initialization"""
        assert metrics_collector is not None
        assert hasattr(metrics_collector, '_metrics_data')

    def test_record_order_metric(self, metrics_collector):
        """Test recording order metrics"""
        metrics_collector.record_order("BTC/USDT", "buy", "filled")
        # Should not raise any exceptions
        assert True

    def test_record_agent_processing_time(self, metrics_collector):
        """Test recording agent processing time"""
        metrics_collector.record_agent_processing_time("parse_intents", 0.5, "success")
        # Should not raise any exceptions
        assert True

    def test_record_websocket_connections(self, metrics_collector):
        """Test recording WebSocket connection metrics"""
        metrics_collector.set_active_websocket_connections(10)
        # Should not raise any exceptions
        assert True

    def test_record_api_request(self, metrics_collector):
        """Test recording API request metrics"""
        metrics_collector.record_api_request("/api/v1/orders", "GET", 200, 0.1)
        # Should not raise any exceptions
        assert True

    def test_get_metrics_summary(self, metrics_collector):
        """Test getting metrics summary"""
        # Record some metrics
        metrics_collector.record_order("BTC/USDT", "buy", "filled")
        metrics_collector.record_api_request("/api/v1/orders", "GET", 200, 0.1)

        summary = metrics_collector.get_metrics_summary()
        assert isinstance(summary, dict)
        # Should contain basic structure
        assert "timestamp" in summary


class TestMetricsCollectorAdvanced:
    """Test advanced metrics collector functionality"""

    @pytest.fixture
    def metrics_collector(self):
        collector = MetricsCollector()
        return collector

    def test_multiple_order_recordings(self, metrics_collector):
        """Test recording multiple orders"""
        # Record multiple orders
        metrics_collector.record_order("BTC/USDT", "buy", "filled")
        metrics_collector.record_order("ETH/USDT", "sell", "filled")
        metrics_collector.record_order("BTC/USDT", "buy", "cancelled")

        # Should not raise any exceptions
        assert True

    def test_performance_timing(self, metrics_collector):
        """Test performance timing functionality"""
        # Record different processing times
        metrics_collector.record_agent_processing_time("parse_intents", 0.1, "success")
        metrics_collector.record_agent_processing_time("generate_plan", 0.2, "success")
        metrics_collector.record_agent_processing_time("execute_plan", 0.5, "error")

        # Should not raise any exceptions
        assert True

    def test_api_metrics_recording(self, metrics_collector):
        """Test API metrics recording"""
        # Record various API calls
        metrics_collector.record_api_request("/api/v1/orders", "POST", 201, 0.15)
        metrics_collector.record_api_request("/api/v1/orders", "GET", 200, 0.05)
        metrics_collector.record_api_request("/api/v1/auth/login", "POST", 401, 0.02)

        # Should not raise any exceptions
        assert True

    def test_websocket_metrics(self, metrics_collector):
        """Test WebSocket metrics"""
        # Test connection count changes
        metrics_collector.set_active_websocket_connections(5)
        metrics_collector.set_active_websocket_connections(10)
        metrics_collector.set_active_websocket_connections(3)

        # Should not raise any exceptions
        assert True


class TestHealthChecker:
    """Test health checking functionality"""

    @pytest.fixture
    def health_checker(self):
        return HealthChecker()

    def test_health_checker_initialization(self, health_checker):
        """Test health checker initialization"""
        assert health_checker is not None
        assert hasattr(health_checker, 'checks')
        assert isinstance(health_checker.checks, dict)
        assert len(health_checker.checks) == 0

    def test_register_check(self, health_checker):
        """Test registering health checks"""
        def dummy_check():
            return True

        health_checker.register_check("test_check", dummy_check)
        assert "test_check" in health_checker.checks
        assert health_checker.checks["test_check"] == dummy_check

    @pytest.mark.asyncio
    async def test_run_health_checks_empty(self, health_checker):
        """Test running health checks with no registered checks"""
        result = await health_checker.run_health_checks()

        assert isinstance(result, dict)
        assert "healthy" in result
        assert "timestamp" in result
        assert "checks" in result
        assert result["healthy"] is True  # No checks means healthy
        assert len(result["checks"]) == 0

    @pytest.mark.asyncio
    async def test_run_health_checks_success(self, health_checker):
        """Test running successful health checks"""
        async def check1():
            return True

        async def check2():
            return {"healthy": True, "message": "All good", "details": {"status": "ok"}}

        health_checker.register_check("check1", check1)
        health_checker.register_check("check2", check2)

        result = await health_checker.run_health_checks()

        assert result["healthy"] is True
        assert len(result["checks"]) == 2
        assert result["checks"]["check1"]["healthy"] is True
        assert result["checks"]["check1"]["message"] == "OK"
        assert result["checks"]["check2"]["healthy"] is True
        assert result["checks"]["check2"]["message"] == "All good"
        assert result["checks"]["check2"]["details"]["status"] == "ok"

    @pytest.mark.asyncio
    async def test_run_health_checks_failure(self, health_checker):
        """Test running health checks with failures"""
        async def failing_check():
            return False

        async def error_check():
            raise Exception("Check failed")

        health_checker.register_check("failing", failing_check)
        health_checker.register_check("error", error_check)

        result = await health_checker.run_health_checks()

        assert result["healthy"] is False
        assert len(result["checks"]) == 2
        assert result["checks"]["failing"]["healthy"] is False
        assert result["checks"]["failing"]["message"] == "Failed"
        assert result["checks"]["error"]["healthy"] is False
        assert "Check failed" in result["checks"]["error"]["message"]

    @pytest.mark.asyncio
    async def test_run_health_checks_mixed(self, health_checker):
        """Test running health checks with mixed results"""
        async def success_check():
            return True

        async def fail_check():
            return {"healthy": False, "message": "Service down"}

        health_checker.register_check("success", success_check)
        health_checker.register_check("fail", fail_check)

        result = await health_checker.run_health_checks()

        assert result["healthy"] is False  # Overall unhealthy due to one failure
        assert result["checks"]["success"]["healthy"] is True
        assert result["checks"]["fail"]["healthy"] is False


class TestPerformanceMonitor:
    """Test performance monitoring functionality"""

    @pytest.fixture
    def performance_monitor(self):
        return PerformanceMonitor()

    def test_performance_monitor_initialization(self, performance_monitor):
        """Test performance monitor initialization"""
        assert performance_monitor is not None
        assert hasattr(performance_monitor, 'active_requests')
        assert hasattr(performance_monitor, 'request_history')
        assert hasattr(performance_monitor, 'max_history')
        assert isinstance(performance_monitor.active_requests, dict)
        assert isinstance(performance_monitor.request_history, list)
        assert performance_monitor.max_history == 1000

    def test_start_request(self, performance_monitor):
        """Test starting request monitoring"""
        request_id = "test_request_123"
        endpoint = "/api/v1/orders"
        method = "GET"

        performance_monitor.start_request(request_id, endpoint, method)

        assert request_id in performance_monitor.active_requests
        request_data = performance_monitor.active_requests[request_id]
        assert request_data["endpoint"] == endpoint
        assert request_data["method"] == method
        assert "start_time" in request_data
        assert "timestamp" in request_data

    def test_end_request(self, performance_monitor):
        """Test ending request monitoring"""
        request_id = "test_request_456"
        endpoint = "/api/v1/orders"
        method = "POST"
        status_code = 201

        # Start request
        performance_monitor.start_request(request_id, endpoint, method)

        # Add small delay to ensure duration > 0
        time.sleep(0.001)

        # End request
        performance_monitor.end_request(request_id, status_code)

        # Request should be removed from active requests
        assert request_id not in performance_monitor.active_requests

        # Request should be in history
        assert len(performance_monitor.request_history) == 1
        history_entry = performance_monitor.request_history[0]
        assert history_entry["endpoint"] == endpoint
        assert history_entry["method"] == method
        assert history_entry["status_code"] == status_code
        assert history_entry["duration"] > 0

    def test_end_request_not_found(self, performance_monitor):
        """Test ending request that was not started"""
        # Should not raise exception
        performance_monitor.end_request("nonexistent_request", 404)
        assert len(performance_monitor.request_history) == 0

    def test_get_performance_stats_empty(self, performance_monitor):
        """Test getting performance stats with no history"""
        stats = performance_monitor.get_performance_stats()
        assert isinstance(stats, dict)
        assert "message" in stats
        assert stats["message"] == "No request history available"

    def test_get_performance_stats_with_data(self, performance_monitor):
        """Test getting performance stats with request history"""
        # Add some test requests
        for i in range(5):
            request_id = f"test_request_{i}"
            performance_monitor.start_request(request_id, f"/api/v1/test{i}", "GET")
            time.sleep(0.001)  # Small delay
            performance_monitor.end_request(request_id, 200)

        stats = performance_monitor.get_performance_stats()

        assert isinstance(stats, dict)
        assert "total_requests" in stats
        assert "active_requests" in stats
        assert "avg_duration" in stats
        assert "min_duration" in stats
        assert "max_duration" in stats
        assert "recent_requests" in stats

        assert stats["total_requests"] == 5
        assert stats["active_requests"] == 0
        assert stats["avg_duration"] > 0
        assert stats["min_duration"] > 0
        assert stats["max_duration"] > 0
        assert len(stats["recent_requests"]) == 5

    def test_max_history_limit(self, performance_monitor):
        """Test that request history respects max limit"""
        # Set a small max history for testing
        performance_monitor.max_history = 3

        # Add more requests than the limit
        for i in range(5):
            request_id = f"test_request_{i}"
            performance_monitor.start_request(request_id, f"/api/v1/test{i}", "GET")
            performance_monitor.end_request(request_id, 200)

        # Should only keep the last 3 requests
        assert len(performance_monitor.request_history) == 3

        # Should be the most recent ones
        endpoints = [req["endpoint"] for req in performance_monitor.request_history]
        assert "/api/v1/test2" in endpoints
        assert "/api/v1/test3" in endpoints
        assert "/api/v1/test4" in endpoints


class TestGlobalFunctions:
    """Test global convenience functions"""

    def test_record_order_metric_function(self):
        """Test global record_order_metric function"""
        # Should not raise any exceptions
        record_order_metric("filled", "BTC/USDT", "buy")
        assert True

    def test_record_agent_metric_function(self):
        """Test global record_agent_metric function"""
        # Should not raise any exceptions
        record_agent_metric("parse_intents", 0.5)
        record_agent_metric("generate_plan", 1.2, "error")
        assert True

    def test_record_api_metric_function(self):
        """Test global record_api_metric function"""
        # Should not raise any exceptions
        record_api_metric("GET", "/api/v1/orders", 200, 0.1)
        assert True

    @pytest.mark.asyncio
    async def test_get_system_health_function(self):
        """Test global get_system_health function"""
        result = await get_system_health()
        assert isinstance(result, dict)
        assert "healthy" in result
        assert "timestamp" in result
        assert "checks" in result

    def test_get_metrics_data_function(self):
        """Test global get_metrics_data function"""
        result = get_metrics_data()
        assert isinstance(result, dict)
        assert "timestamp" in result
        assert "metrics" in result
        assert "prometheus_available" in result


class TestGlobalInstances:
    """Test global monitoring instances"""

    def test_global_metrics_collector(self):
        """Test global metrics_collector instance"""
        assert metrics_collector is not None
        assert isinstance(metrics_collector, MetricsCollector)

    def test_global_health_checker(self):
        """Test global health_checker instance"""
        assert health_checker is not None
        assert isinstance(health_checker, HealthChecker)

    def test_global_performance_monitor(self):
        """Test global performance_monitor instance"""
        assert performance_monitor is not None
        assert isinstance(performance_monitor, PerformanceMonitor)


# Prometheus integration tests removed - not using Prometheus in this project


class TestEdgeCases:
    """Test edge cases and error conditions"""

    def test_metrics_collector_with_none_values(self):
        """Test metrics collector with None values"""
        collector = MetricsCollector()

        # Should handle None values gracefully
        try:
            collector.record_order(None, None, None)
            collector.record_api_request(None, None, None, None)
            assert True  # Should not raise exceptions
        except Exception:
            pytest.fail("Should handle None values gracefully")

    def test_health_checker_with_async_checks(self):
        """Test health checker with async check functions"""
        checker = HealthChecker()

        async def async_check():
            await asyncio.sleep(0.001)  # Simulate async work
            return True

        checker.register_check("async_test", async_check)

        # Should work with async checks
        result = asyncio.run(checker.run_health_checks())
        assert result["healthy"] is True
        assert result["checks"]["async_test"]["healthy"] is True

    def test_performance_monitor_concurrent_requests(self):
        """Test performance monitor with concurrent requests"""
        monitor = PerformanceMonitor()

        # Start multiple requests
        for i in range(10):
            monitor.start_request(f"req_{i}", f"/api/test{i}", "GET")

        assert len(monitor.active_requests) == 10

        # End some requests
        for i in range(5):
            monitor.end_request(f"req_{i}", 200)

        assert len(monitor.active_requests) == 5
        assert len(monitor.request_history) == 5


class TestMetricsCollectorErrorHandling:
    """Test metrics collector error handling"""

    @pytest.fixture
    def metrics_collector(self):
        collector = MetricsCollector()
        return collector

    def test_invalid_metric_values(self, metrics_collector):
        """Test handling of invalid metric values"""
        # Test with None values - should not crash
        try:
            metrics_collector.record_agent_processing_time("test", None, "success")
            metrics_collector.set_active_websocket_connections(None)
        except Exception:
            pass  # Expected to handle gracefully

        # Should not raise any exceptions for the collector itself
        assert metrics_collector is not None

    def test_empty_string_parameters(self, metrics_collector):
        """Test handling of empty string parameters"""
        # Test with empty strings - should not crash
        try:
            metrics_collector.record_order("", "", "")
            metrics_collector.record_api_request("", "", 200, 0.1)
        except Exception:
            pass  # Expected to handle gracefully

        assert metrics_collector is not None

    def test_extreme_values(self, metrics_collector):
        """Test handling of extreme values"""
        # Test with very large numbers
        try:
            metrics_collector.record_agent_processing_time("test", 999999, "success")
            metrics_collector.set_active_websocket_connections(999999)
        except Exception:
            pass  # Expected to handle gracefully

        assert metrics_collector is not None



