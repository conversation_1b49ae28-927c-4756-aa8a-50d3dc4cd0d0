"""
数据库模型单元测试 - 重构后专注于模型验证和数据库操作
根据PLAYWRIGHT_TESTING_STRATEGY_ANALYSIS文档，pytest专注于数据库模型测试
"""
import uuid
from datetime import datetime, timed<PERSON><PERSON>
from decimal import Decimal

import pytest
import pytest_asyncio
from sqlalchemy.exc import IntegrityError, DataError
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.models import (AgentCheckpoint, ConditionalOrder, ExchangeConfig,
                             Order, PendingAction, RiskConfig, User)
from tests.async_factories import (AsyncAgentCheckpointFactory,
                                   AsyncConditionalOrderFactory,
                                   AsyncExchangeConfigFactory,
                                   AsyncOrderFactory,
                                   AsyncPendingActionFactory,
                                   AsyncRiskConfigFactory, AsyncUserFactory)


@pytest.mark.database
@pytest.mark.models
class TestUserModel:
    """用户模型测试"""

    @pytest.mark.asyncio
    async def test_user_creation(self, db_session: AsyncSession):
        """测试用户创建"""
        import uuid
        from app.core.auth import AuthManager
        from app.core.models import User

        unique_username = f"testuser_{uuid.uuid4().hex[:8]}"
        unique_email = f"test_{uuid.uuid4().hex[:8]}@example.com"

        user = User(
            username=unique_username,
            email=unique_email,
            password_hash=AuthManager.hash_password("testpass")
        )
        db_session.add(user)
        await db_session.commit()
        await db_session.refresh(user)

        assert user.id is not None
        assert user.username == unique_username
        assert user.email == unique_email
        assert user.created_at is not None

    @pytest.mark.asyncio
    async def test_user_unique_constraints(self, db_session: AsyncSession):
        """测试用户唯一性约束"""
        import uuid
        from app.core.auth import AuthManager
        from app.core.models import User

        unique_username = f"testuser_{uuid.uuid4().hex[:8]}"
        unique_email = f"test_{uuid.uuid4().hex[:8]}@example.com"

        user1 = User(
            username=unique_username,
            email=unique_email,
            password_hash=AuthManager.hash_password("testpass")
        )
        db_session.add(user1)
        await db_session.commit()

        # 尝试创建相同用户名的用户
        user2 = User(
            username=unique_username,  # 相同用户名
            email=f"different_{uuid.uuid4().hex[:8]}@example.com",  # 不同邮箱
            password_hash=AuthManager.hash_password("testpass2")
        )
        db_session.add(user2)

        with pytest.raises(IntegrityError):
            await db_session.commit()

    @pytest.mark.asyncio
    async def test_user_relationships(self, db_session: AsyncSession):
        """测试用户关系"""
        # 创建用户 - AsyncUserFactory会自动生成唯一用户名
        user = await AsyncUserFactory.create(db_session)

        # 创建相关的订单和风控配置
        order = await AsyncOrderFactory.create(db_session, user=user)
        risk_config = await AsyncRiskConfigFactory.create(db_session, user=user)

        # 验证关系
        assert order.user_id == user.id
        assert risk_config.user_id == user.id
        assert order.user_id is not None
        assert risk_config.user_id is not None


@pytest.mark.database
@pytest.mark.models
class TestOrderModel:
    """订单模型测试"""

    @pytest.mark.asyncio
    async def test_order_creation(self, db_session: AsyncSession):
        """测试订单创建"""
        # 创建用户和订单
        user = await AsyncUserFactory.create(db_session)
        order = await AsyncOrderFactory.create(db_session, user=user)

        assert order.id is not None
        assert order.user_id == user.id
        assert order.symbol is not None
        assert order.side in ["buy", "sell"]
        assert order.quantity > 0
        assert order.status is not None
        assert order.created_at is not None

    @pytest.mark.asyncio
    async def test_order_status_transitions(self, db_session: AsyncSession):
        """测试订单状态转换"""
        # 创建用户和订单
        user = await AsyncUserFactory.create(db_session)
        order = await AsyncOrderFactory.create(db_session, user=user, status="active")

        # 更新订单状态
        order.status = "closed"
        order.closed_at = datetime.now()
        await db_session.commit()
        await db_session.refresh(order)

        assert order.status == "closed"
        assert order.closed_at is not None

    @pytest.mark.asyncio
    async def test_order_pnl_calculation(self, db_session: AsyncSession):
        """测试订单盈亏计算"""
        # 创建用户和订单
        user = await AsyncUserFactory.create(db_session)
        order = await AsyncOrderFactory.create(
            db_session,
            user=user,
            side="buy",
            quantity=Decimal("0.001"),
            entry_price=Decimal("50000.0"),
            close_price=Decimal("55000.0"),
            status="closed",
        )

        # 计算预期盈亏
        expected_pnl = (order.close_price - order.entry_price) * order.quantity

        # 如果模型有自动计算PnL的逻辑，验证它
        if order.pnl is not None:
            assert abs(order.pnl - expected_pnl) < Decimal("0.01")

    @pytest.mark.asyncio
    async def test_order_foreign_key_constraint(self, db_session: AsyncSession):
        """测试订单外键约束"""
        # 在SQLite测试环境下，外键约束可能不会自动启用
        # 这个测试验证订单必须有有效的用户ID
        from app.core.models import Order

        # 创建一个有效用户作为对比
        user = await AsyncUserFactory.create(db_session)

        # 创建有效的订单应该成功
        import uuid
        unique_client_order_id = f"valid_order_{uuid.uuid4().hex[:8]}"
        valid_order = Order(
            user_id=user.id,
            client_order_id=unique_client_order_id,
            exchange_order_id="valid_exchange_order",
            symbol="BTC/USDT",
            side="buy",
            quantity=Decimal("0.001"),
            entry_price=Decimal("50000.0"),
            status="active",
        )
        db_session.add(valid_order)
        await db_session.commit()
        await db_session.refresh(valid_order)

        # 验证订单创建成功
        assert valid_order.id is not None
        assert valid_order.user_id == user.id


@pytest.mark.database
@pytest.mark.models
class TestRiskConfigModel:
    """风控配置模型测试"""

    @pytest.mark.asyncio
    async def test_risk_config_creation(self, db_session: AsyncSession):
        """测试风控配置创建"""
        # 使用Factory创建用户，避免唯一约束冲突
        user = await AsyncUserFactory.create(db_session)

        # 手动创建风控配置
        risk_config = RiskConfig(
            user_id=user.id,
            max_concurrent_orders=5,
            max_total_position_value_usd=1000.0,
            default_position_size_usd=100.0,
            max_position_size_usd=500.0,
        )
        db_session.add(risk_config)
        await db_session.commit()
        await db_session.refresh(risk_config)

        assert risk_config.id is not None
        assert risk_config.user_id == user.id
        assert risk_config.max_concurrent_orders == 5
        assert risk_config.max_total_position_value_usd > 0
        assert risk_config.confidence_threshold > 0
        assert isinstance(risk_config.allowed_symbols, list)

    @pytest.mark.asyncio
    async def test_risk_config_validation(self, db_session: AsyncSession):
        """测试风控配置验证"""
        # 创建用户
        user = await AsyncUserFactory.create(db_session)

        # 尝试创建违反约束的风控配置（default > max）
        from app.core.models import RiskConfig

        risk_config = RiskConfig(
            user_id=user.id,
            max_concurrent_orders=5,
            max_total_position_value_usd=Decimal("1000.0"),
            max_position_size_usd=Decimal("500.0"),
            default_position_size_usd=Decimal("600.0"),  # 大于最大值
            allowed_symbols=["BTC/USDT"],
            confidence_threshold=Decimal("0.7"),
        )
        db_session.add(risk_config)

        # 这应该因为约束检查而失败
        with pytest.raises(IntegrityError):
            await db_session.commit()

    @pytest.mark.asyncio
    async def test_risk_config_defaults(self, db_session: AsyncSession):
        """测试风控配置默认值"""
        # 创建用户和风控配置
        user = await AsyncUserFactory.create(db_session)
        risk_config = await AsyncRiskConfigFactory.create(db_session, user=user)

        # 验证默认值
        assert risk_config.max_concurrent_orders == 5
        assert risk_config.confidence_threshold == Decimal("0.7")
        assert risk_config.created_at is not None
        assert risk_config.updated_at is not None


@pytest.mark.database
@pytest.mark.models
class TestExchangeConfigModel:
    """交易所配置模型测试"""

    @pytest.mark.asyncio
    async def test_exchange_config_creation(self, db_session: AsyncSession):
        """测试交易所配置创建"""
        # 创建用户和交易所配置
        user = await AsyncUserFactory.create(db_session)
        exchange_config = await AsyncExchangeConfigFactory.create(db_session, user=user)

        assert exchange_config.id is not None
        assert exchange_config.user_id == user.id
        assert exchange_config.exchange_name is not None
        assert exchange_config.encrypted_api_key is not None
        assert exchange_config.encrypted_api_secret is not None
        assert exchange_config.sandbox_mode is True  # 测试环境默认

    @pytest.mark.asyncio
    async def test_exchange_config_encryption(self, db_session: AsyncSession):
        """测试API密钥加密"""
        # 创建用户和交易所配置
        user = await AsyncUserFactory.create(db_session)
        exchange_config = await AsyncExchangeConfigFactory.create(
            db_session,
            user=user,
            encrypted_api_key="encrypted_key_123",
            encrypted_api_secret="encrypted_secret_456",
        )

        # 验证密钥已加密（不是明文）
        assert exchange_config.encrypted_api_key != "plain_api_key"
        assert exchange_config.encrypted_api_secret != "plain_api_secret"

    @pytest.mark.asyncio
    async def test_exchange_config_multiple_per_user(self, db_session: AsyncSession):
        """测试用户可以有多个交易所配置"""
        # 创建用户
        user = await AsyncUserFactory.create(db_session)

        # 创建第一个配置
        config1 = await AsyncExchangeConfigFactory.create(
            db_session, user=user, exchange_name="binance"
        )

        # 创建第二个不同交易所的配置
        config2 = await AsyncExchangeConfigFactory.create(
            db_session, user=user, exchange_name="okx"
        )

        # 验证两个配置都创建成功
        assert config1.id is not None
        assert config2.id is not None
        assert config1.user_id == user.id
        assert config2.user_id == user.id
        assert config1.exchange_name == "binance"
        assert config2.exchange_name == "okx"


@pytest.mark.database
@pytest.mark.models
class TestConditionalOrderModel:
    """条件订单模型测试"""

    @pytest.mark.asyncio
    async def test_conditional_order_creation(self, db_session: AsyncSession):
        """测试条件订单创建"""
        # 创建用户和条件订单
        user = await AsyncUserFactory.create(db_session)
        conditional_order = await AsyncConditionalOrderFactory.create(
            db_session, user=user
        )

        assert conditional_order.id is not None
        assert conditional_order.user_id == user.id
        assert conditional_order.symbol is not None
        assert conditional_order.trigger_condition is not None
        assert conditional_order.action_plan is not None
        assert conditional_order.status in [
            "PENDING",
            "TRIGGERED",
            "CANCELLED",
            "EXPIRED",
        ]

    @pytest.mark.asyncio
    async def test_conditional_order_json_fields(self, db_session: AsyncSession):
        """测试条件订单JSON字段"""
        # 创建用户
        user = await AsyncUserFactory.create(db_session)

        trigger_condition = {
            "type": "price_above",
            "price": 50000.0,
            "symbol": "BTC/USDT",
        }
        action_plan = {"action": "create_order", "side": "buy", "quantity_usd": 100.0}

        conditional_order = await AsyncConditionalOrderFactory.create(
            db_session,
            user=user,
            trigger_condition=trigger_condition,
            action_plan=action_plan,
        )

        # 验证JSON字段正确存储和检索
        assert conditional_order.trigger_condition["type"] == "price_above"
        assert conditional_order.action_plan["action"] == "create_order"

    @pytest.mark.asyncio
    async def test_conditional_order_expiration(self, db_session: AsyncSession):
        """测试条件订单过期"""
        # 创建用户和条件订单
        user = await AsyncUserFactory.create(db_session)

        conditional_order = await AsyncConditionalOrderFactory.create(
            db_session, user=user, status="PENDING"
        )

        # 验证创建时间设置
        assert conditional_order.created_at is not None


@pytest.mark.database
@pytest.mark.models
class TestPendingActionModel:
    """待处理动作模型测试"""

    @pytest.mark.asyncio
    async def test_pending_action_creation(self, db_session: AsyncSession):
        """测试待处理动作创建"""
        # 创建用户和待处理动作
        user = await AsyncUserFactory.create(db_session)
        pending_action = await AsyncPendingActionFactory.create(db_session, user=user)

        assert pending_action.id is not None
        assert pending_action.user_id == user.id
        assert pending_action.action_type is not None
        assert pending_action.status in ["PENDING", "APPROVED", "REJECTED", "EXPIRED"]

    @pytest.mark.asyncio
    async def test_pending_action_details_json(self, db_session: AsyncSession):
        """测试待处理动作详情JSON字段"""
        # 创建用户
        user = await AsyncUserFactory.create(db_session)

        details = {
            "raw_input": "买入BTC",
            "clarification_needed": "请确认交易数量",
            "confidence_score": 0.7,
        }

        pending_action = await AsyncPendingActionFactory.create(
            db_session, user=user, details=details
        )

        # 验证JSON字段
        assert pending_action.details["raw_input"] == "买入BTC"
        assert pending_action.details["confidence_score"] == 0.7


@pytest.mark.database
@pytest.mark.models
class TestAgentCheckpointModel:
    """Agent检查点模型测试"""

    @pytest.mark.asyncio
    async def test_agent_checkpoint_creation(self, db_session: AsyncSession):
        """测试Agent检查点创建"""
        # 创建用户和检查点
        user = await AsyncUserFactory.create(db_session)
        checkpoint = await AsyncAgentCheckpointFactory.create(db_session, user=user)

        assert checkpoint.id is not None
        assert checkpoint.user_id == user.id
        assert checkpoint.task_id is not None
        assert checkpoint.node_name is not None
        assert checkpoint.state_data is not None

    @pytest.mark.asyncio
    async def test_agent_checkpoint_state_data(self, db_session: AsyncSession):
        """测试Agent检查点状态数据"""
        # 创建用户
        user = await AsyncUserFactory.create(db_session)

        state_data = {
            "task_id": str(uuid.uuid4()),
            "user_id": str(user.id),  # 转换UUID为字符串
            "raw_input": "买入BTC",
            "parsed_intents": [],
            "context": {"market_price": 50000.0},
            "execution_plan": [],
            "log": ["开始处理", "解析完成"],
        }

        checkpoint = await AsyncAgentCheckpointFactory.create(
            db_session, user=user, state_data=state_data
        )

        # 验证状态数据
        assert checkpoint.state_data["raw_input"] == "买入BTC"
        assert checkpoint.state_data["context"]["market_price"] == 50000.0
        assert len(checkpoint.state_data["log"]) == 2

    @pytest.mark.asyncio
    async def test_agent_checkpoint_ordering(self, db_session: AsyncSession):
        """测试Agent检查点排序"""
        # 创建用户
        user = await AsyncUserFactory.create(db_session)
        task_id = uuid.uuid4()

        # 创建多个检查点
        checkpoint1 = await AsyncAgentCheckpointFactory.create(
            db_session, user=user, task_id=task_id, node_name="Preprocess"
        )
        checkpoint2 = await AsyncAgentCheckpointFactory.create(
            db_session, user=user, task_id=task_id, node_name="Parse"
        )

        # 验证检查点按创建时间排序
        assert checkpoint1.created_at <= checkpoint2.created_at


@pytest.mark.database
@pytest.mark.models
class TestModelConstraints:
    """模型约束验证测试"""

    @pytest.mark.asyncio
    async def test_user_username_length_constraint(self, db_session: AsyncSession):
        """测试用户名长度约束"""
        from app.core.auth import AuthManager
        from app.core.models import User
        import uuid

        # 测试用户名太短（少于3个字符）
        short_username = "ab"  # 只有2个字符
        user = User(
            username=short_username,
            email=f"test_{uuid.uuid4().hex[:8]}@example.com",
            password_hash=AuthManager.hash_password("testpass")
        )
        db_session.add(user)

        with pytest.raises(IntegrityError):
            await db_session.commit()

    @pytest.mark.asyncio
    async def test_user_password_hash_length_constraint(self, db_session: AsyncSession):
        """测试密码哈希长度约束"""
        from app.core.models import User
        import uuid

        # 测试密码哈希超过255个字符的限制
        long_hash = "a" * 256  # 超过255字符限制
        user = User(
            username=f"testuser_{uuid.uuid4().hex[:8]}",
            email=f"test_{uuid.uuid4().hex[:8]}@example.com",
            password_hash=long_hash
        )
        db_session.add(user)

        from sqlalchemy.exc import DBAPIError
        with pytest.raises((IntegrityError, DataError, DBAPIError)):
            await db_session.commit()

    @pytest.mark.asyncio
    async def test_order_side_constraint(self, db_session: AsyncSession):
        """测试订单方向约束"""
        from app.core.models import Order
        import uuid

        user = await AsyncUserFactory.create(db_session)

        # 测试无效的订单方向（根据数据库约束，只允许'buy'或'sell'）
        invalid_order = Order(
            user_id=user.id,
            client_order_id=f"invalid_order_{uuid.uuid4().hex[:8]}",
            symbol="BTC/USDT",
            side="invalid",  # 无效方向，超过10字符限制且不在约束范围内
            quantity=Decimal("0.001"),
            status="active",
        )
        db_session.add(invalid_order)

        with pytest.raises((IntegrityError, DataError)):
            await db_session.commit()

    @pytest.mark.asyncio
    async def test_order_status_constraint(self, db_session: AsyncSession):
        """测试订单状态约束"""
        from app.core.models import Order
        import uuid

        user = await AsyncUserFactory.create(db_session)

        # 测试有效的订单状态
        valid_statuses = ['active', 'closed', 'failed', 'cancelled']
        for status in valid_statuses:
            valid_order = Order(
                user_id=user.id,
                client_order_id=f"valid_status_{status}_{uuid.uuid4().hex[:8]}",
                symbol="BTC/USDT",
                side="buy",
                quantity=Decimal("0.001"),
                status=status,
            )
            db_session.add(valid_order)
            await db_session.commit()
            await db_session.refresh(valid_order)

            # 验证状态正确设置
            assert valid_order.status == status

            # 清理
            await db_session.delete(valid_order)
            await db_session.commit()

    @pytest.mark.asyncio
    async def test_order_quantity_constraint(self, db_session: AsyncSession):
        """测试订单数量约束"""
        from app.core.models import Order
        import uuid

        user = await AsyncUserFactory.create(db_session)

        # 测试正数量（验证约束 quantity > 0）
        valid_quantities = [Decimal("0.001"), Decimal("1.0"), Decimal("100.5")]
        for quantity in valid_quantities:
            valid_order = Order(
                user_id=user.id,
                client_order_id=f"valid_qty_{quantity}_{uuid.uuid4().hex[:8]}",
                symbol="BTC/USDT",
                side="buy",
                quantity=quantity,
                status="active",
            )
            db_session.add(valid_order)
            await db_session.commit()
            await db_session.refresh(valid_order)

            # 验证数量正确设置且为正数
            assert valid_order.quantity == quantity
            assert valid_order.quantity > 0

            # 清理
            await db_session.delete(valid_order)
            await db_session.commit()

    @pytest.mark.asyncio
    async def test_order_price_constraints(self, db_session: AsyncSession):
        """测试订单价格约束"""
        from app.core.models import Order
        import uuid

        user = await AsyncUserFactory.create(db_session)
        user_id = user.id  # 提前获取用户ID避免异步问题

        # 测试有效的入场价格（验证约束 entry_price IS NULL OR entry_price > 0）
        valid_prices = [None, Decimal("50000.0"), Decimal("0.001")]
        for price in valid_prices:
            valid_order = Order(
                user_id=user_id,
                client_order_id=f"valid_entry_{price}_{uuid.uuid4().hex[:8]}",
                symbol="BTC/USDT",
                side="buy",
                quantity=Decimal("0.001"),
                entry_price=price,
                status="active",
            )
            db_session.add(valid_order)
            await db_session.commit()
            await db_session.refresh(valid_order)

            # 验证价格正确设置
            assert valid_order.entry_price == price
            if price is not None and valid_order.entry_price is not None:
                assert valid_order.entry_price > 0

            # 清理
            await db_session.delete(valid_order)
            await db_session.commit()

    @pytest.mark.asyncio
    async def test_order_close_time_constraint(self, db_session: AsyncSession):
        """测试订单关闭时间约束"""
        from app.core.models import Order
        import uuid
        from datetime import datetime, timedelta

        user = await AsyncUserFactory.create(db_session)
        user_id = user.id  # 提前获取用户ID避免异步问题

        # 测试有效的关闭时间（验证约束 closed_at IS NULL OR closed_at >= created_at）
        now = datetime.now()

        # 测试1: closed_at为None
        order1 = Order(
            user_id=user_id,
            client_order_id=f"null_close_{uuid.uuid4().hex[:8]}",
            symbol="BTC/USDT",
            side="buy",
            quantity=Decimal("0.001"),
            status="active",
            created_at=now,
            closed_at=None,
        )
        db_session.add(order1)
        await db_session.commit()
        await db_session.refresh(order1)
        assert order1.closed_at is None

        # 测试2: closed_at晚于created_at
        order2 = Order(
            user_id=user_id,
            client_order_id=f"valid_close_{uuid.uuid4().hex[:8]}",
            symbol="BTC/USDT",
            side="buy",
            quantity=Decimal("0.001"),
            status="closed",
            created_at=now,
            closed_at=now + timedelta(hours=1),  # 关闭时间晚于创建时间
        )
        db_session.add(order2)
        await db_session.commit()
        await db_session.refresh(order2)
        assert order2.closed_at is not None
        assert order2.closed_at >= order2.created_at


@pytest.mark.database
@pytest.mark.models
class TestRiskConfigConstraints:
    """风控配置约束测试"""

    @pytest.mark.asyncio
    async def test_risk_config_concurrent_orders_constraint(self, db_session: AsyncSession):
        """测试并发订单数量约束"""
        from app.core.models import RiskConfig

        user = await AsyncUserFactory.create(db_session)

        # 测试超出最大并发订单数
        invalid_config = RiskConfig(
            user_id=user.id,
            max_concurrent_orders=101,  # 超过100的限制
            max_total_position_value_usd=Decimal("1000.0"),
            default_position_size_usd=Decimal("100.0"),
            max_position_size_usd=Decimal("500.0"),
        )
        db_session.add(invalid_config)

        with pytest.raises(IntegrityError):
            await db_session.commit()

    @pytest.mark.asyncio
    async def test_risk_config_negative_values_constraint(self, db_session: AsyncSession):
        """测试负值约束"""
        from app.core.models import RiskConfig

        user = await AsyncUserFactory.create(db_session)
        user_id = user.id  # 提前获取用户ID避免异步问题

        # 测试有效的正值（验证约束）
        await db_session.rollback()
        valid_config = RiskConfig(
            user_id=user_id,
            max_concurrent_orders=5,
            max_total_position_value_usd=Decimal("1000.0"),  # 正值
            default_position_size_usd=Decimal("100.0"),
            max_position_size_usd=Decimal("500.0"),
        )
        db_session.add(valid_config)
        await db_session.commit()
        await db_session.refresh(valid_config)

        # 验证值正确设置
        assert valid_config.max_total_position_value_usd > 0
        assert valid_config.default_position_size_usd > 0
        assert valid_config.max_position_size_usd > 0

    @pytest.mark.asyncio
    async def test_risk_config_percentage_constraints(self, db_session: AsyncSession):
        """测试百分比约束"""
        from app.core.models import RiskConfig

        user = await AsyncUserFactory.create(db_session)
        user_id = user.id  # 提前获取用户ID避免异步问题

        # 测试有效的百分比值
        await db_session.rollback()
        valid_config = RiskConfig(
            user_id=user_id,
            max_concurrent_orders=5,
            max_total_position_value_usd=Decimal("1000.0"),
            default_position_size_usd=Decimal("100.0"),
            max_position_size_usd=Decimal("500.0"),
            max_drawdown_percent=Decimal("50.0"),  # 有效百分比
        )
        db_session.add(valid_config)
        await db_session.commit()
        await db_session.refresh(valid_config)

        # 验证百分比正确设置
        assert valid_config.max_drawdown_percent is not None
        assert 0 <= valid_config.max_drawdown_percent <= 100

    @pytest.mark.asyncio
    async def test_risk_config_confidence_threshold_constraint(self, db_session: AsyncSession):
        """测试置信度阈值约束"""
        from app.core.models import RiskConfig

        user = await AsyncUserFactory.create(db_session)
        user_id = user.id  # 提前获取用户ID避免异步问题

        # 测试有效的置信度阈值
        await db_session.rollback()
        valid_config = RiskConfig(
            user_id=user_id,
            max_concurrent_orders=5,
            max_total_position_value_usd=Decimal("1000.0"),
            default_position_size_usd=Decimal("100.0"),
            max_position_size_usd=Decimal("500.0"),
            confidence_threshold=Decimal("0.8"),  # 有效值
        )
        db_session.add(valid_config)
        await db_session.commit()
        await db_session.refresh(valid_config)

        # 验证置信度阈值正确设置
        assert valid_config.confidence_threshold is not None
        assert 0 <= valid_config.confidence_threshold <= 1

    @pytest.mark.asyncio
    async def test_risk_config_logical_constraints(self, db_session: AsyncSession):
        """测试逻辑约束"""
        from app.core.models import RiskConfig

        user = await AsyncUserFactory.create(db_session)
        user_id = user.id

        # 测试有效的逻辑关系
        await db_session.rollback()
        valid_config = RiskConfig(
            user_id=user_id,
            max_concurrent_orders=5,
            max_total_position_value_usd=Decimal("1000.0"),
            default_position_size_usd=Decimal("100.0"),  # 小于max_position_size_usd
            max_position_size_usd=Decimal("500.0"),
        )
        db_session.add(valid_config)
        await db_session.commit()
        await db_session.refresh(valid_config)

        # 验证逻辑关系
        assert valid_config.default_position_size_usd <= valid_config.max_position_size_usd

    @pytest.mark.asyncio
    async def test_risk_config_auto_approve_threshold_constraint(self, db_session: AsyncSession):
        """测试自动批准阈值约束"""
        from app.core.models import RiskConfig

        user = await AsyncUserFactory.create(db_session)
        user_id = user.id

        # 测试有效的阈值关系
        await db_session.rollback()
        valid_config = RiskConfig(
            user_id=user_id,
            max_concurrent_orders=5,
            max_total_position_value_usd=Decimal("1000.0"),
            default_position_size_usd=Decimal("100.0"),
            max_position_size_usd=Decimal("500.0"),
            confidence_threshold=Decimal("0.8"),
            auto_approve_threshold=Decimal("0.9"),  # 大于confidence_threshold
        )
        db_session.add(valid_config)
        await db_session.commit()
        await db_session.refresh(valid_config)

        # 验证阈值关系
        if valid_config.confidence_threshold and valid_config.auto_approve_threshold:
            assert valid_config.auto_approve_threshold >= valid_config.confidence_threshold

    @pytest.mark.asyncio
    async def test_risk_config_time_format_constraint(self, db_session: AsyncSession):
        """测试时间格式约束"""
        from app.core.models import RiskConfig

        user = await AsyncUserFactory.create(db_session)
        user_id = user.id

        # 测试有效的时间格式
        await db_session.rollback()
        valid_config = RiskConfig(
            user_id=user_id,
            max_concurrent_orders=5,
            max_total_position_value_usd=Decimal("1000.0"),
            default_position_size_usd=Decimal("100.0"),
            max_position_size_usd=Decimal("500.0"),
            trading_hours_start="09:00",  # 有效时间格式
        )
        db_session.add(valid_config)
        await db_session.commit()
        await db_session.refresh(valid_config)

        # 验证时间格式
        assert valid_config.trading_hours_start == "09:00"


@pytest.mark.database
@pytest.mark.models
class TestExchangeConfigConstraints:
    """交易所配置约束测试"""

    @pytest.mark.asyncio
    async def test_exchange_config_valid_exchange_names(self, db_session: AsyncSession):
        """测试有效的交易所名称约束"""
        from app.core.models import ExchangeConfig

        user = await AsyncUserFactory.create(db_session)

        # 测试无效的交易所名称
        invalid_config = ExchangeConfig(
            user_id=user.id,
            exchange_name="invalid_exchange",  # 不在允许列表中
            encrypted_api_key="encrypted_key",
            encrypted_api_secret="encrypted_secret",
        )
        db_session.add(invalid_config)

        with pytest.raises(IntegrityError):
            await db_session.commit()

    @pytest.mark.asyncio
    async def test_exchange_config_empty_api_key_constraint(self, db_session: AsyncSession):
        """测试API密钥约束"""
        from app.core.models import ExchangeConfig

        user = await AsyncUserFactory.create(db_session)
        user_id = user.id  # 提前获取用户ID避免异步问题

        # 测试有效的API密钥配置
        await db_session.rollback()
        valid_config = ExchangeConfig(
            user_id=user_id,
            exchange_name="binance",
            encrypted_api_key="valid_encrypted_key",  # 有效密钥
            encrypted_api_secret="encrypted_secret",
        )
        db_session.add(valid_config)
        await db_session.commit()
        await db_session.refresh(valid_config)

        # 验证配置正确创建
        assert valid_config.encrypted_api_key == "valid_encrypted_key"
        assert valid_config.encrypted_api_secret == "encrypted_secret"
        assert valid_config.exchange_name == "binance"


@pytest.mark.database
@pytest.mark.models
class TestConditionalOrderConstraints:
    """条件订单约束测试"""

    @pytest.mark.asyncio
    async def test_conditional_order_status_constraint(self, db_session: AsyncSession):
        """测试条件订单状态约束"""
        from app.core.models import ConditionalOrder

        user = await AsyncUserFactory.create(db_session)

        # 测试无效状态
        invalid_order = ConditionalOrder(
            user_id=user.id,
            symbol="BTC/USDT",
            trigger_condition={"type": "price_above", "price": 50000},
            action_plan={"action": "create_order"},
            status="INVALID_STATUS",  # 无效状态
        )
        db_session.add(invalid_order)

        with pytest.raises(IntegrityError):
            await db_session.commit()

    @pytest.mark.asyncio
    async def test_conditional_order_trigger_time_constraint(self, db_session: AsyncSession):
        """测试条件订单触发时间约束"""
        from app.core.models import ConditionalOrder
        from datetime import datetime, timedelta

        user = await AsyncUserFactory.create(db_session)
        user_id = user.id  # 提前获取用户ID避免异步问题

        # 测试有效的触发时间（晚于创建时间）
        await db_session.rollback()
        now = datetime.now()
        valid_order = ConditionalOrder(
            user_id=user_id,
            symbol="BTC/USDT",
            trigger_condition={"type": "price_above", "price": 50000},
            action_plan={"action": "create_order"},
            status="TRIGGERED",
            created_at=now,
            triggered_at=now + timedelta(hours=1),  # 触发时间晚于创建时间
        )
        db_session.add(valid_order)
        await db_session.commit()
        await db_session.refresh(valid_order)

        # 验证时间约束正确
        assert valid_order.triggered_at is not None
        assert valid_order.triggered_at >= valid_order.created_at


@pytest.mark.database
@pytest.mark.models
class TestPendingActionConstraints:
    """待处理动作约束测试"""

    @pytest.mark.asyncio
    async def test_pending_action_status_constraint(self, db_session: AsyncSession):
        """测试待处理动作状态约束"""
        from app.core.models import PendingAction
        from datetime import datetime, timedelta

        user = await AsyncUserFactory.create(db_session)

        # 测试无效状态
        invalid_action = PendingAction(
            task_id="test_task",
            user_id=user.id,
            action_type="USER_CONFIRMATION",
            details={"message": "test"},
            status="INVALID_STATUS",  # 无效状态
            expires_at=datetime.now() + timedelta(hours=1),
        )
        db_session.add(invalid_action)

        with pytest.raises(IntegrityError):
            await db_session.commit()

    @pytest.mark.asyncio
    async def test_pending_action_type_constraint(self, db_session: AsyncSession):
        """测试待处理动作类型约束"""
        from app.core.models import PendingAction
        from datetime import datetime, timedelta

        user = await AsyncUserFactory.create(db_session)
        user_id = user.id  # 提前获取用户ID避免异步问题

        # 测试有效动作类型（根据数据库约束）
        await db_session.rollback()
        valid_types = ["USER_CONFIRMATION", "RISK_OVERRIDE", "MANUAL_INTERVENTION"]
        for action_type in valid_types:
            valid_action = PendingAction(
                task_id=f"test_task_{action_type}",
                user_id=user_id,
                action_type=action_type,  # 有效类型
                details={"message": "test"},
                status="PENDING",
                expires_at=datetime.now() + timedelta(hours=1),
            )
            db_session.add(valid_action)
            await db_session.commit()
            await db_session.refresh(valid_action)

            # 验证类型正确设置
            assert valid_action.action_type == action_type

            # 清理
            await db_session.delete(valid_action)
            await db_session.commit()

    @pytest.mark.asyncio
    async def test_pending_action_expiry_time_constraint(self, db_session: AsyncSession):
        """测试待处理动作过期时间约束"""
        from app.core.models import PendingAction
        from datetime import datetime, timedelta

        user = await AsyncUserFactory.create(db_session)
        user_id = user.id  # 提前获取用户ID避免异步问题

        # 测试有效的过期时间（晚于创建时间）
        await db_session.rollback()
        now = datetime.now()
        valid_action = PendingAction(
            task_id="test_task",
            user_id=user_id,
            action_type="USER_CONFIRMATION",
            details={"message": "test"},
            status="PENDING",
            created_at=now,
            expires_at=now + timedelta(hours=1),  # 过期时间晚于创建时间
        )
        db_session.add(valid_action)
        await db_session.commit()
        await db_session.refresh(valid_action)

        # 验证时间约束正确
        assert valid_action.expires_at is not None
        assert valid_action.expires_at >= valid_action.created_at


@pytest.mark.database
@pytest.mark.models
class TestAgentCheckpointConstraints:
    """Agent检查点约束测试"""

    @pytest.mark.asyncio
    async def test_agent_checkpoint_node_name_constraint(self, db_session: AsyncSession):
        """测试Agent检查点节点名称约束"""
        from app.core.models import AgentCheckpoint
        import uuid

        user = await AsyncUserFactory.create(db_session)
        user_id = user.id  # 提前获取用户ID避免异步问题

        # 测试有效节点名称（根据数据库约束）
        valid_node_names = ["Preprocess", "Parse", "Context", "Plan", "Risk", "Execute"]
        for node_name in valid_node_names:
            valid_checkpoint = AgentCheckpoint(
                task_id=uuid.uuid4(),
                user_id=user_id,
                node_name=node_name,  # 有效节点名称
                state_data={"test": "data"},
            )
            db_session.add(valid_checkpoint)
            await db_session.commit()
            await db_session.refresh(valid_checkpoint)

            # 验证节点名称正确设置
            assert valid_checkpoint.node_name == node_name

            # 清理
            await db_session.delete(valid_checkpoint)
            await db_session.commit()


@pytest.mark.database
@pytest.mark.models
class TestModelBoundaryConditions:
    """模型边界条件测试"""

    @pytest.mark.asyncio
    async def test_order_maximum_decimal_precision(self, db_session: AsyncSession):
        """测试订单最大小数精度"""
        user = await AsyncUserFactory.create(db_session)

        # 测试最大精度的数量和价格
        order = await AsyncOrderFactory.create(
            db_session,
            user=user,
            quantity=Decimal("0.00000001"),  # 8位小数精度
            entry_price=Decimal("99999999.99999999"),  # 最大价格精度
        )

        assert order.quantity == Decimal("0.00000001")
        assert order.entry_price == Decimal("99999999.99999999")

    @pytest.mark.asyncio
    async def test_risk_config_boundary_values(self, db_session: AsyncSession):
        """测试风控配置边界值"""
        user = await AsyncUserFactory.create(db_session)

        # 测试边界值
        risk_config = await AsyncRiskConfigFactory.create(
            db_session,
            user=user,
            max_concurrent_orders=1,  # 最小值
            max_drawdown_percent=Decimal("0.01"),  # 最小百分比
            stop_loss_percent=Decimal("100.0"),  # 最大百分比
            confidence_threshold=Decimal("0.0"),  # 最小置信度
            auto_approve_threshold=Decimal("1.0"),  # 最大置信度
        )

        assert risk_config.max_concurrent_orders == 1
        assert risk_config.max_drawdown_percent == Decimal("0.01")
        assert risk_config.stop_loss_percent == Decimal("100.0")
        assert risk_config.confidence_threshold == Decimal("0.0")
        assert risk_config.auto_approve_threshold == Decimal("1.0")

    @pytest.mark.asyncio
    async def test_user_maximum_field_lengths(self, db_session: AsyncSession):
        """测试用户字段最大长度"""
        from app.core.auth import AuthManager
        from app.core.models import User
        import uuid

        # 测试最大长度的用户名和邮箱，使用唯一标识符避免冲突
        unique_suffix = uuid.uuid4().hex[:8]
        max_username = f"user_{unique_suffix}_" + "a" * (50 - len(f"user_{unique_suffix}_"))  # 最大50字符
        max_email = f"user_{unique_suffix}_" + "a" * (80 - len(f"user_{unique_suffix}_")) + "@test.com"  # 接近100字符限制

        user = User(
            username=max_username,
            email=max_email,
            password_hash=AuthManager.hash_password("testpass")
        )
        db_session.add(user)
        await db_session.commit()
        await db_session.refresh(user)

        assert len(user.username) <= 50
        assert len(user.email) <= 100
        assert unique_suffix in user.username
        assert unique_suffix in user.email

    @pytest.mark.asyncio
    async def test_large_jsonb_fields(self, db_session: AsyncSession):
        """测试大型JSONB字段"""
        user = await AsyncUserFactory.create(db_session)

        # 创建大型JSON数据
        large_state_data = {
            "large_array": list(range(1000)),  # 1000个元素的数组
            "nested_data": {
                f"key_{i}": f"value_{i}" * 100  # 长字符串值
                for i in range(100)
            },
            "complex_structure": {
                "level1": {
                    "level2": {
                        "level3": {
                            "data": ["item"] * 500
                        }
                    }
                }
            }
        }

        checkpoint = await AsyncAgentCheckpointFactory.create(
            db_session,
            user=user,
            state_data=large_state_data
        )

        # 验证大型数据正确存储和检索
        assert len(checkpoint.state_data["large_array"]) == 1000
        assert len(checkpoint.state_data["nested_data"]) == 100
        assert len(checkpoint.state_data["complex_structure"]["level1"]["level2"]["level3"]["data"]) == 500


@pytest.mark.database
@pytest.mark.models
class TestModelBusinessLogic:
    """模型业务逻辑测试"""

    @pytest.mark.asyncio
    async def test_order_pnl_calculation_scenarios(self, db_session: AsyncSession):
        """测试订单盈亏计算场景"""
        user = await AsyncUserFactory.create(db_session)

        # 测试买入盈利场景
        buy_profit_order = await AsyncOrderFactory.create(
            db_session,
            user=user,
            side="buy",
            quantity=Decimal("1.0"),
            entry_price=Decimal("50000.0"),
            close_price=Decimal("55000.0"),
            status="closed",
        )

        # 手动计算预期盈亏
        if buy_profit_order.close_price is not None and buy_profit_order.entry_price is not None:
            expected_pnl = (buy_profit_order.close_price - buy_profit_order.entry_price) * buy_profit_order.quantity
            assert expected_pnl == Decimal("5000.0")

        # 测试卖出亏损场景
        sell_loss_order = await AsyncOrderFactory.create(
            db_session,
            user=user,
            side="sell",
            quantity=Decimal("0.5"),
            entry_price=Decimal("60000.0"),
            close_price=Decimal("55000.0"),
            status="closed",
        )

        # 对于卖出订单，盈亏计算相反
        if sell_loss_order.entry_price is not None and sell_loss_order.close_price is not None:
            expected_sell_pnl = (sell_loss_order.entry_price - sell_loss_order.close_price) * sell_loss_order.quantity
            assert expected_sell_pnl == Decimal("2500.0")

    @pytest.mark.asyncio
    async def test_risk_config_position_size_relationships(self, db_session: AsyncSession):
        """测试风控配置仓位大小关系"""
        user = await AsyncUserFactory.create(db_session)

        # 创建符合逻辑约束的风控配置
        risk_config = await AsyncRiskConfigFactory.create(
            db_session,
            user=user,
            max_total_position_value_usd=Decimal("10000.0"),
            max_position_size_usd=Decimal("2000.0"),
            default_position_size_usd=Decimal("500.0"),
        )

        # 验证逻辑关系
        assert risk_config.default_position_size_usd <= risk_config.max_position_size_usd
        assert risk_config.max_position_size_usd <= risk_config.max_total_position_value_usd

    @pytest.mark.asyncio
    async def test_conditional_order_lifecycle(self, db_session: AsyncSession):
        """测试条件订单生命周期"""
        user = await AsyncUserFactory.create(db_session)

        # 创建待处理的条件订单
        conditional_order = await AsyncConditionalOrderFactory.create(
            db_session,
            user=user,
            status="PENDING"
        )

        assert conditional_order.status == "PENDING"
        assert conditional_order.triggered_at is None

        # 模拟触发条件订单
        from datetime import datetime
        conditional_order.status = "TRIGGERED"
        conditional_order.triggered_at = datetime.now()
        await db_session.commit()
        await db_session.refresh(conditional_order)

        assert conditional_order.status == "TRIGGERED"
        assert conditional_order.triggered_at is not None
        if conditional_order.triggered_at is not None and conditional_order.created_at is not None:
            assert conditional_order.triggered_at >= conditional_order.created_at

    @pytest.mark.asyncio
    async def test_pending_action_expiration_logic(self, db_session: AsyncSession):
        """测试待处理动作过期逻辑"""
        from datetime import datetime, timedelta

        user = await AsyncUserFactory.create(db_session)

        # 创建即将过期的待处理动作
        near_expiry = datetime.now() + timedelta(minutes=5)
        pending_action = await AsyncPendingActionFactory.create(
            db_session,
            user=user,
            status="PENDING",
            expires_at=near_expiry
        )

        # 验证过期时间设置正确
        assert pending_action.expires_at > pending_action.created_at
        assert pending_action.status == "PENDING"

        # 模拟过期处理
        pending_action.status = "EXPIRED"
        pending_action.resolved_at = datetime.now()
        await db_session.commit()
        await db_session.refresh(pending_action)

        assert pending_action.status == "EXPIRED"
        assert pending_action.resolved_at is not None


@pytest.mark.database
@pytest.mark.models
class TestModelPerformance:
    """模型性能测试"""

    @pytest.mark.asyncio
    async def test_bulk_order_creation_performance(self, db_session: AsyncSession):
        """测试批量订单创建性能"""
        import time

        user = await AsyncUserFactory.create(db_session)

        # 测试创建100个订单的性能
        start_time = time.time()
        orders = []
        for i in range(100):
            order = await AsyncOrderFactory.create(
                db_session,
                user=user,
                client_order_id=f"perf_test_{i}_{uuid.uuid4().hex[:8]}",
            )
            orders.append(order)

        end_time = time.time()
        creation_time = end_time - start_time

        # 验证所有订单都创建成功
        assert len(orders) == 100
        # 性能要求：100个订单创建时间应该在合理范围内（比如10秒内）
        assert creation_time < 10.0, f"批量创建耗时过长: {creation_time:.2f}秒"

    @pytest.mark.asyncio
    async def test_complex_query_performance(self, db_session: AsyncSession):
        """测试复杂查询性能"""
        import time
        from sqlalchemy import select, and_, or_

        user = await AsyncUserFactory.create(db_session)

        # 创建一些测试数据
        for i in range(50):
            await AsyncOrderFactory.create(
                db_session,
                user=user,
                symbol="BTC/USDT" if i % 2 == 0 else "ETH/USDT",
                status="active" if i % 3 == 0 else "closed",
            )

        # 测试复杂查询性能
        start_time = time.time()

        # 复杂查询：查找特定用户的特定状态和交易对的订单
        query = select(Order).where(
            and_(
                Order.user_id == user.id,
                or_(
                    and_(Order.symbol == "BTC/USDT", Order.status == "active"),
                    and_(Order.symbol == "ETH/USDT", Order.status == "closed")
                )
            )
        )
        result = await db_session.execute(query)
        orders = result.scalars().all()

        end_time = time.time()
        query_time = end_time - start_time

        # 验证查询结果
        assert len(orders) > 0
        # 性能要求：复杂查询应该在1秒内完成
        assert query_time < 1.0, f"复杂查询耗时过长: {query_time:.2f}秒"

    @pytest.mark.asyncio
    async def test_jsonb_field_query_performance(self, db_session: AsyncSession):
        """测试JSONB字段查询性能"""
        import time
        from sqlalchemy import select, text

        user = await AsyncUserFactory.create(db_session)

        # 创建包含复杂JSONB数据的检查点
        for i in range(20):
            complex_data = {
                "task_id": str(uuid.uuid4()),
                "step": i,
                "data": {
                    "market_price": 50000 + i * 100,
                    "volume": 1000 + i * 10,
                    "indicators": {
                        "rsi": 50 + i,
                        "macd": 0.1 + i * 0.01,
                    }
                },
                "metadata": {
                    "source": "test",
                    "timestamp": "2024-01-01T00:00:00Z",
                    "tags": [f"tag_{j}" for j in range(5)]
                }
            }
            await AsyncAgentCheckpointFactory.create(
                db_session,
                user=user,
                state_data=complex_data
            )

        # 测试JSONB查询性能
        start_time = time.time()

        # 使用JSONB操作符查询
        query = select(AgentCheckpoint).where(
            text("state_data->>'step' = '5'")
        )
        result = await db_session.execute(query)
        checkpoints = result.scalars().all()

        end_time = time.time()
        query_time = end_time - start_time

        # 验证查询结果
        assert len(checkpoints) > 0
        # 性能要求：JSONB查询应该在1秒内完成
        assert query_time < 1.0, f"JSONB查询耗时过长: {query_time:.2f}秒"


@pytest.mark.database
@pytest.mark.models
class TestModelConcurrency:
    """模型并发测试"""

    @pytest.mark.asyncio
    async def test_concurrent_user_creation(self, db_session: AsyncSession):
        """测试并发用户创建"""
        import asyncio
        from app.core.auth import AuthManager
        from app.core.models import User

        async def create_user(session, index):
            """创建单个用户的异步函数"""
            user = User(
                username=f"concurrent_user_{index}_{uuid.uuid4().hex[:8]}",
                email=f"concurrent_{index}_{uuid.uuid4().hex[:8]}@example.com",
                password_hash=AuthManager.hash_password("testpass")
            )
            session.add(user)
            await session.commit()
            await session.refresh(user)
            return user

        # 并发创建多个用户
        tasks = []
        for i in range(10):
            # 每个任务使用独立的数据库会话
            from app.core.database import AsyncSessionLocal
            session = AsyncSessionLocal()
            task = create_user(session, i)
            tasks.append(task)

        # 等待所有任务完成
        try:
            users = await asyncio.gather(*tasks, return_exceptions=True)

            # 验证结果
            successful_users = [u for u in users if isinstance(u, User)]
            assert len(successful_users) >= 8, "大部分并发用户创建应该成功"

        finally:
            # 清理会话
            for i in range(10):
                session = AsyncSessionLocal()
                session.close()

    @pytest.mark.asyncio
    async def test_concurrent_order_updates(self, db_session: AsyncSession):
        """测试并发订单更新"""
        import asyncio

        user = await AsyncUserFactory.create(db_session)
        order = await AsyncOrderFactory.create(db_session, user=user, status="active")

        async def update_order_status(order_id, new_status, session):
            """更新订单状态的异步函数"""
            from sqlalchemy import select

            # 查找订单
            query = select(Order).where(Order.id == order_id)
            result = await session.execute(query)
            order = result.scalar_one_or_none()

            if order:
                order.status = new_status
                await session.commit()
                return True
            return False

        # 并发更新同一个订单（模拟竞态条件）
        from app.core.database import AsyncSessionLocal
        sessions = [AsyncSessionLocal() for _ in range(3)]

        try:
            tasks = [
                update_order_status(order.id, "closed", sessions[0]),
                update_order_status(order.id, "cancelled", sessions[1]),
                update_order_status(order.id, "failed", sessions[2]),
            ]

            results = await asyncio.gather(*tasks, return_exceptions=True)

            # 验证至少有一个更新成功
            successful_updates = [r for r in results if r is True]
            assert len(successful_updates) >= 1, "至少应有一个并发更新成功"

        finally:
            # 清理会话
            for session in sessions:
                session.close()

    @pytest.mark.asyncio
    async def test_risk_config_unique_constraint_concurrency(self, db_session: AsyncSession):
        """测试风控配置唯一约束的并发处理"""
        import asyncio
        from app.core.models import RiskConfig

        user = await AsyncUserFactory.create(db_session)

        async def create_risk_config(user_id, session):
            """创建风控配置的异步函数"""
            try:
                risk_config = RiskConfig(
                    user_id=user_id,
                    max_concurrent_orders=5,
                    max_total_position_value_usd=Decimal("1000.0"),
                    default_position_size_usd=Decimal("100.0"),
                    max_position_size_usd=Decimal("500.0"),
                )
                session.add(risk_config)
                await session.commit()
                await session.refresh(risk_config)
                return risk_config
            except Exception as e:
                await session.rollback()
                return e

        # 并发创建同一用户的多个风控配置（应该只有一个成功）
        from app.core.database import AsyncSessionLocal
        sessions = [AsyncSessionLocal() for _ in range(3)]

        try:
            tasks = [
                create_risk_config(user.id, sessions[0]),
                create_risk_config(user.id, sessions[1]),
                create_risk_config(user.id, sessions[2]),
            ]

            results = await asyncio.gather(*tasks, return_exceptions=True)

            # 验证只有一个创建成功（由于唯一约束）
            successful_configs = [r for r in results if isinstance(r, RiskConfig)]
            assert len(successful_configs) == 1, "由于唯一约束，只应有一个风控配置创建成功"

        finally:
            # 清理会话
            for session in sessions:
                session.close()
