"""
测试API响应格式标准化
"""
import pytest
from unittest.mock import AsyncMock, MagicMock
from fastapi.testclient import TestClient
from fastapi import FastAPI

from app.core.schemas import APIResponse, OrderResponse
from app.api.v1.orders import router as orders_router


class TestAPIResponseFormat:
    """测试API响应格式标准化"""

    def test_api_response_success_creation(self):
        """测试成功响应创建"""
        data = {"test": "data"}
        response = APIResponse.success_response(data=data, message="测试成功")
        
        assert response.success is True
        assert response.message == "测试成功"
        assert response.data == data
        assert response.error_code is None

    def test_api_response_error_creation(self):
        """测试错误响应创建"""
        response = APIResponse.error_response(message="测试失败", error_code="TEST_ERROR")
        
        assert response.success is False
        assert response.message == "测试失败"
        assert response.data is None
        assert response.error_code == "TEST_ERROR"

    def test_api_response_default_values(self):
        """测试API响应默认值"""
        response = APIResponse.success_response()
        
        assert response.success is True
        assert response.message == "操作成功"
        assert response.data is None
        assert response.error_code is None

    def test_api_response_serialization(self):
        """测试API响应序列化"""
        data = {"id": "123", "name": "test"}
        response = APIResponse.success_response(data=data, message="序列化测试")
        
        # 转换为字典
        response_dict = response.model_dump()
        
        expected = {
            "success": True,
            "message": "序列化测试",
            "data": data,
            "error_code": None
        }
        
        assert response_dict == expected

    def test_api_response_with_typed_data(self):
        """测试带类型数据的API响应"""
        # 模拟OrderResponse数据
        order_data = {
            "id": "550e8400-e29b-41d4-a716-446655440000",
            "user_id": "550e8400-e29b-41d4-a716-446655440001",
            "client_order_id": "test_order_123",
            "symbol": "BTC/USDT",
            "side": "buy",
            "quantity": "0.001",
            "status": "active",
            "created_at": "2025-01-01T00:00:00"
        }
        
        response = APIResponse.success_response(data=order_data, message="订单数据获取成功")
        
        assert response.success is True
        assert response.data == order_data
        assert response.message == "订单数据获取成功"

    def test_orders_router_endpoints_exist(self):
        """测试订单路由端点存在"""
        # 检查所有必需的端点
        expected_endpoints = [
            ("GET", "/orders"),
            ("GET", "/orders/{order_id}"),
            ("POST", "/orders"),
            ("POST", "/orders/{order_id}/close"),
            ("PATCH", "/orders/{order_id}"),
            ("DELETE", "/orders/{order_id}")
        ]
        
        actual_endpoints = []
        for route in orders_router.routes:
            for method in route.methods:
                actual_endpoints.append((method, route.path))
        
        for expected_method, expected_path in expected_endpoints:
            assert (expected_method, expected_path) in actual_endpoints, f"Missing endpoint: {expected_method} {expected_path}"

    def test_api_response_error_handling(self):
        """测试API响应错误处理"""
        # 测试不同类型的错误响应
        validation_error = APIResponse.error_response(
            message="验证失败", 
            error_code="VALIDATION_ERROR"
        )
        
        auth_error = APIResponse.error_response(
            message="认证失败", 
            error_code="AUTH_ERROR"
        )
        
        not_found_error = APIResponse.error_response(
            message="资源未找到", 
            error_code="NOT_FOUND"
        )
        
        # 验证所有错误响应格式一致
        for error_response in [validation_error, auth_error, not_found_error]:
            assert error_response.success is False
            assert error_response.data is None
            assert error_response.message is not None
            assert error_response.error_code is not None

    def test_api_response_consistency(self):
        """测试API响应一致性"""
        # 测试不同数据类型的响应格式一致性
        test_cases = [
            ({"simple": "data"}, "简单数据"),
            ([1, 2, 3], "列表数据"),
            ("string_data", "字符串数据"),
            (123, "数字数据"),
            (None, "空数据")
        ]

        for data, message in test_cases:
            response = APIResponse.success_response(data=data, message=message)

            # 验证响应结构一致
            assert hasattr(response, 'success')
            assert hasattr(response, 'message')
            assert hasattr(response, 'data')
            assert hasattr(response, 'error_code')

            # 验证成功响应的值
            assert response.success is True
            assert response.message == message
            assert response.data == data
            assert response.error_code is None

    def test_api_response_datetime_serialization(self):
        """测试API响应中datetime对象的序列化"""
        from datetime import datetime

        test_data = {
            "created_at": datetime(2025, 1, 1, 12, 0, 0),
            "updated_at": None,
            "name": "test"
        }

        response = APIResponse.success_response(data=test_data, message="测试datetime序列化")

        # 验证响应可以正确序列化
        serialized = response.model_dump()

        assert serialized["success"] is True
        assert serialized["message"] == "测试datetime序列化"
        assert serialized["data"]["name"] == "test"
        assert serialized["data"]["updated_at"] is None
        # datetime应该被序列化为ISO格式字符串
        assert isinstance(serialized["data"]["created_at"], datetime)
