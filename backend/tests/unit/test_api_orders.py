"""
订单API单元测试
测试 app/api/v1/orders.py 中的所有端点
目标：将覆盖率从0%提升到80%+
"""

import pytest
from unittest.mock import AsyncMock, Mock, patch
from fastapi.testclient import TestClient
from fastapi import status
from sqlalchemy.ext.asyncio import AsyncSession
import uuid
from decimal import Decimal
from datetime import datetime, timezone

from app.main import app
from app.core.models import User, Order
from app.core.schemas import OrderStatus
from app.core.auth import get_current_user


class TestOrdersAPI:
    """测试订单API端点"""

    def setup_method(self):
        """设置测试环境"""
        self.client = TestClient(app)
        self.mock_user = Mock(spec=User)
        self.mock_user.id = uuid.uuid4()
        self.mock_user.username = "testuser"

        self.sample_order = Mock(spec=Order)
        self.sample_order.id = uuid.uuid4()
        self.sample_order.user_id = self.mock_user.id
        self.sample_order.client_order_id = "order_123456"
        self.sample_order.exchange_order_id = None
        self.sample_order.source_message_id = None
        self.sample_order.symbol = "BTC/USDT"
        self.sample_order.side = "buy"
        self.sample_order.quantity = Decimal("0.1")
        self.sample_order.entry_price = Decimal("50000")
        self.sample_order.close_price = None
        self.sample_order.pnl = None
        self.sample_order.status = "active"
        self.sample_order.created_at = datetime.now(timezone.utc)
        self.sample_order.closed_at = None
        self.sample_order.agent_log = {"created_by": "api"}

        # Mock认证依赖，避免JWT验证
        # 使用app.dependency_overrides来覆盖依赖
        from app.core.auth import get_current_user
        from app.core.database import get_db
        app.dependency_overrides[get_current_user] = lambda: self.mock_user

        # Mock数据库会话以避免真实数据库操作
        self.mock_db = AsyncMock()
        app.dependency_overrides[get_db] = lambda: self.mock_db

    def teardown_method(self):
        """清理测试环境"""
        # 清理依赖覆盖
        app.dependency_overrides.clear()

    def test_get_orders_success(self):
        """测试获取订单列表成功"""

        # Mock查询结果
        mock_result = Mock()
        mock_result.scalars.return_value.all.return_value = [self.sample_order]

        # Mock计数查询结果
        mock_count_result = Mock()
        mock_count_result.scalars.return_value.all.return_value = [self.sample_order]  # 返回一个订单列表用于计数

        # 设置execute的返回值序列：第一次返回订单查询结果，第二次返回计数结果
        self.mock_db.execute.side_effect = [mock_result, mock_count_result]

        response = self.client.get("/api/v1/orders")

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["success"] is True
        assert "orders" in data["data"]
        assert "total" in data["data"]

    def test_get_orders_with_filters(self):
        """测试带过滤条件获取订单列表"""

        # Mock查询结果
        mock_result = Mock()
        mock_result.scalars.return_value.all.return_value = [self.sample_order]

        # Mock计数查询
        mock_count_result = Mock()
        mock_count_result.scalars.return_value.all.return_value = [self.sample_order]  # 返回一个订单列表用于计数

        # 设置execute的返回值序列：第一次返回订单查询结果，第二次返回计数结果
        self.mock_db.execute.side_effect = [mock_result, mock_count_result]
        
        params = {
            "symbol": "BTC/USDT",
            "status": "active",
            "limit": 50,
            "offset": 0
        }
        response = self.client.get("/api/v1/orders", params=params)
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["success"] is True

    def test_get_orders_empty_result(self):
        """测试获取空订单列表"""

        # Mock空查询结果
        mock_result = Mock()
        mock_result.scalars.return_value.all.return_value = []

        # Mock计数查询
        mock_count_result = Mock()
        mock_count_result.scalars.return_value.all.return_value = []  # 返回空列表用于计数

        # 设置execute的返回值序列：第一次返回空订单查询结果，第二次返回计数结果
        self.mock_db.execute.side_effect = [mock_result, mock_count_result]
        
        response = self.client.get("/api/v1/orders")
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["success"] is True
        assert data["data"]["total"] == 0
        assert len(data["data"]["orders"]) == 0

    def test_get_orders_unauthorized(self):
        """测试未认证访问订单列表"""
        # 临时清除认证依赖覆盖来测试真实的认证行为
        app.dependency_overrides.clear()
        try:
            response = self.client.get("/api/v1/orders")
            assert response.status_code == status.HTTP_403_FORBIDDEN
        finally:
            # 重新设置认证Mock
            from app.core.auth import get_current_user
            app.dependency_overrides[get_current_user] = lambda: self.mock_user

    def test_get_order_by_id_success(self):
        """测试根据ID获取订单成功"""

        # Mock查询结果
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = self.sample_order
        self.mock_db.execute.return_value = mock_result
        
        order_id = str(self.sample_order.id)
        response = self.client.get(f"/api/v1/orders/{order_id}")
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["success"] is True
        assert "id" in data["data"]

    def test_get_order_by_id_not_found(self):
        """测试获取不存在的订单"""

        # Mock空查询结果
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = None
        self.mock_db.execute.return_value = mock_result
        
        order_id = str(uuid.uuid4())
        response = self.client.get(f"/api/v1/orders/{order_id}")
        
        assert response.status_code == status.HTTP_404_NOT_FOUND

    def test_get_order_by_id_invalid_uuid(self):
        """测试使用无效UUID获取订单"""
        response = self.client.get("/api/v1/orders/invalid-uuid")

        assert response.status_code == status.HTTP_400_BAD_REQUEST

    @patch('app.services.dynamic_risk_manager.DynamicRiskManager')
    def test_create_order_success(self, mock_risk_manager_class):
        """测试创建订单成功"""

        # Mock风控管理器
        mock_risk_manager = AsyncMock()
        mock_risk_manager.get_user_risk_config.return_value = {
            "max_open_positions": 10,
            "max_position_size_usd": 10000
        }
        mock_risk_manager_class.return_value = mock_risk_manager

        # Mock活跃订单计数查询
        self.mock_db.scalar.return_value = 5  # 少于限制

        # Mock客户端订单ID检查（不存在）
        mock_check_result = Mock()
        mock_check_result.scalar_one_or_none.return_value = None
        self.mock_db.execute.return_value = mock_check_result

        # Mock数据库操作
        self.mock_db.add = Mock()
        self.mock_db.flush = AsyncMock()
        self.mock_db.commit = AsyncMock()
        self.mock_db.refresh = AsyncMock()

        # 设置refresh后的订单属性
        def mock_refresh_side_effect(order):
            order.id = uuid.uuid4()
            order.created_at = datetime.now(timezone.utc)
            order.updated_at = datetime.now(timezone.utc)

        self.mock_db.refresh.side_effect = mock_refresh_side_effect
        
        order_data = {
            "symbol": "BTC/USDT",
            "side": "buy",
            "quantity": "0.1",
            "order_type": "market"
        }
        
        headers = {"Authorization": "Bearer valid_token"}
        response = self.client.post("/api/v1/orders", json=order_data, headers=headers)
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["success"] is True
        assert "订单创建成功" in data["message"]

    @patch('app.services.dynamic_risk_manager.DynamicRiskManager')
    def test_create_order_too_many_active(self, mock_risk_manager_class):
        """测试活跃订单数量超限"""

        # Mock风控管理器
        mock_risk_manager = AsyncMock()
        mock_risk_manager.get_user_risk_config.return_value = {
            "max_open_positions": 5,
            "max_position_size_usd": 10000
        }
        mock_risk_manager_class.return_value = mock_risk_manager

        # Mock活跃订单计数查询（超过限制）
        self.mock_db.scalar.return_value = 10  # 超过限制
        
        order_data = {
            "symbol": "BTC/USDT",
            "side": "buy",
            "quantity": "0.1",
            "order_type": "market"
        }
        
        headers = {"Authorization": "Bearer valid_token"}
        response = self.client.post("/api/v1/orders", json=order_data, headers=headers)
        
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        data = response.json()
        # 自定义错误响应格式是{"success": false, "error": {"code": "...", "message": "..."}}
        assert "超过最大并发订单数量限制" in data["error"]["message"]

    def test_create_order_invalid_data(self):
        """测试创建订单时提供无效数据"""
        invalid_data = {
            "symbol": "",  # 空交易对
            "side": "invalid",  # 无效方向
            "quantity": "-1"  # 负数量
        }
        
        headers = {"Authorization": "Bearer valid_token"}
        response = self.client.post("/api/v1/orders", json=invalid_data, headers=headers)
        
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

    def test_create_order_unauthorized(self):
        """测试未认证创建订单"""
        # 临时清除认证依赖覆盖来测试真实的认证行为
        app.dependency_overrides.clear()

        try:
            order_data = {
                "symbol": "BTC/USDT",
                "side": "buy",
                "quantity": "0.1"
            }

            response = self.client.post("/api/v1/orders", json=order_data)
            assert response.status_code == status.HTTP_403_FORBIDDEN
        finally:
            # 重新设置认证Mock
            from app.core.auth import get_current_user
            from app.core.database import get_db
            app.dependency_overrides[get_current_user] = lambda: self.mock_user
            app.dependency_overrides[get_db] = lambda: self.mock_db

    @patch('app.api.v1.orders.get_db')
    def test_create_order_database_error(self, mock_get_db):
        """测试创建订单时数据库错误"""
        
        # Mock数据库会话
        mock_db = AsyncMock(spec=AsyncSession)
        mock_get_db.return_value = mock_db
        
        # Mock数据库异常
        mock_db.execute.side_effect = Exception("Database error")
        
        order_data = {
            "symbol": "BTC/USDT",
            "side": "buy",
            "quantity": "0.1"
        }
        
        headers = {"Authorization": "Bearer valid_token"}
        response = self.client.post("/api/v1/orders", json=order_data, headers=headers)
        
        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR


class TestOrdersValidation:
    """测试订单相关的数据验证"""

    def setup_method(self):
        """设置测试环境"""
        self.client = TestClient(app)

        # 创建Mock用户
        self.mock_user = Mock(spec=User)
        self.mock_user.id = uuid.uuid4()
        self.mock_user.username = "testuser"

        # 使用app.dependency_overrides来覆盖依赖
        from app.core.auth import get_current_user
        from app.core.database import get_db
        app.dependency_overrides[get_current_user] = lambda: self.mock_user

        # Mock数据库会话以避免真实数据库操作
        self.mock_db = AsyncMock()
        app.dependency_overrides[get_db] = lambda: self.mock_db

    def teardown_method(self):
        """清理测试环境"""
        # 清理依赖覆盖
        app.dependency_overrides.clear()

    def test_get_orders_invalid_pagination(self):
        """测试无效分页参数"""
        # 负数限制
        response = self.client.get("/api/v1/orders?limit=-1")
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        
        # 超大限制
        response = self.client.get("/api/v1/orders?limit=2000")
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        
        # 负数偏移
        response = self.client.get("/api/v1/orders?offset=-1")
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

    def test_create_order_missing_required_fields(self):
        """测试创建订单缺少必填字段"""

        # 缺少交易对
        response = self.client.post("/api/v1/orders", json={
            "side": "buy",
            "quantity": "0.1"
        })
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        
        # 缺少方向
        response = self.client.post("/api/v1/orders", json={
            "symbol": "BTC/USDT",
            "quantity": "0.1"
        })
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        
        # 缺少数量
        response = self.client.post("/api/v1/orders", json={
            "symbol": "BTC/USDT",
            "side": "buy"
        })
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

    def test_create_order_invalid_side(self):
        """测试无效的订单方向"""

        response = self.client.post("/api/v1/orders", json={
            "symbol": "BTC/USDT",
            "side": "invalid_side",
            "quantity": "0.1"
        })
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

    def test_create_order_invalid_quantity(self):
        """测试无效的订单数量"""

        # 零数量
        response = self.client.post("/api/v1/orders", json={
            "symbol": "BTC/USDT",
            "side": "buy",
            "quantity": "0"
        })
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        
        # 负数量
        response = self.client.post("/api/v1/orders", json={
            "symbol": "BTC/USDT",
            "side": "buy",
            "quantity": "-0.1"
        })
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
