"""
测试动作API端点
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from fastapi import HTT<PERSON>Exception
from uuid import uuid4
from datetime import datetime, timezone

from app.api.v1.actions import router
from app.core.models import PendingAction, User
from app.core.schemas import AgentState, APIResponse


class TestActionsAPI:
    """测试动作API"""

    @pytest.fixture
    def mock_user(self):
        """创建模拟用户"""
        user = MagicMock(spec=User)
        user.id = 1
        user.username = "testuser"
        return user

    @pytest.fixture
    def mock_pending_action(self):
        """创建模拟待处理动作"""
        action = MagicMock(spec=PendingAction)
        action.id = uuid4()
        action.user_id = 1
        action.action_type = "order_confirmation"
        action.status = "pending"
        action.details = {"symbol": "BTCUSDT", "quantity": 0.1}
        action.created_at = datetime.now(timezone.utc)
        action.updated_at = datetime.now(timezone.utc)
        return action

    @pytest.fixture
    def mock_db_session(self):
        """创建模拟数据库会话"""
        db = AsyncMock()
        return db

    @pytest.mark.asyncio
    async def test_get_pending_actions_success(self, mock_user, mock_pending_action, mock_db_session):
        """测试成功获取待处理动作列表"""
        # 设置模拟查询结果
        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = [mock_pending_action]
        mock_db_session.execute.return_value = mock_result
        
        from app.api.v1.actions import get_pending_actions
        
        # 执行测试
        result = await get_pending_actions(
            current_user=mock_user,
            status_filter=None,
            limit=50,
            offset=0,
            db=mock_db_session
        )
        
        # 验证结果
        assert result.success is True
        assert len(result.data) == 1
        
        # 验证数据库查询被调用
        mock_db_session.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_pending_actions_with_status_filter(self, mock_user, mock_pending_action, mock_db_session):
        """测试带状态过滤的获取待处理动作"""
        # 设置模拟查询结果
        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = [mock_pending_action]
        mock_db_session.execute.return_value = mock_result
        
        from app.api.v1.actions import get_pending_actions
        
        # 执行测试
        result = await get_pending_actions(
            current_user=mock_user,
            status_filter="pending",
            limit=50,
            offset=0,
            db=mock_db_session
        )
        
        # 验证结果
        assert result.success is True
        assert len(result.data) == 1

    @pytest.mark.asyncio
    async def test_get_pending_actions_with_pagination(self, mock_user, mock_pending_action, mock_db_session):
        """测试带分页的获取待处理动作"""
        # 设置模拟查询结果
        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = [mock_pending_action]
        mock_db_session.execute.return_value = mock_result
        
        from app.api.v1.actions import get_pending_actions
        
        # 执行测试
        result = await get_pending_actions(
            current_user=mock_user,
            status_filter=None,
            limit=10,
            offset=20,
            db=mock_db_session
        )
        
        # 验证结果
        assert result.success is True
        assert len(result.data) == 1

    @pytest.mark.asyncio
    async def test_get_pending_actions_empty_result(self, mock_user, mock_db_session):
        """测试获取空的待处理动作列表"""
        # 设置模拟查询结果为空列表
        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = []
        mock_db_session.execute.return_value = mock_result
        
        from app.api.v1.actions import get_pending_actions
        
        # 执行测试
        result = await get_pending_actions(
            current_user=mock_user,
            status_filter=None,
            limit=50,
            offset=0,
            db=mock_db_session
        )
        
        # 验证结果
        assert result.success is True
        assert len(result.data) == 0
        assert result.data == []

    @pytest.mark.asyncio
    async def test_get_pending_actions_database_error(self, mock_user, mock_db_session):
        """测试数据库错误处理"""
        # 设置模拟数据库抛出异常
        mock_db_session.execute.side_effect = Exception("Database connection error")
        
        from app.api.v1.actions import get_pending_actions
        
        # 执行测试并验证异常
        with pytest.raises(HTTPException) as exc_info:
            await get_pending_actions(
                current_user=mock_user,
                status_filter=None,
                limit=50,
                offset=0,
                db=mock_db_session
            )
        
        assert exc_info.value.status_code == 500
        assert "获取待处理动作失败" in str(exc_info.value.detail)

    def test_pending_action_basic_structure(self, mock_pending_action):
        """测试待处理动作基本结构"""
        assert mock_pending_action.id is not None
        assert mock_pending_action.user_id == 1
        assert mock_pending_action.action_type == "order_confirmation"
        assert mock_pending_action.status == "pending"
        assert mock_pending_action.details == {"symbol": "BTCUSDT", "quantity": 0.1}


class TestActionsValidation:
    """测试动作验证"""

    def test_pending_action_data_structure(self):
        """测试待处理动作数据结构"""
        # 创建模拟动作数据
        action_data = {
            "id": str(uuid4()),
            "user_id": 1,
            "action_type": "order_confirmation",
            "status": "pending",
            "details": {"symbol": "BTCUSDT", "quantity": 0.1},
            "created_at": datetime.now(timezone.utc),
            "updated_at": datetime.now(timezone.utc)
        }
        
        # 验证数据结构
        assert action_data["action_type"] == "order_confirmation"
        assert action_data["status"] == "pending"
        assert action_data["details"]["symbol"] == "BTCUSDT"
        assert action_data["details"]["quantity"] == 0.1

    def test_api_response_structure(self):
        """测试API响应结构"""
        # 创建模拟响应
        response_data = [
            {
                "id": str(uuid4()),
                "action_type": "order_confirmation",
                "status": "pending",
                "details": {"symbol": "BTCUSDT"},
                "created_at": datetime.now(timezone.utc).isoformat(),
                "updated_at": datetime.now(timezone.utc).isoformat()
            }
        ]
        
        # 验证响应结构
        response = APIResponse(
            success=True,
            data=response_data,
            message="获取待处理动作成功"
        )
        
        assert response.success is True
        assert len(response.data) == 1
        assert response.data[0]["action_type"] == "order_confirmation"
        assert response.message == "获取待处理动作成功"


class TestActionsEdgeCases:
    """测试动作边界情况"""

    def test_get_pending_actions_parameters(self):
        """测试获取待处理动作的参数"""
        # 测试默认参数
        assert 50 >= 1  # 默认limit
        assert 0 >= 0   # 默认offset

        # 测试参数范围
        assert 100 <= 100  # 最大limit
        assert 1 >= 1      # 最小limit

    def test_action_status_transitions(self):
        """测试动作状态转换"""
        valid_statuses = ["pending", "approved", "rejected", "expired"]
        
        for status in valid_statuses:
            action_data = {
                "id": str(uuid4()),
                "user_id": 1,
                "action_type": "order_confirmation",
                "status": status,
                "details": {"symbol": "BTCUSDT"},
                "created_at": datetime.now(timezone.utc),
                "updated_at": datetime.now(timezone.utc)
            }
            
            assert action_data["status"] == status
