"""
系统API单元测试
测试 app/api/v1/system.py 中的所有端点
目标：将覆盖率从0%提升到80%+
"""

import pytest
from unittest.mock import AsyncMock, Mock, patch
from fastapi.testclient import TestClient
from fastapi import status
from sqlalchemy.ext.asyncio import AsyncSession
import time

from app.main import app


class TestSystemAPI:
    """测试系统API端点"""

    def setup_method(self):
        """设置测试环境"""
        self.client = TestClient(app)

    def test_health_check_success(self):
        """测试健康检查成功"""
        from app.core.database import get_db
        from app.main import app

        # 创建一个正常工作的Mock数据库会话
        async def mock_get_db_success():
            mock_db = AsyncMock(spec=AsyncSession)
            mock_result = Mock()
            mock_result.fetchone.return_value = (1,)
            mock_db.execute.return_value = mock_result
            yield mock_db

        # 覆盖依赖
        app.dependency_overrides[get_db] = mock_get_db_success

        try:
            response = self.client.get("/api/v1/health")

            assert response.status_code == status.HTTP_200_OK
            data = response.json()

            # 验证标准化API响应格式
            assert data["success"] is True
            assert "data" in data

            # 验证健康检查数据
            health_data = data["data"]
            assert health_data["status"] == "healthy"
            assert "timestamp" in health_data
            assert "checks" in health_data
        finally:
            # 清理依赖覆盖
            app.dependency_overrides.clear()

    def test_health_check_with_database(self):
        """测试包含数据库检查的健康检查"""
        from app.core.database import get_db
        from app.main import app

        # 创建一个正常工作的Mock数据库会话
        async def mock_get_db_with_database():
            mock_db = AsyncMock(spec=AsyncSession)
            mock_db.execute.return_value = None  # execute成功返回None
            yield mock_db

        # 覆盖依赖
        app.dependency_overrides[get_db] = mock_get_db_with_database

        try:
            response = self.client.get("/api/v1/health")

            assert response.status_code == status.HTTP_200_OK
            data = response.json()

            # 验证标准化API响应格式
            assert data["success"] is True
            health_data = data["data"]
            assert health_data["status"] == "healthy"
            assert "checks" in health_data
            assert "database" in health_data["checks"]
        finally:
            # 清理依赖覆盖
            app.dependency_overrides.clear()

    def test_health_check_database_error(self):
        """测试数据库连接错误时的健康检查"""
        from app.core.database import get_db
        from app.main import app

        # 创建一个会抛出异常的Mock数据库会话
        async def mock_get_db_error():
            mock_db = AsyncMock(spec=AsyncSession)
            mock_db.execute.side_effect = Exception("Database connection failed")
            yield mock_db

        # 覆盖依赖
        app.dependency_overrides[get_db] = mock_get_db_error

        try:
            response = self.client.get("/api/v1/health")

            # 健康检查即使数据库失败也返回200，但状态为unhealthy
            assert response.status_code == status.HTTP_200_OK
            data = response.json()

            # 验证标准化API响应格式
            assert data["success"] is True
            health_data = data["data"]
            assert health_data["status"] == "unhealthy"
            assert "checks" in health_data
            assert "unhealthy" in health_data["checks"]["database"]
        finally:
            # 清理依赖覆盖
            app.dependency_overrides.clear()

    def test_version_info(self):
        """测试版本信息获取"""
        response = self.client.get("/api/v1/version")

        assert response.status_code == status.HTTP_200_OK
        data = response.json()

        # 验证标准化API响应格式
        assert data["success"] is True
        assert "data" in data

        # 验证版本信息数据
        version_data = data["data"]
        assert "version" in version_data
        assert "build_date" in version_data
        assert "environment" in version_data
        # git_commit字段存在但可能为None
        assert "git_commit" in version_data

    @patch('app.api.v1.system.psutil.cpu_percent')
    @patch('app.api.v1.system.psutil.virtual_memory')
    @patch('app.api.v1.system.psutil.disk_usage')
    @patch('app.api.v1.system.ws_manager')
    def test_metrics_success(self, mock_ws_manager, mock_disk_usage, mock_virtual_memory, mock_cpu_percent):
        """测试系统指标获取成功"""
        # Mock系统指标
        mock_cpu_percent.return_value = 45.5

        mock_memory = Mock()
        mock_memory.total = 8 * 1024 * 1024 * 1024  # 8GB
        mock_memory.available = 4 * 1024 * 1024 * 1024  # 4GB
        mock_memory.percent = 50.0
        mock_virtual_memory.return_value = mock_memory

        mock_disk = Mock()
        mock_disk.total = 100 * 1024 * 1024 * 1024  # 100GB
        mock_disk.free = 60 * 1024 * 1024 * 1024  # 60GB
        mock_disk.percent = 40.0
        mock_disk_usage.return_value = mock_disk

        # Mock WebSocket manager
        mock_ws_manager.connection_map = {}

        response = self.client.get("/api/v1/metrics")

        assert response.status_code == status.HTTP_200_OK
        data = response.json()

        # 验证标准化API响应格式
        assert data["success"] is True
        assert "data" in data

        # 验证指标数据
        metrics_data = data["data"]
        assert "cpu_usage" in metrics_data
        assert "memory_usage" in metrics_data
        assert "disk_usage" in metrics_data
        assert metrics_data["cpu_usage"] == 45.5
        assert metrics_data["memory_usage"] == 50.0
        assert metrics_data["disk_usage"] == 40.0

    @patch('app.api.v1.system.psutil.cpu_percent')
    def test_metrics_cpu_error(self, mock_cpu_percent):
        """测试CPU指标获取错误"""
        # Mock CPU指标获取失败
        mock_cpu_percent.side_effect = Exception("CPU metrics unavailable")
        
        response = self.client.get("/api/v1/metrics")
        
        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR

    @patch('app.api.v1.system.ws_manager')
    def test_websocket_status_success(self, mock_ws_manager):
        """测试WebSocket状态获取成功（通过status端点）"""
        # Mock WebSocket管理器状态
        mock_ws_manager.connection_map = {"conn1": {}, "conn2": {}, "conn3": {}, "conn4": {}, "conn5": {}}

        response = self.client.get("/api/v1/status")

        assert response.status_code == status.HTTP_200_OK
        data = response.json()

        # 验证标准化API响应格式
        assert data["success"] is True
        status_data = data["data"]
        assert "connections" in status_data
        assert status_data["connections"]["websocket"] == 5

    @patch('app.api.v1.system.ws_manager')
    def test_websocket_status_unhealthy(self, mock_ws_manager):
        """测试WebSocket状态不健康（通过status端点）"""
        # Mock WebSocket管理器状态
        mock_ws_manager.connection_map = {}

        response = self.client.get("/api/v1/status")

        assert response.status_code == status.HTTP_200_OK
        data = response.json()

        # 验证标准化API响应格式
        assert data["success"] is True
        status_data = data["data"]
        assert "connections" in status_data
        assert status_data["connections"]["websocket"] == 0

    @patch('app.api.v1.system.discord_manager')
    def test_discord_status_connected(self, mock_discord_manager):
        """测试Discord状态连接正常"""
        # Mock Discord管理器状态
        mock_discord_manager.get_status.return_value = {
            "is_running": True,
            "connected": True,
            "guild_count": 1,
            "user_count": 100
        }

        response = self.client.get("/api/v1/discord/status")

        assert response.status_code == status.HTTP_200_OK
        data = response.json()

        # 验证标准化API响应格式
        assert data["success"] is True
        assert "data" in data

        # 验证Discord状态数据
        discord_data = data["data"]
        assert discord_data["is_running"] is True
        assert "connected" in discord_data
        assert discord_data["connected"] is True

    @patch('app.api.v1.system.discord_manager')
    def test_discord_status_disconnected(self, mock_discord_manager):
        """测试Discord状态断开连接"""
        # Mock Discord管理器状态
        mock_discord_manager.get_status.return_value = {
            "is_running": False,
            "connected": False,
            "error": "Connection failed"
        }

        response = self.client.get("/api/v1/discord/status")

        assert response.status_code == status.HTTP_200_OK
        data = response.json()

        # 验证标准化API响应格式
        assert data["success"] is True
        assert "data" in data

        # 验证Discord状态数据
        discord_data = data["data"]
        assert discord_data["is_running"] is False
        assert "connected" in discord_data
        assert discord_data["connected"] is False

    @patch('app.api.v1.system.discord_manager')
    def test_discord_status_error(self, mock_discord_manager):
        """测试Discord状态获取错误"""
        # Mock Discord管理器异常
        mock_discord_manager.get_status.side_effect = Exception("Discord service error")

        response = self.client.get("/api/v1/discord/status")

        # Discord API即使出错也返回200，在数据中包含错误信息
        assert response.status_code == status.HTTP_200_OK
        data = response.json()

        # 验证标准化API响应格式
        assert data["success"] is True
        assert "data" in data

        # 验证错误信息
        discord_data = data["data"]
        assert discord_data["is_running"] is False
        assert "error" in discord_data
        assert "Discord service error" in discord_data["error"]

    def test_system_info(self):
        """测试系统状态获取"""
        response = self.client.get("/api/v1/status")

        assert response.status_code == status.HTTP_200_OK
        data = response.json()

        # 验证标准化API响应格式
        assert data["success"] is True
        assert "data" in data

        # 验证系统状态数据
        status_data = data["data"]
        assert "uptime" in status_data
        assert "environment" in status_data
        assert "debug_mode" in status_data

    @patch('app.api.v1.system.get_settings')
    def test_config_info(self, mock_get_settings):
        """测试配置信息获取"""
        # Mock设置
        mock_settings = Mock()
        mock_settings.app_name = "Crypto Trader"
        mock_settings.environment = "test"
        mock_settings.debug = True
        mock_get_settings.return_value = mock_settings

        response = self.client.get("/api/v1/status")

        assert response.status_code == status.HTTP_200_OK
        data = response.json()

        # 验证标准化API响应格式
        assert data["success"] is True
        status_data = data["data"]
        assert "environment" in status_data
        assert "debug_mode" in status_data
        assert status_data["environment"] == "test"
        assert status_data["debug_mode"] is True

    def test_ping(self):
        """测试Discord状态端点（替代ping）"""
        response = self.client.get("/api/v1/discord/status")

        assert response.status_code == status.HTTP_200_OK
        data = response.json()

        # 验证标准化API响应格式
        assert data["success"] is True
        assert "data" in data

        # 验证Discord状态数据
        discord_data = data["data"]
        assert "is_running" in discord_data
        assert "task_done" in discord_data

    @patch('app.api.v1.system.time.time')
    def test_uptime_calculation(self, mock_time):
        """测试运行时间计算"""
        # Mock当前时间
        mock_time.return_value = 1000.0

        # 假设系统启动时间是500.0
        with patch('app.api.v1.system._start_time', 500.0):
            response = self.client.get("/api/v1/status")

            assert response.status_code == status.HTTP_200_OK
            data = response.json()

            # 验证标准化API响应格式
            assert data["success"] is True
            status_data = data["data"]
            assert "uptime" in status_data
            assert status_data["uptime"] == 500  # int(1000.0 - 500.0)

    def test_health_check_response_format(self):
        """测试健康检查响应格式"""
        response = self.client.get("/api/v1/health")

        assert response.status_code == status.HTTP_200_OK
        data = response.json()

        # 验证标准化API响应格式
        assert data["success"] is True
        assert "data" in data

        # 验证健康检查数据格式
        health_data = data["data"]
        required_fields = ["status", "timestamp", "checks"]
        for field in required_fields:
            assert field in health_data

        # 验证数据类型
        assert isinstance(health_data["status"], str)
        assert isinstance(health_data["timestamp"], str)
        assert isinstance(health_data["checks"], dict)

    def test_metrics_response_format(self):
        """测试指标响应格式"""
        with patch('app.api.v1.system.psutil.cpu_percent', return_value=50.0), \
             patch('app.api.v1.system.psutil.virtual_memory') as mock_memory, \
             patch('app.api.v1.system.psutil.disk_usage') as mock_disk, \
             patch('app.api.v1.system.ws_manager') as mock_ws_manager:

            # Mock内存信息
            mock_memory.return_value = Mock(
                total=**********,  # 8GB
                available=**********,  # 4GB
                percent=50.0
            )

            # Mock磁盘信息
            mock_disk.return_value = Mock(
                total=107374182400,  # 100GB
                free=64424509440,  # 60GB
                percent=40.0
            )

            # Mock WebSocket manager
            mock_ws_manager.connection_map = {}

            response = self.client.get("/api/v1/metrics")

            assert response.status_code == status.HTTP_200_OK
            data = response.json()

            # 验证标准化API响应格式
            assert data["success"] is True
            assert "data" in data

            # 验证指标数据格式
            metrics_data = data["data"]
            required_fields = ["cpu_usage", "memory_usage", "disk_usage", "active_connections", "running_tasks"]
            for field in required_fields:
                assert field in metrics_data
                assert isinstance(metrics_data[field], (int, float))

            # 验证时间戳字段
            assert "timestamp" in metrics_data

    def test_version_response_format(self):
        """测试版本信息响应格式"""
        response = self.client.get("/api/v1/version")

        assert response.status_code == status.HTTP_200_OK
        data = response.json()

        # 验证标准化API响应格式
        assert data["success"] is True
        assert "data" in data

        # 验证版本信息数据格式
        version_data = data["data"]
        required_fields = ["version", "build_date", "environment"]
        for field in required_fields:
            assert field in version_data
            assert isinstance(version_data[field], str)

    def test_database_health_check_timeout(self):
        """测试数据库健康检查超时"""
        import asyncio
        from app.core.database import get_db

        # 创建一个异步生成器来模拟get_db的行为
        async def mock_db_generator():
            mock_db = AsyncMock(spec=AsyncSession)
            # Mock数据库查询超时
            mock_db.execute.side_effect = asyncio.TimeoutError("Database query timeout")
            yield mock_db

        # 使用FastAPI的dependency_overrides来替换依赖
        app.dependency_overrides[get_db] = mock_db_generator

        try:
            response = self.client.get("/api/v1/health")

            # 健康检查即使数据库超时也返回200，但状态为unhealthy
            assert response.status_code == status.HTTP_200_OK
            data = response.json()

            # 验证标准化API响应格式
            assert data["success"] is True
            health_data = data["data"]
            assert health_data["status"] == "unhealthy"
            assert "checks" in health_data
            assert "unhealthy" in health_data["checks"]["database"]
            # 检查错误信息包含超时相关的关键词
            error_msg = health_data["checks"]["database"].lower()
            assert any(keyword in error_msg for keyword in ["timeout", "timeouterror", "operation", "progress"])
        finally:
            # 清理dependency_overrides
            app.dependency_overrides.clear()
