"""
测试AsyncIO任务清理功能
"""
import asyncio
import pytest
from unittest.mock import AsyncMock, MagicMock, patch

from app.core.ws_manager import WebSocketManager
from app.services.conditional_order_service import ConditionalOrderService
from app.core.models import User, ConditionalOrder


class TestAsyncIOCleanup:
    """测试AsyncIO任务清理"""

    @pytest.mark.asyncio
    async def test_websocket_manager_shutdown(self):
        """测试WebSocket管理器关闭时的任务清理"""
        ws_manager = WebSocketManager()
        
        # 模拟启动清理任务
        ws_manager.cleanup_task = asyncio.create_task(asyncio.sleep(10))
        
        # 模拟添加连接
        mock_websocket = AsyncMock()
        mock_user = MagicMock()
        mock_user.id = 1
        
        # 创建连接信息并添加心跳任务
        from app.core.ws_manager import ConnectionInfo
        connection_info = ConnectionInfo(mock_websocket, mock_user)
        connection_info.heartbeat_task = asyncio.create_task(asyncio.sleep(10))
        
        ws_manager.connection_map[connection_info.connection_id] = connection_info
        ws_manager.active_connections[1] = [connection_info]
        
        # 执行关闭
        await ws_manager.shutdown()
        
        # 验证任务被取消
        assert ws_manager.cleanup_task is None
        assert connection_info.heartbeat_task.cancelled()
        
        # 验证数据结构被清理
        assert len(ws_manager.connection_map) == 0
        assert len(ws_manager.active_connections) == 0
        assert len(ws_manager.disconnected_users) == 0
        assert len(ws_manager.offline_messages) == 0

    @pytest.mark.asyncio
    async def test_conditional_order_service_shutdown(self):
        """测试条件订单服务关闭时的任务清理"""
        mock_db = AsyncMock()
        service = ConditionalOrderService(mock_db)
        
        # 模拟添加监控任务
        task1 = asyncio.create_task(asyncio.sleep(10))
        task2 = asyncio.create_task(asyncio.sleep(10))
        
        from uuid import uuid4
        order_id1 = uuid4()
        order_id2 = uuid4()
        
        service._monitoring_tasks[order_id1] = task1
        service._monitoring_tasks[order_id2] = task2
        
        # 执行关闭
        await service.shutdown()
        
        # 验证任务被取消
        assert task1.cancelled()
        assert task2.cancelled()
        
        # 验证任务字典被清理
        assert len(service._monitoring_tasks) == 0

    @pytest.mark.asyncio
    async def test_websocket_manager_shutdown_with_errors(self):
        """测试WebSocket管理器关闭时处理错误"""
        ws_manager = WebSocketManager()
        
        # 模拟有问题的WebSocket连接
        mock_websocket = AsyncMock()
        mock_websocket.close.side_effect = Exception("Connection error")
        
        mock_user = MagicMock()
        mock_user.id = 1
        
        from app.core.ws_manager import ConnectionInfo
        connection_info = ConnectionInfo(mock_websocket, mock_user)
        connection_info.heartbeat_task = asyncio.create_task(asyncio.sleep(10))
        
        ws_manager.connection_map[connection_info.connection_id] = connection_info
        
        # 执行关闭（应该不抛出异常）
        await ws_manager.shutdown()
        
        # 验证即使有错误，清理仍然完成
        assert len(ws_manager.connection_map) == 0

    @pytest.mark.asyncio
    async def test_conditional_order_service_shutdown_with_errors(self):
        """测试条件订单服务关闭时处理错误"""
        mock_db = AsyncMock()
        service = ConditionalOrderService(mock_db)
        
        # 创建一个会抛出异常的任务
        async def failing_task():
            await asyncio.sleep(0.1)
            raise Exception("Task error")
        
        task = asyncio.create_task(failing_task())
        
        from uuid import uuid4
        order_id = uuid4()
        service._monitoring_tasks[order_id] = task
        
        # 等待任务开始
        await asyncio.sleep(0.05)
        
        # 执行关闭（应该不抛出异常）
        await service.shutdown()
        
        # 验证任务字典被清理
        assert len(service._monitoring_tasks) == 0

    @pytest.mark.asyncio
    async def test_websocket_manager_multiple_connections_cleanup(self):
        """测试WebSocket管理器清理多个连接"""
        ws_manager = WebSocketManager()
        
        # 创建多个连接
        connections = []
        for i in range(3):
            mock_websocket = AsyncMock()
            mock_user = MagicMock()
            mock_user.id = i + 1
            
            from app.core.ws_manager import ConnectionInfo
            connection_info = ConnectionInfo(mock_websocket, mock_user)
            connection_info.heartbeat_task = asyncio.create_task(asyncio.sleep(10))
            
            ws_manager.connection_map[connection_info.connection_id] = connection_info
            if mock_user.id not in ws_manager.active_connections:
                ws_manager.active_connections[mock_user.id] = []
            ws_manager.active_connections[mock_user.id].append(connection_info)
            
            connections.append(connection_info)
        
        # 执行关闭
        await ws_manager.shutdown()
        
        # 验证所有连接的心跳任务被取消
        for connection_info in connections:
            assert connection_info.heartbeat_task.cancelled()
        
        # 验证所有数据结构被清理
        assert len(ws_manager.connection_map) == 0
        assert len(ws_manager.active_connections) == 0

    @pytest.mark.asyncio
    async def test_websocket_manager_no_cleanup_task(self):
        """测试WebSocket管理器在没有清理任务时的关闭"""
        ws_manager = WebSocketManager()
        
        # 确保没有清理任务
        assert ws_manager.cleanup_task is None
        
        # 执行关闭（应该不抛出异常）
        await ws_manager.shutdown()
        
        # 验证状态正常
        assert ws_manager.cleanup_task is None
        assert len(ws_manager.connection_map) == 0

    @pytest.mark.asyncio
    async def test_conditional_order_service_empty_tasks(self):
        """测试条件订单服务在没有监控任务时的关闭"""
        mock_db = AsyncMock()
        service = ConditionalOrderService(mock_db)
        
        # 确保没有监控任务
        assert len(service._monitoring_tasks) == 0
        
        # 执行关闭（应该不抛出异常）
        await service.shutdown()
        
        # 验证状态正常
        assert len(service._monitoring_tasks) == 0
