"""
Pydantic模型属性测试 - 使用Hypothesis进行属性测试

属性测试能够自动生成大量有效、边界和异常的数据来验证模型的解析、验证和序列化逻辑，
能够发现手写单元测试难以覆盖的边缘案例。
"""
import json
import uuid
from datetime import datetime, timezone
from decimal import Decimal, InvalidOperation
from typing import Any, Dict, List, Optional

import pytest
from hypothesis import assume, given, settings
from hypothesis import strategies as st
from hypothesis.strategies import composite
from pydantic import ValidationError

from app.core.schemas import (ActionType, AgentState, ErrorAnalysis,
                              IntentType, OrderStatus, OrderType, ParsedIntent,
                              TradePlan, TradeResult, TradeSide)

# ============================================================================
# Hypothesis策略定义
# ============================================================================

# 基础类型策略
decimal_strategy = st.decimals(
    min_value=Decimal("0.00000001"),
    max_value=Decimal("999999999.99999999"),
    allow_nan=False,
    allow_infinity=False,
)

positive_decimal_strategy = st.decimals(
    min_value=Decimal("0.00000001"),
    max_value=Decimal("999999999.99999999"),
    allow_nan=False,
    allow_infinity=False,
)

symbol_strategy = st.sampled_from(
    [
        "BTC/USDT",
        "ETH/USDT",
        "SOL/USDT",
        "ADA/USDT",
        "DOT/USDT",
        "AVAX/USDT",
        "MATIC/USDT",
        "LINK/USDT",
        "UNI/USDT",
        "ATOM/USDT",
    ]
)

# UUID策略
uuid_strategy = st.builds(uuid.uuid4).map(str)

# 时间戳策略
datetime_strategy = st.datetimes(
    min_value=datetime(2020, 1, 1, tzinfo=timezone.utc),
    max_value=datetime(2030, 12, 31, tzinfo=timezone.utc),
)

# 文本策略
text_strategy = st.text(
    alphabet=st.characters(whitelist_categories=("Lu", "Ll", "Nd", "Pc", "Pd", "Zs")),
    min_size=1,
    max_size=1000,
)


@composite
def parsed_intent_strategy(draw):
    """生成ParsedIntent的策略"""
    intent_type = draw(st.sampled_from(list(IntentType)))
    raw_text = draw(text_strategy)

    # 根据意图类型生成相应的字段
    if intent_type in [IntentType.CREATE_ORDER, IntentType.MODIFY_ORDER]:
        side = draw(st.sampled_from(list(TradeSide)))
        symbol = draw(symbol_strategy)
        quantity_usd = draw(st.one_of(st.none(), positive_decimal_strategy))
    else:
        side = draw(st.one_of(st.none(), st.sampled_from(list(TradeSide))))
        symbol = draw(st.one_of(st.none(), symbol_strategy))
        quantity_usd = draw(st.one_of(st.none(), positive_decimal_strategy))

    confidence = draw(st.decimals(min_value=0, max_value=1, places=2))

    clarification_needed = None
    if intent_type == IntentType.AMBIGUOUS:
        clarification_needed = draw(text_strategy)

    return {
        "intent_type": intent_type,
        "raw_text": raw_text,
        "side": side,
        "symbol": symbol,
        "quantity_usd": quantity_usd,
        "confidence": confidence,
        "clarification_needed": clarification_needed,
    }


@composite
def trade_plan_strategy(draw):
    """生成TradePlan的策略"""
    symbol = draw(symbol_strategy)
    side = draw(st.sampled_from(list(TradeSide)))
    order_type = draw(st.sampled_from(list(OrderType)))
    quantity = draw(positive_decimal_strategy)

    # 限价单需要价格
    price = None
    if order_type == OrderType.LIMIT:
        price = draw(positive_decimal_strategy)

    return {
        "symbol": symbol,
        "side": side,
        "order_type": order_type,
        "quantity": quantity,
        "price": price,
    }


@composite
def agent_state_strategy(draw):
    """生成AgentState的策略"""
    user_id = draw(st.integers(min_value=1, max_value=999999))
    raw_input = draw(text_strategy)

    # 生成解析意图列表 - 修复策略问题
    parsed_intents = draw(st.lists(parsed_intent_strategy(), min_size=0, max_size=3))

    # 生成执行计划列表 - 修复策略问题
    execution_plan = draw(st.lists(trade_plan_strategy(), min_size=0, max_size=2))

    return {
        "user_id": user_id,
        "raw_input": raw_input,
        "parsed_intents": [ParsedIntent(**intent) for intent in parsed_intents],
        "context": draw(st.dictionaries(st.text(), st.text())),
        "execution_plan": [TradePlan(**plan) for plan in execution_plan],
        "error_message": draw(st.one_of(st.none(), text_strategy)),
        "retry_count": draw(st.integers(min_value=0, max_value=10)),
        "pending_action_id": draw(st.one_of(st.none(), uuid_strategy)),
        "user_response": draw(st.one_of(st.none(), text_strategy)),
        "final_result": draw(
            st.one_of(st.none(), st.lists(st.dictionaries(st.text(), st.text())))
        ),
        "log": draw(st.lists(text_strategy, min_size=0, max_size=10)),
    }


# ============================================================================
# ParsedIntent属性测试
# ============================================================================


class TestParsedIntentProperties:
    """ParsedIntent模型属性测试"""

    @given(parsed_intent_strategy())
    @settings(max_examples=100)
    def test_parsed_intent_serialization_roundtrip(self, intent_data):
        """测试ParsedIntent序列化往返"""
        # 创建模型实例
        intent = ParsedIntent(**intent_data)

        # 序列化为字典
        serialized = intent.model_dump()

        # 从字典重新创建
        deserialized = ParsedIntent(**serialized)

        # 验证往返一致性
        assert intent == deserialized

    @given(parsed_intent_strategy())
    @settings(max_examples=100)
    def test_parsed_intent_json_serialization(self, intent_data):
        """测试ParsedIntent JSON序列化"""
        intent = ParsedIntent(**intent_data)

        # 序列化为JSON
        json_str = intent.model_dump_json()

        # 从JSON反序列化
        json_data = json.loads(json_str)
        deserialized = ParsedIntent(**json_data)

        # 验证一致性
        assert intent.intent_type == deserialized.intent_type
        assert intent.raw_text == deserialized.raw_text
        assert intent.confidence == deserialized.confidence

    @given(st.decimals(min_value=-1, max_value=2, places=2))
    def test_parsed_intent_confidence_validation(self, confidence):
        """测试置信度验证"""
        if 0 <= confidence <= 1:
            # 有效置信度应该通过验证
            intent = ParsedIntent(
                intent_type=IntentType.CREATE_ORDER,
                raw_text="test",
                confidence=confidence,
            )
            assert intent.confidence == confidence
        else:
            # 无效置信度应该抛出验证错误
            with pytest.raises(ValidationError):
                ParsedIntent(
                    intent_type=IntentType.CREATE_ORDER,
                    raw_text="test",
                    confidence=confidence,
                )

    @given(text_strategy)
    def test_parsed_intent_symbol_normalization(self, symbol_input):
        """测试交易对符号规范化"""
        assume(len(symbol_input.strip()) > 0)

        try:
            intent = ParsedIntent(
                intent_type=IntentType.CREATE_ORDER,
                raw_text="test",
                symbol=symbol_input,
                confidence=Decimal("0.8"),
            )

            # 如果创建成功，符号应该被规范化
            if intent.symbol:
                assert "/" in intent.symbol or intent.symbol.isupper()
        except ValidationError:
            # 某些输入可能无法通过验证，这是正常的
            pass


# ============================================================================
# TradePlan属性测试
# ============================================================================


class TestTradePlanProperties:
    """TradePlan模型属性测试"""

    @given(trade_plan_strategy())
    @settings(max_examples=100)
    def test_trade_plan_serialization_roundtrip(self, plan_data):
        """测试TradePlan序列化往返"""
        plan = TradePlan(**plan_data)

        serialized = plan.model_dump()
        deserialized = TradePlan(**serialized)

        assert plan == deserialized

    @given(
        symbol=symbol_strategy,
        side=st.sampled_from(list(TradeSide)),
        quantity=positive_decimal_strategy,
    )
    def test_trade_plan_market_order_no_price(self, symbol, side, quantity):
        """测试市价单不需要价格"""
        plan = TradePlan(
            symbol=symbol, side=side, order_type=OrderType.MARKET, quantity=quantity
        )

        assert plan.price is None
        assert plan.order_type == OrderType.MARKET

    @given(
        symbol=symbol_strategy,
        side=st.sampled_from(list(TradeSide)),
        quantity=positive_decimal_strategy,
        price=positive_decimal_strategy,
    )
    def test_trade_plan_limit_order_with_price(self, symbol, side, quantity, price):
        """测试限价单需要价格"""
        plan = TradePlan(
            symbol=symbol,
            side=side,
            order_type=OrderType.LIMIT,
            quantity=quantity,
            price=price,
        )

        assert plan.price == price
        assert plan.order_type == OrderType.LIMIT

    @given(positive_decimal_strategy)
    def test_trade_plan_quantity_precision(self, quantity):
        """测试数量精度处理"""
        plan = TradePlan(
            symbol="BTC/USDT",
            side=TradeSide.BUY,
            order_type=OrderType.MARKET,
            quantity=quantity,
        )

        # 数量应该保持精度
        assert plan.quantity == quantity
        assert isinstance(plan.quantity, Decimal)


# ============================================================================
# AgentState属性测试
# ============================================================================


class TestAgentStateProperties:
    """AgentState模型属性测试"""

    @given(agent_state_strategy())
    @settings(max_examples=50)  # 减少示例数量，因为AgentState比较复杂
    def test_agent_state_serialization_roundtrip(self, state_data):
        """测试AgentState序列化往返"""
        try:
            state = AgentState(**state_data)

            serialized = state.model_dump()
            deserialized = AgentState(**serialized)

            assert state.user_id == deserialized.user_id
            assert state.raw_input == deserialized.raw_input
            assert len(state.parsed_intents) == len(deserialized.parsed_intents)
            assert len(state.execution_plan) == len(deserialized.execution_plan)

        except ValidationError:
            # 某些随机生成的数据可能无法通过验证
            pass

    def test_agent_state_user_id_validation(self):
        """测试用户ID验证"""
        import uuid
        user_id = uuid.uuid4()
        state = AgentState(user_id=user_id, raw_input="test input")

        assert state.user_id == user_id
        assert isinstance(state.user_id, uuid.UUID)

    @given(st.integers(min_value=0, max_value=100))
    def test_agent_state_retry_count_validation(self, retry_count):
        """测试重试次数验证"""
        import uuid
        state = AgentState(user_id=uuid.uuid4(), raw_input="test", retry_count=retry_count)

        assert state.retry_count == retry_count
        assert state.retry_count >= 0

    @given(st.lists(text_strategy, min_size=0, max_size=20))
    def test_agent_state_log_handling(self, log_entries):
        """测试日志处理"""
        import uuid
        state = AgentState(user_id=uuid.uuid4(), raw_input="test", log=log_entries)

        assert len(state.log) == len(log_entries)
        assert all(isinstance(entry, str) for entry in state.log)


# ============================================================================
# 边界条件和异常情况测试
# ============================================================================


class TestEdgeCases:
    """边界条件和异常情况测试"""

    @given(st.text(min_size=0, max_size=0))
    def test_empty_string_handling(self, empty_text):
        """测试空字符串处理"""
        import uuid
        # 空字符串应该被正确处理
        state = AgentState(user_id=uuid.uuid4(), raw_input=empty_text)

        assert state.raw_input == ""

    @given(st.text(min_size=500, max_size=1500))  # 进一步减少文本长度以避免超时
    @settings(max_examples=10, deadline=30000)  # 限制示例数量和超时时间（30秒）
    def test_very_long_text_handling(self, long_text):
        """测试超长文本处理"""
        try:
            state = AgentState(user_id=1, raw_input=long_text)

            # 如果创建成功，文本应该被保留
            assert len(state.raw_input) == len(long_text)

        except ValidationError:
            # 如果有长度限制，应该抛出验证错误
            pass

    @given(st.decimals(allow_nan=True, allow_infinity=True))
    def test_invalid_decimal_handling(self, invalid_decimal):
        """测试无效Decimal处理"""
        try:
            # 检查是否为NaN或无穷大
            is_invalid = (
                invalid_decimal != invalid_decimal
                or invalid_decimal == Decimal("Infinity")  # NaN检查
                or invalid_decimal == Decimal("-Infinity")
                or str(invalid_decimal).lower() in ["nan", "snan", "inf", "-inf"]
            )

            if is_invalid:
                # NaN和无穷大应该被拒绝
                with pytest.raises((ValidationError, InvalidOperation)):
                    ParsedIntent(
                        intent_type=IntentType.CREATE_ORDER,
                        raw_text="test",
                        quantity_usd=invalid_decimal,
                        confidence=Decimal("0.8"),
                    )
        except InvalidOperation:
            # 某些Decimal操作本身就会抛出InvalidOperation，这是正常的
            pass

    @given(
        st.lists(
            parsed_intent_strategy().map(lambda data: ParsedIntent(**data)),
            min_size=5,
            max_size=20,
        )
    )
    @settings(max_examples=10, deadline=5000)  # 减少示例数量并增加超时时间
    def test_large_intent_list_handling(self, large_intent_list):
        """测试大量意图列表处理"""
        try:
            state = AgentState(
                user_id=1, raw_input="test", parsed_intents=large_intent_list
            )

            assert len(state.parsed_intents) == len(large_intent_list)

        except (ValidationError, MemoryError):
            # 可能有大小限制或内存限制
            pass


# ============================================================================
# 数据一致性测试
# ============================================================================


class TestDataConsistency:
    """数据一致性测试"""

    @given(
        intent_type=st.sampled_from([IntentType.CREATE_ORDER, IntentType.MODIFY_ORDER]),
        side=st.sampled_from(list(TradeSide)),
        symbol=symbol_strategy,
    )
    def test_create_order_intent_consistency(self, intent_type, side, symbol):
        """测试创建订单意图的数据一致性"""
        intent = ParsedIntent(
            intent_type=intent_type,
            raw_text=f"{side.value} {symbol}",
            side=side,
            symbol=symbol,
            confidence=Decimal("0.8"),
        )

        # 创建订单意图应该有交易方向和交易对
        assert intent.side is not None
        assert intent.symbol is not None
        assert intent.intent_type in [IntentType.CREATE_ORDER, IntentType.MODIFY_ORDER]

    @given(
        symbol=symbol_strategy,
        side=st.sampled_from(list(TradeSide)),
        quantity=positive_decimal_strategy,
    )
    def test_trade_plan_consistency(self, symbol, side, quantity):
        """测试交易计划的数据一致性"""
        # 市价单
        market_plan = TradePlan(
            symbol=symbol, side=side, order_type=OrderType.MARKET, quantity=quantity
        )

        assert market_plan.price is None

        # 限价单
        limit_plan = TradePlan(
            symbol=symbol,
            side=side,
            order_type=OrderType.LIMIT,
            quantity=quantity,
            price=Decimal("100.0"),
        )

        assert limit_plan.price is not None
