"""
Agent计划生成测试 - test_agent_nodes_planning.py
测试 app/agent/nodes.py 中的计划生成相关功能
目标：实现10个计划生成测试用例，覆盖generate_plan函数及相关功能
"""

import asyncio
import pytest
import uuid
from decimal import Decimal
from unittest.mock import AsyncMock, Mock, patch

from app.agent.nodes import (
    generate_plan,
    route_after_parsing,
    TradeExecutionEngine,
)
from app.core.schemas import (
    AgentState,
    ParsedIntent,
    IntentType,
    TradeSide,
    OrderType,
    TradePlan,
)


@pytest.mark.unit
@pytest.mark.business_logic
class TestAgentPlanGeneration:
    """Agent计划生成核心功能测试"""

    def setup_method(self):
        """设置测试环境"""
        self.test_user_id = uuid.uuid4()
        self.test_task_id = uuid.uuid4()
        
        # 创建基础测试状态
        self.base_state = AgentState(
            task_id=self.test_task_id,
            user_id=self.test_user_id,
            raw_input="买入 BTC 1000U",
            parsed_intents=[],
            context={
                "risk_config": {
                    "max_concurrent_orders": 5,
                    "max_total_position_value_usd": Decimal("10000"),
                    "default_position_size_usd": Decimal("1000"),
                    "max_position_size_usd": Decimal("5000"),
                    "allowed_symbols": ["BTC/USDT", "ETH/USDT"],
                    "confidence_threshold": Decimal("0.8")
                },
                "market_prices": {
                    "BTC/USDT": Decimal("50000"),
                    "ETH/USDT": Decimal("3000")
                },
                "active_orders": []
            },
            execution_plan=[],
            log=[]
        )

    @pytest.mark.asyncio
    async def test_generate_plan_创建订单意图(self):
        """测试为创建订单意图生成计划"""
        # Arrange
        state = self.base_state.copy()
        state.parsed_intents = [
            ParsedIntent(
                intent_type=IntentType.CREATE_ORDER,
                raw_text="买入 BTC 1000U",
                side=TradeSide.BUY,
                symbol="BTC/USDT",
                quantity_usd=Decimal("1000"),
                confidence=Decimal("0.95")
            )
        ]
        
        # Act
        result = await generate_plan(state)
        
        # Assert
        assert len(result.execution_plan) == 1
        plan = result.execution_plan[0]
        assert plan.symbol == "BTC/USDT"
        assert plan.side == TradeSide.BUY
        assert plan.order_type == OrderType.MARKET
        assert plan.quantity == Decimal("1000") / Decimal("50000")  # 1000 USD / 50000 price
        # 检查日志中是否包含计划生成相关信息
        log_content = " ".join(result.log)
        assert "交易计划" in log_content or "执行计划" in log_content

    @pytest.mark.asyncio
    async def test_generate_plan_平仓意图(self):
        """测试为平仓意图生成计划"""
        # Arrange
        state = self.base_state.copy()
        state.parsed_intents = [
            ParsedIntent(
                intent_type=IntentType.CLOSE_ORDER,
                raw_text="平仓 BTC",
                side=TradeSide.SELL,
                symbol="BTC/USDT",
                target_criteria="all active positions",
                confidence=Decimal("0.9")
            )
        ]
        
        # 添加活跃订单到上下文
        state.context["active_orders"] = [
            {
                "id": "order1",
                "symbol": "BTC/USDT",
                "side": "buy",
                "quantity": 0.1,
                "status": "filled"
            }
        ]
        
        # Act
        result = await generate_plan(state)
        
        # Assert
        assert len(result.execution_plan) == 1
        plan = result.execution_plan[0]
        assert plan.symbol == "BTC/USDT"
        assert plan.side == TradeSide.SELL  # 平仓是反向操作
        assert plan.quantity == Decimal("0.1")
        # 检查日志中是否包含平仓计划相关信息
        log_content = " ".join(result.log)
        assert "平仓计划" in log_content or "执行计划" in log_content

    @pytest.mark.asyncio
    async def test_generate_plan_多个意图(self):
        """测试为多个意图生成计划"""
        # Arrange
        state = self.base_state.copy()
        state.parsed_intents = [
            ParsedIntent(
                intent_type=IntentType.CREATE_ORDER,
                raw_text="买入 BTC 1000U",
                side=TradeSide.BUY,
                symbol="BTC/USDT",
                quantity_usd=Decimal("1000"),
                confidence=Decimal("0.95")
            ),
            ParsedIntent(
                intent_type=IntentType.CREATE_ORDER,
                raw_text="买入 ETH 500U",
                side=TradeSide.BUY,
                symbol="ETH/USDT",
                quantity_usd=Decimal("500"),
                confidence=Decimal("0.9")
            )
        ]
        
        # Act
        result = await generate_plan(state)
        
        # Assert
        assert len(result.execution_plan) == 2
        
        # 验证BTC计划
        btc_plan = result.execution_plan[0]
        assert btc_plan.symbol == "BTC/USDT"
        assert btc_plan.side == TradeSide.BUY
        assert btc_plan.quantity == Decimal("1000") / Decimal("50000")
        
        # 验证ETH计划
        eth_plan = result.execution_plan[1]
        assert eth_plan.symbol == "ETH/USDT"
        assert eth_plan.side == TradeSide.BUY
        assert eth_plan.quantity == Decimal("500") / Decimal("3000")

    @pytest.mark.asyncio
    async def test_generate_plan_超出最大头寸限制(self):
        """测试超出最大头寸限制时的计划生成"""
        # Arrange
        state = self.base_state.copy()
        state.parsed_intents = [
            ParsedIntent(
                intent_type=IntentType.CREATE_ORDER,
                raw_text="买入 BTC 10000U",
                side=TradeSide.BUY,
                symbol="BTC/USDT",
                quantity_usd=Decimal("10000"),  # 超过5000限制
                confidence=Decimal("0.95")
            )
        ]
        
        # Act
        result = await generate_plan(state)
        
        # Assert
        assert len(result.execution_plan) == 1
        plan = result.execution_plan[0]
        # 应该被限制在最大头寸大小
        expected_quantity = Decimal("5000") / Decimal("50000")  # 使用max_position_size_usd
        assert plan.quantity == expected_quantity

    @pytest.mark.asyncio
    async def test_generate_plan_无效市场价格(self):
        """测试无效市场价格时的计划生成"""
        # Arrange
        state = self.base_state.copy()
        state.context["market_prices"]["BTC/USDT"] = Decimal("0")  # 无效价格
        state.parsed_intents = [
            ParsedIntent(
                intent_type=IntentType.CREATE_ORDER,
                raw_text="买入 BTC 1000U",
                side=TradeSide.BUY,
                symbol="BTC/USDT",
                quantity_usd=Decimal("1000"),
                confidence=Decimal("0.95")
            )
        ]
        
        # Act
        result = await generate_plan(state)
        
        # Assert
        assert len(result.execution_plan) == 0  # 不应该生成计划
        # 检查日志中是否包含价格相关信息
        log_content = " ".join(result.log)
        assert "价格无效" in log_content or "未生成任何执行计划" in log_content

    @pytest.mark.asyncio
    async def test_generate_plan_查询意图(self):
        """测试查询意图不生成交易计划"""
        # Arrange
        state = self.base_state.copy()
        state.parsed_intents = [
            ParsedIntent(
                intent_type=IntentType.QUERY_STATUS,
                raw_text="查看我的订单状态",
                confidence=Decimal("0.95")
            )
        ]
        
        # Act
        result = await generate_plan(state)
        
        # Assert
        assert len(result.execution_plan) == 0
        assert "未生成任何执行计划" in result.log[-1]

    @pytest.mark.asyncio
    async def test_generate_plan_修改订单意图(self):
        """测试修改订单意图的处理"""
        # Arrange
        state = self.base_state.copy()
        state.parsed_intents = [
            ParsedIntent(
                intent_type=IntentType.MODIFY_ORDER,
                raw_text="修改订单止损价格",
                confidence=Decimal("0.9")
            )
        ]
        
        # Act
        result = await generate_plan(state)
        
        # Assert
        assert len(result.execution_plan) == 0
        # 检查日志中是否包含修改订单相关信息
        log_content = " ".join(result.log)
        assert "修改订单" in log_content or "未生成任何执行计划" in log_content

    @pytest.mark.asyncio
    async def test_generate_plan_空意图列表(self):
        """测试空意图列表的计划生成"""
        # Arrange
        state = self.base_state.copy()
        state.parsed_intents = []
        
        # Act
        result = await generate_plan(state)
        
        # Assert
        assert len(result.execution_plan) == 0
        assert "未生成任何执行计划" in result.log[-1]

    def test_route_after_parsing_成功路由(self):
        """测试解析后成功路由"""
        # Arrange
        state = self.base_state.copy()
        state.parsed_intents = [
            ParsedIntent(
                intent_type=IntentType.CREATE_ORDER,
                raw_text="买入 BTC 1000U",
                side=TradeSide.BUY,
                symbol="BTC/USDT",
                quantity_usd=Decimal("1000"),
                confidence=Decimal("0.95")
            )
        ]
        
        # Act
        result = route_after_parsing(state)
        
        # Assert
        assert result == "Context"

    def test_route_after_parsing_需要确认(self):
        """测试需要用户确认的路由"""
        # Arrange
        state = self.base_state.copy()
        state.parsed_intents = [
            ParsedIntent(
                intent_type=IntentType.AMBIGUOUS,
                raw_text="我想交易",
                confidence=Decimal("0.5"),
                clarification_needed="请明确指出要买入还是卖出"
            )
        ]
        
        # Act
        result = route_after_parsing(state)
        
        # Assert
        assert result == "UserConfirm"


@pytest.mark.unit
@pytest.mark.business_logic
class TestTradeExecutionEngine:
    """交易执行引擎测试"""

    def setup_method(self):
        """设置测试环境"""
        self.test_user_id = uuid.uuid4()
        self.test_task_id = str(uuid.uuid4())
        
        self.execution_engine = TradeExecutionEngine(
            user_id=self.test_user_id,
            task_id=self.test_task_id,
            simulation_mode=True
        )

    @pytest.mark.asyncio
    async def test_execute_plans_模拟模式(self):
        """测试模拟模式下的计划执行"""
        # Arrange
        plans = [
            TradePlan(
                symbol="BTC/USDT",
                side=TradeSide.BUY,
                quantity=Decimal("0.02"),
                order_type=OrderType.MARKET
            )
        ]
        
        mock_db = AsyncMock()
        
        # Act
        results = await self.execution_engine.execute_plans(plans, mock_db)
        
        # Assert
        assert len(results) == 1
        result = results[0]
        assert result.status == "success"
        # 检查是否有订单ID或客户端订单ID
        assert result.order_id is not None or result.client_order_id is not None
        # 检查是否是模拟模式的结果
        assert result.order_id is None or "sim_" in result.order_id
