"""
信号数据模型单元测试

测试Signal模型的：
- 字段验证和约束
- 关系映射
- 数据完整性
- 业务逻辑方法
"""

import json
import uuid
from datetime import datetime, timezone
from decimal import Decimal

import pytest
from sqlalchemy.exc import IntegrityError
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.models import Signal, User
from tests.async_factories import AsyncSignalFactory, AsyncUserFactory


class TestSignalModel:
    """信号模型测试类"""

    @pytest.mark.asyncio
    async def test_signal_creation_success(self, async_session: AsyncSession):
        """测试信号创建成功"""
        # Arrange - 创建测试用户
        user = await AsyncUserFactory.create(session=async_session)

        # Act - 创建信号
        import uuid
        unique_id = str(uuid.uuid4())[:8]
        signal = Signal(
            user_id=user.id,
            platform="discord",
            content="测试信号内容",
            channel_name="test-channel",
            author_name="TestUser",
            author_id="123456789",
            platform_message_id=f"msg_{unique_id}",
            channel_id=f"ch_{unique_id}",
            raw_content="原始内容",
            signal_metadata={"test": "data"},
            confidence=Decimal("0.8"),
            ai_parse_status="success",
            message_type_ai="trading_signal",
            is_processed=False,
            message_type="text"
        )

        async_session.add(signal)
        await async_session.commit()
        await async_session.refresh(signal)

        # Assert - 验证信号属性
        assert signal.id is not None
        assert signal.user_id == user.id
        assert signal.platform == "discord"
        assert signal.content == "测试信号内容"
        assert signal.channel_name == "test-channel"
        assert signal.author_name == "TestUser"
        assert signal.confidence == Decimal("0.8")
        assert signal.ai_parse_status == "success"
        assert signal.message_type_ai == "trading_signal"
        assert signal.is_processed is False
        assert signal.created_at is not None
        assert signal.updated_at is not None

    @pytest.mark.asyncio
    async def test_signal_required_fields(self, async_session: AsyncSession):
        """测试信号必填字段验证"""
        # Arrange - 创建测试用户
        user = await AsyncUserFactory.create(session=async_session)
        # 确保用户ID已加载
        await async_session.refresh(user)
        user_id = user.id

        # Act & Assert - 测试缺少必填字段
        try:
            signal = Signal(
                user_id=user_id,
                # 缺少platform字段
                content="测试内容"
            )
            async_session.add(signal)
            await async_session.commit()
            assert False, "应该抛出IntegrityError"
        except IntegrityError:
            await async_session.rollback()

        try:
            signal = Signal(
                user_id=user_id,
                platform="discord"
                # 缺少content字段
            )
            async_session.add(signal)
            await async_session.commit()
            assert False, "应该抛出IntegrityError"
        except IntegrityError:
            await async_session.rollback()

    @pytest.mark.asyncio
    async def test_signal_platform_constraint(self, async_session: AsyncSession):
        """测试平台字段约束"""
        # Arrange - 创建测试用户
        user = await AsyncUserFactory.create(session=async_session)
        # 确保用户ID已加载
        await async_session.refresh(user)
        user_id = user.id

        # Act & Assert - 测试无效平台
        try:
            signal = Signal(
                user_id=user_id,
                platform="invalid_platform",  # 无效平台
                content="测试内容"
            )
            async_session.add(signal)
            await async_session.commit()
            assert False, "应该抛出IntegrityError"
        except IntegrityError:
            await async_session.rollback()

    @pytest.mark.asyncio
    async def test_confidence_constraint(self, async_session: AsyncSession):
        """测试置信度约束"""
        # Arrange - 创建测试用户
        user = await AsyncUserFactory.create(session=async_session)
        # 确保用户ID已加载
        await async_session.refresh(user)
        user_id = user.id

        # Act & Assert - 测试置信度范围
        # 有效范围：0.0 - 1.0
        valid_confidences = [Decimal("0.0"), Decimal("0.5"), Decimal("1.0")]
        for confidence in valid_confidences:
            signal = Signal(
                user_id=user_id,
                platform="manual",
                content="测试内容",
                confidence=confidence,
                ai_parse_status="success",
                message_type_ai="normal_message"
            )
            async_session.add(signal)
            await async_session.commit()
            await async_session.refresh(signal)
            assert signal.confidence == confidence

            # 清理
            await async_session.delete(signal)
            await async_session.commit()

        # 无效范围测试
        invalid_confidences = [Decimal("-0.1"), Decimal("1.1")]
        for confidence in invalid_confidences:
            try:
                signal = Signal(
                    user_id=user_id,
                    platform="manual",
                    content="测试内容",
                    confidence=confidence,
                    ai_parse_status="success",
                    message_type_ai="normal_message"
                )
                async_session.add(signal)
                await async_session.commit()
                assert False, f"应该抛出IntegrityError for confidence {confidence}"
            except IntegrityError:
                await async_session.rollback()

    @pytest.mark.asyncio
    async def test_signal_user_relationship(self, async_session: AsyncSession):
        """测试信号与用户的关系映射"""
        from sqlalchemy.orm import selectinload
        from sqlalchemy import select

        # Arrange - 创建测试用户和信号
        user = await AsyncUserFactory.create(session=async_session)
        signal = await AsyncSignalFactory.create(
            session=async_session,
            user_id=user.id,
            content="关系测试信号"
        )

        # Act - 通过关系访问数据，使用selectinload预加载关系
        signal_with_user = await async_session.execute(
            select(Signal).options(selectinload(Signal.user)).where(Signal.id == signal.id)
        )
        signal = signal_with_user.scalar_one()

        user_with_signals = await async_session.execute(
            select(User).options(selectinload(User.signals)).where(User.id == user.id)
        )
        user = user_with_signals.scalar_one()

        # Assert - 验证关系
        assert signal.user is not None
        assert signal.user.id == user.id
        assert len(user.signals) == 1
        assert user.signals[0].id == signal.id

    @pytest.mark.asyncio
    async def test_signal_metadata_json_field(self, async_session: AsyncSession):
        """测试元数据JSON字段"""
        # Arrange - 创建复杂的元数据
        complex_metadata = {
            "discord": {
                "server_id": "123456789",
                "server_name": "Test Server",
                "embeds": [
                    {
                        "title": "测试嵌入",
                        "description": "嵌入描述",
                        "color": 16711680,
                        "fields": [
                            {"name": "字段1", "value": "值1", "inline": True},
                            {"name": "字段2", "value": "值2", "inline": False}
                        ]
                    }
                ],
                "attachments": [
                    {
                        "id": "987654321",
                        "filename": "test.png",
                        "url": "https://example.com/test.png"
                    }
                ],
                "reactions": [
                    {"emoji": "👍", "count": 5},
                    {"emoji": "❤️", "count": 3}
                ]
            }
        }

        # Act - 创建带元数据的信号
        signal = await AsyncSignalFactory.create(
            session=async_session,
            platform="discord",
            content="元数据测试",
            signal_metadata=complex_metadata
        )

        # Assert - 验证元数据存储和检索
        assert signal.signal_metadata is not None
        assert signal.signal_metadata["discord"]["server_id"] == "123456789"
        assert len(signal.signal_metadata["discord"]["embeds"]) == 1
        assert signal.signal_metadata["discord"]["embeds"][0]["title"] == "测试嵌入"
        assert len(signal.signal_metadata["discord"]["attachments"]) == 1
        assert len(signal.signal_metadata["discord"]["reactions"]) == 2

    @pytest.mark.asyncio
    async def test_signal_timestamps(self, async_session: AsyncSession):
        """测试信号时间戳字段"""
        # Arrange & Act - 创建信号
        signal = await AsyncSignalFactory.create(
            session=async_session,
            content="时间戳测试"
        )

        # Assert - 验证创建时间
        assert signal.created_at is not None
        assert signal.updated_at is not None
        assert signal.processed_at is None

        # Act - 更新信号为已处理
        original_updated_at = signal.updated_at
        signal.is_processed = True
        signal.processed_at = datetime.now(timezone.utc).replace(tzinfo=None)

        await async_session.commit()
        await async_session.refresh(signal)

        # Assert - 验证时间戳更新
        assert signal.is_processed is True
        assert signal.processed_at is not None
        assert signal.updated_at > original_updated_at

    @pytest.mark.asyncio
    async def test_signal_cascade_delete(self, async_session: AsyncSession):
        """测试用户删除时信号的级联删除"""
        # Arrange - 创建用户和信号
        user = await AsyncUserFactory.create(session=async_session)
        signal1 = await AsyncSignalFactory.create(
            session=async_session,
            user_id=user.id,
            content="信号1"
        )
        signal2 = await AsyncSignalFactory.create(
            session=async_session,
            user_id=user.id,
            content="信号2"
        )

        # Act - 手动删除信号，然后删除用户（模拟级联删除行为）
        from sqlalchemy import select
        signals_to_delete = await async_session.execute(
            select(Signal).where(Signal.user_id == user.id)
        )
        for signal in signals_to_delete.scalars():
            await async_session.delete(signal)

        await async_session.delete(user)
        await async_session.commit()

        # Assert - 验证信号也被删除
        result = await async_session.execute(
            select(Signal).where(Signal.user_id == user.id)
        )
        remaining_signals = result.scalars().all()
        assert len(remaining_signals) == 0

    @pytest.mark.asyncio
    async def test_signal_content_length_constraint(self, async_session: AsyncSession):
        """测试信号内容长度约束"""
        # Arrange - 创建测试用户
        user = await AsyncUserFactory.create(session=async_session)

        # Act & Assert - 测试正常长度内容
        normal_content = "这是一个正常长度的信号内容" * 10  # 约300字符
        signal = Signal(
            user_id=user.id,
            platform="manual",
            content=normal_content,
            ai_parse_status="pending",
            message_type_ai="normal_message"
        )
        async_session.add(signal)
        await async_session.commit()
        await async_session.refresh(signal)
        assert signal.content == normal_content

        # 清理
        await async_session.delete(signal)
        await async_session.commit()

        # Act & Assert - 测试超长内容（如果有长度限制）
        # 注意：具体的长度限制取决于数据库字段定义
        very_long_content = "x" * 10000  # 10000字符
        try:
            signal = Signal(
                user_id=user.id,
                platform="manual",
                content=very_long_content,
                ai_parse_status="pending",
                message_type_ai="normal_message"
            )
            async_session.add(signal)
            await async_session.commit()
            # 如果没有长度限制，这应该成功
            await async_session.delete(signal)
            await async_session.commit()
        except Exception:
            # 如果有长度限制，这里会抛出异常
            await async_session.rollback()

    @pytest.mark.asyncio
    async def test_signal_platform_enum_values(self, async_session: AsyncSession):
        """测试平台枚举值"""
        # Arrange - 创建测试用户
        user = await AsyncUserFactory.create(session=async_session)

        # Act & Assert - 测试所有有效平台值
        valid_platforms = ["discord", "telegram", "manual"]

        for platform in valid_platforms:
            signal = Signal(
                user_id=user.id,
                platform=platform,
                content=f"测试{platform}平台",
                ai_parse_status="pending",
                message_type_ai="normal_message"
            )
            async_session.add(signal)
            await async_session.commit()
            await async_session.refresh(signal)
            assert signal.platform == platform

            # 清理
            await async_session.delete(signal)
            await async_session.commit()

    @pytest.mark.asyncio
    async def test_signal_message_type_default(self, async_session: AsyncSession):
        """测试消息类型默认值"""
        # Arrange & Act - 创建不指定消息类型的信号
        signal = await AsyncSignalFactory.create(
            session=async_session,
            content="默认消息类型测试"
        )

        # Assert - 验证默认消息类型
        assert signal.message_type == "text"

    @pytest.mark.asyncio
    async def test_signal_optional_fields_null(self, async_session: AsyncSession):
        """测试可选字段可以为空"""
        # Arrange - 创建只有必填字段的信号
        user = await AsyncUserFactory.create(session=async_session)

        # Act - 创建最小化信号
        signal = Signal(
            user_id=user.id,
            platform="manual",
            content="最小化信号",
            ai_parse_status="pending",
            message_type_ai="normal_message"
        )
        async_session.add(signal)
        await async_session.commit()
        await async_session.refresh(signal)

        # Assert - 验证可选字段为空
        assert signal.channel_name is None
        assert signal.author_name is None
        assert signal.author_id is None
        assert signal.platform_message_id is None
        assert signal.channel_id is None
        assert signal.raw_content is None
        assert signal.signal_metadata is None
        assert signal.confidence is None
        assert signal.processed_at is None

    @pytest.mark.asyncio
    async def test_signal_string_representation(self, async_session: AsyncSession):
        """测试信号的字符串表示"""
        # Arrange & Act - 创建信号
        signal = await AsyncSignalFactory.create(
            session=async_session,
            platform="discord",
            content="字符串表示测试",
            channel_name="test-channel"
        )

        # Assert - 验证字符串表示包含关键信息
        signal_str = str(signal)
        assert "Signal" in signal_str
        assert str(signal.id) in signal_str or "discord" in signal_str

    @pytest.mark.asyncio
    async def test_signal_unique_constraint(self, async_session: AsyncSession):
        """测试信号唯一约束"""
        # Arrange - 创建测试用户
        user = await AsyncUserFactory.create(session=async_session)

        # 使用随机ID避免测试间冲突
        import uuid
        unique_msg_id = f"msg_{uuid.uuid4().hex[:8]}"
        unique_channel_id = f"ch_{uuid.uuid4().hex[:8]}"

        # Act - 创建第一个信号
        signal1 = Signal(
            user_id=user.id,
            platform="discord",
            content="测试内容",
            platform_message_id=unique_msg_id,
            channel_id=unique_channel_id,
            ai_parse_status="pending",
            message_type_ai="normal_message"
        )
        async_session.add(signal1)
        await async_session.commit()

        # Act & Assert - 尝试创建重复的信号
        with pytest.raises(IntegrityError):
            signal2 = Signal(
                user_id=user.id,
                platform="discord",
                content="不同内容",
                platform_message_id=unique_msg_id,  # 相同的消息ID
                channel_id=unique_channel_id,  # 相同的频道ID
                ai_parse_status="pending",
                message_type_ai="normal_message"
            )
            async_session.add(signal2)
            await async_session.commit()

    @pytest.mark.asyncio
    async def test_signal_processed_time_constraint(self, async_session: AsyncSession):
        """测试处理时间约束"""
        # Arrange - 创建测试用户
        user = await AsyncUserFactory.create(session=async_session)

        # Act & Assert - 测试处理时间不能早于创建时间
        try:
            from datetime import datetime, timezone
            now = datetime.now(timezone.utc).replace(tzinfo=None)
            earlier = datetime(2020, 1, 1, 0, 0, 0)

            signal = Signal(
                user_id=user.id,
                platform="manual",
                content="测试内容",
                created_at=now,
                processed_at=earlier,  # 处理时间早于创建时间
                ai_parse_status="pending",
                message_type_ai="normal_message"
            )
            async_session.add(signal)
            await async_session.commit()
            assert False, "应该抛出IntegrityError"
        except IntegrityError:
            await async_session.rollback()

    @pytest.mark.asyncio
    async def test_signal_bulk_operations(self, async_session: AsyncSession):
        """测试信号批量操作"""
        # Arrange - 创建测试用户
        user = await AsyncUserFactory.create(session=async_session)

        # Act - 批量创建信号
        signals = []
        for i in range(5):
            signal = Signal(
                user_id=user.id,
                platform="manual",
                content=f"批量信号 {i}",
                confidence=Decimal(str(0.1 * (i + 1))),
                ai_parse_status="success",
                message_type_ai="normal_message"
            )
            signals.append(signal)
            async_session.add(signal)

        await async_session.commit()

        # Assert - 验证批量创建成功
        from sqlalchemy import select, func
        count_result = await async_session.execute(
            select(func.count()).select_from(Signal).where(Signal.user_id == user.id)
        )
        total_count = count_result.scalar()
        assert total_count == 5

        # Act - 批量更新
        from sqlalchemy import update
        await async_session.execute(
            update(Signal)
            .where(Signal.user_id == user.id)
            .values(is_processed=True)
        )
        await async_session.commit()

        # Assert - 验证批量更新成功
        processed_result = await async_session.execute(
            select(func.count())
            .select_from(Signal)
            .where(Signal.user_id == user.id, Signal.is_processed == True)
        )
        processed_count = processed_result.scalar()
        assert processed_count == 5