#!/usr/bin/env python3
"""
Discord配置模型单元测试
测试DiscordConfig模型的所有功能和约束

按照项目测试规范：
- 测试模型字段验证
- 测试数据库约束
- 测试模型方法
- 使用中文注释和错误信息
"""
import uuid
from datetime import datetime, timezone
from typing import List

import pytest
from sqlalchemy.exc import IntegrityError

from app.core.models import DiscordConfig, User
from app.core.security import encrypt_sensitive_data, decrypt_sensitive_data
from app.core.auth import AuthManager


class TestDiscordConfigModel:
    """Discord配置模型测试类"""

    @pytest.fixture
    async def test_user(self, async_session):
        """创建测试用户"""
        unique_id = uuid.uuid4().hex[:8]
        user = User(
            username=f"test_discord_user_{unique_id}",
            email=f"test_discord_{unique_id}@example.com",
            password_hash=AuthManager.hash_password("test_password_123"),
            is_active=True
        )
        async_session.add(user)
        await async_session.commit()
        await async_session.refresh(user)
        return user

    @pytest.mark.asyncio
    async def test_discord_config_creation_success(self, async_session, test_user):
        """测试Discord配置创建成功"""
        # 准备测试数据
        test_token = "test_discord_token_123"
        encrypted_token = encrypt_sensitive_data(test_token)
        
        config = DiscordConfig(
            user_id=test_user.id,
            source_name="测试Discord配置",
            enabled=True,
            encrypted_token=encrypted_token,
            server_ids=["123456789", "987654321"],
            channel_ids=["111111111", "222222222"],
            author_ids=["333333333"],
            allowed_message_types=["text"]
        )
        
        # 执行创建
        async_session.add(config)
        await async_session.commit()
        await async_session.refresh(config)
        
        # 验证结果
        assert config.id is not None
        assert isinstance(config.id, uuid.UUID)
        assert config.user_id == test_user.id
        assert config.source_name == "测试Discord配置"
        assert config.enabled is True
        assert config.encrypted_token == encrypted_token
        assert config.server_ids == ["123456789", "987654321"]
        assert config.channel_ids == ["111111111", "222222222"]
        assert config.author_ids == ["333333333"]
        assert config.allowed_message_types == ["text"]
        assert isinstance(config.created_at, datetime)
        assert isinstance(config.updated_at, datetime)

    @pytest.mark.asyncio
    async def test_discord_config_required_fields(self, async_session, test_user):
        """测试Discord配置必需字段验证"""
        # 测试缺少user_id
        with pytest.raises(IntegrityError, match="not-null constraint"):
            config = DiscordConfig(
                source_name="测试配置",
                enabled=True,
                encrypted_token="encrypted_token"
            )
            async_session.add(config)
            await async_session.commit()



    @pytest.mark.asyncio
    async def test_discord_config_array_fields_default(self, async_session, test_user):
        """测试数组字段的默认值"""
        config = DiscordConfig(
            user_id=test_user.id,
            source_name="测试默认值配置",
            enabled=False,
            encrypted_token="encrypted_token"
            # 不设置数组字段，测试默认值
        )
        
        async_session.add(config)
        await async_session.commit()
        await async_session.refresh(config)
        
        # 验证默认值
        assert config.server_ids == []
        assert config.channel_ids == []
        assert config.author_ids == []
        assert config.allowed_message_types == ["text"]  # 默认值是["text"]

    @pytest.mark.asyncio
    async def test_discord_config_array_fields_with_data(self, async_session, test_user):
        """测试数组字段存储和检索"""
        server_ids = ["123456789", "987654321", "555666777"]
        channel_ids = ["111111111", "222222222"]
        author_ids = ["333333333", "444444444", "555555555"]
        message_types = ["text", "embed"]
        
        config = DiscordConfig(
            user_id=test_user.id,
            source_name="测试数组字段配置",
            enabled=True,
            encrypted_token="encrypted_token",
            server_ids=server_ids,
            channel_ids=channel_ids,
            author_ids=author_ids,
            allowed_message_types=message_types
        )
        
        async_session.add(config)
        await async_session.commit()
        await async_session.refresh(config)
        
        # 验证数组数据完整性
        assert config.server_ids == server_ids
        assert config.channel_ids == channel_ids
        assert config.author_ids == author_ids
        assert config.allowed_message_types == message_types

    @pytest.mark.asyncio
    async def test_discord_config_enabled_field(self, async_session, test_user):
        """测试enabled字段的布尔值处理"""
        # 测试True值
        config_enabled = DiscordConfig(
            user_id=test_user.id,
            source_name="启用的配置",
            enabled=True,
            encrypted_token="encrypted_token"
        )
        
        async_session.add(config_enabled)
        await async_session.commit()
        await async_session.refresh(config_enabled)
        
        assert config_enabled.enabled is True
        
        # 测试False值
        config_disabled = DiscordConfig(
            user_id=test_user.id,
            source_name="禁用的配置",
            enabled=False,
            encrypted_token="encrypted_token"
        )
        
        async_session.add(config_disabled)
        await async_session.commit()
        await async_session.refresh(config_disabled)
        
        assert config_disabled.enabled is False

    @pytest.mark.asyncio
    async def test_discord_config_timestamps(self, async_session, test_user):
        """测试时间戳字段的自动设置和更新"""
        config = DiscordConfig(
            user_id=test_user.id,
            source_name="时间戳测试配置",
            enabled=True,
            encrypted_token="encrypted_token"
        )

        async_session.add(config)
        await async_session.commit()
        await async_session.refresh(config)

        # 验证时间戳字段存在且不为空
        assert config.created_at is not None
        assert config.updated_at is not None
        assert isinstance(config.created_at, datetime)
        assert isinstance(config.updated_at, datetime)

        # 更新配置
        original_created_at = config.created_at
        original_updated_at = config.updated_at

        # 等待一小段时间确保时间戳不同
        import asyncio
        await asyncio.sleep(0.01)

        config.source_name = "更新后的配置名称"
        await async_session.commit()
        await async_session.refresh(config)

        # 验证更新时间戳
        assert config.created_at == original_created_at  # created_at不应该改变
        assert config.updated_at >= original_updated_at   # updated_at应该更新或保持不变

    @pytest.mark.asyncio
    async def test_discord_config_user_relationship(self, async_session, test_user):
        """测试Discord配置与用户的关系"""
        config = DiscordConfig(
            user_id=test_user.id,
            source_name="关系测试配置",
            enabled=True,
            encrypted_token="encrypted_token"
        )
        
        async_session.add(config)
        await async_session.commit()
        await async_session.refresh(config)
        
        # 验证外键关系
        assert config.user_id == test_user.id
        
        # 测试级联删除（如果配置了的话）
        # 注意：这取决于具体的外键约束配置



    @pytest.mark.asyncio
    async def test_discord_config_token_encryption_integration(self, async_session, test_user):
        """测试Discord配置与加密功能的集成"""
        original_token = "super_secret_discord_token_123"
        encrypted_token = encrypt_sensitive_data(original_token)
        
        config = DiscordConfig(
            user_id=test_user.id,
            source_name="加密集成测试配置",
            enabled=True,
            encrypted_token=encrypted_token
        )
        
        async_session.add(config)
        await async_session.commit()
        await async_session.refresh(config)
        
        # 验证存储的是加密数据
        assert config.encrypted_token != original_token
        assert config.encrypted_token == encrypted_token
        
        # 验证可以正确解密
        decrypted_token = decrypt_sensitive_data(config.encrypted_token)
        assert decrypted_token == original_token
