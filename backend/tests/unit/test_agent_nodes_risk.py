"""
Agent风险评估测试 - test_agent_nodes_risk.py
测试 app/agent/nodes.py 中的风险评估相关功能
目标：实现12个风险评估测试用例，覆盖assess_risk函数及相关功能
"""

import asyncio
import pytest
import uuid
from decimal import Decimal
from unittest.mock import AsyncMock, Mock, patch

from app.agent.nodes import (
    assess_risk,
    route_after_risk_assessment,
    RiskAssessmentEngine,
)
from app.core.schemas import (
    AgentState,
    TradePlan,
    TradeSide,
    OrderType,
)
from app.services.dynamic_risk_manager import (
    DynamicRiskManager,
    RiskLevel,
    RiskCategory,
)


@pytest.mark.unit
@pytest.mark.business_logic
class TestAgentRiskAssessment:
    """Agent风险评估核心功能测试"""

    def setup_method(self):
        """设置测试环境"""
        self.test_user_id = uuid.uuid4()
        self.test_task_id = uuid.uuid4()
        
        # 创建基础测试状态
        self.base_state = AgentState(
            task_id=self.test_task_id,
            user_id=self.test_user_id,
            raw_input="买入 BTC 1000U",
            parsed_intents=[],
            context={
                "risk_config": {
                    "max_concurrent_orders": 5,
                    "max_total_position_value_usd": Decimal("10000"),
                    "default_position_size_usd": Decimal("1000"),
                    "max_position_size_usd": Decimal("5000"),
                    "allowed_symbols": ["BTC/USDT", "ETH/USDT"],
                    "confidence_threshold": Decimal("0.8")
                },
                "active_orders": [],
                "market_prices": {
                    "BTC/USDT": 50000  # 简化格式，直接使用价格
                }
            },
            execution_plan=[
                TradePlan(
                    symbol="BTC/USDT",
                    side=TradeSide.BUY,
                    order_type=OrderType.MARKET,
                    quantity=Decimal("1000")
                )
            ],
            log=[]
        )

    @pytest.mark.asyncio
    async def test_assess_risk_成功通过(self):
        """测试风险评估成功通过"""
        # Arrange
        state = self.base_state.copy()
        mock_db = AsyncMock()
        
        # Mock DynamicRiskManager - 修正数据结构
        mock_assessment = {
            "overall_risk": {
                "overall_score": 30,
                "overall_level": RiskLevel.LOW,
                "message": "风险可控",
                "recommendation": "建议设置止损"
            },
            "risk_assessments": {},
            "recommendations": [
                {
                    "type": "position_adjustment",
                    "priority": "low",
                    "message": "建议设置止损",
                    "action": "set_stop_loss",
                    "parameters": {}
                }
            ],
            "market_conditions": {},
            "assessment_timestamp": "2023-01-01T00:00:00"
        }
        
        with patch('app.agent.nodes.DynamicRiskManager') as mock_drm_class:
            mock_drm = AsyncMock()
            mock_drm.assess_dynamic_risk.return_value = mock_assessment
            mock_drm_class.return_value = mock_drm
            
            # Act
            result = await assess_risk(state, mock_db)
            
            # Assert
            assert result.risk_assessment is not None
            assert result.risk_assessment["passed"] is True
            assert result.risk_assessment["risk_score"] == 30
            # 检查日志中是否包含风险评估相关信息
            log_content = " ".join(result.log)
            assert "风险评估" in log_content or "建议" in log_content

    @pytest.mark.asyncio
    async def test_assess_risk_高风险失败(self):
        """测试高风险情况下评估失败"""
        # Arrange
        state = self.base_state.copy()
        mock_db = AsyncMock()
        
        # Mock DynamicRiskManager返回高风险
        mock_assessment = {
            "overall_risk": {
                "overall_score": 85,
                "overall_level": RiskLevel.CRITICAL,
                "message": "风险过高",
                "recommendation": "立即停止交易"
            },
            "risk_assessments": {},
            "recommendations": [
                {
                    "type": "position_adjustment",
                    "priority": "high",
                    "message": "立即停止交易",
                    "action": "stop_trading",
                    "parameters": {}
                }
            ],
            "market_conditions": {},
            "assessment_timestamp": "2023-01-01T00:00:00"
        }
        
        with patch('app.agent.nodes.DynamicRiskManager') as mock_drm_class:
            mock_drm = AsyncMock()
            mock_drm.assess_dynamic_risk.return_value = mock_assessment
            mock_drm_class.return_value = mock_drm
            
            # Act
            result = await assess_risk(state, mock_db)
            
            # Assert
            assert result.risk_assessment is not None
            assert result.risk_assessment["passed"] is False
            assert result.risk_assessment["risk_score"] == 85
            # 检查日志中是否包含风险评估相关信息
            log_content = " ".join(result.log)
            assert "风险评估" in log_content or "建议" in log_content

    @pytest.mark.asyncio
    async def test_assess_risk_空执行计划(self):
        """测试空执行计划的风险评估"""
        # Arrange
        state = self.base_state.copy()
        state.execution_plan = []
        mock_db = AsyncMock()

        # Act
        result = await assess_risk(state, mock_db)

        # Assert
        assert result.risk_assessment is not None
        assert result.risk_assessment["passed"] is False  # 空执行计划应该失败
        assert result.risk_assessment["risk_score"] == 100
        assert "没有执行计划" in result.log[-1]

    @pytest.mark.asyncio
    async def test_assess_risk_异常处理(self):
        """测试风险评估过程中的异常处理"""
        # Arrange
        state = self.base_state.copy()
        mock_db = AsyncMock()
        
        # Mock DynamicRiskManager抛出异常
        with patch('app.agent.nodes.DynamicRiskManager') as mock_drm_class:
            mock_drm = AsyncMock()
            mock_drm.assess_dynamic_risk.side_effect = Exception("风险评估服务不可用")
            mock_drm_class.return_value = mock_drm
            
            # Act
            result = await assess_risk(state, mock_db)
            
            # Assert
            assert result.risk_assessment is not None
            assert result.risk_assessment["passed"] is False
            assert result.risk_assessment["risk_score"] == 100
            assert "动态风险评估过程出错" in result.risk_assessment["reason"]

    def test_route_after_risk_assessment_通过(self):
        """测试风险评估通过后的路由"""
        # Arrange
        state = self.base_state.copy()
        state.risk_assessment = {
            "passed": True,
            "risk_score": 30,
            "reason": "风险可控"
        }
        
        # Act
        result = route_after_risk_assessment(state)
        
        # Assert
        assert result == "Execute"

    def test_route_after_risk_assessment_失败(self):
        """测试风险评估失败后的路由"""
        # Arrange
        state = self.base_state.copy()
        state.risk_assessment = {
            "passed": False,
            "risk_score": 85,
            "reason": "风险过高"
        }
        
        # Act
        result = route_after_risk_assessment(state)
        
        # Assert
        assert result == "Failure"

    def test_route_after_risk_assessment_日志失败(self):
        """测试通过日志判断风险评估失败的路由"""
        # Arrange
        state = self.base_state.copy()
        state.log = ["开始风险评估", "风险评估失败：超出限制"]
        
        # Act
        result = route_after_risk_assessment(state)
        
        # Assert
        assert result == "Failure"

    def test_route_after_risk_assessment_无评估结果(self):
        """测试无风险评估结果时的路由"""
        # Arrange
        state = self.base_state.copy()
        # 没有设置risk_assessment
        
        # Act
        result = route_after_risk_assessment(state)
        
        # Assert
        assert result == "Execute"


@pytest.mark.unit
@pytest.mark.business_logic
class TestRiskAssessmentEngine:
    """风险评估引擎测试"""

    def setup_method(self):
        """设置测试环境"""
        self.risk_config = {
            "max_concurrent_orders": 5,
            "max_total_position_value_usd": 10000,
            "default_position_size_usd": 1000,
            "max_position_size_usd": 5000,
            "allowed_symbols": ["BTC/USDT", "ETH/USDT"],
            "confidence_threshold": 0.8
        }
        
        self.active_orders = []
        
        self.market_prices = {
            "BTC/USDT": 50000  # 简化格式，直接使用价格
        }
        
        self.execution_plan = [
            TradePlan(
                symbol="BTC/USDT",
                side=TradeSide.BUY,
                order_type=OrderType.MARKET,
                quantity=Decimal("1000")
            )
        ]

    @pytest.mark.asyncio
    async def test_risk_assessment_engine_基础合规检查(self):
        """测试风险评估引擎基础合规检查"""
        # Arrange
        engine = RiskAssessmentEngine(
            self.risk_config,
            self.active_orders,
            self.market_prices
        )
        
        # Act
        result = await engine.assess_execution_plan(self.execution_plan)
        
        # Assert
        assert isinstance(result, dict)
        assert "passed" in result
        assert "risk_score" in result
        assert "details" in result
        assert result["risk_score"] >= 0

    @pytest.mark.asyncio
    async def test_risk_assessment_engine_不允许交易对(self):
        """测试不允许的交易对风险检查"""
        # Arrange
        # 修改执行计划为不允许的交易对
        invalid_plan = [
            TradePlan(
                symbol="DOGE/USDT",  # 不在允许列表中
                side=TradeSide.BUY,
                order_type=OrderType.MARKET,
                quantity=Decimal("1000")
            )
        ]
        
        engine = RiskAssessmentEngine(
            self.risk_config,
            self.active_orders,
            self.market_prices
        )
        
        # Act
        result = await engine.assess_execution_plan(invalid_plan)
        
        # Assert
        assert result["passed"] is False
        assert result["risk_score"] > 0
        # 检查是否有相关的失败检查
        failed_checks = result.get("failed_checks", [])
        assert len(failed_checks) > 0

    @pytest.mark.asyncio
    async def test_risk_assessment_engine_超额头寸(self):
        """测试超额头寸风险检查"""
        # Arrange
        # 创建超额头寸的执行计划
        # 如果BTC价格是50000，那么quantity应该是BTC数量，而不是USD价值
        # 要超过5000 USD限制，需要quantity > 5000/50000 = 0.1 BTC
        large_plan = [
            TradePlan(
                symbol="BTC/USDT",
                side=TradeSide.BUY,
                order_type=OrderType.MARKET,
                quantity=Decimal("0.2")  # 0.2 BTC * 50000 = 10000 USD，超过5000限制
            )
        ]
        
        engine = RiskAssessmentEngine(
            self.risk_config,
            self.active_orders,
            self.market_prices
        )
        
        # Act
        result = await engine.assess_execution_plan(large_plan)
        
        # Assert
        assert result["passed"] is False
        assert result["risk_score"] > 0
        # 检查是否有头寸相关的失败检查
        failed_checks = result.get("failed_checks", [])
        position_checks = [check for check in failed_checks if "头寸" in check.get("category", "") or "头寸" in check.get("reason", "")]
        assert len(position_checks) > 0
