"""
Agent工具测试 - 全面覆盖测试
目标：将覆盖率从15%提升到80%+
"""
import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch, Mock
from decimal import Decimal
from datetime import datetime
import uuid
from sqlalchemy.ext.asyncio import AsyncSession

from app.agent.tools import (
    find_orders_by_criteria,
    get_market_price,
    execute_trade,
    SimulationExchange,
    simulation_exchange
)
from app.core.schemas import TradePlan, TradeResult, OrderStatus, Order
from app.core.models import Order as OrderModel


class TestFindOrdersByCriteria:
    """测试订单查找功能"""

    @pytest.fixture
    def mock_db(self):
        """Mock数据库会话"""
        db = AsyncMock(spec=AsyncSession)
        return db

    @pytest.fixture
    def sample_orders(self):
        """示例订单数据"""
        orders = [
            Mock(
                id=uuid.uuid4(),  # 使用 UUID 而不是整数
                user_id=uuid.uuid4(),
                symbol="BTCUSDT",
                side="buy",
                quantity=Decimal("0.1"),
                entry_price=Decimal("50000"),
                pnl=Decimal("100"),
                status=OrderStatus.CLOSED,  # 使用正确的枚举值
                created_at=datetime.now()
            ),
            Mock(
                id=uuid.uuid4(),  # 使用 UUID 而不是整数
                user_id=uuid.uuid4(),
                symbol="ETHUSDT",
                side="sell",
                quantity=Decimal("1.0"),
                entry_price=Decimal("3000"),
                pnl=Decimal("-50"),
                status=OrderStatus.ACTIVE,  # 使用正确的枚举值
                created_at=datetime.now()
            ),
            Mock(
                id=uuid.uuid4(),  # 使用 UUID 而不是整数
                user_id=uuid.uuid4(),
                symbol="SOLUSDT",
                side="buy",
                quantity=Decimal("10"),
                entry_price=Decimal("100"),
                pnl=Decimal("200"),
                status=OrderStatus.FAILED,  # 使用正确的枚举值
                created_at=datetime.now()
            )
        ]
        return orders

    @pytest.mark.asyncio
    async def test_find_orders_by_btc_criteria(self, mock_db, sample_orders):
        """测试按BTC筛选订单"""
        # Mock数据库查询结果
        mock_result = Mock()
        mock_result.scalars.return_value.all.return_value = sample_orders
        mock_db.execute.return_value = mock_result

        user_id = uuid.uuid4()
        result = await find_orders_by_criteria(user_id, "btc orders", mock_db)

        assert len(result) == 1
        assert result[0].symbol == "BTCUSDT"
        assert isinstance(result[0], Order)

    @pytest.mark.asyncio
    async def test_find_orders_by_eth_criteria(self, mock_db, sample_orders):
        """测试按ETH筛选订单"""
        mock_result = Mock()
        mock_result.scalars.return_value.all.return_value = sample_orders
        mock_db.execute.return_value = mock_result

        user_id = uuid.uuid4()
        result = await find_orders_by_criteria(user_id, "eth positions", mock_db)

        assert len(result) == 1
        assert result[0].symbol == "ETHUSDT"

    @pytest.mark.asyncio
    async def test_find_orders_by_sol_criteria(self, mock_db, sample_orders):
        """测试按SOL筛选订单"""
        mock_result = Mock()
        mock_result.scalars.return_value.all.return_value = sample_orders
        mock_db.execute.return_value = mock_result

        user_id = uuid.uuid4()
        result = await find_orders_by_criteria(user_id, "sol trades", mock_db)

        assert len(result) == 1
        assert result[0].symbol == "SOLUSDT"

    @pytest.mark.asyncio
    async def test_find_orders_by_status_closed(self, mock_db, sample_orders):
        """测试按已关闭状态筛选"""
        mock_result = Mock()
        mock_result.scalars.return_value.all.return_value = sample_orders
        mock_db.execute.return_value = mock_result

        user_id = uuid.uuid4()
        result = await find_orders_by_criteria(user_id, "closed orders", mock_db)

        assert len(result) == 1
        assert result[0].status == OrderStatus.CLOSED  # 使用正确的枚举值

    @pytest.mark.asyncio
    async def test_find_orders_by_status_active(self, mock_db, sample_orders):
        """测试按活跃状态筛选"""
        mock_result = Mock()
        mock_result.scalars.return_value.all.return_value = sample_orders
        mock_db.execute.return_value = mock_result

        user_id = uuid.uuid4()
        result = await find_orders_by_criteria(user_id, "active orders", mock_db)

        assert len(result) == 1
        assert result[0].status == OrderStatus.ACTIVE  # 使用正确的枚举值

    @pytest.mark.asyncio
    async def test_find_orders_by_status_no_match(self, mock_db, sample_orders):
        """测试按不匹配状态筛选时返回所有订单"""
        mock_result = Mock()
        mock_result.scalars.return_value.all.return_value = sample_orders
        mock_db.execute.return_value = mock_result

        user_id = uuid.uuid4()
        result = await find_orders_by_criteria(user_id, "unknown status", mock_db)

        assert len(result) == 3  # 返回所有订单，因为没有匹配的状态筛选

    @pytest.mark.asyncio
    async def test_find_orders_by_side_buy(self, mock_db, sample_orders):
        """测试按买入方向筛选"""
        mock_result = Mock()
        mock_result.scalars.return_value.all.return_value = sample_orders
        mock_db.execute.return_value = mock_result

        user_id = uuid.uuid4()
        result = await find_orders_by_criteria(user_id, "buy orders", mock_db)

        buy_orders = [o for o in result if o.side == "buy"]
        assert len(buy_orders) == 2  # BTC and SOL orders are buy

    @pytest.mark.asyncio
    async def test_find_orders_by_side_sell(self, mock_db, sample_orders):
        """测试按卖出方向筛选"""
        mock_result = Mock()
        mock_result.scalars.return_value.all.return_value = sample_orders
        mock_db.execute.return_value = mock_result

        user_id = uuid.uuid4()
        result = await find_orders_by_criteria(user_id, "sell orders", mock_db)

        sell_orders = [o for o in result if o.side == "sell"]
        assert len(sell_orders) == 1  # ETH order is sell

    @pytest.mark.asyncio
    async def test_find_orders_latest(self, mock_db, sample_orders):
        """测试获取最新订单"""
        mock_result = Mock()
        mock_result.scalars.return_value.all.return_value = sample_orders
        mock_db.execute.return_value = mock_result

        user_id = uuid.uuid4()
        result = await find_orders_by_criteria(user_id, "latest order", mock_db)

        assert len(result) == 1  # Should return only the latest order

    @pytest.mark.asyncio
    async def test_find_orders_no_match_returns_all(self, mock_db, sample_orders):
        """测试无匹配条件时返回所有订单"""
        mock_result = Mock()
        mock_result.scalars.return_value.all.return_value = sample_orders
        mock_db.execute.return_value = mock_result

        user_id = uuid.uuid4()
        result = await find_orders_by_criteria(user_id, "unknown criteria", mock_db)

        assert len(result) == 3  # Should return all orders

    @pytest.mark.asyncio
    async def test_find_orders_empty_result(self, mock_db):
        """测试空结果处理"""
        mock_result = Mock()
        mock_result.scalars.return_value.all.return_value = []
        mock_db.execute.return_value = mock_result

        user_id = uuid.uuid4()
        # 修复参数顺序：user_id, criteria, db
        result = await find_orders_by_criteria(user_id, "any criteria", mock_db)

        assert len(result) == 0


class TestGetMarketPrice:
    """测试市场价格获取功能"""

    @pytest.mark.asyncio
    async def test_get_market_price_simulation_mode(self):
        """测试模拟模式下获取价格"""
        with patch('app.agent.tools.settings') as mock_settings:
            mock_settings.trading.simulation_mode = True
            mock_settings.llm.openai_api_key = "sk-mock-test"

            # Mock simulation exchange - 返回协程对象
            with patch('app.agent.tools.simulation_exchange') as mock_sim_exchange:
                async def mock_get_price(symbol):
                    return Decimal("50000.0")
                mock_sim_exchange.get_price = mock_get_price

                price = await get_market_price("BTC/USDT")

                assert price == Decimal("50000.0")
                # 不能直接断言 async 函数的调用，检查结果即可

    @pytest.mark.asyncio
    async def test_get_market_price_simulation_mode_error(self):
        """测试模拟模式下获取价格出错"""
        with patch('app.agent.tools.settings') as mock_settings:
            mock_settings.trading.simulation_mode = True
            mock_settings.llm.openai_api_key = "sk-mock-test"

            with patch('app.agent.tools.simulation_exchange') as mock_sim_exchange:
                mock_sim_exchange.get_price.side_effect = Exception("Simulation error")

                with pytest.raises(Exception, match="Simulation error"):
                    await get_market_price("BTC/USDT")

    @pytest.mark.asyncio
    async def test_get_market_price_real_mode_success(self):
        """测试真实模式下成功获取价格"""
        with patch('app.agent.tools.settings') as mock_settings:
            mock_settings.trading.simulation_mode = False
            mock_settings.llm.openai_api_key = "sk-real-key"

            with patch('app.agent.tools.ccxt.binance') as mock_binance:
                mock_exchange = AsyncMock()
                mock_exchange.fetch_ticker.return_value = {"last": 50000.0}
                mock_binance.return_value = mock_exchange

                price = await get_market_price("BTC/USDT")

                assert price == Decimal("50000.0")
                mock_exchange.fetch_ticker.assert_called_once_with("BTC/USDT")
                mock_exchange.close.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_market_price_real_mode_error_btc_fallback(self):
        """测试真实模式出错时BTC的备用价格"""
        with patch('app.agent.tools.settings') as mock_settings:
            mock_settings.trading.simulation_mode = False
            mock_settings.llm.openai_api_key = "sk-real-key"

            with patch('app.agent.tools.ccxt.binance') as mock_binance:
                mock_exchange = AsyncMock()
                mock_exchange.fetch_ticker.side_effect = Exception("Network error")
                mock_binance.return_value = mock_exchange

                price = await get_market_price("BTC/USDT")

                assert price == Decimal("68000.0")  # Fallback price for BTC

    @pytest.mark.asyncio
    async def test_get_market_price_real_mode_error_eth_fallback(self):
        """测试真实模式出错时ETH的备用价格"""
        with patch('app.agent.tools.settings') as mock_settings:
            mock_settings.trading.simulation_mode = False
            mock_settings.llm.openai_api_key = "sk-real-key"

            with patch('app.agent.tools.ccxt.binance') as mock_binance:
                mock_exchange = AsyncMock()
                mock_exchange.fetch_ticker.side_effect = Exception("Network error")
                mock_binance.return_value = mock_exchange

                price = await get_market_price("ETH/USDT")

                assert price == Decimal("3500.0")  # Fallback price for ETH

    @pytest.mark.asyncio
    async def test_get_market_price_real_mode_error_sol_fallback(self):
        """测试真实模式出错时SOL的备用价格"""
        with patch('app.agent.tools.settings') as mock_settings:
            mock_settings.trading.simulation_mode = False
            mock_settings.llm.openai_api_key = "sk-real-key"

            with patch('app.agent.tools.ccxt.binance') as mock_binance:
                mock_exchange = AsyncMock()
                mock_exchange.fetch_ticker.side_effect = Exception("Network error")
                mock_binance.return_value = mock_exchange

                price = await get_market_price("SOL/USDT")

                assert price == Decimal("150.0")  # Fallback price for SOL

    @pytest.mark.asyncio
    async def test_get_market_price_real_mode_error_unknown_fallback(self):
        """测试真实模式出错时未知币种的备用价格"""
        with patch('app.agent.tools.settings') as mock_settings:
            mock_settings.trading.simulation_mode = False
            mock_settings.llm.openai_api_key = "sk-real-key"

            with patch('app.agent.tools.ccxt.binance') as mock_binance:
                mock_exchange = AsyncMock()
                mock_exchange.fetch_ticker.side_effect = Exception("Network error")
                mock_binance.return_value = mock_exchange

                price = await get_market_price("UNKNOWN/USDT")

                assert price == Decimal("0.0")  # Fallback price for unknown symbols


class TestSimulationExchange:
    """测试模拟交易所功能"""

    def test_simulation_exchange_init(self):
        """测试模拟交易所初始化"""
        exchange = SimulationExchange()

        assert exchange.orders == {}
        assert "BTC/USDT" in exchange.prices
        assert "ETH/USDT" in exchange.prices
        assert "SOL/USDT" in exchange.prices
        assert exchange.prices["BTC/USDT"] == Decimal("68000.0")
        assert exchange.prices["ETH/USDT"] == Decimal("3500.0")
        assert exchange.prices["SOL/USDT"] == Decimal("150.0")

    @pytest.mark.asyncio
    async def test_simulation_exchange_get_price_success(self):
        """测试模拟交易所成功获取价格"""
        exchange = SimulationExchange()

        btc_price = await exchange.get_price("BTC/USDT")
        eth_price = await exchange.get_price("ETH/USDT")
        sol_price = await exchange.get_price("SOL/USDT")

        assert btc_price == Decimal("68000.0")
        assert eth_price == Decimal("3500.0")
        assert sol_price == Decimal("150.0")

    @pytest.mark.asyncio
    async def test_simulation_exchange_get_price_unknown_symbol(self):
        """测试模拟交易所获取未知币种价格"""
        exchange = SimulationExchange()

        with pytest.raises(ValueError, match="Unknown symbol: UNKNOWN/USDT"):
            await exchange.get_price("UNKNOWN/USDT")


class TestExecuteTrade:
    """测试交易执行功能"""

    @pytest.fixture
    def mock_db(self):
        """Mock数据库会话"""
        return AsyncMock(spec=AsyncSession)

    @pytest.fixture
    def sample_trade_plan(self):
        """示例交易计划"""
        return TradePlan(
            symbol="BTC/USDT",
            side="buy",
            quantity=Decimal("0.1"),
            price=Decimal("50000.0"),
            order_type="market"
        )

    @pytest.fixture
    def sample_exchange_config(self):
        """示例交易所配置"""
        config = Mock()
        config.exchange_name = "binance"
        config.api_key = "encrypted_api_key"
        config.api_secret = "encrypted_api_secret"
        config.is_active = True
        return config

    @pytest.mark.asyncio
    async def test_execute_trade_simulation_mode(self, mock_db, sample_trade_plan):
        """测试模拟模式下执行交易"""
        user_id = uuid.uuid4()

        with patch('app.agent.tools.settings') as mock_settings:
            mock_settings.trading.simulation_mode = True
            mock_settings.llm.openai_api_key = "sk-mock-test"

            # Mock database operations
            mock_db.add = Mock()
            mock_db.commit = AsyncMock()
            mock_db.refresh = AsyncMock()

            result = await execute_trade(sample_trade_plan, user_id, mock_db)

            assert isinstance(result, TradeResult)
            assert result.status == "success"  # 使用正确的属性名
            assert result.order_id is not None
            # TradeResult 没有 executed_quantity 和 executed_price 属性
            # 检查 error_message 为空表示成功
            assert result.error_message is None

    @pytest.mark.asyncio
    async def test_execute_trade_real_mode_no_config(self, mock_db, sample_trade_plan):
        """测试真实模式下无交易所配置"""
        user_id = uuid.uuid4()

        with patch('app.agent.tools.settings') as mock_settings:
            mock_settings.trading.simulation_mode = False
            mock_settings.llm.openai_api_key = "sk-real-key"

            # Mock no exchange config found
            mock_result = Mock()
            mock_result.scalar_one_or_none.return_value = None
            mock_db.execute.return_value = mock_result

            result = await execute_trade(sample_trade_plan, user_id, mock_db)

            assert isinstance(result, TradeResult)
            assert result.status == "failure"  # 使用正确的属性名
            assert result.error_message is not None  # 检查错误信息存在

    @pytest.mark.asyncio
    async def test_execute_trade_real_mode_inactive_config(self, mock_db, sample_trade_plan, sample_exchange_config):
        """测试真实模式下交易所配置未激活"""
        user_id = uuid.uuid4()
        sample_exchange_config.is_active = False

        with patch('app.agent.tools.settings') as mock_settings:
            mock_settings.trading.simulation_mode = False
            mock_settings.llm.openai_api_key = "sk-real-key"

            mock_result = Mock()
            mock_result.scalar_one_or_none.return_value = sample_exchange_config
            mock_db.execute.return_value = mock_result

            result = await execute_trade(sample_trade_plan, user_id, mock_db)

            assert isinstance(result, TradeResult)
            assert result.status == "failure"  # 使用正确的属性名
            assert result.error_message is not None  # 检查错误信息存在

    @pytest.mark.asyncio
    async def test_execute_trade_real_mode_success(self, mock_db, sample_trade_plan, sample_exchange_config):
        """测试真实模式下成功执行交易"""
        user_id = uuid.uuid4()

        with patch('app.agent.tools.settings') as mock_settings:
            mock_settings.trading.simulation_mode = False
            mock_settings.llm.openai_api_key = "sk-real-key"

            # Mock exchange config
            mock_result = Mock()
            mock_result.scalar_one_or_none.return_value = sample_exchange_config
            mock_db.execute.return_value = mock_result

            # Mock credential decryption
            with patch('app.agent.tools.decrypt_api_credentials') as mock_decrypt:
                mock_decrypt.return_value = ("decrypted_key", "decrypted_secret")

                # Mock exchange operations
                with patch('app.agent.tools.ccxt.binance') as mock_binance:
                    mock_exchange = AsyncMock()
                    # 确保返回正确的字符串 ID
                    mock_order_response = {
                        "id": "order_123",  # 字符串 ID
                        "amount": 0.1,
                        "price": 50000.0,
                        "status": "closed",
                        "filled": 0.1
                    }
                    mock_exchange.create_order.return_value = mock_order_response
                    mock_binance.return_value = mock_exchange

                    # Mock database operations
                    mock_db.add = Mock()
                    mock_db.commit = AsyncMock()
                    mock_db.refresh = AsyncMock()

                    result = await execute_trade(sample_trade_plan, user_id, mock_db)

                    assert isinstance(result, TradeResult)
                    assert result.status == "success"  # 使用正确的属性名
                    assert result.order_id == "order_123"
                    # TradeResult 没有 executed_quantity 属性

    @pytest.mark.asyncio
    async def test_execute_trade_real_mode_exchange_error(self, mock_db, sample_trade_plan, sample_exchange_config):
        """测试真实模式下交易所错误"""
        user_id = uuid.uuid4()

        with patch('app.agent.tools.settings') as mock_settings:
            mock_settings.trading.simulation_mode = False
            mock_settings.llm.openai_api_key = "sk-real-key"

            mock_result = Mock()
            mock_result.scalar_one_or_none.return_value = sample_exchange_config
            mock_db.execute.return_value = mock_result

            with patch('app.agent.tools.decrypt_api_credentials') as mock_decrypt:
                mock_decrypt.return_value = ("decrypted_key", "decrypted_secret")

                with patch('app.agent.tools.ccxt.binance') as mock_binance:
                    mock_exchange = AsyncMock()
                    mock_exchange.create_market_buy_order.side_effect = Exception("Exchange error")
                    mock_binance.return_value = mock_exchange

                    result = await execute_trade(sample_trade_plan, user_id, mock_db)

                    assert isinstance(result, TradeResult)
                    assert result.status == "failure"  # 使用正确的属性名
                    assert result.error_message is not None  # 检查错误信息存在

    @pytest.mark.asyncio
    async def test_execute_trade_sell_order(self, mock_db, sample_exchange_config):
        """测试执行卖出订单"""
        user_id = uuid.uuid4()
        sell_plan = TradePlan(
            symbol="BTC/USDT",
            side="sell",
            quantity=Decimal("0.1"),
            price=Decimal("50000.0"),
            order_type="market"
        )

        with patch('app.agent.tools.settings') as mock_settings:
            mock_settings.trading.simulation_mode = False
            mock_settings.llm.openai_api_key = "sk-real-key"

            mock_result = Mock()
            mock_result.scalar_one_or_none.return_value = sample_exchange_config
            mock_db.execute.return_value = mock_result

            with patch('app.agent.tools.decrypt_api_credentials') as mock_decrypt:
                mock_decrypt.return_value = ("decrypted_key", "decrypted_secret")

                with patch('app.agent.tools.ccxt.binance') as mock_binance:
                    mock_exchange = AsyncMock()
                    mock_order_response = {
                        "id": "sell_order_123",
                        "amount": 0.1,
                        "price": 50000.0,
                        "status": "closed",
                        "filled": 0.1
                    }
                    mock_exchange.create_order.return_value = mock_order_response
                    mock_binance.return_value = mock_exchange

                    mock_db.add = Mock()
                    mock_db.commit = AsyncMock()
                    mock_db.refresh = AsyncMock()

                    result = await execute_trade(sell_plan, user_id, mock_db)

                    assert isinstance(result, TradeResult)
                    assert result.status == "success"  # 使用正确的属性名
                    assert result.order_id == "sell_order_123"
                    mock_exchange.create_order.assert_called_once()

    @pytest.mark.asyncio
    async def test_execute_trade_limit_order(self, mock_db, sample_exchange_config):
        """测试执行限价订单"""
        user_id = uuid.uuid4()
        limit_plan = TradePlan(
            symbol="BTC/USDT",
            side="buy",
            quantity=Decimal("0.1"),
            price=Decimal("49000.0"),
            order_type="limit"
        )

        with patch('app.agent.tools.settings') as mock_settings:
            mock_settings.trading.simulation_mode = False
            mock_settings.llm.openai_api_key = "sk-real-key"

            mock_result = Mock()
            mock_result.scalar_one_or_none.return_value = sample_exchange_config
            mock_db.execute.return_value = mock_result

            with patch('app.agent.tools.decrypt_api_credentials') as mock_decrypt:
                mock_decrypt.return_value = ("decrypted_key", "decrypted_secret")

                with patch('app.agent.tools.ccxt.binance') as mock_binance:
                    mock_exchange = AsyncMock()
                    mock_order_response = {
                        "id": "limit_order_123",
                        "amount": 0.1,
                        "price": 49000.0,
                        "status": "open",
                        "filled": 0.0
                    }
                    mock_exchange.create_order.return_value = mock_order_response
                    mock_binance.return_value = mock_exchange

                    mock_db.add = Mock()
                    mock_db.commit = AsyncMock()
                    mock_db.refresh = AsyncMock()

                    result = await execute_trade(limit_plan, user_id, mock_db)

                    assert isinstance(result, TradeResult)
                    assert result.status == "success"  # 使用正确的属性名
                    assert result.order_id == "limit_order_123"
                    # 检查 create_order 被调用（实际实现中使用的是通用的 create_order）
