"""
Core Middleware模块单元测试
测试 app/core/middleware.py 中的所有中间件功能
目标：将覆盖率从0%提升到80%+
"""

import pytest
from unittest.mock import AsyncMock, Mock, patch, MagicMock
import time
import json
from fastapi import HTTPException, Request, Response, status
from fastapi.responses import JSONResponse
from pydantic import ValidationError, BaseModel
from sqlalchemy.exc import IntegrityError, OperationalError
from starlette.middleware.base import BaseHTTPMiddleware

from app.core.middleware import (
    ErrorHandlingMiddleware,
    RequestLoggingMiddleware,
    get_error_response,
    create_success_response
)


class TestErrorHandlingMiddleware:
    """测试错误处理中间件"""

    def setup_method(self):
        """设置测试环境"""
        self.middleware = ErrorHandlingMiddleware(app=Mock())

    @pytest.mark.asyncio
    async def test_dispatch_success_request(self):
        """测试成功请求的处理"""
        # Mock请求对象
        mock_request = Mock(spec=Request)
        mock_request.method = "GET"
        mock_request.url = "http://test.com/api/test"
        mock_request.client = Mock()
        mock_request.client.host = "127.0.0.1"
        mock_request.state = Mock()
        
        # Mock成功响应
        mock_response = Mock(spec=Response)
        mock_response.status_code = 200
        
        # Mock call_next函数
        mock_call_next = AsyncMock(return_value=mock_response)
        
        with patch('app.core.middleware.logger') as mock_logger:
            result = await self.middleware.dispatch(mock_request, mock_call_next)
            
            assert result == mock_response
            assert hasattr(mock_request.state, 'request_id')
            mock_logger.info.assert_called()
            mock_call_next.assert_called_once_with(mock_request)

    @pytest.mark.asyncio
    async def test_dispatch_validation_error(self):
        """测试验证错误的处理"""
        # Mock请求对象
        mock_request = Mock(spec=Request)
        mock_request.method = "POST"
        mock_request.url = "http://test.com/api/test"
        mock_request.client = Mock()
        mock_request.client.host = "127.0.0.1"
        mock_request.state = Mock()
        
        # 创建验证错误
        class TestModel(BaseModel):
            name: str
            age: int
        
        try:
            TestModel(name="test", age="invalid")
        except ValidationError as ve:
            validation_error = ve
        
        # Mock call_next抛出验证错误
        mock_call_next = AsyncMock(side_effect=validation_error)
        
        with patch('app.core.middleware.logger') as mock_logger:
            result = await self.middleware.dispatch(mock_request, mock_call_next)
            
            assert isinstance(result, JSONResponse)
            assert result.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
            mock_logger.warning.assert_called()

    @pytest.mark.asyncio
    async def test_dispatch_http_exception(self):
        """测试HTTP异常的处理"""
        # Mock请求对象
        mock_request = Mock(spec=Request)
        mock_request.method = "GET"
        mock_request.url = "http://test.com/api/test"
        mock_request.client = Mock()
        mock_request.client.host = "127.0.0.1"
        mock_request.state = Mock()
        
        # 创建HTTP异常
        http_exception = HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Resource not found"
        )
        
        # Mock call_next抛出HTTP异常
        mock_call_next = AsyncMock(side_effect=http_exception)
        
        with patch('app.core.middleware.logger') as mock_logger:
            result = await self.middleware.dispatch(mock_request, mock_call_next)
            
            assert isinstance(result, JSONResponse)
            assert result.status_code == status.HTTP_404_NOT_FOUND
            mock_logger.warning.assert_called()

    @pytest.mark.asyncio
    async def test_dispatch_integrity_error(self):
        """测试数据库完整性错误的处理"""
        # Mock请求对象
        mock_request = Mock(spec=Request)
        mock_request.method = "POST"
        mock_request.url = "http://test.com/api/test"
        mock_request.client = Mock()
        mock_request.client.host = "127.0.0.1"
        mock_request.state = Mock()
        
        # 创建完整性错误
        integrity_error = IntegrityError("statement", "params", "orig")
        
        # Mock call_next抛出完整性错误
        mock_call_next = AsyncMock(side_effect=integrity_error)
        
        with patch('app.core.middleware.logger') as mock_logger:
            result = await self.middleware.dispatch(mock_request, mock_call_next)
            
            assert isinstance(result, JSONResponse)
            assert result.status_code == status.HTTP_409_CONFLICT
            mock_logger.error.assert_called()

    @pytest.mark.asyncio
    async def test_dispatch_operational_error(self):
        """测试数据库操作错误的处理"""
        # Mock请求对象
        mock_request = Mock(spec=Request)
        mock_request.method = "GET"
        mock_request.url = "http://test.com/api/test"
        mock_request.client = Mock()
        mock_request.client.host = "127.0.0.1"
        mock_request.state = Mock()
        
        # 创建操作错误
        operational_error = OperationalError("statement", "params", "orig")
        
        # Mock call_next抛出操作错误
        mock_call_next = AsyncMock(side_effect=operational_error)
        
        with patch('app.core.middleware.logger') as mock_logger:
            result = await self.middleware.dispatch(mock_request, mock_call_next)
            
            assert isinstance(result, JSONResponse)
            assert result.status_code == status.HTTP_503_SERVICE_UNAVAILABLE
            mock_logger.error.assert_called()

    @pytest.mark.asyncio
    async def test_dispatch_unknown_error(self):
        """测试未知错误的处理"""
        # Mock请求对象
        mock_request = Mock(spec=Request)
        mock_request.method = "GET"
        mock_request.url = "http://test.com/api/test"
        mock_request.client = Mock()
        mock_request.client.host = "127.0.0.1"
        mock_request.state = Mock()
        
        # 创建未知错误
        unknown_error = Exception("Unexpected error occurred")
        
        # Mock call_next抛出未知错误
        mock_call_next = AsyncMock(side_effect=unknown_error)
        
        with patch('app.core.middleware.logger') as mock_logger:
            result = await self.middleware.dispatch(mock_request, mock_call_next)
            
            assert isinstance(result, JSONResponse)
            assert result.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
            mock_logger.error.assert_called()

    @pytest.mark.asyncio
    async def test_dispatch_request_without_client(self):
        """测试没有客户端信息的请求"""
        # Mock请求对象（无客户端信息）
        mock_request = Mock(spec=Request)
        mock_request.method = "GET"
        mock_request.url = "http://test.com/api/test"
        mock_request.client = None
        mock_request.state = Mock()
        
        # Mock成功响应
        mock_response = Mock(spec=Response)
        mock_response.status_code = 200
        
        # Mock call_next函数
        mock_call_next = AsyncMock(return_value=mock_response)
        
        with patch('app.core.middleware.logger') as mock_logger:
            result = await self.middleware.dispatch(mock_request, mock_call_next)
            
            assert result == mock_response
            # 验证日志记录了None作为客户端IP
            mock_logger.info.assert_called()

    @pytest.mark.asyncio
    async def test_handle_validation_error_method(self):
        """测试验证错误处理方法"""
        # 创建验证错误
        class TestModel(BaseModel):
            name: str
            age: int
        
        try:
            TestModel(name="test", age="invalid")
        except ValidationError as ve:
            validation_error = ve
        
        request_id = "test-request-id"
        
        with patch('app.core.middleware.logger') as mock_logger:
            result = await self.middleware._handle_validation_error(validation_error, request_id)
            
            assert isinstance(result, JSONResponse)
            assert result.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
            
            # 检查响应内容
            content = json.loads(result.body.decode())
            assert content["success"] is False
            assert content["error_code"] == "VALIDATION_ERROR"
            assert content["request_id"] == request_id
            assert "details" in content

    @pytest.mark.asyncio
    async def test_handle_http_exception_method(self):
        """测试HTTP异常处理方法"""
        http_exception = HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Unauthorized access"
        )
        request_id = "test-request-id"
        
        with patch('app.core.middleware.logger') as mock_logger:
            result = await self.middleware._handle_http_exception(http_exception, request_id)
            
            assert isinstance(result, JSONResponse)
            assert result.status_code == status.HTTP_401_UNAUTHORIZED
            
            # 检查响应内容
            content = json.loads(result.body.decode())
            assert content["success"] is False
            assert content["error_code"] == "HTTP_ERROR"
            assert content["message"] == "Unauthorized access"
            assert content["request_id"] == request_id

    @pytest.mark.asyncio
    async def test_handle_database_error_method(self):
        """测试数据库错误处理方法"""
        db_error = IntegrityError("statement", "params", "orig")
        request_id = "test-request-id"
        error_type = "INTEGRITY_ERROR"
        
        with patch('app.core.middleware.logger') as mock_logger:
            result = await self.middleware._handle_database_error(db_error, request_id, error_type)
            
            assert isinstance(result, JSONResponse)
            assert result.status_code == status.HTTP_409_CONFLICT
            
            # 检查响应内容
            content = json.loads(result.body.decode())
            assert content["success"] is False
            assert content["error_code"] == error_type
            assert content["request_id"] == request_id

    @pytest.mark.asyncio
    async def test_handle_unknown_error_method(self):
        """测试未知错误处理方法"""
        unknown_error = Exception("Something went wrong")
        request_id = "test-request-id"
        
        with patch('app.core.middleware.logger') as mock_logger:
            with patch('app.core.middleware.settings') as mock_settings:
                mock_settings.debug = False
                
                result = await self.middleware._handle_unknown_error(unknown_error, request_id)
                
                assert isinstance(result, JSONResponse)
                assert result.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
                
                # 检查响应内容
                content = json.loads(result.body.decode())
                assert content["success"] is False
                assert content["error_code"] == "INTERNAL_ERROR"
                assert content["request_id"] == request_id

    @pytest.mark.asyncio
    async def test_handle_unknown_error_debug_mode(self):
        """测试调试模式下的未知错误处理"""
        unknown_error = Exception("Debug error details")
        request_id = "test-request-id"
        
        with patch('app.core.middleware.logger') as mock_logger:
            with patch('app.core.middleware.settings') as mock_settings:
                mock_settings.debug = True
                
                result = await self.middleware._handle_unknown_error(unknown_error, request_id)
                
                assert isinstance(result, JSONResponse)
                assert result.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
                
                # 在调试模式下应该包含更多错误详情
                content = json.loads(result.body.decode())
                assert content["success"] is False
                assert content["error_code"] == "INTERNAL_ERROR"


class TestRequestLoggingMiddleware:
    """测试请求日志中间件"""

    def setup_method(self):
        """设置测试环境"""
        self.middleware = RequestLoggingMiddleware(app=Mock())

    @pytest.mark.asyncio
    async def test_dispatch_success_logging(self):
        """测试成功请求的日志记录"""
        # Mock请求对象
        mock_request = Mock(spec=Request)
        mock_request.method = "GET"
        mock_request.url = "http://test.com/api/users"
        mock_request.headers = {"User-Agent": "test-agent", "Authorization": "Bearer token"}
        mock_request.client = Mock()
        mock_request.client.host = "***********"

        # Mock响应对象
        mock_response = Mock(spec=Response)
        mock_response.status_code = 200
        mock_response.headers = {"Content-Type": "application/json"}

        # Mock call_next函数
        mock_call_next = AsyncMock(return_value=mock_response)

        with patch('app.core.middleware.logger') as mock_logger:
            with patch('time.time', side_effect=[1000.0, 1000.5]):  # 0.5秒响应时间
                result = await self.middleware.dispatch(mock_request, mock_call_next)

                assert result == mock_response
                # 验证记录了请求和响应日志
                assert mock_logger.info.call_count >= 2
                mock_call_next.assert_called_once_with(mock_request)

    @pytest.mark.asyncio
    async def test_dispatch_with_exception_logging(self):
        """测试异常情况下的日志记录"""
        # Mock请求对象
        mock_request = Mock(spec=Request)
        mock_request.method = "POST"
        mock_request.url = "http://test.com/api/orders"
        mock_request.headers = {"Content-Type": "application/json"}
        mock_request.client = Mock()
        mock_request.client.host = "********"

        # Mock call_next抛出异常
        test_exception = Exception("Test error")
        mock_call_next = AsyncMock(side_effect=test_exception)

        with patch('app.core.middleware.logger') as mock_logger:
            with patch('time.time', side_effect=[2000.0, 2000.3]):
                with pytest.raises(Exception, match="Test error"):
                    await self.middleware.dispatch(mock_request, mock_call_next)

                # 验证记录了错误日志
                mock_logger.error.assert_called()

    @pytest.mark.asyncio
    async def test_dispatch_request_without_client_logging(self):
        """测试无客户端信息请求的日志记录"""
        # Mock请求对象（无客户端信息）
        mock_request = Mock(spec=Request)
        mock_request.method = "GET"
        mock_request.url = "http://test.com/api/health"
        mock_request.headers = {}
        mock_request.client = None

        # Mock响应对象
        mock_response = Mock(spec=Response)
        mock_response.status_code = 200
        mock_response.headers = {}

        # Mock call_next函数
        mock_call_next = AsyncMock(return_value=mock_response)

        with patch('app.core.middleware.logger') as mock_logger:
            result = await self.middleware.dispatch(mock_request, mock_call_next)

            assert result == mock_response
            mock_logger.info.assert_called()

    @pytest.mark.asyncio
    async def test_dispatch_performance_logging(self):
        """测试性能日志记录"""
        # Mock请求对象
        mock_request = Mock(spec=Request)
        mock_request.method = "GET"
        mock_request.url = "http://test.com/api/slow-endpoint"
        mock_request.headers = {}
        mock_request.client = Mock()
        mock_request.client.host = "127.0.0.1"

        # Mock响应对象
        mock_response = Mock(spec=Response)
        mock_response.status_code = 200
        mock_response.headers = {}

        # Mock call_next函数
        mock_call_next = AsyncMock(return_value=mock_response)

        with patch('app.core.middleware.logger') as mock_logger:
            # 模拟慢请求（2秒）
            with patch('time.time', side_effect=[5000.0, 5002.0]):
                result = await self.middleware.dispatch(mock_request, mock_call_next)

                assert result == mock_response
                # 验证记录了响应时间
                mock_logger.info.assert_called()


class TestUtilityFunctions:
    """测试工具函数"""

    def test_get_error_response_basic(self):
        """测试基本错误响应创建"""
        error_code = "TEST_ERROR"
        message = "Test error message"
        status_code = 400

        response = get_error_response(error_code, message, status_code)

        assert isinstance(response, JSONResponse)
        assert response.status_code == status_code

        # 检查响应内容
        content = json.loads(response.body.decode())
        assert content["error"] == error_code
        assert content["message"] == message

    def test_get_error_response_with_details(self):
        """测试带详细信息的错误响应"""
        error_code = "VALIDATION_ERROR"
        message = "Validation failed"
        details = {"field": "email", "issue": "invalid format"}
        request_id = "req-123"

        response = get_error_response(
            error_code, message, 422, details, request_id
        )

        assert isinstance(response, JSONResponse)
        assert response.status_code == 422

        content = json.loads(response.body.decode())
        assert content["error"] == error_code
        assert content["message"] == message
        assert content["details"] == details
        assert content["request_id"] == request_id

    def test_get_error_response_default_status(self):
        """测试默认状态码的错误响应"""
        error_code = "UNKNOWN_ERROR"
        message = "Unknown error occurred"

        response = get_error_response(error_code, message)

        assert isinstance(response, JSONResponse)
        assert response.status_code == 500  # 默认状态码

        content = json.loads(response.body.decode())
        assert content["error"] == error_code
        assert content["message"] == message

    def test_create_success_response_basic(self):
        """测试基本成功响应创建"""
        data = {"user_id": 123, "username": "testuser"}
        message = "User created successfully"

        response = create_success_response(data, message)

        assert isinstance(response, dict)
        assert response["success"] is True
        assert response["message"] == message
        assert response["data"] == data

    def test_create_success_response_with_request_id(self):
        """测试带请求ID的成功响应"""
        data = [{"id": 1, "name": "item1"}, {"id": 2, "name": "item2"}]
        message = "Items retrieved successfully"
        request_id = "req-456"

        response = create_success_response(data, message, request_id)

        assert isinstance(response, dict)
        assert response["success"] is True
        assert response["message"] == message
        assert response["data"] == data
        assert response["request_id"] == request_id

    def test_create_success_response_default_message(self):
        """测试默认消息的成功响应"""
        data = {"result": "ok"}

        response = create_success_response(data)

        assert isinstance(response, dict)
        assert response["success"] is True
        assert response["message"] == "操作成功"  # 默认消息
        assert response["data"] == data

    def test_create_success_response_none_data(self):
        """测试空数据的成功响应"""
        data = None
        message = "Operation completed"

        response = create_success_response(data, message)

        assert isinstance(response, dict)
        assert response["success"] is True
        assert response["message"] == message
        assert response["data"] is None

    def test_create_success_response_complex_data(self):
        """测试复杂数据的成功响应"""
        data = {
            "users": [
                {"id": 1, "name": "Alice", "roles": ["admin", "user"]},
                {"id": 2, "name": "Bob", "roles": ["user"]}
            ],
            "pagination": {
                "page": 1,
                "per_page": 10,
                "total": 2
            }
        }
        message = "Users retrieved with pagination"

        response = create_success_response(data, message)

        assert isinstance(response, dict)
        assert response["success"] is True
        assert response["message"] == message
        assert response["data"] == data
