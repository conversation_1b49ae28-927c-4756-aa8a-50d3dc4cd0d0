"""
Discord组件单元测试
测试Discord相关的各个组件的独立功能
"""
import json
import time
from datetime import datetime, timedelta
from unittest.mock import <PERSON>M<PERSON>, Mock, patch

import pytest

from app.core.config import DiscordSettings
from app.services.discord_listener import MessageDeduplicator, SignalProcessor




class TestDiscordSettings:
    """Discord设置测试"""

    def test_token_validation(self):
        """测试token验证"""
        # 用户token（discord.py-self）
        settings = DiscordSettings(token="user_token_here")
        assert settings.token == "user_token_here"

        # Bot token（传统discord.py）
        settings = DiscordSettings(token="Bot abc123")
        assert settings.token == "Bot abc123"

        # Bearer token
        settings = DiscordSettings(token="Bearer xyz789")
        assert settings.token == "Bearer xyz789"

    def test_basic_settings(self):
        """测试基础设置"""
        settings = DiscordSettings(token="test_token")
        assert settings.token == "test_token"


class TestMessageDeduplicatorAdvanced:
    """消息去重器高级测试"""

    def test_cleanup_expired_hashes(self):
        """测试过期哈希清理"""
        deduplicator = MessageDeduplicator(window_minutes=1)

        # 添加一些消息
        deduplicator.is_duplicate("message1", 1, 1)
        deduplicator.is_duplicate("message2", 1, 1)

        # 手动设置过期时间
        old_time = datetime.utcnow() - timedelta(minutes=2)
        for hash_key in deduplicator.hash_timestamps:
            deduplicator.hash_timestamps[hash_key] = old_time

        # 触发清理
        deduplicator._cleanup_expired_hashes(datetime.utcnow())

        # 应该清理掉过期的哈希
        assert len(deduplicator.hash_timestamps) == 0

    def test_max_cache_size(self):
        """测试最大缓存大小限制"""
        deduplicator = MessageDeduplicator(max_cache_size=3)

        # 添加超过最大缓存大小的消息
        for i in range(5):
            deduplicator.is_duplicate(f"message{i}", 1, 1)

        # 缓存大小应该被限制
        assert len(deduplicator.message_hashes) <= 3


class TestSignalProcessorAdvanced:
    """信号处理器高级测试"""

    def test_excluded_message_patterns(self):
        """测试排除消息模式"""
        processor = SignalProcessor()

        # 测试各种排除模式
        excluded_messages = [
            "hi",
            "hello everyone",
            "good morning",
            "gm",
            "thanks",
            "thank you",
            "lol",
            "haha",
            "😂",
            "?????",
            "yes",
            "no",
            "ok",
            "first",
            "2nd",
            "nice",
            "cool",
            "a",
            "123",
            "!@#$%",
        ]

        for message in excluded_messages:
            assert processor._is_excluded_message(
                message.lower()
            ), f"Should exclude: {message}"

    def test_trading_signal_detection(self):
        """测试交易信号检测"""
        processor = SignalProcessor()

        # 测试交易动作
        trading_actions = [
            "buy btc",
            "sell eth",
            "long ada",
            "short sol",
            "买入 btc",
            "卖出 eth",
        ]
        for action in trading_actions:
            assert processor._contains_trading_signal(
                action.lower()
            ), f"Should detect: {action}"

        # 测试加密货币符号
        crypto_signals = ["btc pump", "eth moon", "ada breakout", "sol to 100"]
        for signal in crypto_signals:
            assert processor._contains_trading_signal(
                signal.lower()
            ), f"Should detect: {signal}"

        # 测试交易术语
        trading_terms = ["target 50k", "tp at 100", "stop loss", "support level"]
        for term in trading_terms:
            assert processor._contains_trading_signal(
                term.lower()
            ), f"Should detect: {term}"

        # 测试价格表达式
        price_expressions = ["$50000", "100 usdt", "1.5k", "10%", "x10"]
        for expr in price_expressions:
            assert processor._contains_trading_signal(
                expr.lower()
            ), f"Should detect: {expr}"

    def test_signal_strength_calculation(self):
        """测试信号强度计算详细逻辑"""
        processor = SignalProcessor()

        # 测试不同强度的信号
        test_cases = [
            ("", 0.0),  # 空消息
            ("hello world", 0.0),  # 无信号
            ("btc", 0.2),  # 只有币种
            ("buy btc", 0.5),  # 动作 + 币种
            ("buy btc at $50k", 0.7),  # 动作 + 币种 + 价格
            ("buy btc at $50k, target $60k, stop loss $45k 🚀", 1.0),  # 完整信号
        ]

        for content, expected_min in test_cases:
            strength = processor._calculate_signal_strength(content)
            assert (
                strength >= expected_min
            ), f"Signal '{content}' should have strength >= {expected_min}, got {strength}"

    def test_extract_signal_metadata_comprehensive(self):
        """测试信号元数据提取的全面功能"""
        processor = SignalProcessor()

        # 创建模拟消息对象
        message = Mock()
        message.id = 123456789
        message.channel.id = 987654321
        message.channel.name = "test-channel"
        message.author.id = 111222333
        message.author.display_name = "TestUser"
        message.author.__str__ = lambda self: "TestUser#1234"
        message.created_at = datetime(2024, 1, 1, 12, 0, 0)
        message.content = "buy btc at $50k 🚀"
        message.attachments = []
        message.embeds = []
        message.reference = None
        message.mentions = []
        message.role_mentions = []
        message.channel_mentions = []

        metadata = processor.extract_signal_metadata(message)

        # 验证基本元数据
        assert metadata["message_id"] == "123456789"
        assert metadata["channel_id"] == "987654321"
        assert metadata["channel_name"] == "test-channel"
        assert metadata["author_id"] == "111222333"
        assert metadata["author_name"] == "TestUser"
        assert metadata["author_username"] == "TestUser#1234"
        assert metadata["content_length"] == len(message.content)
        assert metadata["has_attachments"] is False
        assert metadata["has_embeds"] is False
        assert metadata["is_reply"] is False
        assert metadata["mention_count"] == 0
        assert metadata["signal_strength"] > 0

    def test_extract_signal_metadata_with_attachments(self):
        """测试带附件的消息元数据提取"""
        processor = SignalProcessor()

        # 创建带附件的模拟消息
        message = Mock()
        message.id = 123
        message.channel.id = 456
        message.channel.name = "test"
        message.author.id = 789
        message.author.display_name = "User"
        message.author.__str__ = lambda self: "User#0001"
        message.created_at = datetime.now()
        message.content = "check this chart"
        message.reference = None
        message.mentions = []
        message.role_mentions = []
        message.channel_mentions = []

        # 模拟附件
        attachment = Mock()
        attachment.filename = "chart.png"
        attachment.size = 1024
        attachment.content_type = "image/png"
        attachment.url = "https://example.com/chart.png"
        message.attachments = [attachment]

        # 模拟嵌入
        embed = Mock()
        embed.title = "Market Analysis"
        embed.description = "BTC analysis"
        embed.url = "https://example.com"
        embed.color = Mock()
        embed.color.value = 0x00FF00
        embed.fields = []
        message.embeds = [embed]

        metadata = processor.extract_signal_metadata(message)

        assert metadata["has_attachments"] is True
        assert metadata["has_embeds"] is True
        assert len(metadata["attachments"]) == 1
        assert metadata["attachments"][0]["filename"] == "chart.png"
        assert len(metadata["embeds"]) == 1
        assert metadata["embeds"][0]["title"] == "Market Analysis"


class TestSignalProcessorPerformance:
    """信号处理器性能测试"""

    def test_processing_performance(self):
        """测试处理性能"""
        processor = SignalProcessor()

        # 测试大量消息处理的性能
        messages = [
            "buy btc",
            "sell eth",
            "hello world",
            "target 50k",
            "stop loss 45k",
        ] * 1000

        start_time = time.time()

        for message in messages:
            processor.should_process_message(message)

        end_time = time.time()
        processing_time = end_time - start_time

        # 应该能在合理时间内处理大量消息（这里设置为1秒）
        assert processing_time < 1.0, f"Processing took too long: {processing_time}s"

    def test_signal_strength_performance(self):
        """测试信号强度计算性能"""
        processor = SignalProcessor()

        complex_message = (
            "buy btc at $50000, target $60000, stop loss $45000, 🚀💎 to the moon!"
        )

        start_time = time.time()

        for _ in range(1000):
            processor._calculate_signal_strength(complex_message)

        end_time = time.time()
        processing_time = end_time - start_time

        # 信号强度计算应该很快
        assert (
            processing_time < 0.5
        ), f"Signal strength calculation took too long: {processing_time}s"


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
