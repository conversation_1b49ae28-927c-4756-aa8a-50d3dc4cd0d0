"""
交易所服务单元测试
测试 app/services/exchange.py 中的所有功能
目标：将覆盖率从13%提升到80%+
"""

import pytest
from unittest.mock import AsyncMock, Mock, patch, MagicMock
from decimal import Decimal
from datetime import datetime, timezone
import uuid
import ccxt

from app.services.exchange import ExchangeService
from app.core.schemas import TradePlan, TradeResult, TradeSide, OrderType
from app.core.exceptions import BusinessException


class TestExchangeService:
    """测试交易所服务类"""

    def setup_method(self):
        """设置测试环境"""
        self.exchange_service = ExchangeService()
        self.test_api_key = "test_api_key"
        self.test_api_secret = "test_api_secret"

    def test_init(self):
        """测试初始化"""
        service = ExchangeService()
        assert service._exchanges == {}
        assert "binance" in service._supported_exchanges
        assert "okx" in service._supported_exchanges
        assert "bybit" in service._supported_exchanges

    def test_get_exchange_binance_success(self):
        """测试获取Binance交易所实例成功"""
        # Mock交易所类
        mock_exchange_class = Mock()
        mock_exchange_instance = Mock()
        mock_exchange_class.return_value = mock_exchange_instance

        # 替换支持的交易所字典中的binance类
        original_binance = self.exchange_service._supported_exchanges["binance"]
        self.exchange_service._supported_exchanges["binance"] = mock_exchange_class

        try:
            result = self.exchange_service.get_exchange(
                "binance", self.test_api_key, self.test_api_secret
            )

            assert result == mock_exchange_instance
            mock_exchange_class.assert_called_once_with({
                'apiKey': self.test_api_key,
                'secret': self.test_api_secret,
                'sandbox': False,
                'enableRateLimit': True,
                'options': {'defaultType': 'spot'},
            })
        finally:
            # 恢复原始的交易所类
            self.exchange_service._supported_exchanges["binance"] = original_binance

    def test_get_exchange_sandbox_mode(self):
        """测试沙盒模式获取交易所实例"""
        # Mock交易所类
        mock_exchange_class = Mock()
        mock_exchange_instance = Mock()
        mock_exchange_class.return_value = mock_exchange_instance

        # 替换支持的交易所字典中的binance类
        original_binance = self.exchange_service._supported_exchanges["binance"]
        self.exchange_service._supported_exchanges["binance"] = mock_exchange_class

        try:
            result = self.exchange_service.get_exchange(
                "binance", self.test_api_key, self.test_api_secret, sandbox=True
            )

            assert result == mock_exchange_instance
            mock_exchange_class.assert_called_once_with({
                'apiKey': self.test_api_key,
                'secret': self.test_api_secret,
                'sandbox': True,
                'enableRateLimit': True,
                'options': {'defaultType': 'spot'},
            })
        finally:
            # 恢复原始的交易所类
            self.exchange_service._supported_exchanges["binance"] = original_binance

    def test_get_exchange_unsupported(self):
        """测试获取不支持的交易所"""
        with pytest.raises(ValueError, match="不支持的交易所"):
            self.exchange_service.get_exchange(
                "unsupported_exchange", self.test_api_key, self.test_api_secret
            )

    def test_get_exchange_caching(self):
        """测试交易所实例缓存"""
        # Mock交易所类
        mock_exchange_class = Mock()
        mock_exchange_instance = Mock()
        mock_exchange_class.return_value = mock_exchange_instance

        # 替换支持的交易所字典中的binance类
        original_binance = self.exchange_service._supported_exchanges["binance"]
        self.exchange_service._supported_exchanges["binance"] = mock_exchange_class

        try:
            # 第一次调用
            result1 = self.exchange_service.get_exchange(
                "binance", self.test_api_key, self.test_api_secret
            )

            # 第二次调用应该使用缓存
            result2 = self.exchange_service.get_exchange(
                "binance", self.test_api_key, self.test_api_secret
            )

            assert result1 == result2
            assert result1 == mock_exchange_instance
            # 应该只调用一次构造函数
            mock_exchange_class.assert_called_once()
        finally:
            # 恢复原始的交易所类
            self.exchange_service._supported_exchanges["binance"] = original_binance

    @pytest.mark.asyncio
    async def test_get_market_price_success(self):
        """测试获取市场价格成功"""
        mock_exchange = Mock()  # 使用同步Mock而不是AsyncMock
        mock_exchange.fetch_ticker.return_value = {
            'symbol': 'BTC/USDT',
            'last': 50000.0,
            'bid': 49999.0,
            'ask': 50001.0
        }

        price = await self.exchange_service.get_market_price(mock_exchange, "BTC/USDT")

        assert price == 50000.0
        mock_exchange.fetch_ticker.assert_called_once_with("BTC/USDT")

    @pytest.mark.asyncio
    async def test_get_market_price_exchange_error(self):
        """测试获取市场价格时交易所错误"""
        mock_exchange = AsyncMock()
        mock_exchange.fetch_ticker.side_effect = ccxt.NetworkError("网络错误")

        with pytest.raises(ValueError, match="获取 BTC/USDT 价格失败"):
            await self.exchange_service.get_market_price(mock_exchange, "BTC/USDT")

    @pytest.mark.asyncio
    async def test_execute_trade_market_buy_success(self):
        """测试执行市价买单成功"""
        with patch('app.services.exchange.settings') as mock_settings:
            mock_settings.trading.simulation_mode = False

            # 使用同步Mock避免AsyncMock的复杂性
            mock_exchange = Mock()
            mock_exchange.id = "binance"

            # Mock load_markets方法
            mock_exchange.load_markets.return_value = {
                'BTC/USDT': {
                    'base': 'BTC',
                    'quote': 'USDT',
                    'precision': {'amount': 8, 'price': 2},
                    'limits': {'amount': {'min': 0.001}}
                }
            }

            # Mock fetch_balance方法
            mock_exchange.fetch_balance.return_value = {'USDT': {'free': 10000.0}}

            # Mock create_market_order方法
            mock_exchange.create_market_order.return_value = {
                'id': 'order_123',
                'symbol': 'BTC/USDT',
                'amount': 0.1,
                'price': None,
                'average': 50000.0,
                'status': 'closed',
                'filled': 0.1,
                'cost': 5000.0
            }

            # Mock fetch_order方法
            mock_exchange.fetch_order.return_value = {
                'id': 'order_123',
                'symbol': 'BTC/USDT',
                'amount': 0.1,
                'price': None,
                'average': 50000.0,
                'status': 'closed',
                'filled': 0.1,
                'cost': 5000.0
            }

            trade_plan = TradePlan(
                symbol="BTC/USDT",
                side=TradeSide.BUY,
                quantity=Decimal("0.1"),
                order_type=OrderType.MARKET
            )

            result = await self.exchange_service.execute_trade(mock_exchange, trade_plan)

            assert isinstance(result, TradeResult)
            assert result.status == "success"
            assert result.exchange_order_id == "order_123"
            assert result.quantity == 0.1

    @pytest.mark.asyncio
    async def test_execute_trade_market_sell_success(self):
        """测试执行市价卖单成功"""
        with patch('app.services.exchange.settings') as mock_settings:
            mock_settings.trading.simulation_mode = False

            # 使用同步Mock避免AsyncMock的复杂性
            mock_exchange = Mock()
            mock_exchange.id = "binance"

            # Mock load_markets方法
            mock_exchange.load_markets.return_value = {
                'BTC/USDT': {
                    'base': 'BTC',
                    'quote': 'USDT',
                    'precision': {'amount': 8, 'price': 2},
                    'limits': {'amount': {'min': 0.001}}
                }
            }

            # Mock fetch_balance方法
            mock_exchange.fetch_balance.return_value = {'BTC': {'free': 1.0}}

            # Mock create_market_order方法
            mock_exchange.create_market_order.return_value = {
                'id': 'order_456',
                'symbol': 'BTC/USDT',
                'amount': 0.1,
                'price': None,
                'average': 50000.0,
                'status': 'closed',
                'filled': 0.1,
                'cost': 5000.0
            }

            # Mock fetch_order方法
            mock_exchange.fetch_order.return_value = {
                'id': 'order_456',
                'symbol': 'BTC/USDT',
                'amount': 0.1,
                'price': None,
                'average': 50000.0,
                'status': 'closed',
                'filled': 0.1,
                'cost': 5000.0
            }

            trade_plan = TradePlan(
                symbol="BTC/USDT",
                side=TradeSide.SELL,
                quantity=Decimal("0.1"),
                order_type=OrderType.MARKET
            )

            result = await self.exchange_service.execute_trade(mock_exchange, trade_plan)

            assert isinstance(result, TradeResult)
            assert result.status == "success"
            assert result.exchange_order_id == "order_456"
            assert result.quantity == 0.1

    @pytest.mark.asyncio
    async def test_execute_trade_limit_buy_success(self):
        """测试执行限价买单成功"""
        with patch('app.services.exchange.settings') as mock_settings:
            mock_settings.trading.simulation_mode = False

            # 使用同步Mock避免AsyncMock的复杂性
            mock_exchange = Mock()
            mock_exchange.id = "binance"

            # Mock load_markets方法
            mock_exchange.load_markets.return_value = {
                'BTC/USDT': {
                    'base': 'BTC',
                    'quote': 'USDT',
                    'precision': {'amount': 8, 'price': 2},
                    'limits': {'amount': {'min': 0.001}}
                }
            }

            # Mock fetch_balance方法
            mock_exchange.fetch_balance.return_value = {'USDT': {'free': 10000.0}}

            # Mock create_limit_order方法
            mock_exchange.create_limit_order.return_value = {
                'id': 'order_789',
                'symbol': 'BTC/USDT',
                'amount': 0.1,
                'price': 49000.0,
                'average': 49000.0,
                'status': 'closed',
                'filled': 0.1,
                'cost': 4900.0
            }

            # Mock fetch_order方法
            mock_exchange.fetch_order.return_value = {
                'id': 'order_789',
                'symbol': 'BTC/USDT',
                'amount': 0.1,
                'price': 49000.0,
                'average': 49000.0,
                'status': 'closed',
                'filled': 0.1,
                'cost': 4900.0
            }

            trade_plan = TradePlan(
                symbol="BTC/USDT",
                side=TradeSide.BUY,
                quantity=Decimal("0.1"),
                order_type=OrderType.LIMIT,
                price=Decimal("49000.0")
            )

            result = await self.exchange_service.execute_trade(mock_exchange, trade_plan)

            assert isinstance(result, TradeResult)
            assert result.status == "success"
            assert result.exchange_order_id == "order_789"
            assert result.quantity == 0.1

    @pytest.mark.asyncio
    async def test_execute_trade_simulation_mode(self):
        """测试仿真模式执行交易"""
        with patch('app.services.exchange.settings') as mock_settings:
            mock_settings.simulation_mode = True

            trade_plan = TradePlan(
                symbol="BTC/USDT",
                side=TradeSide.BUY,
                quantity=Decimal("0.1"),
                order_type=OrderType.MARKET
            )

            mock_exchange = AsyncMock()
            mock_exchange.id = "binance"

            result = await self.exchange_service.execute_trade(mock_exchange, trade_plan)

            assert isinstance(result, TradeResult)
            assert result.status == "success"
            assert result.quantity == 0.1

    @pytest.mark.asyncio
    async def test_execute_trade_limit_order(self):
        """测试执行限价单"""
        with patch('app.services.exchange.settings') as mock_settings:
            mock_settings.trading.simulation_mode = False

            # 使用同步Mock避免AsyncMock的复杂性
            mock_exchange = Mock()
            mock_exchange.id = "binance"

            # Mock load_markets方法
            mock_exchange.load_markets.return_value = {
                'BTC/USDT': {
                    'base': 'BTC',
                    'quote': 'USDT',
                    'precision': {'amount': 8, 'price': 2},
                    'limits': {'amount': {'min': 0.001}}
                }
            }

            # Mock fetch_balance方法
            mock_exchange.fetch_balance.return_value = {'USDT': {'free': 10000.0}}

            # Mock create_limit_order方法
            mock_exchange.create_limit_order.return_value = {
                'id': 'order_789',
                'symbol': 'BTC/USDT',
                'amount': 0.1,
                'price': 49000.0,
                'average': 49000.0,
                'status': 'closed',
                'filled': 0.1,
                'cost': 4900.0
            }

            # Mock fetch_order方法
            mock_exchange.fetch_order.return_value = {
                'id': 'order_789',
                'symbol': 'BTC/USDT',
                'amount': 0.1,
                'price': 49000.0,
                'average': 49000.0,
                'status': 'closed',
                'filled': 0.1,
                'cost': 4900.0
            }

            trade_plan = TradePlan(
                symbol="BTC/USDT",
                side=TradeSide.BUY,
                quantity=Decimal("0.1"),
                order_type=OrderType.LIMIT,
                price=Decimal("49000.0")
            )

            result = await self.exchange_service.execute_trade(mock_exchange, trade_plan)

            assert isinstance(result, TradeResult)
            assert result.status == "success"
            assert result.exchange_order_id == "order_789"
            assert result.quantity == 0.1

    @pytest.mark.asyncio
    async def test_get_account_balance_success(self):
        """测试获取账户余额成功"""
        mock_exchange = Mock()  # 使用同步Mock
        mock_exchange.fetch_balance.return_value = {
            'USDT': {'free': 1000.0, 'used': 100.0, 'total': 1100.0},
            'BTC': {'free': 0.5, 'used': 0.1, 'total': 0.6}
        }

        balance = await self.exchange_service.get_account_balance(mock_exchange)

        assert balance['USDT']['free'] == 1000.0
        assert balance['BTC']['total'] == 0.6
        mock_exchange.fetch_balance.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_account_balance_specific_currency(self):
        """测试获取特定货币余额"""
        mock_exchange = Mock()  # 使用同步Mock
        mock_exchange.fetch_balance.return_value = {
            'USDT': {'free': 1000.0, 'used': 100.0, 'total': 1100.0}
        }

        balance = await self.exchange_service.get_account_balance(mock_exchange, "USDT")

        assert balance['free'] == 1000.0  # 修正断言，因为返回的是特定货币的余额
        mock_exchange.fetch_balance.assert_called_once()

    def test_normalize_symbol(self):
        """测试交易对符号标准化"""
        # 测试私有方法通过公共方法
        assert self.exchange_service._normalize_symbol("BTCUSDT") == "BTC/USDT"
        assert self.exchange_service._normalize_symbol("BTC/USDT") == "BTC/USDT"
        assert self.exchange_service._normalize_symbol("ETHUSDT") == "ETH/USDT"

    def test_adjust_quantity_for_precision(self):
        """测试数量精度调整"""
        market_info = {'precision': {'amount': 3}}

        adjusted = self.exchange_service._adjust_quantity(0.123456, market_info)

        assert adjusted == 0.123

    @pytest.mark.asyncio
    async def test_get_ticker_success(self):
        """测试获取ticker数据成功"""
        # 直接测试仿真模式，因为这样更简单且稳定
        with patch('app.services.exchange.settings') as mock_settings:
            mock_settings.simulation_mode = True

            ticker = await self.exchange_service.get_ticker("BTC/USDT")

            assert ticker['symbol'] == 'BTC/USDT'
            assert isinstance(ticker['last'], float)
            assert ticker['last'] > 0


class TestExchangeServiceAdditional:
    """额外的测试用例以提升覆盖率"""

    def setup_method(self):
        """设置测试环境"""
        self.exchange_service = ExchangeService()

    def test_get_exchange_unsupported_exchange(self):
        """测试不支持的交易所"""
        with pytest.raises(ValueError, match="不支持的交易所"):
            self.exchange_service.get_exchange("unsupported_exchange", "key", "secret")

    @pytest.mark.asyncio
    async def test_execute_trade_with_exchange_error(self):
        """测试交易执行时的交易所错误"""
        with patch('app.services.exchange.settings') as mock_settings:
            mock_settings.trading.simulation_mode = False

            mock_exchange = Mock()
            mock_exchange.id = "binance"
            mock_exchange.load_markets.side_effect = Exception("Exchange API error")

            trade_plan = TradePlan(
                symbol="BTC/USDT",
                side=TradeSide.BUY,
                quantity=Decimal("0.1"),
                order_type=OrderType.MARKET
            )

            result = await self.exchange_service.execute_trade(mock_exchange, trade_plan)

            assert isinstance(result, TradeResult)
            assert result.status == "failure"
            assert "Exchange API error" in result.error_message

    @pytest.mark.asyncio
    async def test_execute_trade_with_network_error(self):
        """测试交易执行时的网络错误"""
        with patch('app.services.exchange.settings') as mock_settings:
            mock_settings.trading.simulation_mode = False

            mock_exchange = Mock()
            mock_exchange.id = "binance"
            mock_exchange.load_markets.side_effect = ccxt.NetworkError("Network timeout")

            trade_plan = TradePlan(
                symbol="BTC/USDT",
                side=TradeSide.BUY,
                quantity=Decimal("0.1"),
                order_type=OrderType.MARKET
            )

            result = await self.exchange_service.execute_trade(mock_exchange, trade_plan)

            assert isinstance(result, TradeResult)
            assert result.status == "failure"
            assert "网络错误" in result.error_message

    @pytest.mark.asyncio
    async def test_execute_trade_with_insufficient_funds(self):
        """测试交易执行时的余额不足错误"""
        with patch('app.services.exchange.settings') as mock_settings:
            mock_settings.trading.simulation_mode = False

            mock_exchange = Mock()
            mock_exchange.id = "binance"
            mock_exchange.load_markets.side_effect = ccxt.InsufficientFunds("Insufficient balance")

            trade_plan = TradePlan(
                symbol="BTC/USDT",
                side=TradeSide.BUY,
                quantity=Decimal("0.1"),
                order_type=OrderType.MARKET
            )

            result = await self.exchange_service.execute_trade(mock_exchange, trade_plan)

            assert isinstance(result, TradeResult)
            assert result.status == "failure"
            assert "Insufficient balance" in result.error_message

    def test_extract_fees(self):
        """测试费用提取功能"""
        order_data = {
            'fee': {'currency': 'USDT', 'cost': 5.0},
            'fees': [{'currency': 'USDT', 'cost': 5.0}]
        }

        fees = self.exchange_service._extract_fees(order_data)

        assert isinstance(fees, dict)
        assert 'USDT' in fees
        assert fees['USDT'] == 5.0

    def test_extract_fees_no_fee_data(self):
        """测试没有费用数据的情况"""
        order_data = {}

        fees = self.exchange_service._extract_fees(order_data)

        assert fees == {}

    def test_calculate_slippage(self):
        """测试滑点计算功能"""
        trade_plan = TradePlan(
            symbol="BTC/USDT",
            side=TradeSide.BUY,
            quantity=Decimal("0.1"),
            order_type=OrderType.MARKET,
            price=Decimal("50000")
        )

        order_data = {
            'average': 50500.0,  # 实际成交价格
            'price': 50000.0     # 预期价格
        }

        slippage = self.exchange_service._calculate_slippage(trade_plan, order_data)

        assert isinstance(slippage, float)
        assert slippage > 0  # 买入时价格上涨，滑点为正

    def test_calculate_slippage_no_price_data(self):
        """测试没有价格数据时的滑点计算"""
        trade_plan = TradePlan(
            symbol="BTC/USDT",
            side=TradeSide.BUY,
            quantity=Decimal("0.1"),
            order_type=OrderType.MARKET
        )

        order_data = {}

        slippage = self.exchange_service._calculate_slippage(trade_plan, order_data)

        assert slippage == 0.0

    def test_adjust_quantity_edge_cases(self):
        """测试数量调整的边界情况"""
        market_info = {
            'precision': {'amount': 3},
            'limits': {'amount': {'min': 0.001, 'max': 1000.0}}
        }

        # 测试最小值调整
        adjusted = self.exchange_service._adjust_quantity(0.0005, market_info)
        assert adjusted == 0.001  # 应该调整到最小值

        # 测试最大值调整
        adjusted = self.exchange_service._adjust_quantity(2000.0, market_info)
        assert adjusted == 1000.0  # 应该调整到最大值

        # 测试精度调整
        adjusted = self.exchange_service._adjust_quantity(0.123456, market_info)
        assert adjusted == 0.123  # 应该调整到3位小数

    def test_adjust_quantity_no_limits(self):
        """测试没有限制信息时的数量调整"""
        market_info = {}

        adjusted = self.exchange_service._adjust_quantity(0.123456, market_info)
        assert adjusted == 0.123456  # 应该保持原值

    @pytest.mark.asyncio
    async def test_get_ticker_simulation_mode(self):
        """测试仿真模式获取ticker数据"""
        with patch('app.services.exchange.settings') as mock_settings:
            mock_settings.simulation_mode = True

            ticker = await self.exchange_service.get_ticker("BTC/USDT")

            assert ticker['symbol'] == 'BTC/USDT'
            assert isinstance(ticker['last'], float)
            assert ticker['last'] > 0

    @pytest.mark.asyncio
    async def test_get_orderbook_success(self):
        """测试获取订单簿数据成功"""
        with patch('app.services.exchange.settings') as mock_settings:
            mock_settings.simulation_mode = True

            orderbook = await self.exchange_service.get_orderbook("BTC/USDT", 10)

            assert 'bids' in orderbook
            assert 'asks' in orderbook
            assert len(orderbook['bids']) <= 10
            assert len(orderbook['asks']) <= 10
