"""
Core Cache模块单元测试
测试 app/core/cache.py 中的所有缓存功能
目标：将覆盖率从0%提升到80%+
"""
import pytest
import asyncio
import time
import json
import pickle
from unittest.mock import Mock, patch, AsyncMock, MagicMock
from datetime import datetime, timedelta
from decimal import Decimal
from typing import Dict, Any

from app.core.cache import (
    CacheService,
    cache_service,
    get_cached_market_price,
    cache_market_price,
    get_cached_user_config,
    cache_user_config,
    invalidate_user_cache,
    get_cache_health
)


class TestCacheService:
    """Test cache service functionality"""

    @pytest.fixture
    def cache_service(self):
        return CacheService()

    async def test_set_and_get(self, cache_service):
        """Test basic set and get operations"""
        await cache_service.set("test_key", "test_value", ttl=60)
        result = await cache_service.get("test_key")
        assert result == "test_value"

    async def test_get_nonexistent_key(self, cache_service):
        """Test getting non-existent key"""
        result = await cache_service.get("nonexistent")
        assert result is None

    async def test_delete_key(self, cache_service):
        """Test key deletion"""
        await cache_service.set("delete_key", "value")
        await cache_service.delete("delete_key")
        result = await cache_service.get("delete_key")
        assert result is None

    async def test_invalidate_pattern(self, cache_service):
        """Test pattern-based cache invalidation"""
        await cache_service.set("user:123:config", "value1")
        await cache_service.set("user:123:orders", "value2")

        # Test invalidation (may not work in memory cache mode)
        count = await cache_service.invalidate_pattern("user:123:*")
        # Should not raise any exceptions
        assert count >= 0

    async def test_exists(self, cache_service):
        """Test key existence check"""
        await cache_service.set("exists_key", "value")
        assert await cache_service.exists("exists_key") is True
        assert await cache_service.exists("nonexistent") is False

    async def test_complex_object_serialization(self, cache_service):
        """Test complex object serialization"""
        complex_obj = {
            "list": [1, 2, 3],
            "dict": {"nested": True},
            "number": 42.5
        }

        await cache_service.set("complex", complex_obj)
        result = await cache_service.get("complex")
        assert result == complex_obj

    async def test_ttl_functionality(self, cache_service):
        """Test TTL functionality"""
        # Set with short TTL
        await cache_service.set("ttl_key", "value", ttl=1)

        # Should exist immediately
        result = await cache_service.get("ttl_key")
        assert result == "value"

        # Wait for expiration
        await asyncio.sleep(1.1)
        result = await cache_service.get("ttl_key")
        assert result is None


class TestCacheEdgeCases:
    """Test cache edge cases and error handling"""

    @pytest.fixture
    def cache_service(self):
        return CacheService()

    async def test_large_value_storage(self, cache_service):
        """Test storing large values"""
        large_value = "x" * 1000  # 1KB string
        await cache_service.set("large_key", large_value)
        result = await cache_service.get("large_key")
        assert result == large_value

    async def test_concurrent_access(self, cache_service):
        """Test concurrent cache access"""
        async def set_value(key, value):
            await cache_service.set(key, value)

        async def get_value(key):
            return await cache_service.get(key)

        # Concurrent operations
        await asyncio.gather(
            set_value("key1", "value1"),
            set_value("key2", "value2"),
            get_value("key1"),
            get_value("key2")
        )

        # Verify final state
        assert await cache_service.get("key1") == "value1"
        assert await cache_service.get("key2") == "value2"

    async def test_cache_key_collision_handling(self, cache_service):
        """Test cache key collision handling"""
        # Set multiple values with similar keys
        await cache_service.set("user:123", "value1")
        await cache_service.set("user:1234", "value2")

        # Verify no collision
        assert await cache_service.get("user:123") == "value1"
        assert await cache_service.get("user:1234") == "value2"

    async def test_error_handling(self, cache_service):
        """Test error handling in cache operations"""
        # Test with None value
        await cache_service.set("none_key", None)
        result = await cache_service.get("none_key")
        assert result is None

        # Test with empty string
        await cache_service.set("empty_key", "")
        result = await cache_service.get("empty_key")
        assert result == ""


class TestCacheServiceInit:
    """测试缓存服务初始化"""

    def test_cache_service_initialization(self):
        """测试缓存服务初始化"""
        cache = CacheService()
        assert cache is not None
        assert hasattr(cache, '_memory_cache')
        assert hasattr(cache, '_memory_cache_ttl')
        assert cache.is_connected() is True

    def test_cache_close(self):
        """测试缓存关闭"""
        cache = CacheService()
        cache.close()
        # 验证缓存已清理
        assert len(cache._memory_cache) == 0
        assert len(cache._memory_cache_ttl) == 0


class TestCacheServiceMemoryOperations:
    """测试内存缓存操作"""

    def setup_method(self):
        """设置测试环境"""
        self.cache_service = CacheService()
        # 确保使用内存缓存
        self.cache_service.redis = None
        self.cache_service._connected = False

    def test_is_memory_cache_expired_no_key(self):
        """测试内存缓存过期检查 - 无键"""
        result = self.cache_service._is_memory_cache_expired("nonexistent_key")
        assert result is True

    def test_is_memory_cache_expired_not_expired(self):
        """测试内存缓存过期检查 - 未过期"""
        key = "test_key"
        future_time = time.time() + 3600  # 1小时后过期
        self.cache_service._memory_cache_ttl[key] = future_time

        result = self.cache_service._is_memory_cache_expired(key)
        assert result is False

    def test_is_memory_cache_expired_expired(self):
        """测试内存缓存过期检查 - 已过期"""
        key = "test_key"
        past_time = time.time() - 3600  # 1小时前过期
        self.cache_service._memory_cache_ttl[key] = past_time

        result = self.cache_service._is_memory_cache_expired(key)
        assert result is True

    def test_clean_expired_memory_cache(self):
        """测试清理过期内存缓存"""
        # 设置一些测试数据
        current_time = time.time()
        expired_key = "expired_key"
        valid_key = "valid_key"

        self.cache_service._memory_cache[expired_key] = "expired_value"
        self.cache_service._memory_cache[valid_key] = "valid_value"
        self.cache_service._memory_cache_ttl[expired_key] = current_time - 3600  # 过期
        self.cache_service._memory_cache_ttl[valid_key] = current_time + 3600   # 未过期

        self.cache_service._clean_expired_memory_cache()

        assert expired_key not in self.cache_service._memory_cache
        assert expired_key not in self.cache_service._memory_cache_ttl
        assert valid_key in self.cache_service._memory_cache
        assert valid_key in self.cache_service._memory_cache_ttl

    def test_is_connected_memory_cache(self):
        """测试连接状态检查 - 内存缓存总是连接"""
        result = self.cache_service.is_connected()
        assert result is True




class TestCacheServiceMemoryOperations:
    """测试内存缓存操作"""

    def setup_method(self):
        """设置测试环境"""
        self.cache_service = CacheService()

    @pytest.mark.asyncio
    async def test_set_memory_cache_with_ttl(self):
        """测试设置内存缓存（带TTL）"""
        key = "test_key"
        value = {"test": "value"}
        ttl = 300

        result = await self.cache_service.set(key, value, ttl)

        assert result is True
        assert self.cache_service._memory_cache[key] == value
        assert key in self.cache_service._memory_cache_ttl

    @pytest.mark.asyncio
    async def test_set_memory_cache_without_ttl(self):
        """测试设置内存缓存（无TTL）"""
        key = "test_key"
        value = {"test": "value"}

        result = await self.cache_service.set(key, value)

        assert result is True
        assert self.cache_service._memory_cache[key] == value
        assert key in self.cache_service._memory_cache_ttl

    @pytest.mark.asyncio
    async def test_delete_memory_cache(self):
        """测试删除内存缓存"""
        key = "test_key"
        self.cache_service._memory_cache[key] = "value"
        self.cache_service._memory_cache_ttl[key] = time.time() + 3600

        result = await self.cache_service.delete(key)

        assert result is True
        assert key not in self.cache_service._memory_cache
        assert key not in self.cache_service._memory_cache_ttl

    @pytest.mark.asyncio
    async def test_delete_memory_cache_not_found(self):
        """测试删除内存缓存（键不存在）"""
        result = await self.cache_service.delete("nonexistent_key")

        assert result is False

    @pytest.mark.asyncio
    async def test_exists_memory_cache(self):
        """测试检查内存缓存存在"""
        self.cache_service.redis = None
        self.cache_service._connected = False

        key = "test_key"
        self.cache_service._memory_cache[key] = "value"
        self.cache_service._memory_cache_ttl[key] = time.time() + 3600

        result = await self.cache_service.exists(key)

        assert result is True

    @pytest.mark.asyncio
    async def test_exists_memory_cache_expired(self):
        """测试检查内存缓存过期"""
        self.cache_service.redis = None
        self.cache_service._connected = False

        key = "test_key"
        self.cache_service._memory_cache[key] = "value"
        self.cache_service._memory_cache_ttl[key] = time.time() - 3600  # 过期

        result = await self.cache_service.exists(key)

        assert result is False


class TestCacheServiceBusinessLogic:
    """测试缓存服务业务逻辑"""

    def setup_method(self):
        """设置测试环境"""
        self.cache_service = CacheService()

    @pytest.mark.asyncio
    async def test_get_market_price_success(self):
        """测试获取市场价格缓存成功"""
        with patch.object(self.cache_service, 'get', return_value="50000.50") as mock_get:
            result = await self.cache_service.get_market_price("BTC/USDT")

            assert result == Decimal("50000.50")
            mock_get.assert_called_once_with("market_price:BTC/USDT")

    @pytest.mark.asyncio
    async def test_get_market_price_none(self):
        """测试获取市场价格缓存为空"""
        with patch.object(self.cache_service, 'get', return_value=None) as mock_get:
            result = await self.cache_service.get_market_price("BTC/USDT")

            assert result is None

    @pytest.mark.asyncio
    async def test_get_market_price_invalid_decimal(self):
        """测试获取市场价格缓存无效数值"""
        with patch.object(self.cache_service, 'get', return_value="invalid_price") as mock_get:
            result = await self.cache_service.get_market_price("BTC/USDT")

            assert result is None

    @pytest.mark.asyncio
    async def test_set_market_price_success(self):
        """测试设置市场价格缓存成功"""
        with patch.object(self.cache_service, 'set', return_value=True) as mock_set:
            result = await self.cache_service.set_market_price("BTC/USDT", Decimal("50000.50"), 60)

            assert result is True
            mock_set.assert_called_once_with("market_price:BTC/USDT", "50000.50", 60, "json")

    @pytest.mark.asyncio
    async def test_set_market_price_default_ttl(self):
        """测试设置市场价格缓存（默认TTL）"""
        with patch.object(self.cache_service, 'set', return_value=True) as mock_set:
            result = await self.cache_service.set_market_price("BTC/USDT", Decimal("50000.50"))

            assert result is True
            mock_set.assert_called_once_with("market_price:BTC/USDT", "50000.50", 30, "json")

    @pytest.mark.asyncio
    async def test_get_user_config_success(self):
        """测试获取用户配置缓存成功"""
        expected_config = {"theme": "dark", "language": "zh"}
        with patch.object(self.cache_service, 'get', return_value=expected_config) as mock_get:
            result = await self.cache_service.get_user_config(123, "ui")

            assert result == expected_config
            mock_get.assert_called_once_with("user_config:123:ui")

    @pytest.mark.asyncio
    async def test_set_user_config_success(self):
        """测试设置用户配置缓存成功"""
        config = {"theme": "dark", "language": "zh"}
        with patch.object(self.cache_service, 'set', return_value=True) as mock_set:
            result = await self.cache_service.set_user_config(123, "ui", config, 600)

            assert result is True
            mock_set.assert_called_once_with("user_config:123:ui", config, 600)

    @pytest.mark.asyncio
    async def test_set_user_config_default_ttl(self):
        """测试设置用户配置缓存（默认TTL）"""
        config = {"theme": "dark", "language": "zh"}
        with patch.object(self.cache_service, 'set', return_value=True) as mock_set:
            result = await self.cache_service.set_user_config(123, "ui", config)

            assert result is True
            mock_set.assert_called_once_with("user_config:123:ui", config, 300)

    @pytest.mark.asyncio
    async def test_get_exchange_status_success(self):
        """测试获取交易所状态缓存成功"""
        expected_status = {"status": "online", "latency": 50}
        with patch.object(self.cache_service, 'get', return_value=expected_status) as mock_get:
            result = await self.cache_service.get_exchange_status("binance")

            assert result == expected_status
            mock_get.assert_called_once_with("exchange_status:binance")

    @pytest.mark.asyncio
    async def test_set_exchange_status_success(self):
        """测试设置交易所状态缓存成功"""
        status = {"status": "online", "latency": 50}
        with patch.object(self.cache_service, 'set', return_value=True) as mock_set:
            result = await self.cache_service.set_exchange_status("binance", status, 120)

            assert result is True
            mock_set.assert_called_once_with("exchange_status:binance", status, 120)

    @pytest.mark.asyncio
    async def test_set_exchange_status_default_ttl(self):
        """测试设置交易所状态缓存（默认TTL）"""
        status = {"status": "online", "latency": 50}
        with patch.object(self.cache_service, 'set', return_value=True) as mock_set:
            result = await self.cache_service.set_exchange_status("binance", status)

            assert result is True
            mock_set.assert_called_once_with("exchange_status:binance", status, 60)

    @pytest.mark.asyncio
    async def test_invalidate_pattern_memory_cache(self):
        """测试内存缓存模式删除"""
        # 设置一些测试数据
        self.cache_service._memory_cache["user:123:config"] = "config_data"
        self.cache_service._memory_cache["user:123:orders"] = "orders_data"
        self.cache_service._memory_cache["user:456:config"] = "other_config"

        result = await self.cache_service.invalidate_pattern("user:123:*")

        assert result == 2
        assert "user:123:config" not in self.cache_service._memory_cache
        assert "user:123:orders" not in self.cache_service._memory_cache
        assert "user:456:config" in self.cache_service._memory_cache

    @pytest.mark.asyncio
    async def test_get_cache_stats_memory_cache(self):
        """测试获取内存缓存统计"""
        # 添加一些测试数据
        self.cache_service._memory_cache["key1"] = "value1"
        self.cache_service._memory_cache["key2"] = "value2"

        result = await self.cache_service.get_cache_stats()

        assert result["connected"] is True
        assert result["cache_type"] == "memory"
        assert result["total_keys"] == 2

    def test_close_memory_cache(self):
        """测试关闭内存缓存"""
        # 添加一些测试数据
        self.cache_service._memory_cache["key1"] = "value1"
        self.cache_service._memory_cache_ttl["key1"] = time.time() + 3600

        self.cache_service.close()

        # 验证缓存已清理
        assert len(self.cache_service._memory_cache) == 0
        assert len(self.cache_service._memory_cache_ttl) == 0


class TestCacheConvenienceFunctions:
    """测试缓存便捷函数"""

    @pytest.mark.asyncio
    @patch('app.core.cache.cache_service')
    async def test_get_cached_market_price(self, mock_cache_service):
        """测试获取缓存市场价格便捷函数"""
        expected_price = Decimal("50000.50")
        mock_cache_service.get_market_price = AsyncMock(return_value=expected_price)

        result = await get_cached_market_price("BTC/USDT")

        assert result == expected_price
        mock_cache_service.get_market_price.assert_called_once_with("BTC/USDT")

    @pytest.mark.asyncio
    @patch('app.core.cache.cache_service')
    async def test_cache_market_price(self, mock_cache_service):
        """测试缓存市场价格便捷函数"""
        mock_cache_service.set_market_price = AsyncMock(return_value=True)

        result = await cache_market_price("BTC/USDT", Decimal("50000.50"), 60)

        assert result is True
        mock_cache_service.set_market_price.assert_called_once_with("BTC/USDT", Decimal("50000.50"), 60)

    @pytest.mark.asyncio
    @patch('app.core.cache.cache_service')
    async def test_cache_market_price_default_ttl(self, mock_cache_service):
        """测试缓存市场价格便捷函数（默认TTL）"""
        mock_cache_service.set_market_price = AsyncMock(return_value=True)

        result = await cache_market_price("BTC/USDT", Decimal("50000.50"))

        assert result is True
        mock_cache_service.set_market_price.assert_called_once_with("BTC/USDT", Decimal("50000.50"), 30)

    @pytest.mark.asyncio
    @patch('app.core.cache.cache_service')
    async def test_get_cached_user_config(self, mock_cache_service):
        """测试获取缓存用户配置便捷函数"""
        expected_config = {"theme": "dark", "language": "zh"}
        mock_cache_service.get_user_config = AsyncMock(return_value=expected_config)

        result = await get_cached_user_config(123, "ui")

        assert result == expected_config
        mock_cache_service.get_user_config.assert_called_once_with(123, "ui")

    @pytest.mark.asyncio
    @patch('app.core.cache.cache_service')
    async def test_cache_user_config(self, mock_cache_service):
        """测试缓存用户配置便捷函数"""
        config = {"theme": "dark", "language": "zh"}
        mock_cache_service.set_user_config = AsyncMock(return_value=True)

        result = await cache_user_config(123, "ui", config, 600)

        assert result is True
        mock_cache_service.set_user_config.assert_called_once_with(123, "ui", config, 600)

    @pytest.mark.asyncio
    @patch('app.core.cache.cache_service')
    async def test_cache_user_config_default_ttl(self, mock_cache_service):
        """测试缓存用户配置便捷函数（默认TTL）"""
        config = {"theme": "dark", "language": "zh"}
        mock_cache_service.set_user_config = AsyncMock(return_value=True)

        result = await cache_user_config(123, "ui", config)

        assert result is True
        mock_cache_service.set_user_config.assert_called_once_with(123, "ui", config, 300)

    @pytest.mark.asyncio
    @patch('app.core.cache.cache_service')
    async def test_invalidate_user_cache(self, mock_cache_service):
        """测试清除用户缓存便捷函数"""
        mock_cache_service.invalidate_pattern = AsyncMock(return_value=3)

        result = await invalidate_user_cache(123)

        assert result == 3
        mock_cache_service.invalidate_pattern.assert_called_once_with("user_config:123:*")

    @pytest.mark.asyncio
    @patch('app.core.cache.cache_service')
    async def test_get_cache_health(self, mock_cache_service):
        """测试获取缓存健康状态便捷函数"""
        expected_stats = {"connected": True, "memory_usage": 1024000}
        mock_cache_service.get_cache_stats = AsyncMock(return_value=expected_stats)

        result = await get_cache_health()

        assert result == expected_stats
        mock_cache_service.get_cache_stats.assert_called_once()


class TestCacheSerializationMethods:
    """测试缓存序列化方法"""

    def setup_method(self):
        """设置测试环境"""
        self.cache_service = CacheService()

    @pytest.mark.asyncio
    async def test_set_get_json_serialization(self):
        """测试JSON序列化"""
        self.cache_service.redis = None
        self.cache_service._connected = False

        key = "json_test"
        value = {"number": 42, "string": "test", "list": [1, 2, 3]}

        await self.cache_service.set(key, value, serialize_method="json")
        result = await self.cache_service.get(key)

        assert result == value

    @pytest.mark.asyncio
    async def test_set_get_pickle_serialization(self):
        """测试Pickle序列化"""
        self.cache_service.redis = None
        self.cache_service._connected = False

        key = "pickle_test"
        value = {"decimal": Decimal("123.45"), "datetime": datetime.now()}

        await self.cache_service.set(key, value, serialize_method="pickle")
        result = await self.cache_service.get(key)

        assert result["decimal"] == value["decimal"]
        assert isinstance(result["datetime"], datetime)
