"""
Agent状态机测试 - test_agent_graph.py
测试 app/agent/graph.py 中的状态机相关功能
目标：实现6个状态机测试用例，覆盖图构建、状态转换、路由逻辑等功能
"""

import asyncio
import pytest
import uuid
from unittest.mock import AsyncMock, Mock, patch

from app.agent.graph import (
    build_agent_graph,
    run_agent,
    resume_from_checkpoint,
)
from app.agent.nodes import (
    route_after_parsing,
    route_after_risk_assessment,
    route_after_execution,
    route_after_error_analysis,
    route_after_user_confirmation,
)
from app.core.schemas import (
    AgentState,
    AgentStateDict,
    ParsedIntent,
    IntentType,
    TradeSide,
)


@pytest.mark.unit
@pytest.mark.business_logic
class TestAgentGraphConstruction:
    """Agent状态图构建测试"""

    def setup_method(self):
        """设置测试环境"""
        self.mock_db = AsyncMock()

    def test_build_agent_graph_基本构建(self):
        """测试状态图基本构建"""
        # Act
        graph = build_agent_graph(self.mock_db)
        
        # Assert
        assert graph is not None
        # 验证图已编译
        assert hasattr(graph, 'ainvoke')

    @pytest.mark.asyncio
    async def test_build_agent_graph_节点包装(self):
        """测试节点包装函数正确性"""
        # Arrange
        graph = build_agent_graph(self.mock_db)
        
        # 创建测试状态字典
        test_state_dict = {
            "task_id": uuid.uuid4(),
            "user_id": uuid.uuid4(),
            "raw_input": "测试输入",
            "parsed_intents": [],
            "context": {},
            "execution_plan": [],
            "error_message": None,
            "retry_count": 0,
            "pending_action_id": None,
            "user_response": None,
            "final_result": None,
            "log": [],
            "errors": [],
            "current_node": None,
            "status": "initialized",
            "risk_assessment": None,
            "execution_results": [],
        }
        
        # Act & Assert - 验证图可以接受状态字典
        # 这里我们不实际运行图，只验证构建成功
        assert graph is not None

    def test_success_failure_nodes(self):
        """测试成功和失败终态节点"""
        # Arrange
        graph = build_agent_graph(self.mock_db)
        
        # 创建测试状态字典
        test_state_dict = {
            "task_id": uuid.uuid4(),
            "user_id": uuid.uuid4(),
            "raw_input": "测试输入",
            "parsed_intents": [],
            "context": {},
            "execution_plan": [],
            "error_message": None,
            "retry_count": 0,
            "pending_action_id": None,
            "user_response": None,
            "final_result": None,
            "log": [],
            "errors": [],
            "current_node": None,
            "status": "initialized",
            "risk_assessment": None,
            "execution_results": [],
        }
        
        # Act & Assert
        # 验证图构建成功，包含终态节点
        assert graph is not None


@pytest.mark.unit
@pytest.mark.business_logic
class TestAgentStateRouting:
    """Agent状态路由测试"""

    def setup_method(self):
        """设置测试环境"""
        self.test_user_id = uuid.uuid4()
        self.test_task_id = uuid.uuid4()
        
        # 创建基础测试状态
        self.base_state = AgentState(
            task_id=self.test_task_id,
            user_id=self.test_user_id,
            raw_input="测试输入",
            parsed_intents=[],
            context={},
            execution_plan=[],
            log=[]
        )

    def test_route_after_parsing_成功路由(self):
        """测试解析后成功路由到Context"""
        # Arrange
        state = self.base_state.copy()
        state.parsed_intents = [
            ParsedIntent(
                intent_type=IntentType.CREATE_ORDER,
                raw_text="买入 BTC 1000U",
                side=TradeSide.BUY,
                symbol="BTC/USDT",
                confidence=0.95
            )
        ]
        
        # Act
        result = route_after_parsing(state)
        
        # Assert
        assert result == "Context"

    def test_route_after_parsing_需要用户确认(self):
        """测试解析后需要用户确认的路由"""
        # Arrange
        state = self.base_state.copy()
        state.parsed_intents = [
            ParsedIntent(
                intent_type=IntentType.AMBIGUOUS,
                raw_text="我想交易",
                confidence=0.3,
                clarification_needed="请明确指出要买入还是卖出"
            )
        ]
        
        # Act
        result = route_after_parsing(state)
        
        # Assert
        assert result == "UserConfirm"

    def test_route_after_parsing_失败路由(self):
        """测试解析后失败路由"""
        # Arrange
        state = self.base_state.copy()
        state.parsed_intents = []  # 空意图列表
        
        # Act
        result = route_after_parsing(state)
        
        # Assert
        assert result == "Failure"

    def test_route_after_risk_assessment_通过(self):
        """测试风险评估通过路由"""
        # Arrange
        state = self.base_state.copy()
        state.risk_assessment = {
            "passed": True,
            "risk_score": 30,
            "reason": "风险可控"
        }
        
        # Act
        result = route_after_risk_assessment(state)
        
        # Assert
        assert result == "Execute"

    def test_route_after_risk_assessment_失败(self):
        """测试风险评估失败路由"""
        # Arrange
        state = self.base_state.copy()
        state.risk_assessment = {
            "passed": False,
            "risk_score": 85,
            "reason": "风险过高"
        }
        
        # Act
        result = route_after_risk_assessment(state)
        
        # Assert
        assert result == "Failure"

    def test_route_after_execution_成功(self):
        """测试执行成功路由"""
        # Arrange
        state = self.base_state.copy()
        state.error_message = None
        
        # Act
        result = route_after_execution(state)
        
        # Assert
        assert result == "Success"

    def test_route_after_execution_有错误(self):
        """测试执行有错误路由"""
        # Arrange
        state = self.base_state.copy()
        state.error_message = "执行失败"
        
        # Act
        result = route_after_execution(state)
        
        # Assert
        assert result == "AnalyzeError"

    def test_route_after_error_analysis_重试(self):
        """测试错误分析后重试路由"""
        # Arrange
        state = self.base_state.copy()
        state.retry_count = 1
        state.log = ["错误分析完成", "将进行第2次重试"]
        
        # Act
        result = route_after_error_analysis(state)
        
        # Assert
        assert result == "Execute"

    def test_route_after_error_analysis_达到重试限制(self):
        """测试错误分析达到重试限制路由"""
        # Arrange
        state = self.base_state.copy()
        state.retry_count = 5  # 超过默认限制
        
        # Act
        result = route_after_error_analysis(state)
        
        # Assert
        assert result == "Failure"

    def test_route_after_user_confirmation_确认(self):
        """测试用户确认后路由"""
        # Arrange
        state = self.base_state.copy()
        state.user_response = {"approved": True}  # 使用正确的字段名

        # Act
        result = route_after_user_confirmation(state)

        # Assert
        assert result == "Context"

    def test_route_after_user_confirmation_拒绝(self):
        """测试用户拒绝后路由"""
        # Arrange
        state = self.base_state.copy()
        state.user_response = {"approved": False}  # 使用正确的字段名

        # Act
        result = route_after_user_confirmation(state)

        # Assert
        assert result == "Failure"


@pytest.mark.unit
@pytest.mark.business_logic
class TestAgentGraphExecution:
    """Agent状态图执行测试"""

    def setup_method(self):
        """设置测试环境"""
        self.test_user_id = uuid.uuid4()
        self.test_task_id = uuid.uuid4()
        self.mock_db = AsyncMock()
        
        # 创建基础测试状态
        self.base_state = AgentState(
            task_id=self.test_task_id,
            user_id=self.test_user_id,
            raw_input="买入 BTC 1000U",
            parsed_intents=[],
            context={},
            execution_plan=[],
            log=[]
        )

    @pytest.mark.asyncio
    async def test_run_agent_基本执行(self):
        """测试Agent基本执行流程"""
        # Arrange
        state = self.base_state.copy()
        
        # Mock所有节点函数以避免实际执行
        with patch('app.agent.graph.preprocess_text') as mock_preprocess, \
             patch('app.agent.graph.parse_intents') as mock_parse, \
             patch('app.agent.graph.get_context') as mock_context, \
             patch('app.agent.graph.generate_plan') as mock_plan, \
             patch('app.agent.graph.assess_risk') as mock_risk, \
             patch('app.agent.graph.execute_plan') as mock_execute:
            
            # 设置Mock返回值，模拟成功流程
            mock_preprocess.return_value = state
            mock_parse.return_value = state
            mock_context.return_value = state
            mock_plan.return_value = state
            mock_risk.return_value = state
            mock_execute.return_value = state
            
            # Act
            result = await run_agent(state, self.mock_db)
            
            # Assert
            assert "state" in result
            assert isinstance(result["state"], AgentState)

    @pytest.mark.asyncio
    async def test_resume_from_checkpoint(self):
        """测试从检查点恢复执行"""
        # Arrange
        task_id = self.test_task_id
        user_id = self.test_user_id

        # Mock CheckpointManager
        with patch('app.agent.graph.CheckpointManager') as mock_checkpoint_class:
            mock_checkpoint = AsyncMock()
            mock_checkpoint.load_checkpoint.return_value = {
                "state": self.base_state,
                "node_name": "Parse"
            }
            mock_checkpoint_class.return_value = mock_checkpoint

            # Mock run_agent
            with patch('app.agent.graph.run_agent') as mock_run_agent:
                mock_run_agent.return_value = {"state": self.base_state}

                # Act
                result = await resume_from_checkpoint(task_id, user_id, self.mock_db)

                # Assert
                assert result is not None
                mock_checkpoint.load_checkpoint.assert_called_once_with(task_id, user_id)
                mock_run_agent.assert_called_once()

    @pytest.mark.asyncio
    async def test_run_agent_异常处理(self):
        """测试Agent执行异常处理"""
        # Arrange
        state = self.base_state.copy()
        
        # Mock节点函数抛出异常
        with patch('app.agent.graph.preprocess_text') as mock_preprocess:
            mock_preprocess.side_effect = Exception("测试异常")
            
            # Act & Assert
            with pytest.raises(Exception) as exc_info:
                await run_agent(state, self.mock_db)
            
            assert "测试异常" in str(exc_info.value)
