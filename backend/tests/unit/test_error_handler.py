"""
错误处理模块测试
测试错误分类、重试机制、超时处理和错误恢复功能
"""
import pytest
import json
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime, timezone
import asyncio

try:
    from app.core.exceptions import (
        BusinessException,
        ValidationException,
        AuthenticationException,
        AuthorizationException,
        ResourceNotFoundException,
        RateLimitException,
        ExternalServiceException,
        business_exception_handler,
        validation_exception_handler,
        authentication_exception_handler,
        format_error_response
    )
    EXCEPTIONS_AVAILABLE = True
except ImportError:
    EXCEPTIONS_AVAILABLE = False

# 保持向后兼容，如果agent.error_handler存在的话
try:
    from app.agent.error_handler import (
        AgentError,
        ErrorSeverity,
        RecoveryStrategy,
        with_smart_error_handling,
        recovery_manager
    )
    AGENT_ERROR_HANDLER_AVAILABLE = True
except ImportError:
    AGENT_ERROR_HANDLER_AVAILABLE = False


class TestCoreExceptions:
    """核心异常处理测试"""

    @pytest.mark.skipif(not EXCEPTIONS_AVAILABLE, reason="Core exceptions not available")
    def test_business_exception_creation(self):
        """测试业务异常创建"""
        error = BusinessException("Test business error", code="TEST_ERROR", details={"key": "value"})

        assert str(error) == "Test business error"
        assert error.message == "Test business error"
        assert error.code == "TEST_ERROR"
        assert error.details == {"key": "value"}

    @pytest.mark.skipif(not EXCEPTIONS_AVAILABLE, reason="Core exceptions not available")
    def test_validation_exception_creation(self):
        """测试验证异常创建"""
        error = ValidationException("Invalid field", field="username", details={"min_length": 3})

        assert str(error) == "Invalid field"
        assert error.message == "Invalid field"
        assert error.field == "username"
        assert error.details == {"min_length": 3}

    @pytest.mark.skipif(not EXCEPTIONS_AVAILABLE, reason="Core exceptions not available")
    def test_authentication_exception_creation(self):
        """测试认证异常创建"""
        error = AuthenticationException("Invalid token", details={"token_type": "Bearer"})

        assert str(error) == "Invalid token"
        assert error.message == "Invalid token"
        assert error.details == {"token_type": "Bearer"}

    @pytest.mark.skipif(not EXCEPTIONS_AVAILABLE, reason="Core exceptions not available")
    @pytest.mark.asyncio
    async def test_handle_business_exception(self):
        """测试业务异常处理"""
        from fastapi import Request

        # 创建模拟请求
        request = MagicMock(spec=Request)
        request.url = MagicMock()
        request.url.path = "/test"
        request.method = "GET"
        request.state = MagicMock()
        request.state.request_id = None  # 避免JSON序列化问题

        # 创建业务异常
        exc = BusinessException("Test business error", code="TEST_ERROR")

        # 处理异常
        response = await business_exception_handler(request, exc)

        # 验证响应
        assert response.status_code == 400
        response_data = json.loads(response.body.decode())
        assert response_data["error"]["code"] == "TEST_ERROR"
        assert response_data["error"]["message"] == "Test business error"

    @pytest.mark.skipif(not EXCEPTIONS_AVAILABLE, reason="Core exceptions not available")
    @pytest.mark.asyncio
    async def test_handle_validation_exception(self):
        """测试验证异常处理"""
        from fastapi import Request

        # 创建模拟请求
        request = MagicMock(spec=Request)
        request.url = MagicMock()
        request.url.path = "/test"
        request.method = "POST"
        request.state = MagicMock()
        request.state.request_id = None

        # 创建验证异常
        exc = ValidationException("Invalid input", field="email")

        # 处理异常
        response = await validation_exception_handler(request, exc)

        # 验证响应
        assert response.status_code == 422
        response_data = json.loads(response.body.decode())
        assert response_data["error"]["message"] == "Invalid input"
        assert response_data["error"]["details"]["field"] == "email"

    @pytest.mark.skipif(not EXCEPTIONS_AVAILABLE, reason="Core exceptions not available")
    @pytest.mark.asyncio
    async def test_handle_authentication_exception(self):
        """测试认证异常处理"""
        from fastapi import Request

        # 创建模拟请求
        request = MagicMock(spec=Request)
        request.url = MagicMock()
        request.url.path = "/protected"
        request.method = "GET"
        request.state = MagicMock()
        request.state.request_id = None

        # 创建认证异常
        exc = AuthenticationException("Token expired")

        # 处理异常
        response = await authentication_exception_handler(request, exc)

        # 验证响应
        assert response.status_code == 401
        response_data = json.loads(response.body.decode())
        assert response_data["error"]["message"] == "Token expired"

    @pytest.mark.skipif(not EXCEPTIONS_AVAILABLE, reason="Core exceptions not available")
    def test_authorization_exception_creation(self):
        """测试授权异常创建"""
        error = AuthorizationException("Access denied", details={"required_role": "admin"})

        assert str(error) == "Access denied"
        assert error.message == "Access denied"
        assert error.details == {"required_role": "admin"}

    @pytest.mark.skipif(not EXCEPTIONS_AVAILABLE, reason="Core exceptions not available")
    def test_resource_not_found_exception_creation(self):
        """测试资源未找到异常创建"""
        error = ResourceNotFoundException("User", "123")

        assert str(error) == "User not found: 123"
        assert error.resource == "User"
        assert error.identifier == "123"

    @pytest.mark.skipif(not EXCEPTIONS_AVAILABLE, reason="Core exceptions not available")
    def test_rate_limit_exception_creation(self):
        """测试频率限制异常创建"""
        error = RateLimitException("Too many requests", retry_after=60)

        assert str(error) == "Too many requests"
        assert error.message == "Too many requests"
        assert error.retry_after == 60

    @pytest.mark.skipif(not EXCEPTIONS_AVAILABLE, reason="Core exceptions not available")
    def test_external_service_exception_creation(self):
        """测试外部服务异常创建"""
        error = ExternalServiceException("Binance API", "Connection timeout", status_code=503)

        assert str(error) == "Binance API: Connection timeout"
        assert error.service == "Binance API"
        assert error.message == "Connection timeout"
        assert error.status_code == 503

    @pytest.mark.skipif(not EXCEPTIONS_AVAILABLE, reason="Core exceptions not available")
    def test_format_error_response(self):
        """测试错误响应格式化"""
        response = format_error_response(
            error_code="TEST_ERROR",
            message="Test message",
            details={"field": "value"},
            request_id="req-123"
        )

        assert response["success"] is False
        assert response["error"]["code"] == "TEST_ERROR"
        assert response["error"]["message"] == "Test message"
        assert response["error"]["details"] == {"field": "value"}
        assert response["request_id"] == "req-123"

    @pytest.mark.skipif(not EXCEPTIONS_AVAILABLE, reason="Core exceptions not available")
    def test_format_error_response_minimal(self):
        """测试最小错误响应格式化"""
        response = format_error_response("SIMPLE_ERROR", "Simple message")

        assert response["success"] is False
        assert response["error"]["code"] == "SIMPLE_ERROR"
        assert response["error"]["message"] == "Simple message"
        assert "details" not in response["error"]
        assert "request_id" not in response


class TestErrorHandlerBasics:
    """错误处理基础测试"""

    @pytest.mark.skipif(not AGENT_ERROR_HANDLER_AVAILABLE, reason="Agent error handler not available")
    def test_agent_error_creation(self):
        """测试Agent错误创建"""
        from app.agent.error_handler import AgentError, ErrorSeverity

        error = AgentError("Test error", severity=ErrorSeverity.MEDIUM)

        assert str(error) == "Test error"
        assert error.severity == ErrorSeverity.MEDIUM

    def test_error_severity_enum(self):
        """测试错误严重程度枚举"""
        try:
            from app.agent.error_handler import ErrorSeverity
            
            assert ErrorSeverity.LOW.value == "low"
            assert ErrorSeverity.MEDIUM.value == "medium"
            assert ErrorSeverity.HIGH.value == "high"
            assert ErrorSeverity.CRITICAL.value == "critical"
            
        except ImportError:
            pytest.skip("Error handler not available")

    def test_recovery_strategy_enum(self):
        """测试恢复策略枚举"""
        try:
            from app.agent.error_handler import RecoveryStrategy
            
            assert RecoveryStrategy.RETRY.value == "retry"
            assert RecoveryStrategy.FALLBACK.value == "fallback"
            assert RecoveryStrategy.SKIP.value == "skip"
            
        except ImportError:
            pytest.skip("Error handler not available")

    @pytest.mark.asyncio
    async def test_smart_error_handling_decorator(self):
        """测试智能错误处理装饰器"""
        try:
            from app.agent.error_handler import with_smart_error_handling
            from app.core.schemas import AgentState

            @with_smart_error_handling("test_node", timeout_seconds=1.0)
            async def quick_function(state):
                return state

            # 创建一个简单的状态对象
            import uuid
            mock_state = AgentState(
                task_id=uuid.uuid4(),
                user_id=uuid.uuid4(),  # 修复：使用UUID而不是整数
                raw_input="test message"
            )

            result = await quick_function(mock_state)
            assert result.task_id == mock_state.task_id

        except ImportError:
            pytest.skip("Smart error handling not available")


class TestErrorClassification:
    """错误分类测试"""

    def test_error_type_classification(self):
        """测试错误类型分类"""
        # 测试不同类型的错误
        error_types = [
            (ValueError("Invalid input"), "validation"),
            (ConnectionError("Network failed"), "network"),
            (TimeoutError("Request timeout"), "timeout"),
            (PermissionError("Access denied"), "permission"),
            (FileNotFoundError("File not found"), "file_system"),
        ]
        
        for error, expected_type in error_types:
            # 基本的错误分类逻辑
            if isinstance(error, ValueError):
                error_type = "validation"
            elif isinstance(error, ConnectionError):
                error_type = "network"
            elif isinstance(error, TimeoutError):
                error_type = "timeout"
            elif isinstance(error, PermissionError):
                error_type = "permission"
            elif isinstance(error, FileNotFoundError):
                error_type = "file_system"
            else:
                error_type = "unknown"
            
            assert error_type == expected_type

    def test_error_severity_assessment(self):
        """测试错误严重程度评估"""
        try:
            from app.agent.error_handler import ErrorSeverity
            
            # 测试不同严重程度的错误
            severity_cases = [
                ("Minor validation error", ErrorSeverity.LOW),
                ("Network connection issue", ErrorSeverity.MEDIUM),
                ("Database connection failed", ErrorSeverity.HIGH),
                ("System crash", ErrorSeverity.CRITICAL),
            ]
            
            for error_msg, expected_severity in severity_cases:
                # 验证严重程度枚举
                assert expected_severity in ErrorSeverity
                assert isinstance(expected_severity.value, str)
                
        except ImportError:
            pytest.skip("Error handler not available")

    def test_error_context_extraction(self):
        """测试错误上下文提取"""
        # 测试错误上下文信息
        error_context = {
            "timestamp": datetime.now(timezone.utc),
            "user_id": 123,
            "operation": "test_operation",
            "request_id": "req_123",
            "stack_trace": "mock_stack_trace"
        }
        
        # 验证上下文信息完整性
        required_fields = ["timestamp", "user_id", "operation"]
        for field in required_fields:
            assert field in error_context
            assert error_context[field] is not None


class TestRetryMechanism:
    """重试机制测试"""

    @pytest.mark.asyncio
    async def test_retry_with_exponential_backoff(self):
        """测试指数退避重试"""
        call_count = 0
        
        async def failing_operation():
            nonlocal call_count
            call_count += 1
            if call_count < 3:
                raise ConnectionError("Temporary failure")
            return "success"
        
        # Mock重试逻辑
        max_retries = 3
        for attempt in range(max_retries + 1):
            try:
                result = await failing_operation()
                break
            except ConnectionError:
                if attempt == max_retries:
                    raise
                # 计算退避延迟
                delay = min(2 ** attempt, 60)  # 指数退避，最大60秒
                await asyncio.sleep(0.01)  # 模拟延迟
        
        assert result == "success"
        assert call_count == 3

    @pytest.mark.asyncio
    async def test_retry_with_jitter(self):
        """测试带抖动的重试"""
        import random
        
        # 测试抖动计算
        base_delay = 1.0
        max_delay = 60.0
        
        delays = []
        for attempt in range(5):
            # 指数退避 + 抖动
            exponential_delay = min(base_delay * (2 ** attempt), max_delay)
            jitter = random.uniform(0, exponential_delay * 0.1)
            final_delay = exponential_delay + jitter
            delays.append(final_delay)
        
        # 验证延迟递增趋势
        for i in range(1, len(delays)):
            # 考虑抖动，允许一定的变化
            assert delays[i] >= delays[i-1] * 0.9

    def test_retry_condition_evaluation(self):
        """测试重试条件评估"""
        # 测试哪些错误应该重试
        retryable_errors = [
            ConnectionError("Network timeout"),
            TimeoutError("Request timeout"),
            OSError("Temporary system error"),
        ]
        
        non_retryable_errors = [
            ValueError("Invalid input"),
            PermissionError("Access denied"),
            KeyError("Missing key"),
        ]
        
        def is_retryable(error):
            # PermissionError是OSError的子类，所以需要排除
            if isinstance(error, PermissionError):
                return False
            return isinstance(error, (ConnectionError, TimeoutError, OSError))
        
        for error in retryable_errors:
            assert is_retryable(error) is True
        
        for error in non_retryable_errors:
            assert is_retryable(error) is False


class TestTimeoutHandling:
    """超时处理测试"""

    @pytest.mark.asyncio
    async def test_operation_timeout(self):
        """测试操作超时"""
        async def slow_operation():
            await asyncio.sleep(2.0)  # 2秒操作
            return "completed"
        
        # 测试超时控制
        timeout = 1.0  # 1秒超时
        
        with pytest.raises(asyncio.TimeoutError):
            await asyncio.wait_for(slow_operation(), timeout=timeout)

    @pytest.mark.asyncio
    async def test_timeout_with_cleanup(self):
        """测试带清理的超时"""
        cleanup_called = False
        
        async def operation_with_cleanup():
            try:
                await asyncio.sleep(2.0)
                return "completed"
            except asyncio.CancelledError:
                nonlocal cleanup_called
                cleanup_called = True
                raise
        
        # 测试超时和清理
        try:
            await asyncio.wait_for(operation_with_cleanup(), timeout=1.0)
        except asyncio.TimeoutError:
            pass
        
        # 给清理一些时间
        await asyncio.sleep(0.1)
        assert cleanup_called is True

    def test_timeout_configuration(self):
        """测试超时配置"""
        # 测试不同操作的超时配置
        timeout_configs = {
            "database_query": 30.0,
            "api_request": 60.0,
            "file_upload": 300.0,
            "batch_processing": 3600.0,
        }
        
        for operation, timeout in timeout_configs.items():
            assert timeout > 0
            assert isinstance(timeout, (int, float))
            assert timeout <= 3600  # 最大1小时


class TestErrorRecovery:
    """错误恢复测试"""

    @pytest.mark.asyncio
    async def test_fallback_strategy(self):
        """测试降级策略"""
        primary_failed = False
        
        async def primary_operation():
            nonlocal primary_failed
            primary_failed = True
            raise ConnectionError("Primary service unavailable")
        
        async def fallback_operation():
            return "fallback_result"
        
        # 测试降级逻辑
        try:
            result = await primary_operation()
        except ConnectionError:
            result = await fallback_operation()
        
        assert result == "fallback_result"
        assert primary_failed is True

    @pytest.mark.asyncio
    async def test_circuit_breaker_pattern(self):
        """测试断路器模式"""
        failure_count = 0
        circuit_open = False
        
        async def unreliable_service():
            nonlocal failure_count, circuit_open
            
            if circuit_open:
                raise Exception("Circuit breaker is open")
            
            failure_count += 1
            if failure_count <= 3:
                raise ConnectionError("Service failure")
            return "success"
        
        # 模拟断路器逻辑
        max_failures = 3
        
        for i in range(5):
            try:
                result = await unreliable_service()
                break
            except ConnectionError:
                if failure_count >= max_failures:
                    circuit_open = True
            except Exception:
                # 断路器开启
                break
        
        assert circuit_open is True
        assert failure_count >= max_failures

    def test_recovery_strategy_selection(self):
        """测试恢复策略选择"""
        try:
            from app.agent.error_handler import RecoveryStrategy
            
            # 测试不同错误的恢复策略
            error_recovery_map = {
                "network_error": RecoveryStrategy.RETRY,
                "validation_error": RecoveryStrategy.SKIP,
                "critical_error": RecoveryStrategy.USER_INTERVENTION,
                "temporary_error": RecoveryStrategy.ADAPTIVE_RETRY,
            }
            
            for error_type, strategy in error_recovery_map.items():
                assert strategy in RecoveryStrategy
                assert isinstance(strategy.value, str)
                
        except ImportError:
            pytest.skip("Recovery strategy not available")


class TestErrorLogging:
    """错误日志测试"""

    def test_error_log_formatting(self):
        """测试错误日志格式"""
        # 测试错误日志结构
        error_log = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "level": "ERROR",
            "message": "Test error occurred",
            "error_type": "ValueError",
            "stack_trace": "mock_stack_trace",
            "context": {
                "user_id": 123,
                "operation": "test_operation",
                "request_id": "req_123"
            }
        }
        
        # 验证日志结构
        required_fields = ["timestamp", "level", "message", "error_type"]
        for field in required_fields:
            assert field in error_log
            assert error_log[field] is not None

    def test_error_aggregation(self):
        """测试错误聚合"""
        # 模拟错误统计
        error_stats = {
            "total_errors": 100,
            "error_types": {
                "ValueError": 30,
                "ConnectionError": 25,
                "TimeoutError": 20,
                "Other": 25
            },
            "error_rate": 0.05,  # 5%错误率
            "time_window": "1h"
        }
        
        # 验证统计数据
        assert error_stats["total_errors"] > 0
        assert sum(error_stats["error_types"].values()) == error_stats["total_errors"]
        assert 0 <= error_stats["error_rate"] <= 1

    def test_error_alerting_thresholds(self):
        """测试错误告警阈值"""
        # 测试告警阈值配置
        alert_thresholds = {
            "error_rate": 0.1,  # 10%错误率
            "error_count": 1000,  # 1000个错误
            "critical_errors": 1,  # 1个严重错误
            "consecutive_failures": 5,  # 连续5次失败
        }
        
        for metric, threshold in alert_thresholds.items():
            assert threshold > 0
            assert isinstance(threshold, (int, float))


class TestErrorHandlerEdgeCases:
    """错误处理边界情况测试"""

    @pytest.mark.asyncio
    async def test_nested_error_handling(self):
        """测试嵌套错误处理"""
        async def operation_with_nested_errors():
            try:
                raise ValueError("Original error")
            except ValueError as e:
                # 在处理过程中又发生错误
                raise ConnectionError("Error while handling error") from e
        
        with pytest.raises(ConnectionError) as exc_info:
            await operation_with_nested_errors()
        
        # 验证原始错误被保留
        assert exc_info.value.__cause__ is not None
        assert isinstance(exc_info.value.__cause__, ValueError)

    def test_error_handler_memory_usage(self):
        """测试错误处理器内存使用"""
        import sys

        # 创建少量错误对象以避免协程问题
        errors = []
        for i in range(10):  # 减少数量
            try:
                from app.agent.error_handler import AgentError, ErrorSeverity
                error = AgentError(f"Error {i}", severity=ErrorSeverity.LOW)
                errors.append(error)
            except ImportError:
                # 如果模块不可用，创建普通异常
                error = Exception(f"Error {i}")
                errors.append(error)

        # 验证内存使用合理
        total_size = sum(sys.getsizeof(error) for error in errors)
        assert total_size < 1024 * 1024  # 小于1MB

    @pytest.mark.asyncio
    async def test_concurrent_error_handling(self):
        """测试并发错误处理"""
        async def failing_operation(operation_id):
            await asyncio.sleep(0.01)
            raise ValueError(f"Error in operation {operation_id}")
        
        # 并发执行多个失败操作
        tasks = [failing_operation(i) for i in range(10)]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 验证所有操作都失败了
        assert len(results) == 10
        assert all(isinstance(result, Exception) for result in results)

    def test_error_handler_configuration_validation(self):
        """测试错误处理器配置验证"""
        # 测试配置参数验证
        valid_configs = [
            {"max_retries": 3, "timeout": 30.0, "backoff_factor": 2.0},
            {"max_retries": 5, "timeout": 60.0, "backoff_factor": 1.5},
        ]
        
        invalid_configs = [
            {"max_retries": -1, "timeout": 30.0},  # 负数重试次数
            {"max_retries": 3, "timeout": -10.0},  # 负数超时
            {"max_retries": 3, "timeout": 30.0, "backoff_factor": 0},  # 零退避因子
        ]
        
        def validate_config(config):
            return (
                config.get("max_retries", 0) >= 0 and
                config.get("timeout", 0) > 0 and
                config.get("backoff_factor", 1) > 0
            )
        
        for config in valid_configs:
            assert validate_config(config) is True
        
        for config in invalid_configs:
            assert validate_config(config) is False
