"""
Agent Error Handler 单元测试
测试 app/agent/error_handler.py 中的所有类和函数
目标：将覆盖率从29%提升到80%+
"""

import asyncio
import pytest
from datetime import datetime, timezone
from unittest.mock import Mock, patch, AsyncMock

from app.agent.error_handler import (
    AgentError,
    AgentTimeoutError,
    AgentNetworkError,
    AgentValidationError,
    AgentConfigError,
    ErrorSeverity,
    RecoveryStrategy,
    SmartRecoveryManager,
    with_smart_error_handling,
    with_error_handling,
    create_error_state,
    handle_node_error,
)
from app.core.schemas import AgentState


class TestAgentErrorClasses:
    """测试所有Agent错误类"""

    def test_agent_error_basic_creation(self):
        """测试AgentError基本创建"""
        error = AgentError("Test error")
        
        assert error.message == "Test error"
        assert error.error_code == "AGENT_ERROR"
        assert error.node_name is None
        assert error.recoverable is True
        assert error.severity == ErrorSeverity.MEDIUM
        assert error.recovery_strategy == RecoveryStrategy.RETRY
        assert error.context == {}
        assert isinstance(error.timestamp, datetime)
        assert error.retry_count == 0

    def test_agent_error_full_creation(self):
        """测试AgentError完整参数创建"""
        context = {"key": "value"}
        error = AgentError(
            message="Custom error",
            error_code="CUSTOM_ERROR",
            node_name="TestNode",
            recoverable=False,
            severity=ErrorSeverity.HIGH,
            recovery_strategy=RecoveryStrategy.FALLBACK,
            context=context
        )
        
        assert error.message == "Custom error"
        assert error.error_code == "CUSTOM_ERROR"
        assert error.node_name == "TestNode"
        assert error.recoverable is False
        assert error.severity == ErrorSeverity.HIGH
        assert error.recovery_strategy == RecoveryStrategy.FALLBACK
        assert error.context == context

    def test_agent_timeout_error(self):
        """测试AgentTimeoutError"""
        error = AgentTimeoutError("TestNode", 30.0)
        
        assert "TestNode" in error.message
        assert "30.0 seconds" in error.message
        assert error.error_code == "TIMEOUT_ERROR"
        assert error.node_name == "TestNode"
        assert error.recoverable is True
        assert error.timeout_seconds == 30.0

    def test_agent_network_error(self):
        """测试AgentNetworkError"""
        error = AgentNetworkError("Connection failed", "NetworkNode")
        
        assert error.message == "Connection failed"
        assert error.error_code == "NETWORK_ERROR"
        assert error.node_name == "NetworkNode"
        assert error.recoverable is True

    def test_agent_validation_error(self):
        """测试AgentValidationError"""
        error = AgentValidationError("Invalid data", "ValidationNode")
        
        assert error.message == "Invalid data"
        assert error.error_code == "VALIDATION_ERROR"
        assert error.node_name == "ValidationNode"
        assert error.recoverable is False

    def test_agent_config_error(self):
        """测试AgentConfigError"""
        error = AgentConfigError("Config missing", "ConfigNode")
        
        assert error.message == "Config missing"
        assert error.error_code == "CONFIG_ERROR"
        assert error.node_name == "ConfigNode"
        assert error.recoverable is False
        assert error.severity == ErrorSeverity.HIGH
        assert error.recovery_strategy == RecoveryStrategy.USER_INTERVENTION


class TestSmartRecoveryManager:
    """测试智能恢复管理器"""

    def setup_method(self):
        """设置测试环境"""
        self.manager = SmartRecoveryManager()
        self.mock_state = Mock(spec=AgentState)
        self.mock_state.task_id = "test-task-123"
        self.mock_state.errors = []
        self.mock_state.execution_time = 100
        self.mock_state.retry_count = 0
        self.mock_state.current_node = "TestNode"
        self.mock_state.user_id = "test-user-123"
        self.mock_state.status = "running"

    def test_manager_initialization(self):
        """测试管理器初始化"""
        assert self.manager.error_patterns == {}
        assert self.manager.recovery_history == []
        assert self.manager.success_patterns == {}

    def test_create_error_signature(self):
        """测试错误签名创建"""
        error = AgentError("Test error", "TEST_ERROR", "TestNode")
        signature = self.manager._create_error_signature(error, self.mock_state)
        
        assert isinstance(signature, str)
        assert "TEST_ERROR" in signature
        assert "TestNode" in signature

    def test_analyze_error_basic(self):
        """测试基本错误分析"""
        error = AgentError("Test error", "TEST_ERROR", "TestNode")
        analysis = self.manager.analyze_error(error, self.mock_state)
        
        assert "error_signature" in analysis
        assert "recovery_plan" in analysis
        assert "context_analysis" in analysis
        assert "confidence" in analysis
        assert isinstance(analysis["confidence"], float)

    def test_get_base_strategy_by_severity(self):
        """测试基于严重程度的基础策略"""
        # 测试不同严重程度
        low_strategy = self.manager._get_base_strategy_by_severity(ErrorSeverity.LOW)
        assert low_strategy == RecoveryStrategy.RETRY
        
        medium_strategy = self.manager._get_base_strategy_by_severity(ErrorSeverity.MEDIUM)
        assert medium_strategy == RecoveryStrategy.ADAPTIVE_RETRY
        
        high_strategy = self.manager._get_base_strategy_by_severity(ErrorSeverity.HIGH)
        assert high_strategy == RecoveryStrategy.FALLBACK
        
        critical_strategy = self.manager._get_base_strategy_by_severity(ErrorSeverity.CRITICAL)
        assert critical_strategy == RecoveryStrategy.USER_INTERVENTION

    def test_assess_state_health(self):
        """测试状态健康度评估"""
        # 健康状态
        healthy_state = Mock(spec=AgentState)
        healthy_state.errors = []
        healthy_state.execution_time = 50
        healthy_state.retry_count = 0
        
        health = self.manager._assess_state_health(healthy_state)
        assert health == 1.0
        
        # 不健康状态
        unhealthy_state = Mock(spec=AgentState)
        unhealthy_state.errors = [{"error": "test"}] * 3  # 3个错误
        unhealthy_state.execution_time = 400  # 超过5分钟
        unhealthy_state.retry_count = 2
        
        health = self.manager._assess_state_health(unhealthy_state)
        assert health < 1.0
        assert health >= 0.0

    def test_check_resource_availability(self):
        """测试资源可用性检查"""
        # 当前实现总是返回True
        availability = self.manager._check_resource_availability(self.mock_state)
        assert availability is True

    def test_analyze_user_context(self):
        """测试用户上下文分析"""
        context = self.manager._analyze_user_context(self.mock_state)
        assert isinstance(context, dict)
        assert "user_id" in context
        assert "user_preferences" in context
        assert "task_priority" in context

    def test_calculate_error_frequency(self):
        """测试错误频率计算"""
        error = AgentError("Test error", "TEST_ERROR", "TestNode")
        frequency = self.manager._calculate_error_frequency(error, self.mock_state)
        assert isinstance(frequency, int)
        assert frequency >= 0

    def test_get_strategy_parameters(self):
        """测试策略参数获取"""
        error = AgentError("Test error", "TEST_ERROR", "TestNode")
        
        # 测试重试策略参数
        retry_params = self.manager._get_strategy_parameters(
            RecoveryStrategy.RETRY, error, self.mock_state
        )
        assert "max_retries" in retry_params
        assert "retry_delay" in retry_params
        assert "exponential_backoff" in retry_params
        
        # 测试自适应重试策略参数
        adaptive_params = self.manager._get_strategy_parameters(
            RecoveryStrategy.ADAPTIVE_RETRY, error, self.mock_state
        )
        assert "max_retries" in adaptive_params
        assert "initial_delay" in adaptive_params
        assert "max_delay" in adaptive_params
        assert "backoff_factor" in adaptive_params
        assert "jitter" in adaptive_params
        
        # 测试降级策略参数
        fallback_params = self.manager._get_strategy_parameters(
            RecoveryStrategy.FALLBACK, error, self.mock_state
        )
        assert "fallback_node" in fallback_params
        assert "preserve_state" in fallback_params
        
        # 测试重启策略参数
        restart_params = self.manager._get_strategy_parameters(
            RecoveryStrategy.RESTART, error, self.mock_state
        )
        assert "restart_from" in restart_params
        assert "preserve_context" in restart_params
        assert "reset_errors" in restart_params
        
        # 测试用户干预策略参数
        intervention_params = self.manager._get_strategy_parameters(
            RecoveryStrategy.USER_INTERVENTION, error, self.mock_state
        )
        assert "notification_type" in intervention_params
        assert "timeout_seconds" in intervention_params
        assert "auto_fallback" in intervention_params

    def test_determine_fallback_node(self):
        """测试降级节点确定"""
        # 测试已知节点
        assert self.manager._determine_fallback_node("Parse") == "Preprocess"
        assert self.manager._determine_fallback_node("Context") == "Parse"
        assert self.manager._determine_fallback_node("Plan") == "Context"
        assert self.manager._determine_fallback_node("Risk") == "Plan"
        assert self.manager._determine_fallback_node("Execute") == "Risk"
        assert self.manager._determine_fallback_node("AnalyzeError") == "Plan"
        
        # 测试未知节点
        assert self.manager._determine_fallback_node("UnknownNode") == "Preprocess"
        assert self.manager._determine_fallback_node(None) == "Preprocess"

    def test_record_recovery_attempt(self):
        """测试恢复尝试记录"""
        initial_count = len(self.manager.recovery_history)
        
        self.manager.record_recovery_attempt(
            "test_signature",
            RecoveryStrategy.RETRY,
            True,
            1.5
        )
        
        assert len(self.manager.recovery_history) == initial_count + 1
        record = self.manager.recovery_history[-1]
        assert record["error_signature"] == "test_signature"
        assert record["strategy"] == RecoveryStrategy.RETRY
        assert record["success"] is True
        assert record["execution_time"] == 1.5
        assert isinstance(record["timestamp"], datetime)

    def test_adjust_strategy_by_context(self):
        """测试基于上下文的策略调整"""
        error = AgentError("Test error", "TEST_ERROR", "TestNode")
        
        # 测试高错误频率调整
        high_freq_context = {
            "error_frequency": 5,
            "state_health": 0.8,
            "resource_availability": True,
            "user_context": {}
        }
        
        adjusted = self.manager._adjust_strategy_by_context(
            RecoveryStrategy.RETRY, high_freq_context, error, self.mock_state
        )
        assert adjusted["strategy"] == RecoveryStrategy.FALLBACK
        
        # 测试低健康度调整
        low_health_context = {
            "error_frequency": 1,
            "state_health": 0.3,
            "resource_availability": True,
            "user_context": {}
        }
        
        adjusted = self.manager._adjust_strategy_by_context(
            RecoveryStrategy.RETRY, low_health_context, error, self.mock_state
        )
        assert adjusted["strategy"] == RecoveryStrategy.RESTART
        
        # 测试资源不可用调整
        no_resource_context = {
            "error_frequency": 1,
            "state_health": 0.8,
            "resource_availability": False,
            "user_context": {}
        }
        
        adjusted = self.manager._adjust_strategy_by_context(
            RecoveryStrategy.RETRY, no_resource_context, error, self.mock_state
        )
        assert adjusted["strategy"] == RecoveryStrategy.FALLBACK


class TestErrorHandlingDecorators:
    """测试错误处理装饰器"""

    def setup_method(self):
        """设置测试环境"""
        self.mock_state = Mock(spec=AgentState)
        self.mock_state.task_id = "test-task-123"
        self.mock_state.errors = []
        self.mock_state.execution_time = 100
        self.mock_state.retry_count = 0
        self.mock_state.current_node = "TestNode"
        self.mock_state.user_id = "test-user-123"
        self.mock_state.status = "running"

    @pytest.mark.asyncio
    async def test_with_smart_error_handling_success(self):
        """测试智能错误处理装饰器成功执行"""
        @with_smart_error_handling("TestNode", timeout_seconds=5.0)
        async def test_func(state):
            return {"success": True, "result": "test_result"}

        result = await test_func(self.mock_state)
        assert result["success"] is True
        assert result["result"] == "test_result"

    @pytest.mark.asyncio
    async def test_with_smart_error_handling_timeout(self):
        """测试智能错误处理装饰器超时"""
        @with_smart_error_handling("TestNode", timeout_seconds=0.1, enable_smart_recovery=False)
        async def slow_func(state):
            await asyncio.sleep(0.2)
            return {"success": True}

        with pytest.raises(AgentTimeoutError) as exc_info:
            await slow_func(self.mock_state)

        # The error message contains the node name, so we check that
        assert "TestNode" in str(exc_info.value)
        assert exc_info.value.timeout_seconds == 0.1

    @pytest.mark.asyncio
    async def test_with_smart_error_handling_network_error(self):
        """测试网络错误自动分类"""
        @with_smart_error_handling("TestNode", enable_smart_recovery=False)
        async def network_error_func(state):
            raise Exception("Network connection failed")

        with pytest.raises(AgentNetworkError) as exc_info:
            await network_error_func(self.mock_state)

        assert exc_info.value.error_code == "NETWORK_ERROR"
        assert exc_info.value.recovery_strategy == RecoveryStrategy.ADAPTIVE_RETRY

    @pytest.mark.asyncio
    async def test_with_smart_error_handling_validation_error(self):
        """测试验证错误自动分类"""
        @with_smart_error_handling("TestNode", enable_smart_recovery=False)
        async def validation_error_func(state):
            raise Exception("Invalid data format")

        with pytest.raises(AgentValidationError) as exc_info:
            await validation_error_func(self.mock_state)

        assert exc_info.value.error_code == "VALIDATION_ERROR"
        assert exc_info.value.recovery_strategy == RecoveryStrategy.FALLBACK

    @pytest.mark.asyncio
    async def test_with_smart_error_handling_config_error(self):
        """测试配置错误自动分类"""
        @with_smart_error_handling("TestNode", enable_smart_recovery=False)
        async def config_error_func(state):
            raise Exception("Config key missing")

        with pytest.raises(AgentConfigError) as exc_info:
            await config_error_func(self.mock_state)

        assert exc_info.value.error_code == "CONFIG_ERROR"
        assert exc_info.value.recovery_strategy == RecoveryStrategy.USER_INTERVENTION

    @pytest.mark.asyncio
    async def test_with_smart_error_handling_unknown_error(self):
        """测试未知错误自动分类"""
        @with_smart_error_handling("TestNode", enable_smart_recovery=False)
        async def unknown_error_func(state):
            raise Exception("Some unknown error")

        with pytest.raises(AgentError) as exc_info:
            await unknown_error_func(self.mock_state)

        assert exc_info.value.error_code == "UNKNOWN_ERROR"
        assert exc_info.value.recovery_strategy == RecoveryStrategy.RETRY

    @pytest.mark.asyncio
    async def test_with_smart_error_handling_agent_error_passthrough(self):
        """测试AgentError直接传递"""
        @with_smart_error_handling("TestNode", enable_smart_recovery=False)
        async def agent_error_func(state):
            raise AgentValidationError("Custom validation error", "TestNode")

        with pytest.raises(AgentValidationError) as exc_info:
            await agent_error_func(self.mock_state)

        assert exc_info.value.error_code == "VALIDATION_ERROR"
        assert exc_info.value.message == "Custom validation error"

    @pytest.mark.asyncio
    async def test_with_error_handling_backward_compatibility(self):
        """测试向后兼容的错误处理装饰器"""
        @with_error_handling("TestNode", timeout_seconds=5.0)
        async def test_func(state):
            return {"success": True, "result": "test_result"}

        result = await test_func(self.mock_state)
        assert result["success"] is True
        assert result["result"] == "test_result"


class TestUtilityFunctions:
    """测试工具函数"""

    def setup_method(self):
        """设置测试环境"""
        self.mock_state = Mock(spec=AgentState)
        self.mock_state.task_id = "test-task-123"
        self.mock_state.errors = []
        self.mock_state.current_node = "TestNode"
        self.mock_state.status = "running"
        self.mock_state.execution_time = 100
        self.mock_state.retry_count = 0
        self.mock_state.user_id = "test-user-123"

    def test_create_error_state(self):
        """测试创建错误状态"""
        error = AgentError("Test error", "TEST_ERROR", "TestNode")

        # Mock the copy method
        self.mock_state.copy = Mock(return_value=self.mock_state)

        error_state = create_error_state(self.mock_state, error)

        # 验证copy被调用
        self.mock_state.copy.assert_called_once_with(deep=True)

        # 验证错误信息被添加
        assert len(error_state.errors) == 1
        error_info = error_state.errors[0]
        assert error_info["error_code"] == "TEST_ERROR"
        assert error_info["error_message"] == "Test error"
        assert error_info["node_name"] == "TestNode"
        assert error_info["recoverable"] is True
        assert "timestamp" in error_info

        # 验证状态更新
        assert error_state.current_node == "TestNode"
        assert error_state.status == "error"

    @pytest.mark.asyncio
    async def test_handle_node_error_with_agent_error(self):
        """测试处理AgentError节点错误"""
        error = AgentValidationError("Validation failed", "TestNode")

        # Mock the copy method
        self.mock_state.copy = Mock(return_value=self.mock_state)

        with patch('app.agent.error_handler.logger') as mock_logger:
            result_state = await handle_node_error(self.mock_state, error, "TestNode")

            # 验证日志记录
            mock_logger.error.assert_called_once()

            # 验证返回的是错误状态
            assert result_state.status == "error"
            assert len(result_state.errors) == 1

    @pytest.mark.asyncio
    async def test_handle_node_error_with_generic_exception(self):
        """测试处理普通异常节点错误"""
        error = ValueError("Generic error")

        # Mock the copy method
        self.mock_state.copy = Mock(return_value=self.mock_state)

        with patch('app.agent.error_handler.logger') as mock_logger:
            result_state = await handle_node_error(self.mock_state, error, "TestNode")

            # 验证日志记录
            mock_logger.error.assert_called_once()

            # 验证返回的是错误状态
            assert result_state.status == "error"
            assert len(result_state.errors) == 1
            error_info = result_state.errors[0]
            assert error_info["error_code"] == "UNKNOWN_ERROR"
            assert error_info["error_message"] == "Generic error"
