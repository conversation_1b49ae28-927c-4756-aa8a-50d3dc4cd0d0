"""
认证API单元测试
测试 app/api/v1/auth.py 中的所有端点
目标：将覆盖率从0%提升到80%+
"""

import pytest
from unittest.mock import AsyncMock, Mock, patch
from fastapi.testclient import TestClient
from fastapi import status, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
import uuid

from app.main import app
from app.core.models import User
from app.core.auth import AuthManager
from app.core.exceptions import AuthenticationException, BusinessException


class TestAuthAPI:
    """测试认证API端点"""

    def setup_method(self):
        """设置测试环境"""
        self.client = TestClient(app)
        self.test_user_data = {
            "username": "testuser",
            "password": "testpass123"
        }
        self.mock_user = Mock(spec=User)
        self.mock_user.id = uuid.uuid4()
        self.mock_user.username = "testuser"
        self.mock_user.email = "<EMAIL>"
        self.mock_user.is_active = True
        self.mock_user.is_first_time = False
        from datetime import datetime
        mock_datetime = Mock()
        mock_datetime.isoformat.return_value = "2023-01-01T00:00:00"
        self.mock_user.created_at = mock_datetime

    def test_register_success(self):
        """测试用户注册成功"""
        from app.main import app
        from app.api.v1.auth import get_db
        from app.core.auth import AuthManager

        # 创建Mock数据库会话
        mock_db = AsyncMock(spec=AsyncSession)

        # Mock查询结果（用户不存在）
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = None
        mock_db.execute.return_value = mock_result

        # Mock数据库操作
        mock_db.add = Mock()
        mock_db.commit = AsyncMock()
        mock_db.refresh = AsyncMock()

        async def mock_get_db():
            return mock_db

        def mock_hash_password(password):
            return "hashed_password"

        # 覆盖依赖项
        app.dependency_overrides[get_db] = mock_get_db

        with patch.object(AuthManager, 'hash_password', side_effect=mock_hash_password):
            response = self.client.post("/api/v1/auth/register", json=self.test_user_data)

            assert response.status_code == status.HTTP_201_CREATED
            data = response.json()
            assert data["success"] is True
            assert "用户注册成功" in data["message"]
            assert "user_id" in data["data"]

        # 清理依赖覆盖
        app.dependency_overrides.clear()

    def test_register_username_exists(self):
        """测试用户名已存在的情况"""
        from app.main import app
        from app.api.v1.auth import get_db

        # 创建Mock数据库会话
        mock_db = AsyncMock(spec=AsyncSession)

        # Mock查询结果（用户已存在）
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = self.mock_user
        mock_db.execute.return_value = mock_result

        async def mock_get_db():
            return mock_db

        # 覆盖依赖项
        app.dependency_overrides[get_db] = mock_get_db

        response = self.client.post("/api/v1/auth/register", json=self.test_user_data)

        assert response.status_code == status.HTTP_400_BAD_REQUEST
        data = response.json()
        assert "用户名已存在" in data["error"]["message"]

        # 清理依赖覆盖
        app.dependency_overrides.clear()

    def test_register_invalid_data(self):
        """测试注册时提供无效数据"""
        invalid_data = {
            "username": "ab",  # 太短
            "password": "123"  # 太短
        }
        
        response = self.client.post("/api/v1/auth/register", json=invalid_data)
        
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

    def test_login_success(self):
        """测试用户登录成功"""
        # 使用依赖覆盖来Mock认证
        from app.main import app
        from app.api.v1.auth import get_db
        from app.core.auth import AuthManager

        async def mock_get_db():
            return AsyncMock(spec=AsyncSession)

        async def mock_authenticate_user(username, password, db):
            if username == "testuser" and password == "testpass123":
                return self.mock_user
            return None

        def mock_create_access_token(data):
            return "access_token_123"

        def mock_create_refresh_token(data):
            return "refresh_token_123"

        # 覆盖依赖项
        app.dependency_overrides[get_db] = mock_get_db

        with patch.object(AuthManager, 'authenticate_user', side_effect=mock_authenticate_user), \
             patch.object(AuthManager, 'create_access_token', side_effect=mock_create_access_token), \
             patch.object(AuthManager, 'create_refresh_token', side_effect=mock_create_refresh_token):

            # 使用表单数据格式（OAuth2PasswordRequestForm）
            form_data = {
                "username": self.test_user_data["username"],
                "password": self.test_user_data["password"]
            }

            response = self.client.post("/api/v1/auth/login", data=form_data)

            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["access_token"] == "access_token_123"
            assert data["refresh_token"] == "refresh_token_123"
            assert data["token_type"] == "bearer"

        # 清理依赖覆盖
        app.dependency_overrides.clear()

    @patch('app.api.v1.auth.get_db')
    @patch('app.core.auth.AuthManager.authenticate_user')
    def test_login_invalid_credentials(self, mock_authenticate, mock_get_db):
        """测试登录时提供错误凭据"""
        # Mock数据库会话
        mock_db = AsyncMock(spec=AsyncSession)
        mock_get_db.return_value = mock_db
        
        # Mock用户认证失败
        mock_authenticate.return_value = None
        
        form_data = {
            "username": "wronguser",
            "password": "wrongpass"
        }
        
        response = self.client.post("/api/v1/auth/login", data=form_data)
        
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
        data = response.json()
        assert "用户名或密码错误" in data["error"]["message"]

    @patch('app.api.v1.auth.get_db')
    @patch('app.core.auth.AuthManager.authenticate_user')
    def test_login_database_error(self, mock_authenticate, mock_get_db):
        """测试登录时数据库错误"""
        # Mock数据库会话
        mock_db = AsyncMock(spec=AsyncSession)
        mock_get_db.return_value = mock_db
        
        # Mock数据库异常
        mock_authenticate.side_effect = Exception("Database connection failed")
        
        form_data = {
            "username": self.test_user_data["username"],
            "password": self.test_user_data["password"]
        }
        
        response = self.client.post("/api/v1/auth/login", data=form_data)
        
        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR

    @patch('app.api.v1.auth.refresh_access_token')
    def test_refresh_token_success(self, mock_refresh_token):
        """测试刷新令牌成功"""
        # Mock刷新令牌成功
        mock_refresh_token.return_value = {"access_token": "new_access_token_123", "token_type": "bearer"}

        refresh_data = {"refresh_token": "valid_refresh_token"}

        response = self.client.post("/api/v1/auth/refresh", json=refresh_data)

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["access_token"] == "new_access_token_123"
        assert data["token_type"] == "bearer"

    @patch('app.api.v1.auth.refresh_access_token')
    def test_refresh_token_invalid(self, mock_refresh_token):
        """测试无效刷新令牌"""
        # Mock刷新令牌失败
        mock_refresh_token.side_effect = HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid refresh token"
        )
        
        refresh_data = {"refresh_token": "invalid_refresh_token"}
        
        response = self.client.post("/api/v1/auth/refresh", json=refresh_data)
        
        assert response.status_code == status.HTTP_401_UNAUTHORIZED

    def test_refresh_token_missing(self):
        """测试缺少刷新令牌"""
        response = self.client.post("/api/v1/auth/refresh", json={})
        
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

    def test_get_current_user_success(self):
        """测试获取当前用户信息成功"""
        # 使用依赖覆盖来Mock认证
        from app.main import app
        from app.api.v1.auth import get_current_user

        async def mock_get_current_user():
            return self.mock_user

        # 覆盖依赖项
        app.dependency_overrides[get_current_user] = mock_get_current_user

        # Mock认证头
        headers = {"Authorization": "Bearer valid_token"}

        response = self.client.get("/api/v1/auth/me", headers=headers)

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["username"] == "testuser"
        assert "id" in data

        # 清理依赖覆盖
        app.dependency_overrides.clear()

    def test_get_current_user_unauthorized(self):
        """测试未认证访问用户信息"""
        response = self.client.get("/api/v1/auth/me")

        assert response.status_code == status.HTTP_403_FORBIDDEN

    def test_get_current_user_invalid_token(self):
        """测试无效令牌访问用户信息"""
        # 使用依赖覆盖来Mock认证失败
        from app.main import app
        from app.api.v1.auth import get_current_user

        async def mock_get_current_user():
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid token"
            )

        # 覆盖依赖项
        app.dependency_overrides[get_current_user] = mock_get_current_user

        headers = {"Authorization": "Bearer invalid_token"}

        response = self.client.get("/api/v1/auth/me", headers=headers)

        assert response.status_code == status.HTTP_401_UNAUTHORIZED

        # 清理依赖覆盖
        app.dependency_overrides.clear()


class TestAuthValidation:
    """测试认证相关的数据验证"""

    def setup_method(self):
        """设置测试环境"""
        self.client = TestClient(app)

    def test_register_username_too_short(self):
        """测试用户名太短"""
        data = {"username": "ab", "password": "validpass123"}
        response = self.client.post("/api/v1/auth/register", json=data)
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

    def test_register_username_too_long(self):
        """测试用户名太长"""
        data = {"username": "a" * 51, "password": "validpass123"}
        response = self.client.post("/api/v1/auth/register", json=data)
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

    def test_register_password_too_short(self):
        """测试密码太短"""
        data = {"username": "validuser", "password": "123"}
        response = self.client.post("/api/v1/auth/register", json=data)
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

    def test_register_missing_fields(self):
        """测试缺少必填字段"""
        # 缺少用户名
        response = self.client.post("/api/v1/auth/register", json={"password": "validpass123"})
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        
        # 缺少密码
        response = self.client.post("/api/v1/auth/register", json={"username": "validuser"})
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

    def test_login_missing_fields(self):
        """测试登录缺少必填字段"""
        # 缺少用户名
        response = self.client.post("/api/v1/auth/login", data={"password": "validpass123"})
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        
        # 缺少密码
        response = self.client.post("/api/v1/auth/login", data={"username": "validuser"})
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
