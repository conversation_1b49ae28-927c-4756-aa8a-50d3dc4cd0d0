"""
信号API接口单元测试

测试信号管理相关的API端点，包括：
- GET /signals - 获取信号列表
- POST /signals - 创建新信号
- GET /signals/{id} - 获取信号详情
- PUT /signals/{id} - 更新信号
- DELETE /signals/{id} - 删除信号
- GET /signals/stats - 获取信号统计
"""

import json
import uuid
from datetime import datetime, timezone
from decimal import Decimal
from typing import Dict, Any

import pytest
from fastapi import status
from httpx import AsyncClient

from app.core.models import Signal, User
from tests.async_factories import AsyncSignalFactory, AsyncUserFactory


class TestSignalsAPI:
    """信号API测试类"""

    @pytest.mark.asyncio
    async def test_get_signals_success(
        self, async_client: AsyncClient, test_user: User, auth_headers: Dict[str, str]
    ):
        """测试获取信号列表成功"""
        # Arrange - 准备测试数据
        signals = []
        for i in range(3):
            signal = await AsyncSignalFactory.create(
                user_id=test_user.id,
                platform="discord",
                content=f"测试信号内容 {i}",
                channel_name=f"test-channel-{i}",
                author_name=f"test-author-{i}",
                is_processed=i % 2 == 0,  # 交替设置处理状态
                confidence=Decimal(str(0.1 * (i + 1))),
                ai_parse_status="success" if i % 2 == 0 else "pending",
                message_type_ai="trading_signal" if i == 0 else "normal_message"
            )
            signals.append(signal)

        # Act - 执行API调用
        response = await async_client.get("/api/v1/signals", headers=auth_headers)

        # Assert - 验证响应
        assert response.status_code == status.HTTP_200_OK

        data = response.json()
        assert data["success"] is True
        assert "data" in data
        assert "items" in data["data"]
        assert "total" in data["data"]
        assert "page" in data["data"]
        assert "size" in data["data"]

        # 验证返回的信号数据
        items = data["data"]["items"]
        assert len(items) == 3
        assert data["data"]["total"] == 3

        # 验证信号字段
        for item in items:
            assert "id" in item
            assert "platform" in item
            assert "content" in item
            assert "is_processed" in item
            assert "created_at" in item
            assert item["platform"] == "discord"

    @pytest.mark.asyncio
    async def test_get_signals_with_filters(
        self, async_client: AsyncClient, test_user: User, auth_headers: Dict[str, str]
    ):
        """测试带筛选条件的信号列表获取"""
        # Arrange - 创建不同平台和状态的信号
        discord_signal = await AsyncSignalFactory.create(
            user_id=test_user.id,
            platform="discord",
            content="Discord信号",
            is_processed=True
        )

        telegram_signal = await AsyncSignalFactory.create(
            user_id=test_user.id,
            platform="telegram",
            content="Telegram信号",
            is_processed=False
        )

        manual_signal = await AsyncSignalFactory.create(
            user_id=test_user.id,
            platform="manual",
            content="手动信号",
            is_processed=False
        )

        # Act & Assert - 测试平台筛选
        response = await async_client.get(
            "/api/v1/signals?platform=discord",
            headers=auth_headers
        )
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["data"]["total"] == 1
        assert data["data"]["items"][0]["platform"] == "discord"

        # Act & Assert - 测试处理状态筛选
        response = await async_client.get(
            "/api/v1/signals?is_processed=false",
            headers=auth_headers
        )
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["data"]["total"] == 2
        for item in data["data"]["items"]:
            assert item["is_processed"] is False

        # Act & Assert - 测试分页
        response = await async_client.get(
            "/api/v1/signals?page=1&size=2",
            headers=auth_headers
        )
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert len(data["data"]["items"]) == 2
        assert data["data"]["page"] == 1
        assert data["data"]["size"] == 2

    @pytest.mark.asyncio
    async def test_get_signals_unauthorized(self, async_client: AsyncClient):
        """测试未授权访问信号列表"""
        # Act - 不带认证头的请求
        response = await async_client.get("/api/v1/signals")

        # Assert - 验证返回403错误
        assert response.status_code == status.HTTP_403_FORBIDDEN

    @pytest.mark.asyncio
    async def test_get_signals_empty_list(
        self, async_client: AsyncClient, test_user: User, auth_headers: Dict[str, str]
    ):
        """测试获取空信号列表"""
        # Act - 获取信号列表（没有创建任何信号）
        response = await async_client.get("/api/v1/signals", headers=auth_headers)

        # Assert - 验证空列表响应
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["success"] is True
        assert data["data"]["total"] == 0
        assert len(data["data"]["items"]) == 0

    @pytest.mark.asyncio
    async def test_post_signals_success(
        self, async_client: AsyncClient, test_user: User, auth_headers: Dict[str, str]
    ):
        """测试创建信号成功"""
        # Arrange - 准备信号数据
        signal_data = {
            "platform": "manual",
            "content": "这是一个测试信号内容",
            "channel_name": "test-channel",
            "author_name": "test-author",
            "raw_content": "原始内容",
            "metadata": {
                "source": "test",
                "priority": "high"
            }
        }

        # Act - 创建信号
        response = await async_client.post(
            "/api/v1/signals",
            json=signal_data,
            headers=auth_headers
        )

        # Assert - 验证响应
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["success"] is True
        assert "data" in data

        # 验证返回的信号数据
        signal = data["data"]
        assert signal["platform"] == "manual"
        assert signal["content"] == "这是一个测试信号内容"
        assert signal["channel_name"] == "test-channel"
        assert signal["author_name"] == "test-author"
        assert signal["is_processed"] is False
        assert "id" in signal
        assert "created_at" in signal

    @pytest.mark.asyncio
    async def test_post_signals_validation_error(
        self, async_client: AsyncClient, test_user: User, auth_headers: Dict[str, str]
    ):
        """测试创建信号时的数据验证错误"""
        # Arrange - 准备无效数据
        invalid_data_cases = [
            # 缺少必填字段
            {"platform": "manual"},
            {"content": "测试内容"},
            # 无效平台
            {"platform": "invalid", "content": "测试内容"},
            # 内容过长
            {"platform": "manual", "content": "x" * 5000},
            # 空内容
            {"platform": "manual", "content": ""},
        ]

        for invalid_data in invalid_data_cases:
            # Act - 尝试创建信号
            response = await async_client.post(
                "/api/v1/signals",
                json=invalid_data,
                headers=auth_headers
            )

            # Assert - 验证返回400错误
            assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

    @pytest.mark.asyncio
    async def test_get_signal_detail_success(
        self, async_client: AsyncClient, test_user: User, auth_headers: Dict[str, str]
    ):
        """测试获取信号详情成功"""
        # Arrange - 创建测试信号
        signal = await AsyncSignalFactory.create(
            user_id=test_user.id,
            platform="discord",
            content="测试信号详情",
            channel_name="test-channel",
            author_name="test-author",
            raw_content="原始内容",
            signal_metadata={"discord": {"embeds": [], "attachments": []}},
            confidence=Decimal("0.8"),
            ai_parse_status="success",
            message_type_ai="trading_signal",
            is_processed=True
        )

        # Act - 获取信号详情
        response = await async_client.get(
            f"/api/v1/signals/{signal.id}",
            headers=auth_headers
        )

        # Assert - 验证响应
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["success"] is True

        signal_detail = data["data"]
        assert signal_detail["id"] == str(signal.id)
        assert signal_detail["platform"] == "discord"
        assert signal_detail["content"] == "测试信号详情"
        assert signal_detail["raw_content"] == "原始内容"
        assert signal_detail["metadata"] is not None
        assert signal_detail["confidence"] == 0.8
        assert signal_detail["ai_parse_status"] == "success"
        assert signal_detail["message_type_ai"] == "trading_signal"
        assert signal_detail["is_processed"] is True

    @pytest.mark.asyncio
    async def test_get_signal_detail_not_found(
        self, async_client: AsyncClient, test_user: User, auth_headers: Dict[str, str]
    ):
        """测试获取不存在的信号详情"""
        # Arrange - 生成不存在的UUID
        non_existent_id = str(uuid.uuid4())

        # Act - 尝试获取不存在的信号
        response = await async_client.get(
            f"/api/v1/signals/{non_existent_id}",
            headers=auth_headers
        )

        # Assert - 验证返回404错误
        assert response.status_code == status.HTTP_404_NOT_FOUND

    @pytest.mark.asyncio
    async def test_get_signal_detail_invalid_uuid(
        self, async_client: AsyncClient, test_user: User, auth_headers: Dict[str, str]
    ):
        """测试使用无效UUID获取信号详情"""
        # Act - 使用无效UUID
        response = await async_client.get(
            "/api/v1/signals/invalid-uuid",
            headers=auth_headers
        )

        # Assert - 验证返回400错误
        assert response.status_code == status.HTTP_400_BAD_REQUEST

    @pytest.mark.asyncio
    async def test_put_signal_success(
        self, async_client: AsyncClient, test_user: User, auth_headers: Dict[str, str]
    ):
        """测试更新信号成功"""
        # Arrange - 创建测试信号
        signal = await AsyncSignalFactory.create(
            user_id=test_user.id,
            platform="manual",
            content="原始内容",
            is_processed=False,
            confidence=None,
            ai_parse_status="pending",
            message_type_ai="normal_message"
        )

        # Arrange - 准备更新数据
        update_data = {
            "is_processed": True,
            "confidence": 0.9,
            "ai_parse_status": "success",
            "message_type_ai": "trading_signal",
            "metadata": {"updated": True}
        }

        # Act - 更新信号
        response = await async_client.put(
            f"/api/v1/signals/{signal.id}",
            json=update_data,
            headers=auth_headers
        )

        # Assert - 验证响应
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["success"] is True

        updated_signal = data["data"]
        assert updated_signal["is_processed"] is True
        assert updated_signal["confidence"] == 0.9
        assert updated_signal["ai_parse_status"] == "success"
        assert updated_signal["message_type_ai"] == "trading_signal"

    @pytest.mark.asyncio
    async def test_delete_signal_success(
        self, async_client: AsyncClient, test_user: User, auth_headers: Dict[str, str]
    ):
        """测试删除信号成功"""
        # Arrange - 创建测试信号
        signal = await AsyncSignalFactory.create(
            user_id=test_user.id,
            platform="manual",
            content="待删除的信号"
        )

        # Act - 删除信号
        response = await async_client.delete(
            f"/api/v1/signals/{signal.id}",
            headers=auth_headers
        )

        # Assert - 验证响应
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["success"] is True

        # 验证信号已被删除
        get_response = await async_client.get(
            f"/api/v1/signals/{signal.id}",
            headers=auth_headers
        )
        assert get_response.status_code == status.HTTP_404_NOT_FOUND

    @pytest.mark.asyncio
    async def test_get_signals_stats_success(
        self, async_client: AsyncClient, test_user: User, auth_headers: Dict[str, str]
    ):
        """测试获取信号统计成功"""
        # Arrange - 创建不同状态的信号
        await AsyncSignalFactory.create(
            user_id=test_user.id,
            platform="discord",
            content="Discord信号1",
            is_processed=True,
            confidence=Decimal("0.8"),
            ai_parse_status="success",
            message_type_ai="trading_signal"
        )

        await AsyncSignalFactory.create(
            user_id=test_user.id,
            platform="discord",
            content="Discord信号2",
            is_processed=False,
            confidence=Decimal("0.6"),
            ai_parse_status="pending",
            message_type_ai="normal_message"
        )

        await AsyncSignalFactory.create(
            user_id=test_user.id,
            platform="telegram",
            content="Telegram信号",
            is_processed=True,
            confidence=Decimal("0.9"),
            ai_parse_status="success",
            message_type_ai="trading_signal"
        )

        # Act - 获取统计信息
        response = await async_client.get(
            "/api/v1/signals/stats",
            headers=auth_headers
        )

        # Assert - 验证响应
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["success"] is True

        stats = data["data"]
        assert stats["total_signals"] == 3
        assert stats["processed_signals"] == 2
        assert "platform_breakdown" in stats
        assert stats["platform_breakdown"]["discord"] == 2
        assert stats["platform_breakdown"]["telegram"] == 1
        assert "avg_confidence" in stats
        assert "recent_activity" in stats

    @pytest.mark.asyncio
    async def test_get_signals_with_confidence_filter(
        self, async_client: AsyncClient, test_user: User, auth_headers: Dict[str, str]
    ):
        """测试按置信度筛选"""
        # Arrange - 创建不同置信度的信号
        await AsyncSignalFactory.create(
            user_id=test_user.id,
            platform="manual",
            content="低置信度信号",
            confidence=Decimal("0.3"),
            ai_parse_status="success",
            message_type_ai="normal_message"
        )

        await AsyncSignalFactory.create(
            user_id=test_user.id,
            platform="manual",
            content="高置信度信号",
            confidence=Decimal("0.8"),
            ai_parse_status="success",
            message_type_ai="trading_signal"
        )

        # Act & Assert - 测试最小置信度筛选
        response = await async_client.get(
            "/api/v1/signals?confidence_min=0.5",
            headers=auth_headers
        )
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["data"]["total"] == 1
        assert data["data"]["items"][0]["confidence"] == 0.8

        # 测试向后兼容的signal_strength_min参数
        response = await async_client.get(
            "/api/v1/signals?signal_strength_min=0.5",
            headers=auth_headers
        )
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["data"]["total"] == 1
        assert data["data"]["items"][0]["confidence"] == 0.8

    @pytest.mark.asyncio
    async def test_get_signals_with_date_range_filter(
        self, async_client: AsyncClient, test_user: User, auth_headers: Dict[str, str]
    ):
        """测试按日期范围筛选"""
        # Arrange - 创建不同时间的信号
        from datetime import datetime, timezone

        # 创建一个较早的信号
        old_signal = await AsyncSignalFactory.create(
            user_id=test_user.id,
            platform="manual",
            content="较早的信号"
        )

        # 手动设置较早的时间
        from sqlalchemy import update
        from app.core.database import get_db
        async for db in get_db():
            await db.execute(
                update(Signal)
                .where(Signal.id == old_signal.id)
                .values(created_at=datetime(2024, 1, 1, 0, 0, 0))
            )
            await db.commit()
            break

        # 创建一个较新的信号
        await AsyncSignalFactory.create(
            user_id=test_user.id,
            platform="manual",
            content="较新的信号"
        )

        # Act & Assert - 测试日期范围筛选
        response = await async_client.get(
            "/api/v1/signals?date_from=2024-06-01T00:00:00",
            headers=auth_headers
        )
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["data"]["total"] == 1
        assert data["data"]["items"][0]["content"] == "较新的信号"

    @pytest.mark.asyncio
    async def test_update_signal_not_found(
        self, async_client: AsyncClient, test_user: User, auth_headers: Dict[str, str]
    ):
        """测试更新不存在的信号"""
        # Arrange - 生成不存在的UUID
        non_existent_id = str(uuid.uuid4())
        update_data = {"is_processed": True}

        # Act - 尝试更新不存在的信号
        response = await async_client.put(
            f"/api/v1/signals/{non_existent_id}",
            json=update_data,
            headers=auth_headers
        )

        # Assert - 验证返回404错误
        assert response.status_code == status.HTTP_404_NOT_FOUND

    @pytest.mark.asyncio
    async def test_delete_signal_not_found(
        self, async_client: AsyncClient, test_user: User, auth_headers: Dict[str, str]
    ):
        """测试删除不存在的信号"""
        # Arrange - 生成不存在的UUID
        non_existent_id = str(uuid.uuid4())

        # Act - 尝试删除不存在的信号
        response = await async_client.delete(
            f"/api/v1/signals/{non_existent_id}",
            headers=auth_headers
        )

        # Assert - 验证返回404错误
        assert response.status_code == status.HTTP_404_NOT_FOUND

    @pytest.mark.asyncio
    async def test_get_signals_sorting(
        self, async_client: AsyncClient, test_user: User, auth_headers: Dict[str, str]
    ):
        """测试信号列表排序功能"""
        # Arrange - 创建多个信号
        signal1 = await AsyncSignalFactory.create(
            user_id=test_user.id,
            platform="manual",
            content="信号A",
            confidence=Decimal("0.3"),
            ai_parse_status="success",
            message_type_ai="normal_message"
        )

        signal2 = await AsyncSignalFactory.create(
            user_id=test_user.id,
            platform="manual",
            content="信号B",
            confidence=Decimal("0.8"),
            ai_parse_status="success",
            message_type_ai="trading_signal"
        )

        # Act & Assert - 测试按置信度降序排序
        response = await async_client.get(
            "/api/v1/signals?sort_by=confidence&sort_order=desc",
            headers=auth_headers
        )
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert len(data["data"]["items"]) == 2
        assert data["data"]["items"][0]["confidence"] == 0.8
        assert data["data"]["items"][1]["confidence"] == 0.3

        # Act & Assert - 测试按置信度升序排序
        response = await async_client.get(
            "/api/v1/signals?sort_by=confidence&sort_order=asc",
            headers=auth_headers
        )
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["data"]["items"][0]["confidence"] == 0.3
        assert data["data"]["items"][1]["confidence"] == 0.8

        # 测试向后兼容的signal_strength排序
        response = await async_client.get(
            "/api/v1/signals?sort_by=signal_strength&sort_order=desc",
            headers=auth_headers
        )
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["data"]["items"][0]["confidence"] == 0.8
        assert data["data"]["items"][1]["confidence"] == 0.3

    @pytest.mark.asyncio
    async def test_signals_metadata_validation(
        self, async_client: AsyncClient, test_user: User, auth_headers: Dict[str, str]
    ):
        """测试信号元数据验证"""
        # Arrange - 准备包含Discord元数据的信号数据
        discord_metadata = {
            "discord": {
                "guild_id": "123456789012345678",
                "guild_name": "Trading Community",
                "embeds": [
                    {
                        "title": "BTC Signal",
                        "description": "Long position recommended",
                        "color": 65280,
                        "fields": [
                            {"name": "Entry", "value": "$50,000", "inline": True},
                            {"name": "Stop Loss", "value": "$48,000", "inline": True}
                        ]
                    }
                ]
            }
        }

        signal_data = {
            "platform": "discord",
            "content": "BTC 做多信号",
            "channel_name": "vip-signals",
            "author_name": "TraderBot",
            "metadata": discord_metadata
        }

        # Act - 创建包含元数据的信号
        response = await async_client.post(
            "/api/v1/signals",
            json=signal_data,
            headers=auth_headers
        )

        # Assert - 验证创建成功
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["success"] is True
        signal_id = data["data"]["id"]

        # Act - 获取信号详情验证元数据
        response = await async_client.get(
            f"/api/v1/signals/{signal_id}",
            headers=auth_headers
        )

        # Assert - 验证元数据完整性
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        signal_detail = data["data"]
        assert signal_detail["metadata"]["discord"]["guild_id"] == "123456789012345678"
        assert len(signal_detail["metadata"]["discord"]["embeds"]) == 1

    @pytest.mark.asyncio
    async def test_signals_platform_constraint(
        self, async_client: AsyncClient, test_user: User, auth_headers: Dict[str, str]
    ):
        """测试信号平台约束验证"""
        # Arrange - 准备有效和无效的平台数据
        valid_platforms = ["discord", "telegram", "manual"]
        invalid_platforms = ["twitter", "slack", "invalid"]

        # Act & Assert - 测试有效平台
        for platform in valid_platforms:
            signal_data = {
                "platform": platform,
                "content": f"测试{platform}信号",
                "channel_name": "test-channel"
            }

            response = await async_client.post(
                "/api/v1/signals",
                json=signal_data,
                headers=auth_headers
            )
            assert response.status_code == status.HTTP_200_OK

        # Act & Assert - 测试无效平台
        for platform in invalid_platforms:
            signal_data = {
                "platform": platform,
                "content": f"测试{platform}信号",
                "channel_name": "test-channel"
            }

            response = await async_client.post(
                "/api/v1/signals",
                json=signal_data,
                headers=auth_headers
            )
            assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY