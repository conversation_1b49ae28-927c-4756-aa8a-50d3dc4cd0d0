"""
Core Auth模块单元测试
测试 app/core/auth.py 中的所有认证功能
目标：将覆盖率从34%提升到80%+
"""

import pytest
from unittest.mock import AsyncMock, Mock, patch
from datetime import datetime, timedelta, timezone
import jwt
import bcrypt
from fastapi import HTTPException, status
from fastapi.security import HTTPAuthorizationCredentials
from sqlalchemy.ext.asyncio import AsyncSession
import uuid

from app.core.auth import (
    AuthManager,
    get_current_user,
    get_current_user_optional,
    refresh_access_token,
    require_permissions,
    ALGORITHM,
    ACCESS_TOKEN_EXPIRE_MINUTES,
    REFRESH_TOKEN_EXPIRE_DAYS
)
from app.core.models import User
from app.core.exceptions import AuthenticationException


class TestAuthManager:
    """测试认证管理器类"""

    def test_hash_password_success(self):
        """测试密码哈希成功"""
        password = "test_password_123"
        hashed = AuthManager.hash_password(password)
        
        assert isinstance(hashed, str)
        assert hashed != password
        assert len(hashed) > 50  # bcrypt哈希长度通常很长
        assert hashed.startswith("$2b$")  # bcrypt格式

    def test_hash_password_different_results(self):
        """测试相同密码产生不同哈希值（因为salt不同）"""
        password = "same_password"
        hash1 = AuthManager.hash_password(password)
        hash2 = AuthManager.hash_password(password)
        
        assert hash1 != hash2  # 不同的salt应该产生不同的哈希

    def test_verify_password_success(self):
        """测试密码验证成功"""
        password = "correct_password"
        password_hash = AuthManager.hash_password(password)
        
        result = AuthManager.verify_password(password, password_hash)
        assert result is True

    def test_verify_password_failure(self):
        """测试密码验证失败"""
        correct_password = "correct_password"
        wrong_password = "wrong_password"
        password_hash = AuthManager.hash_password(correct_password)
        
        result = AuthManager.verify_password(wrong_password, password_hash)
        assert result is False

    def test_verify_password_invalid_hash(self):
        """测试无效哈希值的密码验证"""
        password = "any_password"
        invalid_hash = "invalid_hash_format"
        
        with pytest.raises(ValueError):
            AuthManager.verify_password(password, invalid_hash)

    @patch('app.core.auth.settings')
    def test_create_access_token_default_expiry(self, mock_settings):
        """测试创建访问令牌（默认过期时间）"""
        mock_settings.jwt.secret_key = "test_secret_key"
        
        data = {"user_id": "123", "username": "testuser"}
        token = AuthManager.create_access_token(data)
        
        assert isinstance(token, str)
        assert len(token) > 50  # JWT令牌通常很长
        
        # 验证令牌内容
        decoded = jwt.decode(token, "test_secret_key", algorithms=[ALGORITHM])
        assert decoded["user_id"] == "123"
        assert decoded["username"] == "testuser"
        assert decoded["type"] == "access"
        assert "exp" in decoded

    @patch('app.core.auth.settings')
    def test_create_access_token_custom_expiry(self, mock_settings):
        """测试创建访问令牌（自定义过期时间）"""
        mock_settings.jwt.secret_key = "test_secret_key"
        
        data = {"user_id": "123"}
        custom_delta = timedelta(hours=2)
        token = AuthManager.create_access_token(data, custom_delta)
        
        decoded = jwt.decode(token, "test_secret_key", algorithms=[ALGORITHM])
        
        # 验证过期时间大约是2小时后
        exp_time = datetime.fromtimestamp(decoded["exp"], tz=timezone.utc)
        expected_time = datetime.now(timezone.utc) + custom_delta
        time_diff = abs((exp_time - expected_time).total_seconds())
        assert time_diff < 60  # 允许1分钟误差

    @patch('app.core.auth.settings')
    def test_create_refresh_token_success(self, mock_settings):
        """测试创建刷新令牌成功"""
        mock_settings.jwt.secret_key = "test_secret_key"
        
        data = {"user_id": "123", "username": "testuser"}
        token = AuthManager.create_refresh_token(data)
        
        assert isinstance(token, str)
        
        # 验证令牌内容
        decoded = jwt.decode(token, "test_secret_key", algorithms=[ALGORITHM])
        assert decoded["user_id"] == "123"
        assert decoded["username"] == "testuser"
        assert decoded["type"] == "refresh"
        
        # 验证过期时间大约是7天后
        exp_time = datetime.fromtimestamp(decoded["exp"], tz=timezone.utc)
        expected_time = datetime.now(timezone.utc) + timedelta(days=REFRESH_TOKEN_EXPIRE_DAYS)
        time_diff = abs((exp_time - expected_time).total_seconds())
        assert time_diff < 3600  # 允许1小时误差

    @patch('app.core.auth.settings')
    def test_verify_token_success(self, mock_settings):
        """测试验证令牌成功"""
        mock_settings.jwt.secret_key = "test_secret_key"
        
        data = {"user_id": "123", "username": "testuser"}
        token = AuthManager.create_access_token(data)
        
        payload = AuthManager.verify_token(token, "access")
        
        assert payload["user_id"] == "123"
        assert payload["username"] == "testuser"
        assert payload["type"] == "access"

    @patch('app.core.auth.settings')
    def test_verify_token_wrong_type(self, mock_settings):
        """测试验证令牌类型错误"""
        mock_settings.jwt.secret_key = "test_secret_key"
        
        data = {"user_id": "123"}
        access_token = AuthManager.create_access_token(data)
        
        with pytest.raises(HTTPException) as exc_info:
            AuthManager.verify_token(access_token, "refresh")
        
        assert exc_info.value.status_code == status.HTTP_401_UNAUTHORIZED
        assert "Invalid token type" in exc_info.value.detail

    @patch('app.core.auth.settings')
    def test_verify_token_expired(self, mock_settings):
        """测试验证过期令牌"""
        mock_settings.jwt.secret_key = "test_secret_key"

        # 创建已过期的令牌
        data = {"user_id": "123", "type": "access"}
        expired_time = datetime.now(timezone.utc) - timedelta(hours=1)
        data["exp"] = expired_time.timestamp()

        expired_token = jwt.encode(data, "test_secret_key", algorithm=ALGORITHM)

        with pytest.raises(HTTPException) as exc_info:
            AuthManager.verify_token(expired_token, "access")

        assert exc_info.value.status_code == status.HTTP_401_UNAUTHORIZED
        assert "Could not validate credentials" in exc_info.value.detail

    @patch('app.core.auth.settings')
    def test_verify_token_invalid_signature(self, mock_settings):
        """测试验证无效签名的令牌"""
        mock_settings.jwt.secret_key = "test_secret_key"

        # 使用错误的密钥创建令牌
        data = {"user_id": "123", "type": "access"}
        wrong_key_token = jwt.encode(data, "wrong_secret_key", algorithm=ALGORITHM)

        with pytest.raises(HTTPException) as exc_info:
            AuthManager.verify_token(wrong_key_token, "access")

        assert exc_info.value.status_code == status.HTTP_401_UNAUTHORIZED
        assert "Could not validate credentials" in exc_info.value.detail

    def test_verify_token_malformed(self):
        """测试验证格式错误的令牌"""
        malformed_token = "not.a.valid.jwt.token"

        with pytest.raises(HTTPException) as exc_info:
            AuthManager.verify_token(malformed_token, "access")

        assert exc_info.value.status_code == status.HTTP_401_UNAUTHORIZED
        assert "Could not validate credentials" in exc_info.value.detail

    @pytest.mark.asyncio
    async def test_authenticate_user_success(self):
        """测试用户认证成功"""
        # Mock数据库会话
        mock_db = AsyncMock(spec=AsyncSession)
        
        # Mock用户对象
        mock_user = Mock(spec=User)
        mock_user.id = uuid.uuid4()
        mock_user.username = "testuser"
        mock_user.password_hash = AuthManager.hash_password("correct_password")
        
        # Mock数据库查询结果
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = mock_user
        mock_db.execute.return_value = mock_result
        
        result = await AuthManager.authenticate_user("testuser", "correct_password", mock_db)
        
        assert result == mock_user
        mock_db.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_authenticate_user_not_found(self):
        """测试用户不存在的认证"""
        # Mock数据库会话
        mock_db = AsyncMock(spec=AsyncSession)
        
        # Mock数据库查询结果（用户不存在）
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = None
        mock_db.execute.return_value = mock_result
        
        result = await AuthManager.authenticate_user("nonexistent", "password", mock_db)
        
        assert result is None

    @pytest.mark.asyncio
    async def test_authenticate_user_wrong_password(self):
        """测试密码错误的认证"""
        # Mock数据库会话
        mock_db = AsyncMock(spec=AsyncSession)
        
        # Mock用户对象
        mock_user = Mock(spec=User)
        mock_user.username = "testuser"
        mock_user.password_hash = AuthManager.hash_password("correct_password")
        
        # Mock数据库查询结果
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = mock_user
        mock_db.execute.return_value = mock_result
        
        result = await AuthManager.authenticate_user("testuser", "wrong_password", mock_db)
        
        assert result is None

    @pytest.mark.asyncio
    async def test_authenticate_user_database_error(self):
        """测试数据库错误的认证"""
        # Mock数据库会话
        mock_db = AsyncMock(spec=AsyncSession)
        
        # Mock数据库异常
        mock_db.execute.side_effect = Exception("Database connection failed")
        
        result = await AuthManager.authenticate_user("testuser", "password", mock_db)

        assert result is None


class TestGetCurrentUser:
    """测试获取当前用户依赖注入函数"""

    @pytest.mark.asyncio
    @patch('app.core.auth.AuthManager.verify_token')
    async def test_get_current_user_success(self, mock_verify_token):
        """测试获取当前用户成功"""
        # Mock令牌验证
        user_id = str(uuid.uuid4())
        mock_verify_token.return_value = {"sub": user_id, "username": "testuser"}

        # Mock数据库会话
        mock_db = AsyncMock(spec=AsyncSession)

        # Mock用户对象
        mock_user = Mock(spec=User)
        mock_user.id = uuid.UUID(user_id)
        mock_user.username = "testuser"

        # Mock数据库查询结果
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = mock_user
        mock_db.execute.return_value = mock_result

        # Mock凭据
        credentials = HTTPAuthorizationCredentials(scheme="Bearer", credentials="valid_token")

        result = await get_current_user(credentials, mock_db)

        assert result == mock_user
        mock_verify_token.assert_called_once_with("valid_token", "access")
        mock_db.execute.assert_called_once()

    @pytest.mark.asyncio
    @patch('app.core.auth.AuthManager.verify_token')
    async def test_get_current_user_invalid_token(self, mock_verify_token):
        """测试无效令牌获取当前用户"""
        # Mock令牌验证失败
        mock_verify_token.side_effect = HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token"
        )

        mock_db = AsyncMock(spec=AsyncSession)
        credentials = HTTPAuthorizationCredentials(scheme="Bearer", credentials="invalid_token")

        with pytest.raises(HTTPException) as exc_info:
            await get_current_user(credentials, mock_db)

        assert exc_info.value.status_code == status.HTTP_401_UNAUTHORIZED

    @pytest.mark.asyncio
    @patch('app.core.auth.AuthManager.verify_token')
    async def test_get_current_user_not_found(self, mock_verify_token):
        """测试用户不存在"""
        # Mock令牌验证成功
        user_id = str(uuid.uuid4())
        mock_verify_token.return_value = {"sub": user_id, "username": "testuser"}

        # Mock数据库会话
        mock_db = AsyncMock(spec=AsyncSession)

        # Mock数据库查询结果（用户不存在）
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = None
        mock_db.execute.return_value = mock_result

        credentials = HTTPAuthorizationCredentials(scheme="Bearer", credentials="valid_token")

        with pytest.raises(HTTPException) as exc_info:
            await get_current_user(credentials, mock_db)

        assert exc_info.value.status_code == status.HTTP_401_UNAUTHORIZED
        assert "User not found" in exc_info.value.detail

    @pytest.mark.asyncio
    @patch('app.core.auth.AuthManager.verify_token')
    async def test_get_current_user_database_error(self, mock_verify_token):
        """测试数据库错误"""
        # Mock令牌验证成功
        user_id = str(uuid.uuid4())
        mock_verify_token.return_value = {"sub": user_id, "username": "testuser"}

        # Mock数据库会话
        mock_db = AsyncMock(spec=AsyncSession)

        # Mock数据库异常
        mock_db.execute.side_effect = Exception("Database connection failed")

        credentials = HTTPAuthorizationCredentials(scheme="Bearer", credentials="valid_token")

        with pytest.raises(HTTPException) as exc_info:
            await get_current_user(credentials, mock_db)

        assert exc_info.value.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR


class TestGetCurrentUserOptional:
    """测试可选获取当前用户依赖注入函数"""

    @pytest.mark.asyncio
    async def test_get_current_user_optional_no_credentials(self):
        """测试无凭据的可选用户获取"""
        mock_db = AsyncMock(spec=AsyncSession)

        result = await get_current_user_optional(None, mock_db)

        assert result is None

    @pytest.mark.asyncio
    @patch('app.core.auth.get_current_user')
    async def test_get_current_user_optional_success(self, mock_get_current_user):
        """测试可选用户获取成功"""
        # Mock用户对象
        mock_user = Mock(spec=User)
        mock_get_current_user.return_value = mock_user

        mock_db = AsyncMock(spec=AsyncSession)
        credentials = HTTPAuthorizationCredentials(scheme="Bearer", credentials="valid_token")

        result = await get_current_user_optional(credentials, mock_db)

        assert result == mock_user
        mock_get_current_user.assert_called_once_with(credentials, mock_db)

    @pytest.mark.asyncio
    @patch('app.core.auth.get_current_user')
    async def test_get_current_user_optional_auth_error(self, mock_get_current_user):
        """测试可选用户获取认证错误"""
        # Mock认证异常
        mock_get_current_user.side_effect = HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token"
        )

        mock_db = AsyncMock(spec=AsyncSession)
        credentials = HTTPAuthorizationCredentials(scheme="Bearer", credentials="invalid_token")

        result = await get_current_user_optional(credentials, mock_db)

        assert result is None


class TestRefreshAccessToken:
    """测试刷新访问令牌功能"""

    @pytest.mark.asyncio
    @patch('app.core.auth.AuthManager.verify_token')
    @patch('app.core.auth.AuthManager.create_access_token')
    async def test_refresh_access_token_success(self, mock_create_token, mock_verify_token):
        """测试刷新访问令牌成功"""
        # Mock刷新令牌验证
        user_id = str(uuid.uuid4())
        mock_verify_token.return_value = {"sub": user_id, "username": "testuser"}

        # Mock新访问令牌创建
        mock_create_token.return_value = "new_access_token"

        # Mock数据库会话
        mock_db = AsyncMock(spec=AsyncSession)

        # Mock用户对象
        mock_user = Mock(spec=User)
        mock_user.id = user_id  # 直接使用字符串，因为实际代码中直接比较
        mock_user.username = "testuser"

        # Mock数据库查询结果
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = mock_user
        mock_db.execute.return_value = mock_result

        result = await refresh_access_token("valid_refresh_token", mock_db)

        assert result["access_token"] == "new_access_token"
        assert result["token_type"] == "bearer"
        mock_verify_token.assert_called_once_with("valid_refresh_token", "refresh")
        mock_create_token.assert_called_once()

    @pytest.mark.asyncio
    @patch('app.core.auth.AuthManager.verify_token')
    async def test_refresh_access_token_invalid_token(self, mock_verify_token):
        """测试无效刷新令牌"""
        # Mock令牌验证失败
        mock_verify_token.side_effect = HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid refresh token"
        )

        mock_db = AsyncMock(spec=AsyncSession)

        with pytest.raises(HTTPException) as exc_info:
            await refresh_access_token("invalid_refresh_token", mock_db)

        assert exc_info.value.status_code == status.HTTP_401_UNAUTHORIZED

    @pytest.mark.asyncio
    @patch('app.core.auth.AuthManager.verify_token')
    async def test_refresh_access_token_user_not_found(self, mock_verify_token):
        """测试刷新令牌时用户不存在"""
        # Mock刷新令牌验证成功
        user_id = str(uuid.uuid4())
        mock_verify_token.return_value = {"sub": user_id, "username": "testuser"}

        # Mock数据库会话
        mock_db = AsyncMock(spec=AsyncSession)

        # Mock数据库查询结果（用户不存在）
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = None
        mock_db.execute.return_value = mock_result

        with pytest.raises(HTTPException) as exc_info:
            await refresh_access_token("valid_refresh_token", mock_db)

        assert exc_info.value.status_code == status.HTTP_401_UNAUTHORIZED
        assert "User not found" in exc_info.value.detail


class TestRequirePermissions:
    """测试权限检查装饰器"""

    @pytest.mark.asyncio
    async def test_require_permissions_decorator(self):
        """测试权限装饰器（当前版本跳过权限检查）"""
        @require_permissions("admin", "read")
        async def protected_function(arg1, arg2):
            return f"result: {arg1} + {arg2}"

        # 当前版本应该跳过权限检查，直接执行函数
        result = await protected_function("test1", "test2")

        assert result == "result: test1 + test2"

    def test_require_permissions_decorator_creation(self):
        """测试权限装饰器创建"""
        decorator = require_permissions("admin", "write")

        # 装饰器应该是可调用的
        assert callable(decorator)

        # 装饰器应该能够装饰函数
        @decorator
        async def test_func():
            return "decorated"

        assert callable(test_func)
