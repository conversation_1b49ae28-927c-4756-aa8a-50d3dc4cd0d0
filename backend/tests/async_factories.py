"""
异步兼容的测试数据创建工具

替代Factory Boy，专门为异步SQLAlchemy设计
"""
import uuid
from datetime import datetime, timezone
from decimal import Decimal
from typing import Any, Dict, List, Optional

from sqlalchemy.ext.asyncio import AsyncSession

from app.core.auth import AuthManager
from app.core.models import (AgentCheckpoint, ConditionalOrder, ExchangeConfig,
                             Order, PendingAction, RiskConfig, Signal, User)


class TestDataCleaner:
    """测试数据清理工具"""

    @staticmethod
    async def clean_all_data(session: AsyncSession):
        """清理所有测试数据"""
        # 按依赖关系顺序删除
        tables_to_clean = [
            AgentCheckpoint,
            PendingAction,
            ConditionalOrder,
            Order,
            Signal,
            ExchangeConfig,
            RiskConfig,
            User,
        ]

        for table_class in tables_to_clean:
            try:
                await session.execute(f"DELETE FROM {table_class.__tablename__}")
            except Exception as e:
                # 如果表不存在或其他错误，继续清理其他表
                pass

        await session.commit()

    @staticmethod
    async def clean_user_data(session: AsyncSession, user_id: int):
        """清理特定用户的数据"""
        # 删除用户相关的所有数据
        tables_with_user_id = [
            (AgentCheckpoint, "user_id"),
            (PendingAction, "user_id"),
            (ConditionalOrder, "user_id"),
            (Order, "user_id"),
            (ExchangeConfig, "user_id"),
            (RiskConfig, "user_id"),
        ]

        for table_class, user_field in tables_with_user_id:
            try:
                await session.execute(
                    f"DELETE FROM {table_class.__tablename__} WHERE {user_field} = :user_id",
                    {"user_id": user_id},
                )
            except Exception:
                pass

        # 最后删除用户
        try:
            await session.execute(
                f"DELETE FROM {User.__tablename__} WHERE id = :user_id",
                {"user_id": user_id},
            )
        except Exception:
            pass

        await session.commit()


class AsyncUserFactory:
    """异步用户工厂"""

    @staticmethod
    async def create(
        session: AsyncSession,
        username: Optional[str] = None,
        password_hash: Optional[str] = None,
        email: Optional[str] = None,
        **kwargs,
    ) -> User:
        """创建用户"""
        if username is None:
            username = f"user_{uuid.uuid4().hex[:8]}"
        if password_hash is None:
            password_hash = AuthManager.hash_password("testpassword123")
        if email is None:
            email = f"{username}@example.com"

        user = User(username=username, password_hash=password_hash, email=email, **kwargs)
        session.add(user)
        await session.commit()
        await session.refresh(user)
        return user

    @staticmethod
    async def create_batch(session: AsyncSession, count: int, **kwargs) -> List[User]:
        """批量创建用户"""
        users = []
        for i in range(count):
            user_kwargs = kwargs.copy()
            if "username" not in user_kwargs:
                user_kwargs["username"] = f"user_{uuid.uuid4().hex[:8]}"
            user = await AsyncUserFactory.create(session, **user_kwargs)
            users.append(user)
        return users


class AsyncRiskConfigFactory:
    """异步风控配置工厂"""

    @staticmethod
    async def create(
        session: AsyncSession,
        user: Optional[User] = None,
        user_id: Optional[int] = None,
        **kwargs,
    ) -> RiskConfig:
        """创建风控配置"""
        if user is None and user_id is None:
            user = await AsyncUserFactory.create(session)
            user_id = user.id
        elif user is not None:
            user_id = user.id

        defaults = {
            "max_concurrent_orders": 5,
            "max_total_position_value_usd": Decimal("1000.0"),
            "max_position_size_usd": Decimal("500.0"),
            "default_position_size_usd": Decimal("100.0"),
            "allowed_symbols": ["BTC/USDT", "ETH/USDT", "SOL/USDT"],
            "confidence_threshold": Decimal("0.7"),
        }
        defaults.update(kwargs)

        risk_config = RiskConfig(user_id=user_id, **defaults)
        session.add(risk_config)
        await session.commit()
        await session.refresh(risk_config)
        return risk_config


class AsyncOrderFactory:
    """异步订单工厂"""

    @staticmethod
    async def create(
        session: AsyncSession,
        user: Optional[User] = None,
        user_id: Optional[int] = None,
        **kwargs,
    ) -> Order:
        """创建订单"""
        if user is None and user_id is None:
            user = await AsyncUserFactory.create(session)
            user_id = user.id
        elif user is not None:
            user_id = user.id

        defaults = {
            "client_order_id": f"order_{uuid.uuid4().hex[:8]}",
            "exchange_order_id": str(uuid.uuid4()),
            "symbol": "BTC/USDT",
            "side": "buy",
            "quantity": Decimal("0.001"),
            "entry_price": Decimal("50000.0"),
            "status": "active",
        }
        defaults.update(kwargs)

        order = Order(user_id=user_id, **defaults)
        session.add(order)
        await session.commit()
        await session.refresh(order)
        return order


class AsyncExchangeConfigFactory:
    """异步交易所配置工厂"""

    @staticmethod
    async def create(
        session: AsyncSession,
        user: Optional[User] = None,
        user_id: Optional[int] = None,
        **kwargs,
    ) -> ExchangeConfig:
        """创建交易所配置"""
        if user is None and user_id is None:
            user = await AsyncUserFactory.create(session)
            user_id = user.id
        elif user is not None:
            user_id = user.id

        defaults = {
            "exchange_name": "binance",
            "encrypted_api_key": "test_api_key",
            "encrypted_api_secret": "test_api_secret",
            "is_active": True,
        }
        defaults.update(kwargs)

        exchange_config = ExchangeConfig(user_id=user_id, **defaults)
        session.add(exchange_config)
        await session.commit()
        await session.refresh(exchange_config)
        return exchange_config


class AsyncConditionalOrderFactory:
    """异步条件订单工厂"""

    @staticmethod
    async def create(
        session: AsyncSession,
        user: Optional[User] = None,
        user_id: Optional[int] = None,
        **kwargs,
    ) -> ConditionalOrder:
        """创建条件订单"""
        if user is None and user_id is None:
            user = await AsyncUserFactory.create(session)
            user_id = user.id
        elif user is not None:
            user_id = user.id

        defaults = {
            "symbol": "BTC/USDT",
            "trigger_condition": {"type": "price", "operator": ">", "value": 50000},
            "action_plan": {"action": "buy", "quantity": 0.001},
            "status": "PENDING",
        }
        defaults.update(kwargs)

        conditional_order = ConditionalOrder(user_id=user_id, **defaults)
        session.add(conditional_order)
        await session.commit()
        await session.refresh(conditional_order)
        return conditional_order


class AsyncPendingActionFactory:
    """异步待处理动作工厂"""

    @staticmethod
    async def create(
        session: AsyncSession,
        user: Optional[User] = None,
        user_id: Optional[int] = None,
        **kwargs,
    ) -> PendingAction:
        """创建待处理动作"""
        if user is None and user_id is None:
            user = await AsyncUserFactory.create(session)
            user_id = user.id
        elif user is not None:
            user_id = user.id

        from datetime import timedelta

        defaults = {
            "task_id": str(uuid.uuid4()),
            "action_type": "USER_CONFIRMATION",
            "details": {"message": "请确认交易", "amount": 100},
            "status": "PENDING",
            "expires_at": datetime.now(timezone.utc).replace(tzinfo=None) + timedelta(hours=1),  # 修复：移除时区信息
        }
        defaults.update(kwargs)

        pending_action = PendingAction(user_id=user_id, **defaults)
        session.add(pending_action)
        await session.commit()
        await session.refresh(pending_action)
        return pending_action


class AsyncAgentCheckpointFactory:
    """异步Agent检查点工厂"""

    @staticmethod
    async def create(
        session: AsyncSession,
        user: Optional[User] = None,
        user_id: Optional[int] = None,
        **kwargs,
    ) -> AgentCheckpoint:
        """创建Agent检查点"""
        if user is None and user_id is None:
            user = await AsyncUserFactory.create(session)
            user_id = user.id
        elif user is not None:
            user_id = user.id

        defaults = {
            "task_id": str(uuid.uuid4()),
            "node_name": "Preprocess",
            "state_data": {
                "task_id": str(uuid.uuid4()),
                "user_id": str(user_id),  # 修复：将UUID转换为字符串
                "raw_input": "测试输入",
                "parsed_intents": [],
                "context": {},
                "execution_plan": [],
                "log": ["测试日志"],
            },
        }
        defaults.update(kwargs)

        checkpoint = AgentCheckpoint(user_id=user_id, **defaults)
        session.add(checkpoint)
        await session.commit()
        await session.refresh(checkpoint)
        return checkpoint


class AsyncSignalFactory:
    """信号数据异步工厂"""

    @staticmethod
    async def create(
        session: Optional[AsyncSession] = None,
        user_id: Optional[int] = None,
        **kwargs
    ) -> Signal:
        """创建信号记录"""
        from app.core.database import AsyncSessionLocal

        if session is None:
            async with AsyncSessionLocal() as session:
                return await AsyncSignalFactory._create_signal(session, user_id, **kwargs)
        else:
            return await AsyncSignalFactory._create_signal(session, user_id, **kwargs)

    @staticmethod
    async def _create_signal(
        session: AsyncSession,
        user_id: Optional[int] = None,
        **kwargs
    ) -> Signal:
        """内部创建信号方法"""
        # 如果没有提供user_id，创建一个测试用户
        if user_id is None:
            user = await AsyncUserFactory.create(session=session)
            user_id = user.id

        # 设置默认值
        defaults = {
            "platform": "manual",
            "content": "测试信号内容",
            "channel_name": None,
            "author_name": None,
            "author_id": None,
            "platform_message_id": None,
            "channel_id": None,
            "raw_content": None,
            "signal_metadata": None,
            "confidence": None,
            "ai_parse_status": "pending",
            "message_type_ai": "normal_message",
            "llm_service": None,
            "is_processed": False,
            "processed_at": None,
            "message_type": "text",
        }
        defaults.update(kwargs)

        signal = Signal(user_id=user_id, **defaults)
        session.add(signal)
        await session.commit()
        await session.refresh(signal)
        return signal

    @staticmethod
    async def create_discord_signal(
        session: Optional[AsyncSession] = None,
        user_id: Optional[int] = None,
        **kwargs
    ) -> Signal:
        """创建Discord信号记录"""
        discord_defaults = {
            "platform": "discord",
            "channel_name": "test-channel",
            "author_name": "TestBot",
            "author_id": "123456789",
            "platform_message_id": "987654321",
            "channel_id": "111222333",
            "signal_metadata": {
                "discord": {
                    "guild_id": "444555666",
                    "guild_name": "Test Server",
                    "embeds": [],
                    "attachments": [],
                    "reactions": []
                }
            }
        }
        discord_defaults.update(kwargs)

        return await AsyncSignalFactory.create(
            session=session,
            user_id=user_id,
            **discord_defaults
        )

    @staticmethod
    async def create_telegram_signal(
        session: Optional[AsyncSession] = None,
        user_id: Optional[int] = None,
        **kwargs
    ) -> Signal:
        """创建Telegram信号记录"""
        telegram_defaults = {
            "platform": "telegram",
            "channel_name": "test_channel",
            "author_name": "TestUser",
            "author_id": "123456789",
            "platform_message_id": "987654321",
            "channel_id": "-1001234567890",
            "signal_metadata": {
                "telegram": {
                    "chat_type": "channel",
                    "entities": [],
                    "media": None
                }
            }
        }
        telegram_defaults.update(kwargs)

        return await AsyncSignalFactory.create(
            session=session,
            user_id=user_id,
            **telegram_defaults
        )
