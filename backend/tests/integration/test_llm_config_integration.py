"""
LLM配置集成测试

测试LLM配置功能的端到端集成
"""

import pytest
import uuid
from decimal import Decimal

from fastapi import status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from app.core.models import User, LLMConfig
from app.core.schemas import LLMProvider


class TestLLMConfigIntegration:
    """LLM配置集成测试类"""

    @pytest.fixture
    async def test_user(self, async_session: AsyncSession):
        """创建测试用户"""
        user = User(
            id=uuid.uuid4(),
            username="integration_test_user",
            email="<EMAIL>",
            hashed_password="hashed_password"
        )
        async_session.add(user)
        await async_session.commit()
        await async_session.refresh(user)
        return user

    async def test_complete_llm_config_workflow(
        self, 
        authenticated_client, 
        async_session: AsyncSession,
        test_user: User
    ):
        """测试完整的LLM配置工作流程"""
        
        # 1. 创建第一个配置
        config1_data = {
            "config_name": "ChatGPT主配置",
            "provider": "chatgpt",
            "enabled": True,
            "is_default": True,
            "api_key": "sk-test123456789",
            "api_base_url": "https://api.openai.com/v1",
            "model_name": "gpt-4",
            "max_tokens": 4096,
            "temperature": 0.7,
            "timeout_seconds": 60,
            "max_retries": 3
        }
        
        response = await authenticated_client.post(
            "/api/v1/llm-configs",
            json=config1_data
        )
        assert response.status_code == status.HTTP_200_OK
        config1 = response.json()["data"]
        config1_id = config1["id"]
        
        # 2. 创建第二个配置
        config2_data = {
            "config_name": "DeepSeek备用配置",
            "provider": "deepseek",
            "enabled": False,
            "is_default": False,
            "api_key": "ds-test987654321",
            "model_name": "deepseek-chat",
            "max_tokens": 2048,
            "temperature": 0.8,
            "timeout_seconds": 30,
            "max_retries": 2
        }
        
        response = await authenticated_client.post(
            "/api/v1/llm-configs",
            json=config2_data
        )
        assert response.status_code == status.HTTP_200_OK
        config2 = response.json()["data"]
        config2_id = config2["id"]
        
        # 3. 验证配置列表
        response = await authenticated_client.get("/api/v1/llm-configs")
        assert response.status_code == status.HTTP_200_OK
        configs = response.json()["data"]
        assert len(configs) == 2
        
        # 验证默认配置
        default_configs = [c for c in configs if c["is_default"]]
        assert len(default_configs) == 1
        assert default_configs[0]["id"] == config1_id
        
        # 4. 更新第二个配置并设为默认
        update_data = {
            **config2_data,
            "enabled": True,
            "is_default": True,
            "api_key": "ds-updated-key"
        }
        
        response = await authenticated_client.put(
            f"/api/v1/llm-configs/{config2_id}",
            json=update_data
        )
        assert response.status_code == status.HTTP_200_OK
        
        # 5. 验证默认配置已切换
        response = await authenticated_client.get("/api/v1/llm-configs")
        configs = response.json()["data"]
        default_configs = [c for c in configs if c["is_default"]]
        assert len(default_configs) == 1
        assert default_configs[0]["id"] == config2_id
        
        # 6. 测试配置连接
        # 使用统一的默认测试消息，不再发送自定义消息
        response = await authenticated_client.post(
            f"/api/v1/llm-configs/{config1_id}/test",
            json={}
        )
        assert response.status_code == status.HTTP_200_OK
        test_result = response.json()["data"]
        assert "success" in test_result
        
        # 7. 删除第一个配置
        response = await authenticated_client.delete(f"/api/v1/llm-configs/{config1_id}")
        assert response.status_code == status.HTTP_200_OK
        
        # 8. 验证配置已删除
        response = await authenticated_client.get("/api/v1/llm-configs")
        configs = response.json()["data"]
        assert len(configs) == 1
        assert configs[0]["id"] == config2_id
        
        # 9. 验证数据库状态
        stmt = select(LLMConfig).where(LLMConfig.user_id == test_user.id)
        result = await async_session.execute(stmt)
        db_configs = result.scalars().all()
        assert len(db_configs) == 1
        assert str(db_configs[0].id) == config2_id

    async def test_multiple_users_isolation(
        self, 
        authenticated_client, 
        async_session: AsyncSession
    ):
        """测试多用户配置隔离"""
        
        # 创建两个用户
        user1 = User(
            id=uuid.uuid4(),
            username="user1",
            email="<EMAIL>",
            hashed_password="hashed_password"
        )
        user2 = User(
            id=uuid.uuid4(),
            username="user2", 
            email="<EMAIL>",
            hashed_password="hashed_password"
        )
        async_session.add_all([user1, user2])
        await async_session.commit()
        
        # 为用户1创建配置
        config1 = LLMConfig(
            id=uuid.uuid4(),
            user_id=user1.id,
            config_name="用户1配置",
            provider="chatgpt",
            enabled=True,
            is_default=True,
            api_key="user1_key",
            model_name="gpt-4",
            max_tokens=4096,
            temperature=Decimal("0.7"),
            timeout_seconds=60,
            max_retries=3
        )
        
        # 为用户2创建配置
        config2 = LLMConfig(
            id=uuid.uuid4(),
            user_id=user2.id,
            config_name="用户2配置",
            provider="deepseek",
            enabled=True,
            is_default=True,
            api_key="user2_key",
            model_name="deepseek-chat",
            max_tokens=2048,
            temperature=Decimal("0.8"),
            timeout_seconds=30,
            max_retries=2
        )
        
        async_session.add_all([config1, config2])
        await async_session.commit()
        
        # 验证用户只能看到自己的配置
        # 注意：这里需要模拟不同用户的认证，实际测试中需要相应的认证机制
        
        # 验证数据库中的隔离
        stmt1 = select(LLMConfig).where(LLMConfig.user_id == user1.id)
        result1 = await async_session.execute(stmt1)
        user1_configs = result1.scalars().all()
        assert len(user1_configs) == 1
        assert user1_configs[0].config_name == "用户1配置"
        
        stmt2 = select(LLMConfig).where(LLMConfig.user_id == user2.id)
        result2 = await async_session.execute(stmt2)
        user2_configs = result2.scalars().all()
        assert len(user2_configs) == 1
        assert user2_configs[0].config_name == "用户2配置"

    async def test_default_config_constraints(
        self, 
        authenticated_client, 
        async_session: AsyncSession,
        test_user: User
    ):
        """测试默认配置约束"""
        
        # 创建多个配置，但只有一个可以是默认的
        configs_data = [
            {
                "config_name": "配置1",
                "provider": "chatgpt",
                "enabled": True,
                "is_default": True,
                "api_key": "key1",
                "model_name": "gpt-4",
                "max_tokens": 4096,
                "temperature": 0.7,
                "timeout_seconds": 60,
                "max_retries": 3
            },
            {
                "config_name": "配置2",
                "provider": "deepseek",
                "enabled": True,
                "is_default": True,  # 这个应该会取消第一个的默认状态
                "api_key": "key2",
                "model_name": "deepseek-chat",
                "max_tokens": 2048,
                "temperature": 0.8,
                "timeout_seconds": 30,
                "max_retries": 2
            }
        ]
        
        config_ids = []
        for config_data in configs_data:
            response = await authenticated_client.post(
                "/api/v1/llm-configs",
                json=config_data
            )
            assert response.status_code == status.HTTP_200_OK
            config_ids.append(response.json()["data"]["id"])
        
        # 验证只有最后一个配置是默认的
        response = await authenticated_client.get("/api/v1/llm-configs")
        configs = response.json()["data"]
        default_configs = [c for c in configs if c["is_default"]]
        assert len(default_configs) == 1
        assert default_configs[0]["config_name"] == "配置2"
        
        # 使用API设置第一个配置为默认
        response = await authenticated_client.post(
            f"/api/v1/llm-configs/{config_ids[0]}/set-default"
        )
        assert response.status_code == status.HTTP_200_OK
        
        # 再次验证默认配置
        response = await authenticated_client.get("/api/v1/llm-configs")
        configs = response.json()["data"]
        default_configs = [c for c in configs if c["is_default"]]
        assert len(default_configs) == 1
        assert default_configs[0]["config_name"] == "配置1"

    async def test_config_validation_constraints(
        self, 
        authenticated_client, 
        test_user: User
    ):
        """测试配置验证约束"""
        
        # 测试各种无效的配置数据
        invalid_configs = [
            # 无效的温度值
            {
                "config_name": "无效温度",
                "provider": "chatgpt",
                "enabled": True,
                "is_default": False,
                "api_key": "test_key",
                "model_name": "gpt-4",
                "max_tokens": 4096,
                "temperature": 3.0,  # 超出范围
                "timeout_seconds": 60,
                "max_retries": 3
            },
            # 无效的token数
            {
                "config_name": "无效Token",
                "provider": "chatgpt",
                "enabled": True,
                "is_default": False,
                "api_key": "test_key",
                "model_name": "gpt-4",
                "max_tokens": 50000,  # 超出范围
                "temperature": 0.7,
                "timeout_seconds": 60,
                "max_retries": 3
            },
            # 无效的超时时间
            {
                "config_name": "无效超时",
                "provider": "chatgpt",
                "enabled": True,
                "is_default": False,
                "api_key": "test_key",
                "model_name": "gpt-4",
                "max_tokens": 4096,
                "temperature": 0.7,
                "timeout_seconds": 500,  # 超出范围
                "max_retries": 3
            }
        ]
        
        for invalid_config in invalid_configs:
            response = await authenticated_client.post(
                "/api/v1/llm-configs",
                json=invalid_config
            )
            # 应该返回400或422错误
            assert response.status_code in [
                status.HTTP_400_BAD_REQUEST, 
                status.HTTP_422_UNPROCESSABLE_ENTITY
            ]

    async def test_api_key_masking(
        self, 
        authenticated_client, 
        test_user: User
    ):
        """测试API密钥脱敏"""
        
        config_data = {
            "config_name": "密钥测试",
            "provider": "chatgpt",
            "enabled": True,
            "is_default": False,
            "api_key": "sk-1234567890abcdefghijklmnopqrstuvwxyz",
            "model_name": "gpt-4",
            "max_tokens": 4096,
            "temperature": 0.7,
            "timeout_seconds": 60,
            "max_retries": 3
        }
        
        # 创建配置
        response = await authenticated_client.post(
            "/api/v1/llm-configs",
            json=config_data
        )
        assert response.status_code == status.HTTP_200_OK
        
        # 验证返回的密钥已脱敏
        config = response.json()["data"]
        assert config["api_key_masked"] == "sk-1***wxyz"
        assert "api_key" not in config  # 不应该返回原始密钥
        
        # 获取配置列表时也应该脱敏
        response = await authenticated_client.get("/api/v1/llm-configs")
        configs = response.json()["data"]
        for config in configs:
            assert "api_key_masked" in config
            assert "api_key" not in config
