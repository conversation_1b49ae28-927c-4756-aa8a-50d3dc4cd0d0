#!/usr/bin/env python3
"""
Discord集成独立测试脚本
用于快速验证Discord集成功能是否正常工作
可以独立运行，也可以通过pytest运行
"""
import asyncio
import os
import sys
from pathlib import Path

import pytest

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from app.core.config import get_settings
from app.services.discord_listener import (DiscordListenerManager,
                                           MessageDeduplicator,
                                           SignalProcessor,
                                           TradingSignalClient)


@pytest.mark.integration
@pytest.mark.asyncio
async def test_message_deduplicator():
    """测试消息去重器"""
    print("🧪 测试消息去重器...")

    deduplicator = MessageDeduplicator(window_minutes=1, max_cache_size=100)

    # 测试基本功能
    assert not deduplicator.is_duplicate("buy btc", 123, 456), "第一次消息不应该重复"
    assert deduplicator.is_duplicate("buy btc", 123, 456), "相同消息应该被识别为重复"
    assert not deduplicator.is_duplicate("sell eth", 123, 456), "不同消息不应该重复"

    print("✅ 消息去重器测试通过")


@pytest.mark.integration
@pytest.mark.asyncio
async def test_signal_processor():
    """测试信号处理器"""
    print("🧪 测试信号处理器...")

    processor = SignalProcessor()

    # 测试信号识别
    test_cases = [
        ("buy btc", True, "交易动作应该被识别"),
        ("sell eth at $3000", True, "完整交易信号应该被识别"),
        ("btc to the moon 🚀", True, "包含币种和emoji的消息应该被识别"),
        ("target 50k", True, "交易术语应该被识别"),
        ("hello world", False, "普通对话不应该被识别"),
        ("good morning", False, "问候语应该被过滤"),
        ("thanks", False, "感谢语应该被过滤"),
    ]

    for content, expected, description in test_cases:
        result = processor.should_process_message(content)
        assert (
            result == expected
        ), f"{description}: '{content}' -> {result}, 期望 {expected}"

    # 测试信号强度计算
    strong_signal = "buy btc at $50k, target $60k, stop loss $45k 🚀"
    strength = processor._calculate_signal_strength(strong_signal)
    assert strength > 0.5, f"强信号强度应该 > 0.5，实际: {strength}"

    weak_signal = "btc"
    strength = processor._calculate_signal_strength(weak_signal)
    assert 0 < strength < 0.5, f"弱信号强度应该在 0-0.5 之间，实际: {strength}"

    print("✅ 信号处理器测试通过")


@pytest.mark.integration
@pytest.mark.asyncio
async def test_discord_settings():
    """测试Discord配置"""
    print("🧪 测试Discord配置...")

    try:
        settings = get_settings()
        discord_config = settings.discord

        print(f"  Token配置: {'✅ 已配置' if discord_config.token else '❌ 未配置'}")
        print(f"  监控频道: {'✅ 已配置' if discord_config.monitored_channels else '❌ 未配置'}")
        print(f"  自动启动: {'✅ 启用' if discord_config.auto_start else '❌ 禁用'}")
        print(f"  重连尝试: {discord_config.reconnect_attempts}")
        print(f"  重连延迟: {discord_config.reconnect_delay}秒")
        print(f"  去重窗口: {discord_config.deduplication_window}分钟")
        print(f"  缓存大小: {discord_config.message_cache_size}")

        # 测试频道配置解析
        if discord_config.monitored_channels:
            channel_configs = discord_config.get_channel_configs()
            print(f"  解析到 {len(channel_configs)} 个监控频道")
            for config in channel_configs:
                print(
                    f"    - 频道ID: {config.channel_id}, 用户ID: {config.user_id}, 启用: {config.enabled}"
                )

        print("✅ Discord配置测试通过")

    except Exception as e:
        print(f"❌ Discord配置测试失败: {e}")
        return False

    return True


@pytest.mark.integration
@pytest.mark.asyncio
async def test_discord_manager():
    """测试Discord管理器"""
    print("🧪 测试Discord管理器...")

    try:
        from app.services.discord_listener import discord_manager

        # 获取状态
        status = discord_manager.get_status()
        print(f"  运行状态: {'✅ 运行中' if status['is_running'] else '❌ 未运行'}")
        print(f"  任务状态: {'✅ 活跃' if not status['task_done'] else '❌ 已完成'}")

        # 如果有客户端，获取详细状态
        if discord_manager.client:
            client_status = discord_manager.client.get_status()
            print(f"  连接状态: {'✅ 已连接' if client_status['connected'] else '❌ 未连接'}")
            print(f"  监控频道: {client_status['monitored_channels']}")
            print(
                f"  重连尝试: {client_status['reconnect_attempts']}/{client_status['max_reconnect_attempts']}"
            )

        print("✅ Discord管理器测试通过")

    except Exception as e:
        print(f"❌ Discord管理器测试失败: {e}")
        return False

    return True


@pytest.mark.integration
@pytest.mark.slow
@pytest.mark.asyncio
async def test_performance():
    """测试性能"""
    print("🧪 测试性能...")

    import time

    processor = SignalProcessor()

    # 测试大量消息处理性能
    messages = [
        "buy btc",
        "sell eth",
        "hello world",
        "target 50k",
        "stop loss 45k",
    ] * 1000

    start_time = time.time()

    for message in messages:
        processor.should_process_message(message)

    end_time = time.time()
    processing_time = end_time - start_time

    print(f"  处理 {len(messages)} 条消息耗时: {processing_time:.3f}秒")
    print(f"  平均每条消息: {processing_time/len(messages)*1000:.3f}毫秒")

    if processing_time < 1.0:
        print("✅ 性能测试通过")
        return True
    else:
        print("❌ 性能测试失败：处理时间过长")
        return False


async def run_standalone_tests():
    """独立运行所有测试的主函数"""
    print("🚀 开始Discord集成测试\n")

    tests = [
        ("消息去重器", test_message_deduplicator),
        ("信号处理器", test_signal_processor),
        ("Discord配置", test_discord_settings),
        ("Discord管理器", test_discord_manager),
        ("性能测试", test_performance),
    ]

    passed = 0
    failed = 0

    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"测试: {test_name}")
        print("=" * 50)

        try:
            result = await test_func()
            if result is not False:
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                failed += 1
                print(f"❌ {test_name} 失败")
        except Exception as e:
            failed += 1
            print(f"❌ {test_name} 异常: {e}")
            import traceback

            traceback.print_exc()

    print(f"\n{'='*50}")
    print("测试总结")
    print("=" * 50)
    print(f"✅ 通过: {passed}")
    print(f"❌ 失败: {failed}")
    print(f"📊 总计: {passed + failed}")

    if failed == 0:
        print("\n🎉 所有测试通过！Discord集成功能正常")
        return 0
    else:
        print(f"\n⚠️  有 {failed} 个测试失败，请检查配置和代码")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(run_standalone_tests())
    sys.exit(exit_code)
