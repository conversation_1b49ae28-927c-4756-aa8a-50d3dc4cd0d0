#!/usr/bin/env python3
"""
Discord配置系统集成测试
测试Discord配置API端点、数据库交互和服务集成

按照项目测试规范：
- 使用真实数据库连接
- 测试完整的API工作流程
- 验证数据持久化
- 测试认证和授权
"""
import asyncio
import uuid
from typing import Dict, Any

import httpx
import pytest
from sqlalchemy import select

from app.core.auth import AuthManager
from app.core.database import AsyncSessionLocal
from app.core.models import User, DiscordConfig
from app.core.security import decrypt_sensitive_data


class TestDiscordConfigIntegration:
    """Discord配置系统集成测试类"""

    @pytest.fixture(autouse=True)
    async def setup_test_user(self):
        """为每个测试创建独立的测试用户"""
        self.test_user_data = {
            "username": f"test_discord_user_{uuid.uuid4().hex[:8]}",
            "email": f"test_discord_{uuid.uuid4().hex[:8]}@example.com",
            "password": "test_password_123"
        }
        
        async with AsyncSessionLocal() as session:
            # 创建测试用户
            password_hash = AuthManager.hash_password(self.test_user_data["password"])
            user = User(
                username=self.test_user_data["username"],
                email=self.test_user_data["email"],
                password_hash=password_hash,
                is_active=True
            )
            session.add(user)
            await session.commit()
            await session.refresh(user)
            
            self.test_user = user
            self.test_user_id = str(user.id)
        
        # 获取认证token
        self.access_token = await self._login_user()
        self.headers = {
            "Authorization": f"Bearer {self.access_token}",
            "Content-Type": "application/json"
        }
        
        yield
        
        # 清理测试数据
        await self._cleanup_test_data()

    async def _login_user(self) -> str:
        """通过API登录获取访问令牌"""
        async with httpx.AsyncClient() as client:
            login_data = {
                "username": self.test_user_data["username"],
                "password": self.test_user_data["password"]
            }
            
            response = await client.post(
                "http://localhost:8000/api/v1/auth/login",
                data=login_data,
                headers={"Content-Type": "application/x-www-form-urlencoded"}
            )
            
            if response.status_code == 200:
                data = response.json()
                return data["access_token"]
            else:
                raise Exception(f"登录失败: {response.status_code} - {response.text}")

    async def _cleanup_test_data(self):
        """清理测试数据"""
        async with AsyncSessionLocal() as session:
            # 删除用户的所有Discord配置
            await session.execute(
                select(DiscordConfig).where(DiscordConfig.user_id == self.test_user.id)
            )
            configs = (await session.execute(
                select(DiscordConfig).where(DiscordConfig.user_id == self.test_user.id)
            )).scalars().all()
            
            for config in configs:
                await session.delete(config)
            
            # 删除测试用户
            await session.delete(self.test_user)
            await session.commit()

    @pytest.mark.asyncio
    async def test_discord_config_crud_workflow(self):
        """测试Discord配置完整的CRUD工作流程"""
        base_url = "http://localhost:8000/api/v1"
        
        async with httpx.AsyncClient() as client:
            # 1. 测试获取空配置列表
            response = await client.get(f"{base_url}/discord-configs", headers=self.headers)
            assert response.status_code == 200
            configs = response.json()
            assert isinstance(configs, list)
            assert len(configs) == 0
            
            # 2. 测试创建Discord配置
            config_data = {
                "source_name": "测试Discord配置",
                "enabled": True,
                "token": "test_token_123",
                "server_ids": ["123456789", "987654321"],
                "channel_ids": ["111111111", "222222222"],
                "author_ids": ["333333333"],
                "allowed_message_types": ["text"]
            }
            
            response = await client.post(
                f"{base_url}/discord-configs",
                headers=self.headers,
                json=config_data
            )
            assert response.status_code == 200
            created_config = response.json()
            config_id = created_config["id"]
            
            # 验证创建的配置
            assert created_config["source_name"] == config_data["source_name"]
            assert created_config["enabled"] == config_data["enabled"]
            assert created_config["has_token"] is True
            assert created_config["server_ids"] == config_data["server_ids"]
            assert created_config["channel_ids"] == config_data["channel_ids"]
            assert created_config["author_ids"] == config_data["author_ids"]
            assert created_config["allowed_message_types"] == config_data["allowed_message_types"]
            
            # 3. 测试获取单个配置
            response = await client.get(f"{base_url}/discord-configs/{config_id}", headers=self.headers)
            assert response.status_code == 200
            config = response.json()
            assert config["id"] == config_id
            assert config["source_name"] == config_data["source_name"]
            
            # 4. 测试更新配置
            update_data = {
                "source_name": "更新后的Discord配置",
                "enabled": False,
                "token": "updated_test_token_456",
                "server_ids": ["123456789"],
                "channel_ids": ["111111111"],
                "author_ids": [],
                "allowed_message_types": ["text"]
            }
            
            response = await client.put(
                f"{base_url}/discord-configs/{config_id}",
                headers=self.headers,
                json=update_data
            )
            assert response.status_code == 200
            updated_config = response.json()
            assert updated_config["source_name"] == update_data["source_name"]
            assert updated_config["enabled"] == update_data["enabled"]
            
            # 5. 测试删除配置
            response = await client.delete(f"{base_url}/discord-configs/{config_id}", headers=self.headers)
            assert response.status_code == 200
            
            # 6. 验证配置已删除
            response = await client.get(f"{base_url}/discord-configs/{config_id}", headers=self.headers)
            assert response.status_code == 404

    @pytest.mark.asyncio
    async def test_discord_config_validation(self):
        """测试Discord配置数据验证"""
        base_url = "http://localhost:8000/api/v1"
        
        async with httpx.AsyncClient() as client:
            # 测试缺少必需字段
            invalid_data = {
                "enabled": True,
                # 缺少 source_name 和 token
            }
            
            response = await client.post(
                f"{base_url}/discord-configs",
                headers=self.headers,
                json=invalid_data
            )
            assert response.status_code == 422  # 验证错误
            
            # 测试无效的source_name长度
            invalid_data = {
                "source_name": "a" * 101,  # 超过100字符限制
                "enabled": True,
                "token": "test_token",
                "server_ids": [],
                "channel_ids": [],
                "author_ids": [],
                "allowed_message_types": ["text"]
            }
            
            response = await client.post(
                f"{base_url}/discord-configs",
                headers=self.headers,
                json=invalid_data
            )
            assert response.status_code == 422

    @pytest.mark.asyncio
    async def test_discord_config_authentication(self):
        """测试Discord配置API的认证要求"""
        base_url = "http://localhost:8000/api/v1"
        
        async with httpx.AsyncClient() as client:
            # 测试无认证访问
            response = await client.get(f"{base_url}/discord-configs")
            assert response.status_code == 403  # 需要认证
            
            # 测试无效token
            invalid_headers = {
                "Authorization": "Bearer invalid_token",
                "Content-Type": "application/json"
            }
            
            response = await client.get(f"{base_url}/discord-configs", headers=invalid_headers)
            assert response.status_code == 401  # 无效认证

    @pytest.mark.asyncio
    async def test_discord_config_user_isolation(self):
        """测试Discord配置的用户隔离"""
        # 这个测试需要创建另一个用户来验证数据隔离
        # 由于复杂性，这里先标记为TODO
        # TODO: 实现用户隔离测试
        pass

    @pytest.mark.asyncio
    async def test_discord_config_encryption(self):
        """测试Discord token加密存储"""
        base_url = "http://localhost:8000/api/v1"
        test_token = "test_secret_token_123"
        
        async with httpx.AsyncClient() as client:
            # 创建配置
            config_data = {
                "source_name": "加密测试配置",
                "enabled": True,
                "token": test_token,
                "server_ids": [],
                "channel_ids": [],
                "author_ids": [],
                "allowed_message_types": ["text"]
            }
            
            response = await client.post(
                f"{base_url}/discord-configs",
                headers=self.headers,
                json=config_data
            )
            assert response.status_code == 200
            created_config = response.json()
            config_id = created_config["id"]
            
            # 验证数据库中token已加密
            async with AsyncSessionLocal() as session:
                result = await session.execute(
                    select(DiscordConfig).where(DiscordConfig.id == config_id)
                )
                db_config = result.scalar_one()
                
                # 验证存储的是加密数据，不是明文
                assert db_config.encrypted_token != test_token
                assert len(db_config.encrypted_token) > len(test_token)

                # 验证加密数据格式正确（Fernet加密的base64格式）
                import base64
                try:
                    base64.urlsafe_b64decode(db_config.encrypted_token.encode())
                    # 如果能成功解码base64，说明格式正确
                except Exception:
                    pytest.fail("加密数据格式不正确")
            
            # 清理
            await client.delete(f"{base_url}/discord-configs/{config_id}", headers=self.headers)




    @pytest.mark.asyncio
    async def test_discord_config_data_integrity(self):
        """测试Discord配置数据完整性"""
        base_url = "http://localhost:8000/api/v1"

        async with httpx.AsyncClient() as client:
            # 创建包含特殊字符的配置
            config_data = {
                "source_name": "数据完整性测试配置 🤖 特殊字符 & 符号",
                "enabled": True,
                "token": "special_chars_token_!@#$%^&*()",
                "server_ids": ["123456789012345678"],  # 18位Discord ID
                "channel_ids": ["987654321098765432"],
                "author_ids": ["555666777888999000"],
                "allowed_message_types": ["text"]
            }

            # 创建配置
            response = await client.post(
                f"{base_url}/discord-configs",
                headers=self.headers,
                json=config_data
            )
            assert response.status_code == 200
            created_config = response.json()
            config_id = created_config["id"]

            # 验证特殊字符正确保存
            assert created_config["source_name"] == config_data["source_name"]
            assert created_config["server_ids"] == config_data["server_ids"]
            assert created_config["channel_ids"] == config_data["channel_ids"]
            assert created_config["author_ids"] == config_data["author_ids"]

            # 重新获取配置验证数据持久化
            response = await client.get(f"{base_url}/discord-configs/{config_id}", headers=self.headers)
            assert response.status_code == 200
            retrieved_config = response.json()

            # 验证数据一致性
            assert retrieved_config["source_name"] == config_data["source_name"]
            assert retrieved_config["server_ids"] == config_data["server_ids"]
            assert retrieved_config["channel_ids"] == config_data["channel_ids"]
            assert retrieved_config["author_ids"] == config_data["author_ids"]

            # 清理
            await client.delete(f"{base_url}/discord-configs/{config_id}", headers=self.headers)

    @pytest.mark.asyncio
    async def test_discord_config_edge_cases(self):
        """测试Discord配置边界情况"""
        base_url = "http://localhost:8000/api/v1"

        async with httpx.AsyncClient() as client:
            # 测试最大长度的source_name
            max_length_name = "a" * 100  # 最大100字符
            config_data = {
                "source_name": max_length_name,
                "enabled": True,
                "token": "edge_case_token",
                "server_ids": [],
                "channel_ids": [],
                "author_ids": [],
                "allowed_message_types": ["text"]
            }

            response = await client.post(
                f"{base_url}/discord-configs",
                headers=self.headers,
                json=config_data
            )
            assert response.status_code == 200
            config_id = response.json()["id"]

            # 验证长名称正确保存
            retrieved_config = response.json()
            assert retrieved_config["source_name"] == max_length_name
            assert len(retrieved_config["source_name"]) == 100

            # 测试大量ID的情况
            large_server_ids = [f"server_{i:018d}" for i in range(50)]  # 50个服务器ID
            large_channel_ids = [f"channel_{i:018d}" for i in range(100)]  # 100个频道ID

            update_data = {
                "source_name": "大量ID测试配置",
                "enabled": True,
                "token": "large_ids_token",
                "server_ids": large_server_ids,
                "channel_ids": large_channel_ids,
                "author_ids": [],
                "allowed_message_types": ["text"]
            }

            response = await client.put(
                f"{base_url}/discord-configs/{config_id}",
                headers=self.headers,
                json=update_data
            )
            assert response.status_code == 200

            # 验证大量ID正确保存
            updated_config = response.json()
            assert len(updated_config["server_ids"]) == 50
            assert len(updated_config["channel_ids"]) == 100
            assert updated_config["server_ids"] == large_server_ids
            assert updated_config["channel_ids"] == large_channel_ids

            # 清理
            await client.delete(f"{base_url}/discord-configs/{config_id}", headers=self.headers)


if __name__ == "__main__":
    # 允许直接运行此文件进行测试
    asyncio.run(pytest.main([__file__, "-v"]))
