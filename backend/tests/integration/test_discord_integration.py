"""
Discord集成测试
测试Discord监听器、消息处理、信号识别等功能
"""
import asyncio
import json
from datetime import datetime, timezone
from typing import Any, Dict
from unittest.mock import AsyncMock, MagicMock, Mock, PropertyMock, patch

import pytest

from app.core.config import get_settings
from app.core.config import DiscordFilterConfig, DiscordMessageType
from app.services.discord_listener import (DiscordListenerManager,
                                           MessageDeduplicator,
                                           SignalProcessor,
                                           TradingSignalClient,
                                           discord_manager)


class TestMessageDeduplicator:
    """消息去重器测试"""

    def test_init(self):
        """测试初始化"""
        deduplicator = MessageDeduplicator(window_minutes=10, max_cache_size=500)
        assert deduplicator.window_minutes == 10
        assert deduplicator.max_cache_size == 500
        assert len(deduplicator.message_hashes) == 0

    def test_generate_message_hash(self):
        """测试消息哈希生成"""
        deduplicator = MessageDeduplicator()

        # 相同内容应该生成相同哈希
        hash1 = deduplicator._generate_message_hash("buy btc", 123, 456)
        hash2 = deduplicator._generate_message_hash("buy btc", 123, 456)
        assert hash1 == hash2

        # 不同内容应该生成不同哈希
        hash3 = deduplicator._generate_message_hash("sell btc", 123, 456)
        assert hash1 != hash3

        # 内容标准化测试
        hash4 = deduplicator._generate_message_hash("  BUY   BTC  ", 123, 456)
        assert hash1 == hash4  # 应该标准化为相同内容

    def test_is_duplicate(self):
        """测试重复检测"""
        deduplicator = MessageDeduplicator(window_minutes=1)

        # 第一次不应该是重复
        assert not deduplicator.is_duplicate("buy btc", 123, 456)

        # 立即再次检查应该是重复
        assert deduplicator.is_duplicate("buy btc", 123, 456)

        # 不同消息不应该是重复
        assert not deduplicator.is_duplicate("sell eth", 123, 456)


class TestSignalProcessor:
    """信号处理器测试"""

    def test_init(self):
        """测试初始化"""
        processor = SignalProcessor()
        assert len(processor.trading_actions) > 0
        assert len(processor.crypto_symbols) > 0
        assert len(processor.trading_terms) > 0
        assert len(processor.trading_emojis) > 0

    @pytest.mark.parametrize(
        "content,expected",
        [
            ("buy btc", True),
            ("sell eth", True),
            ("long ada", True),
            ("short sol", True),
            ("btc to the moon 🚀", True),
            ("target 50k", True),
            ("tp at 100", True),
            ("stop loss 45k", True),
            ("hello world", False),
            ("good morning", False),
            ("thanks", False),
            ("lol", False),
            ("???", False),
            ("123", False),
        ],
    )
    def test_should_process_message(self, content, expected):
        """测试消息处理判断"""
        processor = SignalProcessor()
        result = processor.should_process_message(content)
        assert result == expected

    def test_should_process_message_with_filters(self):
        """测试带过滤器的消息处理"""
        processor = SignalProcessor()

        # 使用频道特定过滤器
        channel_filters = ["special", "vip"]

        # 包含过滤器关键词的消息应该被处理
        assert processor.should_process_message("special signal", channel_filters)
        assert processor.should_process_message("vip call", channel_filters)

        # 不包含过滤器关键词但包含交易信号的消息也应该被处理
        assert processor.should_process_message("buy btc", channel_filters)

    def test_calculate_signal_strength(self):
        """测试信号强度计算"""
        processor = SignalProcessor()

        # 强信号：包含多个交易元素
        strong_signal = "buy btc at $50k, target $60k, stop loss $45k 🚀"
        strength = processor._calculate_signal_strength(strong_signal)
        assert strength > 0.5

        # 弱信号：只包含少量交易元素
        weak_signal = "btc"
        strength = processor._calculate_signal_strength(weak_signal)
        assert 0 < strength < 0.5

        # 无信号
        no_signal = "hello world"
        strength = processor._calculate_signal_strength(no_signal)
        assert strength == 0.0


class MockDiscordMessage:
    """模拟Discord消息对象"""

    def __init__(
        self, content: str, author_id: int = 123, channel_id: int = 456, **kwargs
    ):
        self.id = kwargs.get("message_id", 789)
        self.content = content
        self.author = Mock()
        self.author.id = author_id
        self.author.display_name = kwargs.get("author_name", "TestUser")
        self.author.bot = kwargs.get("is_bot", False)

        self.channel = Mock()
        self.channel.id = channel_id
        self.channel.name = kwargs.get("channel_name", "test-channel")

        # 添加guild属性
        self.guild = Mock()
        self.guild.id = kwargs.get("guild_id", 123456)
        self.guild.name = kwargs.get("guild_name", "Test Guild")

        self.created_at = kwargs.get("timestamp", datetime.now(timezone.utc))
        self.attachments = kwargs.get("attachments", [])
        self.embeds = kwargs.get("embeds", [])
        self.reference = kwargs.get("reference", None)
        self.mentions = kwargs.get("mentions", [])
        self.role_mentions = kwargs.get("role_mentions", [])
        self.channel_mentions = kwargs.get("channel_mentions", [])


class TestTradingSignalClient:
    """Discord客户端测试"""

    @pytest.fixture
    def mock_settings(self):
        """模拟设置"""
        settings = Mock()
        settings.discord.monitored_channels = json.dumps(
            {
                "123456": {
                    "name": "test-channel",
                    "user_id": 1,
                    "enabled": True,
                    "signal_filters": ["vip"],
                }
            }
        )
        settings.discord.deduplication_window = 5
        settings.discord.message_cache_size = 1000
        settings.discord.reconnect_attempts = 3
        settings.discord.reconnect_delay = 10
        return settings

    def test_parse_monitored_channels(self):
        """测试监控频道解析"""
        # 创建过滤配置
        filter_config = DiscordFilterConfig(
            enabled=True,
            server_ids={"123456"},
            channel_ids={"test-channel-id"},
            author_ids=set(),
            allowed_message_types={DiscordMessageType.TEXT}
        )

        with patch("app.services.discord_listener.settings") as mock_settings:
            mock_settings.discord_filter_config = filter_config
            mock_settings.discord.deduplication_window = 5
            mock_settings.discord.message_cache_size = 1000
            mock_settings.default_user_id = "test-user-id"

            client = TradingSignalClient()

            # 验证过滤配置存在且正确类型
            assert client.filter_config is not None
            assert isinstance(client.filter_config, DiscordFilterConfig)
            assert client.filter_config.enabled == True

    @patch("app.services.discord_listener.settings")
    @pytest.mark.asyncio
    async def test_on_message_filtering(self, mock_settings):
        """测试消息过滤"""
        # 模拟设置
        mock_settings.discord_filter_config = DiscordFilterConfig(
            enabled=True,
            server_ids={"123456"},
            channel_ids={"123456"},
            author_ids=set(),
            allowed_message_types={DiscordMessageType.TEXT}
        )
        mock_settings.discord.deduplication_window = 5
        mock_settings.discord.message_cache_size = 1000
        mock_settings.default_user_id = "test-user-id"

        client = TradingSignalClient()

        # 模拟user属性 - 使用property patch
        mock_user = Mock()
        mock_user.id = 999  # 不同于消息作者

        with patch.object(
            type(client), "user", new_callable=PropertyMock
        ) as mock_user_prop:
            mock_user_prop.return_value = mock_user
            # 模拟process_discord_message方法（新的方法名）
            client.process_discord_message = AsyncMock()

            # 测试正常交易信号
            message = MockDiscordMessage("buy btc", channel_id=123456, guild_id=123456)
            await client.on_message(message)
            client.process_discord_message.assert_called_once()

            # 重置mock
            client.process_discord_message.reset_mock()

            # 测试被过滤的消息（不在允许的频道中）
            message = MockDiscordMessage("hello world", channel_id=999999, guild_id=123456)
            await client.on_message(message)
            client.process_discord_message.assert_not_called()

    @patch("app.services.discord_listener.settings")
    @pytest.mark.asyncio
    async def test_on_message_deduplication(self, mock_settings):
        """测试消息去重"""
        # 模拟设置
        mock_settings.discord_filter_config = DiscordFilterConfig(
            enabled=True,
            server_ids={"123456"},
            channel_ids={"123456"},
            author_ids=set(),
            allowed_message_types={DiscordMessageType.TEXT}
        )
        mock_settings.discord.deduplication_window = 5
        mock_settings.discord.message_cache_size = 1000
        mock_settings.default_user_id = "test-user-id"

        client = TradingSignalClient()

        # 模拟user属性 - 使用property patch
        mock_user = Mock()
        mock_user.id = 999

        with patch.object(
            type(client), "user", new_callable=PropertyMock
        ) as mock_user_prop:
            mock_user_prop.return_value = mock_user
            client.process_discord_message = AsyncMock()

            # 发送相同消息两次
            message1 = MockDiscordMessage("buy btc", channel_id=123456, guild_id=123456)
            message2 = MockDiscordMessage("buy btc", channel_id=123456, guild_id=123456)

            await client.on_message(message1)
            await client.on_message(message2)

            # 应该只处理一次
            assert client.process_discord_message.call_count == 1


class TestDiscordListenerManager:
    """Discord监听器管理器测试"""

    @pytest.fixture
    def manager(self):
        """创建管理器实例"""
        return DiscordListenerManager()

    def test_init(self, manager):
        """测试初始化"""
        assert manager.client is None
        assert manager.listener_task is None
        assert not manager.is_running

    @pytest.mark.asyncio
    async def test_start_stop(self, manager):
        """测试启动和停止"""
        # 模拟启动
        with patch(
            "app.services.discord_listener.start_discord_listener"
        ) as mock_start:
            mock_start.return_value = AsyncMock()
            await manager.start()
            assert manager.is_running
            assert manager.listener_task is not None

        # 模拟停止
        await manager.stop()
        assert not manager.is_running

    def test_get_status(self, manager):
        """测试状态获取"""
        status = manager.get_status()
        assert "is_running" in status
        assert "task_done" in status
        assert "client_status" in status


@pytest.mark.integration
class TestDiscordIntegration:
    """Discord集成测试"""

    @pytest.mark.asyncio
    async def test_full_message_processing_flow(self):
        """测试完整的消息处理流程"""
        # 这个测试需要实际的Discord环境，通常在CI/CD中跳过
        pytest.skip("Requires actual Discord environment")

    @patch("app.services.discord_listener.settings")
    @pytest.mark.asyncio
    async def test_api_integration(self, mock_settings):
        """测试API集成"""
        # 模拟设置
        mock_settings.discord.token = "test_token"
        mock_settings.discord.monitored_channels = json.dumps(
            {"123": {"user_id": 1, "name": "test"}}
        )

        # 测试管理器状态
        status = discord_manager.get_status()
        assert isinstance(status, dict)
        assert "is_running" in status


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
