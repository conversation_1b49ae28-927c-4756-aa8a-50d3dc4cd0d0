"""
Mock交易所实现 - 用于仿真模式测试

提供与真实交易所完全相同的接口，但在内存中模拟订单撮合、成交和状态变更。
"""
import asyncio
import random
import uuid
from dataclasses import dataclass, field
from datetime import datetime, timezone
from decimal import Decimal
from typing import Any, Dict, List, Optional

from app.core.schemas import OrderType, TradePlan, TradeResult, TradeSide


@dataclass
class MockOrder:
    """模拟订单"""

    id: str
    client_order_id: str
    symbol: str
    side: str
    order_type: str
    quantity: Decimal
    price: Optional[Decimal] = None
    filled_quantity: Decimal = Decimal("0")
    status: str = "open"  # open, filled, cancelled, failed
    created_at: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    filled_at: Optional[datetime] = None
    average_price: Optional[Decimal] = None


@dataclass
class MockTicker:
    """模拟价格数据"""

    symbol: str
    last: Decimal
    bid: Decimal
    ask: Decimal
    high: Decimal
    low: Decimal
    volume: Decimal
    timestamp: datetime = field(default_factory=lambda: datetime.now(timezone.utc))


class MockExchange:
    """
    Mock交易所类 - 实现与真实交易所相同的接口

    特性：
    1. 内存中订单管理
    2. 实时价格模拟
    3. 订单撮合模拟
    4. 延迟和错误模拟
    """

    def __init__(self):
        # 订单存储
        self.orders: Dict[str, MockOrder] = {}
        self.order_history: List[MockOrder] = []

        # 价格数据
        self.tickers: Dict[str, MockTicker] = {}
        self._init_default_prices()

        # 配置
        self.latency_ms = 100  # 模拟网络延迟
        self.error_rate = 0.0  # 错误率 (0.0 - 1.0)
        self.slippage_rate = 0.001  # 滑点率

        # 市场状态
        self.market_open = True
        self.maintenance_mode = False

        # 启动价格更新任务
        self._price_update_task = None
        self._start_price_updates()

    def _init_default_prices(self):
        """初始化默认价格"""
        default_prices = {
            "BTC/USDT": Decimal("68000.0"),
            "ETH/USDT": Decimal("3500.0"),
            "SOL/USDT": Decimal("150.0"),
            "ADA/USDT": Decimal("0.45"),
            "DOT/USDT": Decimal("7.5"),
        }

        for symbol, price in default_prices.items():
            spread = price * Decimal("0.001")  # 0.1% spread
            self.tickers[symbol] = MockTicker(
                symbol=symbol,
                last=price,
                bid=price - spread,
                ask=price + spread,
                high=price * Decimal("1.02"),
                low=price * Decimal("0.98"),
                volume=Decimal("1000000"),
            )

    def _start_price_updates(self):
        """启动价格更新任务"""

        async def update_prices():
            while True:
                await asyncio.sleep(1)  # 每秒更新一次
                self._update_prices()

        # 在实际使用中，这个任务会在事件循环中运行
        # 这里只是为了演示，实际测试中可能需要手动调用
        pass

    def _update_prices(self):
        """更新价格（模拟市场波动）"""
        for symbol, ticker in self.tickers.items():
            # 随机价格变动 (-0.5% to +0.5%)
            change_rate = Decimal(str(random.uniform(-0.005, 0.005)))
            new_price = ticker.last * (Decimal("1") + change_rate)

            # 确保价格不会变成负数
            new_price = max(new_price, Decimal("0.01"))

            spread = new_price * Decimal("0.001")
            ticker.last = new_price
            ticker.bid = new_price - spread
            ticker.ask = new_price + spread
            ticker.timestamp = datetime.now(timezone.utc)

    async def _simulate_latency(self):
        """模拟网络延迟"""
        if self.latency_ms > 0:
            await asyncio.sleep(self.latency_ms / 1000)

    def _should_simulate_error(self) -> bool:
        """判断是否应该模拟错误"""
        return random.random() < self.error_rate

    async def get_ticker(self, symbol: str) -> Dict[str, Any]:
        """获取价格数据"""
        await self._simulate_latency()

        if self._should_simulate_error():
            raise Exception(f"Network error getting ticker for {symbol}")

        if symbol not in self.tickers:
            raise Exception(f"Symbol {symbol} not found")

        ticker = self.tickers[symbol]
        return {
            "symbol": ticker.symbol,
            "last": float(ticker.last),
            "bid": float(ticker.bid),
            "ask": float(ticker.ask),
            "high": float(ticker.high),
            "low": float(ticker.low),
            "volume": float(ticker.volume),
            "timestamp": ticker.timestamp.isoformat(),
        }

    async def get_price(self, symbol: str) -> Decimal:
        """获取当前价格"""
        ticker_data = await self.get_ticker(symbol)
        return Decimal(str(ticker_data["last"]))

    async def create_market_order(
        self, symbol: str, side: str, quantity: Decimal
    ) -> Dict[str, Any]:
        """创建市价订单"""
        await self._simulate_latency()

        if self._should_simulate_error():
            raise Exception("Failed to create order: Insufficient balance")

        if not self.market_open:
            raise Exception("Market is closed")

        if self.maintenance_mode:
            raise Exception("Exchange is under maintenance")

        # 创建订单
        order_id = str(uuid.uuid4())
        client_order_id = f"mock_{uuid.uuid4()}"

        order = MockOrder(
            id=order_id,
            client_order_id=client_order_id,
            symbol=symbol,
            side=side,
            order_type="market",
            quantity=quantity,
        )

        # 模拟立即成交
        ticker = self.tickers.get(symbol)
        if not ticker:
            raise Exception(f"Symbol {symbol} not supported")

        # 计算成交价格（考虑滑点）
        base_price = ticker.ask if side == "buy" else ticker.bid
        slippage = base_price * Decimal(str(self.slippage_rate))
        fill_price = base_price + slippage if side == "buy" else base_price - slippage

        order.status = "filled"
        order.filled_quantity = quantity
        order.average_price = fill_price
        order.filled_at = datetime.now(timezone.utc)

        # 存储订单
        self.orders[order_id] = order
        self.order_history.append(order)

        return {
            "id": order_id,
            "client_order_id": client_order_id,
            "symbol": symbol,
            "side": side,
            "type": "market",
            "quantity": float(quantity),
            "filled_quantity": float(order.filled_quantity),
            "status": order.status,
            "average_price": float(fill_price),
            "created_at": order.created_at.isoformat(),
            "filled_at": order.filled_at.isoformat() if order.filled_at else None,
        }

    async def create_limit_order(
        self, symbol: str, side: str, quantity: Decimal, price: Decimal
    ) -> Dict[str, Any]:
        """创建限价订单"""
        await self._simulate_latency()

        if self._should_simulate_error():
            raise Exception("Failed to create limit order: Invalid price")

        order_id = str(uuid.uuid4())
        client_order_id = f"mock_{uuid.uuid4()}"

        order = MockOrder(
            id=order_id,
            client_order_id=client_order_id,
            symbol=symbol,
            side=side,
            order_type="limit",
            quantity=quantity,
            price=price,
        )

        # 限价订单通常不会立即成交
        order.status = "open"

        self.orders[order_id] = order

        return {
            "id": order_id,
            "client_order_id": client_order_id,
            "symbol": symbol,
            "side": side,
            "type": "limit",
            "quantity": float(quantity),
            "price": float(price),
            "status": order.status,
            "created_at": order.created_at.isoformat(),
        }

    async def cancel_order(self, order_id: str) -> Dict[str, Any]:
        """取消订单"""
        await self._simulate_latency()

        if order_id not in self.orders:
            raise Exception(f"Order {order_id} not found")

        order = self.orders[order_id]
        if order.status in ["filled", "cancelled"]:
            raise Exception(f"Cannot cancel order in status: {order.status}")

        order.status = "cancelled"

        return {
            "id": order_id,
            "status": "cancelled",
            "cancelled_at": datetime.now(timezone.utc).isoformat(),
        }

    async def get_order(self, order_id: str) -> Dict[str, Any]:
        """获取订单信息"""
        await self._simulate_latency()

        if order_id not in self.orders:
            raise Exception(f"Order {order_id} not found")

        order = self.orders[order_id]
        return {
            "id": order.id,
            "client_order_id": order.client_order_id,
            "symbol": order.symbol,
            "side": order.side,
            "type": order.order_type,
            "quantity": float(order.quantity),
            "filled_quantity": float(order.filled_quantity),
            "price": float(order.price) if order.price else None,
            "average_price": float(order.average_price)
            if order.average_price
            else None,
            "status": order.status,
            "created_at": order.created_at.isoformat(),
            "filled_at": order.filled_at.isoformat() if order.filled_at else None,
        }

    async def get_balance(self) -> Dict[str, Decimal]:
        """获取账户余额（模拟）"""
        await self._simulate_latency()

        # 返回模拟余额
        return {
            "USDT": Decimal("10000.0"),
            "BTC": Decimal("0.1"),
            "ETH": Decimal("2.0"),
            "SOL": Decimal("50.0"),
        }

    def set_error_rate(self, rate: float):
        """设置错误率"""
        self.error_rate = max(0.0, min(1.0, rate))

    def set_latency(self, ms: int):
        """设置延迟"""
        self.latency_ms = max(0, ms)

    def set_market_status(self, open: bool):
        """设置市场状态"""
        self.market_open = open

    def set_maintenance_mode(self, enabled: bool):
        """设置维护模式"""
        self.maintenance_mode = enabled

    def reset(self):
        """重置交易所状态"""
        self.orders.clear()
        self.order_history.clear()
        self._init_default_prices()
        self.error_rate = 0.0
        self.latency_ms = 100
        self.market_open = True
        self.maintenance_mode = False


# 全局Mock交易所实例
mock_exchange = MockExchange()


class MockExchangeService:
    """Mock交易所服务适配器"""

    def __init__(self):
        self.exchange = mock_exchange

    async def get_market_price(self, symbol: str) -> Decimal:
        """获取市场价格"""
        return await self.exchange.get_price(symbol)

    async def execute_trade(self, trade_plan: TradePlan) -> TradeResult:
        """执行交易"""
        try:
            if trade_plan.order_type == OrderType.MARKET:
                result = await self.exchange.create_market_order(
                    symbol=trade_plan.symbol,
                    side=trade_plan.side.value,
                    quantity=trade_plan.quantity,
                )
            else:
                result = await self.exchange.create_limit_order(
                    symbol=trade_plan.symbol,
                    side=trade_plan.side.value,
                    quantity=trade_plan.quantity,
                    price=trade_plan.price,
                )

            return TradeResult(
                status="success",
                order_id=result["id"],
                client_order_id=result["client_order_id"],
            )

        except Exception as e:
            return TradeResult(
                status="failure",
                client_order_id=f"failed_{uuid.uuid4()}",
                error_message=str(e),
            )
