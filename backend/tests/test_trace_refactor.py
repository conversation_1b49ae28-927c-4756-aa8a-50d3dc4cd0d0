"""
测试追踪服务重构后的功能
验证统一的数据提取方法和向后兼容性
"""

import pytest
import uuid
from datetime import datetime, timezone
from unittest.mock import Mock, AsyncMock
from types import SimpleNamespace

from app.services.agent_trace_service import AgentTraceService, trace_service
from app.core.schemas import AgentState, ParsedIntent, TradePlan


class TestTraceServiceRefactor:
    """测试追踪服务重构功能"""

    def setup_method(self):
        """测试前准备"""
        self.service = AgentTraceService()
        self.mock_state = self._create_mock_state()

    def _create_mock_state(self):
        """创建模拟的AgentState对象"""
        state = Mock(spec=AgentState)
        state.task_id = uuid.uuid4()
        state.user_id = uuid.uuid4()
        state.signal_id = uuid.uuid4()
        state.raw_input = "买入BTC 100U"
        state.parsed_intents = [
            Mock(
                intent_type="CREATE_ORDER",
                symbol="BTC/USDT",
                side="buy",
                confidence=0.95,
                clarification_needed=None
            )
        ]
        state.context = {
            "risk_config": {"max_position_size": 1000},
            "active_orders": []
        }
        state.execution_plan = [
            Mock(quantity=100.0)
        ]
        state.retry_count = 0
        return state

    def test_extract_input_data(self):
        """测试统一的输入数据提取"""
        # 测试Parse节点
        result = self.service._extract_input_data(self.mock_state, "Parse")

        assert result["task_id"] == str(self.mock_state.task_id)
        assert result["user_id"] == str(self.mock_state.user_id)
        assert result["signal_id"] == str(self.mock_state.signal_id)
        assert result["raw_input"] == "买入BTC 100U"
        assert result["input_length"] == len("买入BTC 100U")
        # 语言检测基于中文字符比例，"买入BTC 100U"中中文字符比例不足30%
        assert result["language_detected"] == "english"
        assert result["preprocessing_applied"] is True

    def test_extract_output_data(self):
        """测试统一的输出数据提取"""
        result = self.service._extract_output_data(self.mock_state, "Parse")

        assert result["task_id"] == str(self.mock_state.task_id)
        assert result["node_name"] == "Parse"
        assert result["execution_successful"] is True
        assert "timestamp" in result



    def test_all_nodes_data_extraction(self):
        """测试所有节点的统一数据提取"""
        nodes = ["Parse", "Context", "Plan", "Risk", "Execute"]

        for node in nodes:
            # 测试输入数据提取
            input_result = self.service._extract_input_data(self.mock_state, node)
            assert input_result["task_id"] == str(self.mock_state.task_id)
            assert input_result["node_name"] == node

            # 测试输出数据提取
            output_result = self.service._extract_output_data(self.mock_state, node)
            assert output_result["task_id"] == str(self.mock_state.task_id)
            assert output_result["node_name"] == node

    def test_language_detection(self):
        """测试语言检测功能"""
        # 测试中文
        chinese_result = self.service._detect_language("买入BTC")
        assert chinese_result == "chinese"
        
        # 测试英文
        english_result = self.service._detect_language("buy BTC")
        assert english_result == "english"
        
        # 测试空字符串
        empty_result = self.service._detect_language("")
        assert empty_result == "unknown"

    def test_calculate_total_amount(self):
        """测试总金额计算"""
        plans = [Mock(quantity=100.0), Mock(quantity=200.0)]
        total = self.service._calculate_total_amount(plans)
        assert total == 300.0

    def test_confidence_calculation(self):
        """测试置信度计算"""
        intents = [
            Mock(confidence=0.8),
            Mock(confidence=0.9),
            Mock(confidence=0.7)
        ]
        avg_confidence = self.service._calculate_avg_confidence(intents)
        assert abs(avg_confidence - 0.8) < 0.001  # 处理浮点数精度问题

    @pytest.mark.asyncio
    async def test_trace_decorator_integration(self):
        """测试追踪装饰器的集成功能"""
        from app.services.agent_trace_service import trace_agent_node
        
        # 创建模拟的数据库会话
        mock_db = AsyncMock()
        
        # 创建被装饰的函数
        @trace_agent_node("Parse")
        async def mock_parse_node(state, db):
            return Mock(parsed_intents=["intent1", "intent2"])
        
        # 执行测试
        result = await mock_parse_node(self.mock_state, mock_db)
        
        # 验证结果
        assert result is not None
        assert hasattr(result, 'parsed_intents')


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
