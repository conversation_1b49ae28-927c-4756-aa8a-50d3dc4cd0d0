"""
测试数据工厂 - 使用Factory Boy创建测试数据
"""
import uuid
from datetime import datetime, timezone
from decimal import Decimal

import factory
import factory.fuzzy
from factory.alchemy import SQLAlchemyModelFactory
from faker import Faker

from app.core.auth import AuthManager
from app.core.models import (AgentCheckpoint, ConditionalOrder, ExchangeConfig,
                             Order, PendingAction, RiskConfig, User)
from app.core.schemas import (AgentState, IntentType, OrderType, ParsedIntent,
                              TradePlan, TradeSide)

fake = Faker()


class UserFactory(SQLAlchemyModelFactory):
    """用户工厂"""

    class Meta:
        model = User
        sqlalchemy_session_persistence = "commit"

    username = factory.Sequence(lambda n: f"user{n}")
    password_hash = factory.LazyAttribute(
        lambda obj: AuthManager.hash_password("testpassword123")
    )


class RiskConfigFactory(SQLAlchemyModelFactory):
    """风控配置工厂"""

    class Meta:
        model = RiskConfig
        sqlalchemy_session_persistence = "commit"

    user_id = factory.LazyFunction(lambda: uuid.uuid4())
    max_concurrent_orders = factory.fuzzy.FuzzyInteger(1, 10)
    max_total_position_value_usd = factory.fuzzy.FuzzyDecimal(1000, 5000)
    max_position_size_usd = factory.fuzzy.FuzzyDecimal(200, 1000)
    default_position_size_usd = factory.LazyAttribute(
        lambda obj: min(float(obj.max_position_size_usd) * 0.5, 100.0)
    )
    allowed_symbols = factory.LazyFunction(lambda: ["BTC/USDT", "ETH/USDT", "SOL/USDT"])
    confidence_threshold = factory.fuzzy.FuzzyDecimal(0.5, 0.95)


class OrderFactory(SQLAlchemyModelFactory):
    """订单工厂"""

    class Meta:
        model = Order
        sqlalchemy_session_persistence = "commit"

    # 不设置id，让数据库自动生成
    user_id = factory.LazyFunction(lambda: uuid.uuid4())
    client_order_id = factory.LazyFunction(lambda: f"order_{uuid.uuid4()}")
    exchange_order_id = factory.LazyFunction(lambda: str(uuid.uuid4()))
    symbol = factory.fuzzy.FuzzyChoice(["BTC/USDT", "ETH/USDT", "SOL/USDT"])
    side = factory.fuzzy.FuzzyChoice(["buy", "sell"])
    quantity = factory.fuzzy.FuzzyDecimal(0.001, 1.0)
    entry_price = factory.fuzzy.FuzzyDecimal(100, 100000)
    status = factory.fuzzy.FuzzyChoice(["active", "closed", "failed", "cancelled"])


class ExchangeConfigFactory(SQLAlchemyModelFactory):
    """交易所配置工厂"""

    class Meta:
        model = ExchangeConfig
        sqlalchemy_session_persistence = "commit"

    # 不设置id，让数据库自动生成
    user_id = factory.LazyFunction(lambda: uuid.uuid4())
    exchange_name = factory.fuzzy.FuzzyChoice(["binance", "okx", "bybit"])
    encrypted_api_key = factory.LazyFunction(lambda: fake.sha256())
    encrypted_api_secret = factory.LazyFunction(lambda: fake.sha256())
    sandbox_mode = True
    is_active = True


class ConditionalOrderFactory(SQLAlchemyModelFactory):
    """条件订单工厂"""

    class Meta:
        model = ConditionalOrder
        sqlalchemy_session_persistence = "commit"

    # 不设置id，让数据库自动生成
    user_id = factory.LazyFunction(lambda: uuid.uuid4())
    symbol = factory.fuzzy.FuzzyChoice(["BTC/USDT", "ETH/USDT", "SOL/USDT"])
    trigger_condition = factory.LazyFunction(
        lambda: {
            "type": "price_above",
            "price": float(
                fake.pydecimal(left_digits=5, right_digits=2, positive=True)
            ),
        }
    )
    action_plan = factory.LazyFunction(
        lambda: {
            "action": "create_order",
            "side": fake.random_element(["buy", "sell"]),
            "quantity_usd": float(
                fake.pydecimal(left_digits=3, right_digits=2, positive=True)
            ),
        }
    )
    status = factory.fuzzy.FuzzyChoice(["PENDING", "TRIGGERED", "CANCELLED", "EXPIRED"])


class PendingActionFactory(SQLAlchemyModelFactory):
    """待处理动作工厂"""

    class Meta:
        model = PendingAction
        sqlalchemy_session_persistence = "commit"

    # 不设置id，让数据库自动生成
    task_id = factory.LazyFunction(lambda: str(uuid.uuid4()))
    user_id = factory.LazyFunction(lambda: uuid.uuid4())
    action_type = factory.fuzzy.FuzzyChoice(
        ["USER_CONFIRMATION", "RISK_OVERRIDE", "MANUAL_INTERVENTION"]
    )
    details = factory.LazyFunction(
        lambda: {
            "raw_input": fake.sentence(),
            "clarification_needed": fake.sentence(),
            "confidence_score": float(
                fake.pydecimal(
                    left_digits=1, right_digits=2, positive=True, max_value=1
                )
            ),
        }
    )
    status = factory.fuzzy.FuzzyChoice(["PENDING", "APPROVED", "REJECTED", "EXPIRED"])
    expires_at = factory.LazyFunction(lambda: fake.future_datetime(end_date="+1d"))


class AgentCheckpointFactory(SQLAlchemyModelFactory):
    """Agent检查点工厂"""

    class Meta:
        model = AgentCheckpoint
        sqlalchemy_session_persistence = "commit"

    # 不设置id，让数据库自动生成
    task_id = factory.LazyFunction(lambda: str(uuid.uuid4()))  # 转换为字符串
    user_id = factory.LazyFunction(lambda: uuid.uuid4())
    node_name = factory.fuzzy.FuzzyChoice(
        [
            "Preprocess",
            "Parse",
            "Context",
            "Plan",
            "Risk",
            "Execute",
            "AnalyzeError",
            "UserConfirm",
            "Success",
            "Failure",
        ]
    )
    state_data = factory.LazyFunction(
        lambda: {
            "task_id": str(uuid.uuid4()),
            "user_id": fake.random_int(min=1, max=1000),
            "raw_input": fake.sentence(),
            "parsed_intents": [],
            "context": {},
            "execution_plan": [],
            "log": [fake.sentence() for _ in range(fake.random_int(min=1, max=5))],
        }
    )


# ============================================================================
# Pydantic模型工厂
# ============================================================================


class ParsedIntentFactory(factory.Factory):
    """解析意图工厂"""

    class Meta:
        model = ParsedIntent

    intent_type = factory.fuzzy.FuzzyChoice(
        [
            IntentType.CREATE_ORDER,
            IntentType.CLOSE_ORDER,
            IntentType.MODIFY_ORDER,
            IntentType.QUERY_STATUS,
        ]
    )
    raw_text = factory.LazyFunction(lambda: fake.sentence())
    side = factory.fuzzy.FuzzyChoice([TradeSide.BUY, TradeSide.SELL, None])
    symbol = factory.fuzzy.FuzzyChoice(["BTC/USDT", "ETH/USDT", "SOL/USDT", None])
    quantity_usd = factory.LazyAttribute(
        lambda obj: Decimal(
            str(fake.pydecimal(left_digits=3, right_digits=2, positive=True))
        )
        if fake.boolean()
        else None
    )
    confidence = factory.LazyFunction(
        lambda: Decimal(
            str(
                fake.pydecimal(
                    left_digits=1, right_digits=2, positive=True, max_value=1
                )
            )
        )
    )


class TradePlanFactory(factory.Factory):
    """交易计划工厂"""

    class Meta:
        model = TradePlan

    symbol = factory.fuzzy.FuzzyChoice(["BTC/USDT", "ETH/USDT", "SOL/USDT"])
    side = factory.fuzzy.FuzzyChoice([TradeSide.BUY, TradeSide.SELL])
    order_type = factory.fuzzy.FuzzyChoice([OrderType.MARKET, OrderType.LIMIT])
    quantity = factory.LazyFunction(
        lambda: Decimal(
            str(fake.pydecimal(left_digits=1, right_digits=8, positive=True))
        )
    )
    price = factory.LazyAttribute(
        lambda obj: Decimal(
            str(fake.pydecimal(left_digits=5, right_digits=2, positive=True))
        )
        if obj.order_type == OrderType.LIMIT
        else None
    )


class AgentStateFactory(factory.Factory):
    """Agent状态工厂"""

    class Meta:
        model = AgentState

    user_id = factory.LazyFunction(lambda: uuid.uuid4())
    raw_input = factory.LazyFunction(lambda: fake.sentence())
    parsed_intents = factory.LazyFunction(lambda: [])
    context = factory.LazyFunction(lambda: {})
    execution_plan = factory.LazyFunction(lambda: [])
    error_message = None
    retry_count = 0
    pending_action_id = None
    user_response = None
    final_result = None
    log = factory.LazyFunction(lambda: [])


# ============================================================================
# 特殊场景工厂
# ============================================================================


class BuyOrderFactory(OrderFactory):
    """买单工厂"""

    side = "buy"
    symbol = "BTC/USDT"
    entry_price = factory.fuzzy.FuzzyDecimal(65000, 70000)


class SellOrderFactory(OrderFactory):
    """卖单工厂"""

    side = "sell"
    symbol = "BTC/USDT"
    entry_price = factory.fuzzy.FuzzyDecimal(65000, 70000)


class ActiveOrderFactory(OrderFactory):
    """活跃订单工厂"""

    status = "active"


class ClosedOrderFactory(OrderFactory):
    """已关闭订单工厂"""

    status = "closed"
    close_price = factory.fuzzy.FuzzyDecimal(65000, 70000)
    pnl = factory.fuzzy.FuzzyDecimal(-1000, 1000)


class ProfitableOrderFactory(ClosedOrderFactory):
    """盈利订单工厂"""

    pnl = factory.fuzzy.FuzzyDecimal(1, 1000)


class LossOrderFactory(ClosedOrderFactory):
    """亏损订单工厂"""

    pnl = factory.fuzzy.FuzzyDecimal(-1000, -1)


class CreateOrderIntentFactory(ParsedIntentFactory):
    """创建订单意图工厂"""

    intent_type = IntentType.CREATE_ORDER
    side = TradeSide.BUY
    symbol = "BTC/USDT"
    quantity_usd = Decimal("100.0")
    confidence = Decimal("0.95")


class CloseOrderIntentFactory(ParsedIntentFactory):
    """平仓意图工厂"""

    intent_type = IntentType.CLOSE_ORDER
    side = TradeSide.SELL
    symbol = "BTC/USDT"
    confidence = Decimal("0.90")


class AmbiguousIntentFactory(ParsedIntentFactory):
    """模糊意图工厂"""

    intent_type = IntentType.AMBIGUOUS
    side = None
    clarification_needed = factory.LazyFunction(lambda: fake.sentence())
    confidence = factory.fuzzy.FuzzyDecimal(0.1, 0.6)


# ============================================================================
# 批量创建工具
# ============================================================================


def create_test_user_with_orders(session, order_count=5):
    """创建带有多个订单的测试用户"""
    user = UserFactory(sqlalchemy_session=session)
    orders = [
        OrderFactory(sqlalchemy_session=session, user_id=user.id)
        for _ in range(order_count)
    ]
    return user, orders


def create_test_user_with_risk_config(session):
    """创建带有风控配置的测试用户"""
    user = UserFactory(sqlalchemy_session=session)
    risk_config = RiskConfigFactory(sqlalchemy_session=session, user_id=user.id)
    return user, risk_config


def create_complete_test_scenario(session):
    """创建完整的测试场景"""
    user = UserFactory(sqlalchemy_session=session)
    risk_config = RiskConfigFactory(sqlalchemy_session=session, user_id=user.id)
    exchange_config = ExchangeConfigFactory(sqlalchemy_session=session, user_id=user.id)

    # 创建不同状态的订单
    active_orders = [
        ActiveOrderFactory(sqlalchemy_session=session, user_id=user.id)
        for _ in range(2)
    ]
    closed_orders = [
        ClosedOrderFactory(sqlalchemy_session=session, user_id=user.id)
        for _ in range(3)
    ]

    return {
        "user": user,
        "risk_config": risk_config,
        "exchange_config": exchange_config,
        "active_orders": active_orders,
        "closed_orders": closed_orders,
    }
