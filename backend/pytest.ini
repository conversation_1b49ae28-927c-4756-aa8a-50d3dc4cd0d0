[pytest]
# 测试发现配置
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# 异步测试配置
asyncio_mode = auto

# 标记定义 - 根据PLAYWRIGHT_TESTING_STRATEGY_ANALYSIS重构
markers =
    unit: 单元测试 - 测试独立的函数和类 (pytest专用)
    integration: 集成测试 - 测试模块间的交互 (pytest专用)
    database: 数据库测试 - 测试数据库模型和操作 (pytest专用)
    business_logic: 业务逻辑测试 - 测试核心业务算法 (pytest专用)
    models: 模型测试 - 测试数据模型验证和关系 (pytest专用)
    services: 服务测试 - 测试内部服务组件 (pytest专用)
    utils: 工具函数测试 - 测试辅助工具函数 (pytest专用)
    websocket: WebSocket测试 - 测试WebSocket连接和消息
    slow: 慢速测试 - 运行时间较长的测试
    simulation: 仿真模式测试 - 使用Mock交易所的测试
    property: 属性测试 - 使用Hypothesis的属性测试

# 输出配置 - 统一输出到temp目录
addopts =
    -v
    --strict-markers
    --strict-config
    --tb=short
    --cov=app
    --cov-report=html:../temp/backend/coverage/html
    --cov-report=term-missing
    --cov-report=xml:../temp/backend/coverage/coverage.xml
    --cov-report=json:../temp/backend/coverage/coverage.json
    --junitxml=../temp/backend/reports/junit.xml
    ; --cov-fail-under=80 // TODO 启用覆盖率检查
    --durations=10

# 过滤警告
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore:.*unclosed.*:ResourceWarning

# 最小版本要求
minversion = 7.0

# 测试超时（秒）- 需要pytest-timeout插件
# timeout = 60  # 暂时禁用，避免配置错误

# 并行测试配置（需要pytest-xdist）
# 使用 -n auto 自动检测CPU核心数
# 使用 -n 4 指定4个进程

# 日志配置
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# 测试数据库配置
# 这些环境变量会在测试中使用
# 根据《0. 项目规范.md》要求使用PostgreSQL数据库
# 注意：env配置不是pytest标准选项，环境变量应在测试运行时设置
