# AI Agent 驱动的加密货币智能跟单系统

基于LangGraph状态机的AI Agent实现的加密货币智能跟单系统。

## 项目结构

项目采用Monorepo风格的结构，将前端、后端和部署配置清晰地分离在同一个代码仓库中，便于统一管理。

```
/
├── .env.example                # 环境变量模板文件
├── .gitignore                  # Git 忽略文件配置
├── docker-compose.yml          # Docker Compose 编排文件
├── README.md                   # 项目总说明文档
|
├── backend/                    # 【后端】FastAPI 单体应用
│   ├── alembic/                # Alembic 数据库迁移脚本目录
│   ├── app/                    # FastAPI 应用核心源代码
│   │   ├── api/                # API 路由层
│   │   ├── agent/              # AI 核心 (LangGraph)
│   │   ├── core/               # 核心业务逻辑、模型和配置
│   │   │   ├── config.py       # 集中化配置管理
│   │   ├── services/           # 后台服务和外部系统交互
│   │   ├── utils/              # 工具函数
│   │   └── main.py             # FastAPI 应用入口
│   ├── tests/                  # 测试代码
│   ├── .env.example            # 后端环境变量模板
│   ├── Dockerfile              # 后端服务的 Dockerfile
│   └── requirements.txt        # Python 依赖
|
└── frontend/                   # 【前端】Vue.js 3 应用
    ├── public/                 # 静态资源
    ├── src/                    # 源代码
    │   ├── api/                # 封装对后端 API 的调用
    │   ├── assets/             # 静态资源
    │   ├── components/         # Vue 组件
    │   ├── composables/        # Vue 3 组合式函数
    │   ├── router/             # Vue Router 路由配置
    │   ├── stores/             # Pinia 状态管理
    │   ├── views/              # 页面级组件
    │   └── main.js             # Vue 应用入口
    ├── Dockerfile              # 前端应用的 Dockerfile
    ├── package.json            # Node.js 依赖与脚本
    └── vite.config.js          # Vite 配置文件
```

## 技术栈

- **后端**:
  - Python 3.11+
  - FastAPI
  - LangGraph (AI Agent 框架)
  - SQLAlchemy 2.x (Async)
  - PostgreSQL
  - Alembic (数据库迁移)
  - Pydantic Settings (集中式配置)

- **前端**:
  - Vue.js 3 (Composition API)
  - Vuetify 3
  - Pinia (状态管理)
  - Vue Router

- **部署**:
  - Docker & Docker Compose

## 快速开始

### 环境准备

1. 安装 [Docker](https://docs.docker.com/get-docker/) 和 [Docker Compose](https://docs.docker.com/compose/install/)
2. 克隆本仓库
3. 复制环境变量模板并修改

```bash
cp backend/.env.example backend/.env
# 编辑 backend/.env 文件，填入必要的配置
```

### 启动服务

```bash
docker-compose up -d
```

访问:
- 前端: http://localhost:3000
- API文档: http://localhost:8000/docs

### 开发模式

#### 后端开发

推荐使用 Anaconda 创建虚拟环境:

```bash
# 创建环境
conda env create -f environment.yml

# 激活环境
conda activate crypto-trader

# 安装开发依赖
pip install -r backend/requirements-dev.txt

# 运行数据库迁移
cd backend
alembic upgrade head

# 启动开发服务器
uvicorn app.main:app --reload
```

#### 前端开发

```bash
cd frontend
npm install
npm run dev
```

## 配置系统

项目使用集中式配置管理，基于Pydantic Settings实现。所有配置参数都集中在`backend/app/core/config.py`模块中。

### 配置分组

配置项按功能域被分成以下几组:

- **数据库配置 (db)**: 数据库连接、连接池等
- **安全配置 (security)**: 加密密钥、JWT、密码哈希等
- **API配置 (api)**: API主机、端口、CORS等
- **LLM配置 (llm)**: OpenAI/Anthropic密钥、模型选择等
- **Discord配置 (discord)**: Discord用户令牌、消息过滤配置等
- **交易配置 (trading)**: 交易所、模拟模式等

### 环境变量

所有配置都可以通过环境变量覆盖，详见`backend/.env.example`。主要环境变量:

```
# 必要配置
APP_SECRET_KEY=<加密密钥>
JWT_SECRET_KEY=<JWT密钥>
DATABASE_URL=<数据库连接URL>
OPENAI_API_KEY=<OpenAI API密钥>

# 可选配置
SIMULATION_MODE=true  # 启用模拟交易模式
DISCORD_TOKEN=<Discord用户令牌>  # 启用Discord监听
DISCORD_FILTER_CONFIG=<Discord过滤配置>  # 配置消息过滤规则
DEFAULT_USER_ID=<默认用户ID>  # 用于存储Discord消息
```

详细说明请参考`backend/app/core/README.md`。

## 🧪 测试

### 快速开始

#### 环境检查（首次运行推荐）
```bash
# 检查测试环境是否就绪
python scripts/test/check_test_environment.py

# 这将检查所有依赖、配置和环境变量
```

#### 统一测试脚本（推荐 - 架构一致性版）
```bash
# 查看测试命令清单 - 使用架构一致性版本（代码量减少61%）
python scripts/test.py --all                  # 显示所有测试命令（不执行）
python scripts/test.py --execute-all          # 实际运行所有测试

# 后端测试（委托给Python脚本）
python scripts/test.py --pytest unit           # 单元测试
python scripts/test.py --pytest integration    # 集成测试
python scripts/test.py --pytest e2e           # 端到端测试

# 前端测试（统一委托模式）
python scripts/test.py --vitest unit           # 前端单元测试
python scripts/test.py --vitest components     # 前端组件测试
python scripts/test.py --playwright api        # API测试
python scripts/test.py --playwright e2e        # E2E测试

# 环境管理
python scripts/test.py --vitest validate      # 验证前端测试环境
python scripts/test.py --check-env            # 检查测试环境
python scripts/test.py --status               # 查看服务状态
```

#### 专门的测试脚本（架构一致性版）
```bash
# 后端测试 - 使用架构一致性版本（代码量减少46%）
python scripts/test/backend/run_backend_tests.py unit        # 单元测试
python scripts/test/backend/run_backend_tests.py integration # 集成测试
python scripts/test/backend/run_backend_tests.py all         # 所有后端测试

# 前端单元测试 - 新集成到统一系统
node scripts/test/frontend/run_unit_tests.js unit           # 单元测试
node scripts/test/frontend/run_unit_tests.js components     # 组件测试
node scripts/test/frontend/run_unit_tests.js validate       # 环境验证

# 前端API测试 - 使用架构一致性版本（代码量减少57%）
python scripts/test/frontend/run_api_tests.py all      # API测试
python scripts/test/frontend/run_api_tests.py auth     # 认证测试
python scripts/test/frontend/run_api_tests.py validate # 环境验证

# 前端E2E测试 - 使用架构一致性版本（代码量减少54%）
python scripts/test/frontend/run_e2e_tests.py all      # E2E测试
python scripts/test/frontend/run_e2e_tests.py smoke    # 冒烟测试
python scripts/test/frontend/run_e2e_tests.py validate # 环境验证
```

#### 项目构建脚本（极简重构版）
```bash
# 项目构建 - 使用极简版本（代码量减少53%）
python scripts/build.py build      # 构建前端项目
python scripts/build.py install    # 安装前端依赖
python scripts/build.py clean      # 清理构建文件
python scripts/build.py clean-all  # 清理所有文件（包括依赖）
python scripts/build.py rebuild    # 重新构建（清理+安装+构建）
```

#### 数据库管理脚本（极简重构版）
```bash
# SQL脚本执行 - 使用极简版本（代码量减少47%）
python scripts/db/run_sqls.py all         # 执行完整流程
python scripts/db/run_sqls.py init        # 初始化数据库
python scripts/db/run_sqls.py create-user # 创建用户
python scripts/db/run_sqls.py mock-data   # 生成测试数据
python scripts/db/run_sqls.py cleanup     # 清理数据
python scripts/db/run_sqls.py check-health # 健康检查
```

#### 环境配置脚本（保持复杂性）
```bash
# 环境配置 - 服务启动脚本，保持必要的复杂性
python scripts/env.py test                    # 配置测试环境
python scripts/env.py dev --verbose           # 配置开发环境
python scripts/env.py prod --validate-only    # 验证生产环境
```

#### 开发服务器脚本（保持复杂性）
```bash
# 开发服务器 - 服务启动脚本，保持必要的复杂性
python scripts/start.py                       # 启动前后端服务器
python scripts/start.py backend               # 只启动后端服务器
python scripts/start.py frontend              # 只启动前端服务器
python scripts/start.py --verbose             # 显示详细日志
```

#### 分别运行测试

**后端测试**
```bash
cd backend

# 运行所有测试
pytest

# 运行单元测试
pytest -m unit

# 运行集成测试
pytest -m integration

# 生成覆盖率报告
pytest --cov=app --cov-report=html
```

**前端测试**
```bash
cd frontend

# 安装Playwright浏览器（首次运行）
npm run install:playwright

# 启动依赖服务（新终端）
# 终端1: 后端服务
cd backend && python -m uvicorn app.main:app --reload --port 8000

# 终端2: 前端服务
cd frontend && npm run dev

# 终端3: 运行E2E测试
npm run test:e2e

# 运行单元测试
npm run test

# 运行API测试
npm run test:api
```

### 测试环境要求

**后端测试**
- Python 3.8+
- pytest, pytest-asyncio, pytest-cov
- SQLite（测试数据库）

**前端测试**
- Node.js 16+
- Playwright浏览器
- 前端和后端服务运行中

### 测试覆盖率目标

- **后端总体覆盖率**: ≥ 80%
- **前端E2E覆盖率**: 主要用户流程100%
- **API测试覆盖率**: 所有端点100%

### 故障排除

**常见问题**
```bash
# 后端测试失败
export TESTING=true
export DATABASE_URL=sqlite+aiosqlite:///./test.db

# 前端E2E测试超时
# 确保服务运行在正确端口
curl http://localhost:5173  # 前端
curl http://localhost:8000/health  # 后端

# Playwright浏览器问题
npx playwright install
```

**详细文档**
- [后端测试指南](backend/tests/README.md)
- [前端测试指南](frontend/tests/README.md)
- [E2E测试优化指南](frontend/docs/E2E_TEST_OPTIMIZATION_GUIDE.md)
- [测试故障排除指南](docs/测试故障排除指南.md)

## 环境

本项目推荐使用 Anaconda/Miniconda 进行 Python 依赖管理，以获得更好的开发体验和环境稳定性。

### 创建项目环境

```bash
# 克隆项目
git clone <repository-url>
cd crypto_trader

# 创建 conda 环境
conda env create -f environment.yml

# 激活环境
conda activate crypto-trader

# 验证安装
python --version
conda list
```

### 开发工作流

**激活环境（每次开发前）:**
```bash
conda activate crypto-trader
```

**添加新依赖:**
```bash
# 优先使用 conda
conda install package_name

# 如果 conda 中没有，使用 pip
pip install package_name

# 更新环境文件
conda env export --no-builds > environment.yml
```

**同步环境（当 environment.yml 更新后）:**
```bash
conda env update -f environment.yml --prune
```

**退出环境:**
```bash
conda deactivate
```

### 常用命令

```bash
# 查看所有环境
conda env list

# 删除环境
conda env remove -n crypto-trader

# 查看当前环境的包
conda list

# 搜索包
conda search package_name

# 更新 conda 本身
conda update conda
```

## 许可证

MIT 