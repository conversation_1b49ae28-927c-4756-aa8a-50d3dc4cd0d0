# 生产环境配置 (Production Environment Configuration)

# 应用基础配置
APP_NAME=AI Crypto Trading Agent
APP_VERSION=1.0.0
DEBUG=false
LOG_LEVEL=INFO

# 数据库配置
DATABASE_URL=postgresql+asyncpg://postgres:postgres@localhost:5432/crypto_trader
DB_POOL_SIZE=20
DB_MAX_OVERFLOW=40
DB_POOL_TIMEOUT=30
DB_POOL_RECYCLE=1800
DB_ECHO=false

# PostgreSQL Docker配置
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres
POSTGRES_DB=crypto_trader

# 安全配置
APP_SECRET_KEY=your_production_secret_key_here_min_32_chars
JWT_SECRET_KEY=your_production_jwt_secret_key_min_32
JWT_ALGORITHM=HS256
JWT_EXPIRE_MINUTES=60
PASSWORD_HASH_ROUNDS=12

# API配置
API_HOST=0.0.0.0
API_PORT=8000
API_PREFIX=/api/v1
API_URL=https://your-domain.com
CORS_ORIGINS=https://your-frontend-domain.com

# LLM配置
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here
DEFAULT_LLM_PROVIDER=openai
DEFAULT_LLM_MODEL=gpt-4
LLM_MAX_RETRIES=3
LLM_REQUEST_TIMEOUT=60
LLM_MAX_TOKENS=4096

# Discord配置
DISCORD_TOKEN=your_discord_user_token_here

# Discord过滤配置 - 生产环境配置
DISCORD_FILTER_CONFIG={"enabled":true,"server_ids":[],"channel_ids":[],"author_ids":[],"allowed_message_types":["text"]}

# 默认用户ID - 生产环境
DEFAULT_USER_ID=your-default-user-uuid-here

# 交易配置
DEFAULT_EXCHANGE=binance
SIMULATION_MODE=false
PRICE_CHECK_INTERVAL=60

# ========================================
# 前端配置 (Frontend Configuration)
# ========================================

# 前端API配置 (Frontend API Configuration)
VITE_API_BASE_URL=
VITE_WS_URL=

# 前端应用配置 (Frontend Application Configuration)
VITE_APP_TITLE=AI Crypto Trading Agent
VITE_APP_VERSION=0.1.0
VITE_DEBUG=false
