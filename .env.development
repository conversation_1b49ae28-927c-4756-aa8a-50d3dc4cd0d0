# Discord集成测试开发环境配置
# 基于docker-compose.test.yml的开发环境配置

# ========================================
# 应用基础配置
# ========================================
APP_NAME=AI Crypto Trading Agent - Discord测试环境
APP_VERSION=1.0.0
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=DEBUG

# 日志配置
LOG_TO_FILE=true
LOG_FILE_PATH=logs
LOG_FILE_MAX_SIZE=10
LOG_FILE_BACKUP_COUNT=5
LOG_RETENTION_DAYS=7

# ========================================
# 数据库配置
# ========================================
DATABASE_URL=postgresql+asyncpg://crypto_trader:dev_password_123@postgres-dev:5432/crypto_trader_dev
DB_POOL_SIZE=10
DB_MAX_OVERFLOW=20
DB_POOL_TIMEOUT=30
DB_POOL_RECYCLE=1800
DB_ECHO=false

# PostgreSQL Docker配置
POSTGRES_USER=crypto_trader
POSTGRES_PASSWORD=dev_password_123
POSTGRES_DB=crypto_trader_dev

# ========================================
# 安全配置
# ========================================
APP_SECRET_KEY=development_secret_key_min_32_chars_here_change_in_production
JWT_SECRET_KEY=development_jwt_secret_key_min_32_chars_here_change_in_production
JWT_ALGORITHM=HS256
JWT_EXPIRE_MINUTES=60
PASSWORD_HASH_ROUNDS=12

# ========================================
# API配置
# ========================================
API_HOST=0.0.0.0
API_PORT=8000
API_PREFIX=/api/v1
API_URL=http://localhost:8000
CORS_ORIGINS=http://localhost:5173,http://localhost:3000

# ========================================
# LLM配置（测试环境占位符）
# ========================================
OPENAI_API_KEY=test-openai-key-placeholder
ANTHROPIC_API_KEY=test-anthropic-key-placeholder
DEFAULT_LLM_PROVIDER=openai
DEFAULT_LLM_MODEL=gpt-4
LLM_MAX_RETRIES=3
LLM_REQUEST_TIMEOUT=60
LLM_MAX_TOKENS=4096

# ========================================
# Discord配置（待填入实际配置）
# ========================================
# Discord Bot Token - 请替换为实际的Bot Token
DISCORD_TOKEN=MTIxODkzMjIxMjU1MzIyNDIyMg.GsNVs4.jXqSor2nUBLMOrllF3rpErfUJnxH_85UFFFdQI

# 已移除旧的DISCORD_CHANNEL_IDS配置，现在使用DISCORD_FILTER_CONFIG

# Discord监控配置 - 简化版本，统一使用扁平化配置格式
# 移除复杂的MONITORED_CHANNELS嵌套结构，所有配置统一在DISCORD_FILTER_CONFIG中管理
# 注意：server_ids替代了原来的guild_ids术语，更符合用户理解

# Discord过滤和监控配置 - 简化的统一配置格式
# 当前配置：完全禁用所有过滤器以便调试验证完整的消息处理流程
# DISCORD_FILTER_CONFIG={"enabled":false,"server_ids":[],"channel_ids":[],"author_ids":[],"allowed_message_types":["text"]}

# 默认用户ID - 用于存储所有捕获的消息
DEFAULT_USER_ID=708db973-fc1f-4be0-9c52-a9736a10372c

# ========================================
# Discord 代理设置（推荐使用代理）
# ========================================
# 支持 http/https/socks5 代理，请根据实际代理配置
# DISCORD_PROXY=http://127.0.0.1:7890
# DISCORD_PROXY=https://127.0.0.1:7890
# DISCORD_PROXY=socks5://127.0.0.1:1080
# DISCORD_PROXY=***************************************
# DISCORD_PROXY=http://host.docker.internal:7897

# Discord 其他配置
DISCORD_AUTO_START=true
DISCORD_RECONNECT_ATTEMPTS=5
DISCORD_RECONNECT_DELAY=30
DISCORD_MESSAGE_CACHE_SIZE=1000
DISCORD_DEDUPLICATION_WINDOW=5

# ========================================
# 交易配置
# ========================================
DEFAULT_EXCHANGE=binance
SIMULATION_MODE=true
PRICE_CHECK_INTERVAL=60

# 测试环境标识
TESTING=true

# ========================================
# 前端配置
# ========================================
VITE_API_BASE_URL=http://localhost:8000
VITE_WS_URL=ws://localhost:8000
VITE_APP_TITLE=AI Crypto Trading Agent - Discord测试
VITE_APP_VERSION=1.0.0
VITE_DEBUG=true

# 前端开发服务器配置
FRONTEND_URL=http://localhost:5173
NODE_ENV=development
