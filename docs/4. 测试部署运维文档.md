# 测试部署运维文档

## 目录
1. [Docker Compose 测试环境架构](#1-docker-compose-测试环境架构)
2. [生产环境部署策略](#2-生产环境部署策略)
3. [配置与迁移](#3-配置与迁移)
4. [运维与监控](#4-运维与监控)
5. [配置验证脚本](#5-配置验证脚本)
6. [技术栈](#6-技术栈)

---

# 1. Docker Compose 测试环境架构

## 1.1 概述

本项目已完成从传统本地服务管理到Docker Compose容器化架构的重构。新架构提供了更稳定、可重复的测试环境，支持热加载开发和自动化测试。

## 1.2 测试环境服务配置

### PostgreSQL 数据库服务
- **容器名**: `crypto_trader_postgres_test`
- **端口**: `5432:5432`
- **环境变量**:
  - `POSTGRES_DB=crypto_trader_test`
  - `POSTGRES_USER=test_user`
  - `POSTGRES_PASSWORD=test_password`
- **健康检查**: 自动检测数据库连接状态
- **数据持久化**: 使用Docker卷 `postgres_test_data`

### 后端服务 (FastAPI)
- **容器名**: `crypto_trader_backend_test`
- **端口**: `8000:8000`
- **依赖**: PostgreSQL服务
- **热加载**: 支持代码变更自动重启
- **卷挂载**: `./backend:/app` (实时同步代码)
- **健康检查**: HTTP端点检查 `/api/v1/health`

### 前端服务 (Vue.js + Vite)
- **容器名**: `crypto_trader_frontend_test`
- **端口**: `5173:5173`
- **热加载**: Vite HMR (热模块替换)
- **卷挂载**: `./frontend:/app` (实时同步代码)
- **开发模式**: 支持实时预览和调试

## 1.3 测试环境配置文件

### docker-compose.test.yml
主要的Docker Compose测试配置文件，定义了所有测试服务的配置。

```yaml
services:
  postgres-test:
    image: postgres:15
    container_name: crypto_trader_postgres_test
    environment:
      POSTGRES_DB: crypto_trader_test
      POSTGRES_USER: test_user
      POSTGRES_PASSWORD: test_password
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U test_user -d crypto_trader_test"]
      interval: 5s
      timeout: 5s
      retries: 5
    volumes:
      - postgres_test_data:/var/lib/postgresql/data

  backend-test:
    build:
      context: ./backend
      dockerfile: Dockerfile.dev
    container_name: crypto_trader_backend_test
    ports:
      - "8000:8000"
    depends_on:
      postgres-test:
        condition: service_healthy
    environment:
      - DATABASE_URL=*******************************************************/crypto_trader_test
      - ENVIRONMENT=test
    volumes:
      - ./backend:/app
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/v1/health"]
      interval: 10s
      timeout: 5s
      retries: 3

  frontend-test:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    container_name: crypto_trader_frontend_test
    ports:
      - "5173:5173"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    environment:
      - VITE_API_BASE_URL=http://localhost:8000
```

## 1.4 热加载功能

### 后端热加载
- **技术**: FastAPI + uvicorn `--reload` 模式
- **监控范围**: `/app` 目录下所有Python文件
- **重启触发**: 文件保存时自动重启服务器
- **验证方法**: 修改API端点，观察容器日志中的重启信息

### 前端热加载
- **技术**: Vite HMR (热模块替换)
- **监控范围**: `/app/src` 目录下所有前端文件
- **更新方式**: 无需重启，实时更新浏览器内容
- **验证方法**: 修改Vue组件，观察浏览器实时更新

## 1.5 测试环境服务管理

### ServiceManager 重构
新的ServiceManager类专门为Docker Compose环境设计：

```python
class ServiceManager:
    def __init__(self, compose_file: str = "docker-compose.test.yml"):
        self.compose_file = compose_file
        self.config = Config()

    async def is_service_healthy(self, service_name: str) -> bool:
        """检查Docker服务健康状态"""

    async def start_services(self, services: List[str]) -> bool:
        """启动指定的Docker服务"""

    async def stop_services(self, services: List[str]) -> bool:
        """停止指定的Docker服务"""
```

### 主要方法变更
- `check_service_health` → `is_service_healthy`
- 新增 `check_port_available` 方法（兼容性）
- 所有服务操作通过Docker Compose命令执行

## 1.6 测试环境使用方法

### 启动开发环境
```bash
# 启动所有服务
docker-compose -f docker-compose.test.yml up -d

# 启动特定服务
docker-compose -f docker-compose.test.yml up -d postgres-test backend-test

# 查看服务状态
docker-compose -f docker-compose.test.yml ps
```

### 运行测试
```bash
# 检查服务状态
python scripts/test.py --status

# 运行单元测试
python scripts/test.py --pytest unit

# 运行API测试
python scripts/test.py --playwright api

# 运行所有测试
python scripts/test.py --all
```

### 开发调试
```bash
# 查看服务日志
docker logs crypto_trader_backend_test --tail 20
docker logs crypto_trader_frontend_test --tail 20

# 进入容器调试
docker exec -it crypto_trader_backend_test bash
docker exec -it crypto_trader_frontend_test sh
```

### 停止服务
```bash
# 停止所有服务
docker-compose -f docker-compose.test.yml down

# 停止并清理卷
docker-compose -f docker-compose.test.yml down -v
```

## 1.7 测试环境优势

### 环境一致性
- 所有开发者使用相同的容器化环境
- 消除"在我机器上能运行"的问题
- 版本控制包含完整的环境配置

### 隔离性
- 每个服务在独立容器中运行
- 避免端口冲突和依赖冲突
- 测试环境与生产环境隔离

### 可扩展性
- 易于添加新的服务组件
- 支持多环境配置（开发、测试、生产）
- 便于CI/CD集成

### 开发效率
- 热加载支持快速开发迭代
- 一键启动完整开发环境
- 自动化健康检查和依赖管理

## 1.8 测试环境故障排除

### 常见问题

1. **端口占用**
   ```bash
   # 检查端口使用情况
   lsof -i :5432 -i :8000 -i :5173

   # 停止冲突的服务
   docker-compose -f docker-compose.test.yml down
   ```

2. **容器启动失败**
   ```bash
   # 查看详细日志
   docker-compose -f docker-compose.test.yml logs [service-name]

   # 重新构建镜像
   docker-compose -f docker-compose.test.yml build --no-cache
   ```

3. **热加载不工作**
   ```bash
   # 检查卷挂载
   docker inspect crypto_trader_backend_test | grep Mounts -A 10

   # 重启服务
   docker-compose -f docker-compose.test.yml restart backend-test
   ```

4. **前端HMR热加载失效**
   
   **问题症状**: 修改前端代码后浏览器不自动刷新，需要手动刷新页面
   
   **解决方案**:
   ```bash
   # 1. 检查HMR端口映射
   docker port crypto_trader_frontend_dev | grep 24678
   
   # 2. 如果端口未映射，需要重新构建容器
   docker-compose -f docker-compose.dev.yml down
   docker-compose -f docker-compose.dev.yml build frontend-dev
   docker-compose -f docker-compose.dev.yml up -d
   
   # 3. 验证HMR工作状态
   docker-compose -f docker-compose.dev.yml logs frontend-dev --tail=10
   ```
   
   **关键配置要点**:
   - Docker Compose需要映射HMR专用端口 `24678:24678`
   - 环境变量: `VITE_HMR_PORT=24678`, `VITE_HMR_HOST=0.0.0.0`
   - Vite配置需要从环境变量读取HMR设置
   - Dockerfile需要暴露HMR端口 `EXPOSE 24678`

## 1.9 迁移指南

从旧的本地服务管理迁移到Docker Compose架构时，需要注意：

1. **测试脚本更新**: 所有`check_service_health`调用已更新为`is_service_healthy`
2. **端口配置**: 服务端口通过Docker Compose配置管理
3. **环境变量**: 数据库连接等配置已容器化
4. **依赖管理**: 服务依赖通过`depends_on`自动处理

---

# 2. 生产环境部署策略

## 2.1 容器化部署

- **技术**: **Docker** & **Docker Compose**
- **实现**: 提供一个 `docker-compose.yml` 文件，定义 `backend`, `frontend` (Nginx), 两个服务，实现一键启动
- **构建优化**: 前后端均采用多阶段构建的 `Dockerfile`，以优化最终镜像的大小和安全性
- **配置管理**: 所有敏感信息和环境相关配置**必须**通过 `.env` 文件和环境变量进行管理

## 2.2 生产环境后端 Dockerfile

```dockerfile
# 多阶段构建 - 后端
FROM python:3.11-slim as builder

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 安装Python依赖
COPY requirements.txt .
RUN pip install --no-cache-dir --user -r requirements.txt

# 生产阶段
FROM python:3.11-slim

# 创建非root用户
RUN useradd --create-home --shell /bin/bash app

# 复制依赖
COPY --from=builder /root/.local /home/<USER>/.local

# 设置环境变量
ENV PATH=/home/<USER>/.local/bin:$PATH
ENV PYTHONPATH=/app

# 切换到app用户
USER app
WORKDIR /app

# 复制应用代码
COPY --chown=app:app . .

# 健康检查
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# 启动应用
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

## 2.3 生产环境前端 Dockerfile

```dockerfile
# 多阶段构建 - 前端
FROM node:18-alpine as builder

WORKDIR /app

# 复制package文件
COPY package*.json ./

# 安装依赖
RUN npm ci --only=production

# 复制源代码
COPY . .

# 构建应用
RUN npm run build

# 生产阶段
FROM nginx:alpine

# 复制构建产物
COPY --from=builder /app/dist /usr/share/nginx/html

# 复制nginx配置
COPY nginx.conf /etc/nginx/nginx.conf

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider http://localhost/ || exit 1

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
```

## 2.4 生产环境 Docker Compose 配置

```yaml
# docker-compose.yml
version: '3.8'

services:
  # 后端服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    environment:
      - DATABASE_URL=postgresql+asyncpg://${DB_USER:-postgres}:${DB_PASSWORD:-password}@postgres:5432/${DB_NAME:-crypto_trader}
      - APP_SECRET_KEY=${APP_SECRET_KEY}
      - JWT_SECRET_KEY=${JWT_SECRET_KEY}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ENVIRONMENT=${ENVIRONMENT:-production}
    volumes:
      - ./logs:/app/logs
    ports:
      - "8000:8000"
    depends_on:
      postgres:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped

  # 前端服务
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - backend
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost/"]
      interval: 30s
      timeout: 5s
      retries: 3
    restart: unless-stopped

networks:
  default:
    driver: bridge
```

---

# 3. 配置与迁移

## 3.1 配置管理

项目采用基于 **Pydantic Settings** 的分层配置策略，实现了类型安全、自动验证和环境感知的配置管理系统。

### 3.1.1 配置层次结构

配置系统按优先级从高到低的加载顺序：

1. **环境变量**：运行时动态配置，优先级最高
2. **`.env` 文件**：开发环境配置，支持多环境文件（`.env.dev`, `.env.prod`）
3. **配置文件**：业务逻辑配置（风险参数、交易对白名单等）
4. **代码默认值**：合理的默认配置，优先级最低

### 3.1.2 环境变量模板

`.env.example`：

```bash
# .env.example
# ===========================================
# AI Crypto Trading Agent - 环境配置模板
# ===========================================

# 应用基础配置
APP_NAME="AI Crypto Trading Agent"
APP_VERSION="1.0.0"
ENVIRONMENT="production"  # development, staging, production
DEBUG="false"
LOG_LEVEL="INFO"  # DEBUG, INFO, WARNING, ERROR, CRITICAL

# 安全配置（生产环境必须修改）
APP_SECRET_KEY="your-32-character-secret-key-here"
JWT_SECRET_KEY="your-32-character-jwt-secret-here"
JWT_EXPIRE_MINUTES="30"

# 数据库配置
DB_NAME="crypto_trader"
DB_USER="postgres"
DB_PASSWORD="your-secure-password-here"
DB_HOST="postgres"
DB_PORT="5432"
DATABASE_URL="postgresql+asyncpg://${DB_USER}:${DB_PASSWORD}@${DB_HOST}:${DB_PORT}/${DB_NAME}"

# API配置
API_HOST="0.0.0.0"
API_PORT="8000"
API_PREFIX="/api/v1"
CORS_ORIGINS="http://localhost:3000,https://yourdomain.com"

# LLM配置
OPENAI_API_KEY="your-openai-api-key-here"
OPENAI_MODEL="gpt-4"
ANTHROPIC_API_KEY="your-anthropic-api-key-here"

# Discord配置
DISCORD_BOT_TOKEN="your-discord-bot-token-here"
DISCORD_CHANNEL_IDS="channel1,channel2,channel3"

# 交易所配置（示例，实际配置在数据库中）
DEFAULT_EXCHANGE="binance"
SANDBOX_MODE="true"

# 风控配置
MAX_CONCURRENT_ORDERS="10"
DEFAULT_RISK_PERCENTAGE="0.02"
SUPPORTED_SYMBOLS="BTC/USDT,ETH/USDT,BNB/USDT"

# 监控配置
ENABLE_METRICS="true"
METRICS_PORT="9090"
LOKI_URL="http://loki:3100"

# 邮件通知配置（可选）
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_USER="<EMAIL>"
SMTP_PASSWORD="your-app-password"
NOTIFICATION_EMAIL="<EMAIL>"
```

## 3.2 数据库迁移 (Database Migration)

- **工具**: **Alembic**。作为 SQLAlchemy 的官方迁移工具，与数据模型紧密集成。
- **工作流**: 数据库结构（Schema）的任何变更都通过 Alembic 生成版本化的迁移脚本，并在部署流程中自动执行，确保部署的可靠性和可重复性。

---

# 4. 运维与监控

## 4.1 可观测性 (Observability)

- **结构化日志 (Structured Logging)**: 所有日志输出（使用 `structlog` 库）必须是 JSON 格式，并包含 `task_id`、`user_id`、`signal_id` 等上下文信息，便于在日志聚合平台（如 Loki）或 `docker-compose logs` 中进行筛选和分析。
- **健康检查端点**: 提供 `/health` 和 `/health/detailed` 端点，用于监控数据库、LLM API 等核心依赖的健康状况。
- **信号功能监控**: 监控信号接收率、AI解析成功率、处理延迟等关键指标，确保信号功能的稳定性。

### 4.1.1 结构化日志配置

```python
# app/core/logging.py
import structlog
import logging
from typing import Any, Dict
import json
from datetime import datetime

def setup_logging(log_level: str = "INFO", environment: str = "development"):
    """配置结构化日志"""

    # 配置标准库logging
    logging.basicConfig(
        format="%(message)s",
        level=getattr(logging, log_level.upper()),
    )

    # 配置structlog
    processors = [
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
    ]

    if environment == "development":
        # 开发环境使用彩色输出
        processors.append(structlog.dev.ConsoleRenderer())
    else:
        # 生产环境使用JSON格式
        processors.append(structlog.processors.JSONRenderer())

    structlog.configure(
        processors=processors,
        wrapper_class=structlog.stdlib.BoundLogger,
        logger_factory=structlog.stdlib.LoggerFactory(),
        cache_logger_on_first_use=True,
    )
```

### 4.1.2 健康检查端点

```python
# app/api/v1/health.py
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
from app.core.database import get_db
from app.core.config import get_settings
import asyncio
import time
from typing import Dict, Any

router = APIRouter()

@router.get("/health")
async def health_check():
    """基础健康检查"""
    return {
        "status": "healthy",
        "timestamp": time.time(),
        "version": get_settings().app_version
    }

@router.get("/health/detailed")
async def detailed_health_check(db: AsyncSession = Depends(get_db)):
    """详细健康检查"""
    checks = {}
    overall_status = "healthy"

    # 数据库连接检查
    try:
        start_time = time.time()
        await db.execute("SELECT 1")
        db_latency = (time.time() - start_time) * 1000
        checks["database"] = {
            "status": "healthy",
            "latency_ms": round(db_latency, 2)
        }
    except Exception as e:
        checks["database"] = {
            "status": "unhealthy",
            "error": str(e)
        }
        overall_status = "unhealthy"

    # LLM API检查（可选）
    try:
        # 这里可以添加对LLM API的简单检查
        checks["llm_api"] = {
            "status": "healthy",
            "provider": "openai"
        }
    except Exception as e:
        checks["llm_api"] = {
            "status": "degraded",
            "error": str(e)
        }
        if overall_status == "healthy":
            overall_status = "degraded"

    # WebSocket连接统计
    # 这里可以添加WebSocket连接数统计
    checks["websocket"] = {
        "status": "healthy",
        "active_connections": 0  # 从WebSocketManager获取
    }

    response = {
        "status": overall_status,
        "timestamp": time.time(),
        "version": get_settings().app_version,
        "checks": checks
    }

    if overall_status == "unhealthy":
        raise HTTPException(status_code=503, detail=response)

    return response
```

## 4.2 备份与恢复

- **数据库备份**: 提供 `backup_database.sh` 脚本，用于定期执行 `pg_dump` 并清理旧备份。

  ```bash
  #!/bin/bash
  # scripts/backup_database.sh

  set -e

  # 配置
  BACKUP_DIR="/backups"
  DATE=$(date +"%Y%m%d_%H%M%S")
  BACKUP_FILE="crypto_trader_backup_${DATE}.sql"
  RETENTION_DAYS=7

  # 创建备份目录
  mkdir -p $BACKUP_DIR

  # 执行备份
  echo "Starting database backup..."
  pg_dump $DATABASE_URL > "$BACKUP_DIR/$BACKUP_FILE"

  # 压缩备份文件
  gzip "$BACKUP_DIR/$BACKUP_FILE"

  # 清理旧备份
  find $BACKUP_DIR -name "crypto_trader_backup_*.sql.gz" -mtime +$RETENTION_DAYS -delete

  echo "Backup completed: $BACKUP_FILE.gz"

  # 可选：上传到云存储
  if [ ! -z "$AWS_S3_BUCKET" ]; then
      aws s3 cp "$BACKUP_DIR/$BACKUP_FILE.gz" "s3://$AWS_S3_BUCKET/backups/"
      echo "Backup uploaded to S3"
  fi
  ```

- **数据库恢复**: 提供 `restore_database.sh` 脚本，用于从备份文件中恢复数据库。

  ```bash
  #!/bin/bash
  # scripts/restore_database.sh

  set -e

  if [ -z "$1" ]; then
      echo "Usage: $0 <backup_file>"
      exit 1
  fi

  BACKUP_FILE=$1

  # 检查备份文件是否存在
  if [ ! -f "$BACKUP_FILE" ]; then
      echo "Backup file not found: $BACKUP_FILE"
      exit 1
  fi

  # 确认操作
  read -p "This will overwrite the current database. Are you sure? (y/N) " -n 1 -r
  echo
  if [[ ! $REPLY =~ ^[Yy]$ ]]; then
      echo "Operation cancelled"
      exit 1
  fi

  # 停止应用服务
  echo "Stopping application services..."
  docker-compose stop backend

  # 解压备份文件（如果需要）
  if [[ $BACKUP_FILE == *.gz ]]; then
      gunzip -c "$BACKUP_FILE" | psql $DATABASE_URL
  else
      psql $DATABASE_URL < "$BACKUP_FILE"
  fi

  # 重启应用服务
  echo "Starting application services..."
  docker-compose start backend

  echo "Database restore completed"
  ```

---

# 5. 配置验证脚本

在 CI/CD 流程或容器启动脚本中运行此脚本，可以有效防止因配置错误导致的生产事故。

```python
# scripts/validate_config.py
#!/usr/bin/env python3
"""
配置验证脚本
用于部署前验证环境配置的完整性和正确性
"""

import os
import sys
from typing import List, Tuple
import re

def validate_required_vars() -> List[str]:
    """验证必需的环境变量"""
    required_vars = [
        'APP_SECRET_KEY',
        'JWT_SECRET_KEY',
        'DATABASE_URL',
        'OPENAI_API_KEY'
    ]

    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)

    return missing_vars

def validate_secret_strength() -> List[str]:
    """验证密钥强度"""
    issues = []

    # 检查APP_SECRET_KEY
    app_secret = os.getenv('APP_SECRET_KEY', '')
    if len(app_secret) < 32:
        issues.append('APP_SECRET_KEY must be at least 32 characters long')

    if app_secret.startswith('development_') and os.getenv('ENVIRONMENT') == 'production':
        issues.append('Development secret key cannot be used in production')

    # 检查JWT_SECRET_KEY
    jwt_secret = os.getenv('JWT_SECRET_KEY', '')
    if len(jwt_secret) < 32:
        issues.append('JWT_SECRET_KEY must be at least 32 characters long')

    return issues

def validate_database_config() -> List[str]:
    """验证数据库配置"""
    issues = []

    db_url = os.getenv('DATABASE_URL', '')
    if not db_url:
        issues.append('DATABASE_URL is required')
        return issues

    # 检查数据库URL格式
    if not re.match(r'^postgresql\+asyncpg://.+', db_url):
        issues.append('DATABASE_URL must use postgresql+asyncpg driver')

    # 生产环境不能使用内存数据库
    if ':memory:' in db_url and os.getenv('ENVIRONMENT') == 'production':
        issues.append('In-memory database cannot be used in production')

    return issues

def validate_api_config() -> List[str]:
    """验证API配置"""
    issues = []

    # 检查端口号
    try:
        port = int(os.getenv('API_PORT', '8000'))
        if not (1 <= port <= 65535):
            issues.append('API_PORT must be between 1 and 65535')
    except ValueError:
        issues.append('API_PORT must be a valid integer')

    # 检查CORS配置
    cors_origins = os.getenv('CORS_ORIGINS', '')
    if not cors_origins and os.getenv('ENVIRONMENT') == 'production':
        issues.append('CORS_ORIGINS should be configured for production')

    return issues

def main():
    """主验证函数"""
    print("🔍 Validating configuration...")

    all_issues = []

    # 验证必需变量
    missing_vars = validate_required_vars()
    if missing_vars:
        all_issues.extend([f"Missing required variable: {var}" for var in missing_vars])

    # 验证密钥强度
    all_issues.extend(validate_secret_strength())

    # 验证数据库配置
    all_issues.extend(validate_database_config())

    # 验证API配置
    all_issues.extend(validate_api_config())

    if all_issues:
        print("❌ Configuration validation failed:")
        for issue in all_issues:
            print(f"  - {issue}")
        sys.exit(1)
    else:
        print("✅ Configuration validation passed!")
        sys.exit(0)

if __name__ == '__main__':
    main()
```

---

# 6. 技术栈

|                  |                        |                                                                                        |
| ---------------- | ---------------------- | -------------------------------------------------------------------------------------- |
| **领域**         | **技术/框架**          | **备注**                                                                               |
| **依赖管理**     | Anaconda/Miniconda     | **推荐**：统一的 Python 环境和包管理，支持跨平台，解决依赖冲突，提供优化的科学计算库。 |
| **备选依赖管理** | pip + requirements.txt | 传统方式，适合简单部署场景。                                                           |
| **容器化**       | Docker                 | 容器化部署，简化流程。                                                                 |
| **编排工具**     | Docker Compose         | 多容器应用编排。                                                                       |
| **后端测试**     | pytest                 | Python 测试框架。                                                                      |
| **前端测试**     | Vitest                 | 基于 Vite 的测试框架。                                                                 |
| **代码质量**     | Black, isort, flake8   | Python 代码格式化和静态分析工具。                                                      |

---

## 总结

本文档涵盖了AI Crypto Trading Agent项目的完整测试部署运维体系：

### 测试环境特点
- **Docker Compose架构**: 提供一致、可重复的测试环境
- **热加载支持**: 前后端代码变更实时生效，提高开发效率
- **自动化测试**: 集成的测试脚本支持单元测试、API测试和E2E测试
- **健康检查**: 自动监控服务状态，确保环境稳定性

### 生产环境特点
- **多阶段构建**: 优化镜像大小和安全性
- **配置管理**: 分层配置策略，支持多环境部署
- **可观测性**: 结构化日志和健康检查端点
- **备份恢复**: 自动化数据库备份和恢复流程

### 最佳实践
1. **开发阶段**: 使用Docker Compose测试环境进行开发和调试
2. **测试阶段**: 运行完整的自动化测试套件验证功能
3. **部署阶段**: 使用配置验证脚本确保环境配置正确
4. **运维阶段**: 监控服务健康状态，定期执行备份

## 7. 测试性能优化要点

### 主要性能问题
- Vitest单元测试: 4.96秒 (目标<3秒)
- 测试环境启动: 7.63秒 (目标<3秒)
- Vuetify重复警告影响输出

### 简单优化措施
1. **修复Vuetify重复警告** - 使用单例模式
2. **优化WebSocket Mock** - 减少连接延迟
3. **并行执行** - 启用Vitest threads模式

### 下一步计划
1. **CI/CD集成**: 在GitHub Actions中集成Docker Compose测试环境 ✅
2. **监控增强**: 添加Prometheus和Grafana监控服务
3. **安全加固**: 实施容器安全最佳实践
4. **性能优化**: 优化容器启动时间和资源使用 ✅
5. **测试智能化**: 实施测试选择和并行化策略 🆕
6. **质量自动化**: 建立自动化质量门禁和报告 🆕

通过本文档的指导，开发团队可以高效地进行项目开发、测试和部署，确保系统的稳定性和可维护性。

---

**文档更新**: 2025-07-24 - 添加测试性能监控和CI/CD优化策略
