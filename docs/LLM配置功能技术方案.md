# 🤖 LLM 配置功能技术方案

**版本**: 1.0 | **日期**: 2025-07-29 | **项目**: AI Crypto Trading Agent

> **实现状态**: ✅ 已完成 (2025-07-29)
>
> **验证状态**: ✅ 功能验证通过
>
> **部署状态**: ✅ 开发环境运行正常

## 📋 需求概述

### 目标
为 LLM 模块添加用户级别的配置功能，支持用户配置多个 LLM 服务提供商实例，替代当前的环境变量配置方式。

### 支持的 LLM 服务
- **DeepSeek**: 国产大模型服务
- **Gemini**: Google 的 AI 模型服务  
- **ChatGPT**: OpenAI 的 GPT 系列模型
- **Claude**: Anthropic 的 Claude 系列模型

### 功能要求
1. 每个用户可以配置多个 LLM 实例
2. 支持启用/禁用特定 LLM 配置
3. 支持设置默认 LLM 服务
4. 在"信号源"配置模块之后添加"LLM 配置"选项卡

## 🗄️ 数据模型设计

### LLM 配置表结构
```sql
CREATE TABLE llm_configs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    
    -- 基础配置
    config_name VARCHAR(100) NOT NULL,           -- 用户自定义配置名称
    provider VARCHAR(20) NOT NULL,               -- LLM服务提供商
    enabled BOOLEAN NOT NULL DEFAULT false,      -- 是否启用
    is_default BOOLEAN NOT NULL DEFAULT false,   -- 是否为默认配置
    
    -- API配置
    api_key TEXT NOT NULL,                       -- 加密存储的API密钥
    api_base_url VARCHAR(255),                   -- 自定义API基础URL（可选）
    model_name VARCHAR(100) NOT NULL,            -- 模型名称
    
    -- 请求参数配置
    max_tokens INTEGER DEFAULT 4096,             -- 最大token数
    temperature DECIMAL(3,2) DEFAULT 0.7,        -- 温度参数
    timeout_seconds INTEGER DEFAULT 60,          -- 请求超时时间
    max_retries INTEGER DEFAULT 3,               -- 最大重试次数
    
    -- 时间戳
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    
    -- 约束
    CONSTRAINT valid_provider CHECK (provider IN ('deepseek', 'gemini', 'chatgpt', 'claude')),
    CONSTRAINT valid_temperature CHECK (temperature >= 0.0 AND temperature <= 2.0),
    CONSTRAINT valid_max_tokens CHECK (max_tokens > 0 AND max_tokens <= 32768),
    CONSTRAINT valid_timeout CHECK (timeout_seconds > 0 AND timeout_seconds <= 300),
    CONSTRAINT valid_max_retries CHECK (max_retries >= 0 AND max_retries <= 10),
    CONSTRAINT unique_user_config_name UNIQUE (user_id, config_name),
    CONSTRAINT unique_user_default UNIQUE (user_id, is_default) WHERE is_default = true
);

-- 索引
CREATE INDEX idx_llm_configs_user_id ON llm_configs(user_id);
CREATE INDEX idx_llm_configs_user_enabled ON llm_configs(user_id, enabled);
CREATE INDEX idx_llm_configs_provider ON llm_configs(provider);
```

### 支持的模型配置
```python
LLM_PROVIDER_MODELS = {
    "deepseek": {
        "models": ["deepseek-chat", "deepseek-coder"],
        "default_model": "deepseek-chat",
        "api_base_url": "https://api.deepseek.com/v1",
        "max_tokens": 4096
    },
    "gemini": {
        "models": ["gemini-pro", "gemini-pro-vision"],
        "default_model": "gemini-pro", 
        "api_base_url": "https://generativelanguage.googleapis.com/v1",
        "max_tokens": 8192
    },
    "chatgpt": {
        "models": ["gpt-4", "gpt-4-turbo", "gpt-3.5-turbo"],
        "default_model": "gpt-4",
        "api_base_url": "https://api.openai.com/v1",
        "max_tokens": 4096
    },
    "claude": {
        "models": ["claude-3-opus", "claude-3-sonnet", "claude-3-haiku"],
        "default_model": "claude-3-sonnet",
        "api_base_url": "https://api.anthropic.com/v1",
        "max_tokens": 4096
    }
}
```

## 🔧 后端实现设计

### 1. 数据模型 (models.py)
```python
class LLMConfig(Base):
    """LLM配置模型"""
    __tablename__ = "llm_configs"
    
    id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), ForeignKey("users.id", ondelete="CASCADE"))
    
    # 基础配置
    config_name: Mapped[str] = mapped_column(String(100), nullable=False)
    provider: Mapped[str] = mapped_column(String(20), nullable=False)
    enabled: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False)
    is_default: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False)
    
    # API配置
    api_key: Mapped[str] = mapped_column(Text, nullable=False)  # 加密存储
    api_base_url: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)
    model_name: Mapped[str] = mapped_column(String(100), nullable=False)
    
    # 请求参数
    max_tokens: Mapped[int] = mapped_column(Integer, default=4096, nullable=False)
    temperature: Mapped[Decimal] = mapped_column(DECIMAL(3,2), default=Decimal('0.7'), nullable=False)
    timeout_seconds: Mapped[int] = mapped_column(Integer, default=60, nullable=False)
    max_retries: Mapped[int] = mapped_column(Integer, default=3, nullable=False)
    
    # 时间戳
    created_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), default=func.now())
    updated_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), default=func.now(), onupdate=func.now())
```

### 2. API Schema (schemas.py)
```python
class LLMProvider(str, Enum):
    DEEPSEEK = "deepseek"
    GEMINI = "gemini" 
    CHATGPT = "chatgpt"
    CLAUDE = "claude"

class LLMConfigRequest(BaseModel):
    config_name: str = Field(..., min_length=1, max_length=100)
    provider: LLMProvider
    enabled: bool = False
    is_default: bool = False
    api_key: str = Field(..., min_length=1)
    api_base_url: Optional[str] = None
    model_name: str = Field(..., min_length=1, max_length=100)
    max_tokens: int = Field(default=4096, ge=1, le=32768)
    temperature: float = Field(default=0.7, ge=0.0, le=2.0)
    timeout_seconds: int = Field(default=60, ge=1, le=300)
    max_retries: int = Field(default=3, ge=0, le=10)

class LLMConfigResponse(BaseModel):
    id: str
    config_name: str
    provider: str
    enabled: bool
    is_default: bool
    api_base_url: Optional[str]
    model_name: str
    max_tokens: int
    temperature: float
    timeout_seconds: int
    max_retries: int
    created_at: datetime
    updated_at: datetime
    
    # 不返回敏感信息
    api_key_masked: str = Field(description="脱敏后的API密钥")
```

### 3. API 路由 (api/v1/llm_configs.py)
```python
@router.get("", response_model=APIResponse[List[LLMConfigResponse]])
async def get_llm_configs(current_user: User = Depends(get_current_user), db: AsyncSession = Depends(get_db)):
    """获取用户的LLM配置列表"""

@router.post("", response_model=APIResponse[LLMConfigResponse])
async def create_llm_config(request: LLMConfigRequest, current_user: User = Depends(get_current_user), db: AsyncSession = Depends(get_db)):
    """创建LLM配置"""

@router.put("/{config_id}", response_model=APIResponse[LLMConfigResponse])
async def update_llm_config(config_id: str, request: LLMConfigRequest, current_user: User = Depends(get_current_user), db: AsyncSession = Depends(get_db)):
    """更新LLM配置"""

@router.delete("/{config_id}", response_model=APIResponse[dict])
async def delete_llm_config(config_id: str, current_user: User = Depends(get_current_user), db: AsyncSession = Depends(get_db)):
    """删除LLM配置"""

@router.post("/{config_id}/test", response_model=APIResponse[dict])
async def test_llm_config(config_id: str, current_user: User = Depends(get_current_user), db: AsyncSession = Depends(get_db)):
    """测试LLM配置连接"""

@router.post("/{config_id}/set-default", response_model=APIResponse[dict])
async def set_default_llm_config(config_id: str, current_user: User = Depends(get_current_user), db: AsyncSession = Depends(get_db)):
    """设置为默认LLM配置"""
```

## 🎨 前端实现设计

### 1. 类型定义 (types/llm.types.ts)
```typescript
export interface LLMConfig {
  id: string
  config_name: string
  provider: LLMProvider
  enabled: boolean
  is_default: boolean
  api_base_url?: string
  model_name: string
  max_tokens: number
  temperature: number
  timeout_seconds: number
  max_retries: number
  api_key_masked: string
  created_at: string
  updated_at: string
}

export interface LLMConfigRequest {
  config_name: string
  provider: LLMProvider
  enabled: boolean
  is_default: boolean
  api_key: string
  api_base_url?: string
  model_name: string
  max_tokens: number
  temperature: number
  timeout_seconds: number
  max_retries: number
}

export enum LLMProvider {
  DEEPSEEK = 'deepseek',
  GEMINI = 'gemini',
  CHATGPT = 'chatgpt',
  CLAUDE = 'claude'
}
```

### 2. 组件结构
```
frontend/src/components/llm/
├── LLMConfigPanel.vue      # 主面板组件
├── LLMConfigCard.vue       # 配置卡片组件
├── LLMConfigDialog.vue     # 配置对话框组件
└── LLMProviderIcon.vue     # 提供商图标组件
```

### 3. ConfigsView.vue 集成
在现有的选项卡中添加 LLM 配置选项卡，放置在信号源配置之后：

```vue
<v-tabs v-model="activeTab" bg-color="primary" dark>
  <v-tab value="signals">
    <v-icon start>mdi-signal</v-icon>
    信号源
  </v-tab>
  <v-tab value="llm">
    <v-icon start>mdi-robot</v-icon>
    LLM 配置
  </v-tab>
  <v-tab value="exchanges">
    <v-icon start>mdi-bank</v-icon>
    交易所配置
  </v-tab>
  <!-- 其他选项卡... -->
</v-tabs>

<v-window v-model="activeTab">
  <v-window-item value="llm">
    <div class="llm-config">
      <LLMConfigPanel />
    </div>
  </v-window-item>
  <!-- 其他窗口项... -->
</v-window>
```

## 🔐 安全考虑

### API 密钥加密存储
- 使用 AES 加密存储 API 密钥
- 前端显示时进行脱敏处理（如：`sk-***...***abc`）
- 编辑时支持保留原密钥或输入新密钥

### 权限控制
- 用户只能访问自己的 LLM 配置
- 管理员可以查看所有配置（但不能查看密钥）

## 📝 实施计划

### 阶段1：后端基础实现
1. 创建数据模型和迁移脚本
2. 实现 API 路由和业务逻辑
3. 编写后端单元测试和集成测试

### 阶段2：前端组件实现
1. 创建类型定义
2. 实现 LLM 配置组件
3. 集成到配置页面
4. 编写前端组件测试

### 阶段3：功能验证
1. 在 Docker 开发环境中测试
2. 验证 API 调用和数据持久化
3. 测试用户界面交互
4. 更新技术文档

## 🎯 成功标准

1. ✅ 用户可以创建、编辑、删除多个 LLM 配置
2. ✅ 支持四种 LLM 服务提供商
3. ✅ 配置可以启用/禁用和设置默认
4. ✅ API 密钥安全存储和脱敏显示
5. ✅ UI 与现有信号源配置风格一致
6. ✅ 所有功能通过测试验证
