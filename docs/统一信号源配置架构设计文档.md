# Discord信号源配置系统设计文档

## 1. 项目背景与目标

### 1.1 现状分析
- **现有配置管理模式**：基于ConfigsView.vue的选项卡式配置界面
- **数据库设计模式**：用户级别配置隔离（ExchangeConfig、RiskConfig）
- **API设计模式**：RESTful CRUD接口，统一响应格式
- **前端组件模式**：可复用的配置表单组件，Pinia状态管理

### 1.2 设计目标
- **重构现有Discord配置**：直接替换现有的discord_configs表和相关代码
- **简洁实用**：专注于Discord的实际需求，避免过度设计
- **预留扩展性**：为未来其他信号源保留基本的架构空间
- **一致性**：与现有配置管理模式保持一致

## 2. 设计原则

### 2.1 简化原则
- **专注Discord**：当前阶段只实现Discord信号源的完整功能
- **避免过度抽象**：不引入复杂的插件化机制和通用处理器
- **实用优先**：基于实际需求设计，不为扩展性而增加不必要的复杂度
- **直接重构**：不考虑向后兼容，直接替换现有系统

### 2.2 架构预留
- **表结构预留**：使用source_type字段为未来扩展预留空间
- **代码结构预留**：保持清晰的模块划分，便于未来添加新信号源
- **接口预留**：API设计考虑未来扩展的可能性

## 3. 数据库设计

### 3.1 简化的信号源配置表
```sql
CREATE TABLE signal_source_configs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,

    -- 基础配置
    source_type VARCHAR(50) NOT NULL DEFAULT 'discord', -- 当前只支持discord，预留扩展
    source_name VARCHAR(100) NOT NULL, -- 用户自定义名称
    enabled BOOLEAN NOT NULL DEFAULT false,

    -- Discord认证配置
    encrypted_token TEXT NOT NULL, -- Discord Bot Token

    -- Discord过滤配置
    server_ids TEXT[] DEFAULT '{}', -- 服务器ID列表
    channel_ids TEXT[] DEFAULT '{}', -- 频道ID列表
    author_ids TEXT[] DEFAULT '{}', -- 作者ID列表
    allowed_message_types TEXT[] DEFAULT '{"text"}', -- 允许的消息类型

    -- 时间戳
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),

    -- 约束
    CONSTRAINT valid_source_type CHECK (source_type = 'discord'), -- 当前只支持discord
    CONSTRAINT non_empty_token CHECK (length(encrypted_token) > 0),
    CONSTRAINT valid_message_types CHECK (
        allowed_message_types <@ ARRAY['text', 'embed', 'attachment', 'reply']
    ),
    UNIQUE(user_id, source_name) -- 用户下信号源名称唯一
);

-- 索引设计
CREATE INDEX idx_signal_configs_user_id ON signal_source_configs(user_id);
CREATE INDEX idx_signal_configs_user_enabled ON signal_source_configs(user_id, enabled);
CREATE INDEX idx_signal_configs_updated ON signal_source_configs(updated_at);

-- 数组字段索引（用于ID过滤查询）
CREATE INDEX idx_signal_configs_server_ids ON signal_source_configs USING gin(server_ids);
CREATE INDEX idx_signal_configs_channel_ids ON signal_source_configs USING gin(channel_ids);
CREATE INDEX idx_signal_configs_author_ids ON signal_source_configs USING gin(author_ids);
```

### 3.2 Discord配置示例
```json
{
  "id": "550e8400-e29b-41d4-a716-446655440000",
  "user_id": "123e4567-e89b-12d3-a456-426614174000",
  "source_type": "discord",
  "source_name": "主要交易信号",
  "enabled": true,
  "encrypted_token": "encrypted_discord_token_here",
  "server_ids": ["123456789012345678"],
  "channel_ids": ["987654321098765432", "111222333444555666"],
  "author_ids": ["555666777888999000"],
  "allowed_message_types": ["text", "embed"],
  "created_at": "2024-01-01T00:00:00Z",
  "updated_at": "2024-01-01T00:00:00Z"
}
```

## 4. 后端实现设计

### 4.1 简化的数据模型
```python
# backend/app/core/models.py

class DiscordConfig(Base):
    """Discord信号源配置模型"""
    __tablename__ = "signal_source_configs"

    id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), ForeignKey("users.id", ondelete="CASCADE"), nullable=False)

    # 基础配置
    source_type: Mapped[str] = mapped_column(String(50), default="discord", nullable=False)
    source_name: Mapped[str] = mapped_column(String(100), nullable=False)
    enabled: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False)

    # Discord认证
    encrypted_token: Mapped[str] = mapped_column(Text, nullable=False)

    # Discord过滤配置
    server_ids: Mapped[List[str]] = mapped_column(ARRAY(String), default=[], nullable=False)
    channel_ids: Mapped[List[str]] = mapped_column(ARRAY(String), default=[], nullable=False)
    author_ids: Mapped[List[str]] = mapped_column(ARRAY(String), default=[], nullable=False)
    allowed_message_types: Mapped[List[str]] = mapped_column(ARRAY(String), default=["text"], nullable=False)

    # 时间戳
    created_at: Mapped[datetime] = mapped_column(DateTime, default=utc_now, nullable=False)
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=utc_now, onupdate=utc_now, nullable=False)

    # 约束和索引
    __table_args__ = (
        CheckConstraint("source_type = 'discord'", name="valid_source_type"),
        CheckConstraint("length(encrypted_token) > 0", name="non_empty_token"),
        CheckConstraint(
            "allowed_message_types <@ ARRAY['text', 'embed', 'attachment', 'reply']",
            name="valid_message_types"
        ),
        UniqueConstraint("user_id", "source_name", name="unique_user_source_name"),
        Index("idx_signal_configs_user_id", "user_id"),
        Index("idx_signal_configs_user_enabled", "user_id", "enabled"),
        Index("idx_signal_configs_server_ids", "server_ids", postgresql_using="gin"),
        Index("idx_signal_configs_channel_ids", "channel_ids", postgresql_using="gin"),
        Index("idx_signal_configs_author_ids", "author_ids", postgresql_using="gin"),
    )

    # 关系映射
    user: Mapped["User"] = relationship("User", back_populates="discord_configs")
```

### 4.2 简化的Schema设计
```python
# backend/app/core/schemas.py

class DiscordConfigRequest(BaseModel):
    """Discord配置请求模型"""
    source_name: str = Field(..., min_length=1, max_length=100, description="配置名称")
    enabled: bool = Field(default=False, description="是否启用")
    token: str = Field(..., min_length=1, description="Discord Bot Token")
    server_ids: List[str] = Field(default=[], description="服务器ID列表")
    channel_ids: List[str] = Field(default=[], description="频道ID列表")
    author_ids: List[str] = Field(default=[], description="作者ID列表")
    allowed_message_types: List[str] = Field(default=["text"], description="允许的消息类型")

    @field_validator("server_ids", "channel_ids", "author_ids")
    @classmethod
    def validate_id_lists(cls, v):
        """验证ID列表格式"""
        if not isinstance(v, list):
            raise ValueError("必须是列表格式")
        for item in v:
            if not isinstance(item, str) or not item.strip():
                raise ValueError("ID不能为空")
        return [item.strip() for item in v if item.strip()]

    @field_validator("allowed_message_types")
    @classmethod
    def validate_message_types(cls, v):
        """验证消息类型"""
        valid_types = {"text", "embed", "attachment", "reply"}
        if not v:
            return ["text"]
        for msg_type in v:
            if msg_type not in valid_types:
                raise ValueError(f"无效的消息类型: {msg_type}")
        return v

class DiscordConfigResponse(BaseModel):
    """Discord配置响应模型"""
    id: uuid.UUID
    user_id: uuid.UUID
    source_name: str
    enabled: bool
    has_token: bool  # 不返回实际token
    server_ids: List[str]
    channel_ids: List[str]
    author_ids: List[str]
    allowed_message_types: List[str]
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

    @classmethod
    def from_orm_with_token_flag(cls, obj):
        """从ORM对象创建响应，设置token标志"""
        data = obj.__dict__.copy()
        data["has_token"] = bool(obj.encrypted_token)
        return cls(**data)
```

### 4.3 简化的API接口设计
```python
# backend/app/api/v1/discord_configs.py

router = APIRouter(prefix="/discord-configs", tags=["Discord配置"])

@router.get("", response_model=List[DiscordConfigResponse])
async def list_discord_configs(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """获取用户的Discord配置列表"""
    query = select(DiscordConfig).where(DiscordConfig.user_id == current_user.id)
    result = await db.execute(query)
    configs = result.scalars().all()

    return [DiscordConfigResponse.from_orm_with_token_flag(config) for config in configs]

@router.post("", response_model=DiscordConfigResponse)
async def create_discord_config(
    request: DiscordConfigRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """创建Discord配置"""
    # 检查配置名称是否重复
    existing = await db.execute(
        select(DiscordConfig).where(
            DiscordConfig.user_id == current_user.id,
            DiscordConfig.source_name == request.source_name
        )
    )
    if existing.scalar_one_or_none():
        raise HTTPException(status_code=400, detail="配置名称已存在")

    # 加密token
    encrypted_token = encrypt_sensitive_data(request.token)

    # 创建配置
    config = DiscordConfig(
        user_id=current_user.id,
        source_name=request.source_name,
        enabled=request.enabled,
        encrypted_token=encrypted_token,
        server_ids=request.server_ids,
        channel_ids=request.channel_ids,
        author_ids=request.author_ids,
        allowed_message_types=request.allowed_message_types,
    )

    db.add(config)
    await db.commit()
    await db.refresh(config)

    return DiscordConfigResponse.from_orm_with_token_flag(config)

@router.get("/{config_id}", response_model=DiscordConfigResponse)
async def get_discord_config(
    config_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """获取特定Discord配置"""
    config = await get_user_discord_config(db, config_id, current_user.id)
    return DiscordConfigResponse.from_orm_with_token_flag(config)

@router.put("/{config_id}", response_model=DiscordConfigResponse)
async def update_discord_config(
    config_id: uuid.UUID,
    request: DiscordConfigRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """更新Discord配置"""
    config = await get_user_discord_config(db, config_id, current_user.id)

    # 检查名称冲突（排除当前配置）
    if request.source_name != config.source_name:
        existing = await db.execute(
            select(DiscordConfig).where(
                DiscordConfig.user_id == current_user.id,
                DiscordConfig.source_name == request.source_name,
                DiscordConfig.id != config_id
            )
        )
        if existing.scalar_one_or_none():
            raise HTTPException(status_code=400, detail="配置名称已存在")

    # 更新配置
    config.source_name = request.source_name
    config.enabled = request.enabled
    config.encrypted_token = encrypt_sensitive_data(request.token)
    config.server_ids = request.server_ids
    config.channel_ids = request.channel_ids
    config.author_ids = request.author_ids
    config.allowed_message_types = request.allowed_message_types

    await db.commit()
    await db.refresh(config)

    return DiscordConfigResponse.from_orm_with_token_flag(config)

@router.delete("/{config_id}")
async def delete_discord_config(
    config_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """删除Discord配置"""
    config = await get_user_discord_config(db, config_id, current_user.id)
    await db.delete(config)
    await db.commit()

    return {"success": True, "message": "配置已删除"}

@router.post("/{config_id}/test")
async def test_discord_config(
    config_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """测试Discord配置连接"""
    config = await get_user_discord_config(db, config_id, current_user.id)

    try:
        # 解密token并测试连接
        token = decrypt_sensitive_data(config.encrypted_token)
        success = await test_discord_connection(token)

        return {
            "success": success,
            "message": "连接成功" if success else "连接失败",
            "timestamp": datetime.utcnow()
        }
    except Exception as e:
        return {
            "success": False,
            "message": f"测试失败: {str(e)}",
            "timestamp": datetime.utcnow()
        }

# 辅助函数
async def get_user_discord_config(db: AsyncSession, config_id: uuid.UUID, user_id: uuid.UUID):
    """获取用户的Discord配置"""
    query = select(DiscordConfig).where(
        DiscordConfig.id == config_id,
        DiscordConfig.user_id == user_id
    )
    result = await db.execute(query)
    config = result.scalar_one_or_none()

    if not config:
        raise HTTPException(status_code=404, detail="配置不存在")

    return config
```

## 5. 前端实现设计

### 5.1 ConfigsView.vue集成
```vue
<!-- frontend/src/views/ConfigsView.vue -->
<template>
  <v-tabs v-model="activeTab" bg-color="primary" dark>
    <!-- 现有选项卡 -->
    <v-tab value="exchanges">交易所配置</v-tab>
    <v-tab value="risk">风控规则</v-tab>

    <!-- Discord信号源选项卡 -->
    <v-tab value="discord">
      <v-icon start>mdi-discord</v-icon>
      Discord信号源
    </v-tab>

    <v-tab value="system">系统设置</v-tab>
  </v-tabs>

  <v-window v-model="activeTab">
    <!-- Discord配置面板 -->
    <v-window-item value="discord">
      <DiscordConfigPanel />
    </v-window-item>
  </v-window>
</template>
```

### 5.2 Discord配置面板组件
```vue
<!-- frontend/src/components/DiscordConfigPanel.vue -->
<template>
  <v-container>
    <!-- 页面标题 -->
    <v-row>
      <v-col cols="12">
        <div class="d-flex justify-space-between align-center mb-4">
          <div>
            <h2 class="text-h4 mb-2">Discord信号源配置</h2>
            <p class="text-body-2 text-medium-emphasis">
              配置Discord机器人来监听交易信号
            </p>
          </div>
          <v-btn color="primary" @click="showAddDialog = true">
            <v-icon start>mdi-plus</v-icon>
            添加配置
          </v-btn>
        </div>
      </v-col>
    </v-row>

    <!-- 配置列表 -->
    <v-row v-if="discordConfigs.length > 0">
      <v-col v-for="config in discordConfigs" :key="config.id" cols="12" md="6" lg="4">
        <DiscordConfigCard
          :config="config"
          @edit="editConfig"
          @delete="deleteConfig"
          @toggle="toggleConfig"
          @test="testConfig"
        />
      </v-col>
    </v-row>

    <!-- 空状态 -->
    <v-row v-else>
      <v-col cols="12">
        <v-card class="text-center pa-8">
          <v-icon size="64" color="grey-lighten-1" class="mb-4">
            mdi-discord
          </v-icon>
          <h3 class="text-h6 mb-2">还没有Discord配置</h3>
          <p class="text-body-2 text-medium-emphasis mb-4">
            添加第一个Discord信号源配置来开始监听交易信号
          </p>
          <v-btn color="primary" @click="showAddDialog = true">
            添加Discord配置
          </v-btn>
        </v-card>
      </v-col>
    </v-row>

    <!-- 添加/编辑对话框 -->
    <DiscordConfigDialog
      v-model="showAddDialog"
      :config="editingConfig"
      @save="saveConfig"
    />
  </v-container>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useDiscordConfigStore } from '@/stores/discordConfig'
import DiscordConfigCard from './DiscordConfigCard.vue'
import DiscordConfigDialog from './DiscordConfigDialog.vue'

const discordConfigStore = useDiscordConfigStore()
const { discordConfigs, loading } = storeToRefs(discordConfigStore)

const showAddDialog = ref(false)
const editingConfig = ref(null)

const editConfig = (config) => {
  editingConfig.value = config
  showAddDialog.value = true
}

const deleteConfig = async (config) => {
  if (confirm(`确定要删除配置"${config.source_name}"吗？`)) {
    await discordConfigStore.deleteConfig(config.id)
  }
}

const toggleConfig = async (config) => {
  await discordConfigStore.updateConfig(config.id, { enabled: config.enabled })
}

const testConfig = async (config) => {
  await discordConfigStore.testConfig(config.id)
}

const saveConfig = async (configData) => {
  if (editingConfig.value) {
    await discordConfigStore.updateConfig(editingConfig.value.id, configData)
  } else {
    await discordConfigStore.createConfig(configData)
  }
  showAddDialog.value = false
  editingConfig.value = null
}

onMounted(() => {
  discordConfigStore.loadConfigs()
})
</script>
```

### 5.3 Discord配置卡片组件
```vue
<!-- frontend/src/components/DiscordConfigCard.vue -->
<template>
  <v-card class="discord-config-card" :class="{ 'disabled': !config.enabled }">
    <v-card-title class="d-flex align-center">
      <v-icon color="indigo" class="mr-2">mdi-discord</v-icon>
      <span>{{ config.source_name }}</span>
      <v-spacer />
      <v-switch
        v-model="config.enabled"
        @change="$emit('toggle', config)"
        hide-details
        density="compact"
        color="primary"
      />
    </v-card-title>

    <v-card-text>
      <!-- 状态指示器 -->
      <v-chip
        :color="config.enabled ? 'success' : 'grey'"
        size="small"
        class="mb-3"
      >
        {{ config.enabled ? '已启用' : '已禁用' }}
      </v-chip>

      <!-- 配置信息 -->
      <div class="config-info">
        <div class="info-item">
          <v-icon size="16" class="mr-1">mdi-key</v-icon>
          <span class="text-caption">
            Token: {{ config.has_token ? '已配置' : '未配置' }}
          </span>
        </div>

        <div class="info-item">
          <v-icon size="16" class="mr-1">mdi-server</v-icon>
          <span class="text-caption">
            服务器: {{ config.server_ids.length }} 个
          </span>
        </div>

        <div class="info-item">
          <v-icon size="16" class="mr-1">mdi-pound</v-icon>
          <span class="text-caption">
            频道: {{ config.channel_ids.length }} 个
          </span>
        </div>

        <div class="info-item">
          <v-icon size="16" class="mr-1">mdi-account</v-icon>
          <span class="text-caption">
            作者: {{ config.author_ids.length }} 个
          </span>
        </div>
      </div>

      <!-- 创建时间 -->
      <div class="text-caption text-medium-emphasis mt-2">
        创建于: {{ formatTime(config.created_at) }}
      </div>
    </v-card-text>

    <v-card-actions>
      <v-btn variant="text" size="small" @click="$emit('edit', config)">
        <v-icon start size="16">mdi-pencil</v-icon>
        编辑
      </v-btn>
      <v-btn variant="text" size="small" color="error" @click="$emit('delete', config)">
        <v-icon start size="16">mdi-delete</v-icon>
        删除
      </v-btn>
      <v-spacer />
      <v-btn
        variant="text"
        size="small"
        color="primary"
        :loading="testing"
        @click="testConnection"
      >
        <v-icon start size="16">mdi-connection</v-icon>
        测试
      </v-btn>
    </v-card-actions>
  </v-card>
</template>

<script setup>
import { ref } from 'vue'
import { formatDistanceToNow } from 'date-fns'
import { zhCN } from 'date-fns/locale'

const props = defineProps({
  config: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['edit', 'delete', 'toggle', 'test'])

const testing = ref(false)

const formatTime = (dateString) => {
  return formatDistanceToNow(new Date(dateString), {
    addSuffix: true,
    locale: zhCN
  })
}

const testConnection = async () => {
  testing.value = true
  try {
    await emit('test', props.config)
  } finally {
    testing.value = false
  }
}
</script>

<style scoped>
.discord-config-card.disabled {
  opacity: 0.6;
}

.config-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.info-item {
  display: flex;
  align-items: center;
  color: rgba(var(--v-theme-on-surface), 0.7);
}
</style>
```

## 6. Discord服务管理器设计

### 6.1 简化的Discord配置管理器
```python
# backend/app/services/discord_config_manager.py

class DiscordConfigManager:
    """Discord配置管理器"""

    def __init__(self):
        self.active_clients: Dict[uuid.UUID, TradingSignalClient] = {}
        self.config_cache: Dict[uuid.UUID, DiscordConfig] = {}

    async def start_user_discord_sources(self, user_id: uuid.UUID, db: AsyncSession):
        """启动用户的所有Discord信号源"""
        # 查询用户的启用配置
        query = select(DiscordConfig).where(
            DiscordConfig.user_id == user_id,
            DiscordConfig.enabled == True
        )
        result = await db.execute(query)
        configs = result.scalars().all()

        for config in configs:
            await self.start_discord_client(config)

    async def stop_user_discord_sources(self, user_id: uuid.UUID):
        """停止用户的所有Discord信号源"""
        configs_to_stop = [
            config_id for config_id, client in self.active_clients.items()
            if config_id in self.config_cache and self.config_cache[config_id].user_id == user_id
        ]

        for config_id in configs_to_stop:
            await self.stop_discord_client(config_id)

    async def start_discord_client(self, config: DiscordConfig):
        """启动单个Discord客户端"""
        if config.id in self.active_clients:
            await self.stop_discord_client(config.id)

        try:
            # 解密token
            token = decrypt_sensitive_data(config.encrypted_token)

            # 创建过滤配置
            filter_config = {
                "server_ids": set(config.server_ids),
                "channel_ids": set(config.channel_ids),
                "author_ids": set(config.author_ids),
                "allowed_message_types": set(config.allowed_message_types),
            }

            # 创建Discord客户端
            client = TradingSignalClient(
                filter_config=filter_config,
                user_id=config.user_id
            )

            # 启动客户端
            await client.start(token)

            # 缓存客户端和配置
            self.active_clients[config.id] = client
            self.config_cache[config.id] = config

            logger.info(f"Discord client started for config {config.id}")

        except Exception as e:
            logger.error(f"Failed to start Discord client for config {config.id}: {e}")
            raise

    async def stop_discord_client(self, config_id: uuid.UUID):
        """停止单个Discord客户端"""
        if config_id in self.active_clients:
            client = self.active_clients[config_id]
            try:
                if not client.is_closed():
                    await client.close()
            except Exception as e:
                logger.error(f"Error stopping Discord client {config_id}: {e}")
            finally:
                del self.active_clients[config_id]
                if config_id in self.config_cache:
                    del self.config_cache[config_id]

    async def restart_discord_client(self, config_id: uuid.UUID, db: AsyncSession):
        """重启Discord客户端"""
        # 停止现有客户端
        await self.stop_discord_client(config_id)

        # 重新加载配置
        query = select(DiscordConfig).where(DiscordConfig.id == config_id)
        result = await db.execute(query)
        config = result.scalar_one_or_none()

        if config and config.enabled:
            await self.start_discord_client(config)

    def get_client_status(self, config_id: uuid.UUID) -> Dict:
        """获取Discord客户端状态"""
        if config_id not in self.active_clients:
            return {"is_running": False, "is_connected": False}

        client = self.active_clients[config_id]
        return {
            "is_running": True,
            "is_connected": not client.is_closed(),
            "latency": client.latency if hasattr(client, 'latency') else None,
            "guild_count": len(client.guilds) if hasattr(client, 'guilds') else 0,
        }

    async def reload_config(self, config_id: uuid.UUID, db: AsyncSession):
        """重新加载配置（配置更新后调用）"""
        await self.restart_discord_client(config_id, db)

# 全局实例
discord_config_manager = DiscordConfigManager()
```

### 6.2 Discord连接测试服务
```python
# backend/app/services/discord_test_service.py

async def test_discord_connection(token: str) -> bool:
    """测试Discord连接"""
    try:
        # 创建临时客户端
        client = discord.Client(intents=discord.Intents.default())

        # 尝试登录
        await client.login(token)

        # 获取基本信息
        user = client.user
        if user:
            logger.info(f"Discord connection test successful for user: {user.name}")
            success = True
        else:
            success = False

        # 关闭连接
        await client.close()

        return success

    except discord.LoginFailure:
        logger.error("Discord login failed: Invalid token")
        return False
    except discord.HTTPException as e:
        logger.error(f"Discord HTTP error: {e}")
        return False
    except Exception as e:
        logger.error(f"Discord connection test failed: {e}")
        return False
```

## 7. 实施计划

### 7.1 直接重构策略
由于处于开发阶段，我们采用直接重构的方式，不考虑向后兼容性：

1. **删除现有Discord配置系统**
   - 删除 `discord_configs` 表
   - 删除相关的API接口和前端组件
   - 删除现有的Discord配置管理代码

2. **实施新的Discord配置系统**
   - 创建新的 `signal_source_configs` 表
   - 实现新的数据模型和API接口
   - 创建新的前端配置界面
   - 集成新的Discord配置管理器

### 7.2 实施步骤

#### Step 1: 数据库重构（1天）
- [ ] 创建数据库迁移脚本
- [ ] 删除旧的 `discord_configs` 表
- [ ] 创建新的 `signal_source_configs` 表
- [ ] 更新数据库初始化脚本

#### Step 2: 后端重构（2天）
- [ ] 更新数据模型 (`DiscordConfig`)
- [ ] 重写API接口 (`discord_configs.py`)
- [ ] 实现新的Schema验证
- [ ] 更新Discord配置管理器
- [ ] 实现连接测试服务

#### Step 3: 前端重构（2天）
- [ ] 创建新的Discord配置组件
- [ ] 实现Pinia状态管理
- [ ] 集成到ConfigsView.vue
- [ ] 实现配置表单和验证
- [ ] 添加测试连接功能

#### Step 4: 集成测试（1天）
- [ ] 端到端功能测试
- [ ] Discord连接测试
- [ ] 配置管理测试
- [ ] 用户界面测试
- [ ] 性能测试

### 7.3 扩展预留
为未来扩展预留的架构空间：

1. **数据库层面**
   - `source_type` 字段支持其他信号源类型
   - 表结构可以容纳其他信号源的配置需求

2. **代码层面**
   - 清晰的模块划分，便于添加新的信号源处理器
   - API接口设计考虑了扩展性
   - 前端组件结构支持添加新的信号源类型

3. **配置层面**
   - 统一的配置管理模式
   - 可扩展的过滤配置结构
   - 灵活的认证配置支持

## 8. 技术考虑

### 8.1 性能优化
- **配置缓存**：在DiscordConfigManager中缓存活跃配置
- **Set-based过滤**：使用Set进行O(1)的ID过滤查询
- **异步处理**：非阻塞的Discord客户端管理
- **连接复用**：避免频繁的Discord连接创建和销毁

### 8.2 安全考虑
- **Token加密**：Discord Token使用AES加密存储
- **用户隔离**：严格的用户级别配置隔离
- **输入验证**：严格的ID格式和消息类型验证
- **权限检查**：API层面的用户权限验证

### 8.3 错误处理
- **连接重试**：Discord连接失败时的自动重试机制
- **优雅降级**：单个配置失败不影响其他配置
- **错误日志**：详细的错误日志记录和监控
- **用户反馈**：清晰的错误信息反馈给用户

### 8.4 扩展性设计
- **模块化结构**：清晰的代码模块划分
- **配置抽象**：为未来信号源类型预留接口
- **数据库设计**：支持多种信号源类型的表结构
- **前端组件**：可复用的配置组件架构

## 9. 前端状态管理

### 9.1 Discord配置状态管理
```typescript
// frontend/src/stores/discordConfig.ts

export const useDiscordConfigStore = defineStore('discordConfig', () => {
  // 状态
  const configs = ref<DiscordConfig[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)

  // 计算属性
  const enabledConfigs = computed(() =>
    configs.value.filter(config => config.enabled)
  )

  const configCount = computed(() => configs.value.length)

  // 操作方法
  const loadConfigs = async () => {
    loading.value = true
    error.value = null
    try {
      const response = await api.get('/discord-configs')
      configs.value = response.data
    } catch (err) {
      error.value = err.message
      console.error('Failed to load Discord configs:', err)
    } finally {
      loading.value = false
    }
  }

  const createConfig = async (configData: DiscordConfigRequest) => {
    try {
      const response = await api.post('/discord-configs', configData)
      configs.value.push(response.data)
      return response.data
    } catch (err) {
      error.value = err.message
      throw err
    }
  }

  const updateConfig = async (id: string, configData: Partial<DiscordConfigRequest>) => {
    try {
      const response = await api.put(`/discord-configs/${id}`, configData)
      const index = configs.value.findIndex(c => c.id === id)
      if (index !== -1) {
        configs.value[index] = response.data
      }
      return response.data
    } catch (err) {
      error.value = err.message
      throw err
    }
  }

  const deleteConfig = async (id: string) => {
    try {
      await api.delete(`/discord-configs/${id}`)
      configs.value = configs.value.filter(c => c.id !== id)
    } catch (err) {
      error.value = err.message
      throw err
    }
  }

  const testConfig = async (id: string) => {
    try {
      const response = await api.post(`/discord-configs/${id}/test`)
      return response.data
    } catch (err) {
      error.value = err.message
      throw err
    }
  }

  return {
    configs,
    loading,
    error,
    enabledConfigs,
    configCount,
    loadConfigs,
    createConfig,
    updateConfig,
    deleteConfig,
    testConfig,
  }
})
```

### 9.2 Discord配置对话框组件
```vue
<!-- frontend/src/components/DiscordConfigDialog.vue -->
<template>
  <v-dialog v-model="dialog" max-width="700px" persistent>
    <v-card>
      <v-card-title class="d-flex align-center">
        <v-icon color="indigo" class="mr-2">mdi-discord</v-icon>
        {{ isEditing ? '编辑Discord配置' : '添加Discord配置' }}
      </v-card-title>

      <v-card-text>
        <v-form ref="form" v-model="valid">
          <!-- 基础配置 -->
          <v-row>
            <v-col cols="12">
              <v-text-field
                v-model="formData.source_name"
                label="配置名称"
                :rules="[rules.required, rules.maxLength(100)]"
                placeholder="例如：主要交易信号"
              />
            </v-col>
          </v-row>

          <v-row>
            <v-col cols="12">
              <v-text-field
                v-model="formData.token"
                label="Discord Bot Token"
                :rules="[rules.required]"
                :type="showToken ? 'text' : 'password'"
                :append-inner-icon="showToken ? 'mdi-eye' : 'mdi-eye-off'"
                @click:append-inner="showToken = !showToken"
                placeholder="请输入Discord Bot Token"
              />
            </v-col>
          </v-row>

          <v-row>
            <v-col cols="12">
              <v-switch
                v-model="formData.enabled"
                label="启用此配置"
                color="primary"
              />
            </v-col>
          </v-row>

          <!-- 过滤配置 -->
          <v-divider class="my-4" />
          <h3 class="mb-3">过滤配置</h3>

          <v-row>
            <v-col cols="12">
              <v-combobox
                v-model="formData.server_ids"
                label="服务器ID列表"
                multiple
                chips
                clearable
                :rules="[rules.validIdList]"
                placeholder="输入服务器ID后按回车添加"
                hint="留空表示监听所有服务器"
              />
            </v-col>
          </v-row>

          <v-row>
            <v-col cols="12">
              <v-combobox
                v-model="formData.channel_ids"
                label="频道ID列表"
                multiple
                chips
                clearable
                :rules="[rules.validIdList]"
                placeholder="输入频道ID后按回车添加"
                hint="留空表示监听所有频道"
              />
            </v-col>
          </v-row>

          <v-row>
            <v-col cols="12">
              <v-combobox
                v-model="formData.author_ids"
                label="作者ID列表"
                multiple
                chips
                clearable
                :rules="[rules.validIdList]"
                placeholder="输入作者ID后按回车添加"
                hint="留空表示监听所有作者"
              />
            </v-col>
          </v-row>

          <v-row>
            <v-col cols="12">
              <v-select
                v-model="formData.allowed_message_types"
                :items="messageTypeOptions"
                label="允许的消息类型"
                multiple
                chips
                :rules="[rules.required]"
              />
            </v-col>
          </v-row>
        </v-form>
      </v-card-text>

      <v-card-actions>
        <v-spacer />
        <v-btn variant="text" @click="close">取消</v-btn>
        <v-btn
          color="primary"
          :disabled="!valid"
          :loading="saving"
          @click="save"
        >
          保存
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'

const props = defineProps({
  modelValue: Boolean,
  config: Object,
})

const emit = defineEmits(['update:modelValue', 'save'])

// 响应式数据
const dialog = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const form = ref(null)
const valid = ref(false)
const saving = ref(false)
const showToken = ref(false)

const isEditing = computed(() => !!props.config)

// 表单数据
const formData = ref({
  source_name: '',
  token: '',
  enabled: false,
  server_ids: [],
  channel_ids: [],
  author_ids: [],
  allowed_message_types: ['text'],
})

// 选项数据
const messageTypeOptions = [
  { title: '文本消息', value: 'text' },
  { title: '嵌入消息', value: 'embed' },
  { title: '附件消息', value: 'attachment' },
  { title: '回复消息', value: 'reply' },
]

// 验证规则
const rules = {
  required: (value) => !!value || '此字段为必填项',
  maxLength: (max) => (value) => !value || value.length <= max || `最多${max}个字符`,
  validIdList: (value) => {
    if (!value || value.length === 0) return true
    return value.every(id => /^\d+$/.test(id)) || 'ID必须是数字'
  },
}

// 监听配置变化
watch(() => props.config, (newConfig) => {
  if (newConfig) {
    formData.value = {
      source_name: newConfig.source_name || '',
      token: '', // 不显示现有token
      enabled: newConfig.enabled || false,
      server_ids: [...(newConfig.server_ids || [])],
      channel_ids: [...(newConfig.channel_ids || [])],
      author_ids: [...(newConfig.author_ids || [])],
      allowed_message_types: [...(newConfig.allowed_message_types || ['text'])],
    }
  } else {
    resetForm()
  }
}, { immediate: true })

const resetForm = () => {
  formData.value = {
    source_name: '',
    token: '',
    enabled: false,
    server_ids: [],
    channel_ids: [],
    author_ids: [],
    allowed_message_types: ['text'],
  }
}

const close = () => {
  dialog.value = false
  resetForm()
}

const save = async () => {
  if (!valid.value) return

  saving.value = true
  try {
    await emit('save', { ...formData.value })
    close()
  } catch (error) {
    console.error('Save failed:', error)
  } finally {
    saving.value = false
  }
}
</script>
```

## 10. 数据库迁移脚本

### 10.1 创建新表的迁移脚本
```sql
-- migrations/001_create_signal_source_configs.sql

-- 删除旧的discord_configs表（如果存在）
DROP TABLE IF EXISTS discord_configs CASCADE;

-- 创建新的signal_source_configs表
CREATE TABLE signal_source_configs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,

    -- 基础配置
    source_type VARCHAR(50) NOT NULL DEFAULT 'discord',
    source_name VARCHAR(100) NOT NULL,
    enabled BOOLEAN NOT NULL DEFAULT false,

    -- Discord认证配置
    encrypted_token TEXT NOT NULL,

    -- Discord过滤配置
    server_ids TEXT[] DEFAULT '{}',
    channel_ids TEXT[] DEFAULT '{}',
    author_ids TEXT[] DEFAULT '{}',
    allowed_message_types TEXT[] DEFAULT '{"text"}',

    -- 时间戳
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),

    -- 约束
    CONSTRAINT valid_source_type CHECK (source_type = 'discord'),
    CONSTRAINT non_empty_token CHECK (length(encrypted_token) > 0),
    CONSTRAINT valid_message_types CHECK (
        allowed_message_types <@ ARRAY['text', 'embed', 'attachment', 'reply']
    ),
    UNIQUE(user_id, source_name)
);

-- 创建索引
CREATE INDEX idx_signal_configs_user_id ON signal_source_configs(user_id);
CREATE INDEX idx_signal_configs_user_enabled ON signal_source_configs(user_id, enabled);
CREATE INDEX idx_signal_configs_updated ON signal_source_configs(updated_at);

-- 数组字段索引
CREATE INDEX idx_signal_configs_server_ids ON signal_source_configs USING gin(server_ids);
CREATE INDEX idx_signal_configs_channel_ids ON signal_source_configs USING gin(channel_ids);
CREATE INDEX idx_signal_configs_author_ids ON signal_source_configs USING gin(author_ids);

-- 创建更新时间触发器
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_signal_source_configs_updated_at
    BEFORE UPDATE ON signal_source_configs
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

### 10.2 更新User模型关系
```python
# backend/app/core/models.py - 更新User模型

class User(Base):
    # ... 现有字段 ...

    # 关系映射
    signals: Mapped[List["Signal"]] = relationship("Signal", back_populates="user")
    orders: Mapped[List["Order"]] = relationship("Order", back_populates="user")

    # 更新Discord配置关系
    discord_configs: Mapped[List["DiscordConfig"]] = relationship(
        "DiscordConfig",
        back_populates="user",
        cascade="all, delete-orphan"
    )
```

## 11. 总结

### 11.1 设计优势

#### 1. **简洁实用**
- 专注于Discord信号源的实际需求
- 避免了过度设计和不必要的复杂性
- 直接重构，无需考虑向后兼容性

#### 2. **架构清晰**
- 清晰的数据库表结构设计
- 简化的API接口和数据模型
- 模块化的前端组件架构

#### 3. **扩展预留**
- `source_type` 字段为未来扩展预留空间
- 清晰的代码模块划分
- 可复用的配置管理模式

#### 4. **用户体验**
- 统一的配置界面风格
- 直观的配置管理操作
- 实时的连接测试功能

### 11.2 技术特点

#### 1. **数据库设计**
- 使用PostgreSQL ARRAY字段存储ID列表
- 合理的约束和索引设计
- 支持高效的过滤查询

#### 2. **安全性**
- Discord Token加密存储
- 严格的用户权限隔离
- 完整的输入验证

#### 3. **性能优化**
- Set-based过滤提升查询性能
- 配置缓存减少数据库访问
- 异步处理避免阻塞

### 11.3 实施建议

#### 1. **开发顺序**
1. 数据库迁移和模型创建
2. 后端API接口实现
3. 前端组件开发
4. 集成测试和优化

#### 2. **测试重点**
- Discord连接测试
- 配置CRUD操作测试
- 用户权限隔离测试
- 前端界面交互测试

#### 3. **部署注意事项**
- 确保数据库迁移脚本正确执行
- 验证Discord Token加密/解密功能
- 测试配置管理器的启停功能

### 11.4 未来扩展路径

当需要添加其他信号源时，可以按照以下步骤扩展：

1. **数据库层面**
   - 更新 `source_type` 约束条件
   - 根据需要添加新的配置字段

2. **后端层面**
   - 创建新的信号源处理器
   - 扩展API接口支持新的信号源类型
   - 添加相应的Schema验证

3. **前端层面**
   - 创建新的信号源配置组件
   - 扩展ConfigsView.vue添加新的选项卡
   - 更新状态管理支持新的信号源

这个简化的设计既满足了当前Discord信号源的需求，又为未来的扩展提供了良好的基础架构。通过专注于实际需求而非过度设计，我们可以更快地实现功能并投入使用。


