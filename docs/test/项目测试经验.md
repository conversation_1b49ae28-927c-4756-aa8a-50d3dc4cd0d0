# 🔧 项目测试经验

**版本**: 2.1 | **更新**: 2025-07-27 | **项目**: AI Crypto Trading Agent

## 🚀 快速诊断指南

### 环境检查命令
```bash
# 快速环境检查
python scripts/test.py --check-env

# 查看所有测试命令
python scripts/test.py --all

# 检查服务状态
python scripts/test.py --status
```

### 快速修复清单
| 问题类型 | 快速检查 | 快速修复 |
|---------|----------|----------|
| **环境问题** | `docker-compose ps` | `docker-compose up -d postgres backend` |
| **端口冲突** | `lsof -i :8000` | `kill -9 <PID>` 或使用备用端口 |
| **格式检查失败** | `black --check app/` | `black app/` |
| **类型检查失败** | `mypy app/` | 修复类型注解 |
| **前端构建失败** | `npm run build` | `npm install` 或清理缓存 |
| **FastAPI依赖注入错误** | 检查函数参数 | 使用`MagicMock()`创建mock用户 |
| **数据库会话错误** | 检查session管理 | 使用`async with async_session()`正确管理 |
| **Vuetify组件选择器失败** | 检查DOM结构 | 使用`${selector} input`查找内部元素 |
| **表单验证按钮禁用** | 检查表单状态 | 设置`isFormValid.value = true` |
| **页面导航超时** | 检查网络连接 | 实现重试机制，增加超时时间 |
| **元素不可见/不可点击** | 检查元素状态 | 等待元素可见，滚动到视图内 |

### 常见错误快速识别
```bash
# 后端API测试错误
TypeError: function() missing 1 required positional argument: 'current_user'
→ FastAPI依赖注入问题，使用MagicMock创建mock用户

sqlalchemy.exc.InvalidRequestError: Object is not bound to a Session
→ 数据库会话管理问题，检查session生命周期

# 前端E2E测试错误
Error: locator.fill: Element is not an <input>, <textarea> or [contenteditable] element
→ Vuetify组件选择器问题，查找内部input元素

TimeoutError: page.goto: Timeout 30000ms exceeded
→ 页面导航超时，实现重试机制

Button is disabled due to form validation
→ 表单验证状态问题，检查isFormValid设置
```

### 紧急故障排除流程
1. **立即检查** → `python scripts/test.py --status`
2. **环境验证** → `python scripts/test.py --check-env`
3. **分步执行** → 按测试规范的分步流程执行
4. **查看日志** → 检查具体错误信息
5. **参考下方详细指南** → 查找具体问题的解决方案

---

## 📋 详细问题解决指南

*以下为详细的问题分析和解决方案，适用于深度排查和复杂问题处理*

## 🚨 常见失败场景

### 后端 API 测试

#### FastAPI 依赖注入错误
```bash
TypeError: function() missing 1 required positional argument: 'current_user'
```
**解决方案**:
```python
# ❌ 错误
response = await api_function(user_id=user_id, service=mock_service)

# ✅ 正确
mock_user = MagicMock()
mock_user.id = "test-user-id"
response = await api_function(current_user=mock_user, service=mock_service)
```

#### 数据库会话错误
```bash
sqlalchemy.exc.InvalidRequestError: Object is not bound to a Session
```
**解决方案**:
```python
@pytest.fixture
async def db_session():
    async with async_session() as session:
        try:
            yield session
        finally:
            await session.rollback()
            await session.close()
```

#### 异步测试超时
```bash
asyncio.TimeoutError: Test execution timed out after 30 seconds
```
**解决方案**:
```python
@pytest.mark.asyncio
async def test_async_operation():
    async with asyncio.timeout(10):
        result = await long_running_operation()
        assert result is not None
```

#### 代码格式检查失败 
```bash
would reformat backend/app/main.py
```
**解决方案**:
```bash
# 自动格式化
black backend/app/

# 类型检查修复
mypy backend/app/ --ignore-missing-imports
```

### 前端 E2E 测试

#### Vuetify 组件选择器失败
```bash
Error: locator.fill: Element is not an <input>, <textarea> or [contenteditable] element
```

**解决方案**:
```javascript
// ❌ 错误
await page.locator('[data-testid="username-field"]').fill('testuser')

// ✅ 正确 - 查找内部 input 元素
const field = page.locator('[data-testid="username-field"] input').first()
await field.fill('testuser')

// 或点击激活
await page.locator('[data-testid="username-field"]').click()
await page.keyboard.type('testuser')
```

#### 表单验证导致按钮禁用
```bash
Button is disabled due to form validation
```

**解决方案**:
```vue
<script setup>
const isFormValid = ref(true) // 预设为 true

onMounted(async () => {
  if (username.value && password.value) {
    isFormValid.value = true
  }
  await nextTick()
  if (form.value) {
    await form.value.validate()
  }
})
</script>
```

#### 页面导航超时
```bash
TimeoutError: page.goto: Timeout 30000ms exceeded
```

**解决方案**:
```javascript
// 重试机制
async function navigateWithRetry(page, url, maxRetries = 3) {
  for (let i = 0; i < maxRetries; i++) {
    try {
      await page.goto(url, { waitUntil: 'networkidle', timeout: 20000 })
      return
    } catch (error) {
      if (i === maxRetries - 1) throw error
      await page.waitForTimeout(2000)
    }
  }
}
```

#### 元素不可见或不可点击
```bash
Element is not visible or clickable
```
**解决方案**:
```javascript
async function clickElementSafely(page, selector) {
  const element = page.locator(selector).first()
  await element.waitFor({ state: 'visible', timeout: 5000 })
  await element.scrollIntoViewIfNeeded()
  await page.waitForTimeout(500) // 等待动画
  await element.click()
}
```

## 🎯 E2E测试修复经验 (2025-07-27)

### 测试ID命名约定和添加策略

**命名约定**:
```javascript
// 统一使用kebab-case格式，描述性命名
data-testid="create-signal-btn"        // 创建信号按钮
data-testid="signal-details-dialog"    // 信号详情对话框
data-testid="platform-filter"          // 平台筛选器
data-testid="batch-mark-processed"     // 批量标记已处理按钮
```

**添加策略**:
```vue
<!-- 1. 关键交互元素必须添加测试ID -->
<v-btn @click="createSignal" data-testid="create-signal-btn">创建信号</v-btn>

<!-- 2. 对话框和容器组件 -->
<v-dialog v-model="visible" data-testid="signal-details-dialog">

<!-- 3. 表单字段使用功能描述 -->
<v-textarea v-model="content" data-testid="create-signal-content" />

<!-- 4. 批量操作按钮使用动作描述 -->
<v-btn @click="batchProcess" data-testid="batch-mark-processed">
```

**按组件分组命名**:
- `create-signal-*`: 创建信号相关元素
- `signal-details-*`: 信号详情相关元素
- `batch-*`: 批量操作相关元素
- `view-toggle-*`: 视图切换相关元素

### 信号行交互解决方案

**问题**: Vuetify v-data-table不支持为行添加自定义data-signal-id属性

**解决方案**: 使用内容文本定位策略
```javascript
// ❌ 不可行 - Vuetify表格行没有自定义属性
await page.locator(`[data-signal-id="${signalId}"]`).click()

// ✅ 可行 - 使用内容文本过滤
await page.locator('[data-testid="table-view"] tbody tr')
  .filter({ hasText: '详情测试信号' })
  .click()

// ✅ 复选框选择
await page.locator('[data-testid="table-view"] tbody tr')
  .filter({ hasText: '批量操作信号1' })
  .locator('input[type="checkbox"]')
  .check()
```

**最佳实践**:
- 使用唯一的信号内容文本作为定位标识
- 结合表格容器的测试ID提高选择器精确性
- 对于批量操作，使用内容文本 + 复选框选择器

### 测试断言优化策略

**问题**: snackbar消息显示时间短，容易导致测试不稳定

**解决方案**: 专注于UI交互验证，避免依赖短暂显示的元素
```javascript
// ❌ 不稳定 - snackbar消息可能已消失
await expect(page.locator('.v-snackbar')).toContainText('操作成功')

// ✅ 稳定 - 验证UI状态变化
await expect(page.locator('[data-testid="toggle-processed-btn"]'))
  .toContainText('标记为未处理')

// ✅ 稳定 - 验证对话框关闭
await expect(page.locator('[data-testid="create-signal-dialog"]'))
  .not.toBeVisible()

// ✅ 稳定 - 验证数据出现在列表中
await expect(page.locator('[data-testid="signals-list"]'))
  .toContainText('这是一个E2E测试信号')
```

**断言优化原则**:
- 验证持久的UI状态而不是临时消息
- 使用功能结果验证而不是过程验证
- 优先验证用户可见的状态变化

### 并行测试配置和性能优化

**Playwright并行配置**:
```javascript
// playwright.config.js
const getWorkersForEnvironment = () => {
  if (process.env.CI) return 1
  // 本地环境使用CPU核心数的一半，但至少2个，最多4个
  const cpuCount = os.cpus().length
  return Math.min(Math.max(Math.floor(cpuCount / 2), 2), 4)
}

export default defineConfig({
  workers: getWorkersForEnvironment(),
  fullyParallel: true,
  // ...
})
```

**性能优化策略**:
```javascript
// 1. 使用refreshSignalsList()避免完整页面重新加载
const refreshSignalsList = async (page) => {
  await page.evaluate(() => {
    const signalsView = document.querySelector('[data-testid="signals-view"]')?.__vue__
    if (signalsView && signalsView.loadSignals) {
      signalsView.loadSignals()
    }
  })
  await page.waitForLoadState('networkidle')
}

// 2. 智能等待策略
await page.waitForFunction(() => {
  const listElement = document.querySelector('[data-testid="signals-list"]')
  return listElement && listElement.textContent.includes('测试信号')
}, { timeout: 5000 })

// 3. 批量断言更新
await update_tasks([
  {"task_id": "task1", "state": "COMPLETE"},
  {"task_id": "task2", "state": "IN_PROGRESS"}
])
```

**性能指标**:
- 单个测试执行时间: ~4秒 (优化后)
- 11个测试总时长: ~42秒 (并行执行)
- 成功率: 8/11 = 72.7%

### 可复用的修复模式

**Vuetify组件交互模式**:
```javascript
// 下拉选择器交互
await page.locator('[data-testid="platform-filter"]').click()
await page.waitForSelector('.v-list-item', { timeout: 5000 })
await page.locator('.v-list-item:has-text("Discord")').click()

// 文本输入框交互 (避开Vuetify内部元素)
await page.locator('[data-testid="create-signal-content"] textarea:not([readonly])')
  .fill('测试内容')

// 对话框交互模式
await page.locator('[data-testid="delete-signal-btn"]').click()
await expect(page.locator('[data-testid="delete-confirm-dialog"]')).toBeVisible()
await page.locator('[data-testid="confirm-delete-btn"]').click()
await expect(page.locator('[data-testid="delete-confirm-dialog"]')).not.toBeVisible()
```

**测试数据隔离模式**:
```javascript
// 使用唯一标识符避免测试间冲突
const testUser = await testDataFactory.createUser({
  username: `test_user_${Date.now()}_${Math.random().toString(36).substr(2, 8)}`
})

// 智能清理机制
await testDataFactory.cleanupUser(testUser.id, authHeaders)
```

## 🎯 E2E测试最佳实践

### 容错验证模式
**使用多重选择器提高测试稳定性**:
```javascript
async function verifyElementWithFallback(page, selectors, description) {
  for (const selector of selectors) {
    const element = page.locator(selector).first()
    if (await element.isVisible({ timeout: 5000 }).catch(() => false)) {
      console.log(`✅ ${description}: ${selector}`)
      return element
    }
  }
  throw new Error(`❌ 未找到元素: ${description}`)
}

// 使用示例
const orderRowSelectors = [
  `[data-testid="order-row-${orderId}"]`,
  `tbody tr:has-text("${orderId}")`,
  `tr:has-text("${orderId}")`
]
await verifyElementWithFallback(page, orderRowSelectors, '订单行')
```

### 测试数据管理
**动态测试数据生成**:
```javascript
class TestDataFactory {
  static generateUniqueId(prefix = 'test') {
    return `${prefix}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  static async createTestUser() {
    const uniqueId = this.generateUniqueId('user')
    return {
      username: `testuser_${uniqueId}`,
      email: `test_${uniqueId}@example.com`,
      password: 'TestPassword123!'
    }
  }

  static async cleanupTestData(identifier) {
    console.log(`🧹 清理测试数据: ${identifier}`)
    // 实现清理逻辑
  }
}
```

### 测试结构模板
**标准化的测试结构**:
```javascript
import { test, expect } from '@playwright/test'
import { TestDataFactory } from '../utils/test-data-factory'

test.describe('功能模块测试', () => {
  let testData

  test.beforeEach(async ({ page }) => {
    testData = await TestDataFactory.createTestScenario()
    await page.goto('/target-page')
  })

  test.afterEach(async () => {
    if (testData) {
      await TestDataFactory.cleanup(testData.id)
    }
  })

  test('应该执行核心功能', async ({ page }) => {
    // 步骤1：准备
    await TestLogger.step('准备测试环境', async () => {
      // 准备逻辑
    })

    // 步骤2：执行
    await TestLogger.step('执行核心操作', async () => {
      // 执行逻辑
    })

    // 步骤3：验证
    await TestLogger.step('验证结果', async () => {
      // 验证逻辑
    })
  })
})
```

### 错误处理和恢复测试
**网络错误模拟**:
```javascript
// 模拟网络错误
await page.route('**/api/**', route => {
  route.abort('failed')
})

// 验证错误处理
const errorSelectors = [
  'text=网络连接失败',
  'text=页面加载出错',
  'text=刷新页面'
]
await verifyElementWithFallback(page, errorSelectors, '错误提示')
```

### 用户状态模拟
**本地存储操作**:
```javascript
// 模拟首次用户
await page.evaluate(() => {
  localStorage.setItem('isFirstTime', 'true')
})

// 验证用户信息
const userInfoSelectors = [
  '[data-testid="user-info"]',
  '.user-profile',
  '.v-list-item:has-text("demo")'
]
```

### 测试日志和调试
**结构化日志记录**:
```javascript
class TestLogger {
  static async step(description, action) {
    console.log(`🔄 ${description}`)
    try {
      await action()
      console.log(`✅ ${description} - 完成`)
    } catch (error) {
      console.log(`❌ ${description} - 失败: ${error.message}`)
      throw error
    }
  }
}

// 使用示例
await TestLogger.step('用户登录', async () => {
  await fillLoginForm(page, credentials)
  await submitLogin(page)
})
```

**解决方案**:
```javascript
async function clickElementSafely(page, selector) {
  const element = page.locator(selector).first()
  
  // 等待元素可见
  await element.waitFor({ state: 'visible', timeout: 5000 })
  
  // 滚动到元素位置
  await element.scrollIntoViewIfNeeded()
  
  // 等待动画完成
  await page.waitForTimeout(500)
  
  // 确保元素可点击
  await element.waitFor({ state: 'attached' })
  
  // 执行点击
  await element.click()
}
```

### 前端单元测试

#### Vuetify 重复警告
```bash
[Vuetify] Multiple instances of Vuetify detected
```
**解决方案**:
```javascript
// tests/setup/vitest-setup.js - 单例模式
let vuetifyInstance = null

function getVuetifyInstance() {
  if (!vuetifyInstance) {
    vuetifyInstance = createVuetify({ components, directives })
  }
  return vuetifyInstance
}
```

#### WebSocket Mock 失败
```bash
WebSocket connection failed in test environment
```
**解决方案**:
```javascript
// 优化的 WebSocket Mock
global.WebSocket = vi.fn().mockImplementation(() => ({
  readyState: 1, // OPEN 状态
  close: vi.fn(),
  send: vi.fn(),
  addEventListener: vi.fn(),
  removeEventListener: vi.fn()
}))
```

### 测试环境问题

#### Docker 数据库连接错误
```bash
psycopg2.OperationalError: could not connect to server
```
**解决方案**:
```bash
# 检查和启动服务
docker-compose ps
docker-compose up -d postgres

# 验证连接
docker-compose exec postgres psql -U crypto_trader -d crypto_trader_test -c "SELECT 1;"
```

#### 端口冲突
```bash
Error: Port 8000 is already in use
```
**解决方案**:
```bash
# 查找和终止进程
lsof -i :8000
kill -9 <PID>

# 或使用不同端口
export API_PORT=8001
export FRONTEND_PORT=5174
```

#### Discord 库兼容性 
```bash
AttributeError: 'Intents' object has no attribute 'message_content'
```
**解决方案**:
```python
# ✅ 正确 - discord.py-self 不需要 intents
class TradingSignalClient(discord.Client):
    def __init__(self, *args, **kwargs):
        # discord.py-self 不需要 intents 参数
        super().__init__(*args, **kwargs)

# ❌ 错误 - 标准 discord.py 的写法
# intents = discord.Intents.default()
# super().__init__(intents=intents, *args, **kwargs)
```

## 🔍 环境排查指南

### 环境检查清单
```bash
# 服务状态检查
python scripts/test.py --status
docker-compose ps

# 环境变量检查
env | grep -E "(DATABASE_URL|API_BASE_URL|FRONTEND_URL)"

# 端口占用检查
netstat -tulpn | grep -E "(5432|8000|5173)"
```

### 分步诊断流程 
```bash
# 1. 基础环境检查
python scripts/test.py --check-env

# 2. 数据库连接测试
python -c "
import asyncpg, asyncio
async def test_db():
    conn = await asyncpg.connect('postgresql://crypto_trader:test_password_123@localhost:5432/crypto_trader_test')
    result = await conn.fetchval('SELECT 1')
    print(f'Database: {result}')
    await conn.close()
asyncio.run(test_db())
"

# 3. 服务可用性测试
curl -f http://localhost:8000/health || echo "Backend unavailable"
curl -f http://localhost:5173 || echo "Frontend unavailable"
```

### 日志分析
```bash
# 详细测试日志
python scripts/test.py --pytest unit --verbose

# Playwright 调试
npx playwright test --debug

# Docker 服务日志
docker-compose logs --tail=50 postgres backend
```

## 🛠️ 调试技巧

### 后端调试
```python
# 调试断点
import pdb; pdb.set_trace()

# 详细模式
pytest -s -vv tests/unit/test_specific.py::test_function

# 异常信息
pytest --tb=long tests/unit/test_specific.py
```

### 前端调试
```javascript
// Playwright 调试
test('debug test', async ({ page }) => {
  await page.pause() // 调试模式
  await page.screenshot({ path: 'debug.png' }) // 截图
  page.on('console', msg => console.log('PAGE:', msg.text())) // 日志
})
```

### 性能调试
```bash
# 执行时间分析
pytest --durations=10 tests/

# 性能追踪
npx playwright test --trace on

# 内存监控
python -m memory_profiler scripts/test.py --pytest unit
```

## 📈 优化经验

### 稳定性提升
- 使用明确等待条件而非固定延迟
- 实现重试机制处理网络不稳定
- 正确管理测试数据生命周期
- 使用事务回滚确保测试隔离

### 性能优化
- 并行执行独立测试
- 复用数据库连接和浏览器实例
- 使用内存数据库进行快速测试
- 优化测试数据创建和清理

### 可维护性
- 使用 Page Object 模式组织 E2E 测试
- 创建可复用的测试工具函数
- 统一错误处理和日志记录
- 定期重构和清理过时测试

## 🔄 持续改进

### 定期维护
- **每周**: 分析失败模式，更新解决方案
- **每月**: 审查性能，优化慢速测试
- **每季度**: 评估架构，引入新工具

### 团队协作
- 记录新问题和解决方案
- 分享调试技巧和最佳实践
- 定期技术分享

### 工具改进
- 引入更好的测试工具
- 自动化问题检测和修复
- 建立质量监控仪表板

---
**维护**: 持续更新的团队知识库 | **更新**: 2025-07-27 | **团队**: AI Crypto Trading

## 📝 更新日志

### 2025-07-27 - E2E测试修复经验
- 添加测试ID命名约定和添加策略
- 记录信号行交互的内容文本定位解决方案
- 总结测试断言优化策略（避免snackbar依赖）
- 文档化并行测试配置和性能优化方法
- 提供可复用的Vuetify组件交互模式
