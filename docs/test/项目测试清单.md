# 📋 项目测试清单

**版本**: 2.0 | **更新**: 2025-07-26 | **项目**: AI Crypto Trading Agent

## 🎯 测试覆盖指导

### 格式标准
`测试模块 - 测试类别(文档位置) - 测试方法 - 功能描述 - 备注说明`

### 优先级标记
- 🔴 P0: 核心功能，必须测试
- 🟡 P1: 重要功能，应该测试
- 🟢 P2: 辅助功能，可选测试

## 🎭 Playwright 测试清单 (前端集成)

### API 接口测试 (`frontend/tests/api-unified/`)

#### 🔴 P0 - 认证 API (auth.api.test.js)
- 认证API - 用户登录 - POST /api/v1/auth/login - 正常登录流程 - 真实数据
- 认证API - 登录失败 - POST /api/v1/auth/login - 错误凭据处理 - 边界条件
- 认证API - 字段验证 - POST /api/v1/auth/login - 必需字段验证 - 输入验证
- 认证API - 安全测试 - POST /api/v1/auth/login - SQL注入/XSS防护 - 安全性
- 认证API - 用户注册 - POST /api/v1/auth/register - 注册流程 - 真实数据
- 认证API - 用户信息 - GET /api/v1/auth/me - 信息获取 - 数据完整性
- 认证API - Token验证 - GET /api/v1/auth/me - token机制 - 安全性

**实现状态**: ✅ **27个测试已实现** | **通过率**: 100% | **覆盖范围**: 用户认证、授权、安全性

| 已实现测试 | 测试目的 | 覆盖API端点 | 状态 |
|-----------|----------|-------------|------|
| 应该成功登录有效用户 | 验证正常登录流程 | POST /api/v1/auth/login | ✅ |
| 应该拒绝无效凭据 | 验证错误凭据处理 | POST /api/v1/auth/login | ✅ |
| 应该验证必需字段 | 验证字段验证逻辑 | POST /api/v1/auth/login | ✅ |
| 应该处理特殊字符和边界情况 | 验证输入安全性 | POST /api/v1/auth/login | ✅ |
| 应该处理并发登录请求 | 验证并发处理能力 | POST /api/v1/auth/login | ✅ |
| 应该成功注册新用户 | 验证用户注册流程 | POST /api/v1/auth/register | ✅ |
| 应该拒绝重复用户名 | 验证唯一性约束 | POST /api/v1/auth/register | ✅ |
| 应该验证密码强度 | 验证密码策略 | POST /api/v1/auth/register | ✅ |
| 应该获取当前用户信息 | 验证用户信息获取 | GET /api/v1/auth/me | ✅ |
| 应该拒绝无效token | 验证token验证 | GET /api/v1/auth/me | ✅ |
| 应该要求认证头 | 验证认证要求 | GET /api/v1/auth/me | ✅ |
| Token应该有合理的过期时间 | 验证token生命周期 | 多个端点 | ✅ |
| 应该防止SQL注入攻击 | 验证安全防护 | POST /api/v1/auth/login | ✅ |
| 应该防止XSS攻击 | 验证XSS防护 | POST /api/v1/auth/login | ✅ |

#### 🔴 P0 - Agent API (agent.api.test.js)
- AgentAPI - 消息处理 - POST /api/v1/agent/process - 交易指令处理 - 核心业务
- AgentAPI - 输入验证 - POST /api/v1/agent/process - 输入验证逻辑 - 数据验证
- AgentAPI - 复杂指令 - POST /api/v1/agent/process - 复杂场景处理 - 业务逻辑
- AgentAPI - 任务查询 - GET /api/v1/agent/tasks/{id} - 状态查询 - 状态管理
- AgentAPI - 错误处理 - GET /api/v1/agent/tasks/{id} - 不存在任务 - 错误处理
- AgentAPI - 并发处理 - POST /api/v1/agent/process - 并发消息 - 性能测试

**实现状态**: ✅ **20个测试已实现** | **通过率**: 100% | **覆盖范围**: Agent消息处理、任务管理、性能

| 已实现测试 | 测试目的 | 覆盖API端点 | 状态 |
|-----------|----------|-------------|------|
| 应该成功处理有效的交易指令 | 验证消息处理核心功能 | POST /api/v1/agent/process | ✅ |
| 应该拒绝空的或无效的输入 | 验证输入验证 | POST /api/v1/agent/process | ✅ |
| 应该处理复杂的交易指令 | 验证复杂场景处理 | POST /api/v1/agent/process | ✅ |
| 应该处理特殊字符和边界情况 | 验证边界条件 | POST /api/v1/agent/process | ✅ |
| 应该要求认证 | 验证认证要求 | POST /api/v1/agent/process | ✅ |
| 应该拒绝无效的认证token | 验证token验证 | POST /api/v1/agent/process | ✅ |
| 应该成功查询任务状态 | 验证状态查询功能 | GET /api/v1/agent/tasks/{id} | ✅ |
| 应该处理不存在的任务ID | 验证错误处理 | GET /api/v1/agent/tasks/{id} | ✅ |
| 应该处理并发消息处理请求 | 验证并发处理能力 | POST /api/v1/agent/process | ✅ |
| 应该在合理时间内响应 | 验证性能要求 | 多个端点 | ✅ |

#### 🟡 P1 - 健康检查 API (health.api.test.js)
- 健康检查 - 基础检查 - GET /api/v1/health - 系统健康状态 - 系统监控
- 健康检查 - 系统状态 - GET /api/v1/health/status - 详细状态 - 状态监控
- 健康检查 - 就绪检查 - GET /api/v1/health/ready - 服务就绪 - 部署验证

**实现状态**: ✅ **15个测试已实现** | **通过率**: 100% | **覆盖范围**: 系统监控、状态检查、性能指标

| 测试分类 | 测试数量 | 主要覆盖端点 | 状态 |
|---------|----------|-------------|------|
| 基础健康检查 | 3个 | GET /api/v1/health | ✅ |
| 系统状态检查 | 4个 | GET /api/v1/health/status | ✅ |
| 就绪状态检查 | 3个 | GET /api/v1/health/ready | ✅ |
| 性能监控 | 2个 | GET /api/v1/health/metrics | ✅ |
| 错误处理 | 3个 | 多个健康检查端点 | ✅ |

#### 🔴 P0 - 订单 API (orders.api.test.js)
- 订单API - 订单列表 - GET /api/v1/orders - 列表获取 - 数据查询
- 订单API - 单个订单 - GET /api/v1/orders/{id} - 单个查询 - 数据详情
- 订单API - 认证授权 - 所有订单端点 - 权限控制 - 安全性
- 订单API - 并发性能 - 多个订单端点 - 并发访问 - 性能测试

**实现状态**: ✅ **34个测试已实现** | **通过率**: 100% | **覆盖范围**: 订单管理、查询、认证、性能

| 测试分类 | 测试数量 | 主要覆盖端点 | 状态 |
|---------|----------|-------------|------|
| 订单列表API | 6个 | GET /api/v1/orders | ✅ |
| 单个订单API | 3个 | GET /api/v1/orders/{id} | ✅ |
| 认证和授权 | 3个 | 所有订单端点 | ✅ |
| 并发和性能测试 | 3个 | 多个订单端点 | ✅ |
| 错误处理和边界情况 | 3个 | 多个订单端点 | ✅ |

### E2E 测试 (`frontend/tests/e2e/`)

#### 🔴 P0 - 用户认证流程 (auth.test.js)
- E2E认证 - 完整登录 - 登录页面到仪表盘 - 完整登录流程 - 用户旅程
- E2E认证 - 注册流程 - 注册页面到激活 - 用户注册流程 - 用户引导
- E2E认证 - 权限验证 - 受保护页面访问 - 权限控制 - 安全性

#### 🔴 P0 - 综合业务流程 (comprehensive-flow.test.js)
- E2E业务 - 交易流程 - 登录到下单完成 - 完整交易流程 - 核心业务
- E2E业务 - 订单管理 - 创建到取消订单 - 订单生命周期 - 订单管理
- E2E业务 - 配置管理 - 风险配置设置 - 配置功能 - 系统配置

#### 🔴 P0 - 真实数据交易流程 (real-data-trading-flow.test.js)
- E2E真实 - 真实用户流程 - 真实账户交易 - 真实数据集成 - 集成验证
- E2E真实 - 数据一致性 - 前后端数据同步 - 数据一致性 - 数据完整性
- E2E真实 - WebSocket通信 - 实时数据更新 - 实时通信 - 实时性

**实现状态**: ✅ **完整E2E测试套件已实现** | **通过率**: 100% | **覆盖范围**: 端到端真实数据验证

| 测试文件 | 测试目的 | 覆盖流程 | 状态 |
|---------|----------|---------|------|
| `auth.test.js` | 用户认证流程 | 登录、注册、权限验证 | ✅ |
| `comprehensive-flow.test.js` | 综合业务流程 | 完整交易流程 | ✅ |
| `real-data-trading-flow.test.js` | 真实数据交易流程 | 端到端真实数据验证 | ✅ |
| `conditional-orders-flow.test.js` | 条件订单流程 | 高级订单功能 | ✅ |
| `config-management.test.js` | 配置管理流程 | 系统配置和设置 | ✅ |

**真实数据交易流程测试详情**:
| 测试步骤 | 验证内容 | 状态 |
|---------|----------|------|
| 用户注册和登录 | 创建真实用户并验证登录流程 | ✅ |
| 订单创建 | 创建BTC/USDT和ETH/USDT真实订单 | ✅ |
| 前端数据显示 | 验证订单在表格中正确显示 | ✅ |
| 元素定位 | 验证订单行级别的测试ID定位 | ✅ |
| 配置页面导航 | 验证导航到配置页面功能 | ✅ |
| 配置页面验证 | 验证交易所配置页面显示 | ✅ |
| 数据清理 | 测试后自动清理测试数据 | ✅ |

#### 🟡 P1 - 条件订单流程 (conditional-orders-flow.test.js)
- E2E条件 - 条件订单创建 - 高级订单功能 - 条件订单逻辑 - 高级功能
- E2E条件 - 触发机制 - 条件满足时执行 - 触发逻辑 - 业务规则

#### 🟡 P1 - 配置管理流程 (config-management.test.js)
- E2E配置 - 系统配置 - 交易所配置设置 - 配置管理 - 系统管理
- E2E配置 - 配置验证 - 配置项验证逻辑 - 配置有效性 - 数据验证

#### 🟡 P1 - UI系统测试 (ui-system.test.js)
- UI系统 - 组件加载 - 主要UI组件渲染 - 界面完整性 - UI验证
- UI系统 - 响应式设计 - 不同屏幕尺寸适配 - 响应式布局 - 兼容性
- UI系统 - 主题切换 - 明暗主题切换功能 - 主题一致性 - 用户体验

#### 🟡 P1 - 用户引导测试 (user-guidance.test.js)
- 用户引导 - 首次访问 - 新用户引导流程 - 用户体验 - 引导完整性
- 用户引导 - 功能介绍 - 关键功能说明 - 用户教育 - 功能发现
- 用户引导 - 帮助系统 - 帮助文档和提示 - 用户支持 - 自助服务

#### 🟢 P2 - 错误边界测试 (error-boundary.test.js)
- 错误边界 - JavaScript错误 - 前端错误捕获和处理 - 错误恢复 - 稳定性
- 错误边界 - 网络错误 - 网络异常处理 - 错误提示 - 用户体验
- 错误边界 - 数据错误 - 数据异常处理 - 数据验证 - 数据完整性

#### 🟢 P2 - 用户管理测试 (user-management.test.js)
- 用户管理 - 用户信息 - 用户信息显示和编辑 - 信息管理 - 数据准确性
- 用户管理 - 用户设置 - 用户偏好设置 - 个性化 - 用户体验
- 用户管理 - 会话管理 - 用户会话状态管理 - 安全性 - 状态一致性

## 🐍 pytest 测试清单 (后端测试)

### 单元测试 (`backend/tests/unit/`)

#### 🔴 P0 - Agent 节点测试 (test_agent_nodes.py)
- Agent单元 - 文本预处理 - preprocess_text() - 文本预处理逻辑 - 数据处理
- Agent单元 - 意图解析 - parse_intents() - 意图解析算法 - AI逻辑
- Agent单元 - 上下文获取 - get_context() - 上下文获取逻辑 - 上下文管理
- Agent单元 - 计划生成 - generate_plan() - 计划生成算法 - 决策逻辑
- Agent单元 - 风险评估 - assess_risk() - 风险评估逻辑 - 风险管理
- Agent单元 - 计划执行 - execute_plan() - 计划执行逻辑 - 执行引擎
- Agent单元 - 错误分析 - analyze_error() - 错误分析逻辑 - 错误处理

**实现状态**: ✅ **20个测试已实现** | **通过率**: 100% | **覆盖范围**: Agent核心业务逻辑

| 测试类 | 测试数量 | 测试目的 | 状态 |
|-------|----------|----------|------|
| TestPreprocessText | 3个 | 文本预处理逻辑 | ✅ |
| TestParseIntents | 5个 | 意图解析算法 | ✅ |
| TestGetContext | 3个 | 上下文获取逻辑 | ✅ |
| TestGeneratePlan | 2个 | 计划生成算法 | ✅ |
| TestAssessRisk | 2个 | 风险评估逻辑 | ✅ |
| TestExecutePlan | 2个 | 计划执行逻辑 | ✅ |
| TestAnalyzeError | 2个 | 错误分析逻辑 | ✅ |
| TestRequestUserConfirmation | 1个 | 用户确认逻辑 | ✅ |

#### 🔴 P0 - 数据模型测试 (test_models.py)
- 模型测试 - User模型 - User类所有方法 - 用户数据模型 - 数据完整性
- 模型测试 - Order模型 - Order类所有方法 - 订单数据模型 - 业务模型
- 模型测试 - RiskConfig模型 - RiskConfig类所有方法 - 风险配置模型 - 配置管理
- 模型测试 - AgentCheckpoint模型 - AgentCheckpoint类所有方法 - Agent状态模型 - 状态管理
- 模型测试 - PendingAction模型 - PendingAction类所有方法 - 待处理动作模型 - 任务管理
- 模型测试 - ConditionalOrder模型 - ConditionalOrder类所有方法 - 条件订单模型 - 高级功能
- 模型测试 - TradingConfig模型 - TradingConfig类所有方法 - 交易配置模型 - 交易管理

**实现状态**: ✅ **54个测试已实现** | **通过率**: 100% | **覆盖范围**: 所有数据库模型

| 模型类 | 测试数量 | 测试目的 | 状态 |
|-------|----------|----------|------|
| User模型 | 8个 | 用户数据模型验证 | ✅ |
| Order模型 | 8个 | 订单数据模型验证 | ✅ |
| RiskConfig模型 | 8个 | 风险配置模型验证 | ✅ |
| AgentCheckpoint模型 | 8个 | Agent状态模型验证 | ✅ |
| PendingAction模型 | 8个 | 待处理动作模型验证 | ✅ |
| ConditionalOrder模型 | 8个 | 条件订单模型验证 | ✅ |
| TradingConfig模型 | 6个 | 交易配置模型验证 | ✅ |

#### 🔴 P0 - API 路由业务逻辑 (test_api_routes.py)
- API逻辑 - Agent业务逻辑 - Agent相关API函数 - Agent业务逻辑 - 业务层
- API逻辑 - 订单业务逻辑 - Order相关API函数 - 订单业务逻辑 - 订单管理
- API逻辑 - 健康检查逻辑 - Health相关API函数 - 健康检查逻辑 - 系统监控

#### 🟡 P1 - WebSocket 测试 (test_websocket.py)
- WebSocket - 连接管理 - WebSocket连接生命周期 - 连接管理 - 实时通信
- WebSocket - 消息处理 - 消息发送接收逻辑 - 消息处理 - 消息传递
- WebSocket - 错误处理 - 连接错误处理 - 错误恢复 - 容错性

### 集成测试 (`backend/tests/integration/`)

#### 🔴 P0 - Agent 工作流集成 (test_agent_workflow.py)
- Agent集成 - 完整买单流程 - 从指令到执行 - 买单工作流 - 核心业务
- Agent集成 - 完整卖单流程 - 从指令到执行 - 卖单工作流 - 核心业务
- Agent集成 - 风险拒绝流程 - 高风险指令处理 - 风险控制 - 风险管理
- Agent集成 - 错误重试机制 - 失败后重试逻辑 - 错误恢复 - 容错性
- Agent集成 - 状态管理 - Agent状态变更 - 状态一致性 - 状态管理
- Agent集成 - 数据库集成 - 数据持久化 - 数据存储 - 数据完整性

**实现状态**: ✅ **11个测试已实现** | **通过率**: 100% | **覆盖范围**: Agent完整工作流程

| 测试类 | 测试数量 | 测试目的 | 状态 |
|-------|----------|----------|------|
| TestAgentWorkflowIntegration | 6个 | 完整业务流程集成 | ✅ |
| TestAgentStateManagement | 2个 | 状态管理集成 | ✅ |
| TestDatabaseIntegration | 3个 | 数据库集成 | ✅ |

#### 🟡 P1 - Discord 集成 (test_discord_integration.py)
- Discord集成 - 消息去重 - 重复消息处理 - 消息去重逻辑 - 消息处理
- Discord集成 - 信号处理 - 交易信号解析 - 信号处理逻辑 - 信号解析
- Discord集成 - 客户端集成 - Discord客户端功能 - Discord集成 - 外部集成

**实现状态**: ✅ **32个测试已实现** | **通过率**: 100% (1个跳过) | **覆盖范围**: Discord服务集成

| 测试类 | 测试数量 | 测试目的 | 状态 |
|-------|----------|----------|------|
| TestMessageDeduplicator | 3个 | 消息去重逻辑 | ✅ |
| TestSignalProcessor | 15个 | 信号处理逻辑 | ✅ |
| TestTradingSignalClient | 3个 | Discord客户端 | ✅ |
| TestDiscordListenerManager | 3个 | 监听器管理 | ✅ |
| TestDiscordIntegration | 2个 | 完整集成流程 | ✅ |
| TestDiscordStandalone | 5个 | Discord组件独立验证 | ✅ |

## ⚡ Vitest 测试清单 (前端单元)

### 组件测试 (`frontend/tests/components/`)

#### 🟡 P1 - 仪表盘组件 (dashboard.test.js)
- 组件测试 - DashboardView - 仪表盘主视图组件 - 仪表盘渲染 - UI组件
- 组件测试 - StatsCards - 统计卡片组件 - 统计数据显示 - 数据展示
- 组件测试 - LiveLogStream - 实时日志流组件 - 日志流功能 - 实时显示
- 组件测试 - PendingActionsList - 待处理动作列表 - 动作列表 - 列表组件

#### 🟡 P1 - 订单组件 (orders.test.js)
- 组件测试 - OrderList - 订单列表组件 - 订单列表渲染 - 列表组件
- 组件测试 - OrderForm - 订单表单组件 - 订单创建表单 - 表单组件
- 组件测试 - OrderStatus - 订单状态组件 - 状态显示 - 状态组件

#### 🟢 P2 - 条件订单组件 (conditional-orders.test.js)
- 组件测试 - ConditionalOrderForm - 条件订单表单 - 条件订单创建 - 高级表单
- 组件测试 - ConditionBuilder - 条件构建器 - 条件逻辑构建 - 逻辑组件

### 🟡 P1 - 工具函数测试 (`frontend/tests/unit/utils/`)
- 工具测试 - 数据格式化 - 格式化函数 - 数据格式化逻辑 - 数据处理
- 工具测试 - 验证逻辑 - 验证函数 - 输入验证逻辑 - 数据验证
- 工具测试 - API客户端 - HTTP客户端函数 - API调用逻辑 - 网络通信

### 🟡 P1 - Store 状态测试 (`frontend/tests/unit/stores/`)
- Store测试 - 用户Store - 用户状态管理 - 用户状态逻辑 - 状态管理
- Store测试 - 订单Store - 订单状态管理 - 订单状态逻辑 - 业务状态
- Store测试 - 配置Store - 配置状态管理 - 配置状态逻辑 - 配置管理

## 📊 覆盖率目标和实现状态

### 当前实现状态总览
| 模块类型 | 已实现测试数 | 通过率 | 当前覆盖率 | 目标覆盖率 | 状态 |
|---------|-------------|--------|-----------|-----------|------|
| **API模块** | 96个 | 100% | 98.4% | 100% | 🟢 接近完成 |
| **核心业务逻辑** | 85个 | 100% | 88.3% | 95% | 🟡 需要补充 |
| **E2E流程** | 完整套件 | 100% | 80% | 80% | ✅ 已达标 |
| **前端组件** | 待补充 | - | 27.24% | 70% | 🔴 急需补充 |
| **工具函数** | 待补充 | - | 15.88% | 80% | 🔴 急需补充 |
| **Store模块** | 待补充 | - | 65% | 85% | 🟡 需要补充 |

### 详细实现统计
| 测试类型 | 文件数 | 测试数量 | 通过率 | 覆盖重点 |
|---------|-------|----------|--------|----------|
| **Playwright API测试** | 4个 | 96个 | 100% | 认证、Agent、健康检查、订单 |
| **Playwright E2E测试** | 5个 | 完整套件 | 100% | 真实数据流程、用户旅程 |
| **pytest 单元测试** | 3个 | 85个 | 100% | Agent节点、数据模型、API逻辑 |
| **pytest 集成测试** | 3个 | 43个 | 100% | Agent工作流、Discord集成 |
| **Vitest 组件测试** | 待补充 | 待补充 | - | 前端组件、工具函数、Store |

### 执行计划
1. **P0 阶段** ✅: 核心API和业务逻辑 → **已完成95%+覆盖率**
2. **P1 阶段** 🟡: 重要功能和组件 → **进行中，目标80%覆盖率**
3. **P2 阶段** ⏭️: 辅助功能和优化 → **计划中，目标70%覆盖率**

## 🎯 AI 测试生成指导

### 使用此清单进行测试用例生成时：
1. **按优先级选择** - 优先生成 P0 测试
2. **遵循格式标准** - 使用标准化格式
3. **包含完整信息** - 模块、类别、方法、描述、备注
4. **真实数据原则** - API/E2E 测试使用真实数据
5. **分层策略** - 在合适层级验证功能
6. **参考实现状态** - 借鉴已实现测试的模式和标准

---
**维护**: 随功能开发持续更新 | **更新**: 2025-07-26 | **审查**: 2025-08-26
