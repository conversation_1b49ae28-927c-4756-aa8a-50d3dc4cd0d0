# 信号追踪优化文档

## 1. 概述

### 1.1 文档目标

本文档基于对现有Agent执行追踪系统的深入分析，提供针对性的优化方案，以增强信号处理、LLM调用和订单执行过程的业务数据记录完整性。优化方案遵循项目的核心设计原则：**状态驱动与可追溯**、**务实的数据持久化**，避免过度设计，专注于实用性和可维护性。

### 1.2 现状分析

**✅ 已实现的基础设施：**
- `agent_execution_traces`表结构完整，包含所有必要字段
- `AgentTraceService`类提供完整的追踪功能
- `@trace_agent_node`装饰器已应用到所有节点
- 与现有系统架构完全兼容

**❌ 关键问题：**
- 业务数据记录不完整：仅记录基础统计信息，缺少详细业务内容
- LLM处理过程缺乏可见性：prompt、响应、token使用等关键信息未被记录
- 订单执行细节缺失：交易过程中的价格、滑点、手续费等信息记录不完整
- 错误处理信息不够详细：缺少错误上下文和恢复建议

### 1.3 设计原则

遵循《后端设计文档》中的核心原则：

1. **状态驱动与可追溯**：确保每个决策路径都是完全透明、可审计、可恢复、可调试的
2. **务实的数据持久化**：利用PostgreSQL的JSONB字段灵活性存储半结构化的业务数据
3. **契约优先**：基于Pydantic模型的严格数据结构定义
4. **避免过度设计**：专注于核心业务需求，保持系统简洁性

## 2. 优化方案设计

### 2.1 数据记录增强策略

基于现有`agent_execution_traces`表的JSONB字段，通过优化`input_data`、`output_data`和`error_data`的数据结构，实现业务数据的完整记录。

#### 2.1.1 核心数据结构定义

```python
# 基础追踪数据结构
class TraceInputData(BaseModel):
    """节点输入数据标准结构"""
    # 通用字段
    raw_input: str
    task_id: str
    user_id: str
    timestamp: datetime
    node_sequence: Optional[str]
    retry_count: int = 0
    
    # 节点特定字段（使用Union类型）
    node_specific: Union[
        PreprocessInputData,
        ParseInputData,
        ContextInputData,
        PlanInputData,
        RiskInputData,
        ExecuteInputData
    ]

class TraceOutputData(BaseModel):
    """节点输出数据标准结构"""
    # 通用字段
    execution_success: bool
    processing_time_ms: int
    timestamp: datetime
    
    # 节点特定字段
    node_specific: Union[
        PreprocessOutputData,
        ParseOutputData,
        ContextOutputData,
        PlanOutputData,
        RiskOutputData,
        ExecuteOutputData
    ]

class TraceErrorData(BaseModel):
    """错误数据标准结构"""
    error_type: str
    error_message: str
    error_context: Dict[str, Any]
    stack_trace: Optional[str]
    recovery_suggestion: Optional[str]
    is_retryable: bool
    node_state_snapshot: Dict[str, Any]
```

### 2.2 节点特定数据结构

#### 2.2.1 Parse节点（LLM处理追踪）

```python
class ParseInputData(BaseModel):
    """Parse节点输入数据"""
    signal_content: str
    signal_length: int
    context_available: bool
    llm_config: Dict[str, Any]
    prompt_data: LLMPromptData

class LLMPromptData(BaseModel):
    """LLM调用数据"""
    system_prompt: str
    user_prompt: str
    model: str
    temperature: float
    max_tokens: Optional[int]
    context_info: Optional[str]

class ParseOutputData(BaseModel):
    """Parse节点输出数据"""
    llm_response: LLMResponseData
    parsed_intents: List[Dict[str, Any]]
    parsing_success: bool
    confidence_scores: List[float]
    intent_types: List[str]

class LLMResponseData(BaseModel):
    """LLM响应数据"""
    response_content: str
    response_time_ms: int
    token_usage: Optional[Dict[str, int]]
    model_used: str
    finish_reason: Optional[str]
```

#### 2.2.2 Execute节点（订单执行追踪）

```python
class ExecuteInputData(BaseModel):
    """Execute节点输入数据"""
    execution_plans: List[TradePlanData]
    risk_approved: bool
    market_context: MarketContextData

class TradePlanData(BaseModel):
    """交易计划数据"""
    plan_id: str
    symbol: str
    side: str
    order_type: str
    quantity: float
    price: Optional[float]
    risk_parameters: Dict[str, Any]

class MarketContextData(BaseModel):
    """市场上下文数据"""
    market_prices: Dict[str, float]
    active_orders_count: int
    account_balance: Optional[Dict[str, float]]
    market_conditions: Optional[str]

class ExecuteOutputData(BaseModel):
    """Execute节点输出数据"""
    execution_results: List[TradeExecutionResult]
    success_count: int
    failure_count: int
    total_volume: float
    execution_summary: ExecutionSummary

class TradeExecutionResult(BaseModel):
    """单个交易执行结果"""
    plan_id: str
    status: str
    order_id: Optional[str]
    client_order_id: str
    execution_price: Optional[float]
    executed_quantity: Optional[float]
    fees: Optional[Dict[str, float]]
    slippage: Optional[float]
    error_message: Optional[str]
    execution_time_ms: int

class ExecutionSummary(BaseModel):
    """执行摘要"""
    total_plans: int
    successful_executions: int
    failed_executions: int
    total_fees: float
    average_slippage: float
    execution_efficiency: float
```

## 3. 实施方案

### 3.1 优先级分级

#### 高优先级（立即实施）
1. **Parse节点LLM追踪增强**：记录prompt、响应、token使用
2. **Execute节点订单执行追踪**：记录交易详情、价格、手续费
3. **基础错误处理增强**：详细错误上下文和恢复建议

#### 中优先级（第二阶段）
1. **Context节点市场数据追踪**：记录风控配置、市场价格
2. **Plan节点决策过程追踪**：记录计划生成逻辑和参数
3. **性能指标优化**：执行时间、资源使用等

#### 低优先级（优化阶段）
1. **Preprocess节点文本处理追踪**：记录规范化过程
2. **Risk节点风险评估追踪**：记录风险计算过程
3. **历史数据分析工具**：基于追踪数据的分析功能

### 3.2 具体实施步骤

#### 步骤1：增强`_extract_input_data`函数

```python
def _extract_input_data(state: AgentState, node_name: str) -> Dict[str, Any]:
    """提取节点输入数据 - 增强版"""
    base_data = {
        "raw_input": state.raw_input,
        "task_id": str(state.task_id),
        "user_id": str(state.user_id),
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "node_sequence": state.current_node,
        "retry_count": state.retry_count
    }
    
    if node_name == "Parse":
        return {
            **base_data,
            "node_specific": {
                "signal_content": state.raw_input,
                "signal_length": len(state.raw_input),
                "context_available": bool(state.context),
                "parsed_intents_count": len(state.parsed_intents),
                "llm_config": {
                    "model": getattr(settings.llm, 'default_model', 'unknown'),
                    "temperature": 0.1,
                    "provider": getattr(settings.llm, 'default_provider', 'unknown')
                }
            }
        }
    elif node_name == "Execute":
        return {
            **base_data,
            "node_specific": {
                "execution_plans": [
                    {
                        "plan_id": str(plan.plan_id),
                        "symbol": plan.symbol,
                        "side": plan.side,
                        "order_type": plan.order_type,
                        "quantity": float(plan.quantity),
                        "price": float(plan.price) if plan.price else None
                    }
                    for plan in state.execution_plan
                ],
                "risk_approved": bool(state.risk_assessment),
                "market_context": {
                    "market_prices": state.context.get("market_prices", {}),
                    "active_orders_count": len(state.context.get("active_orders", [])),
                    "risk_config": state.context.get("risk_config", {})
                }
            }
        }
    # ... 其他节点的实现
    
    return base_data

#### 步骤2：增强`_extract_output_data`函数

```python
def _extract_output_data(result: Any, node_name: str) -> Dict[str, Any]:
    """提取节点输出数据 - 增强版"""
    base_data = {
        "execution_success": True,
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "result_type": type(result).__name__
    }

    if node_name == "Parse":
        parsed_intents = getattr(result, 'parsed_intents', [])
        return {
            **base_data,
            "node_specific": {
                "parsed_intents": [
                    {
                        "intent_type": intent.intent_type,
                        "raw_text": intent.raw_text,
                        "side": intent.side,
                        "symbol": intent.symbol,
                        "quantity_usd": intent.quantity_usd,
                        "confidence": intent.confidence,
                        "clarification_needed": intent.clarification_needed
                    }
                    for intent in parsed_intents
                ],
                "parsing_success": len(parsed_intents) > 0,
                "confidence_scores": [intent.confidence for intent in parsed_intents],
                "intent_types": [intent.intent_type for intent in parsed_intents],
                "total_intents": len(parsed_intents)
            }
        }
    elif node_name == "Execute":
        execution_results = getattr(result, 'execution_results', [])
        return {
            **base_data,
            "node_specific": {
                "execution_results": [
                    {
                        "status": res.get("status"),
                        "order_id": res.get("order_id"),
                        "client_order_id": res.get("client_order_id"),
                        "error_message": res.get("error_message"),
                        "execution_price": res.get("price"),
                        "executed_quantity": res.get("quantity")
                    }
                    for res in execution_results
                ],
                "execution_summary": {
                    "total_plans": len(execution_results),
                    "success_count": len([r for r in execution_results if r.get("status") == "success"]),
                    "failure_count": len([r for r in execution_results if r.get("status") != "success"]),
                    "execution_efficiency": len([r for r in execution_results if r.get("status") == "success"]) / max(len(execution_results), 1)
                }
            }
        }
    # ... 其他节点的实现

    return base_data

#### 步骤3：LLM调用追踪增强

在`parse_intents`函数中添加详细的LLM调用追踪：

```python
async def parse_intents_with_enhanced_tracking(state: AgentState, db: AsyncSession) -> AgentState:
    """增强版意图解析，包含详细的LLM调用追踪"""

    # 构建上下文信息
    context_info = ""
    if state.context and "active_orders" in state.context:
        active_count = len(state.context["active_orders"])
        context_info = f"\n\n当前用户有 {active_count} 个活跃订单。"

    # 准备LLM调用数据
    system_prompt = PARSE_INTENTS_PROMPT
    user_prompt = f"User Input: {state.raw_input}{context_info}\n\nParse this into trading intents."

    # 记录LLM调用前的状态（可以通过修改trace_agent_node装饰器实现）
    llm_input_data = {
        "prompt_data": {
            "system_prompt": system_prompt,
            "user_prompt": user_prompt,
            "model": settings.llm.default_model,
            "temperature": 0.1,
            "context_info": context_info
        }
    }

    # 执行LLM调用
    start_time = time.time()
    try:
        openai_client = get_openai_client()
        if openai_client is None:
            raise ValueError("OpenAI客户端不可用")

        response = await openai_client.chat.completions.create(
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt},
            ],
            model=settings.llm.default_model,
            temperature=0.1,
        )

        end_time = time.time()

        # 记录LLM响应数据
        llm_output_data = {
            "llm_response": {
                "response_content": response.choices[0].message.content,
                "response_time_ms": int((end_time - start_time) * 1000),
                "token_usage": response.usage.dict() if response.usage else None,
                "model_used": response.model,
                "finish_reason": response.choices[0].finish_reason
            }
        }

        # 解析响应内容
        content = response.choices[0].message.content
        parsed_data = json.loads(content)
        if isinstance(parsed_data, list):
            parsed_intents = [ParsedIntent(**item) for item in parsed_data]
        else:
            parsed_intents = [ParsedIntent(**parsed_data)]

        state.parsed_intents = parsed_intents
        state.log.append(f"LLM解析成功，返回 {len(parsed_intents)} 个意图")

        # 将LLM数据添加到状态中，供追踪装饰器使用
        if not hasattr(state, '_trace_data'):
            state._trace_data = {}
        state._trace_data['llm_output'] = llm_output_data

    except Exception as e:
        end_time = time.time()
        error_data = {
            "error_type": type(e).__name__,
            "error_message": str(e),
            "response_time_ms": int((end_time - start_time) * 1000),
            "llm_config": llm_input_data["prompt_data"]
        }

        if not hasattr(state, '_trace_data'):
            state._trace_data = {}
        state._trace_data['llm_error'] = error_data

        # 回退到模拟模式
        state.parsed_intents = await _mock_parse_intents(state.raw_input)
        state.log.append(f"LLM解析失败，使用模拟模式: {str(e)}")

    return state
```

### 3.3 错误处理增强

#### 增强错误数据记录

```python
async def complete_trace_with_enhanced_error(
    self,
    db: AsyncSession,
    trace_id: uuid.UUID,
    error: Exception,
    context: Dict[str, Any] = None
) -> bool:
    """增强版错误追踪完成"""

    # 分析错误类型和可恢复性
    error_analysis = self._analyze_error(error, context)

    error_data = {
        "error_type": type(error).__name__,
        "error_message": str(error),
        "error_category": error_analysis["category"],
        "is_retryable": error_analysis["is_retryable"],
        "recovery_suggestion": error_analysis["recovery_suggestion"],
        "error_context": {
            "node_state": context.get("node_state", {}) if context else {},
            "retry_count": context.get("retry_count", 0) if context else 0,
            "input_summary": context.get("input_summary", {}) if context else {},
            "execution_environment": {
                "simulation_mode": getattr(settings.trading, "simulation_mode", True),
                "llm_provider": getattr(settings.llm, "default_provider", "unknown")
            }
        },
        "stack_trace": traceback.format_exc() if context and context.get("include_stack_trace") else None
    }

    return await self.complete_trace(
        db=db,
        trace_id=trace_id,
        status="failed",
        error_data=error_data
    )

def _analyze_error(self, error: Exception, context: Dict[str, Any] = None) -> Dict[str, Any]:
    """分析错误类型和可恢复性"""
    error_type = type(error).__name__
    error_message = str(error)

    # 网络相关错误
    if "timeout" in error_message.lower() or "connection" in error_message.lower():
        return {
            "category": "network",
            "is_retryable": True,
            "recovery_suggestion": "网络连接问题，系统将自动重试"
        }

    # LLM相关错误
    elif "openai" in error_message.lower() or "api" in error_message.lower():
        return {
            "category": "llm",
            "is_retryable": True,
            "recovery_suggestion": "LLM服务暂时不可用，将使用模拟模式或重试"
        }

    # 交易所相关错误
    elif "insufficient" in error_message.lower() or "balance" in error_message.lower():
        return {
            "category": "exchange",
            "is_retryable": False,
            "recovery_suggestion": "余额不足，请检查账户资金或调整交易数量"
        }

    # 数据验证错误
    elif "validation" in error_message.lower() or isinstance(error, ValueError):
        return {
            "category": "validation",
            "is_retryable": False,
            "recovery_suggestion": "数据格式错误，请检查输入参数"
        }

    # 默认处理
    else:
        return {
            "category": "unknown",
            "is_retryable": False,
            "recovery_suggestion": "未知错误，请联系技术支持"
        }
```

## 4. 数据查询和分析

### 4.1 业务查询接口

基于增强的追踪数据，提供以下查询能力：

```python
class AgentTraceQueryService:
    """Agent追踪数据查询服务"""

    async def get_signal_processing_flow(
        self,
        db: AsyncSession,
        task_id: uuid.UUID
    ) -> Dict[str, Any]:
        """获取完整的信号处理流程"""

        traces = await self.get_task_traces(db, task_id)

        flow_data = {
            "task_id": str(task_id),
            "total_nodes": len(traces),
            "processing_timeline": [],
            "llm_interactions": [],
            "trade_executions": [],
            "errors": []
        }

        for trace in traces:
            # 处理时间线
            flow_data["processing_timeline"].append({
                "node_name": trace.node_name,
                "started_at": trace.started_at,
                "completed_at": trace.completed_at,
                "duration_ms": trace.duration_ms,
                "status": trace.status
            })

            # LLM交互记录
            if trace.node_name == "Parse" and trace.output_data:
                llm_data = trace.output_data.get("node_specific", {}).get("llm_response")
                if llm_data:
                    flow_data["llm_interactions"].append({
                        "node_name": trace.node_name,
                        "model_used": llm_data.get("model_used"),
                        "response_time_ms": llm_data.get("response_time_ms"),
                        "token_usage": llm_data.get("token_usage"),
                        "success": trace.status == "completed"
                    })

            # 交易执行记录
            if trace.node_name == "Execute" and trace.output_data:
                exec_data = trace.output_data.get("node_specific", {})
                if exec_data:
                    flow_data["trade_executions"].append({
                        "execution_results": exec_data.get("execution_results", []),
                        "execution_summary": exec_data.get("execution_summary", {}),
                        "success": trace.status == "completed"
                    })

            # 错误记录
            if trace.status == "failed" and trace.error_data:
                flow_data["errors"].append({
                    "node_name": trace.node_name,
                    "error_type": trace.error_data.get("error_type"),
                    "error_message": trace.error_data.get("error_message"),
                    "is_retryable": trace.error_data.get("is_retryable"),
                    "recovery_suggestion": trace.error_data.get("recovery_suggestion")
                })

        return flow_data

    async def get_llm_performance_metrics(
        self,
        db: AsyncSession,
        user_id: uuid.UUID,
        time_range: Optional[Tuple[datetime, datetime]] = None
    ) -> Dict[str, Any]:
        """获取LLM性能指标"""

        query = select(AgentExecutionTrace).where(
            and_(
                AgentExecutionTrace.user_id == user_id,
                AgentExecutionTrace.node_name == "Parse"
            )
        )

        if time_range:
            query = query.where(
                AgentExecutionTrace.created_at.between(time_range[0], time_range[1])
            )

        result = await db.execute(query)
        traces = result.scalars().all()

        metrics = {
            "total_llm_calls": len(traces),
            "success_rate": 0,
            "average_response_time_ms": 0,
            "total_tokens_used": 0,
            "model_distribution": {},
            "error_distribution": {}
        }

        if not traces:
            return metrics

        successful_calls = 0
        total_response_time = 0
        total_tokens = 0

        for trace in traces:
            if trace.status == "completed":
                successful_calls += 1

                if trace.output_data and "node_specific" in trace.output_data:
                    llm_data = trace.output_data["node_specific"].get("llm_response", {})

                    # 响应时间
                    response_time = llm_data.get("response_time_ms", 0)
                    total_response_time += response_time

                    # Token使用量
                    token_usage = llm_data.get("token_usage", {})
                    if token_usage:
                        total_tokens += token_usage.get("total_tokens", 0)

                    # 模型分布
                    model = llm_data.get("model_used", "unknown")
                    metrics["model_distribution"][model] = metrics["model_distribution"].get(model, 0) + 1

            elif trace.status == "failed" and trace.error_data:
                error_type = trace.error_data.get("error_type", "unknown")
                metrics["error_distribution"][error_type] = metrics["error_distribution"].get(error_type, 0) + 1

        metrics["success_rate"] = successful_calls / len(traces)
        metrics["average_response_time_ms"] = total_response_time / max(successful_calls, 1)
        metrics["total_tokens_used"] = total_tokens

        return metrics
```

## 5. 实施计划

### 5.1 第一阶段（高优先级）

**目标**：实现核心业务数据的完整记录

**时间**：1-2周

**任务**：
1. 修改`_extract_input_data`和`_extract_output_data`函数
2. 增强Parse节点的LLM调用追踪
3. 增强Execute节点的订单执行追踪
4. 基础错误处理增强

**验收标准**：
- 能够完整记录LLM的prompt、响应和token使用
- 能够详细记录订单执行的价格、数量、手续费
- 错误信息包含详细的上下文和恢复建议

### 5.2 第二阶段（中优先级）

**目标**：完善其他节点的数据记录

**时间**：1周

**任务**：
1. Context节点市场数据追踪
2. Plan节点决策过程追踪
3. 查询接口开发

**验收标准**：
- 所有节点都有详细的业务数据记录
- 提供完整的信号处理流程查询接口

### 5.3 第三阶段（低优先级）

**目标**：系统优化和分析工具

**时间**：1-2周

**任务**：
1. 性能指标优化
2. 历史数据分析工具
3. 监控和告警功能

## 6. 兼容性保证

### 6.1 向后兼容

- 所有修改都基于现有的`agent_execution_traces`表结构
- 不改变现有API接口
- 保持与现有系统架构的完全兼容

### 6.2 渐进式升级

- 支持新旧数据格式并存
- 可以逐步迁移现有数据
- 不影响系统的正常运行

### 6.3 性能考虑

- 利用现有的GIN索引进行JSONB查询优化
- 异步写入避免影响主流程性能
- 合理的数据清理策略防止存储膨胀

## 7. 总结

本优化方案基于现有系统架构，通过增强JSONB字段的数据结构，实现了信号处理全流程的业务数据完整记录。方案遵循项目的核心设计原则，避免过度设计，专注于实用性和可维护性。

**核心价值**：
- **完整的业务流程可视性**：从信号输入到订单执行的全程追踪
- **精确的问题定位能力**：详细的错误上下文和恢复建议
- **数据驱动的优化决策**：基于真实数据的系统优化
- **更高的系统可靠性**：通过完整的追踪数据提升调试效率

实施后，开发人员将能够完全理解信号处理和订单生成的完整流程，为系统优化和问题解决提供强有力的数据支持。
```
