# 1. 视图与组件设计 (Vue.js 3 + Vuetify 3 + TypeScript)

### 1.1 前端架构与状态管理

- **架构**: 采用基于 Vue 3 (Composition API) 的现代化组件架构，使用 **TypeScript** 提供类型安全，**Vue Router** 进行页面路由管理。
- **UI 框架**: **Vuetify 3**，提供一套美观、开箱即用的 Material Design 组件库。
- **类型系统**: **TypeScript** 全面覆盖，提供完整的类型定义和接口规范。
- **状态管理**: **Pinia** (TypeScript版本)。核心 Store 包括：
  - `authStore`: 管理用户认证状态、安全Token存储、用户信息管理。
  - `orderStore`: 存储和管理所有订单数据，支持实时更新和虚拟滚动。
  - `signalStore`: 管理信号数据，包括信号列表、筛选状态、AI解析结果和实时更新。
  - `configStore`: 管理用户的交易所和风控配置。
  - `uiStore`: 管理全局 UI 状态，包括通知系统、加载状态、主题切换。
  - `websocketStore`: 封装 WebSocket 连接、消息认证、心跳和重连逻辑。
  - `agentStore`: 管理 AI Agent 的状态、任务执行和待处理动作。

#### **1.1.1 状态管理数据流架构**

```mermaid
flowchart TD
    subgraph "WebSocket Layer"
        WS[WebSocket Connection]
    end

    subgraph "Store Layer"
        AS[authStore]
        OS[orderStore]
        SS[signalStore]
        CS[configStore]
        US[uiStore]
        WSS[websocketStore]
        AGS[agentStore]
    end

    subgraph "Component Layer"
        DV[DashboardView]
        SV[SignalsView]
        OV[OrdersView]
        CV[ConditionalOrdersView]
        CFV[ConfigsView]
        OW[OnboardingWizard]
    end

    subgraph "UI Components"
        SC[StatsCards]
        PAL[PendingActionsList]
        LLS[LiveLogStream]
        ROT[RecentOrdersTable]
        ODT[OrdersDataTable]
        SF[SignalFilters]
        SL[SignalsList]
        SC2[SignalCard]
        DM[DiscordMessage]
        TM[TelegramMessage]
        GM[GenericMessage]
        SD[SignalDetails]
        COT[ConditionalOrdersTable]
        VST[VirtualScrollTable]
        EL[EnhancedLoading]
        SSL[SimpleSkeletonLoader]
    end

    WS --> WSS
    WSS --> |ORDER_UPDATE| OS
    WSS --> |NOTIFICATION| US
    WSS --> |AGENT_STATE_TRANSITION| AGS
    WSS --> |PENDING_ACTION_REQUIRED| AGS

    AS --> DV
    OS --> DV
    OS --> OV
    CS --> CV
    CS --> CFV
    US --> DV
    US --> OV
    AGS --> DV

    DV --> SC
    DV --> PAL
    DV --> LLS
    DV --> ROT

    OV --> ODT
    CV --> COT
```

#### **1.1.2 核心 Store 详细职责**

**authStore (认证状态管理)**:

- 状态: `user: User | null`, `token: string | null`, `isLoading: boolean`, `error: string | null`
- 方法: `login()`, `register()`, `logout()`, `initializeUser()`, `getAuthHeaders()`, `refreshToken()`, `shouldRefreshToken()`
- 计算属性: `isAuthenticated`, `isFirstTimeLogin`
- 安全特性: 使用SecureStorage进行token加密存储，支持sessionStorage和token过期检测

**orderStore (订单数据管理)**:

- 状态: `orders`, `loading`, `error`, `filters`
- 方法: `fetchOrders()`, `closePosition()`, `closeAllPositions()`, `updateOrder()`, `addOrder()`
- 计算属性: `filteredOrders`, `activeOrders`, `totalPnL`, `activeOrdersCount`

**configStore (配置管理)**:

- 状态: `exchangeConfigs`, `riskConfig`, `configs`, `loading`, `error`
- 方法: `fetchConfigs()`, `updateExchangeConfig()`, `updateRiskConfig()`, `validateThresholds()`
- 计算属性: `hasExchangeConfig`, `hasRiskConfig`, `isThresholdValid`
- 验证逻辑: 确保 `auto_approve_threshold >= confidence_threshold`，提供实时验证反馈

**uiStore (UI 状态管理)**:

- 状态: `notifications`, `isLoading`, `loadingMessage`, `showOnboardingWizard`, `sidebarOpen`, `theme`
- 方法: `addNotification()`, `removeNotification()`, `setLoading()`, `toggleTheme()`
- 计算属性: `hasNotifications`, `unreadNotifications`

**websocketStore (WebSocket 连接管理)**:

- 状态: `socket: WebSocket | null`, `isConnected: boolean`, `reconnectAttempts: number`, `heartbeatInterval: NodeJS.Timeout | null`
- 方法: `connect()`, `disconnect()`, `sendMessage()`, `handleMessage()`, `handleWebSocketMessage()`
- 安全特性: 基于消息的认证机制，移除URL中的token暴露
- 消息处理: 统一的WebSocket消息处理器，支持类型安全的消息分发
- 自动重连机制和心跳检测，指数退避算法

**agentStore (AI Agent 状态管理)**:

- 状态: `isConnected`, `currentTask`, `agentState`, `pendingActions`, `logs`
- 方法: `handlePendingActionRequired()`, `respondToAction()`, `clearPendingAction()`
- 计算属性: `isProcessing`, `activePendingActions`

### 1.2 视图与组件设计

需确保 `PendingActionsList` 能处理因缺少 `side` 而发起的确认请求。

#### **1.2.0 前端组件架构总览**

前端采用基于 Vue 3 Composition API 的现代化组件架构，通过清晰的职责分离和模块化设计，确保代码的可维护性和可扩展性。

```mermaid
graph TD
    A[App.vue] --> B[DashboardView]
    A --> C[SignalsView]
    A --> D[OrdersView]
    A --> E[ConditionalOrdersView]
    A --> F[ConfigsView]
    A --> G[OnboardingWizard]

    B --> H[StatsCards]
    B --> I[PendingActionsList]
    B --> J[LiveLogStream]

    C --> K[SignalFilters]
    C --> L[SignalsList]
    L --> M[SignalCard]
    M --> N[DiscordMessage]
    M --> O[TelegramMessage]
    M --> P[GenericMessage]
    C --> Q[SignalDetails]
    B --> J[RecentOrdersTable]

    C --> K[OrdersDataTable]
    C --> L[StatusFilterChips]
    C --> M[ConfirmCancelDialog]

    D --> N[ConditionalOrdersTable]
    D --> O[CreateConditionalOrderDialog]
    D --> P[ConditionalOrderDetailsDialog]

    E --> Q[ExchangeConfigForm]
    E --> R[RiskConfigForm]

    F --> S[WizardSteps]

    subgraph "Pinia Stores"
        T[authStore]
        U[orderStore]
        V[configStore]
        W[uiStore]
        X[websocketStore]
        Y[agentStore]
    end

    B -.-> T
    B -.-> U
    B -.-> W
    B -.-> X
    B -.-> Y

    C -.-> U
    C -.-> W

    D -.-> U
    D -.-> V

    E -.-> V
    E -.-> T
```

#### **1.2.1 `DashboardView` (仪表盘)**

- **布局**: 采用栅格系统（`v-row`, `v-col`）布局，响应式设计。
- **核心组件**:
  - **`StatsCards`**: 独立组件，一行展示多个 `v-card`，分别显示总浮动盈亏 (PnL)、活跃订单数、24 小时收益率等关键指标。实时从 `orderStore` 计算统计数据。
  - **`PendingActionsList`**: **关键交互组件**。以列表形式（`v-list`）展示所有待确认动作。每一项都是一个 `v-card`，包含：
    - **AI 的提问**: 清晰展示 `details.clarification_needed` 的内容 (例如 "无法确定 'btc 68000' 的交易方向，请选择：")。
    - **原始指令**: 显示触发此动作的原始文本。
    - **解析结果展示**: 显示AI解析出的交易对、方向、金额、置信度等信息，使用进度条显示置信度。
    - **风险评估**: 如果有风险警告，显示风险级别和原因。
    - **智能操作按钮**:
      - 对于方向确认：提供【做多】和【做空】按钮，带有方向图标
      - 对于数量确认：提供输入框或滑块让用户直接修正
      - 对于一般确认：提供【批准】/【拒绝】按钮
    - **紧急取消**: 提供"紧急取消所有待处理动作"按钮，带有脉冲动画效果
    - **响应处理**: 通过 `agentStore.respondToAction()` 处理用户响应，支持批量操作。
  - **`LiveLogStream`**: 独立组件，一个类似终端的只读文本区域，实时显示 `AGENT_STATE_TRANSITION` 消息，为用户提供系统正在"思考"的透明度。功能包括：
    - 每条日志前缀时间戳和任务 ID
    - 支持自动滚动和手动滚动锁定
    - 日志级别过滤（INFO、WARNING、ERROR）
    - 任务ID过滤，可只显示特定任务的日志
    - 日志搜索功能
    - 导出日志功能
  - **`RecentOrdersTable`**: 独立组件，一个简化的数据表格，只显示最近 5 条订单的状态变更。功能包括：
    - 订单状态实时更新（通过WebSocket）
    - 盈亏颜色标识（绿色盈利，红色亏损）
    - 快速操作按钮（查看详情、关闭订单）
    - 订单状态图标和进度指示
  - **紧急控制区**: 包含紧急操作按钮，具有醒目的警告样式：
    - "暂停所有 Agent"：停止所有正在执行的任务
    - "一键清仓所有头寸"：关闭所有活跃订单
    - "紧急断开交易所连接"：断开所有交易所连接
    - 所有紧急操作都需要二次确认，并记录操作日志

#### **1.2.2 `SignalsView` (信号管理页)**

- **布局**: 页面主体为信号列表和筛选器，支持多种视图模式（列表视图、卡片视图）。
- **核心组件**:
  - **`SignalFilters`**: 独立组件，提供多维度筛选功能:
    - **平台筛选**: Discord、Telegram、手动输入的快速筛选按钮
    - **AI解析状态**: 待解析、成功、失败、部分成功的状态筛选
    - **消息类型**: 交易信号、市场分析、价格提醒、普通消息的类型筛选
    - **LLM服务**: DeepSeek、Gemini、ChatGPT、Claude的服务筛选
    - **置信度范围**: 滑块控件设置置信度范围筛选
    - **日期范围**: 日期选择器设置时间范围筛选
    - **快速筛选**: 高置信度、交易信号、解析失败等预设筛选选项
  - **`SignalsList`**: 独立组件，信号列表容器:
    - **视图切换**: 支持列表视图和卡片视图切换
    - **虚拟滚动**: 使用虚拟滚动优化大量数据的渲染性能
    - **实时更新**: 通过WebSocket接收新信号并实时更新列表
    - **批量操作**: 支持批量标记为已处理、批量删除等操作
  - **`SignalCard`**: 独立组件，单个信号的卡片展示:
    - **平台标识**: 显示信号来源平台的图标和名称
    - **置信度指示**: 进度条和百分比显示AI解析置信度
    - **状态标签**: 彩色标签显示AI解析状态和消息类型
    - **LLM服务标识**: 显示使用的LLM服务图标
    - **内容预览**: 显示信号内容的摘要，支持展开查看完整内容
    - **操作按钮**: 查看详情、标记已处理、执行交易等快速操作
  - **平台特定渲染组件**:
    - **`DiscordMessage`**: Discord消息的原生样式渲染组件:
      - 支持Discord原生样式和原始格式两种渲染模式
      - 显示用户头像、用户名、时间戳
      - 渲染Discord Embeds、附件、反应等特殊元素
      - 支持回复消息的层级显示
    - **`TelegramMessage`**: Telegram消息的渲染组件:
      - 支持Telegram特有的消息格式和样式
      - 显示转发信息、媒体附件、文本格式
      - 支持频道和群组消息的不同展示
    - **`GenericMessage`**: 通用消息渲染组件:
      - 用于手动输入和其他平台的消息显示
      - 提供统一的消息展示格式
  - **`SignalDetails`**: 独立组件，信号详情对话框:
    - **完整信息展示**: 显示信号的所有字段信息
    - **AI解析结果**: 展示AI解析的订单数据、置信度分解等
    - **原始内容对比**: 并排显示原始内容和处理后内容
    - **元数据查看**: 展示平台特定的元数据信息
    - **操作历史**: 显示信号的处理历史和状态变更记录
    - **执行交易**: 提供将信号转换为实际订单的功能

#### **1.2.3 `OrdersView` (订单管理页)**

- **布局**: 页面主体为一个功能完整的数据表格，包含筛选器和操作区域。
- **核心组件**:
  - **筛选器区域**:
    - **`StatusFilterChips`**: 独立组件，提供状态快速筛选（活跃、已关闭、失败、已取消、待处理）。
    - **高级筛选**: 包含交易对筛选、方向筛选（做多/做空）、日期范围选择器。
    - **搜索框**: 支持按交易对名称快速搜索。
  - **`OrdersDataTable` (内嵌 `v-data-table`)**:
    - **列**: 交易对、方向（带颜色标识）、开仓价、当前价（带涨跌标识）、数量（含 USD 价值）、浮动盈亏(PnL)（带颜色标识）、状态、创建/关闭时间。
    - **功能**: 支持按列排序、分页显示、行选择、批量操作。
    - **交互**: 点击行可查看订单详情，支持快速关闭操作。
  - **`ConfirmCancelDialog`**: 独立组件，用于确认关闭订单操作，包含风险提示。
  - **订单详情对话框**: 展示完整的订单信息，包括 Agent 执行日志、价格历史图表等。

#### **1.2.4 `ConditionalOrdersView` (条件订单管理页)**

- **布局**: 页面主体由条件订单列表和创建条件订单功能组成，采用卡片式布局。
- **核心组件**:
  - **`ConditionalOrdersTable`**: 独立组件，条件订单数据表 (`v-data-table`):
    - **列**: 状态（带颜色标识）、交易对、触发条件（价格/时间）、行动计划（方向+金额）、创建时间、操作按钮。
    - **功能**: 支持按状态（等待触发、已触发、已取消、已过期）筛选，按列排序，分页显示。
    - **交互**: 支持快速取消、查看详情、编辑等操作。
  - **`CreateConditionalOrderDialog`**: 独立组件，创建条件订单对话框:
    - **触发条件部分**: 包含交易对选择、价格条件（大于/小于等）、目标价格等字段。
    - **行动计划部分**: 包含交易方向（做多/做空）、订单类型（市价单/限价单）、金额等字段。
    - **表单验证**: 对输入的所有参数进行实时验证，保证创建的条件订单有效。
    - **预览功能**: 在提交前显示条件订单的完整配置预览。
  - **`ConditionalOrderDetailsDialog`**: 独立组件，详情查看对话框:
    - 展示条件订单的完整信息，包括触发条件、行动计划、创建时间等。
    - 对于已触发的订单，提供查看关联实际订单的功能。
    - 包含执行历史和状态变更日志。

#### **1.2.5 `ConfigsView` (配置管理页)**

- **布局**: 使用选项卡（`v-tabs`）区分不同类型的配置，每个选项卡采用卡片式布局。
- **核心组件**:
  - **`ExchangeConfigForm`**: 独立组件表单，允许用户添加/编辑/删除交易所配置:
    - **字段**: 交易所名称、API Key、API Secret（密码类型）、沙盒模式开关。
    - **安全性**: API Secret 字段只在新增时可写，编辑时显示为掩码。
    - **验证**: 实时验证 API 凭证的有效性，显示连接状态。
    - **测试功能**: 提供"测试连接"按钮验证配置正确性。
  - **`RiskConfigForm`**: 独立组件表单，包含多个输入框用于设置风控参数:
    - **字段**: 最大并发订单数、最大总持仓价值、默认/最大单笔订单金额、允许交易币种、AI 置信度阈值、AI 自动批准阈值。
    - **智能提示**: 为每个参数提供建议值和风险说明。
    - **实时预览**: 显示当前配置下的风控限制效果。
    - **阈值验证**: 确保自动批准阈值大于等于置信度阈值，提供实时验证反馈。

#### **1.2.5 `OnboardingWizard` (首次设置向导)**

- **形式**: 独立组件，一个多步骤的全屏模态框（`v-dialog`），新用户首次登录后自动触发。
- **步骤设计**:
  1. **欢迎页**: 介绍系统功能和设置流程。
  2. **连接信号源**: Discord Bot 配置和权限设置。
  3. **配置交易所**: 引导用户添加交易所 API 配置。
  4. **设置基础风控**: 配置初始风控参数，提供保守/平衡/激进三种预设。包括AI置信度阈值和自动批准阈值的设置，确保用户理解分层决策机制。
  5. **完成设置**: 总结配置信息，提供快速入门指南。
- **交互特性**:
  - 支持步骤间前进/后退导航。
  - 每步都有输入验证和进度指示。
  - 可跳过非必需步骤，后续可在设置页面完善。

#### **1.2.6 新增性能优化组件**

**`VirtualScrollTable` (虚拟滚动表格)**:

- **功能**: 为大数据集提供高性能渲染的虚拟滚动表格组件。
- **特性**:
  - 支持自定义行高和容器高度
  - 智能计算可见区域，只渲染必要的DOM元素
  - 内置滚动指示器显示当前位置
  - 支持所有Vuetify DataTable的插槽和功能
  - 响应式设计，适配移动端
- **性能**: 可处理10万+行数据而不影响页面性能
- **使用场景**: 订单历史、交易记录、日志查看等大数据表格

**`EnhancedLoading` (增强加载组件)**:

- **功能**: 提供多种加载状态和用户反馈的统一加载组件。
- **加载类型**:
  - 骨架屏模式：模拟内容结构的加载动画
  - 卡片骨架屏：适用于卡片列表的加载状态
  - 表格骨架屏：适用于数据表格的加载状态
  - 传统指示器：圆形、线性、点状、脉冲动画
- **高级特性**:
  - 超时检测和重试机制
  - 进度显示和状态消息
  - 全屏和局部加载模式
  - 深色主题支持
- **用户体验**: 减少感知加载时间，提供更好的视觉反馈

**`SimpleSkeletonLoader` (简单骨架屏)**:

- **功能**: 轻量级骨架屏组件，用于快速实现加载状态。
- **特性**:
  - 可配置行数和动画效果
  - 随机宽度模拟真实内容
  - 支持插槽，加载完成后显示实际内容
  - CSS动画优化，性能友好

#### **1.2.7 系统级增强组件**

**WebSocket消息处理系统**:

- **架构**: 统一的消息处理器 (`WebSocketMessageHandler`)
- **功能**:
  - 类型安全的消息分发和处理
  - 支持多种消息类型：订单更新、Agent状态、通知、心跳
  - 自动错误处理和恢复机制
  - 消息优先级和批处理支持
- **安全性**: 基于消息的认证，避免token在URL中暴露

**全局错误处理系统**:

- **架构**: 统一的错误处理器 (`ErrorHandler`)
- **错误类型**:
  - API错误：网络请求失败、服务器错误
  - 网络错误：连接超时、断网处理
  - WebSocket错误：连接中断、消息错误
  - 认证错误：token过期、权限不足
  - 表单验证错误：输入验证失败
- **功能**:
  - 用户友好的错误消息转换
  - 错误日志记录和上报
  - 自动错误恢复和重试机制
  - 开发环境详细错误信息

#### **1.2.8 组件实现特色总结**

**模块化设计原则**:

- 每个功能组件都是独立的 Vue 组件，具有清晰的 props 接口和事件发射。
- 组件间通过 Pinia store 进行状态共享，避免 prop drilling。
- 所有组件都支持响应式设计，适配桌面和移动端。

**用户体验优化**:

- **实时数据更新**: 通过 WebSocket 实现订单状态、价格变动的实时更新。
- **加载状态管理**: 所有异步操作都有相应的加载指示器。
- **错误处理**: 统一的错误提示和恢复机制。
- **键盘快捷键**: 支持常用操作的快捷键（如 Ctrl+R 刷新订单）。

**性能优化**:

- **虚拟滚动**: 大数据表格使用 `VirtualScrollTable` 组件，支持10万+行数据高性能渲染。
- **代码分割**: 使用 Vite 的动态导入和手动chunk分割，减少初始包大小。
- **懒加载**: 路由级别的懒加载，非关键组件按需加载。
- **智能缓存**:
  - 页面级缓存系统 (`PageCache`)，支持TTL和版本控制
  - API响应缓存，减少重复请求
  - 静态资源缓存优化
- **Bundle优化**:
  - Vendor chunk分离 (Vue、Vuetify、工具库)
  - Tree-shaking优化，移除未使用代码
  - 压缩和Gzip优化
- **渲染优化**:
  - 骨架屏减少感知加载时间
  - 虚拟DOM优化和组件懒渲染
  - CSS-in-JS优化和样式缓存

**颜色规范**：

```scss
// 主色调
$primary: #1976D2;     // 蓝色 - 主要操作
$secondary: #424242;   // 灰色 - 次要信息
$accent: #82B1FF;      // 浅蓝 - 强调元素

// 语义色彩
$success: #4CAF50;     // 绿色 - 成功/盈利
$warning: #FF9800;     // 橙色 - 警告
$error: #F44336;       // 红色 - 错误/亏损
$info: #2196F3;        // 蓝色 - 信息

// 交易专用色彩
$profit: #00C853;      // 盈利绿
$loss: #D32F2F;        // 亏损红
$buy: #4CAF50;         // 买入绿
$sell: #F44336;        // 卖出红
```

**字体规范**：

```scss
// 字体族
$font-family-base: 'Roboto', 'Noto Sans SC', sans-serif;
$font-family-mono: 'Roboto Mono', 'Consolas', monospace;

// 字体大小
$font-size-xs: 0.75rem;   // 12px
$font-size-sm: 0.875rem;  // 14px
$font-size-base: 1rem;    // 16px
$font-size-lg: 1.125rem;  // 18px
$font-size-xl: 1.25rem;   // 20px
```

**响应式设计**：

```scss
// 断点定义
$breakpoints: (
        'xs': 0,
        'sm': 600px,
        'md': 960px,
        'lg': 1264px,
        'xl': 1904px
);
```

**布局适配策略**：

- **移动端 (xs, sm)**：单列布局，简化操作界面
- **平板端 (md)**：两列布局，保持核心功能
- **桌面端 (lg, xl)**：多列布局，展示完整信息

### 1.3 用户引导的后端逻辑

- **新用户初始化**: 当一个新用户注册成功后，或首次完成 `OnboardingWizard` 时，后端业务逻辑**必须**为其在 `RISK_CONFIGS` 表中创建一条记录，并填入系统预设的、安全的默认值（例如，`max_concurrent_orders = 5`, `default_position_size_usd = 100`, `confidence_threshold = 0.80`, `auto_approve_threshold = 0.95` 等）。这确保了即使用户跳过配置，风控系统依然能正常工作。

# 2. 技术栈与工程化

### 2.1 前端技术栈

|                 |               |                                              |
| --------------- | ------------- | -------------------------------------------- |
| **领域**        | **技术/框架** | **备注**                                     |
| **前端框架**    | Vue.js 3      | 现代化前端框架，使用 Composition API。       |
| **UI 组件库**   | Vuetify 3     | Material Design 组件库，提供丰富的 UI 组件。 |
| **类型系统**    | TypeScript    | 100%覆盖，类型安全的 JavaScript 超集。      |
| **状态管理**    | Pinia         | Vue 3 官方推荐的状态管理库，TypeScript版本。|
| **路由管理**    | Vue Router    | Vue.js 官方路由管理器，支持懒加载。          |
| **HTTP 客户端** | 自定义API客户端| 统一的HTTP客户端，支持错误处理和拦截器。     |
| **构建工具**    | Vite          | 快速的前端构建工具，支持代码分割和优化。     |
| **测试框架**    | Vitest        | 快速的单元测试框架，与Vite深度集成。         |
| **PWA支持**     | Vite-PWA      | 渐进式Web应用支持，离线缓存和安装提示。      |
| **无障碍**      | WCAG 2.1      | 完整的无障碍功能支持，键盘导航和屏幕阅读器。 |

### 2.2 项目目录结构

这是一个“Monorepo”风格的结构，将前端、后端和部署配置清晰地分离在同一个代码仓库中，便于统一管理。

```
/ai-crypto-trading-agent/
|
└── 📁 frontend/                   # 【前端】Vue.js 3 应用
    ├── 📁 public/
    ├── 📁 src/
    │   ├── 📁 api/                   # 封装对后端 RESTful API 的调用
    │   ├── 📁 assets/                # 静态资源 (CSS, images)
    │   ├── 📁 components/            # 可复用的 Vue 组件
    │   │   ├── 📄 ConditionalOrderDetailsDialog.vue
    │   │   ├── 📄 ConditionalOrdersTable.vue
    │   │   ├── 📄 ConfirmCancelDialog.vue
    │   │   ├── 📄 CreateConditionalOrderDialog.vue
    │   │   ├── 📄 DiscordMessage.vue           # Discord消息渲染组件
    │   │   ├── 📄 EnhancedLoading.vue          # 增强加载组件
    │   │   ├── 📄 GenericMessage.vue           # 通用消息渲染组件
    │   │   ├── 📄 LiveLogStream.vue
    │   │   ├── 📄 OnboardingWizard.vue
    │   │   ├── 📄 PendingActionsList.vue
    │   │   ├── 📄 RecentOrdersTable.vue
    │   │   ├── 📄 SignalCard.vue               # 信号卡片组件
    │   │   ├── 📄 SignalDetails.vue            # 信号详情对话框
    │   │   ├── 📄 SignalFilters.vue            # 信号筛选器组件
    │   │   ├── 📄 SignalsList.vue              # 信号列表组件
    │   │   ├── 📄 SimpleSkeletonLoader.vue     # 简单骨架屏
    │   │   ├── 📄 StatsCards.vue
    │   │   ├── 📄 StatusFilterChips.vue
    │   │   ├── 📄 TelegramMessage.vue          # Telegram消息渲染组件
    │   │   └── 📄 VirtualScrollTable.vue       # 虚拟滚动表格
    │   ├── 📁 composables/           # Vue 3 组合式函数
    │   │   └── 📄 useAccessibility.ts          # 无障碍功能组合式函数
    │   ├── 📁 router/                # Vue Router 路由配置
    │   ├── 📁 stores/                # Pinia 状态管理 (TypeScript版本)
    │   │   ├── 📄 auth.ts            # 认证状态管理
    │   │   ├── 📄 agent.ts           # AI Agent状态管理
    │   │   ├── 📄 config.ts          # 配置管理
    │   │   ├── 📄 order.ts           # 订单数据管理
    │   │   ├── 📄 signal.ts          # 信号数据管理
    │   │   ├── 📄 ui.ts              # UI状态管理
    │   │   ├── 📄 websocket.ts       # WebSocket连接管理
    │   │   └── 📄 index.js           # Store导出
    │   ├── 📁 styles/                # 样式文件
    │   │   └── 📄 accessibility.css  # 无障碍功能样式
    │   ├── 📁 types/                 # TypeScript类型定义
    │   │   └── 📄 index.ts           # 全局类型定义
    │   ├── 📁 utils/                 # 工具函数
    │   │   ├── 📄 errorHandler.ts    # 全局错误处理
    │   │   ├── 📄 pageCache.ts       # 页面缓存系统
    │   │   ├── 📄 secureStorage.ts   # 安全存储工具
    │   │   └── 📄 websocketHandler.ts # WebSocket消息处理
    │   ├── 📁 views/                 # 页面级组件
    │   │   ├── 📄 ConditionalOrdersView.vue
    │   │   ├── 📄 ConfigsView.vue
    │   │   ├── 📄 DashboardView.vue
    │   │   ├── 📄 LoginView.vue
    │   │   ├── 📄 NotFoundView.vue
    │   │   ├── 📄 OrdersView.vue
    │   │   └── 📄 SignalsView.vue
    │   ├── 📄 App.vue                # 根组件
    │   └── 📄 main.js                # Vue 应用入口
    ├── 📄 Dockerfile                 # 前端应用的 Dockerfile (基于 Nginx)
    ├── 📄 package.json               # Node.js 依赖与脚本
    └── 📄 vite.config.js             # Vite 配置文件
```

**架构设计释义**：

- 采用了业界主流的 Vue 3 架构模式，与设计文档中的前端设计完全对应。
- `views/` 对应页面，`components/` 对应可复用 UI 块，`stores/` 对应 Pinia 状态管理，职责非常清晰。

```mermaid
graph TD
    A[Vue.js 应用] --> B[路由管理 - Vue Router]
    A --> C[状态管理 - Pinia]
    A --> D[UI组件 - Vuetify]

    C --> C1[authStore - 用户认证]
    C --> C2[orderStore - 订单管理]
    C --> C3[configStore - 配置管理]
    C --> C4[agentStore - AI状态]
    C --> C5[websocketStore - 实时通信]

    D --> D1[DashboardView - 仪表盘]
    D --> D2[SignalsView - 信号管理]
    D --> D3[OrdersView - 订单管理]
    D --> D4[ConfigsView - 配置页面]
    D --> D5[ConditionalOrdersView - 条件订单]
```

### 2.3 TypeScript类型系统

项目采用100% TypeScript覆盖，提供完整的类型安全保障：

**核心类型定义** (`src/types/index.ts`):

```typescript
// 用户相关类型
export interface User {
  id: number
  username: string
  email: string
  is_first_time: boolean
}

// API响应类型
export interface ApiResponse<T = any> {
  data: T
  message?: string
  status: number
}

// WebSocket消息类型
export interface WebSocketMessage {
  event_type: 'ORDER_UPDATE' | 'SIGNAL_RECEIVED' | 'SIGNAL_PARSED' | 'SIGNAL_PROCESSED' | 'AGENT_STATE_TRANSITION' | 'NOTIFICATION' | 'HEARTBEAT'
  payload: any
  timestamp: string
}

// 信号相关类型
export enum AIParseStatus {
  PENDING = 'pending',
  SUCCESS = 'success',
  FAILED = 'failed',
  PARTIAL = 'partial'
}

export enum MessageTypeAI {
  NORMAL_MESSAGE = 'normal_message',
  TRADING_SIGNAL = 'trading_signal',
  MARKET_ANALYSIS = 'market_analysis',
  PRICE_ALERT = 'price_alert',
  AMBIGUOUS = 'ambiguous'
}

export enum LLMService {
  DEEPSEEK = 'deepseek',
  GEMINI = 'gemini',
  CHATGPT = 'chatgpt',
  CLAUDE = 'claude'
}

export interface Signal {
  id: string
  platform: 'discord' | 'telegram' | 'manual'
  platform_message_id?: string
  channel_id?: string
  channel_name?: string
  author_id?: string
  author_name?: string
  content: string
  raw_content?: string
  message_type: 'text' | 'embed' | 'attachment' | 'reply'
  metadata?: {
    discord?: any
    telegram?: any
    ai_analysis?: {
      order_data?: any
      parsed_content?: any
      analysis_metadata?: any
    }
  }
  confidence?: number
  ai_parse_status: AIParseStatus
  message_type_ai: MessageTypeAI
  llm_service?: LLMService
  is_processed: boolean
  processed_at?: string
  created_at: string
  updated_at: string
}

export interface SignalFilters {
  platform?: string
  ai_parse_status?: AIParseStatus
  message_type_ai?: MessageTypeAI
  llm_service?: LLMService
  confidence_min?: number
  confidence_max?: number
  date_from?: string
  date_to?: string
}

export interface SignalStats {
  total_signals: number
  processed_signals: number
  platform_breakdown: Record<string, number>
  avg_confidence?: number
  recent_activity: Array<Record<string, any>>
}

// 订单相关类型
export interface Order {
  id: string
  symbol: string
  side: 'buy' | 'sell'
  status: 'pending' | 'filled' | 'cancelled' | 'rejected'
  quantity: number
  price: number
  created_at: string
}

// 风控配置相关类型
export interface RiskConfig {
  id: number
  user_id: number
  max_concurrent_orders: number
  max_position_size_usd: number
  max_daily_loss_usd: number
  allowed_symbols: string[]
  confidence_threshold: number
  auto_approve_threshold: number
  created_at: string
  updated_at: string
}

export interface RiskConfigUpdate {
  max_concurrent_orders?: number
  max_position_size_usd?: number
  max_daily_loss_usd?: number
  allowed_symbols?: string[]
  confidence_threshold?: number
  auto_approve_threshold?: number
}

export interface RiskConfigFormData extends RiskConfigUpdate {
  // 表单特有的字段，如临时状态等
  _isValid?: boolean
  _errors?: Record<string, string>
}
```

**Store类型安全**:
- 所有store使用TypeScript重写，提供完整的类型注解
- 状态、方法和计算属性都有明确的类型定义
- 支持类型推导和IDE智能提示

### 2.4 PWA (渐进式Web应用) 支持

**Service Worker配置**:
- 自动更新策略，无需用户手动刷新
- 离线缓存支持，关键页面可离线访问
- API响应缓存，提升加载速度

**Web App Manifest**:
```json
{
  "name": "AI Crypto Trading Agent",
  "short_name": "CryptoAgent",
  "description": "AI-powered cryptocurrency trading platform",
  "theme_color": "#1976d2",
  "background_color": "#ffffff",
  "display": "standalone",
  "orientation": "portrait"
}
```

**PWA特性**:
- 可安装到设备主屏幕
- 原生应用般的用户体验
- 支持推送通知 (预留接口)
- 离线工作能力

### 2.5 无障碍功能 (WCAG 2.1 兼容)

**键盘导航支持**:
- 完整的Tab键导航
- 快捷键支持 (Alt+M跳转主内容, Alt+N跳转导航)
- 焦点管理和焦点陷阱

**屏幕阅读器支持**:
- 完整的ARIA标签和属性
- 语义化HTML结构
- 动态内容变化通知

**视觉辅助功能**:
- 高对比度模式 (Ctrl+Alt+H切换)
- 跳转链接 (Skip Links)
- 焦点指示器优化

**组合式函数** (`useAccessibility`):
```typescript
export function useAccessibility() {
  return {
    announceToScreenReader,    // 屏幕阅读器通知
    setFocus,                  // 焦点管理
    trapFocusInContainer,      // 焦点陷阱
    toggleHighContrast,        // 高对比度切换
    announcePageChange,        // 页面变化通知
    announceFormErrors         // 表单错误通知
  }
}
```

# 3. 实时通信与安全架构

### 3.1 安全的WebSocket连接

**消息认证机制**:
```typescript
// 安全的WebSocket连接 - 不在URL中暴露token
function connect() {
  // 1. 建立连接 (不包含敏感信息)
  const wsUrl = `${import.meta.env.VITE_WS_URL}/ws`
  socket.value = new WebSocket(wsUrl)

  // 2. 连接建立后发送认证消息
  socket.value.onopen = () => {
    const token = authStore.token
    if (token) {
      socket.value.send(JSON.stringify({
        type: 'auth',
        token: token
      }))
    }
  }
}
```

**统一消息处理**:
```typescript
// WebSocket消息处理器
export class WebSocketMessageHandler {
  handleMessage(message: WebSocketMessage): void {
    switch (message.event_type) {
      case 'ORDER_UPDATE':
        this.handleOrderUpdate(message.payload)
        break
      case 'AGENT_STATE_TRANSITION':
        this.handleAgentStateTransition(message.payload)
        break
      case 'NOTIFICATION':
        this.handleNotification(message.payload)
        break
    }
  }
}
```

### 3.2 客户端重连策略

```typescript
// TypeScript版本的WebSocket重连逻辑
interface WebSocketConfig {
  maxReconnectAttempts: number
  reconnectInterval: number
  maxReconnectInterval: number
}

class SecureWebSocketClient {
  private config: WebSocketConfig = {
    maxReconnectAttempts: 10,
    reconnectInterval: 1000,
    maxReconnectInterval: 30000
  }

  connect(): void {
    // 安全连接，不在URL中暴露token
    const wsUrl = this.getWebSocketUrl()
    this.ws = new WebSocket(wsUrl)

    this.ws.onopen = () => {
      this.sendAuthMessage()
      this.resetReconnectState()
      this.startHeartbeat()
    }

    this.ws.onclose = (event) => {
      if (event.code !== 1000) {
        this.scheduleReconnect()
      }
    }

    this.ws.onmessage = (event) => {
      this.handleMessage(JSON.parse(event.data))
    }
  }

  private sendAuthMessage(): void {
    const token = this.getAuthToken()
    if (token) {
      this.send({ type: 'auth', token })
    }
  }

  private scheduleReconnect(): void {
    if (this.reconnectAttempts < this.config.maxReconnectAttempts) {
      setTimeout(() => {
        this.reconnectAttempts++
        this.connect()
        // 指数退避策略
        this.config.reconnectInterval = Math.min(
          this.config.reconnectInterval * 2,
          this.config.maxReconnectInterval
        )
      }, this.config.reconnectInterval)
    }
  }
}
```

### 3.3 安全存储系统

**SecureStorage类**:
```typescript
export class SecureStorage {
  // 使用sessionStorage而不是localStorage
  // 提供基础加密和token过期检测

  static setToken(token: string): void {
    const encrypted = this.encrypt(token)
    sessionStorage.setItem(this.TOKEN_KEY, encrypted)
  }

  static getToken(): string | null {
    const encrypted = sessionStorage.getItem(this.TOKEN_KEY)
    return encrypted ? this.decrypt(encrypted) : null
  }

  static isTokenExpiringSoon(token: string): boolean {
    // JWT过期检测逻辑
    const payload = this.parseJWTPayload(token)
    const exp = payload.exp
    const now = Math.floor(Date.now() / 1000)
    return (exp - now) < 300 // 5分钟内过期
  }
}
```

## 🔒 类型安全架构

### 运行时类型验证

项目采用 **zod** 运行时验证库，在数据流的关键节点确保类型安全：

- **验证时机**: API响应接收时、WebSocket消息处理时、用户输入提交前
- **验证范围**: 所有业务数据类型（Order、RiskConfig、AgentState、OrderStats等）
- **错误处理**: 分层错误处理机制，开发环境提供详细错误信息，生产环境显示用户友好提示
- **性能影响**: 每次验证增加0.1-2ms开销，通过schema缓存优化性能

**架构价值**: 在运行时捕获前后端类型不匹配问题，避免界面崩溃和数据错误，提供100%的数据类型可靠性保障。

### 金融精度处理

采用 **decimal.js** 专业金融计算库，解决JavaScript number精度问题：

- **应用场景**: 所有金融数值计算（价格、数量、盈亏、百分比等）
- **数据传输**: 金融数值统一使用string类型从后端接收，避免精度丢失
- **计算工具**: 提供安全的数学运算函数（add、multiply、compare等）
- **显示格式**: 统一的金融数值格式化和货币显示标准

**架构价值**: 确保金融计算的精确性，避免因精度问题导致的资金损失，符合金融级应用的严格要求。

### API客户端增强

扩展原有API客户端，集成类型验证和错误处理机制：

- **验证函数**: `getWithValidation`、`postWithValidation` 等带验证的API调用函数
- **错误分类**: 区分网络错误、API错误、验证错误，提供针对性的处理策略
- **降级机制**: 验证失败时的备用方案，确保系统可用性
- **监控上报**: 生产环境的错误监控和自动上报机制

**架构价值**: 在API层面建立类型安全防线，提供统一的错误处理体验，增强系统的健壮性和可维护性。
