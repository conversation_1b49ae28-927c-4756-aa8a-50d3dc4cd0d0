# **AI Agent 驱动的加密货币智能跟单系统 - 需求规格说明书**

## 1. 项目概述

### 1.1 项目背景与核心问题

在 Discord、Telegram 等社交平台中，普通投资者依赖人工方式跟踪专业交易员发布的交易信号，此过程存在四大核心痛点：**高延迟性**导致错失良机、**操作繁琐**易引发失误、**无法全天候跟踪** 以及交易员指令**语言高度非标准化**，难以通过传统自动化工具解析。

### 1.2 项目目标与愿景

本项目旨在构建一个基于**自主 AI 智能体 (Autonomous AI Agent)** 的高级自动化交易系统，以解决上述痛点。

系统的核心愿景是，交付一个**单一但强大**的、基于**状态机**的 AI 智能体。这个智能体在逻辑上封装了分析、规划、风控、执行和记忆等多种能力，通过一个**结构化、可追溯**的工作流，取代传统固化、刚性的软件流程。该系统将能够实时监控、**深度理解**、规划、风控并执行来自社交平台的自然语言交易信号，**其核心能力包括直接从模糊的文本中判断交易方向（`buy`/`sell`）**，实现端到端的自动化交易，并确保过程的高效、精准与安全。最终目标是交付一个具备**自适应学习和演化能力的下一代交易智能体**。

### 1.3 核心设计原则

系统的设计与实现必须遵循以下四大核心原则，以平衡个人开发的敏捷性与项目的长期扩展性：

- **单一应用，模块化设计 (Monolith First, Modular Design)**：以单个 FastAPI 应用作为起点，内部逻辑高度模块化，为未来可能的微服务化拆分预留接口。
- **状态驱动与可追溯 (State-Driven & Traceable)**：系统的核心是围绕一个明确的状态机构建的。每一次状态的变迁都必须被记录，确保每个决策路径都是完全透明和可审计的。
- **契约优先 (Contract-First)**：所有内部模块间、前后端间以及与外部系统的交互，都必须有严格的、基于代码的模式（Schema）定义。
- **务实的数据持久化 (Pragmatic Persistence)**：选择功能强大且成熟的 PostgreSQL 作为**唯一的**核心数据存储，利用其 JSONB 字段的灵活性来存储半结构化的 AI 日志和上下文数据。

## 2. 系统架构

为实现上述目标，系统采纳“结构化单体”架构，将所有核心逻辑内聚在一个后端应用中，极大降低了开发和部署的复杂性。

### 2.1 系统架构图 (C4 - 容器图)

```mermaid
C4Container
    title 系统容器架构图

    Person(user, "交易员", "使用系统进行智能跟单交易")
    Person(admin, "管理员", "系统配置和监控")

    System_Boundary(trading_system, "AI智能跟单系统") {
        Container(frontend, "前端应用", "Vue.js 3 + Vuetify", "用户界面，提供交易管理和配置功能")
        Container(backend, "后端API", "FastAPI + Python", "业务逻辑处理和API服务")
        Container(agent, "AI Agent", "LangGraph + LLM", "智能交易决策引擎")
        Container(websocket, "WebSocket服务", "FastAPI WebSocket", "实时通信和状态推送")
        ContainerDb(database, "数据库", "SQLite/PostgreSQL", "持久化存储")
    }

    System_Ext(discord, "Discord", "交易信号源")
    System_Ext(exchanges, "加密货币交易所", "Binance, OKX, Bybit")
    System_Ext(llm_api, "LLM API", "OpenAI GPT-4 / Anthropic Claude")

    Rel(user, frontend, "使用", "HTTPS")
    Rel(admin, frontend, "管理", "HTTPS")
    Rel(frontend, backend, "API调用", "REST/JSON")
    Rel(frontend, websocket, "实时通信", "WebSocket")
    Rel(backend, agent, "调用", "Python函数")
    Rel(backend, database, "读写", "SQL")
    Rel(agent, database, "状态持久化", "SQL")
    Rel(agent, llm_api, "AI推理", "HTTPS/API")
    Rel(backend, exchanges, "交易执行", "REST API")
    Rel(backend, discord, "监听信号", "Discord Bot API")
```

```mermaid
graph TD
    subgraph "外部系统 (External Systems)"
        direction LR
        ext_discord["Discord API"]
        ext_exchange["CCXT 统一交易所 API"]
        ext_llm["LLM API (OpenAI, Anthropic, etc.)"]
    end

    actor_user["用户 (User)"]

    subgraph "智能跟单系统 (System Boundary - 单机部署)"
        direction TB

        frontend["<b>前端应用 (Frontend)</b><br/>(Vue.js 3 + Vuetify 3)<br/>提供用户交互界面"]

        subgraph "后端单体应用 (Backend Monolith - FastAPI)"
            direction LR
            api_gateway["<b>API & WebSocket 层</b><br/>处理用户请求，管理实时连接"]
            agent_core["<b>AI 核心 (AI Core)</b><br/><i>系统的智能决策中枢</i><br/>基于 LangGraph 状态机执行任务"]
            background_tasks["<b>后台任务 (Background Tasks)</b><br/>包含 Discord 监听器和价格观察者"]
        end

        subgraph "数据存储 (Data Persistence)"
            db_postgres["<b>PostgreSQL 数据库</b><br/>(Alembic 管理)<br/>存储所有核心业务数据<br/><b>并包含 Agent 状态检查点</b>"]
        end
    end
```

### 2.2 核心组件职责

- **前端应用 (Frontend)**: 用户与系统交互的唯一入口，负责展示数据、接收用户输入，并通过 API 和 WebSocket 与后端通信。
- **后端单体应用 (Backend Monolith)**:
  - **API & WebSocket 层**: 系统的“门面”，处理所有外部请求，验证用户身份，并通过 WebSocket 向前端实时推送状态更新。
  - **AI 核心 (AI Core)**: 系统的“大脑”，基于 LangGraph 实现了一个状态驱动的工作流，负责解析指令、制定计划、评估风险和执行任务。
  - **后台任务 (Background Tasks)**: 负责主动监听外部事件，如通过 `Discord 监听器` 接收新消息，或由 `价格观察者` 监控条件订单的触发。

## 3. 功能性需求 (Functional Requirements)

### 3.1 指令解析与执行场景

系统必须能够准确、自动地处理以下所有指令场景。其处理逻辑将遵循一个统一的、基于状态机的工作流，**其中交易方向的判断完全由 AI 在解析阶段完成**。

|             |                              |                                                                                |                                                                                                                                                                  |
| ----------- | ---------------------------- | ------------------------------------------------------------------------------ | ---------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **案例 ID** | **场景描述**                 | **用户输入样例 (中/英)**                                                       | **核心处理逻辑 (基于 LangGraph 状态机)**                                                                                                                         |
| **FN-01**   | **新建标准订单**             | `做多 BTC/USDT，100U`<br/>`long btc with 100 usd`                              | **Parse** 节点调用 LLM，直接将输入解析为包含 `side: "buy"` 的 `CREATE_ORDER` 意图，随后工作流顺序执行。                                                          |
| **FN-02**   | **单订单模糊平仓**           | `平了 / 走了 / tp`<br/>`close it / tp`                                         | **Parse** 节点调用 LLM，解析为 `CLOSE_ORDER` 意图，并基于常识（如 `tp` 通常是平多仓）推断出 `side: "sell"`。                                                     |
| **FN-03**   | **多订单智能平仓**           | `把 btc 平了`<br/>`close all btc positions`                                    | **Parse** 节点调用 LLM，识别平仓意图和 `btc` 目标，并推断出 `side`。**Context** 节点随后找到所有活跃 BTC 订单用于生成计划。                                      |
| **FN-04**   | **多订单模糊平仓 (AI 澄清)** | `平掉那个盈利的 / 刚开的那个平了`<br/>`close the profitable one`               | **Parse** 节点调用 LLM，识别出 `CLOSE_ORDER` 意图，但因上下文不足，将 `side` 字段留空 (`null`) 并生成澄清问题。工作流路由到 **UserConfirm** 节点，等待用户确认。 |
| **FN-05**   | **修改订单参数**             | `BTC 止损调到 10500`<br/>`move btc sl to 10500`                                | **Parse** 节点调用 LLM，解析出 `MODIFY_ORDER` 意图。对于此意图，`side` 字段为 `null`，因其不涉及方向改变。                                                       |
| **FN-06**   | **复合指令处理**             | `ETH 1800 做空进场，止损设 1850`<br/>`short eth at 1800, set sl to 1850`       | **Parse** 节点调用 LLM，将指令分解为两个原子意图：一个 `CREATE_ORDER` 意图（包含 `side: "sell"`）和一个 `MODIFY_ORDER` 意图。                                    |
| **FN-07**   | **自然语言指代**             | A: `关注 BTC 单`<br/>B: `止损调低 200 点`                                      | **Context** 节点获取历史消息。**Parse** 节点在解析 B 消息时，结合上下文，理解其修改的是 A 中提到的 BTC 订单。                                                    |
| **FN-08**   | **批量操作**                 | `清仓所有 / 全部止盈`<br/>`close all positions`                                | **Parse** 节点调用 LLM，识别批量操作意图，并为 `CLOSE_ORDER` 推断出 `side`。**Plan** 节点生成多个平仓动作。                                                      |
| **FN-09**   | **条件指令**                 | `如果 ETH 跌破 1800，就开一个空单`<br/>`if eth drops below 1800, open a short` | **Parse** 节点调用 LLM，识别为条件指令，并解析出其中包含的 `CREATE_ORDER` 意图（含 `side: "sell"`）。**Execute** 节点创建条件订单记录。                          |
| **FN-10**   | **参数继承与补充**           | `ETH 再加仓 100U`<br/>`add 100u to eth`                                        | **Context** 节点找到最近的 ETH 订单。**Parse** 节点结合上下文，解析出 `CREATE_ORDER` 意图，并继承原订单的 `side`。                                               |
| **FN-11**   | **查询状态**                 | `我的 btc 怎么样了`<br/>`how's my btc doing`                                   | **Parse** 节点调用 LLM，识别为 `QUERY_STATUS` 意图，其 `side` 字段为 `null`。**Execute** 节点调用工具获取状态并推送。                                            |

#### 3.1.2 信号管理功能场景

系统必须能够统一管理来自不同平台的交易信号，并提供完整的信号生命周期管理功能。

|             |                              |                                                                                |                                                                                                                                                                  |
| ----------- | ---------------------------- | ------------------------------------------------------------------------------ | ---------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **案例 ID** | **场景描述**                 | **用户操作样例**                                                               | **系统处理逻辑**                                                                                                                                                 |
| **SG-01**   | **Discord信号自动收集**      | Discord频道中发布：`🚀 BTC 做多 50000 止损 48000`                              | 系统自动监听Discord消息，解析并存储到signals表，设置ai_parse_status为pending，等待AI解析处理。                                                                    |
| **SG-02**   | **信号AI解析处理**           | 用户在信号管理页点击"AI解析"按钮                                               | 调用LLM服务解析信号内容，提取交易意图，更新confidence、message_type_ai等字段，将解析结果存储到metadata的ai_analysis中。                                          |
| **SG-03**   | **信号筛选和查询**           | 用户设置筛选条件：平台=Discord，置信度>0.8，消息类型=交易信号                   | 系统根据筛选条件查询signals表，支持按平台、AI解析状态、消息类型、置信度范围等多维度筛选。                                                                        |
| **SG-04**   | **信号详情查看**             | 用户点击信号卡片查看详情                                                       | 展示完整的信号信息，包括原始内容、AI解析结果、订单数据结构、处理元数据等。支持原生平台样式和原始格式两种渲染模式。                                               |
| **SG-05**   | **手动创建信号**             | 用户在信号管理页点击"手动添加信号"，输入交易内容                               | 创建platform=manual的信号记录，用户可以手动输入交易信号内容，系统同样进行AI解析处理。                                                                            |
| **SG-06**   | **信号状态管理**             | 用户标记信号为"已处理"或"忽略"                                                 | 更新信号的is_processed状态和processed_at时间戳，支持批量操作。                                                                                                   |
| **SG-07**   | **信号统计分析**             | 用户查看信号统计页面                                                           | 展示信号总数、处理状态分布、平台来源分布、AI解析成功率、平均置信度等统计信息。                                                                                   |
| **SG-08**   | **智能信号自动执行**         | AI智能体自动识别高置信度信号（confidence > 阈值），触发自主决策流程             | AI智能体基于LangGraph状态机自动执行完整工作流：**Parse**→**Context**→**Plan**→**Risk**→**Execute**，包含智能风控评估、仓位管理和执行决策，实现真正的自主交易。 |
| **SG-09**   | **智能信号人机协作执行**     | AI智能体识别中等置信度信号（阈值范围内），生成执行建议并请求用户确认           | AI智能体执行**Parse**→**Context**→**Plan**→**Risk**流程，生成详细的执行计划和风险评估报告，通过**UserConfirm**节点请求用户批准，体现人机协作的智能决策。     |

### 3.2 用户界面与控制

系统需提供一个基于 Web 的前端界面，作为用户与 Agent 系统交互的核心门户。

- **`DashboardView` (仪表盘)**: 展示核心概览信息，**必须包含一个“待处理动作”列表**，用于展示需要用户手动【批准】或【拒绝】的模糊指令（例如，AI 无法确定交易方向时，会在此处请求用户选择“做多”或“做空”）。
- **`SignalsView` (信号管理页)**: 统一管理来自不同平台（Discord、Telegram等）的交易信号，支持信号的查看、筛选、分析和处理状态跟踪。提供多种渲染模式（原生平台样式、原始格式）和AI解析结果展示。**核心功能包括智能自动执行配置（置信度阈值设置）、人机协作决策界面，以及实时的AI智能体执行状态监控**。
- **`OrdersView` (订单管理页)**: 以可筛选、可排序的数据表格形式，详细展示所有历史订单和活跃订单。
- **`ConfigsView` (配置管理页)**: 允许用户通过表单管理**信号源、交易所 API Key 和风控规则**。
- **紧急控制**: 必须提供“一键暂停所有交易”和“一键清仓所有头寸”的紧急按钮。

### 3.3 用户引导与人机交互

- **首次用户设置向导**: 新用户首次登录后，系统必须启动一个分步式的设置向导，引导用户完成所有必要的初始配置。
- **人机交互规则**:
  - **交互触发条件**: 必须是可量化的，例如 AI 解析置信度低于用户设定的阈值，或者**AI 无法确定交易方向 (`side` 为 `null`)**。
  - **交互超时机制**: 每一个待处理动作都必须设定一个可配置的超时时限，超时后默认执行“拒绝”操作。

## 4. 数据、状态与通信规约

### 4.1 数据持久化 Schema

系统需使用 PostgreSQL 数据库，其核心数据表结构需遵循[后端设计文档](./2.%20后端设计文档.md)中定义的 ERD，至少包含 `users`, `orders`, `signals`, `pending_actions`, `exchange_configs`, `risk_configs`, `conditional_orders`, `agent_checkpoints` 等表。这些表的设计能够支持 AI 解析出的结构化意图数据。

### 4.2 核心任务状态机

每个任务都必须拥有一个明确的状态，并遵循预定义的转换路径，该路径由 LangGraph 的状态图定义，至少应包括：`Preprocess`, `Parse`, `Context`, `Plan`, `Risk`, `Execute`, `UserConfirm`, `AnalyzeError` 等核心状态节点。AI 的核心智能（包括方向判断）被封装在 `Parse` 节点的逻辑中。

### 4.3 前后端通信协议

所有前后端实时通信必须遵循统一的、基于 Pydantic Schema 的 WebSocket 消息格式，如 `OrderUpdateEvent`, `PendingActionRequiredEvent`, `SignalUpdateEvent` 等。该协议能够承载因 AI 无法确定方向而需要用户澄清的交互请求，以及新信号的实时推送。

## 5. 非功能性需求 (Non-Functional Requirements)

|                      |                                                                                                                                                                                                                                 |
| -------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **类别**             | **需求描述**                                                                                                                                                                                                                    |
| **性能**             | 从系统收到 Discord 消息到向交易所成功下单的端到端延迟应控制在 **5 秒**以内。                                                                                                                                                    |
| **可靠性与容错**     | 系统需 7x24 小时稳定运行。任务状态必须通过 **PostgreSQL 中的检查点** 进行持久化，以支持在系统重启后恢复。**数据库的结构变更必须通过迁移工具（如 Alembic）进行版本化管理，确保部署的可重复性和安全性。**                         |
| **安全性与数据隐私** | - 所有敏感凭证（如 API 密钥）在存入数据库前必须使用强加密算法加密。 <br>- 风控检查是不可被绕过的安全检查点。 <br>- 必须遵循最小权限原则。                                                                                       |
| **成本与可控性**     | - 必须为 AI Agent 的推理过程设置**最大循环次数**，防止无限循环。 <br>- 用户可在配置页设置**LLM 调用成本预算**。                                                                                                                 |
| **可测试性与验证**   | - 系统必须提供一个**“仿真模式”**，用于在不执行真实交易的情况下，对预设场景进行回放测试。                                                                                                                                        |
| 可观测性与可解释性   | - 所有日志记录必须采用**结构化日志 (JSON 格式)**，并强制包含`task_id`, `user_id`等上下文信息，便于聚合查询。 <br>- 核心业务流程必须支持**分布式追踪 (OpenTelemetry)**，以可视化端到端的调用链，便于定位性能瓶颈和调试复杂错误。 |
| **幂等性**           | 必须通过数据库唯一约束等机制，防止同一条交易信号被重复执行。                                                                                                                                                                    |

## 6. 技术栈选型

|                   |                        |                                                                                        |
| ----------------- | ---------------------- | -------------------------------------------------------------------------------------- |
| **领域**          | **技术/框架**          | **备注**                                                                               |
| **语言**          | Python 3.11+           | 利用最新的类型提示和异步特性。使用 _Anaconda_                                          |
| **Web 框架**      | FastAPI                | 性能卓越，与 Pydantic 无缝集成。                                                       |
| **AI Agent 框架** | LangGraph              | 提供确定性、可追溯的状态机。                                                           |
| **结构化输出**    | Instructor             | 强制 LLM 输出 Pydantic 模型，保证健壮性。                                              |
| **数据库**        | PostgreSQL             | **首选数据库**，统一开发与生产环境，利用其高级功能 (JSONB, etc.)                     |
| **ORM**           | SQLAlchemy 2.x (Async) | 成熟可靠的异步 ORM。                                                                   |
| **数据库迁移**    | Alembic                | 管理数据库 Schema 演进。                                                               |
| **缓存/会话**     | Redis                  | **(可选/未来扩展)** 用于性能优化和高可用性。                                           |
| **交易所交互**    | ccxt                   | 业界标准的加密货币交易所交互库。                                                       |
| **前端框架**      | Vue.js 3 + Vuetify 3   | 现代化、高效的前端开发组合。                                                           |
| **部署**          | Docker                 | 容器化部署，简化流程。                                                                 |
| **编排工具**      | Docker Compose         | 多容器应用编排。                                                                       |
| **依赖管理**      | Anaconda/Miniconda     | **推荐**：统一的 Python 环境和包管理，支持跨平台，解决依赖冲突，提供优化的科学计算库。 |
| **备选依赖管理**  | pip + requirements.txt | 传统方式，适合简单部署场景。                                                           |

## 7. 开发与部署策略

项目开发采用分阶段实施策略，以控制风险并逐步交付价值：

- **第一阶段：核心功能 MVP**: 搭建基础架构，实现用户认证、交易所配置，构建处理标准指令的 Agent 图，完成前端订单展示。
- **第二阶段：增强 AI 与交互**: 完善人机交互规则，增强 AI Prompt 以精准判断交易方向，处理模糊指令和条件指令。
- **第三阶段：长期学习与扩展**: 引入向量数据库和 RAG 机制，构建 Agent 的长期记忆和自适应学习能力。
