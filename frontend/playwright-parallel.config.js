/**
 * Playwright 并行测试优化配置
 * 专门针对大量e2e测试的并行执行优化
 */

import { defineConfig, devices } from '@playwright/test'
import os from 'os'

// 并行测试优化配置
const PARALLEL_CONFIG = {
  API_BASE_URL: process.env.API_BASE_URL || 'http://localhost:8000',
  FRONTEND_URL: process.env.FRONTEND_URL || 'http://localhost:5173',
  
  // 并行执行参数
  PARALLEL: {
    // 根据CPU核心数动态调整worker数量
    MAX_WORKERS: Math.min(os.cpus().length, 8), // 最多8个worker
    MIN_WORKERS: 2, // 最少2个worker
    CI_WORKERS: 4,  // CI环境固定4个worker
    
    // 测试分片配置
    SHARD_COUNT: process.env.SHARD_COUNT ? parseInt(process.env.SHARD_COUNT) : 1,
    SHARD_INDEX: process.env.SHARD_INDEX ? parseInt(process.env.SHARD_INDEX) : 1,
  },
  
  // 超时优化（并行时需要更宽松的超时）
  TIMEOUTS: {
    TEST: 180000,        // 单个测试180秒（3分钟）
    ACTION: 30000,       // 操作超时30秒
    NAVIGATION: 45000,   // 导航超时45秒
    EXPECT: 15000,       // 断言超时15秒
    GLOBAL_SETUP: 600000 // 全局设置600秒（10分钟）
  }
}

// 动态计算最优worker数量
const getOptimalWorkers = () => {
  if (process.env.CI) {
    return PARALLEL_CONFIG.PARALLEL.CI_WORKERS
  }
  
  const cpuCount = os.cpus().length
  const memoryGB = Math.floor(os.totalmem() / (1024 * 1024 * 1024))
  
  // 基于CPU和内存计算最优worker数
  const cpuBasedWorkers = Math.floor(cpuCount * 0.75) // 使用75%的CPU
  const memoryBasedWorkers = Math.floor(memoryGB / 2) // 每2GB内存一个worker
  
  const optimalWorkers = Math.min(
    cpuBasedWorkers,
    memoryBasedWorkers,
    PARALLEL_CONFIG.PARALLEL.MAX_WORKERS
  )
  
  return Math.max(optimalWorkers, PARALLEL_CONFIG.PARALLEL.MIN_WORKERS)
}

export default defineConfig({
  testDir: './tests',
  
  // 启用完全并行
  fullyParallel: true,
  
  // 优化的worker配置
  workers: getOptimalWorkers(),
  
  // 测试分片支持（用于CI/CD管道）
  shard: process.env.SHARD_COUNT ? {
    total: PARALLEL_CONFIG.PARALLEL.SHARD_COUNT,
    current: PARALLEL_CONFIG.PARALLEL.SHARD_INDEX
  } : undefined,
  
  // 失败重试策略
  retries: process.env.CI ? 2 : 1,
  
  // 禁止test.only在CI中
  forbidOnly: !!process.env.CI,
  
  // 优化的报告配置
  reporter: [
    ['html', {
      outputFolder: '../temp/frontend/playwright/parallel-report',
      open: 'never'
    }],
    ['json', {
      outputFile: '../temp/frontend/playwright/parallel-results.json'
    }],
    ['junit', {
      outputFile: '../temp/frontend/playwright/parallel-junit.xml'
    }],
    // 并行执行时使用简洁的控制台输出
    ['line'],
    // 添加并行执行进度报告
    ['github'] // 如果在GitHub Actions中运行
  ],
  
  // 全局配置
  use: {
    baseURL: PARALLEL_CONFIG.FRONTEND_URL,
    
    // 并行执行时的调试配置
    trace: 'retain-on-failure', // 只在失败时保留trace
    video: 'retain-on-failure',
    screenshot: 'only-on-failure',
    
    // 优化的超时配置
    actionTimeout: PARALLEL_CONFIG.TIMEOUTS.ACTION,
    navigationTimeout: PARALLEL_CONFIG.TIMEOUTS.NAVIGATION,
    
    expect: {
      timeout: PARALLEL_CONFIG.TIMEOUTS.EXPECT
    },
    
    // 网络优化
    ignoreHTTPSErrors: true,
    acceptDownloads: false, // 并行时禁用下载以提高性能
    
    // 视口优化（减少渲染开销）
    viewport: { width: 1280, height: 720 },
    
    // 并行执行时的浏览器优化
    launchOptions: {
      args: [
        '--disable-web-security',
        '--disable-features=VizDisplayCompositor',
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-gpu',
        '--disable-background-timer-throttling',
        '--disable-backgrounding-occluded-windows',
        '--disable-renderer-backgrounding'
      ]
    }
  },
  
  // 项目配置 - 按优先级和依赖关系分组
  projects: [
    // 第一组：快速API测试（无UI依赖）
    {
      name: 'api-fast',
      testDir: './tests/api-unified',
      use: {
        baseURL: PARALLEL_CONFIG.API_BASE_URL,
      },
      testMatch: ['**/*.api.test.js'],
      // API测试可以更多并行
      fullyParallel: true
    },
    
    // 第二组：核心功能测试（高优先级）
    {
      name: 'core-features',
      testDir: './tests/e2e',
      use: {
        ...devices['Desktop Chrome'],
        baseURL: PARALLEL_CONFIG.FRONTEND_URL,
      },
      testMatch: [
        '**/auth.test.js',
        '**/dashboard-navigation.test.js',
        '**/core-functionality.test.js',
        '**/order-management-flow.test.js'
      ],
      dependencies: ['api-fast'] // 依赖API测试通过
    },
    
    // 第三组：业务流程测试（中优先级）
    {
      name: 'business-flows',
      testDir: './tests/e2e',
      use: {
        ...devices['Desktop Chrome'],
        baseURL: PARALLEL_CONFIG.FRONTEND_URL,
      },
      testMatch: [
        '**/trading-flow.spec.js',
        '**/conditional-orders-flow.test.js',
        '**/signal-management-complete.e2e.test.js',
        '**/comprehensive-flow.test.js',
        '**/complete-user-journey.test.js'
      ],
      dependencies: ['core-features']
    },
    
    // 第四组：高级功能测试（低优先级）
    {
      name: 'advanced-features',
      testDir: './tests/e2e',
      use: {
        ...devices['Desktop Chrome'],
        baseURL: PARALLEL_CONFIG.FRONTEND_URL,
      },
      testMatch: [
        '**/advanced-trading.test.js',
        '**/realtime-features.test.js',
        '**/websocket-real-data.test.js',
        '**/performance-load.test.js'
      ],
      dependencies: ['business-flows']
    },
    
    // 第五组：系统测试（独立运行）
    {
      name: 'system-tests',
      testDir: './tests/e2e',
      use: {
        ...devices['Desktop Chrome'],
        baseURL: PARALLEL_CONFIG.FRONTEND_URL,
      },
      testMatch: [
        '**/config-management.test.js',
        '**/system-preferences.test.js',
        '**/user-management.test.js'
      ]
    },

    // 第五组B：错误处理测试（耗时较长，独立运行）
    {
      name: 'error-handling',
      testDir: './tests/e2e',
      use: {
        ...devices['Desktop Chrome'],
        baseURL: PARALLEL_CONFIG.FRONTEND_URL,
      },
      testMatch: [
        '**/error-boundary.test.js',
        '**/console-error-*.test.js'
      ],
      fullyParallel: false, // 串行执行，避免错误日志混乱
      timeout: 240000 // 4分钟超时
    },
    
    // 第六组：UI/UX测试（可并行）
    {
      name: 'ui-ux-tests',
      testDir: './tests/e2e',
      use: {
        ...devices['Desktop Chrome'],
        baseURL: PARALLEL_CONFIG.FRONTEND_URL,
      },
      testMatch: [
        '**/responsive-mobile.test.js',
        '**/ui-system.test.js',
        '**/onboarding-wizard.test.js'
      ]
    },
    
    // 第七组：数据和测试管理（最后运行）
    {
      name: 'test-management',
      testDir: './tests/e2e',
      use: {
        ...devices['Desktop Chrome'],
        baseURL: PARALLEL_CONFIG.FRONTEND_URL,
      },
      testMatch: [
        '**/test-data-management.test.js',
        '**/test-report-analysis.test.js'
      ]
    }
  ],
  
  // 全局超时
  timeout: PARALLEL_CONFIG.TIMEOUTS.TEST,
  globalTimeout: PARALLEL_CONFIG.TIMEOUTS.GLOBAL_SETUP,
  
  // 输出目录
  outputDir: '../temp/frontend/playwright/parallel-artifacts',
  
  // 并行执行时不启动webServer（假设服务已运行）
  webServer: undefined,
  
  // 全局设置和清理
  globalSetup: './tests/setup/parallel-global-setup.js',
  globalTeardown: './tests/setup/parallel-global-teardown.js'
})