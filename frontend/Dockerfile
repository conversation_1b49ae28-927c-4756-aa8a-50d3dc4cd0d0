# 多阶段构建：开发环境和生产环境
ARG NODE_ENV=production

# 开发环境
FROM node:18-alpine as development

WORKDIR /app

# 配置npm使用国内镜像源
RUN npm config set registry https://registry.npmmirror.com/

# 复制package文件
COPY package*.json ./

# 安装依赖
RUN npm install

# 在开发模式下，源代码通过volume挂载，不需要COPY
# 这样可以实现真正的热重载

# 暴露Vite开发服务器端口和HMR端口
EXPOSE 5173
EXPOSE 24678

# 开发模式启动命令 - 添加HMR相关参数
CMD ["npm", "run", "dev", "--", "--host", "0.0.0.0", "--port", "5173"]

# 生产构建阶段
FROM node:18-alpine as build

WORKDIR /app

# 配置npm使用国内镜像源
RUN npm config set registry https://registry.npmmirror.com/

COPY package*.json ./

RUN npm install

COPY . .

RUN npm run build

# 生产环境
FROM nginx:stable-alpine as production

COPY --from=build /app/dist /usr/share/nginx/html

COPY nginx.conf /etc/nginx/conf.d/default.conf

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
