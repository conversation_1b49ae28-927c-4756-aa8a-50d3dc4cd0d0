<template>
  <v-container fluid class="monitoring-view pa-4">
    <!-- 页面标题和控制栏 -->
    <div class="d-flex align-center justify-space-between mb-6">
      <div>
        <h1 class="text-h4 font-weight-bold mb-2">信号处理监控中心</h1>
        <div class="d-flex align-center text-body-2 text-medium-emphasis">
          <v-icon size="small" class="mr-1">mdi-clock-outline</v-icon>
          <span>最后更新: {{ formatLastUpdated }}</span>
          <v-chip
            v-if="monitoringStore.isHealthy"
            size="small"
            color="success"
            variant="tonal"
            class="ml-3"
          >
            <v-icon start>mdi-check-circle</v-icon>
            系统正常
          </v-chip>
          <v-chip
            v-else
            size="small"
            color="error"
            variant="tonal"
            class="ml-3"
          >
            <v-icon start>mdi-alert-circle</v-icon>
            需要关注
          </v-chip>
        </div>
      </div>
      
      <!-- 控制按钮 -->
      <div class="d-flex align-center gap-2">
        <!-- 自动刷新控制 -->
        <v-btn-toggle
          v-model="monitoringStore.autoRefreshEnabled"
          mandatory
          variant="outlined"
          size="small"
        >
          <v-btn :value="true" @click="monitoringStore.toggleAutoRefresh()">
            <v-icon>mdi-refresh-auto</v-icon>
            <v-tooltip activator="parent">自动刷新</v-tooltip>
          </v-btn>
        </v-btn-toggle>
        
        <!-- 刷新间隔设置 -->
        <v-menu>
          <template #activator="{ props }">
            <v-btn
              variant="outlined"
              size="small"
              v-bind="props"
            >
              {{ monitoringStore.refreshIntervalSeconds }}s
              <v-icon end>mdi-chevron-down</v-icon>
            </v-btn>
          </template>
          
          <v-list>
            <v-list-item
              v-for="interval in refreshIntervals"
              :key="interval"
              @click="monitoringStore.setRefreshInterval(interval)"
            >
              <v-list-item-title>{{ interval }}秒</v-list-item-title>
              <template #append>
                <v-icon v-if="monitoringStore.refreshIntervalSeconds === interval">
                  mdi-check
                </v-icon>
              </template>
            </v-list-item>
          </v-list>
        </v-menu>
        
        <!-- 手动刷新 -->
        <v-btn
          color="primary"
          variant="outlined"
          size="small"
          @click="handleRefresh"
          :loading="monitoringStore.loading"
        >
          <v-icon>mdi-refresh</v-icon>
          刷新
        </v-btn>
      </div>
    </div>
    
    <!-- 错误提示 -->
    <v-alert
      v-if="monitoringStore.error"
      type="error"
      variant="tonal"
      class="mb-6"
      closable
      @click:close="monitoringStore.clearError()"
    >
      <v-alert-title>数据加载失败</v-alert-title>
      <div>{{ monitoringStore.error }}</div>
      <template #append>
        <v-btn
          size="small"
          variant="outlined"
          @click="handleRefresh"
        >
          重试
        </v-btn>
      </template>
    </v-alert>
    
    <!-- KPI指标卡片区域 -->
    <v-row class="mb-6">
      <v-col
        v-for="(kpi, key) in monitoringStore.kpiMetrics"
        :key="key"
        cols="12"
        sm="6"
        md="3"
      >
        <KpiCard
          :title="getKpiTitle(key)"
          :value="kpi.value"
          :trend="kpi.trend"
          :color="kpi.color"
          :icon="kpi.icon"
          :format="kpi.format"
          :loading="monitoringStore.loading"
          clickable
          @click="handleKpiClick(key, kpi)"
        />
      </v-col>
    </v-row>
    
    <!-- 交互式流程图区域 -->
    <v-row class="mb-6">
      <v-col cols="12">
        <SignalFlowChart
          :nodes="monitoringStore.flowNodes"
          :connections="monitoringStore.flowConnections"
          :loading="monitoringStore.loading"
          @node-click="handleNodeClick"
        />
      </v-col>
    </v-row>
    
    <!-- 实时状态和告警区域 -->
    <v-row>
      <v-col cols="12" md="8">
        <RealTimeStatusList
          :signals="monitoringStore.recentSignals"
          :loading="monitoringStore.loading"
          @refresh="monitoringStore.refreshRecentSignals"
          @load-more="handleLoadMoreSignals"
          @signal-click="handleSignalClick"
        />
      </v-col>
      
      <v-col cols="12" md="4">
        <AlertNotificationPanel
          :alerts="monitoringStore.activeAlerts"
          :loading="monitoringStore.loading"
          @dismiss-alert="handleDismissAlert"
          @clear-all="handleClearAllAlerts"
          @refresh="monitoringStore.refreshActiveAlerts"
          @view-history="handleViewAlertHistory"
          @alert-action="handleAlertAction"
        />
      </v-col>
    </v-row>
  </v-container>
</template>

<script setup>
import { computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useMonitoringStore } from '@/stores/monitoring'
import KpiCard from '@/components/monitoring/KpiCard.vue'
import SignalFlowChart from '@/components/monitoring/SignalFlowChart.vue'
import RealTimeStatusList from '@/components/monitoring/RealTimeStatusList.vue'
import AlertNotificationPanel from '@/components/monitoring/AlertNotificationPanel.vue'

const router = useRouter()
const monitoringStore = useMonitoringStore()

// 刷新间隔选项
const refreshIntervals = [10, 30, 60, 120, 300]

// 计算属性
const formatLastUpdated = computed(() => {
  if (!monitoringStore.lastUpdated) return '从未更新'
  
  const date = new Date(monitoringStore.lastUpdated)
  return date.toLocaleString()
})

// KPI标题映射
const getKpiTitle = (key) => {
  const titleMap = {
    success_rate: '成功率',
    avg_processing_time: '平均处理时间',
    processing_count: '处理中',
    stuck_count: '异常信号'
  }
  return titleMap[key] || key
}

// 事件处理函数
const handleRefresh = async () => {
  await monitoringStore.fetchAllMonitoringData()
}

const handleKpiClick = (key, kpi) => {
  console.log('KPI clicked:', key, kpi)
  // 可以导航到详细页面或显示更多信息
}

const handleNodeClick = (node) => {
  console.log('Node clicked:', node)
  // 节点点击处理逻辑已在组件内部实现
}

const handleSignalClick = (signal) => {
  console.log('Signal clicked:', signal)
  // 可以导航到信号详情页面
  // router.push(`/signals/${signal.id}`)
}

const handleLoadMoreSignals = () => {
  // 加载更多信号
  monitoringStore.fetchRecentSignals(20)
}

const handleDismissAlert = (alertId) => {
  monitoringStore.removeAlert(alertId)
}

const handleClearAllAlerts = () => {
  monitoringStore.clearAllAlerts()
}

const handleViewAlertHistory = () => {
  // 导航到告警历史页面
  // router.push('/monitoring/alerts/history')
  console.log('View alert history')
}

const handleAlertAction = ({ alert, action }) => {
  console.log('Alert action:', alert, action)
  
  switch (action.action) {
    case 'view_stuck_signals':
      // 查看卡住的信号
      break
    case 'retry_stuck_signals':
      // 重试卡住的信号
      break
    case 'view_slow_signals':
      // 查看处理缓慢的信号
      break
    case 'view_failed_signals':
      // 查看失败的信号
      break
    default:
      console.log('Unknown action:', action.action)
  }
}

// 生命周期钩子
onMounted(async () => {
  // 初始化数据加载
  await monitoringStore.fetchAllMonitoringData()
  
  // 启动自动刷新
  monitoringStore.startAutoRefresh()
})

onUnmounted(() => {
  // 停止自动刷新
  monitoringStore.stopAutoRefresh()
})

// 设置页面标题
document.title = '监控中心 - 加密货币交易系统'
</script>

<style scoped>
.monitoring-view {
  min-height: 100vh;
  background-color: rgb(var(--v-theme-background));
}

/* 响应式设计 */
@media (max-width: 960px) {
  .monitoring-view {
    padding: 16px 8px !important;
  }
  
  .text-h4 {
    font-size: 1.5rem !important;
  }
}

@media (max-width: 600px) {
  .monitoring-view {
    padding: 12px 4px !important;
  }
  
  .text-h4 {
    font-size: 1.25rem !important;
  }
  
  .d-flex.justify-space-between {
    flex-direction: column;
    align-items: flex-start !important;
    gap: 16px;
  }
}

/* 动画效果 */
.monitoring-view .v-row {
  animation: fadeInUp 0.6s ease-out;
}

.monitoring-view .v-row:nth-child(2) {
  animation-delay: 0.1s;
}

.monitoring-view .v-row:nth-child(3) {
  animation-delay: 0.2s;
}

.monitoring-view .v-row:nth-child(4) {
  animation-delay: 0.3s;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 深色主题适配 */
.v-theme--dark .monitoring-view {
  background-color: rgb(var(--v-theme-background));
}
</style>
