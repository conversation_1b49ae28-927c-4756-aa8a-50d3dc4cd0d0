<template>
  <v-container class="not-found-container d-flex align-center justify-center" data-testid="404-page">
    <div class="text-center">
      <v-icon
        size="120"
        color="grey-lighten-1"
        class="mb-6"
      >
        mdi-robot-confused
      </v-icon>

      <h1 class="text-h2 font-weight-bold mb-4 text-grey-darken-1" data-testid="404-title">
        404
      </h1>

      <h2 class="text-h4 mb-4 text-grey-darken-2" data-testid="404-message">
        页面未找到
      </h2>
      
      <p class="text-body-1 mb-6 text-grey-darken-1" style="max-width: 400px;">
        抱歉，您访问的页面不存在。可能是链接错误或页面已被移动。
      </p>
      
      <div class="d-flex flex-column flex-sm-row gap-4 justify-center">
        <v-btn
          color="primary"
          size="large"
          variant="flat"
          prepend-icon="mdi-home"
          @click="goHome"
        >
          返回首页
        </v-btn>
        
        <v-btn
          color="grey"
          size="large"
          variant="outlined"
          prepend-icon="mdi-arrow-left"
          @click="goBack"
        >
          返回上页
        </v-btn>
      </div>
      
      <v-divider class="my-8" style="max-width: 300px; margin: 32px auto;"></v-divider>
      
      <div class="text-center">
        <p class="text-body-2 text-grey mb-4">
          常用页面
        </p>
        
        <div class="d-flex flex-wrap justify-center gap-2">
          <v-chip
            v-for="link in quickLinks"
            :key="link.name"
            :to="link.path"
            color="primary"
            variant="outlined"
            size="small"
            :prepend-icon="link.icon"
            clickable
          >
            {{ link.name }}
          </v-chip>
        </div>
      </div>
    </div>
  </v-container>
</template>

<script setup>
import { useRouter } from 'vue-router'

const router = useRouter()

// 快速链接
const quickLinks = [
  { name: '仪表盘', path: '/', icon: 'mdi-view-dashboard' },
  { name: '订单管理', path: '/orders', icon: 'mdi-format-list-bulleted' },
  { name: '系统配置', path: '/configs', icon: 'mdi-cog' }
]

// 方法
function goHome() {
  router.push('/')
}

function goBack() {
  if (window.history.length > 1) {
    router.go(-1)
  } else {
    router.push('/')
  }
}
</script>

<style scoped>
.not-found-container {
  min-height: calc(100vh - 64px); /* 减去app-bar的高度 */
}

.gap-4 {
  gap: 16px;
}

.gap-2 {
  gap: 8px;
}

@media (max-width: 600px) {
  .text-h2 {
    font-size: 3rem !important;
  }
  
  .text-h4 {
    font-size: 1.5rem !important;
  }
}
</style>