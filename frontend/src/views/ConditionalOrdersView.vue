<template>
  <v-container fluid>
    <v-row>
      <v-col cols="12">
        <div class="d-flex align-center justify-space-between mb-4">
          <h1 class="text-h4 font-weight-bold">条件订单管理</h1>
          <v-btn 
            color="primary" 
            prepend-icon="mdi-plus"
            @click="openCreateDialog"
          >
            创建条件订单
          </v-btn>
        </div>
      </v-col>
    </v-row>

    <!-- 状态过滤器与搜索框 -->
    <v-row>
      <v-col cols="12" md="6">
        <StatusFilterChips
          v-model:selected="selectedStatus"
        />
      </v-col>

      <v-col cols="12" md="6" class="d-flex justify-end">
        <v-text-field
          v-model="search"
          label="搜索"
          append-inner-icon="mdi-magnify"
          density="compact"
          hide-details
          variant="outlined"
          style="max-width: 300px"
        ></v-text-field>
      </v-col>
    </v-row>

    <!-- 条件订单列表 -->
    <div class="mt-4">
      <ConditionalOrdersTable 
        :orders="filteredOrders"
        :loading="loading"
        :search="search"
        @view-details="viewOrderDetails"
        @cancel-order="cancelOrder"
        @create-new="openCreateDialog"
      />
    </div>

    <!-- 创建条件订单对话框 -->
    <CreateConditionalOrderDialog
      v-model:open="createDialogOpen"
      :submitting="submitting"
      @submit="createConditionalOrder"
    />

    <!-- 详情对话框 -->
    <ConditionalOrderDetailsDialog
      v-model:open="detailDialogOpen"
      :order="selectedOrder"
      @cancel="confirmCancelOrder"
      @view-linked-order="viewLinkedOrder"
    />
    
    <!-- 确认取消对话框 -->
    <ConfirmCancelDialog
      v-model:open="confirmCancelDialogOpen"
      :loading="cancelling"
      @confirm="confirmCancel"
    />
  </v-container>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useOrderStore } from '@/stores/order';
import { useUIStore } from '@/stores/ui';

// 导入子组件
import StatusFilterChips from '@/components/StatusFilterChips.vue';
import ConditionalOrdersTable from '@/components/ConditionalOrdersTable.vue';
import CreateConditionalOrderDialog from '@/components/CreateConditionalOrderDialog.vue';
import ConditionalOrderDetailsDialog from '@/components/ConditionalOrderDetailsDialog.vue';
import ConfirmCancelDialog from '@/components/ConfirmCancelDialog.vue';

// 路由与存储
const router = useRouter();
const orderStore = useOrderStore();
const uiStore = useUIStore();

// 响应式状态
const loading = ref(false);
const submitting = ref(false);
const cancelling = ref(false);
const conditionalOrders = ref([]);
const selectedStatus = ref([]);
const search = ref('');
const createDialogOpen = ref(false);
const detailDialogOpen = ref(false);
const confirmCancelDialogOpen = ref(false);
const selectedOrder = ref(null);

// 计算属性
const filteredOrders = computed(() => {
  if (selectedStatus.value.length === 0) {
    return conditionalOrders.value;
  }
  return conditionalOrders.value.filter(order => 
    selectedStatus.value.includes(order.status)
  );
});

// 页面加载
onMounted(() => {
  fetchConditionalOrders();
});

// 方法
function openCreateDialog() {
  createDialogOpen.value = true;
}

async function createConditionalOrder(orderData) {
  submitting.value = true;
  
  try {
    // 调用API创建条件订单
    await orderStore.createConditionalOrder(orderData);
    
    // 成功后关闭对话框并刷新数据
    createDialogOpen.value = false;
    uiStore.showSuccess('条件订单创建成功');
    await fetchConditionalOrders();
  } catch (error) {
    console.error('Failed to create conditional order:', error);
    uiStore.showError('创建条件订单失败: ' + error.message);
  } finally {
    submitting.value = false;
  }
}

function viewOrderDetails(order) {
  selectedOrder.value = order;
  detailDialogOpen.value = true;
}

function cancelOrder(order) {
  selectedOrder.value = order;
  confirmCancelDialogOpen.value = true;
}

function confirmCancelOrder(order) {
  selectedOrder.value = order;
  confirmCancelDialogOpen.value = true;
}

async function confirmCancel() {
  if (!selectedOrder.value) return;
  
  cancelling.value = true;
  
  try {
    // 调用API取消条件订单
    await orderStore.cancelConditionalOrder(selectedOrder.value.id);
    
    // 成功后关闭对话框并刷新数据
    confirmCancelDialogOpen.value = false;
    detailDialogOpen.value = false;
    uiStore.showSuccess('条件订单已取消');
    await fetchConditionalOrders();
  } catch (error) {
    console.error('Failed to cancel conditional order:', error);
    uiStore.showError('取消条件订单失败: ' + error.message);
  } finally {
    cancelling.value = false;
  }
}

function viewLinkedOrder(orderId) {
  // 跳转到订单详情页
  detailDialogOpen.value = false;
  router.push({
    name: 'orders',
    query: { highlight: orderId }
  });
}

// 获取条件订单列表
async function fetchConditionalOrders() {
  loading.value = true;
  
  try {
    // 调用API获取条件订单列表
    const result = await orderStore.getConditionalOrders();
    conditionalOrders.value = result;
  } catch (error) {
    console.error('Failed to fetch conditional orders:', error);
    uiStore.showError('获取条件订单失败: ' + error.message);
    conditionalOrders.value = [];
  } finally {
    loading.value = false;
  }
}
</script>

<style scoped>
.gap-2 {
  gap: 8px;
}

.condition-display {
  max-width: 250px;
  white-space: normal;
  word-break: break-word;
}

:deep(.v-data-table th) {
  white-space: nowrap;
}
</style> 