<template>
  <v-container>
    <v-row>
      <v-col cols="12">
        <h1>信号卡片组件演示</h1>
        <p>这个页面展示了新的SignalCard组件在不同主题下的效果。</p>
      </v-col>
    </v-row>

    <!-- 主题选择器 -->
    <v-row>
      <v-col cols="12">
        <v-card>
          <v-card-title>主题选择</v-card-title>
          <v-card-text>
            <v-btn-toggle v-model="selectedTheme" mandatory>
              <v-btn value="auto">自动</v-btn>
              <v-btn value="discord">Discord</v-btn>
              <v-btn value="telegram">Telegram</v-btn>
              <v-btn value="generic">通用</v-btn>
            </v-btn-toggle>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <!-- 信号卡片展示 -->
    <v-row>
      <v-col
        v-for="signal in demoSignals"
        :key="signal.id"
        cols="12"
        md="6"
        lg="4"
      >
        <SignalCard
          :signal="signal"
          :theme="selectedTheme"
          :selected="selectedSignals.includes(signal.id)"
          :selectable="true"
          :hover="true"
          :show-actions="true"
          :show-delete-action="true"
          :show-theme-toggle="true"
          @click="handleCardClick"
          @selection-change="handleSelectionChange"
          @view-details="handleViewDetails"
          @toggle-processed="handleToggleProcessed"
          @delete="handleDelete"
          @theme-change="handleThemeChange"
        />
      </v-col>
    </v-row>

    <!-- 事件日志 -->
    <v-row>
      <v-col cols="12">
        <v-card>
          <v-card-title>事件日志</v-card-title>
          <v-card-text>
            <div
              v-for="(event, index) in eventLog"
              :key="index"
              class="mb-1 text-caption"
            >
              <strong>{{ event.type }}:</strong> {{ event.message }}
            </div>
          </v-card-text>
          <v-card-actions>
            <v-btn @click="clearEventLog" size="small">清空日志</v-btn>
          </v-card-actions>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<script setup>
import { ref } from 'vue'
import SignalCard from '@/components/signals/SignalCard.vue'

// 响应式数据
const selectedTheme = ref('auto')
const selectedSignals = ref([])
const eventLog = ref([])

// 演示数据
const demoSignals = ref([
  {
    id: 'demo-discord-1',
    platform: 'discord',
    author_name: 'CryptoTrader',
    channel_name: 'signals',
    content: '🚀 BTC 突破关键阻力位！目标价格 $50,000。建议逢低买入。',
    created_at: new Date().toISOString(),
    ai_parse_status: 'completed',
    message_type_ai: 'signal',
    is_processed: false,
    confidence: 0.92,
    llm_service: 'openai',
    metadata: {
      discord: {
        author_avatar: 'https://cdn.discordapp.com/avatars/123456789/avatar.png',
        is_bot: false,
        embeds: [
          {
            title: 'BTC 技术分析',
            description: '比特币价格突破了重要的阻力位，技术指标显示强烈的看涨信号。',
            color: '#f1c40f',
            author: {
              name: 'TradingView',
              icon_url: 'https://tradingview.com/icon.png'
            },
            fields: [
              { name: '当前价格', value: '$48,500', inline: true },
              { name: '目标价格', value: '$50,000', inline: true },
              { name: '止损价格', value: '$46,000', inline: true }
            ]
          }
        ],
        attachments: [
          {
            filename: 'btc_chart.png',
            size: 245760,
            url: 'https://example.com/chart.png'
          }
        ],
        reactions: [
          { emoji: '🚀', count: 15 },
          { emoji: '💎', count: 8 },
          { emoji: '📈', count: 12 }
        ]
      }
    }
  },
  {
    id: 'demo-telegram-1',
    platform: 'telegram',
    author_name: 'AlgoTrader',
    channel_name: 'crypto_signals',
    content: 'ETH/USDT 长线信号：价格接近支撑位，预期反弹。',
    created_at: new Date(Date.now() - 3600000).toISOString(),
    ai_parse_status: 'completed',
    message_type_ai: 'signal',
    is_processed: true,
    confidence: 0.78,
    llm_service: 'claude',
    metadata: {
      telegram: {
        has_media: true,
        forward_from: 'Premium Signals'
      }
    }
  },
  {
    id: 'demo-manual-1',
    platform: 'manual',
    author_name: '手动输入',
    channel_name: null,
    content: '根据技术分析，DOGE 可能在未来24小时内出现突破。',
    created_at: new Date(Date.now() - 7200000).toISOString(),
    ai_parse_status: 'pending',
    message_type_ai: 'unknown',
    is_processed: false,
    confidence: null,
    llm_service: null,
    metadata: {}
  }
])

// 方法
const addEvent = (type, message) => {
  eventLog.value.unshift({
    type,
    message,
    timestamp: new Date().toLocaleTimeString()
  })
  
  // 限制日志条数
  if (eventLog.value.length > 20) {
    eventLog.value = eventLog.value.slice(0, 20)
  }
}

const handleCardClick = (signal) => {
  addEvent('点击', `点击了信号卡片: ${signal.author_name}`)
}

const handleSelectionChange = (signalId, selected) => {
  if (selected) {
    if (!selectedSignals.value.includes(signalId)) {
      selectedSignals.value.push(signalId)
    }
  } else {
    const index = selectedSignals.value.indexOf(signalId)
    if (index > -1) {
      selectedSignals.value.splice(index, 1)
    }
  }
  
  addEvent('选择', `信号 ${signalId} ${selected ? '已选中' : '已取消选中'}`)
}

const handleViewDetails = (signal) => {
  addEvent('查看详情', `查看信号详情: ${signal.author_name}`)
}

const handleToggleProcessed = (signal) => {
  // 更新信号状态
  const signalIndex = demoSignals.value.findIndex(s => s.id === signal.id)
  if (signalIndex > -1) {
    demoSignals.value[signalIndex].is_processed = !demoSignals.value[signalIndex].is_processed
  }
  
  addEvent('切换处理状态', `信号 ${signal.author_name} 处理状态已切换`)
}

const handleDelete = (signal) => {
  addEvent('删除', `删除信号: ${signal.author_name}`)
}

const handleThemeChange = (theme, signal) => {
  selectedTheme.value = theme
  addEvent('主题切换', `切换到 ${theme} 主题`)
}

const clearEventLog = () => {
  eventLog.value = []
}
</script>

<style scoped>
.v-container {
  max-width: 1200px;
}
</style>
