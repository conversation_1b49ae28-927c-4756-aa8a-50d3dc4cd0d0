<template>
  <v-container fluid class="dashboard-container" data-testid="dashboard">
    <!-- 用户欢迎区域 -->
    <v-row class="mb-4">
      <v-col cols="12">
        <div class="d-flex align-center justify-space-between">
          <div>
            <h1 class="text-h4 font-weight-bold mb-1">
              欢迎回来，<span data-testid="user-welcome">{{ authStore.user?.username || '用户' }}</span>
            </h1>
            <p class="text-subtitle-1 text-medium-emphasis mb-0">
              今天是 {{ new Date().toLocaleDateString('zh-CN', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                weekday: 'long'
              }) }}
            </p>
          </div>
          <v-chip
            :color="agentStore.agentState === 'idle' ? 'success' : 'warning'"
            size="large"
            variant="flat"
          >
            <v-icon class="mr-1">{{ getAgentStatusIcon(agentStore.agentState) }}</v-icon>
            Agent {{ getAgentStatusText(agentStore.agentState) }}
          </v-chip>
        </div>
      </v-col>
    </v-row>

    <!-- 统计卡片行 -->
    <v-row class="mb-6" data-testid="stats-cards">
      <v-col cols="12" md="3" v-for="stat in statsCards" :key="stat.title">
        <v-card class="stat-card" elevation="2">
          <v-card-text>
            <div class="d-flex align-center">
              <v-icon :color="stat.color" size="40" class="mr-4">
                {{ stat.icon }}
              </v-icon>
              <div>
                <div
                  class="text-h4 font-weight-bold"
                  :class="`${stat.color}--text`"
                  :data-testid="getStatTestId(stat.title)"
                >
                  {{ stat.value }}
                </div>
                <div class="text-subtitle-1 text-medium-emphasis">
                  {{ stat.title }}
                </div>
                <div v-if="stat.change" class="text-caption" :class="stat.changeColor">
                  {{ stat.change }}
                </div>
              </div>
            </div>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <v-row>
      <!-- 左侧：待处理动作和最近订单 -->
      <v-col cols="12" lg="8">
        <!-- 待处理动作列表 -->
        <v-card class="mb-6" elevation="2">
          <v-card-title class="d-flex align-center">
            <v-icon class="mr-2" color="warning">mdi-alert-circle</v-icon>
            待处理动作
            <v-chip 
              v-if="pendingActionsCount > 0" 
              class="ml-2" 
              color="warning" 
              size="small"
            >
              {{ pendingActionsCount }}
            </v-chip>
          </v-card-title>
          
          <v-card-text>
            <PendingActionsList :actions="pendingActions" />
          </v-card-text>
        </v-card>

        <!-- 最近订单 -->
        <v-card elevation="2">
          <v-card-title class="d-flex align-center justify-space-between">
            <div class="d-flex align-center">
              <v-icon class="mr-2" color="primary">mdi-format-list-bulleted</v-icon>
              最近订单
            </div>
            <v-btn 
              variant="text" 
              size="small" 
              to="/orders"
              append-icon="mdi-arrow-right"
            >
              查看全部
            </v-btn>
          </v-card-title>
          
          <v-card-text>
            <RecentOrdersTable 
              :orders="recentOrders"
              :loading="orderStore.loading"
              :items-per-page="5"
            />
          </v-card-text>
        </v-card>
      </v-col>

      <!-- 右侧：实时日志流 -->
      <v-col cols="12" lg="4">
        <v-card elevation="2" class="live-log-card">
          <v-card-title class="d-flex align-center justify-space-between">
            <div class="d-flex align-center">
              <v-icon class="mr-2" color="info">mdi-console</v-icon>
              实时日志
            </div>
            <v-btn 
              variant="text" 
              size="small" 
              @click="clearLogs"
              prepend-icon="mdi-delete-sweep"
            >
              清空
            </v-btn>
          </v-card-title>
          
          <v-card-text class="pa-0">
            <LiveLogStream :logs="agentLogs" />
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <!-- 紧急控制按钮 -->
    <v-row class="mt-6">
      <v-col cols="12">
        <v-card color="error" variant="outlined" class="emergency-controls">
          <v-card-title class="text-error d-flex align-center">
            <v-icon class="mr-2" size="24">mdi-alert-octagon</v-icon>
            紧急控制区
            <v-chip color="error" class="ml-2" size="small">警告</v-chip>
          </v-card-title>
          <v-card-text>
            <p class="text-body-2 mb-4">这些操作将立即生效，可能会影响您的交易状态。请谨慎操作。</p>
            <div class="d-flex gap-4">
              <v-btn
                color="warning"
                variant="elevated"
                size="large"
                class="emergency-btn"
                @click="pauseAllTrading"
                :loading="pausingTrading"
                prepend-icon="mdi-pause"
              >
                暂停所有交易
              </v-btn>
              <v-btn
                color="error"
                variant="elevated"
                size="large"
                class="emergency-btn"
                @click="confirmCloseAllPositions"
                :loading="closingPositions"
                prepend-icon="mdi-close-circle"
              >
                一键清仓所有头寸
              </v-btn>
            </div>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useAgentStore } from '@/stores/agent'
import { useOrderStore } from '@/stores/order'
import { useUIStore } from '@/stores/ui'
import { useAuthStore } from '@/stores/auth'
import { useWebSocketStore } from '@/stores/websocket'
import RecentOrdersTable from '@/components/RecentOrdersTable.vue'
import LiveLogStream from '@/components/LiveLogStream.vue'
import PendingActionsList from '@/components/PendingActionsList.vue'

// Stores
const agentStore = useAgentStore()
const orderStore = useOrderStore()
const uiStore = useUIStore()
const authStore = useAuthStore()
const wsStore = useWebSocketStore()

// 响应式状态
const pausingTrading = ref(false)
const closingPositions = ref(false)

// 计算属性
const pendingActions = computed(() => agentStore.activePendingActions || [])
const pendingActionsCount = computed(() => (pendingActions.value || []).length)
const recentOrders = computed(() => (orderStore.recentOrders || []).slice(0, 5))
const agentLogs = computed(() => agentStore.agentLogs)

// 统计卡片数据
const statsCards = computed(() => [
  {
    title: '总浮动盈亏',
    value: formatCurrency(orderStore.totalPnL || 0),
    icon: 'mdi-chart-line',
    color: (orderStore.totalPnL || 0) >= 0 ? 'success' : 'error',
    change: orderStore.totalPnLChange ? `${orderStore.totalPnLChange > 0 ? '+' : ''}${orderStore.totalPnLChange.toFixed(2)}%` : null,
    changeColor: (orderStore.totalPnLChange || 0) >= 0 ? 'success--text' : 'error--text'
  },
  {
    title: '活跃订单',
    value: orderStore.activeOrdersCount || 0,
    icon: 'mdi-format-list-checks',
    color: 'primary',
    change: null
  },
  {
    title: '24小时收益率',
    value: `${(orderStore.dailyReturn || 0).toFixed(2)}%`,
    icon: 'mdi-trending-up',
    color: (orderStore.dailyReturn || 0) >= 0 ? 'success' : 'error',
    change: null
  },
  {
    title: 'Agent状态',
    value: getAgentStatusText(agentStore.agentState),
    icon: getAgentStatusIcon(agentStore.agentState),
    color: getAgentStatusColor(agentStore.agentState),
    change: null
  }
])

// 方法
function getStatTestId(title) {
  const testIdMap = {
    '总浮动盈亏': 'total-pnl-stat',
    '活跃订单': 'total-orders-stat',
    '24小时收益率': 'daily-return-stat',
    'Agent状态': 'agent-status-stat'
  }
  return testIdMap[title] || title.toLowerCase().replace(/\s+/g, '-')
}

function formatCurrency(value) {
  if (value === null || value === undefined) return '$0.00'
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 2
  }).format(value)
}

function getAgentStatusText(status) {
  const statusMap = {
    idle: '空闲',
    processing: '处理中',
    waiting_confirmation: '等待确认',
    completed: '已完成',
    failed: '失败'
  }
  return statusMap[status] || status
}

function getAgentStatusIcon(status) {
  const iconMap = {
    idle: 'mdi-sleep',
    processing: 'mdi-cog',
    waiting_confirmation: 'mdi-account-question',
    completed: 'mdi-check-circle',
    failed: 'mdi-alert-circle'
  }
  return iconMap[status] || 'mdi-help-circle'
}

function getAgentStatusColor(status) {
  const colorMap = {
    idle: 'info',
    processing: 'warning',
    waiting_confirmation: 'orange',
    completed: 'success',
    failed: 'error'
  }
  return colorMap[status] || 'grey'
}

function getActionCardColor(action) {
  if (action.status === 'pending') return 'warning'
  if (action.status === 'approved') return 'success'
  if (action.status === 'rejected') return 'error'
  return 'grey'
}

function getActionStatusColor(status) {
  const colorMap = {
    pending: 'warning',
    approved: 'success',
    rejected: 'error',
    expired: 'grey'
  }
  return colorMap[status] || 'grey'
}

function getActionStatusText(status) {
  const statusMap = {
    pending: '待处理',
    approved: '已批准',
    rejected: '已拒绝',
    expired: '已过期'
  }
  return statusMap[status] || status
}

function isDirectionConfirmation(action) {
  // 检查是否是方向确认类型的动作
  return action.details.clarification_needed?.includes('交易方向') || 
         action.details.clarification_needed?.includes('做多还是做空')
}

function formatExpiryTime(expiresAt) {
  if (!expiresAt) return '无过期时间'
  
  const expiry = new Date(expiresAt)
  const now = new Date()
  const diff = expiry - now
  
  if (diff <= 0) return '已过期'
  
  const minutes = Math.floor(diff / 60000)
  const seconds = Math.floor((diff % 60000) / 1000)
  
  if (minutes > 0) {
    return `${minutes}分${seconds}秒后过期`
  } else {
    return `${seconds}秒后过期`
  }
}

async function respondToAction(actionId, response) {
  try {
    const action = (pendingActions.value || []).find(a => a.id === actionId)
    if (action) {
      action.responding = true
    }

    await agentStore.respondToPendingAction(actionId, response)
    uiStore.showSuccess('响应已提交')
  } catch (error) {
    console.error('Failed to respond to action:', error)
    uiStore.showError('响应提交失败: ' + error.message)
  } finally {
    const action = (pendingActions.value || []).find(a => a.id === actionId)
    if (action) {
      action.responding = false
    }
  }
}

async function pauseAllTrading() {
  const confirmed = await uiStore.showConfirmDialog({
    title: '确认暂停交易',
    message: '这将暂停所有自动交易功能，您确定要继续吗？',
    confirmText: '暂停',
    cancelText: '取消'
  })
  
  if (!confirmed) return
  
  try {
    pausingTrading.value = true
    await agentStore.pauseAllTrading()
    uiStore.showSuccess('所有交易已暂停')
  } catch (error) {
    console.error('Failed to pause trading:', error)
    uiStore.showError('暂停交易失败: ' + error.message)
  } finally {
    pausingTrading.value = false
  }
}

async function confirmCloseAllPositions() {
  const confirmed = await uiStore.showConfirmDialog({
    title: '确认一键清仓',
    message: '这将关闭所有活跃头寸，此操作不可撤销。您确定要继续吗？',
    confirmText: '清仓',
    cancelText: '取消'
  })
  
  if (!confirmed) return
  
  try {
    closingPositions.value = true
    await orderStore.closeAllPositions()
    uiStore.showSuccess('所有头寸已关闭')
  } catch (error) {
    console.error('Failed to close positions:', error)
    uiStore.showError('清仓失败: ' + error.message)
  } finally {
    closingPositions.value = false
  }
}

function clearLogs() {
  agentStore.clearLogs()
  uiStore.showInfo('日志已清空')
}

// 生命周期
onMounted(async () => {
  // 检查用户是否已认证
  if (!authStore.isAuthenticated) {
    console.warn('User not authenticated, skipping data fetch')
    return
  }

  // 连接WebSocket
  if (!wsStore.isConnected) {
    await wsStore.connect()
  }

  // 加载初始数据
  try {
    await Promise.all([
      orderStore.fetchOrders(),
      agentStore.fetchPendingActions()
    ])
  } catch (error) {
    console.error('Failed to load dashboard data:', error)
    uiStore.showError('加载数据失败: ' + error.message)
  }
})

onUnmounted(() => {
  // 组件卸载时不断开WebSocket连接，因为其他页面可能还需要
})
</script>

<style scoped>
.dashboard-container {
  max-width: 1400px;
  margin: 0 auto;
}

.stat-card {
  height: 120px;
  transition: transform 0.2s ease-in-out;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.pending-action-card {
  transition: all 0.2s ease-in-out;
}

.pending-action-card:hover {
  transform: translateY(-1px);
}

.live-log-card {
  height: 600px;
  display: flex;
  flex-direction: column;
}

.live-log-card .v-card-text {
  flex: 1;
  overflow: hidden;
}

.emergency-controls {
  border: 2px dashed rgb(var(--v-theme-error));
  position: relative;
}

.emergency-btn {
  min-width: 180px;
  font-weight: bold;
  letter-spacing: 0.5px;
  box-shadow: 0 4px 8px rgba(var(--v-theme-error), 0.3);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.emergency-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(var(--v-theme-error), 0.4);
}

.gap-2 {
  gap: 8px;
}

.gap-4 {
  gap: 16px;
}
</style>