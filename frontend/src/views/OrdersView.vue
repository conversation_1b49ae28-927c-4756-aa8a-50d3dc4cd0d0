<template>
  <div class="orders-view">
    <v-container fluid>
      <!-- 页面标题和操作栏 -->
      <v-row class="mb-4">
        <v-col cols="12">
          <div class="d-flex align-center justify-space-between">
            <div>
              <h1 class="text-h4 font-weight-bold">订单管理</h1>
              <p class="text-body-1 text-medium-emphasis mt-1">
                查看和管理所有交易订单
              </p>
            </div>
            
            <div class="d-flex gap-2">
              <v-btn
                color="error"
                variant="outlined"
                prepend-icon="mdi-close-circle"
                @click="closeAllPositions"
                :loading="orderStore.loading"
                data-testid="close-all-positions-button"
              >
                一键清仓
              </v-btn>

              <v-btn
                color="primary"
                variant="flat"
                prepend-icon="mdi-refresh"
                @click="refreshOrders"
                :loading="orderStore.loading"
                data-testid="refresh-button"
              >
                刷新
              </v-btn>
            </div>
          </div>
        </v-col>
      </v-row>

      <!-- 统计卡片 -->
      <v-row class="mb-4">
        <v-col cols="12" sm="6" md="3">
          <v-card>
            <v-card-text>
              <div class="d-flex align-center">
                <v-icon color="primary" size="40" class="mr-3">
                  mdi-format-list-bulleted
                </v-icon>
                <div>
                  <div class="text-h5 font-weight-bold">{{ stats.totalOrders }}</div>
                  <div class="text-body-2 text-medium-emphasis">总订单数</div>
                </div>
              </div>
            </v-card-text>
          </v-card>
        </v-col>
        
        <v-col cols="12" sm="6" md="3">
          <v-card>
            <v-card-text>
              <div class="d-flex align-center">
                <v-icon color="success" size="40" class="mr-3">
                  mdi-trending-up
                </v-icon>
                <div>
                  <div class="text-h5 font-weight-bold">{{ stats.activeOrders }}</div>
                  <div class="text-body-2 text-medium-emphasis">活跃订单</div>
                </div>
              </div>
            </v-card-text>
          </v-card>
        </v-col>
        
        <v-col cols="12" sm="6" md="3">
          <v-card>
            <v-card-text>
              <div class="d-flex align-center">
                <v-icon 
                  :color="stats.totalPnL >= 0 ? 'success' : 'error'" 
                  size="40" 
                  class="mr-3"
                >
                  {{ stats.totalPnL >= 0 ? 'mdi-trending-up' : 'mdi-trending-down' }}
                </v-icon>
                <div>
                  <div 
                    class="text-h5 font-weight-bold"
                    :class="stats.totalPnL >= 0 ? 'text-success' : 'text-error'"
                  >
                    {{ formatPnL(stats.totalPnL) }}
                  </div>
                  <div class="text-body-2 text-medium-emphasis">总盈亏</div>
                </div>
              </div>
            </v-card-text>
          </v-card>
        </v-col>
        
        <v-col cols="12" sm="6" md="3">
          <v-card>
            <v-card-text>
              <div class="d-flex align-center">
                <v-icon color="info" size="40" class="mr-3">
                  mdi-percent
                </v-icon>
                <div>
                  <div class="text-h5 font-weight-bold">{{ stats.winRate.toFixed(1) }}%</div>
                  <div class="text-body-2 text-medium-emphasis">胜率</div>
                </div>
              </div>
            </v-card-text>
          </v-card>
        </v-col>
      </v-row>

      <!-- 筛选和搜索 -->
      <v-row class="mb-4">
        <v-col cols="12">
          <v-card>
            <v-card-text>
              <v-row>
                <v-col cols="12" md="3">
                  <v-text-field
                    v-model="searchQuery"
                    label="搜索交易对"
                    prepend-inner-icon="mdi-magnify"
                    variant="outlined"
                    density="compact"
                    clearable
                    hide-details
                  />
                </v-col>
                
                <v-col cols="12" md="2">
                  <v-select
                    v-model="statusFilter"
                    :items="statusOptions"
                    label="状态筛选"
                    variant="outlined"
                    density="compact"
                    clearable
                    hide-details
                  />
                </v-col>
                
                <v-col cols="12" md="2">
                  <v-select
                    v-model="sideFilter"
                    :items="sideOptions"
                    label="方向筛选"
                    variant="outlined"
                    density="compact"
                    clearable
                    hide-details
                  />
                </v-col>
                
                <v-col cols="12" md="3">
                  <v-menu
                    v-model="dateMenu"
                    :close-on-content-click="false"
                    transition="scale-transition"
                    offset-y
                    min-width="auto"
                  >
                    <template v-slot:activator="{ props }">
                      <v-text-field
                        v-model="dateRangeText"
                        label="日期范围"
                        prepend-inner-icon="mdi-calendar"
                        variant="outlined"
                        density="compact"
                        readonly
                        v-bind="props"
                        clearable
                        @click:clear="clearDateRange"
                        hide-details
                      />
                    </template>
                    <v-date-picker
                      v-model="dateRange"
                      range
                      @update:model-value="dateMenu = false"
                    />
                  </v-menu>
                </v-col>
                
                <v-col cols="12" md="2">
                  <v-btn
                    color="primary"
                    variant="outlined"
                    block
                    @click="clearFilters"
                    prepend-icon="mdi-filter-off"
                  >
                    清除筛选
                  </v-btn>
                </v-col>
              </v-row>
            </v-card-text>
          </v-card>
        </v-col>
      </v-row>

      <!-- 订单表格 -->
      <v-row>
        <v-col cols="12">
          <v-card>
            <v-card-title class="d-flex align-center">
              <v-icon class="mr-2" color="primary">mdi-format-list-bulleted</v-icon>
              订单列表
              <v-spacer></v-spacer>
              <v-chip 
                v-if="filteredOrders.length !== orderStore.orders.length"
                color="info"
                size="small"
              >
                显示 {{ filteredOrders.length }} / {{ orderStore.orders.length }} 条
              </v-chip>
            </v-card-title>
            
            <v-card-text>
              <v-data-table
                :headers="headers"
                :items="filteredOrders"
                :loading="orderStore.loading"
                item-value="id"
                class="elevation-0"
                data-testid="orders-table"
                role="table"
                aria-label="订单列表表格"
                :items-per-page="itemsPerPage"
                :page="currentPage"
                @update:page="currentPage = $event"
                @update:items-per-page="itemsPerPage = $event"
              >
                <!-- 为表格行添加测试ID -->
                <template v-slot:body="{ items }">
                  <tbody>
                    <tr
                      v-for="item in items"
                      :key="item.id"
                      :data-testid="`order-row-${item.id}`"
                      class="v-data-table__tr"
                    >
                      <td class="v-data-table__td">
                        <div class="d-flex align-center">
                          <v-avatar size="32" class="mr-3">
                            <v-img :src="getCryptoIcon(item.symbol)" :alt="item.symbol" />
                          </v-avatar>
                          <div>
                            <div class="font-weight-medium">{{ item.symbol }}</div>
                            <div class="text-caption text-medium-emphasis">
                              {{ item.exchange || 'Unknown' }}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td class="v-data-table__td">
                        <v-chip
                          :color="item.side === 'buy' ? 'success' : 'error'"
                          size="small"
                          variant="flat"
                        >
                          <v-icon start size="16">
                            {{ item.side === 'buy' ? 'mdi-trending-up' : 'mdi-trending-down' }}
                          </v-icon>
                          {{ item.side === 'buy' ? '做多' : '做空' }}
                        </v-chip>
                      </td>
                      <td class="v-data-table__td text-right">
                        <div class="font-mono font-weight-medium">{{ formatQuantity(item.quantity) }}</div>
                        <div class="text-caption text-medium-emphasis">
                          ${{ formatNumber((item.quantity || 0) * (item.entry_price || 0)) }}
                        </div>
                      </td>
                      <td class="v-data-table__td text-right">
                        <div class="font-mono">${{ formatPrice(item.entry_price) }}</div>
                        <div class="text-caption text-medium-emphasis">开仓价</div>
                      </td>
                      <td class="v-data-table__td text-right">
                        <div class="font-mono">${{ formatPrice(item.current_price || item.entry_price) }}</div>
                        <div
                          class="text-caption d-flex align-center justify-end"
                          :class="getPriceChangeColor(item.price_change)"
                        >
                          <v-icon
                            v-if="item.price_change !== undefined"
                            size="12"
                            class="mr-1"
                          >
                            {{ item.price_change >= 0 ? 'mdi-trending-up' : 'mdi-trending-down' }}
                          </v-icon>
                          {{ formatPercentage(item.price_change || 0) }}
                        </div>
                      </td>
                      <td class="v-data-table__td text-right">
                        <div
                          class="font-mono font-weight-medium"
                          :class="getPnLColor(item.pnl)"
                        >
                          {{ formatPnL(item.pnl || 0) }}
                        </div>
                        <div
                          v-if="item.pnl_percentage !== undefined"
                          class="text-caption"
                          :class="getPnLColor(item.pnl)"
                        >
                          {{ formatPercentage(item.pnl_percentage) }}
                        </div>
                      </td>
                      <td class="v-data-table__td">
                        <v-chip
                          :color="getStatusColor(item.status)"
                          size="small"
                          variant="flat"
                        >
                          {{ getStatusText(item.status) }}
                        </v-chip>
                      </td>
                      <td class="v-data-table__td text-right">
                        <div class="text-body-2">{{ formatDate(item.created_at) }}</div>
                        <div class="text-caption text-medium-emphasis">
                          {{ formatTime(item.created_at) }}
                        </div>
                      </td>
                      <td class="v-data-table__td">
                        <div class="d-flex gap-1">
                          <v-btn
                            v-if="item.status === 'active'"
                            icon="mdi-close"
                            size="small"
                            variant="text"
                            color="error"
                            @click="closeOrder(item)"
                            :loading="item.closing"
                            :data-testid="`close-order-${item.id}`"
                          >
                            <v-icon>mdi-close</v-icon>
                            <v-tooltip activator="parent" location="top">
                              平仓
                            </v-tooltip>
                          </v-btn>

                          <v-btn
                            icon="mdi-information"
                            size="small"
                            variant="text"
                            color="info"
                            @click="showOrderDetails(item)"
                            :data-testid="`details-button-${item.id}`"
                          >
                            <v-icon>mdi-information</v-icon>
                            <v-tooltip activator="parent" location="top">
                              详情
                            </v-tooltip>
                          </v-btn>

                          <v-btn
                            icon="mdi-chart-line"
                            size="small"
                            variant="text"
                            color="primary"
                            @click="viewChart(item)"
                          >
                            <v-icon>mdi-chart-line</v-icon>
                            <v-tooltip activator="parent" location="top">
                              图表
                            </v-tooltip>
                          </v-btn>
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </template>


                <!-- 空状态 -->
                <template v-slot:no-data>
                  <div class="text-center py-8" data-testid="empty-orders-state">
                    <v-icon size="64" color="grey-lighten-1" class="mb-4">
                      mdi-format-list-bulleted-square
                    </v-icon>
                    <div class="text-h6 text-medium-emphasis" data-testid="empty-orders-message">暂无订单数据</div>
                    <div class="text-body-2 text-medium-emphasis">
                      {{ hasFilters ? '没有符合筛选条件的订单' : '开始交易后，订单信息将在这里显示' }}
                    </div>
                    <v-btn
                      v-if="hasFilters"
                      color="primary"
                      variant="outlined"
                      class="mt-4"
                      @click="clearFilters"
                    >
                      清除筛选
                    </v-btn>
                  </div>
                </template>
              </v-data-table>
            </v-card-text>
          </v-card>
        </v-col>
      </v-row>
    </v-container>

    <!-- 订单详情对话框 -->
    <v-dialog v-model="showDetailsDialog" max-width="800">
      <v-card v-if="selectedOrder">
        <v-card-title class="d-flex align-center">
          <v-icon class="mr-2" color="primary">mdi-information</v-icon>
          订单详情 - {{ selectedOrder.symbol }}
        </v-card-title>
        
        <v-card-text>
          <v-row>
            <v-col cols="12" md="6">
              <v-card variant="outlined">
                <v-card-subtitle>基本信息</v-card-subtitle>
                <v-card-text>
                  <v-row dense>
                    <v-col cols="6">
                      <div class="text-caption text-medium-emphasis">订单ID</div>
                      <div class="font-mono text-body-2">{{ selectedOrder.id }}</div>
                    </v-col>
                    <v-col cols="6">
                      <div class="text-caption text-medium-emphasis">交易所订单ID</div>
                      <div class="font-mono text-body-2">{{ selectedOrder.exchange_order_id || 'N/A' }}</div>
                    </v-col>
                    <v-col cols="6">
                      <div class="text-caption text-medium-emphasis">交易对</div>
                      <div class="text-body-2">{{ selectedOrder.symbol }}</div>
                    </v-col>
                    <v-col cols="6">
                      <div class="text-caption text-medium-emphasis">交易所</div>
                      <div class="text-body-2">{{ selectedOrder.exchange || 'N/A' }}</div>
                    </v-col>
                    <v-col cols="6">
                      <div class="text-caption text-medium-emphasis">方向</div>
                      <v-chip 
                        :color="selectedOrder.side === 'buy' ? 'success' : 'error'"
                        size="small"
                      >
                        {{ selectedOrder.side === 'buy' ? '做多' : '做空' }}
                      </v-chip>
                    </v-col>
                    <v-col cols="6">
                      <div class="text-caption text-medium-emphasis">状态</div>
                      <v-chip 
                        :color="getStatusColor(selectedOrder.status)"
                        size="small"
                      >
                        {{ getStatusText(selectedOrder.status) }}
                      </v-chip>
                    </v-col>
                  </v-row>
                </v-card-text>
              </v-card>
            </v-col>
            
            <v-col cols="12" md="6">
              <v-card variant="outlined">
                <v-card-subtitle>交易数据</v-card-subtitle>
                <v-card-text>
                  <v-row dense>
                    <v-col cols="6">
                      <div class="text-caption text-medium-emphasis">数量</div>
                      <div class="text-body-2">{{ formatQuantity(selectedOrder.quantity) }}</div>
                    </v-col>
                    <v-col cols="6">
                      <div class="text-caption text-medium-emphasis">金额 (USD)</div>
                      <div class="text-body-2">${{ formatNumber(selectedOrder.quantity_usd) }}</div>
                    </v-col>
                    <v-col cols="6">
                      <div class="text-caption text-medium-emphasis">开仓价</div>
                      <div class="text-body-2">${{ formatPrice(selectedOrder.entry_price) }}</div>
                    </v-col>
                    <v-col cols="6">
                      <div class="text-caption text-medium-emphasis">当前价</div>
                      <div class="text-body-2">${{ formatPrice(selectedOrder.current_price) }}</div>
                    </v-col>
                    <v-col cols="6">
                      <div class="text-caption text-medium-emphasis">浮动盈亏</div>
                      <div class="text-body-2" :class="getPnLColor(selectedOrder.pnl)">
                        {{ formatPnL(selectedOrder.pnl) }}
                      </div>
                    </v-col>
                    <v-col cols="6">
                      <div class="text-caption text-medium-emphasis">盈亏比例</div>
                      <div class="text-body-2" :class="getPnLColor(selectedOrder.pnl)">
                        {{ formatPercentage(selectedOrder.pnl_percentage) }}
                      </div>
                    </v-col>
                  </v-row>
                </v-card-text>
              </v-card>
            </v-col>
            
            <v-col cols="12">
              <v-card variant="outlined">
                <v-card-subtitle>时间信息</v-card-subtitle>
                <v-card-text>
                  <v-row dense>
                    <v-col cols="6">
                      <div class="text-caption text-medium-emphasis">创建时间</div>
                      <div class="text-body-2">{{ formatDateTime(selectedOrder.created_at) }}</div>
                    </v-col>
                    <v-col cols="6">
                      <div class="text-caption text-medium-emphasis">更新时间</div>
                      <div class="text-body-2">{{ formatDateTime(selectedOrder.updated_at) }}</div>
                    </v-col>
                    <v-col cols="6" v-if="selectedOrder.closed_at">
                      <div class="text-caption text-medium-emphasis">关闭时间</div>
                      <div class="text-body-2">{{ formatDateTime(selectedOrder.closed_at) }}</div>
                    </v-col>
                  </v-row>
                </v-card-text>
              </v-card>
            </v-col>
            
            <v-col cols="12" v-if="selectedOrder.agent_log">
              <v-card variant="outlined">
                <v-card-subtitle>Agent日志</v-card-subtitle>
                <v-card-text>
                  <v-code class="text-body-2">
                    {{ JSON.stringify(selectedOrder.agent_log, null, 2) }}
                  </v-code>
                </v-card-text>
              </v-card>
            </v-col>
          </v-row>
        </v-card-text>
        
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn 
            variant="text" 
            @click="showDetailsDialog = false"
          >
            关闭
          </v-btn>
          <v-btn
            v-if="selectedOrder.status === 'ACTIVE'"
            color="error"
            variant="outlined"
            @click="closeOrder(selectedOrder)"
            :loading="selectedOrder.closing"
          >
            平仓
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useOrderStore } from '@/stores/order'
import { useUIStore } from '@/stores/ui'

// Stores
const orderStore = useOrderStore()
const uiStore = useUIStore()

// 响应式状态
const searchQuery = ref('')
const statusFilter = ref(null)
const sideFilter = ref(null)
const dateRange = ref([])
const dateMenu = ref(false)
const currentPage = ref(1)
const itemsPerPage = ref(25)
const showDetailsDialog = ref(false)
const selectedOrder = ref(null)

// 筛选选项
const statusOptions = [
  { title: '活跃', value: 'ACTIVE' },
  { title: '已关闭', value: 'CLOSED' },
  { title: '失败', value: 'FAILED' },
  { title: '已取消', value: 'CANCELLED' },
  { title: '待处理', value: 'PENDING' }
]

const sideOptions = [
  { title: '做多', value: 'buy' },
  { title: '做空', value: 'sell' }
]

// 表格头部配置
const headers = [
  { title: '交易对', key: 'symbol', sortable: true, width: '150px' },
  { title: '方向', key: 'side', sortable: true, width: '100px' },
  { title: '数量', key: 'quantity', sortable: true, width: '120px' },
  { title: '开仓价', key: 'entry_price', sortable: true, width: '100px' },
  { title: '当前价', key: 'current_price', sortable: true, width: '100px' },
  { title: '浮动盈亏', key: 'pnl', sortable: true, width: '120px' },
  { title: '状态', key: 'status', sortable: true, width: '100px' },
  { title: '创建时间', key: 'created_at', sortable: true, width: '120px' },
  { title: '操作', key: 'actions', sortable: false, width: '120px' }
]

// 计算属性
const stats = computed(() => ({
  totalOrders: orderStore.orders.length,
  activeOrders: orderStore.orders.filter(o => o.status === 'ACTIVE').length,
  totalPnL: orderStore.orders.reduce((sum, order) => sum + (order.pnl || 0), 0),
  winRate: calculateWinRate()
}))

const filteredOrders = computed(() => {
  let orders = [...orderStore.orders]
  
  // 搜索筛选
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    orders = orders.filter(order => 
      order.symbol.toLowerCase().includes(query)
    )
  }
  
  // 状态筛选
  if (statusFilter.value) {
    orders = orders.filter(order => order.status === statusFilter.value)
  }
  
  // 方向筛选
  if (sideFilter.value) {
    orders = orders.filter(order => order.side === sideFilter.value)
  }
  
  // 日期范围筛选
  if (dateRange.value && dateRange.value.length === 2) {
    const [startDate, endDate] = dateRange.value
    orders = orders.filter(order => {
      const orderDate = new Date(order.created_at)
      return orderDate >= startDate && orderDate <= endDate
    })
  }
  
  return orders
})

const dateRangeText = computed(() => {
  if (!dateRange.value || dateRange.value.length !== 2) return ''
  const [start, end] = dateRange.value
  return `${formatDate(start)} - ${formatDate(end)}`
})

const hasFilters = computed(() => {
  return searchQuery.value || statusFilter.value || sideFilter.value || 
         (dateRange.value && dateRange.value.length === 2)
})

// 方法
function calculateWinRate() {
  const closedOrders = orderStore.orders.filter(o => o.status === 'CLOSED')
  if (closedOrders.length === 0) return 0
  const winningOrders = closedOrders.filter(o => (o.pnl || 0) > 0)
  return (winningOrders.length / closedOrders.length) * 100
}

function getCryptoIcon(symbol) {
  const baseCurrency = symbol.split('/')[0].toLowerCase()
  return `https://cryptoicons.org/api/icon/${baseCurrency}/32`
}

function formatQuantity(quantity) {
  if (quantity === null || quantity === undefined) return '0'
  return parseFloat(quantity).toFixed(6)
}

function formatPrice(price) {
  if (price === null || price === undefined) return '0.00'
  return parseFloat(price).toFixed(2)
}

function formatNumber(value) {
  if (value === null || value === undefined) return '0'
  return parseFloat(value).toLocaleString()
}

function formatPnL(pnl) {
  if (pnl === null || pnl === undefined) return '$0.00'
  const value = parseFloat(pnl)
  const sign = value >= 0 ? '+' : ''
  return `${sign}$${value.toFixed(2)}`
}

function formatPercentage(percentage) {
  if (percentage === null || percentage === undefined) return '0.00%'
  const value = parseFloat(percentage)
  const sign = value >= 0 ? '+' : ''
  return `${sign}${value.toFixed(2)}%`
}

function formatDate(date) {
  if (!date) return 'N/A'
  return new Date(date).toLocaleDateString('zh-CN')
}

function formatTime(date) {
  if (!date) return 'N/A'
  return new Date(date).toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit'
  })
}

function formatDateTime(dateTime) {
  if (!dateTime) return 'N/A'
  return new Date(dateTime).toLocaleString('zh-CN')
}

function getPnLColor(pnl) {
  if (pnl === null || pnl === undefined) return ''
  const value = parseFloat(pnl)
  if (value > 0) return 'text-success'
  if (value < 0) return 'text-error'
  return ''
}

function getPriceChangeColor(change) {
  if (change === null || change === undefined) return ''
  const value = parseFloat(change)
  if (value > 0) return 'text-success'
  if (value < 0) return 'text-error'
  return ''
}

function getStatusColor(status) {
  const colorMap = {
    'ACTIVE': 'success',
    'CLOSED': 'info',
    'FAILED': 'error',
    'CANCELLED': 'warning',
    'PENDING': 'orange'
  }
  return colorMap[status] || 'grey'
}

function getStatusText(status) {
  const textMap = {
    'ACTIVE': '活跃',
    'CLOSED': '已关闭',
    'FAILED': '失败',
    'CANCELLED': '已取消',
    'PENDING': '待处理'
  }
  return textMap[status] || status
}

function clearFilters() {
  searchQuery.value = ''
  statusFilter.value = null
  sideFilter.value = null
  dateRange.value = []
}

function clearDateRange() {
  dateRange.value = []
}

async function refreshOrders() {
  try {
    await orderStore.fetchOrders()
    uiStore.showSuccess('订单数据已刷新')
  } catch (error) {
    console.error('Failed to refresh orders:', error)
    uiStore.showError('刷新失败: ' + error.message)
  }
}

async function closeOrder(order) {
  const confirmed = await uiStore.showConfirmDialog({
    title: '确认平仓',
    message: `确定要平仓 ${order.symbol} ${order.side === 'buy' ? '多' : '空'}单吗？`,
    confirmText: '平仓',
    cancelText: '取消'
  })
  
  if (!confirmed) return
  
  try {
    order.closing = true
    await orderStore.closeOrder(order.id)
    uiStore.showSuccess('平仓指令已提交')
    showDetailsDialog.value = false
  } catch (error) {
    console.error('Failed to close order:', error)
    uiStore.showError('平仓失败: ' + error.message)
  } finally {
    order.closing = false
  }
}

async function closeAllPositions() {
  const activeOrders = orderStore.orders.filter(o => o.status === 'ACTIVE')
  if (activeOrders.length === 0) {
    uiStore.showWarning('没有活跃的头寸需要关闭')
    return
  }
  
  const confirmed = await uiStore.showConfirmDialog({
    title: '确认一键清仓',
    message: `确定要关闭所有 ${activeOrders.length} 个活跃头寸吗？这个操作不可撤销！`,
    confirmText: '清仓',
    cancelText: '取消'
  })
  
  if (!confirmed) return
  
  try {
    await orderStore.closeAllPositions()
    uiStore.showSuccess('清仓指令已提交')
  } catch (error) {
    console.error('Failed to close all positions:', error)
    uiStore.showError('清仓失败: ' + error.message)
  }
}

function showOrderDetails(order) {
  selectedOrder.value = order
  showDetailsDialog.value = true
}

function viewChart(order) {
  // TODO: 实现图表查看功能
  uiStore.showInfo('图表功能即将推出')
}

// 生命周期
onMounted(async () => {
  await refreshOrders()
})
</script>

<style scoped>
.font-mono {
  font-family: 'Roboto Mono', monospace;
}

.gap-1 {
  gap: 4px;
}

.gap-2 {
  gap: 8px;
}

:deep(.v-data-table__td) {
  padding: 12px 16px !important;
}

:deep(.v-data-table-header__content) {
  font-weight: 600;
}
</style>