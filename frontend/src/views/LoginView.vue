<template>
  <v-container class="fill-height">
    <v-row class="fill-height" align="center" justify="center">
      <v-col cols="12" sm="8" md="6" lg="4">
        <v-card elevation="4">
          <v-card-title class="text-center pt-5 pb-4">
            <v-icon size="36" color="primary" class="mb-3">mdi-robot</v-icon>
            <h1 class="text-h4 font-weight-bold" data-testid="page-title">AI Agent 智能跟单系统</h1>
            <div class="text-subtitle-1 text-medium-emphasis">请登录以继续</div>
          </v-card-title>
          
          <v-card-text class="pt-0">
            <v-form ref="form" v-model="isFormValid" @submit.prevent="handleLogin">
              <v-text-field
                v-model="username"
                label="用户名"
                prepend-inner-icon="mdi-account"
                variant="outlined"
                :rules="[v => !!v || '请输入用户名']"
                autofocus
                autocomplete="username"
                name="username"
                data-testid="username-input"
                aria-label="用户名输入框"
                role="textbox"
                :aria-required="true"
                :aria-invalid="!username && error ? 'true' : 'false'"
              ></v-text-field>
              
              <v-text-field
                v-model="password"
                label="密码"
                prepend-inner-icon="mdi-lock"
                variant="outlined"
                :rules="[v => !!v || '请输入密码']"
                :type="showPassword ? 'text' : 'password'"
                autocomplete="current-password"
                name="password"
                data-testid="password-input"
                aria-label="密码输入框"
                role="textbox"
                :aria-required="true"
                :aria-invalid="!password && error ? 'true' : 'false'"
              >
                <template #append-inner>
                  <v-btn
                    icon
                    variant="text"
                    size="small"
                    @click="showPassword = !showPassword"
                    :aria-label="showPassword ? '隐藏密码' : '显示密码'"
                    data-testid="password-toggle"
                  >
                    <v-icon>{{ showPassword ? 'mdi-eye' : 'mdi-eye-off' }}</v-icon>
                  </v-btn>
                </template>
              </v-text-field>
              
              <div
                v-if="error"
                class="text-error text-center mb-3"
                role="alert"
                aria-live="assertive"
                data-testid="error-message"
              >
                <v-icon class="mr-1" aria-hidden="true">mdi-alert-circle</v-icon>
                {{ error }}
              </div>
              
              <v-btn
                color="primary"
                block
                size="large"
                :disabled="!isFormValid"
                :loading="loading"
                class="mt-4"
                @click="handleLogin"
                data-testid="login-button"
                type="submit"
                aria-label="登录到系统"
                :aria-describedby="error ? 'login-error' : undefined"
              >
                登录
              </v-btn>
              
              <div class="text-center mt-3">
                <a
                  href="#"
                  @click.prevent="forgotPassword"
                  class="text-decoration-none text-body-2"
                  aria-label="忘记密码帮助"
                  role="button"
                  tabindex="0"
                  @keydown.enter="forgotPassword"
                  @keydown.space.prevent="forgotPassword"
                >
                  忘记密码?
                </a>
              </div>
            </v-form>
          </v-card-text>
          

        </v-card>
        
        <div class="text-center mt-6">
          <div class="text-body-2 text-medium-emphasis">
            © {{ new Date().getFullYear() }} AI Agent 智能跟单系统
          </div>
        </div>
      </v-col>
    </v-row>
  </v-container>
</template>

<script setup>
import { ref, onMounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useUIStore } from '@/stores/ui'

const router = useRouter()
const authStore = useAuthStore()
const uiStore = useUIStore()

// 状态 - 预填充demo账户信息
const form = ref(null)
const username = ref('demo')
const password = ref('password123')
const showPassword = ref(false)
const error = ref('')
const loading = ref(false)
const isFormValid = ref(true) // 预填充数据时设为true，避免测试中的禁用问题

// 组件挂载时检查认证状态
onMounted(async () => {
  // 如果已经登录，重定向到仪表板
  if (authStore.isAuthenticated) {
    router.push('/dashboard')
    return
  }

  // 清除可能的错误状态
  authStore.clearError()
  error.value = ''

  // 等待下一个tick，然后验证表单
  await nextTick()
  if (form.value) {
    await form.value.validate()
  }
})

// 方法
async function handleLogin() {
  if (!isFormValid.value) return

  error.value = ''
  loading.value = true

  try {
    await authStore.login({
      username: username.value,
      password: password.value
    })

    // 登录成功后跳转到仪表板
    await router.push('/dashboard')
    uiStore.showSuccess('登录成功')
  } catch (err) {
    console.error('Login error:', err)
    error.value = err?.message || '登录失败，请检查用户名和密码'
  } finally {
    loading.value = false
  }
}

function forgotPassword() {
  uiStore.showInfo('请联系管理员重置您的密码', '密码找回')
}
</script>

<style scoped>
.v-card-title {
  display: flex;
  flex-direction: column;
}
</style>
