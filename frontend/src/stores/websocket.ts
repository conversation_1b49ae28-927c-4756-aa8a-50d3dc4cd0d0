import { defineStore } from 'pinia'
import { ref } from 'vue'
import { useAuthStore } from './auth'
import { useUIStore } from './ui'
import type { WebSocketMessage } from '@/types'
import { websocketHandler } from '@/utils/websocketHandler'

export const useWebSocketStore = defineStore('websocket', () => {
  // Stores
  const authStore = useAuthStore()
  const uiStore = useUIStore()
  
  // 状态
  const socket = ref<WebSocket | null>(null)
  const isConnected = ref<boolean>(false)
  const isConnecting = ref<boolean>(false)
  const isDisconnected = ref<boolean>(true)
  const reconnectAttempts = ref<number>(0)
  const maxReconnectAttempts = 10
  const reconnectInterval = ref<number>(1000) // 初始重连间隔1秒
  const reconnectTimer = ref<number | null>(null)
  const heartbeatInterval = ref<number | null>(null)
  const lastMessageTime = ref<number>(Date.now())
  const lastMessage = ref<any>(null)
  const lastError = ref<string | null>(null)
  const messageHistory = ref<any[]>([])
  const messageCount = ref<number>(0)
  const subscriptions = ref<Set<string>>(new Set())
  const eventListeners = ref<Record<string, Function[]>>({})
  const lastConnectedAt = ref<number | null>(null)
  const config = ref({
    url: '',
    autoReconnect: true,
    reconnectInterval: 1000,
    maxReconnectAttempts: 10,
    heartbeatInterval: 30000
  })
  
  // 方法
  async function connect() {
    if (isConnected.value || isConnecting.value || socket.value) {
      console.log('WebSocket already connected or connecting')
      return
    }

    try {
      const token = authStore.token
      if (!token) {
        console.error('No token available for WebSocket connection')
        return
      }

      isConnecting.value = true
      isDisconnected.value = false
      lastError.value = null

      // 清理之前的连接和计时器
      cleanup()

      // 构造WebSocket URL - 不在URL中暴露token
      let wsUrl
      if (import.meta.env.VITE_WS_URL) {
        wsUrl = `${import.meta.env.VITE_WS_URL}/ws`
      } else {
        const wsProtocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:'
        const wsHost = window.location.hostname === 'localhost' ? 'localhost:8000' : window.location.host
        wsUrl = `${wsProtocol}//${wsHost}/ws`
      }

      config.value.url = wsUrl
      console.log(`正在连接WebSocket: ${wsUrl}`)
      socket.value = new WebSocket(wsUrl)

      socket.value.onopen = handleOpen
      socket.value.onclose = handleClose
      socket.value.onmessage = handleMessage
      socket.value.onerror = handleError

    } catch (error) {
      console.error('WebSocket连接失败:', error)
      isConnecting.value = false
      isDisconnected.value = true
      lastError.value = error instanceof Error ? error.message : String(error)
      scheduleReconnect()
    }
  }
  
  function handleOpen() {
    console.log('WebSocket连接成功')

    // 连接建立后发送认证信息
    const token = authStore.token
    if (token && socket.value) {
      socket.value.send(JSON.stringify({
        type: 'auth',
        token: token
      }))
    }

    isConnected.value = true
    isConnecting.value = false
    isDisconnected.value = false
    reconnectAttempts.value = 0
    reconnectInterval.value = 1000 // 重置重连间隔
    lastError.value = null
    lastConnectedAt.value = Date.now()

    // 设置心跳检测
    startHeartbeat()

    // 触发连接事件
    triggerEventListeners('connect', null)

    // 通知UI
    uiStore.showSuccess('WebSocket已连接')
  }
  
  function handleClose(event: CloseEvent) {
    console.log(`WebSocket关闭: ${event.code} - ${event.reason}`)
    isConnected.value = false
    isConnecting.value = false
    isDisconnected.value = true

    // 停止心跳
    if (heartbeatInterval.value !== null) {
      clearInterval(heartbeatInterval.value)
      heartbeatInterval.value = null
    }

    // 尝试重连
    if (config.value.autoReconnect) {
      scheduleReconnect()
    }

    // 通知UI
    uiStore.showWarning('WebSocket断开连接，正在尝试重连...')
  }

  function handleError(error: Event) {
    console.error('WebSocket错误:', error)

    // 提取错误消息
    let errorMessage = 'WebSocket error'
    if (error instanceof ErrorEvent) {
      errorMessage = error.message
    } else if (error instanceof Error) {
      errorMessage = error.message
    } else if (typeof error === 'object' && error !== null && 'message' in error) {
      errorMessage = (error as any).message
    }

    lastError.value = errorMessage
    isConnecting.value = false
    isConnected.value = false
    isDisconnected.value = true

    // 触发错误事件
    triggerEventListeners('error', error)

    // 通知UI
    uiStore.showError('WebSocket出错')
  }
  
  function handleMessage(event: MessageEvent) {
    try {
      lastMessageTime.value = Date.now()
      messageCount.value++
      const message: WebSocketMessage = JSON.parse(event.data)

      // 存储最后一条消息
      lastMessage.value = message

      // 添加到消息历史（最新的在最后）
      messageHistory.value.push(message)
      if (messageHistory.value.length > 100) {
        messageHistory.value = messageHistory.value.slice(-100)
      }

      // 处理错误消息
      if (message.type === 'error') {
        lastError.value = message.message || 'Unknown error'
        triggerEventListeners('error', message)
        return
      }

      // 处理服务器的心跳响应
      if (message.event_type === 'HEARTBEAT' || message.event_type === 'PONG' || message.type === 'pong') {
        console.debug('收到服务器心跳响应')
        return
      }

      // 处理ping消息，自动回复pong
      if (message.event_type === 'PING' || message.type === 'ping') {
        console.debug('收到ping消息，回复pong')
        sendMessage({ type: 'pong', timestamp: Date.now() })
        return
      }

      // 触发消息事件
      triggerEventListeners('message', message)

      // 统一消息分发处理
      handleWebSocketMessage(message)

    } catch (error) {
      console.error('处理WebSocket消息时出错:', error)
      lastError.value = error instanceof Error ? error.message : String(error)
    }
  }

  // 统一的WebSocket消息处理
  function handleWebSocketMessage(message: WebSocketMessage) {
    // 使用统一的消息处理器
    websocketHandler.handleMessage(message)
  }
  
  function startHeartbeat() {
    // 每30秒检查一次连接状态
    if (heartbeatInterval.value !== null) {
      clearInterval(heartbeatInterval.value)
    }
    heartbeatInterval.value = setInterval(() => {
      const now = Date.now()
      // 如果60秒没有收到任何消息，则认为连接可能已断开
      if (now - lastMessageTime.value > 60000) {
        console.warn('60秒内未收到WebSocket消息，正在重连...')
        reconnect()
      } else if (socket.value && socket.value.readyState === WebSocket.OPEN) {
        // 发送心跳包
        socket.value.send(JSON.stringify({ type: 'ping', timestamp: now }))
      }
    }, 30000) as unknown as number
  }
  
  function scheduleReconnect() {
    if (reconnectTimer.value !== null) {
      clearTimeout(reconnectTimer.value)
    }
    
    if (reconnectAttempts.value >= maxReconnectAttempts) {
      console.warn('已达到最大重连次数，放弃重连')
      uiStore.showError('WebSocket连接失败，请刷新页面重试')
      return
    }
    
    // 使用指数退避算法计算下次重连时间
    const delay = Math.min(30000, reconnectInterval.value * Math.pow(1.5, reconnectAttempts.value))
    console.log(`计划${delay}ms后重连 (尝试 ${reconnectAttempts.value + 1})`)
    
    reconnectTimer.value = setTimeout(() => {
      reconnectAttempts.value++
      reconnect()
    }, delay) as unknown as number
  }
  
  function reconnect() {
    console.log(`正在尝试重连 (${reconnectAttempts.value}/${maxReconnectAttempts})`)
    cleanup()
    connect()
  }
  
  function cleanup() {
    if (socket.value) {
      // 移除所有事件监听器
      socket.value.onopen = null
      socket.value.onclose = null
      socket.value.onmessage = null
      socket.value.onerror = null

      // 关闭连接
      if (socket.value.readyState === WebSocket.OPEN ||
          socket.value.readyState === WebSocket.CONNECTING) {
        socket.value.close()
      }

      socket.value = null
    }

    // 清除所有定时器
    if (reconnectTimer.value !== null) {
      clearTimeout(reconnectTimer.value)
      reconnectTimer.value = null
    }
    if (heartbeatInterval.value !== null) {
      clearInterval(heartbeatInterval.value)
      heartbeatInterval.value = null
    }

    isConnected.value = false
    isConnecting.value = false
    isDisconnected.value = true
  }
  
  function disconnect() {
    console.log('手动断开WebSocket连接')
    cleanup()
  }
  
  // 发送消息
  function sendMessage(message: any) {
    if (!isConnected.value || !socket.value) {
      console.warn('WebSocket not connected, cannot send message')
      return false
    }

    try {
      const messageStr = typeof message === 'string' ? message : JSON.stringify(message)
      socket.value.send(messageStr)
      return true
    } catch (error) {
      console.error('Failed to send WebSocket message:', error)
      lastError.value = error instanceof Error ? error.message : String(error)
      return false
    }
  }

  // 订阅频道
  function subscribe(channel: string) {
    subscriptions.value.add(channel)
    return sendMessage({
      type: 'subscribe',
      channel: channel
    })
  }

  // 取消订阅
  function unsubscribe(channel: string) {
    subscriptions.value.delete(channel)
    return sendMessage({
      type: 'unsubscribe',
      channel: channel
    })
  }

  // 添加事件监听器
  function addEventListener(event: string, listener: Function) {
    if (!eventListeners.value[event]) {
      eventListeners.value[event] = []
    }
    eventListeners.value[event].push(listener)
  }

  // 移除事件监听器
  function removeEventListener(event: string, listener: Function) {
    if (eventListeners.value[event]) {
      const index = eventListeners.value[event].indexOf(listener)
      if (index > -1) {
        eventListeners.value[event].splice(index, 1)
      }
    }
  }

  // 触发事件监听器
  function triggerEventListeners(event: string, data: any) {
    if (eventListeners.value[event]) {
      eventListeners.value[event].forEach(listener => {
        try {
          listener(data)
        } catch (error) {
          console.error('Error in event listener:', error)
        }
      })
    }
  }

  // 获取连接状态
  function getConnectionState() {
    if (isConnected.value) return 'connected'
    if (isConnecting.value) return 'connecting'
    if (isDisconnected.value) return 'disconnected'
    return 'unknown'
  }

  // 清除消息历史
  function clearMessageHistory() {
    messageHistory.value = []
    messageCount.value = 0
  }

  // 获取订阅列表
  function getSubscriptions() {
    return Array.from(subscriptions.value)
  }

  // 检查是否已订阅
  function isSubscribed(channel: string) {
    return subscriptions.value.has(channel)
  }

  // 获取所有订阅（别名）
  function getAllSubscriptions() {
    return getSubscriptions()
  }

  // 清除所有订阅
  function clearSubscriptions() {
    subscriptions.value.clear()
  }

  // 清除错误状态
  function clearError() {
    lastError.value = null
  }

  // 获取连接统计信息
  function getConnectionStats() {
    return {
      connected: isConnected.value,
      connecting: isConnecting.value,
      disconnected: isDisconnected.value,
      reconnectAttempts: reconnectAttempts.value,
      messageCount: messageCount.value,
      totalMessages: messageCount.value, // 别名
      lastMessageTime: lastMessageTime.value,
      lastConnectedAt: lastConnectedAt.value,
      subscriptionCount: subscriptions.value.size
    }
  }

  // 重置统计信息
  function resetStats() {
    messageCount.value = 0
    reconnectAttempts.value = 0
    clearMessageHistory()
  }

  // 按类型过滤消息
  function getMessagesByType(type: string) {
    return messageHistory.value.filter(msg => msg.type === type)
  }

  // 更新配置
  function updateConfig(newConfig: Partial<typeof config.value>) {
    config.value = { ...config.value, ...newConfig }
  }

  // 重置配置到默认值
  function resetConfig() {
    config.value = {
      url: '',
      autoReconnect: true,
      reconnectInterval: 3000, // 测试期望的默认值
      maxReconnectAttempts: 10,
      heartbeatInterval: 30000
    }
  }

  // 设置自动重连
  function setAutoReconnect(enabled: boolean) {
    config.value.autoReconnect = enabled
  }

  // 清除所有事件监听器
  function clearEventListeners() {
    // 保持现有的键，但清空数组
    Object.keys(eventListeners.value).forEach(key => {
      eventListeners.value[key] = []
    })
  }

  return {
    // 状态
    socket,
    isConnected,
    isConnecting,
    isDisconnected,
    reconnectAttempts,
    lastMessageTime,
    lastMessage,
    lastError,
    messageHistory,
    messageCount,
    subscriptions,
    config,
    eventListeners,
    lastConnectedAt,

    // 方法
    connect,
    disconnect,
    reconnect,
    sendMessage,
    subscribe,
    unsubscribe,
    addEventListener,
    removeEventListener,
    getConnectionState,
    clearMessageHistory,
    getSubscriptions,
    isSubscribed,
    getAllSubscriptions,
    clearSubscriptions,
    clearError,
    getConnectionStats,
    resetStats,
    getMessagesByType,
    updateConfig,
    resetConfig,
    setAutoReconnect,
    clearEventListeners
  }
})