import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { useAuthStore } from './auth'
import { get, post } from '@/api/client'
import { add, multiply, compare } from '@/utils/decimal'

import type { Order, OrderFilters, OrderStats, OrderStatus, OrderSide, OrderType } from '@/types'

export const useOrderStore = defineStore('order', () => {
  // Stores
  const authStore = useAuthStore()

  // 状态
  const orders = ref<Order[]>([])
  const loading = ref<boolean>(false)
  const error = ref<string | null>(null)
  const filters = ref<OrderFilters>({
    status: '',
    symbol: '',
    side: '',
    dateRange: null
  })

  // 计算属性
  const filteredOrders = computed(() => {
    let result = orders.value

    if (filters.value.status) {
      result = result.filter(order => order.status === filters.value.status)
    }

    if (filters.value.symbol) {
      result = result.filter(order => 
        order.symbol.toLowerCase().includes(filters.value.symbol.toLowerCase())
      )
    }

    if (filters.value.side) {
      result = result.filter(order => order.side === filters.value.side)
    }

    if (filters.value.dateRange && filters.value.dateRange.length === 2) {
      const [start, end] = filters.value.dateRange
      result = result.filter(order => {
        const orderDate = new Date(order.created_at)
        const startDate = new Date(start)
        const endDate = new Date(end)
        return orderDate >= startDate && orderDate <= endDate
      })
    }

    return result.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
  })

  const activeOrders = computed(() =>
    orders.value.filter(order => ['pending', 'partially_filled', 'active'].includes(order.status))
  )

  const completedOrders = computed(() => 
    orders.value.filter(order => ['filled', 'cancelled'].includes(order.status))
  )

  const totalPnL = computed(() => {
    return orders.value
      .filter(order => order.status === 'filled' && order.pnl !== undefined)
      .reduce((sum, order) => add(sum, order.pnl || '0'), '0')
  })

  const orderStats = computed((): OrderStats => {
    const stats: OrderStats = {
      total: orders.value.length,
      pending: 0,
      filled: 0,
      cancelled: 0,
      failed: 0,
      partially_filled: 0,
      totalVolume: '0',
      totalPnL: totalPnL.value
    }

    orders.value.forEach(order => {
      // 使用类型安全的方式更新统计
      switch (order.status) {
        case 'pending':
          stats.pending += 1
          break
        case 'filled':
          stats.filled += 1
          break
        case 'cancelled':
          stats.cancelled += 1
          break
        case 'failed':
          stats.failed += 1
          break
        case 'partially_filled':
          stats.partially_filled += 1
          break
      }

      if (order.filled_quantity && order.price) {
        stats.totalVolume = add(stats.totalVolume, multiply(order.filled_quantity, order.price))
      }
    })

    return stats
  })

  const winRate = computed(() => {
    const profitableOrders = orders.value.filter(order =>
      compare(order.pnl_usd || '0', '0') > 0 && ['filled'].includes(order.status)
    ).length
    const totalCompletedOrders = orders.value.filter(order =>
      ['filled'].includes(order.status)
    ).length

    return totalCompletedOrders > 0 ? (profitableOrders / totalCompletedOrders) * 100 : 0
  })

  const recentOrders = computed(() => {
    return orders.value
      .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
      .slice(0, 10)
  })

  const totalOrders = computed(() => orders.value.length)

  const cancelledOrders = computed(() => 
    orders.value.filter(order => ['cancelled', 'rejected', 'expired'].includes(order.status))
  )

  const isLoading = computed(() => loading.value)

  // 添加缺失的计算属性
  const activeOrdersCount = computed(() => activeOrders.value.length)

  const dailyReturn = computed(() => {
    // 计算24小时收益率
    const now = new Date()
    const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000)

    const todayOrders = orders.value.filter(order => {
      const orderDate = new Date(order.created_at)
      return orderDate >= yesterday && order.status === 'filled' && order.pnl_usd !== undefined
    })

    if (todayOrders.length === 0) return 0

    const totalPnL = todayOrders.reduce((sum, order) => add(sum, order.pnl_usd || '0'), '0')
    const totalVolume = todayOrders.reduce((sum, order) => {
      return add(sum, multiply(order.filled_quantity || '0', order.price || '0'))
    }, '0')

    return compare(totalVolume, '0') > 0 ? parseFloat(multiply(totalPnL, '100')) / parseFloat(totalVolume) : 0
  })

  const totalPnLChange = computed(() => {
    // 计算PnL变化百分比 - 这里可以根据实际需求实现
    // 暂时返回null，避免undefined错误
    return null
  })

  // 方法
  async function fetchOrders(params: Record<string, any> = {}) {
    loading.value = true
    error.value = null

    try {
      // 确保用户已认证并获取user_id
      if (!authStore.user?.id) {
        throw new Error('用户未登录或用户信息不完整')
      }

      // 添加user_id到查询参数
      const queryParams = {
        user_id: authStore.user.id,
        ...params
      }

      const data = await get('/api/v1/orders', queryParams)

      // 处理APIResponse格式的响应
      let ordersData = []
      if (Array.isArray(data)) {
        // 直接是数组格式
        ordersData = data
      } else if (data && data.success && data.data) {
        // APIResponse格式：{success: true, message: '...', data: {...}}
        if (Array.isArray(data.data)) {
          ordersData = data.data
        } else if (data.data && Array.isArray(data.data.orders)) {
          ordersData = data.data.orders
        } else {
          console.warn('APIResponse中的data字段格式不正确:', data.data)
          ordersData = []
        }
      } else if (data && Array.isArray(data.orders)) {
        // 旧格式：{orders: [...]}
        ordersData = data.orders
      } else if (data && Array.isArray(data.data)) {
        // 旧格式：{data: [...]}
        ordersData = data.data
      } else {
        console.warn('API返回的数据格式不正确，期望数组或APIResponse格式:', data)
        ordersData = []
      }

      orders.value = ordersData

      return data
    } catch (err) {
      error.value = err instanceof Error ? err.message : String(err)
      throw err
    } finally {
      loading.value = false
    }
  }

  // 获取条件订单
  async function fetchConditionalOrders() {
    loading.value = true
    error.value = null

    try {
      const data = await get('/api/v1/conditional-orders')
      return data
    } catch (err) {
      error.value = err instanceof Error ? err.message : String(err)
      throw err
    } finally {
      loading.value = false
    }
  }

  // 获取条件订单（别名，与ConditionalOrdersView中调用保持一致）
  const getConditionalOrders = fetchConditionalOrders;

  // 创建条件订单
  async function createConditionalOrder(orderData: any) {
    loading.value = true
    error.value = null

    try {
      const data = await post('/api/v1/conditional-orders', orderData)
      return data
    } catch (err) {
      error.value = err instanceof Error ? err.message : String(err)
      throw err
    } finally {
      loading.value = false
    }
  }

  // 取消条件订单
  async function cancelConditionalOrder(orderId: string) {
    loading.value = true
    error.value = null
    
    try {
      const response = await fetch(`/api/v1/conditional-orders/${orderId}/cancel`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...authStore.getAuthHeaders()
        } as HeadersInit
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.detail || '取消条件订单失败')
      }

      const data = await response.json()
      return data
    } catch (err) {
      error.value = err instanceof Error ? err.message : String(err)
      throw err
    } finally {
      loading.value = false
    }
  }

  // 获取单个订单详情
  async function fetchOrderById(orderId: string) {
    loading.value = true
    error.value = null

    try {
      const order = await get(`/api/v1/orders/${orderId}`)

      // 更新本地订单列表中的订单
      const index = orders.value.findIndex(o => o.id === orderId)
      if (index >= 0) {
        orders.value[index] = order
      } else {
        orders.value.unshift(order)
      }

      return order
    } catch (err) {
      error.value = err instanceof Error ? err.message : String(err)
      console.error('Error fetching order:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  // 取消订单
  async function cancelOrder(orderId: string) {
    loading.value = true
    error.value = null

    try {
      const response = await fetch(`/api/v1/orders/${orderId}/cancel`, {
        method: 'POST'
      })

      if (!response.ok) {
        throw new Error('Failed to cancel order')
      }

      const updatedOrder = await response.json()
      
      // 更新本地订单状态
      const index = orders.value.findIndex(o => o.id === orderId)
      if (index >= 0) {
        orders.value[index] = updatedOrder
      }

      return updatedOrder
    } catch (err) {
      error.value = err instanceof Error ? err.message : String(err)
      console.error('Error cancelling order:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  // 批量取消订单
  async function cancelMultipleOrders(orderIds: string[]) {
    loading.value = true
    error.value = null

    try {
      const response = await fetch('/api/v1/orders/cancel_batch', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ order_ids: orderIds })
      })

      if (!response.ok) {
        throw new Error('Failed to cancel orders')
      }

      const result = await response.json()
      
      // 刷新订单列表
      await fetchOrders()

      return result
    } catch (err) {
      error.value = err instanceof Error ? err.message : String(err)
      console.error('Error cancelling multiple orders:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  // 平仓单个订单
  async function closePosition(orderId: string) {
    loading.value = true
    error.value = null

    try {
      const response = await fetch(`/api/v1/orders/${orderId}/close`, {
        method: 'POST'
      })

      if (!response.ok) {
        throw new Error('Failed to close position')
      }

      const updatedOrder = await response.json()
      
      // 更新本地订单状态
      const index = orders.value.findIndex(o => o.id === orderId)
      if (index >= 0) {
        orders.value[index] = updatedOrder
      }

      return updatedOrder
    } catch (err) {
      error.value = err instanceof Error ? err.message : String(err)
      console.error('Error closing position:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  // 一键清仓
  async function closeAllPositions() {
    loading.value = true
    error.value = null

    try {
      const response = await fetch('/api/v1/orders/close-all', {
        method: 'POST'
      })

      if (!response.ok) {
        throw new Error('Failed to close all positions')
      }

      const result = await response.json()
      
      // 刷新订单列表
      await fetchOrders()

      return result
    } catch (err) {
      error.value = err instanceof Error ? err.message : String(err)
      console.error('Error closing all positions:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  // 更新订单（通过WebSocket或其他方式）
  function updateOrder(updatedOrder: Order) {
    const index = orders.value.findIndex(o => o.id === updatedOrder.id)
    if (index >= 0) {
      orders.value[index] = updatedOrder
    } else {
      orders.value.unshift(updatedOrder)
    }
  }

  // 添加新订单
  function addOrder(newOrder: Order) {
    orders.value.unshift(newOrder)
  }

  // 处理订单更新（WebSocket）
  function handleOrderUpdate(payload: any) {
    const index = orders.value.findIndex(o => o.id === payload.id)
    if (index >= 0) {
      orders.value[index] = { ...orders.value[index], ...payload }
    } else {
      orders.value.unshift(payload)
    }
  }

  // 设置过滤器
  function setFilters(newFilters: Partial<OrderFilters>) {
    filters.value = { ...filters.value, ...newFilters }
  }

  // 清除过滤器
  function clearFilters() {
    filters.value = {
      status: '',
      symbol: '',
      side: '',
      dateRange: null
    }
  }

  // 清除错误
  function clearError() {
    error.value = null
  }

  // 获取订单状态的显示文本
  function getOrderStatusText(status: OrderStatus) {
    const statusMap = {
      'pending': '待成交',
      'partially_filled': '部分成交',
      'filled': '已成交',
      'cancelled': '已取消',
      'failed': '已失败',
      'expired': '已过期'
    }
    return statusMap[status] || status
  }

  // 获取订单方向的显示文本
  function getOrderSideText(side: OrderSide) {
    const sideMap = {
      'buy': '买入',
      'sell': '卖出'
    }
    return sideMap[side] || side
  }

  // 获取订单类型的显示文本
  function getOrderTypeText(type: OrderType) {
    const typeMap = {
      'market': '市价单',
      'limit': '限价单',
      'stop': '止损单',
      'stop_limit': '止损限价单'
    }
    return typeMap[type] || type
  }

  // 格式化价格
  function formatPrice(price: number | null | undefined, symbol: string = '') {
    if (!price) return '-'
    
    // 根据交易对确定小数位数
    let decimals = 2
    if (symbol.includes('USDT') || symbol.includes('USD')) {
      decimals = 2
    } else if (symbol.includes('BTC')) {
      decimals = 8
    } else if (symbol.includes('ETH')) {
      decimals = 6
    }

    return Number(price).toFixed(decimals)
  }

  // 格式化数量
  function formatQuantity(quantity: number | null | undefined, symbol: string = '') {
    if (!quantity) return '-'

    // 根据交易对确定小数位数
    let decimals = 4
    if (symbol.includes('BTC')) {
      decimals = 8
    } else if (symbol.includes('ETH')) {
      decimals = 6
    }

    return Number(quantity).toFixed(decimals)
  }

  // 计算订单价值
  function calculateOrderValue(order: { quantity: string | number; price: string | number }) {
    const quantity = typeof order.quantity === 'string' ? parseFloat(order.quantity) : order.quantity
    const price = typeof order.price === 'string' ? parseFloat(order.price) : order.price
    return quantity * price
  }

  // 计算手续费
  function calculateFee(order: { quantity: string | number; price: string | number }, feeRate: number) {
    const orderValue = calculateOrderValue(order)
    return orderValue * feeRate
  }

  // 验证订单数据
  function validateOrder(order: { symbol?: string; side?: string; quantity?: string | number; price?: string | number }) {
    // 检查必填字段
    if (!order.symbol || order.symbol.trim() === '') return false
    if (!order.side || !['buy', 'sell'].includes(order.side)) return false

    // 检查数量
    const quantity = typeof order.quantity === 'string' ? parseFloat(order.quantity) : order.quantity
    if (!quantity || quantity <= 0) return false

    // 检查价格
    const price = typeof order.price === 'string' ? parseFloat(order.price) : order.price
    if (!price || price <= 0) return false

    return true
  }

  return {
    // 状态
    orders,
    loading,
    error,
    filters,
    
    // 计算属性
    filteredOrders,
    activeOrders,
    completedOrders,
    totalPnL,
    orderStats,
    winRate,
    recentOrders,
    totalOrders,
    cancelledOrders,
    isLoading,
    activeOrdersCount,
    dailyReturn,
    totalPnLChange,
    
    // 方法
    fetchOrders,
    fetchConditionalOrders,
    getConditionalOrders,
    createConditionalOrder,
    cancelConditionalOrder,
    fetchOrderById,
    cancelOrder,
    cancelMultipleOrders,
    closePosition,
    closeAllPositions,
    updateOrder,
    addOrder,
    handleOrderUpdate,
    setFilters,
    clearFilters,
    clearError,
    getOrderStatusText,
    getOrderSideText,
    getOrderTypeText,
    formatPrice,
    formatQuantity,
    calculateOrderValue,
    calculateFee,
    validateOrder
  }
})