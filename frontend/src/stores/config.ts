import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type {
  ExchangeConfig,
  RiskConfig,
  SystemConfig,
  AppConfig
} from '@/types'

export const useConfigStore = defineStore('config', () => {
  // 状态
  const exchangeConfigs = ref<ExchangeConfig[]>([])
  const riskConfig = ref<RiskConfig | null>(null)
  const configs = ref<AppConfig>({
    exchanges: [],
    risk: {} as RiskConfig,
    signals: [],
    system: {} as SystemConfig
  })
  const loading = ref<boolean>(false)
  const error = ref<string | null>(null)

  // 计算属性
  const hasExchangeConfig = computed(() => exchangeConfigs.value.length > 0)
  const hasRiskConfig = computed(() => riskConfig.value !== null)
  const isConfigured = computed(() => hasExchangeConfig.value && hasRiskConfig.value)
  const enabledExchanges = computed(() => {
    return configs.value.exchanges?.filter(exchange => exchange.enabled) || []
  })
  const isAutoTradingEnabled = computed(() => {
    return configs.value.system?.auto_trading_enabled || false
  })

  // 获取交易所配置
  async function fetchExchangeConfigs() {
    loading.value = true
    error.value = null
    
    try {
      const response = await fetch('/api/v1/configs/exchange')
      if (!response.ok) {
        throw new Error('Failed to fetch exchange configs')
      }
      
      const data = await response.json()
      exchangeConfigs.value = data
    } catch (err) {
      error.value = err instanceof Error ? err.message : String(err)
      console.error('Error fetching exchange configs:', err)
    } finally {
      loading.value = false
    }
  }

  // 创建交易所配置
  async function createExchangeConfig(config: Partial<ExchangeConfig>) {
    loading.value = true
    error.value = null
    
    try {
      const response = await fetch('/api/v1/configs/exchange', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(config)
      })
      
      if (!response.ok) {
        throw new Error('Failed to create exchange config')
      }
      
      const newConfig = await response.json()
      exchangeConfigs.value.push(newConfig)
      return newConfig
    } catch (err) {
      error.value = err instanceof Error ? err.message : String(err)
      console.error('Error creating exchange config:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  // 更新交易所配置
  async function updateExchangeConfig(id: string, config: Partial<ExchangeConfig>) {
    loading.value = true
    error.value = null
    
    try {
      const response = await fetch(`/api/v1/exchanges/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(config)
      })
      
      if (!response.ok) {
        throw new Error('Failed to update exchange config')
      }
      
      const updatedConfig = await response.json()
      const index = exchangeConfigs.value.findIndex(c => c.id === id)
      if (index >= 0) {
        exchangeConfigs.value[index] = updatedConfig
      }
      return updatedConfig
    } catch (err) {
      error.value = err instanceof Error ? err.message : String(err)
      console.error('Error updating exchange config:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  // 删除交易所配置
  async function deleteExchangeConfig(id: string) {
    loading.value = true
    error.value = null
    
    try {
      const response = await fetch(`/api/v1/exchanges/${id}`, {
        method: 'DELETE'
      })
      
      if (!response.ok) {
        throw new Error('Failed to delete exchange config')
      }
      
      exchangeConfigs.value = exchangeConfigs.value.filter(c => c.id !== id)
    } catch (err) {
      error.value = err instanceof Error ? err.message : String(err)
      console.error('Error deleting exchange config:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  // 获取风险配置
  async function fetchRiskConfig() {
    loading.value = true
    error.value = null
    
    try {
      const response = await fetch('/api/v1/configs/risk')
      if (!response.ok) {
        throw new Error('Failed to fetch risk config')
      }
      
      const data = await response.json()
      riskConfig.value = data
    } catch (err) {
      error.value = err instanceof Error ? err.message : String(err)
      console.error('Error fetching risk config:', err)
    } finally {
      loading.value = false
    }
  }

  // 更新风险配置
  async function updateRiskConfig(config: Partial<RiskConfig>) {
    loading.value = true
    error.value = null
    
    try {
      const response = await fetch('/api/v1/configs/risk', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(config)
      })
      
      if (!response.ok) {
        throw new Error('Failed to update risk config')
      }
      
      const updatedConfig = await response.json()
      riskConfig.value = updatedConfig
      return updatedConfig
    } catch (err) {
      error.value = err instanceof Error ? err.message : String(err)
      console.error('Error updating risk config:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  // 测试交易所连接
  async function testExchangeConnection(exchangeId: string) {
    loading.value = true
    error.value = null
    
    try {
      const response = await fetch(`/api/v1/exchanges/${exchangeId}/test`, {
        method: 'POST'
      })
      
      if (!response.ok) {
        throw new Error('Failed to test exchange connection')
      }
      
      const result = await response.json()
      return result
    } catch (err) {
      error.value = err instanceof Error ? err.message : String(err)
      console.error('Error testing exchange connection:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  // 清除错误
  function clearError() {
    error.value = null
  }

  // 加载所有配置 (用于OnboardingWizard和ConfigsView)
  async function loadConfigs() {
    loading.value = true
    error.value = null

    try {
      // 模拟API调用或使用现有的fetch方法
      await Promise.all([
        fetchExchangeConfigs(),
        fetchRiskConfig()
      ])
      
      // 同步到configs对象
      configs.value.exchanges = exchangeConfigs.value
      configs.value.risk = riskConfig.value || {} as RiskConfig
      
      // 设置默认的signals和system配置
      if (!configs.value.signals) {
        configs.value.signals = []
      }
      if (!configs.value.system) {
        configs.value.system = {
          auto_trading_enabled: true,
          max_concurrent_trades: 3,
          default_trade_amount: '1000',
          order_timeout_seconds: 30,
          log_level: 'INFO',
          notification_settings: {
            email: true,
            sms: false,
            push: true
          }
        }
      }
    } catch (err) {
      console.error('Failed to load configs:', err)
      error.value = err instanceof Error ? err.message : String(err)
      
      // 使用默认配置
      configs.value = {
        exchanges: [],
        risk: {
          max_position_size_usd: '1000',
          max_daily_loss_usd: '500',
          max_open_positions: 5,
          default_stop_loss_pct: '5',
          enable_stop_loss: true,
          enable_take_profit: true
        },
        signals: [],
        system: {
          auto_trading_enabled: true,
          max_concurrent_trades: 3,
          default_trade_amount: '1000',
          order_timeout_seconds: 30,
          log_level: 'INFO',
          notification_settings: {
            email: true,
            sms: false,
            push: true
          }
        }
      }
    } finally {
      loading.value = false
    }
  }

  // 保存所有配置 (用于OnboardingWizard和ConfigsView)
  async function saveConfigs(newConfigs: Partial<AppConfig>) {
    loading.value = true
    error.value = null

    try {
      // 更新exchanges配置
      if (newConfigs.exchanges) {
        for (const exchange of newConfigs.exchanges) {
          if (exchange.id) {
            await updateExchangeConfig(exchange.id, exchange)
          } else {
            await createExchangeConfig(exchange)
          }
        }
      }

      // 更新risk配置
      if (newConfigs.risk) {
        await updateRiskConfig(newConfigs.risk)
      }

      // 更新configs对象
      configs.value = { ...configs.value, ...newConfigs }
    } catch (err) {
      console.error('Failed to save configs:', err)
      error.value = err instanceof Error ? err.message : String(err)
      throw err
    } finally {
      loading.value = false
    }
  }

  // 初始化配置
  async function initializeConfigs() {
    await loadConfigs()
  }

  return {
    // 状态
    exchangeConfigs,
    riskConfig,
    configs,
    loading,
    error,
    
    // 计算属性
    hasExchangeConfig,
    hasRiskConfig,
    isConfigured,
    enabledExchanges,
    isAutoTradingEnabled,
    
    // 方法
    fetchExchangeConfigs,
    createExchangeConfig,
    updateExchangeConfig,
    deleteExchangeConfig,
    fetchRiskConfig,
    updateRiskConfig,
    testExchangeConnection,
    clearError,
    loadConfigs,
    saveConfigs,
    initializeConfigs
  }
})