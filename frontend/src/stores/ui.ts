import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { NotificationOptions, ConfirmDialog } from '@/types'

interface Notification extends NotificationOptions {
  id: number
  timestamp: string
  read: boolean
  autoClose: boolean
  duration: number
}

export const useUIStore = defineStore('ui', () => {
  // 状态
  const notifications = ref<Notification[]>([])
  const isLoading = ref<boolean>(false)
  const loadingMessage = ref<string>('')
  const showOnboardingWizard = ref<boolean>(false)
  const sidebarOpen = ref<boolean>(true)
  const theme = ref<string>(localStorage.getItem('theme') || 'dark')

  // 计算属性
  const hasNotifications = computed(() => notifications.value.length > 0)
  const unreadNotifications = computed(() => 
    notifications.value.filter(n => !n.read).length
  )

  // 通知管理
  function addNotification(notification: NotificationOptions): number {
    const newNotification: Notification = {
      id: Date.now() + Math.random(),
      timestamp: new Date().toISOString(),
      read: false,
      autoClose: true,
      duration: 5000,
      ...notification
    }

    notifications.value.unshift(newNotification)

    // 限制通知数量
    if (notifications.value.length > 100) {
      notifications.value = notifications.value.slice(0, 100)
    }

    // 自动关闭通知
    if (newNotification.autoClose) {
      setTimeout(() => {
        removeNotification(newNotification.id)
      }, newNotification.duration)
    }

    return newNotification.id
  }

  function removeNotification(id: number) {
    const index = notifications.value.findIndex(n => n.id === id)
    if (index >= 0) {
      notifications.value.splice(index, 1)
    }
  }

  function markNotificationAsRead(id: number) {
    const notification = notifications.value.find(n => n.id === id)
    if (notification) {
      notification.read = true
    }
  }

  function markAllNotificationsAsRead() {
    notifications.value.forEach(n => n.read = true)
  }

  function clearNotifications() {
    notifications.value = []
  }

  function clearReadNotifications() {
    notifications.value = notifications.value.filter(n => !n.read)
  }

  // 加载状态管理
  function setLoading(loading: boolean, message: string = '') {
    isLoading.value = loading
    loadingMessage.value = message
  }

  function showLoading(message: string = '加载中...') {
    setLoading(true, message)
  }

  function hideLoading() {
    setLoading(false, '')
  }

  // 主题管理
  function setTheme(newTheme: string) {
    theme.value = newTheme
    localStorage.setItem('theme', newTheme)
  }

  function toggleTheme() {
    const newTheme = theme.value === 'dark' ? 'light' : 'dark'
    setTheme(newTheme)
  }

  // 侧边栏管理
  function toggleSidebar() {
    sidebarOpen.value = !sidebarOpen.value
  }

  function setSidebarOpen(open: boolean) {
    sidebarOpen.value = open
  }

  // 引导向导管理
  function openOnboardingWizard() {
    showOnboardingWizard.value = true
  }

  function closeOnboardingWizard() {
    showOnboardingWizard.value = false
  }

  // 快捷通知方法
  function showSuccess(message: string, title: string = '成功') {
    return addNotification({
      type: 'success',
      title,
      message,
      color: 'success'
    })
  }

  function showError(message: string, title: string = '错误') {
    return addNotification({
      type: 'error',
      title,
      message,
      color: 'error',
      autoClose: false // 错误通知不自动关闭
    })
  }

  function showWarning(message: string, title: string = '警告') {
    return addNotification({
      type: 'warning',
      title,
      message,
      color: 'warning'
    })
  }

  function showInfo(message: string, title: string = '信息') {
    return addNotification({
      type: 'info',
      title,
      message,
      color: 'info'
    })
  }

  // 确认对话框状态
  const confirmDialog = ref<ConfirmDialog>({
    show: false,
    title: '',
    message: '',
    confirmText: '确认',
    cancelText: '取消',
    onConfirm: null,
    onCancel: null
  })

  function showConfirmDialog(options: any) {
    return new Promise((resolve) => {
      confirmDialog.value = {
        show: true,
        title: options.title || '确认',
        message: options.message || '',
        confirmText: options.confirmText || '确认',
        cancelText: options.cancelText || '取消',
        onConfirm: () => {
          confirmDialog.value.show = false
          resolve(true)
        },
        onCancel: () => {
          confirmDialog.value.show = false
          resolve(false)
        }
      }
    })
  }

  function hideConfirmDialog() {
    confirmDialog.value.show = false
  }

  return {
    // 状态
    notifications,
    isLoading,
    loadingMessage,
    showOnboardingWizard,
    sidebarOpen,
    theme,
    confirmDialog,
    
    // 计算属性
    hasNotifications,
    unreadNotifications,
    
    // 通知方法
    addNotification,
    removeNotification,
    markNotificationAsRead,
    markAllNotificationsAsRead,
    clearNotifications,
    clearReadNotifications,
    
    // 加载状态方法
    setLoading,
    showLoading,
    hideLoading,
    
    // 主题方法
    setTheme,
    toggleTheme,
    
    // 侧边栏方法
    toggleSidebar,
    setSidebarOpen,
    
    // 引导向导方法
    openOnboardingWizard,
    closeOnboardingWizard,
    
    // 快捷通知方法
    showSuccess,
    showError,
    showWarning,
    showInfo,
    
    // 确认对话框方法
    showConfirmDialog,
    hideConfirmDialog,
  }
})