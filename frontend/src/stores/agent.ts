import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { get } from '@/api/client'
import type {
  AgentTask,
  PendingAction,
  AgentNotification,
  AgentLog,
  TradingSignal,
  Order
} from '@/types'

export const useAgentStore = defineStore('agent', () => {
  // 状态
  const isConnected = ref<boolean>(false)
  const currentTask = ref<AgentTask | null>(null)
  const agentState = ref<'idle' | 'processing' | 'waiting_confirmation' | 'completed' | 'failed'>('idle')
  const pendingActions = ref<PendingAction[]>([])
  const orders = ref<Order[]>([])
  const notifications = ref<AgentNotification[]>([])
  const logs = ref<AgentLog[]>([])
  const websocket = ref<WebSocket | null>(null)

  // 计算属性
  const isProcessing = computed(() => agentState.value === 'processing')
  const hasNotifications = computed(() => notifications.value.length > 0)
  const activePendingActions = computed(() => 
    pendingActions.value.filter(action => action.status === 'pending')
  )
  const agentLogs = computed(() => logs.value)

  // WebSocket连接
  function connectWebSocket(token: string) {
    if (websocket.value) {
      websocket.value.close()
    }

    const wsUrl = `ws://localhost:8000/ws/${token}`
    websocket.value = new WebSocket(wsUrl)

    websocket.value.onopen = () => {
      isConnected.value = true
      console.log('WebSocket connected')
    }

    websocket.value.onclose = () => {
      isConnected.value = false
      console.log('WebSocket disconnected')
    }

    websocket.value.onmessage = (event: MessageEvent) => {
      const message = JSON.parse(event.data)
      handleWebSocketMessage(message)
    }

    websocket.value.onerror = (error: Event) => {
      console.error('WebSocket error:', error)
    }
  }

  // 处理WebSocket消息
  function handleWebSocketMessage(message: any) {
    // 添加日志记录
    addLog({
      level: 'info',
      message: `收到WebSocket消息: ${message.event_type}`,
      timestamp: new Date().toISOString(),
      details: message
    })

    switch (message.event_type) {
      case 'AGENT_STATE_TRANSITION':
        handleAgentStateTransition(message.payload)
        break
      case 'PENDING_ACTION_REQUIRED':
        handlePendingActionRequired(message.payload)
        break
      case 'ORDER_UPDATE':
        handleOrderUpdate(message.payload)
        break
      case 'NOTIFICATION':
        handleNotification(message.payload)
        break
      case 'TASK_COMPLETED':
        handleTaskCompleted(message.payload)
        break
      case 'LOG':
        handleLogMessage(message.payload)
        break
      default:
        console.log('Unknown message type:', message.event_type)
        addLog({
          level: 'warn',
          message: `未知消息类型: ${message.event_type}`,
          timestamp: new Date().toISOString(),
          details: message
        })
    }
  }

  // 处理Agent状态转换
  function handleAgentStateTransition(payload: any) {
    console.log(`Agent transition: ${payload.from_node} -> ${payload.to_node}`)
    
    // 更新当前任务
    if (payload.task_id) {
      currentTask.value = payload.task_id
    }

    // 根据节点更新状态
    switch (payload.to_node) {
      case 'Preprocess':
      case 'Parse':
      case 'Context':
      case 'Plan':
      case 'Risk':
      case 'Execute':
        agentState.value = 'processing'
        break
      case 'UserConfirm':
        agentState.value = 'waiting_confirmation'
        break
      case 'Success':
        agentState.value = 'completed'
        break
      case 'Failure':
        agentState.value = 'failed'
        break
    }

    // 添加通知
    addNotification({
      type: 'info',
      title: 'Agent状态更新',
      message: `从 ${payload.from_node} 转换到 ${payload.to_node}`,
      timestamp: payload.timestamp
    })
  }

  // 处理待确认动作
  function handlePendingActionRequired(payload: any) {
    pendingActions.value.push({
      id: payload.action_id,
      details: payload.details,
      expiresAt: payload.expires_at,
      status: 'pending'
    })

    addNotification({
      type: 'warning',
      title: '需要用户确认',
      message: payload.details.clarification_needed || '请确认交易计划',
      timestamp: new Date().toISOString()
    })
  }

  // 处理订单更新
  function handleOrderUpdate(payload: any) {
    const existingIndex = orders.value.findIndex(order => order.id === payload.id)
    if (existingIndex >= 0) {
      orders.value[existingIndex] = payload
    } else {
      orders.value.unshift(payload)
    }

    addNotification({
      type: 'success',
      title: '订单更新',
      message: `订单 ${payload.symbol} ${payload.side} 状态: ${payload.status}`,
      timestamp: new Date().toISOString()
    })
  }

  // 处理任务完成
  function handleTaskCompleted(payload: any) {
    currentTask.value = null
    agentState.value = payload.status === 'success' ? 'completed' : 'failed'

    addNotification({
      type: payload.status === 'success' ? 'success' : 'error',
      title: '任务完成',
      message: payload.message,
      timestamp: new Date().toISOString()
    })
  }

  // 处理通知
  function handleNotification(payload: any) {
    addNotification({
      type: payload.type || 'info',
      title: payload.title || '通知',
      message: payload.message,
      timestamp: new Date().toISOString()
    })
  }

  // 添加通知
  function addNotification(notification: Partial<AgentNotification>) {
    const newNotification: AgentNotification = {
      id: Date.now(),
      type: notification.type || 'info',
      title: notification.title || '',
      message: notification.message || '',
      timestamp: notification.timestamp || new Date().toISOString(),
      read: notification.read || false
    }
    notifications.value.unshift(newNotification)

    // 限制通知数量
    if (notifications.value.length > 50) {
      notifications.value = notifications.value.slice(0, 50)
    }
  }

  // 清除通知
  function clearNotifications() {
    notifications.value = []
  }

  // 移除通知
  function removeNotification(id: number) {
    const index = notifications.value.findIndex(n => n.id === id)
    if (index >= 0) {
      notifications.value.splice(index, 1)
    }
  }

  // 处理日志消息
  function handleLogMessage(payload: any) {
    addLog({
      level: payload.level?.toLowerCase() || 'info',
      message: payload.message,
      timestamp: payload.timestamp || new Date().toISOString(),
      details: payload.details
    })
  }

  // 添加日志
  function addLog(log: Partial<AgentLog>) {
    const newLog: AgentLog = {
      id: Date.now() + Math.random(),
      level: log.level || 'info',
      message: log.message || '',
      timestamp: log.timestamp || new Date().toISOString(),
      details: log.details
    }
    logs.value.unshift(newLog)

    // 限制日志数量，保留最近2000条
    if (logs.value.length > 2000) {
      logs.value = logs.value.slice(0, 2000)
    }
  }

  // 清除日志
  function clearLogs() {
    logs.value = []
  }

  // 获取待处理动作
  async function fetchPendingActions() {
    try {
      const response = await get('/api/v1/pending-actions')
      pendingActions.value = response.data || []
    } catch (error) {
      console.error('Failed to fetch pending actions:', error)
      throw error
    }
  }

  // 响应待确认动作
  async function respondToPendingAction(actionId: string, response: any, additionalData: Record<string, any> = {}) {
    try {
      const res = await fetch(`/api/v1/actions/${actionId}/respond`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          approved: response === 'approve',
          ...additionalData
        })
      })

      if (res.ok) {
        // 更新待确认动作状态
        const action = pendingActions.value.find(a => a.id === actionId)
        if (action) {
          action.status = 'responded'
        }

        addNotification({
          type: 'success',
          title: '响应已发送',
          message: '您的响应已成功发送给Agent',
          timestamp: new Date().toISOString()
        })
      } else {
        throw new Error('Failed to respond to pending action')
      }
    } catch (error) {
      console.error('Error responding to pending action:', error)
      addNotification({
        type: 'error',
        title: '响应失败',
        message: '发送响应时出现错误，请重试',
        timestamp: new Date().toISOString()
      })
    }
  }
  
  // 紧急批量拒绝所有待处理动作
  async function rejectAllPendingActions(actionIds?: string[]) {
    try {
      // 如果没有提供actionIds，则使用当前所有待处理的动作ID
      const ids = actionIds || activePendingActions.value.map(action => action.id)
      
      if (ids.length === 0) {
        console.log('没有需要拒绝的待处理动作')
        return
      }
      
      // 记录日志
      addLog({
        level: 'warn',
        message: `紧急拒绝${ids.length}个待处理动作`,
        timestamp: new Date().toISOString(),
        details: { actionIds: ids }
      })
      
      // 调用批量拒绝API
      const res = await fetch(`/api/v1/actions/batch-reject`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action_ids: ids,
          reason: 'EMERGENCY_CANCEL'
        })
      })
      
      if (!res.ok) {
        const errorData = await res.json()
        throw new Error(errorData.detail || '批量拒绝失败')
      }
      
      // 更新本地状态
      pendingActions.value = pendingActions.value.map(action => {
        if (ids.includes(action.id)) {
          return { ...action, status: 'rejected' }
        }
        return action
      })
      
      // 添加系统通知
      addNotification({
        type: 'warning',
        title: '紧急取消',
        message: `已取消${ids.length}个待处理动作`,
        timestamp: new Date().toISOString()
      })
      
      return await res.json()
    } catch (error) {
      console.error('批量拒绝失败:', error)
      addLog({
        level: 'error',
        message: `批量拒绝失败: ${error instanceof Error ? error.message : String(error)}`,
        timestamp: new Date().toISOString(),
        details: { error: error instanceof Error ? error.toString() : String(error) }
      })
      throw error
    }
  }
  
  // 发送交易信号
  async function sendTradingSignal(signal: TradingSignal) {
    try {
      const res = await fetch('/api/v1/process_signal', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          text: signal,
          user_id: 1 // TODO: 从认证状态获取真实用户ID
        })
      })

      if (res.ok) {
        const result = await res.json()
        currentTask.value = result.task_id
        agentState.value = 'processing'

        addNotification({
          type: 'info',
          title: '信号已发送',
          message: `交易信号已发送，任务ID: ${result.task_id}`,
          timestamp: new Date().toISOString()
        })

        return result
      } else {
        throw new Error('Failed to send trading signal')
      }
    } catch (error) {
      console.error('Error sending trading signal:', error)
      addNotification({
        type: 'error',
        title: '发送失败',
        message: '发送交易信号时出现错误，请重试',
        timestamp: new Date().toISOString()
      })
      throw error
    }
  }

  // 断开WebSocket连接
  function disconnect() {
    if (websocket.value) {
      websocket.value.close()
      websocket.value = null
    }
    isConnected.value = false
  }

  return {
    // 状态
    isConnected,
    currentTask,
    agentState,
    pendingActions,
    orders,
    notifications,
    logs,
    
    // 计算属性
    isProcessing,
    hasNotifications,
    activePendingActions,
    agentLogs,
    
    // 方法
    connectWebSocket,
    disconnect,
    sendTradingSignal,
    respondToPendingAction,
    rejectAllPendingActions,
    addNotification,
    clearNotifications,
    removeNotification,
    addLog,
    clearLogs,
    fetchPendingActions
  }
})