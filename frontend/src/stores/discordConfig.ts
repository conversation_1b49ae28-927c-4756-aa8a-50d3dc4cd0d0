/**
 * Discord配置状态管理
 * 基于简化设计原则，专注于Discord配置的状态管理
 */
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import * as discordConfigApi from '@/api/discordConfig'

export interface DiscordConfig {
  id: string
  user_id: string
  source_name: string
  enabled: boolean
  has_token: boolean
  server_ids: string[]
  channel_ids: string[]
  author_ids: string[]
  allowed_message_types: string[]
  created_at: string
  updated_at: string
}

export interface DiscordConfigRequest {
  source_name: string
  enabled: boolean
  token: string
  server_ids: string[]
  channel_ids: string[]
  author_ids: string[]
  allowed_message_types: string[]
}

export const useDiscordConfigStore = defineStore('discordConfig', () => {
  // 状态
  const configs = ref<DiscordConfig[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)

  // 计算属性
  const enabledConfigs = computed(() => 
    configs.value.filter(config => config.enabled)
  )

  const configCount = computed(() => configs.value.length)



  // 操作
  const fetchConfigs = async () => {
    loading.value = true
    error.value = null

    try {
      const response = await discordConfigApi.getDiscordConfigs()
      configs.value = response
    } catch (err: any) {
      const errorMessage = err.response?.data?.detail || err.message || '获取Discord配置失败'
      error.value = `获取Discord配置失败: ${errorMessage}`
    } finally {
      loading.value = false
    }
  }

  const createConfig = async (configData: DiscordConfigRequest): Promise<boolean> => {
    loading.value = true
    error.value = null

    try {
      const response = await discordConfigApi.createDiscordConfig(configData)
      configs.value.push(response)

      return true
    } catch (err: any) {
      const errorMessage = err.response?.data?.detail || err.message || '创建Discord配置失败'
      error.value = `创建Discord配置失败: ${errorMessage}`
      return false
    } finally {
      loading.value = false
    }
  }

  const updateConfig = async (id: string, configData: DiscordConfigRequest): Promise<boolean> => {
    loading.value = true
    error.value = null

    try {
      const response = await discordConfigApi.updateDiscordConfig(id, configData)
      const index = configs.value.findIndex(config => config.id === id)
      if (index !== -1) {
        configs.value[index] = response
      }

      return true
    } catch (err: any) {
      const errorMessage = err.response?.data?.detail || err.message || '更新Discord配置失败'
      error.value = `更新Discord配置失败: ${errorMessage}`
      return false
    } finally {
      loading.value = false
    }
  }

  const deleteConfig = async (id: string): Promise<boolean> => {
    loading.value = true
    error.value = null

    try {
      await discordConfigApi.deleteDiscordConfig(id)
      configs.value = configs.value.filter(config => config.id !== id)

      return true
    } catch (err: any) {
      const errorMessage = err.response?.data?.detail || err.message || '删除Discord配置失败'
      error.value = `删除Discord配置失败: ${errorMessage}`
      return false
    } finally {
      loading.value = false
    }
  }

  const getConfigById = (id: string): DiscordConfig | undefined => {
    return configs.value.find(config => config.id === id)
  }

  const toggleConfigEnabled = async (id: string): Promise<boolean> => {
    loading.value = true
    error.value = null

    try {
      const response = await discordConfigApi.toggleDiscordConfig(id)
      const index = configs.value.findIndex(config => config.id === id)
      if (index !== -1) {
        configs.value[index] = response
      }

      return true
    } catch (err: any) {
      const errorMessage = err.response?.data?.detail || err.message || '切换配置状态失败'
      error.value = `切换配置状态失败: ${errorMessage}`
      return false
    } finally {
      loading.value = false
    }
  }

  const clearError = () => {
    error.value = null
  }

  const resetStore = () => {
    configs.value = []
    loading.value = false
    error.value = null
  }

  return {
    // 状态
    configs,
    loading,
    error,
    
    // 计算属性
    enabledConfigs,
    configCount,
    
    // 操作
    fetchConfigs,
    createConfig,
    updateConfig,
    deleteConfig,
    getConfigById,
    toggleConfigEnabled,
    clearError,
    resetStore
  }
})
