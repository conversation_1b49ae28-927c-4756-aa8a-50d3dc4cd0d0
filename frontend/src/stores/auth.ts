import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { post, get } from '@/api/client'
import { SecureStorage } from '@/utils/secureStorage'
import type { User, LoginCredentials, RegisterData } from '@/types'

export const useAuthStore = defineStore('auth', () => {
  // 状态 - 确保初始值不为undefined
  const user = ref<User | null>(null)
  const token = ref<string | null>(null)
  const isLoading = ref<boolean>(false)
  const error = ref<string | null>(null)

  // 安全初始化存储的数据
  const initializeFromStorage = () => {
    try {
      const storedUser = SecureStorage.getUser()
      const storedToken = SecureStorage.getToken()
      
      if (storedUser && typeof storedUser === 'object') {
        user.value = storedUser
      }
      
      if (storedToken && typeof storedToken === 'string') {
        token.value = storedToken
      }
    } catch (error) {
      console.error('Failed to initialize from storage:', error)
      // 清除可能损坏的数据
      SecureStorage.clear()
    }
  }

  // 立即初始化
  initializeFromStorage()

  // 计算属性
  const isAuthenticated = computed(() => !!token.value && !!user.value)
  const isFirstTimeLogin = computed(() => user.value?.is_first_time || false)
  const userPermissions = computed(() => user.value?.permissions || [])
  const userRoles = computed(() => user.value?.roles || [])

  // 登录
  async function login(credentials: LoginCredentials) {
    isLoading.value = true
    error.value = null

    try {
      // 第一步：获取token - 使用FormData发送OAuth2表单数据
      const formData = new FormData()
      formData.append('username', credentials.username)
      formData.append('password', credentials.password)

      const loginData = await post('/api/v1/auth/login', formData)
      token.value = loginData.access_token
      if (token.value) {
        SecureStorage.setToken(token.value)
      }

      // 第二步：获取用户信息
      const userData = await get('/api/v1/auth/me')
      user.value = userData
      SecureStorage.setUser(userData)

      return { ...loginData, user: userData }
    } catch (err) {
      error.value = err instanceof Error ? err.message : String(err)
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // 注册
  async function register(userData: RegisterData) {
    isLoading.value = true
    error.value = null

    try {
      const data = await post('/api/v1/auth/register', userData)
      return data
    } catch (err) {
      error.value = err instanceof Error ? err.message : String(err)
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // 登出
  function logout() {
    user.value = null
    token.value = null
    SecureStorage.clear()
  }

  // 初始化用户信息
  async function initializeUser() {
    if (!token.value) return

    try {
      const userData = await get('/api/v1/auth/me')
      user.value = userData
      SecureStorage.setUser(userData)
    } catch (err) {
      console.error('Failed to initialize user:', err)
      logout()
    }
  }

  // 更新用户信息
  function updateUser(userData: Partial<User>) {
    if (user.value) {
      user.value = { ...user.value, ...userData }
      SecureStorage.setUser(user.value)
    }
  }

  // 标记用户已完成首次设置
  function markFirstTimeSetupComplete() {
    if (user.value) {
      user.value.is_first_time = false
      SecureStorage.setUser(user.value)
    }
  }

  // 获取认证头
  function getAuthHeaders() {
    return token.value ? { 'Authorization': `Bearer ${token.value}` } : {}
  }

  // 清除错误
  function clearError() {
    error.value = null
  }

  // 检查token是否需要刷新
  function shouldRefreshToken() {
    if (!token.value) return false
    return SecureStorage.isTokenExpiringSoon(token.value)
  }

  // 刷新token（如果后端支持）
  async function refreshToken(): Promise<boolean> {
    if (!token.value) return false

    try {
      const data = await post('/api/v1/auth/refresh', { token: token.value })
      token.value = data.access_token
      if (token.value) {
        SecureStorage.setToken(token.value)
      }
      return true
    } catch (err) {
      console.error('Token refresh failed:', err)
      logout()
      return false
    }
  }

  // 更新用户资料
  async function updateProfile(profileData: Partial<User>) {
    isLoading.value = true
    error.value = null

    try {
      const updatedUser = await post('/api/v1/auth/profile', profileData)
      user.value = updatedUser
      SecureStorage.setUser(updatedUser)
      return updatedUser
    } catch (err) {
      error.value = err instanceof Error ? err.message : String(err)
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // 修改密码
  async function changePassword(passwordData: { currentPassword: string; newPassword: string; confirmPassword: string }) {
    isLoading.value = true
    error.value = null

    try {
      const result = await post('/api/v1/auth/change-password', passwordData)
      return result
    } catch (err) {
      error.value = err instanceof Error ? err.message : String(err)
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // 请求密码重置
  async function requestPasswordReset(email: string) {
    isLoading.value = true
    error.value = null

    try {
      const result = await post('/api/v1/auth/forgot-password', { email })
      return result
    } catch (err) {
      error.value = err instanceof Error ? err.message : String(err)
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // 重置密码
  async function resetPassword(resetData: { token: string; password: string; confirmPassword: string }) {
    isLoading.value = true
    error.value = null

    try {
      const result = await post('/api/v1/auth/reset-password', resetData)
      return result
    } catch (err) {
      error.value = err instanceof Error ? err.message : String(err)
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // 验证邮箱
  async function verifyEmail(verificationToken: string) {
    isLoading.value = true
    error.value = null

    try {
      const result = await post('/api/v1/auth/verify-email', { token: verificationToken })
      if (user.value) {
        user.value.email_verified = true
        SecureStorage.setUser(user.value)
      }
      return result
    } catch (err) {
      error.value = err instanceof Error ? err.message : String(err)
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // 重新发送验证邮件
  async function resendVerificationEmail() {
    isLoading.value = true
    error.value = null

    try {
      const result = await post('/api/v1/auth/resend-verification')
      return result
    } catch (err) {
      error.value = err instanceof Error ? err.message : String(err)
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // 启用2FA
  async function enable2FA() {
    isLoading.value = true
    error.value = null

    try {
      const result = await post('/api/v1/auth/2fa/enable')
      return result
    } catch (err) {
      error.value = err instanceof Error ? err.message : String(err)
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // 验证2FA
  async function verify2FA(code: string) {
    isLoading.value = true
    error.value = null

    try {
      const result = await post('/api/v1/auth/2fa/verify', { code })
      if (user.value) {
        user.value.two_factor_enabled = true
        SecureStorage.setUser(user.value)
      }
      return result
    } catch (err) {
      error.value = err instanceof Error ? err.message : String(err)
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // 禁用2FA
  async function disable2FA(code: string) {
    isLoading.value = true
    error.value = null

    try {
      const result = await post('/api/v1/auth/2fa/disable', { code })
      if (user.value) {
        user.value.two_factor_enabled = false
        SecureStorage.setUser(user.value)
      }
      return result
    } catch (err) {
      error.value = err instanceof Error ? err.message : String(err)
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // 检查用户权限
  function hasPermission(permission: string): boolean {
    return userPermissions.value.includes(permission)
  }

  // 检查用户角色
  function hasRole(role: string): boolean {
    return userRoles.value.includes(role)
  }

  // 处理token过期
  function handleTokenExpiration() {
    logout()
  }

  // 验证token格式
  function isValidToken(tokenToValidate: string): boolean {
    if (!tokenToValidate || typeof tokenToValidate !== 'string') return false
    // 简单的JWT格式检查
    const parts = tokenToValidate.split('.')
    return parts.length === 3
  }

  // 持久化认证状态
  function persistAuth() {
    if (token.value && user.value) {
      SecureStorage.setToken(token.value)
      SecureStorage.setUser(user.value)
    }
  }

  // 恢复认证状态
  function restoreAuth() {
    try {
      initializeFromStorage()
    } catch (error) {
      console.error('Failed to restore auth state:', error)
      logout()
    }
  }

  return {
    // 状态
    user,
    token,
    isLoading,
    error,

    // 计算属性
    isAuthenticated,
    isFirstTimeLogin,
    userPermissions,
    userRoles,

    // 基础方法
    login,
    register,
    logout,
    initializeUser,
    updateUser,
    markFirstTimeSetupComplete,
    getAuthHeaders,
    clearError,
    shouldRefreshToken,
    refreshToken,

    // 扩展方法
    updateProfile,
    changePassword,
    requestPasswordReset,
    resetPassword,
    verifyEmail,
    resendVerificationEmail,
    enable2FA,
    verify2FA,
    disable2FA,

    // 权限和角色
    hasPermission,
    hasRole,

    // 工具方法
    handleTokenExpiration,
    isValidToken,
    persistAuth,
    restoreAuth,
  }
})