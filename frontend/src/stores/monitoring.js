/**
 * 监控数据管理 Store
 * 使用 Pinia 管理监控页面的状态和数据
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { monitoringAPI, cachedApiCall } from '@/api/monitoring'

export const useMonitoringStore = defineStore('monitoring', () => {
  // 状态数据
  const loading = ref(false)
  const error = ref(null)
  const lastUpdated = ref(null)
  
  // KPI指标数据
  const kpiMetrics = ref({
    success_rate: { value: 0, trend: { direction: 'stable', value: 0, text: '' }, format: 'percentage', color: 'success', icon: 'mdi-check-circle' },
    avg_processing_time: { value: 0, trend: { direction: 'stable', value: 0, text: '' }, format: 'duration', color: 'info', icon: 'mdi-clock-outline' },
    processing_count: { value: 0, trend: { direction: 'stable', value: 0, text: '' }, format: 'number', color: 'warning', icon: 'mdi-cog' },
    stuck_count: { value: 0, trend: { direction: 'stable', value: 0, text: '' }, format: 'number', color: 'success', icon: 'mdi-alert-circle' }
  })
  
  // 流程图数据
  const flowNodes = ref([])
  const flowConnections = ref([])
  
  // 最近信号数据
  const recentSignals = ref([])
  
  // 告警数据
  const activeAlerts = ref([])
  
  // 自动刷新相关
  const refreshInterval = ref(null)
  const autoRefreshEnabled = ref(true)
  const refreshIntervalSeconds = ref(30)
  
  // 计算属性
  const hasActiveAlerts = computed(() => activeAlerts.value.length > 0)
  const criticalAlertsCount = computed(() => 
    activeAlerts.value.filter(alert => alert.severity === 'critical').length
  )
  const isHealthy = computed(() => 
    kpiMetrics.value.success_rate.value > 80 && 
    kpiMetrics.value.stuck_count.value === 0 &&
    !hasActiveAlerts.value
  )
  
  // 获取KPI指标数据
  const fetchKPIMetrics = async (hours = 24) => {
    try {
      const response = await cachedApiCall(
        `kpi-metrics-${hours}`,
        () => monitoringAPI.getKPIMetrics(hours)
      )
      
      if (response.success && response.data) {
        kpiMetrics.value = response.data
      }
    } catch (err) {
      console.error('Failed to fetch KPI metrics:', err)
      error.value = err.message || '获取KPI指标失败'
    }
  }
  
  // 获取流程状态数据
  const fetchFlowStatus = async () => {
    try {
      const response = await cachedApiCall(
        'flow-status',
        () => monitoringAPI.getFlowStatus()
      )
      
      if (response.success && response.data) {
        flowNodes.value = response.data.nodes || []
        flowConnections.value = response.data.connections || []
      }
    } catch (err) {
      console.error('Failed to fetch flow status:', err)
      error.value = err.message || '获取流程状态失败'
    }
  }
  
  // 获取最近信号数据
  const fetchRecentSignals = async (limit = 10) => {
    try {
      const response = await cachedApiCall(
        `recent-signals-${limit}`,
        () => monitoringAPI.getRecentSignals(limit)
      )
      
      if (response.success && response.data) {
        recentSignals.value = response.data
      }
    } catch (err) {
      console.error('Failed to fetch recent signals:', err)
      error.value = err.message || '获取最近信号失败'
    }
  }
  
  // 获取活跃告警
  const fetchActiveAlerts = async () => {
    try {
      const response = await cachedApiCall(
        'active-alerts',
        () => monitoringAPI.getActiveAlerts()
      )
      
      if (response.success && response.data) {
        activeAlerts.value = response.data
      }
    } catch (err) {
      console.error('Failed to fetch active alerts:', err)
      error.value = err.message || '获取告警信息失败'
    }
  }
  
  // 获取所有监控数据
  const fetchAllMonitoringData = async () => {
    if (loading.value) return
    
    loading.value = true
    error.value = null
    
    try {
      await Promise.all([
        fetchKPIMetrics(),
        fetchFlowStatus(),
        fetchRecentSignals(),
        fetchActiveAlerts()
      ])
      
      lastUpdated.value = new Date()
    } catch (err) {
      console.error('Failed to fetch monitoring data:', err)
      error.value = err.message || '获取监控数据失败'
    } finally {
      loading.value = false
    }
  }
  
  // 刷新单个数据源
  const refreshKPIMetrics = () => fetchKPIMetrics()
  const refreshFlowStatus = () => fetchFlowStatus()
  const refreshRecentSignals = () => fetchRecentSignals()
  const refreshActiveAlerts = () => fetchActiveAlerts()
  
  // 启动自动刷新
  const startAutoRefresh = () => {
    if (refreshInterval.value) {
      clearInterval(refreshInterval.value)
    }
    
    if (autoRefreshEnabled.value) {
      refreshInterval.value = setInterval(() => {
        fetchAllMonitoringData()
      }, refreshIntervalSeconds.value * 1000)
    }
  }
  
  // 停止自动刷新
  const stopAutoRefresh = () => {
    if (refreshInterval.value) {
      clearInterval(refreshInterval.value)
      refreshInterval.value = null
    }
  }
  
  // 切换自动刷新
  const toggleAutoRefresh = () => {
    autoRefreshEnabled.value = !autoRefreshEnabled.value
    if (autoRefreshEnabled.value) {
      startAutoRefresh()
    } else {
      stopAutoRefresh()
    }
  }
  
  // 设置刷新间隔
  const setRefreshInterval = (seconds) => {
    refreshIntervalSeconds.value = seconds
    if (autoRefreshEnabled.value) {
      startAutoRefresh()
    }
  }
  
  // 清除错误
  const clearError = () => {
    error.value = null
  }
  
  // 重置所有数据
  const resetData = () => {
    kpiMetrics.value = {
      success_rate: { value: 0, trend: { direction: 'stable', value: 0, text: '' }, format: 'percentage', color: 'success', icon: 'mdi-check-circle' },
      avg_processing_time: { value: 0, trend: { direction: 'stable', value: 0, text: '' }, format: 'duration', color: 'info', icon: 'mdi-clock-outline' },
      processing_count: { value: 0, trend: { direction: 'stable', value: 0, text: '' }, format: 'number', color: 'warning', icon: 'mdi-cog' },
      stuck_count: { value: 0, trend: { direction: 'stable', value: 0, text: '' }, format: 'number', color: 'success', icon: 'mdi-alert-circle' }
    }
    flowNodes.value = []
    flowConnections.value = []
    recentSignals.value = []
    activeAlerts.value = []
    lastUpdated.value = null
    error.value = null
  }
  
  // 更新KPI指标
  const updateKPIMetrics = (data) => {
    kpiMetrics.value = { ...kpiMetrics.value, ...data }
  }
  
  // 更新流程节点
  const updateFlowNodes = (nodes) => {
    flowNodes.value = nodes
  }
  
  // 添加告警
  const addAlert = (alert) => {
    const existingIndex = activeAlerts.value.findIndex(a => a.id === alert.id)
    if (existingIndex >= 0) {
      activeAlerts.value[existingIndex] = alert
    } else {
      activeAlerts.value.push(alert)
    }
  }
  
  // 移除告警
  const removeAlert = (alertId) => {
    const index = activeAlerts.value.findIndex(a => a.id === alertId)
    if (index >= 0) {
      activeAlerts.value.splice(index, 1)
    }
  }
  
  // 清除所有告警
  const clearAllAlerts = () => {
    activeAlerts.value = []
  }
  
  // 获取节点详情
  const getNodeDetails = async (nodeId) => {
    try {
      const response = await monitoringAPI.getNodeDetails(nodeId)
      return response.success ? response.data : null
    } catch (err) {
      console.error('Failed to get node details:', err)
      return null
    }
  }
  
  return {
    // 状态
    loading,
    error,
    lastUpdated,
    
    // 数据
    kpiMetrics,
    flowNodes,
    flowConnections,
    recentSignals,
    activeAlerts,
    
    // 自动刷新
    refreshInterval,
    autoRefreshEnabled,
    refreshIntervalSeconds,
    
    // 计算属性
    hasActiveAlerts,
    criticalAlertsCount,
    isHealthy,
    
    // 方法
    fetchKPIMetrics,
    fetchFlowStatus,
    fetchRecentSignals,
    fetchActiveAlerts,
    fetchAllMonitoringData,
    
    // 刷新方法
    refreshKPIMetrics,
    refreshFlowStatus,
    refreshRecentSignals,
    refreshActiveAlerts,
    
    // 自动刷新控制
    startAutoRefresh,
    stopAutoRefresh,
    toggleAutoRefresh,
    setRefreshInterval,
    
    // 工具方法
    clearError,
    resetData,
    updateKPIMetrics,
    updateFlowNodes,
    addAlert,
    removeAlert,
    clearAllAlerts,
    getNodeDetails
  }
})
