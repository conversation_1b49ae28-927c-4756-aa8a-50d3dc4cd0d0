/**
 * 安全存储工具类
 * 提供更安全的token存储方式
 */

export class SecureStorage {
  private static readonly TOKEN_KEY = 'auth_token'
  private static readonly USER_KEY = 'user_data'

  /**
   * 简单的加密实现（生产环境应使用更强的加密）
   * @param data 要加密的数据
   * @returns 加密后的数据
   */
  private static encrypt(data: string): string {
    try {
      // 使用base64编码作为简单的混淆（不是真正的加密）
      // 在生产环境中，应该使用更强的加密算法
      return btoa(encodeURIComponent(data))
    } catch {
      return data
    }
  }

  /**
   * 解密数据
   * @param data 要解密的数据
   * @returns 解密后的数据
   */
  private static decrypt(data: string): string {
    try {
      return decodeURIComponent(atob(data))
    } catch {
      // 如果解密失败，返回原始数据（可能是未加密的）
      return data
    }
  }

  /**
   * 存储token
   * @param token JWT token
   */
  static setToken(token: string): void {
    try {
      const encrypted = this.encrypt(token)
      // 使用sessionStorage而不是localStorage提高安全性
      // sessionStorage在浏览器关闭后会自动清除
      sessionStorage.setItem(this.TOKEN_KEY, encrypted)
    } catch (error) {
      console.error('Failed to store token:', error)
    }
  }

  /**
   * 获取token
   * @returns JWT token或null
   */
  static getToken(): string | null {
    try {
      const encrypted = sessionStorage.getItem(this.TOKEN_KEY)
      if (!encrypted) return null
      
      const decrypted = this.decrypt(encrypted)
      // 验证token格式（基本检查）
      if (!decrypted || typeof decrypted !== 'string' || decrypted.length < 10) {
        this.removeToken()
        return null
      }
      
      return decrypted
    } catch (error) {
      console.error('Failed to retrieve token:', error)
      // 清除损坏的token
      this.removeToken()
      return null
    }
  }

  /**
   * 移除token
   */
  static removeToken(): void {
    try {
      sessionStorage.removeItem(this.TOKEN_KEY)
    } catch (error) {
      console.error('Failed to remove token:', error)
    }
  }

  /**
   * 存储用户数据
   * @param user 用户数据
   */
  static setUser(user: any): void {
    try {
      const encrypted = this.encrypt(JSON.stringify(user))
      sessionStorage.setItem(this.USER_KEY, encrypted)
    } catch (error) {
      console.error('Failed to store user data:', error)
    }
  }

  /**
   * 获取用户数据
   * @returns 用户数据或null
   */
  static getUser(): any | null {
    try {
      const encrypted = sessionStorage.getItem(this.USER_KEY)
      if (!encrypted || encrypted === 'undefined' || encrypted === 'null') {
        return null
      }

      const decrypted = this.decrypt(encrypted)
      if (!decrypted || decrypted === 'undefined' || decrypted === 'null') {
        return null
      }

      // 确保decrypted是有效的JSON字符串
      if (typeof decrypted !== 'string' || decrypted.trim() === '') {
        return null
      }

      const parsed = JSON.parse(decrypted)
      // 确保返回的对象有基本结构
      return parsed && typeof parsed === 'object' ? parsed : null
    } catch (error) {
      console.error('Failed to retrieve user data:', error)
      // 清除损坏的数据
      this.removeUser()
      return null
    }
  }

  /**
   * 移除用户数据
   */
  static removeUser(): void {
    try {
      sessionStorage.removeItem(this.USER_KEY)
    } catch (error) {
      console.error('Failed to remove user data:', error)
    }
  }

  /**
   * 清除所有存储的数据
   */
  static clear(): void {
    this.removeToken()
    this.removeUser()
  }

  /**
   * 检查token是否即将过期
   * @param token JWT token
   * @returns 是否即将过期
   */
  static isTokenExpiringSoon(token: string): boolean {
    try {
      // 解析JWT token的payload部分
      const parts = token.split('.')
      if (parts.length !== 3) return true

      const payload = JSON.parse(atob(parts[1]))
      const exp = payload.exp
      
      if (!exp) return true

      // 检查是否在5分钟内过期
      const now = Math.floor(Date.now() / 1000)
      const fiveMinutes = 5 * 60
      
      return (exp - now) < fiveMinutes
    } catch {
      return true
    }
  }

  /**
   * 获取token的过期时间
   * @param token JWT token
   * @returns 过期时间戳或null
   */
  static getTokenExpiration(token: string): number | null {
    try {
      const parts = token.split('.')
      if (parts.length !== 3) return null

      const payload = JSON.parse(atob(parts[1]))
      return payload.exp || null
    } catch {
      return null
    }
  }
}
