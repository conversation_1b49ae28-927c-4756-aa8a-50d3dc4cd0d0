/**
 * Decimal工具函数
 * 用于处理金融精度计算，避免JavaScript number的精度问题
 */

import Decimal from 'decimal.js'

// 类型定义
type DecimalValue = string | number

/**
 * 创建Decimal实例
 * @param value - 数值字符串或数字
 * @returns Decimal实例
 */
export function decimal(value: DecimalValue) {
  return new Decimal(value)
}

/**
 * 安全地将Decimal转换为显示字符串
 * @param value - Decimal值或字符串
 * @param decimals - 小数位数，默认为8位
 * @returns 格式化的字符串
 */
export function formatDecimal(value: DecimalValue, decimals: number = 8): string {
  if (!value) return '0'
  const d = new Decimal(value)
  return d.toFixed(decimals)
}

/**
 * 格式化为货币显示
 * @param value - Decimal值或字符串
 * @param currency - 货币符号，默认为'$'
 * @param decimals - 小数位数，默认为2位
 * @returns 格式化的货币字符串
 */
export function formatCurrency(value: DecimalValue, currency: string = '$', decimals: number = 2): string {
  if (!value) return `${currency}0.00`
  const d = new Decimal(value)
  return `${currency}${d.toFixed(decimals)}`
}

/**
 * 格式化百分比
 * @param value - Decimal值或字符串
 * @param decimals - 小数位数，默认为2位
 * @returns 格式化的百分比字符串
 */
export function formatPercentage(value: DecimalValue, decimals: number = 2): string {
  if (!value) return '0.00%'
  const d = new Decimal(value)
  return `${d.toFixed(decimals)}%`
}

/**
 * 安全的加法运算
 * @param a - 第一个数值
 * @param b - 第二个数值
 * @returns 计算结果的字符串
 */
export function add(a: DecimalValue, b: DecimalValue): string {
  return new Decimal(a).plus(new Decimal(b)).toString()
}

/**
 * 安全的减法运算
 * @param a - 被减数
 * @param b - 减数
 * @returns 计算结果的字符串
 */
export function subtract(a: DecimalValue, b: DecimalValue): string {
  return new Decimal(a).minus(new Decimal(b)).toString()
}

/**
 * 安全的乘法运算
 * @param a - 第一个数值
 * @param b - 第二个数值
 * @returns 计算结果的字符串
 */
export function multiply(a: DecimalValue, b: DecimalValue): string {
  return new Decimal(a).times(new Decimal(b)).toString()
}

/**
 * 安全的除法运算
 * @param a - 被除数
 * @param b - 除数
 * @returns 计算结果的字符串
 */
export function divide(a: DecimalValue, b: DecimalValue): string {
  return new Decimal(a).dividedBy(new Decimal(b)).toString()
}

/**
 * 比较两个数值
 * @param a - 第一个数值
 * @param b - 第二个数值
 * @returns -1: a < b, 0: a = b, 1: a > b
 */
export function compare(a: DecimalValue, b: DecimalValue): number {
  return new Decimal(a).comparedTo(new Decimal(b))
}

/**
 * 检查数值是否为零
 * @param value - 要检查的数值
 * @returns 是否为零
 */
export function isZero(value: DecimalValue): boolean {
  return new Decimal(value).isZero()
}

/**
 * 检查数值是否为正数
 * @param value - 要检查的数值
 * @returns 是否为正数
 */
export function isPositive(value: DecimalValue): boolean {
  return new Decimal(value).greaterThan(0)
}

/**
 * 检查数值是否为负数
 * @param value - 要检查的数值
 * @returns 是否为负数
 */
export function isNegative(value: DecimalValue): boolean {
  return new Decimal(value).lessThan(0)
}

/**
 * 获取绝对值
 * @param value - 输入数值
 * @returns 绝对值的字符串
 */
export function abs(value: DecimalValue): string {
  return new Decimal(value).abs().toString()
}

/**
 * 计算百分比变化
 * @param oldValue - 原始值
 * @param newValue - 新值
 * @returns 百分比变化的字符串
 */
export function percentageChange(oldValue: DecimalValue, newValue: DecimalValue): string {
  const old = new Decimal(oldValue)
  const newVal = new Decimal(newValue)

  if (old.isZero()) {
    return newVal.isZero() ? '0' : '100'
  }

  return newVal.minus(old).dividedBy(old).times(100).toString()
}

/**
 * 验证字符串是否为有效的数值
 * @param value - 要验证的字符串
 * @returns 是否为有效数值
 */
export function isValidDecimal(value: string): boolean {
  try {
    new Decimal(value)
    return true
  } catch {
    return false
  }
}

/**
 * 安全地解析数值字符串，如果无效则返回默认值
 * @param value - 要解析的字符串
 * @param defaultValue - 默认值
 * @returns 解析结果的字符串
 */
export function safeParseDecimal(value: string, defaultValue: string = '0'): string {
  try {
    return new Decimal(value).toString()
  } catch {
    return defaultValue
  }
}
