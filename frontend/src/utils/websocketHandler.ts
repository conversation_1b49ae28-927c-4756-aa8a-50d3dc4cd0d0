/**
 * 统一的WebSocket消息处理器
 * 提供类型安全的消息处理和分发机制
 */

import type { WebSocketMessage, OrderUpdatePayload } from '@/types'
import { useOrderStore } from '@/stores/order'
import { useAgentStore } from '@/stores/agent'
import { useUIStore } from '@/stores/ui'

export class WebSocketMessageHandler {
  private static instance: WebSocketMessageHandler
  private _orderStore: any = null
  private _agentStore: any = null
  private _uiStore: any = null

  private constructor() {}

  private get orderStore() {
    if (!this._orderStore) {
      this._orderStore = useOrderStore()
    }
    return this._orderStore
  }

  private get agentStore() {
    if (!this._agentStore) {
      this._agentStore = useAgentStore()
    }
    return this._agentStore
  }

  private get uiStore() {
    if (!this._uiStore) {
      this._uiStore = useUIStore()
    }
    return this._uiStore
  }

  static getInstance(): WebSocketMessageHandler {
    if (!WebSocketMessageHandler.instance) {
      WebSocketMessageHandler.instance = new WebSocketMessageHandler()
    }
    return WebSocketMessageHandler.instance
  }

  /**
   * 处理WebSocket消息
   * @param message WebSocket消息
   */
  handleMessage(message: WebSocketMessage): void {
    try {
      console.log('处理WebSocket消息:', message.event_type, message.payload)

      // 处理event_type为null、undefined或None的情况
      if (!message.event_type || message.event_type === 'None' || message.event_type === null) {
        console.warn('收到无效的WebSocket消息类型:', message.event_type, '消息内容:', message)
        return
      }

      switch (message.event_type) {
        case 'ORDER_UPDATE':
          this.handleOrderUpdate(message.payload as OrderUpdatePayload)
          break

        case 'AGENT_STATE_TRANSITION':
          this.handleAgentStateTransition(message.payload)
          break

        case 'NOTIFICATION':
          this.handleNotification(message.payload)
          break

        case 'HEARTBEAT':
          this.handleHeartbeat(message.payload)
          break

        case 'PING':
          this.handlePing(message.payload)
          break

        case 'PONG':
          this.handlePong(message.payload)
          break

        case 'CONNECTION_ESTABLISHED':
          this.handleConnectionEstablished(message.payload)
          break

        case 'ERROR':
          this.handleError(message.payload)
          break

        default:
          console.warn('未知的WebSocket消息类型:', message.event_type)
      }
    } catch (error) {
      console.error('处理WebSocket消息时出错:', error)
      this.uiStore.showError('处理实时消息时出错')
    }
  }

  /**
   * 处理订单更新消息
   */
  private handleOrderUpdate(payload: OrderUpdatePayload): void {
    console.log('订单更新:', payload)
    
    // 更新订单store中的数据
    this.orderStore.updateOrderFromWebSocket(payload)
    
    // 显示通知
    const statusText = this.getOrderStatusText(payload.status)
    this.uiStore.showSuccess(`订单 ${payload.symbol} ${statusText}`)
  }

  /**
   * 处理Agent状态转换消息
   */
  private handleAgentStateTransition(payload: any): void {
    console.log('Agent状态变化:', payload)
    
    // 更新agent store中的状态
    this.agentStore.updateStateFromWebSocket(payload)
    
    // 显示状态变化通知
    if (payload.status && payload.current_action) {
      this.uiStore.showInfo(`AI Agent: ${payload.current_action}`)
    }
  }

  /**
   * 处理通知消息
   */
  private handleNotification(payload: any): void {
    const { type = 'info', title, message, duration } = payload
    
    this.uiStore.addNotification({
      type,
      title,
      message,
      duration
    })
  }

  /**
   * 处理心跳消息
   */
  private handleHeartbeat(payload: any): void {
    console.debug('收到心跳:', payload)
    // 心跳消息通常不需要特殊处理，只是确认连接正常
  }

  /**
   * 处理PING消息
   */
  private handlePing(payload: any): void {
    console.debug('收到PING消息:', payload)
    // PING消息通常由WebSocket store自动处理，这里只记录
  }

  /**
   * 处理PONG消息
   */
  private handlePong(payload: any): void {
    console.debug('收到PONG消息:', payload)
    // PONG消息通常由WebSocket store自动处理，这里只记录
  }

  /**
   * 获取订单状态的中文描述
   */
  private getOrderStatusText(status: string): string {
    const statusMap: Record<string, string> = {
      'pending': '待处理',
      'filled': '已成交',
      'cancelled': '已取消',
      'rejected': '已拒绝'
    }
    return statusMap[status] || status
  }



  /**
   * 处理连接建立消息
   * @param payload 连接数据
   */
  private handleConnectionEstablished(payload: any): void {
    console.log('WebSocket连接已建立:', payload)
    this.uiStore.showSuccess('实时连接已建立')
  }

  /**
   * 处理错误消息
   * @param payload 错误数据
   */
  private handleError(payload: any): void {
    console.error('WebSocket错误详情:', payload)

    // 如果是非关键错误，只在控制台显示，不打扰用户
    if (payload.message && (
      payload.message.includes('Unknown event type') ||
      payload.message.includes('Missing required field: event_type')
    )) {
      console.warn('WebSocket收到协议错误，这通常不影响功能:', payload.message)
      return
    }

    this.uiStore.showError(payload.message || '实时连接出现错误')
  }
}

// 导出单例实例
export const websocketHandler = WebSocketMessageHandler.getInstance()

// 保存事件监听器引用以便清理
let messageEventListener: EventListener | null = null

/**
 * 初始化WebSocket消息处理
 * 在应用启动时调用
 */
export function initializeWebSocketHandler(): void {
  // 监听全局WebSocket消息事件
  messageEventListener = ((event: CustomEvent) => {
    const message = event.detail as WebSocketMessage
    websocketHandler.handleMessage(message)
  }) as EventListener

  window.addEventListener('ws-message', messageEventListener)
  console.log('WebSocket消息处理器已初始化')
}

/**
 * 清理WebSocket消息处理
 * 在应用销毁时调用
 */
export function cleanupWebSocketHandler(): void {
  if (messageEventListener) {
    window.removeEventListener('ws-message', messageEventListener)
    messageEventListener = null
  }
  console.log('WebSocket消息处理器已清理')
}
