/**
 * 智能页面缓存系统
 * 提供页面数据缓存、预加载和智能失效机制
 */

interface CacheEntry<T = any> {
  data: T
  timestamp: number
  ttl: number // Time to live in milliseconds
  key: string
  version?: string
}

interface CacheOptions {
  ttl?: number // 缓存时间，默认5分钟
  version?: string // 数据版本，用于缓存失效
  maxSize?: number // 最大缓存条目数
}

export class PageCache {
  private static instance: PageCache
  private cache = new Map<string, CacheEntry>()
  private readonly defaultTTL = 5 * 60 * 1000 // 5分钟
  private readonly maxSize = 50 // 最大缓存50个条目

  private constructor() {
    // 定期清理过期缓存
    setInterval(() => this.cleanup(), 60 * 1000) // 每分钟清理一次
  }

  static getInstance(): PageCache {
    if (!PageCache.instance) {
      PageCache.instance = new PageCache()
    }
    return PageCache.instance
  }

  /**
   * 设置缓存
   */
  set<T>(key: string, data: T, options: CacheOptions = {}): void {
    const ttl = options.ttl || this.defaultTTL
    const entry: CacheEntry<T> = {
      data,
      timestamp: Date.now(),
      ttl,
      key,
      version: options.version
    }

    // 如果缓存已满，删除最旧的条目
    if (this.cache.size >= (options.maxSize || this.maxSize)) {
      this.evictOldest()
    }

    this.cache.set(key, entry)
  }

  /**
   * 获取缓存
   */
  get<T>(key: string, version?: string): T | null {
    const entry = this.cache.get(key)
    
    if (!entry) {
      return null
    }

    // 检查是否过期
    if (this.isExpired(entry)) {
      this.cache.delete(key)
      return null
    }

    // 检查版本是否匹配
    if (version && entry.version && entry.version !== version) {
      this.cache.delete(key)
      return null
    }

    return entry.data as T
  }

  /**
   * 删除缓存
   */
  delete(key: string): boolean {
    return this.cache.delete(key)
  }

  /**
   * 清空所有缓存
   */
  clear(): void {
    this.cache.clear()
  }

  /**
   * 检查缓存是否存在且有效
   */
  has(key: string, version?: string): boolean {
    const entry = this.cache.get(key)
    
    if (!entry || this.isExpired(entry)) {
      return false
    }

    if (version && entry.version && entry.version !== version) {
      return false
    }

    return true
  }

  /**
   * 获取缓存统计信息
   */
  getStats(): {
    size: number
    keys: string[]
    totalMemory: number
  } {
    const keys = Array.from(this.cache.keys())
    const totalMemory = this.estimateMemoryUsage()

    return {
      size: this.cache.size,
      keys,
      totalMemory
    }
  }

  /**
   * 预加载数据到缓存
   */
  async preload<T>(
    key: string, 
    loader: () => Promise<T>, 
    options: CacheOptions = {}
  ): Promise<T> {
    // 如果缓存存在且有效，直接返回
    const cached = this.get<T>(key, options.version)
    if (cached !== null) {
      return cached
    }

    // 加载数据并缓存
    try {
      const data = await loader()
      this.set(key, data, options)
      return data
    } catch (error) {
      console.error(`预加载失败: ${key}`, error)
      throw error
    }
  }

  /**
   * 批量失效缓存（支持模式匹配）
   */
  invalidatePattern(pattern: string): number {
    let count = 0
    const regex = new RegExp(pattern.replace(/\*/g, '.*'))

    for (const key of this.cache.keys()) {
      if (regex.test(key)) {
        this.cache.delete(key)
        count++
      }
    }

    return count
  }

  /**
   * 检查缓存条目是否过期
   */
  private isExpired(entry: CacheEntry): boolean {
    return Date.now() - entry.timestamp > entry.ttl
  }

  /**
   * 清理过期缓存
   */
  private cleanup(): void {
    for (const [key, entry] of this.cache.entries()) {
      if (this.isExpired(entry)) {
        this.cache.delete(key)
      }
    }
  }

  /**
   * 删除最旧的缓存条目
   */
  private evictOldest(): void {
    let oldestKey = ''
    let oldestTimestamp = Date.now()

    for (const [key, entry] of this.cache.entries()) {
      if (entry.timestamp < oldestTimestamp) {
        oldestTimestamp = entry.timestamp
        oldestKey = key
      }
    }

    if (oldestKey) {
      this.cache.delete(oldestKey)
    }
  }

  /**
   * 估算内存使用量（简单估算）
   */
  private estimateMemoryUsage(): number {
    let totalSize = 0
    
    for (const entry of this.cache.values()) {
      // 简单估算：JSON字符串长度 * 2（Unicode字符）
      totalSize += JSON.stringify(entry.data).length * 2
    }

    return totalSize
  }
}

// 导出单例实例
export const pageCache = PageCache.getInstance()

/**
 * 页面缓存组合式函数
 */
export function usePageCache() {
  return {
    set: <T>(key: string, data: T, options?: CacheOptions) => 
      pageCache.set(key, data, options),
    
    get: <T>(key: string, version?: string) => 
      pageCache.get<T>(key, version),
    
    delete: (key: string) => 
      pageCache.delete(key),
    
    has: (key: string, version?: string) => 
      pageCache.has(key, version),
    
    preload: <T>(key: string, loader: () => Promise<T>, options?: CacheOptions) => 
      pageCache.preload(key, loader, options),
    
    invalidatePattern: (pattern: string) => 
      pageCache.invalidatePattern(pattern),
    
    getStats: () => 
      pageCache.getStats()
  }
}

/**
 * 缓存键生成器
 */
export const CacheKeys = {
  orders: (filters?: any) => `orders:${JSON.stringify(filters || {})}`,
  orderDetail: (id: string) => `order:${id}`,
  agentState: () => 'agent:state',
  agentConfig: () => 'agent:config',
  userProfile: (userId: string) => `user:${userId}`,
  marketData: (symbol: string) => `market:${symbol}`,
  dashboard: (userId: string) => `dashboard:${userId}`
}
