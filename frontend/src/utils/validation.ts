/**
 * 运行时类型验证工具
 * 使用zod进行API响应验证，确保前后端类型一致性
 */

import { z } from 'zod'

// 基础验证器
const DecimalString = z.string().refine(
  (val) => !isNaN(Number(val)) && isFinite(Number(val)),
  { message: "必须是有效的数值字符串" }
)

const UUIDString = z.string().uuid()
const DateTimeString = z.string().datetime()

// 订单相关验证器
export const OrderStatusSchema = z.enum(['pending', 'filled', 'cancelled', 'failed', 'partially_filled', 'expired'])
export const OrderSideSchema = z.enum(['buy', 'sell'])
export const OrderTypeSchema = z.enum(['market', 'limit', 'stop', 'stop_limit'])

export const OrderSchema = z.object({
  id: UUIDString,
  symbol: z.string(),
  side: OrderSideSchema,
  status: OrderStatusSchema,
  quantity: DecimalString,
  price: DecimalString,
  filled_quantity: DecimalString.optional(),
  pnl: DecimalString.optional(),
  pnl_usd: DecimalString.optional(),
  created_at: DateTimeString,
  updated_at: DateTimeString.optional(),
  filled_at: DateTimeString.optional()
})

export const ConditionalOrderSchema = z.object({
  id: UUIDString,
  symbol: z.string(),
  side: OrderSideSchema,
  condition_type: z.enum(['price_above', 'price_below', 'rsi_above', 'rsi_below']),
  condition_value: DecimalString,
  quantity: DecimalString,
  price: DecimalString.optional(),
  status: z.enum(['active', 'triggered', 'cancelled']),
  created_at: DateTimeString
})

export const OrderUpdatePayloadSchema = z.object({
  id: UUIDString,
  symbol: z.string(),
  side: OrderSideSchema,
  status: OrderStatusSchema,
  quantity: DecimalString,
  price: DecimalString
})

export const OrderStatsSchema = z.object({
  total: z.number(),
  pending: z.number(),
  filled: z.number(),
  cancelled: z.number(),
  failed: z.number(),
  partially_filled: z.number(),
  totalVolume: DecimalString,
  totalPnL: DecimalString
})

// 配置相关验证器
export const ExchangeConfigSchema = z.object({
  id: UUIDString,
  name: z.string(),
  enabled: z.boolean(),
  api_key: z.string(),
  api_secret: z.string(),
  sandbox: z.boolean(),
  trading_pairs: z.array(z.string()),
  created_at: DateTimeString,
  updated_at: DateTimeString.optional()
})

export const RiskConfigSchema = z.object({
  id: UUIDString.optional(),
  max_position_size: DecimalString.optional(),
  max_position_size_usd: DecimalString.optional(),
  stop_loss_percentage: DecimalString.optional(),
  take_profit_percentage: DecimalString.optional(),
  max_daily_loss: DecimalString.optional(),
  max_daily_loss_usd: DecimalString.optional(),
  max_open_positions: z.number().optional(),
  default_stop_loss_pct: DecimalString.optional(),
  enable_stop_loss: z.boolean().optional(),
  enable_take_profit: z.boolean().optional(),
  risk_level: z.enum(['low', 'medium', 'high']).optional(),
  created_at: DateTimeString.optional(),
  updated_at: DateTimeString.optional()
})

export const SystemConfigSchema = z.object({
  auto_trading_enabled: z.boolean(),
  max_concurrent_trades: z.number(),
  default_trade_amount: DecimalString,
  order_timeout_seconds: z.number().optional(),
  log_level: z.string().optional(),
  notification_settings: z.object({
    email: z.boolean(),
    sms: z.boolean(),
    push: z.boolean()
  })
})

export const SignalConfigSchema = z.object({
  id: UUIDString,
  name: z.string(),
  enabled: z.boolean(),
  parameters: z.record(z.string(), z.any()),
  created_at: DateTimeString,
  updated_at: DateTimeString.optional()
})

export const AppConfigSchema = z.object({
  exchanges: z.array(ExchangeConfigSchema),
  risk: RiskConfigSchema,
  signals: z.array(SignalConfigSchema),
  system: SystemConfigSchema
})

// WebSocket相关验证器
export const WebSocketMessageSchema = z.object({
  event_type: z.enum(['ORDER_UPDATE', 'AGENT_STATE_TRANSITION', 'NOTIFICATION', 'HEARTBEAT', 'CONNECTION_ESTABLISHED', 'ERROR']),
  payload: z.any(),
  timestamp: DateTimeString
})

// Agent相关验证器
export const AgentStateSchema = z.object({
  status: z.enum(['idle', 'analyzing', 'trading', 'error']),
  current_action: z.string().optional(),
  last_update: DateTimeString,
  error_message: z.string().optional()
})

// 验证函数
export function validateOrder(data: unknown) {
  return OrderSchema.parse(data)
}

export function validateOrderArray(data: unknown) {
  return z.array(OrderSchema).parse(data)
}

export function validateOrderStats(data: unknown) {
  return OrderStatsSchema.parse(data)
}

export function validateExchangeConfig(data: unknown) {
  return ExchangeConfigSchema.parse(data)
}

export function validateRiskConfig(data: unknown) {
  return RiskConfigSchema.parse(data)
}

export function validateAppConfig(data: unknown) {
  return AppConfigSchema.parse(data)
}

export function validateWebSocketMessage(data: unknown) {
  return WebSocketMessageSchema.parse(data)
}

export function validateAgentState(data: unknown) {
  return AgentStateSchema.parse(data)
}

// 安全验证函数 - 返回验证结果而不抛出异常
export function safeValidateOrder(data: unknown): { success: true; data: z.infer<typeof OrderSchema> } | { success: false; error: string } {
  try {
    const result = OrderSchema.parse(data)
    return { success: true, data: result }
  } catch (error) {
    return { success: false, error: error instanceof Error ? error.message : 'Unknown validation error' }
  }
}

export function safeValidateOrderArray(data: unknown): { success: true; data: z.infer<typeof OrderSchema>[] } | { success: false; error: string } {
  try {
    const result = z.array(OrderSchema).parse(data)
    return { success: true, data: result }
  } catch (error) {
    return { success: false, error: error instanceof Error ? error.message : 'Unknown validation error' }
  }
}

export function safeValidateAppConfig(data: unknown): { success: true; data: z.infer<typeof AppConfigSchema> } | { success: false; error: string } {
  try {
    const result = AppConfigSchema.parse(data)
    return { success: true, data: result }
  } catch (error) {
    return { success: false, error: error instanceof Error ? error.message : 'Unknown validation error' }
  }
}

// 类型导出
export type ValidatedOrder = z.infer<typeof OrderSchema>
export type ValidatedOrderStats = z.infer<typeof OrderStatsSchema>
export type ValidatedExchangeConfig = z.infer<typeof ExchangeConfigSchema>
export type ValidatedRiskConfig = z.infer<typeof RiskConfigSchema>
export type ValidatedAppConfig = z.infer<typeof AppConfigSchema>
export type ValidatedWebSocketMessage = z.infer<typeof WebSocketMessageSchema>
export type ValidatedAgentState = z.infer<typeof AgentStateSchema>
