/**
 * 全局错误处理系统
 * 提供统一的错误处理、日志记录和用户友好的错误消息
 */

import type { ApiError } from '@/types'
import { useUIStore } from '@/stores/ui'

export interface ErrorContext {
  component?: string
  action?: string
  userId?: string
  timestamp?: string
  userAgent?: string
  url?: string
}

export class ErrorHandler {
  private static instance: ErrorHandler
  private _uiStore: any = null

  private constructor() {}

  private get uiStore() {
    if (!this._uiStore) {
      this._uiStore = useUIStore()
    }
    return this._uiStore
  }

  static getInstance(): ErrorHandler {
    if (!ErrorHandler.instance) {
      ErrorHandler.instance = new ErrorHandler()
    }
    return ErrorHandler.instance
  }

  /**
   * 处理API错误
   */
  handleApiError(error: ApiError, context?: ErrorContext): void {
    console.error('API错误:', error, context)

    const userMessage = this.getApiErrorMessage(error)
    this.uiStore.showError(userMessage)

    // 记录错误日志
    this.logError('API_ERROR', error, context)
  }

  /**
   * 处理网络错误
   */
  handleNetworkError(error: Error, context?: ErrorContext): void {
    console.error('网络错误:', error, context)

    const userMessage = '网络连接出现问题，请检查您的网络连接'
    this.uiStore.showError(userMessage)

    this.logError('NETWORK_ERROR', error, context)
  }

  /**
   * 处理WebSocket错误
   */
  handleWebSocketError(error: Event, context?: ErrorContext): void {
    console.error('WebSocket错误:', error, context)

    const userMessage = '实时连接中断，正在尝试重新连接...'
    this.uiStore.showWarning(userMessage)

    this.logError('WEBSOCKET_ERROR', error, context)
  }

  /**
   * 处理认证错误
   */
  handleAuthError(error: Error, context?: ErrorContext): void {
    console.error('认证错误:', error, context)

    const userMessage = '登录已过期，请重新登录'
    this.uiStore.showError(userMessage)

    this.logError('AUTH_ERROR', error, context)

    // 可以在这里触发自动登出
    // const authStore = useAuthStore()
    // authStore.logout()
  }

  /**
   * 处理表单验证错误
   */
  handleValidationError(errors: Record<string, string>, context?: ErrorContext): void {
    console.warn('表单验证错误:', errors, context)

    const errorMessages = Object.values(errors)
    if (errorMessages.length > 0) {
      this.uiStore.showWarning(`请检查输入: ${errorMessages[0]}`)
    }

    this.logError('VALIDATION_ERROR', errors, context)
  }

  /**
   * 处理未知错误
   */
  handleUnknownError(error: unknown, context?: ErrorContext): void {
    console.error('未知错误:', error, context)

    const userMessage = '发生了意外错误，请稍后重试'
    this.uiStore.showError(userMessage)

    this.logError('UNKNOWN_ERROR', error, context)
  }

  /**
   * 获取API错误的用户友好消息
   */
  private getApiErrorMessage(error: ApiError): string {
    const statusMessages: Record<number, string> = {
      400: '请求参数有误，请检查输入',
      401: '登录已过期，请重新登录',
      403: '没有权限执行此操作',
      404: '请求的资源不存在',
      409: '操作冲突，请刷新页面后重试',
      422: '数据验证失败，请检查输入',
      429: '请求过于频繁，请稍后重试',
      500: '服务器内部错误，请稍后重试',
      502: '服务暂时不可用，请稍后重试',
      503: '服务维护中，请稍后重试'
    }

    return statusMessages[error.status] || error.message || '发生了未知错误'
  }

  /**
   * 记录错误日志
   */
  private logError(type: string, error: unknown, context?: ErrorContext): void {
    const logEntry = {
      type,
      error: this.serializeError(error),
      context: {
        ...context,
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        url: window.location.href
      }
    }

    // 在开发环境中输出到控制台
    if (import.meta.env.DEV) {
      console.group(`🚨 ${type}`)
      console.error('Error:', error)
      console.info('Context:', logEntry.context)
      console.groupEnd()
    }

    // 在生产环境中可以发送到日志服务
    if (import.meta.env.PROD) {
      this.sendToLogService(logEntry)
    }
  }

  /**
   * 序列化错误对象
   */
  private serializeError(error: unknown): any {
    if (error instanceof Error) {
      return {
        name: error.name,
        message: error.message,
        stack: error.stack
      }
    }
    return error
  }

  /**
   * 发送错误日志到日志服务
   */
  private sendToLogService(logEntry: any): void {
    // 这里可以集成第三方日志服务，如 Sentry、LogRocket 等
    // 暂时只是存储到本地存储作为示例
    try {
      const logs = JSON.parse(localStorage.getItem('error_logs') || '[]')
      logs.push(logEntry)
      
      // 只保留最近100条日志
      if (logs.length > 100) {
        logs.splice(0, logs.length - 100)
      }
      
      localStorage.setItem('error_logs', JSON.stringify(logs))
    } catch (e) {
      console.error('Failed to save error log:', e)
    }
  }
}

// 导出单例实例
export const errorHandler = ErrorHandler.getInstance()

/**
 * 全局错误处理初始化
 */
export function initializeErrorHandler(): void {
  // 捕获未处理的Promise拒绝
  window.addEventListener('unhandledrejection', (event) => {
    errorHandler.handleUnknownError(event.reason, {
      component: 'Global',
      action: 'unhandledrejection'
    })
  })

  // 捕获未处理的JavaScript错误
  window.addEventListener('error', (event) => {
    errorHandler.handleUnknownError(event.error, {
      component: 'Global',
      action: 'javascript_error',
      url: event.filename
    })
  })

  console.log('全局错误处理器已初始化')
}

/**
 * 错误边界组合式函数
 * 用于Vue组件中的错误处理
 */
export function useErrorHandler() {
  return {
    handleApiError: (error: ApiError, context?: ErrorContext) => 
      errorHandler.handleApiError(error, context),
    
    handleNetworkError: (error: Error, context?: ErrorContext) => 
      errorHandler.handleNetworkError(error, context),
    
    handleValidationError: (errors: Record<string, string>, context?: ErrorContext) => 
      errorHandler.handleValidationError(errors, context),
    
    handleUnknownError: (error: unknown, context?: ErrorContext) => 
      errorHandler.handleUnknownError(error, context)
  }
}
