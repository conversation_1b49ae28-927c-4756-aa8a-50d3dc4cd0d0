/**
 * Agent 相关 API
 * 封装了与AI Agent交互的API请求，包括状态查询、任务管理、待处理动作等操作
 */

import { get, post, put, del } from './client.ts'

/**
 * 获取 Agent 状态
 * @returns {Promise<import('./types').AgentStatusResponse>} 返回Agent当前状态
 */
export async function getAgentStatus() {
  return get('/agent/status')
}

/**
 * 启动 Agent
 * @returns {Promise<import('./types').StatusResponse>} 返回启动状态
 */
export async function startAgent() {
  return post('/agent/start')
}

/**
 * 停止 Agent
 * @returns {Promise<import('./types').StatusResponse>} 返回停止状态
 */
export async function stopAgent() {
  return post('/agent/stop')
}

/**
 * 重启 Agent
 * @returns {Promise<import('./types').StatusResponse>} 返回重启状态
 */
export async function restartAgent() {
  return post('/agent/restart')
}

/**
 * 获取 Agent 日志
 * @param {Object} [params={}] - 查询参数，如分页、级别过滤等
 * @returns {Promise<import('./types').AgentLogsResponse>} 返回日志列表
 */
export async function getAgentLogs(params = {}) {
  return get('/agent/logs', params)
}

/**
 * 清除 Agent 日志
 * @returns {Promise<import('./types').StatusResponse>} 返回清除状态
 */
export async function clearAgentLogs() {
  return del('/agent/logs')
}

/**
 * 获取当前任务
 * @returns {Promise<import('./types').TaskInfo>} 返回当前执行中的任务信息
 */
export async function getCurrentTask() {
  return get('/agent/task/current')
}

/**
 * 获取任务历史
 * @param {Object} [params={}] - 查询参数，如分页、时间范围等
 * @returns {Promise<import('./types').TaskHistoryResponse>} 返回任务历史列表
 */
export async function getTaskHistory(params = {}) {
  return get('/agent/tasks', params)
}

/**
 * 取消当前任务
 * @returns {Promise<import('./types').StatusResponse>} 返回取消状态
 */
export async function cancelCurrentTask() {
  return post('/agent/task/cancel')
}

/**
 * 获取待处理动作
 * @returns {Promise<import('./types').PendingActionListResponse>} 返回待处理动作列表
 */
export async function getPendingActions() {
  return get('/agent/actions/pending')
}

/**
 * 响应待处理动作
 * @param {string} actionId - 动作ID
 * @param {import('./types').ActionResponseRequest} response - 响应数据
 * @returns {Promise<import('./types').StatusResponse>} 返回响应状态
 */
export async function respondToPendingAction(actionId, response) {
  return post(`/agent/actions/${actionId}/respond`, response)
}

/**
 * 发送交易信号
 * @param {import('./types').TradingSignalRequest} signalData - 交易信号数据
 * @returns {Promise<Object>} 返回创建的任务信息
 */
export async function sendTradingSignal(signalData) {
  return post('/agent/signals', signalData)
}

/**
 * 获取信号历史
 * @param {Object} [params={}] - 查询参数，如分页、时间范围等
 * @returns {Promise<Array<Object>>} 返回信号历史列表
 */
export async function getSignalHistory(params = {}) {
  return get('/agent/signals', params)
}

/**
 * 获取 Agent 性能统计
 * @param {Object} [params={}] - 查询参数，如时间范围等
 * @returns {Promise<Object>} 返回性能统计数据
 */
export async function getAgentStats(params = {}) {
  return get('/agent/stats', params)
}

/**
 * 获取 Agent 配置
 * @returns {Promise<import('./types').AgentConfig>} 返回Agent配置
 */
export async function getAgentConfig() {
  return get('/agent/config')
}

/**
 * 更新 Agent 配置
 * @param {import('./types').AgentConfig} configData - Agent配置数据
 * @returns {Promise<import('./types').AgentConfig>} 返回更新后的配置
 */
export async function updateAgentConfig(configData) {
  return put('/agent/config', configData)
}

/**
 * 获取 Agent 健康状态
 * @returns {Promise<import('./types').AgentHealth>} 返回健康状态信息
 */
export async function getAgentHealth() {
  return get('/agent/health')
}

/**
 * 强制同步 Agent 状态
 * @returns {Promise<import('./types').StatusResponse>} 返回同步状态
 */
export async function syncAgentState() {
  return post('/agent/sync')
}

/**
 * 获取 Agent 版本信息
 * @returns {Promise<import('./types').AgentVersion>} 返回版本信息
 */
export async function getAgentVersion() {
  return get('/agent/version')
}

/**
 * 更新 Agent
 * @returns {Promise<import('./types').StatusResponse>} 返回更新状态
 */
export async function updateAgent() {
  return post('/agent/update')
}

/**
 * 获取 Agent 诊断信息
 * @returns {Promise<import('./types').AgentDiagnostics>} 返回诊断信息
 */
export async function getAgentDiagnostics() {
  return get('/agent/diagnostics')
}

/**
 * 导出 Agent 数据
 * @param {import('./types').ExportAgentDataRequest} [params={}] - 导出参数
 * @returns {Promise<Object>} 返回导出的数据
 */
export async function exportAgentData(params = {}) {
  return get('/agent/export', params)
}

export default {
  getAgentStatus,
  startAgent,
  stopAgent,
  restartAgent,
  getAgentLogs,
  clearAgentLogs,
  getCurrentTask,
  getTaskHistory,
  cancelCurrentTask,
  getPendingActions,
  respondToPendingAction,
  sendTradingSignal,
  getSignalHistory,
  getAgentStats,
  getAgentConfig,
  updateAgentConfig,
  getAgentHealth,
  syncAgentState,
  getAgentVersion,
  updateAgent,
  getAgentDiagnostics,
  exportAgentData
}