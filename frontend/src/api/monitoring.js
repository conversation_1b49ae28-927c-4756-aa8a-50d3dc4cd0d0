/**
 * 监控API客户端
 * 提供监控数据的API调用接口
 */

import { get } from './client'

export const monitoringAPI = {
  /**
   * 获取KPI指标数据
   * @param {number} hours - 统计时间范围（小时）
   * @returns {Promise<Object>} KPI指标数据
   */
  async getKPIMetrics(hours = 24) {
    try {
      const response = await get(`/api/v1/monitoring/kpi`, { hours })
      return response
    } catch (error) {
      console.error('Failed to fetch KPI metrics:', error)
      throw error
    }
  },

  /**
   * 获取信号处理流程状态
   * @returns {Promise<Object>} 流程状态数据
   */
  async getFlowStatus() {
    try {
      const response = await get('/api/v1/monitoring/flow-status')
      return response
    } catch (error) {
      console.error('Failed to fetch flow status:', error)
      throw error
    }
  },

  /**
   * 获取最近的信号处理状态
   * @param {number} limit - 返回信号数量限制
   * @returns {Promise<Array>} 最近信号列表
   */
  async getRecentSignals(limit = 10) {
    try {
      const response = await get(`/api/v1/monitoring/recent-signals`, { limit })
      return response
    } catch (error) {
      console.error('Failed to fetch recent signals:', error)
      throw error
    }
  },

  /**
   * 获取活跃告警
   * @returns {Promise<Array>} 告警列表
   */
  async getActiveAlerts() {
    try {
      const response = await get('/api/v1/monitoring/alerts')
      return response
    } catch (error) {
      console.error('Failed to fetch active alerts:', error)
      throw error
    }
  },

  /**
   * 获取节点详细信息
   * @param {string} nodeId - 节点ID
   * @returns {Promise<Object>} 节点详情
   */
  async getNodeDetails(nodeId) {
    try {
      const response = await get(`/api/v1/monitoring/nodes/${nodeId}`)
      return response
    } catch (error) {
      console.error('Failed to fetch node details:', error)
      throw error
    }
  },

  /**
   * 获取监控仪表板数据
   * @returns {Promise<Object>} 仪表板数据
   */
  async getDashboardData() {
    try {
      const response = await get('/api/v1/monitoring/dashboard')
      return response
    } catch (error) {
      console.error('Failed to fetch dashboard data:', error)
      throw error
    }
  },

  /**
   * 获取信号处理状态
   * @returns {Promise<Object>} 信号状态统计
   */
  async getSignalsStatus() {
    try {
      const response = await get('/api/v1/monitoring/signals/status')
      return response
    } catch (error) {
      console.error('Failed to fetch signals status:', error)
      throw error
    }
  },

  /**
   * 获取性能指标
   * @param {number} hours - 统计时间范围（小时）
   * @returns {Promise<Object>} 性能指标数据
   */
  async getPerformanceMetrics(hours = 24) {
    try {
      const response = await get(`/api/v1/monitoring/metrics/performance`, { hours })
      return response
    } catch (error) {
      console.error('Failed to fetch performance metrics:', error)
      throw error
    }
  },

  /**
   * 获取信号时间线
   * @param {string} signalId - 信号ID
   * @returns {Promise<Object>} 信号时间线数据
   */
  async getSignalTimeline(signalId) {
    try {
      const response = await get(`/api/v1/monitoring/signals/${signalId}/timeline`)
      return response
    } catch (error) {
      console.error('Failed to fetch signal timeline:', error)
      throw error
    }
  }
}

/**
 * 监控数据缓存管理
 */
export class MonitoringCache {
  constructor() {
    this.cache = new Map()
    this.CACHE_TTL = 30000 // 30秒缓存
  }

  /**
   * 获取缓存数据
   * @param {string} key - 缓存键
   * @returns {any|null} 缓存的数据或null
   */
  get(key) {
    const cached = this.cache.get(key)
    if (cached && Date.now() - cached.timestamp < this.CACHE_TTL) {
      return cached.data
    }
    return null
  }

  /**
   * 设置缓存数据
   * @param {string} key - 缓存键
   * @param {any} data - 要缓存的数据
   */
  set(key, data) {
    this.cache.set(key, {
      data,
      timestamp: Date.now()
    })
  }

  /**
   * 清除所有缓存
   */
  clear() {
    this.cache.clear()
  }

  /**
   * 清除过期缓存
   */
  clearExpired() {
    const now = Date.now()
    for (const [key, value] of this.cache.entries()) {
      if (now - value.timestamp >= this.CACHE_TTL) {
        this.cache.delete(key)
      }
    }
  }
}

// 创建全局缓存实例
export const monitoringCache = new MonitoringCache()

/**
 * 带缓存的API调用包装器
 * @param {string} cacheKey - 缓存键
 * @param {Function} apiCall - API调用函数
 * @returns {Promise<any>} API响应数据
 */
export async function cachedApiCall(cacheKey, apiCall) {
  // 尝试从缓存获取
  const cached = monitoringCache.get(cacheKey)
  if (cached) {
    return cached
  }

  // 调用API并缓存结果
  try {
    const data = await apiCall()
    monitoringCache.set(cacheKey, data)
    return data
  } catch (error) {
    console.error(`Cached API call failed for key ${cacheKey}:`, error)
    throw error
  }
}

/**
 * 监控数据格式化工具
 */
export const monitoringUtils = {
  /**
   * 格式化处理时间
   * @param {number} seconds - 秒数
   * @returns {string} 格式化的时间字符串
   */
  formatDuration(seconds) {
    if (seconds < 60) {
      return `${seconds.toFixed(1)}秒`
    } else if (seconds < 3600) {
      return `${(seconds / 60).toFixed(1)}分钟`
    } else {
      return `${(seconds / 3600).toFixed(1)}小时`
    }
  },

  /**
   * 格式化百分比
   * @param {number} value - 数值
   * @param {number} decimals - 小数位数
   * @returns {string} 格式化的百分比字符串
   */
  formatPercentage(value, decimals = 1) {
    return `${value.toFixed(decimals)}%`
  },

  /**
   * 格式化数字
   * @param {number} value - 数值
   * @returns {string} 格式化的数字字符串
   */
  formatNumber(value) {
    if (value >= 1000000) {
      return `${(value / 1000000).toFixed(1)}M`
    } else if (value >= 1000) {
      return `${(value / 1000).toFixed(1)}K`
    } else {
      return value.toString()
    }
  },

  /**
   * 获取状态颜色
   * @param {string} status - 状态
   * @returns {string} 颜色名称
   */
  getStatusColor(status) {
    const colorMap = {
      'success': 'success',
      'completed': 'success',
      'failed': 'error',
      'error': 'error',
      'processing': 'warning',
      'pending': 'info',
      'unknown': 'grey'
    }
    return colorMap[status] || 'grey'
  },

  /**
   * 获取状态图标
   * @param {string} status - 状态
   * @returns {string} 图标名称
   */
  getStatusIcon(status) {
    const iconMap = {
      'success': 'mdi-check-circle',
      'completed': 'mdi-check-circle',
      'failed': 'mdi-alert-circle',
      'error': 'mdi-alert-circle',
      'processing': 'mdi-cog',
      'pending': 'mdi-clock-outline',
      'unknown': 'mdi-help-circle'
    }
    return iconMap[status] || 'mdi-help-circle'
  }
}
