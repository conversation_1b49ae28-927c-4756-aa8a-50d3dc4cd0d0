/**
 * API 客户端 - 统一的 HTTP 请求接口
 * 提供认证、错误处理、请求拦截等功能
 * @module api/client
 */

import { useAuthStore } from '@/stores/auth'
import { useUIStore } from '@/stores/ui'
import { z } from 'zod'

// 类型定义
interface RequestConfig extends RequestInit {
  headers?: Record<string, string>
  signal?: AbortSignal
  body?: string | FormData
}

interface RequestOptions {
  headers?: Record<string, string>
  timeout?: number
  [key: string]: any
}

interface BatchRequest {
  method: 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE'
  endpoint: string
  data?: any
  options?: RequestOptions
}

export interface APIResponse<T = any> {
  success: boolean
  data: T
  message?: string
  timestamp?: string
  request_id?: string
}

export interface APIErrorData {
  error?: {
    message?: string
    code?: string
    details?: any
  }
  message?: string
  detail?: string
  [key: string]: any
}

// API 基础配置
/**
 * 获取API基础URL
 * @returns {string} API基础URL
 */
const getApiBaseUrl = () => {
  // 检查localStorage中的覆盖设置（用于测试环境）
  if (typeof window !== 'undefined') {
    const override = localStorage.getItem('api_base_url_override')
    if (override) {
      console.log('🔧 Using API URL override from localStorage:', override)
      return override
    }
  }

  // 在开发环境中使用空字符串，让Vite代理处理API请求
  if (import.meta.env.DEV) {
    console.log('🔧 Development environment: using Vite proxy (empty base URL)')
    return ''
  }

  // 生产环境使用环境变量或相对路径
  if (import.meta.env.VITE_API_BASE_URL) {
    return import.meta.env.VITE_API_BASE_URL
  }

  return ''
}

// 动态获取API基础URL，支持运行时覆盖
const getApiBaseUrlDynamic = () => {
  // 检查localStorage中的覆盖设置（用于测试环境）
  if (typeof window !== 'undefined') {
    const override = localStorage.getItem('api_base_url_override')
    if (override) {
      console.log('🔧 Using API URL override from localStorage:', override)
      return override
    }
  }
  return getApiBaseUrl()
}
const API_TIMEOUT = 30000 // 30秒超时

/**
 * HTTP 状态码映射
 * @enum {number}
 */
const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  CONFLICT: 409,
  UNPROCESSABLE_ENTITY: 422,
  INTERNAL_SERVER_ERROR: 500,
  BAD_GATEWAY: 502,
  SERVICE_UNAVAILABLE: 503
}

/**
 * API 错误类
 * @class
 * @extends Error
 */
export class APIError extends Error {
  public readonly status: number
  public readonly data: APIErrorData | null

  /**
   * 创建一个API错误实例
   * @param message - 错误消息
   * @param status - HTTP状态码
   * @param data - 错误相关的数据
   */
  constructor(message: string, status: number, data: APIErrorData | null = null) {
    super(message)
    this.name = 'APIError'
    this.status = status
    this.data = data
  }

  /**
   * 检查是否为网络错误
   */
  isNetworkError(): boolean {
    return this.status === 0
  }

  /**
   * 检查是否为客户端错误 (4xx)
   */
  isClientError(): boolean {
    return this.status >= 400 && this.status < 500
  }

  /**
   * 检查是否为服务器错误 (5xx)
   */
  isServerError(): boolean {
    return this.status >= 500 && this.status < 600
  }
}

/**
 * 创建请求配置
 * @param options - 请求选项
 * @returns 处理后的请求配置
 */
function createRequestConfig(options: RequestOptions = {}): RequestConfig {
  const authStore = useAuthStore()

  const config: RequestConfig = {
    method: 'GET',
    headers: {
      ...(options.headers || {})
    },
    ...options
  }

  // 只有在没有明确设置Content-Type且不是FormData时才设置默认Content-Type
  if (config.headers && !config.headers['Content-Type'] && !(options.body instanceof FormData)) {
    config.headers['Content-Type'] = 'application/json'
  }

  // 添加认证头
  if (authStore.token) {
    config.headers!.Authorization = `Bearer ${authStore.token}`
  }

  return config
}

/**
 * 处理响应
 * @param response - fetch API的响应对象
 * @returns 处理后的响应数据
 * @throws APIError 当响应状态码不是2xx时抛出
 */
async function handleResponse<T = any>(response: Response): Promise<T> {
  const contentType = response.headers.get('content-type')
  const isJson = contentType && contentType.includes('application/json')
  
  let data: APIErrorData | string | null = null
  if (isJson) {
    try {
      data = await response.json() as APIErrorData
    } catch (error) {
      console.warn('Failed to parse JSON response:', error)
    }
  } else {
    data = await response.text()
  }

  if (!response.ok) {
    // 优先使用后端返回的中文错误消息
    let message = `HTTP ${response.status}: ${response.statusText}`

    if (typeof data === 'object' && data !== null) {
      const errorData = data as APIErrorData
      if (errorData.error?.message) {
        message = errorData.error.message
      } else if (errorData.message) {
        message = errorData.message
      } else if (errorData.detail) {
        message = errorData.detail
      }
    }

    // 默认错误消息
    if (message === `HTTP ${response.status}: ${response.statusText}`) {
      if (response.status === 401) {
        message = '用户名或密码错误'
      } else if (response.status === 403) {
        message = '访问被拒绝'
      } else if (response.status === 404) {
        message = '请求的资源不存在'
      } else if (response.status === 500) {
        message = '服务器内部错误'
      }
    }

    const errorData = typeof data === 'object' ? data as APIErrorData : null
    throw new APIError(message, response.status, errorData)
  }

  // 如果是标准API响应格式，提取data字段
  if (typeof data === 'object' && data !== null && 'success' in data && 'data' in data) {
    const apiResponse = data as { success: boolean; data: any; message?: string; error_code?: string | null }
    if (apiResponse.success) {
      return apiResponse.data as T
    }
  }

  return data as T
}

/**
 * 处理网络错误
 * @param error - 错误对象
 * @param url - 请求URL
 * @throws APIError 处理后的API错误
 */
function handleNetworkError(error: Error, _url: string): never {
  const uiStore = useUIStore()
  
  if (error.name === 'AbortError') {
    const message = '请求已取消'
    uiStore.showWarning(message)
    throw new APIError(message, 0)
  }
  
  if (error.name === 'TypeError' && error.message.includes('fetch')) {
    const message = '网络连接失败，请检查网络设置'
    uiStore.showError(message, '网络错误')
    throw new APIError(message, 0)
  }
  
  throw error
}

/**
 * 基础请求方法
 * @param endpoint - API端点路径
 * @param options - 请求选项
 * @returns 响应数据
 */
async function request(endpoint: string, options: RequestOptions = {}): Promise<any> {
  const url = `${getApiBaseUrlDynamic()}${endpoint}`
  const config = createRequestConfig(options)
  
  // 创建 AbortController 用于超时控制
  const controller = new AbortController()
  const timeoutId = setTimeout(() => controller.abort(), API_TIMEOUT)
  
  config.signal = controller.signal

  try {
    const response = await fetch(url, config)
    clearTimeout(timeoutId)
    return await handleResponse(response)
  } catch (error) {
    clearTimeout(timeoutId)
    return handleNetworkError(error as Error, url)
  }
}

/**
 * GET 请求
 * @param endpoint - API端点路径
 * @param params - 查询参数
 * @param options - 请求选项
 * @returns 响应数据
 */
export async function get<T = any>(endpoint: string, params: Record<string, any> = {}, options: RequestOptions = {}): Promise<T> {
  const searchParams = new URLSearchParams()
  
  Object.entries(params).forEach(([key, value]) => {
    if (value !== null && value !== undefined && value !== '') {
      searchParams.append(key, String(value))
    }
  })
  
  const queryString = searchParams.toString()
  const url = queryString ? `${endpoint}?${queryString}` : endpoint
  
  return request(url, { method: 'GET', ...options })
}

/**
 * POST 请求
 * @param endpoint - API端点路径
 * @param data - 请求数据
 * @param options - 请求选项
 * @returns 响应数据
 */
export async function post<T = any>(endpoint: string, data: any = null, options: RequestOptions = {}): Promise<T> {
  const config: RequestOptions = { method: 'POST', ...options }

  if (data) {
    if (data instanceof FormData) {
      // FormData 不需要设置 Content-Type，让浏览器自动设置
      config.body = data
    } else {
      // 对于非FormData，序列化为JSON
      config.body = JSON.stringify(data)
    }
  }

  return request(endpoint, config)
}

/**
 * PUT 请求
 * @param endpoint - API端点路径
 * @param data - 请求数据
 * @param options - 请求选项
 * @returns 响应数据
 */
export async function put<T = any>(endpoint: string, data: any = null, options: RequestOptions = {}): Promise<T> {
  const config: RequestOptions = { method: 'PUT', ...options }

  if (data) {
    config.body = JSON.stringify(data)
  }

  return request(endpoint, config)
}

/**
 * PATCH 请求
 * @param endpoint - API端点路径
 * @param data - 请求数据
 * @param options - 请求选项
 * @returns 响应数据
 */
export async function patch<T = any>(endpoint: string, data: any = null, options: RequestOptions = {}): Promise<T> {
  const config: RequestOptions = { method: 'PATCH', ...options }

  if (data) {
    config.body = JSON.stringify(data)
  }

  return request(endpoint, config)
}

/**
 * DELETE 请求
 * @param endpoint - API端点路径
 * @param options - 请求选项
 * @returns 响应数据
 */
export async function del<T = any>(endpoint: string, options: RequestOptions = {}): Promise<T> {
  return request(endpoint, { method: 'DELETE', ...options })
}

/**
 * 文件上传
 * @param endpoint - API端点路径
 * @param file - 要上传的文件
 * @param additionalData - 附加数据
 * @param onProgress - 进度回调函数
 * @returns 响应数据
 */
export async function upload<T = any>(
  endpoint: string,
  file: File,
  additionalData: Record<string, any> = {},
  onProgress: ((progress: number) => void) | null = null
): Promise<T> {
  const formData = new FormData()
  formData.append('file', file)
  
  Object.entries(additionalData).forEach(([key, value]) => {
    formData.append(key, String(value))
  })
  
  const config = { method: 'POST' }
  
  // 如果支持进度回调
  if (onProgress && typeof onProgress === 'function') {
    // 注意：fetch API 不直接支持上传进度，这里提供接口供将来扩展
    console.warn('Upload progress callback is not supported with fetch API')
  }
  
  return request(endpoint, {
    ...config,
    body: formData,
    headers: {} // 让浏览器自动设置 Content-Type
  })
}

/**
 * 批量请求
 * @param requests - 请求对象数组，每个对象包含 method, endpoint, data, options 字段
 * @returns 响应数据数组
 */
export async function batch<T = any>(requests: BatchRequest[]): Promise<T[]> {
  try {
    const promises = requests.map(({ method, endpoint, data, options }: BatchRequest) => {
      switch (method.toLowerCase()) {
        case 'get':
          return get(endpoint, data, options)
        case 'post':
          return post(endpoint, data, options)
        case 'put':
          return put(endpoint, data, options)
        case 'patch':
          return patch(endpoint, data, options)
        case 'delete':
          return del(endpoint, options)
        default:
          throw new Error(`Unsupported method: ${method}`)
      }
    })
    
    return Promise.all(promises)
  } catch (error) {
    console.error('Batch request failed:', error)
    throw error
  }
}

/**
 * 健康检查
 * @returns 健康状态
 */
export async function healthCheck(): Promise<{ status: string }> {
  return get<{ status: string }>('/health')
}

/**
 * 带运行时验证的GET请求
 * @param endpoint - API端点路径
 * @param schema - zod验证模式
 * @param params - 查询参数
 * @param options - 请求选项
 * @returns 验证后的响应数据
 */
export async function getWithValidation<T>(
  endpoint: string,
  schema: z.ZodSchema<T>,
  params: Record<string, any> = {},
  options: RequestOptions = {}
): Promise<T> {
  const response = await get(endpoint, params, options)
  try {
    return schema.parse(response)
  } catch (error) {
    console.error('API响应验证失败:', error)
    throw new APIError('API响应格式不正确', 422, { validation_error: error })
  }
}

/**
 * 带运行时验证的POST请求
 * @param endpoint - API端点路径
 * @param schema - zod验证模式
 * @param data - 请求数据
 * @param options - 请求选项
 * @returns 验证后的响应数据
 */
export async function postWithValidation<T>(
  endpoint: string,
  schema: z.ZodSchema<T>,
  data: any = null,
  options: RequestOptions = {}
): Promise<T> {
  const response = await post(endpoint, data, options)
  try {
    return schema.parse(response)
  } catch (error) {
    console.error('API响应验证失败:', error)
    throw new APIError('API响应格式不正确', 422, { validation_error: error })
  }
}

// 导出
export {
  HTTP_STATUS
}

// 默认导出
export default {
  get,
  post,
  put,
  patch,
  delete: del,
  upload,
  batch,
  healthCheck,
  HTTP_STATUS,
  APIError
}