/**
 * Discord配置相关API
 * 封装Discord配置管理的所有API调用
 */
import { get, post, put, patch, del } from './client'
import type { DiscordConfig, DiscordConfigRequest } from '@/stores/discordConfig'

/**
 * 获取Discord配置列表
 * @returns {Promise<DiscordConfig[]>} Discord配置列表
 */
export async function getDiscordConfigs(): Promise<DiscordConfig[]> {
  return get('/api/v1/discord-configs')
}

/**
 * 获取单个Discord配置
 * @param {string} id - 配置ID
 * @returns {Promise<DiscordConfig>} Discord配置详情
 */
export async function getDiscordConfig(id: string): Promise<DiscordConfig> {
  return get(`/api/v1/discord-configs/${id}`)
}

/**
 * 创建Discord配置
 * @param {DiscordConfigRequest} configData - 配置数据
 * @returns {Promise<DiscordConfig>} 创建的配置
 */
export async function createDiscordConfig(configData: DiscordConfigRequest): Promise<DiscordConfig> {
  return post('/api/v1/discord-configs', configData)
}

/**
 * 更新Discord配置
 * @param {string} id - 配置ID
 * @param {DiscordConfigRequest} configData - 更新的配置数据
 * @returns {Promise<DiscordConfig>} 更新后的配置
 */
export async function updateDiscordConfig(id: string, configData: DiscordConfigRequest): Promise<DiscordConfig> {
  return put(`/api/v1/discord-configs/${id}`, configData)
}

/**
 * 切换Discord配置的启用/禁用状态
 * @param {string} id - 配置ID
 * @returns {Promise<DiscordConfig>} 更新后的配置
 */
export async function toggleDiscordConfig(id: string): Promise<DiscordConfig> {
  return patch(`/api/v1/discord-configs/${id}/toggle`)
}

/**
 * 删除Discord配置
 * @param {string} id - 配置ID
 * @returns {Promise<{message: string}>} 删除结果
 */
export async function deleteDiscordConfig(id: string): Promise<{message: string}> {
  return del(`/api/v1/discord-configs/${id}`)
}

export default {
  getDiscordConfigs,
  getDiscordConfig,
  createDiscordConfig,
  updateDiscordConfig,
  toggleDiscordConfig,
  deleteDiscordConfig
}
