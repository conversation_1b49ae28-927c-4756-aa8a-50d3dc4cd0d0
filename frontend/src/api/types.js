/**
 * API 类型定义
 * 此文件包含所有API接口的请求和响应类型定义，用于提高代码的可维护性和类型安全性
 */

// =================================
// 通用类型
// =================================

/**
 * 分页响应基础类型
 * @typedef {Object} PaginationResponse
 * @property {number} total - 总记录数
 * @property {number} page - 当前页码
 * @property {number} limit - 每页记录数
 * @property {number} total_pages - 总页数
 */

/**
 * 通用状态响应
 * @typedef {Object} StatusResponse
 * @property {boolean} success - 操作是否成功
 * @property {string} message - 状态消息
 */

// =================================
// 认证相关类型
// =================================

/**
 * 登录请求参数
 * @typedef {Object} LoginRequest
 * @property {string} username - 用户名
 * @property {string} password - 密码
 */

/**
 * 令牌响应
 * @typedef {Object} TokenResponse
 * @property {string} access_token - 访问令牌
 * @property {string} token_type - 令牌类型，通常为 "Bearer"
 * @property {number} expires_in - 令牌有效期（秒）
 * @property {string} refresh_token - 刷新令牌
 * @property {Object} user - 用户信息
 */

/**
 * 刷新令牌请求
 * @typedef {Object} RefreshTokenRequest
 * @property {string} refresh_token - 刷新令牌
 */

/**
 * 用户注册请求
 * @typedef {Object} RegisterRequest
 * @property {string} username - 用户名
 * @property {string} email - 电子邮箱
 * @property {string} password - 密码
 */

/**
 * 用户信息
 * @typedef {Object} UserInfo
 * @property {number} id - 用户ID
 * @property {string} username - 用户名
 * @property {string} email - 电子邮箱
 * @property {boolean} is_first_time - 是否首次登录
 * @property {Array<string>} permissions - 权限列表
 * @property {string} created_at - 创建时间
 */

/**
 * 密码修改请求
 * @typedef {Object} ChangePasswordRequest
 * @property {string} old_password - 旧密码
 * @property {string} new_password - 新密码
 */

/**
 * 密码重置请求
 * @typedef {Object} ResetPasswordRequest
 * @property {string} email - 用户电子邮箱
 */

/**
 * 密码重置确认请求
 * @typedef {Object} ResetPasswordConfirmRequest
 * @property {string} token - 重置令牌
 * @property {string} new_password - 新密码
 */

// =================================
// 订单相关类型
// =================================

/**
 * 订单状态枚举
 * @typedef {'active'|'closed'|'failed'|'cancelled'} OrderStatus
 */

/**
 * 交易方向枚举
 * @typedef {'buy'|'sell'} TradeSide
 */

/**
 * 订单类型枚举
 * @typedef {'market'|'limit'} OrderType
 */

/**
 * 订单信息
 * @typedef {Object} Order
 * @property {string} id - 订单ID
 * @property {string} symbol - 交易对，如 "BTC/USDT"
 * @property {TradeSide} side - 交易方向
 * @property {OrderType} order_type - 订单类型
 * @property {number} quantity - 数量
 * @property {number} [price] - 价格（限价单）
 * @property {OrderStatus} status - 订单状态
 * @property {number} filled_quantity - 已成交数量
 * @property {number} [entry_price] - 开仓价格
 * @property {number} [pnl] - 盈亏
 * @property {number} [pnl_percent] - 盈亏百分比
 * @property {string} created_at - 创建时间
 * @property {string} [updated_at] - 更新时间
 */

/**
 * 订单列表响应
 * @typedef {Object} OrderListResponse
 * @property {Array<Order>} items - 订单列表
 * @property {PaginationResponse} pagination - 分页信息
 */

/**
 * 创建订单请求
 * @typedef {Object} CreateOrderRequest
 * @property {string} symbol - 交易对
 * @property {TradeSide} side - 交易方向
 * @property {OrderType} order_type - 订单类型
 * @property {number} quantity - 数量
 * @property {number} [price] - 价格（限价单）
 * @property {number} [stop_loss] - 止损价格
 * @property {number} [take_profit] - 止盈价格
 */

/**
 * 更新订单请求
 * @typedef {Object} UpdateOrderRequest
 * @property {number} [stop_loss] - 止损价格
 * @property {number} [take_profit] - 止盈价格
 */

/**
 * 批量取消订单请求
 * @typedef {Object} CancelMultipleOrdersRequest
 * @property {Array<string>} order_ids - 订单ID列表
 */

/**
 * 交易对信息
 * @typedef {Object} TradingPair
 * @property {string} symbol - 交易对
 * @property {string} base_asset - 基础资产
 * @property {string} quote_asset - 计价资产
 * @property {number} min_price - 最小价格
 * @property {number} max_price - 最大价格
 * @property {number} tick_size - 最小价格变动单位
 * @property {number} min_qty - 最小交易数量
 * @property {number} max_qty - 最大交易数量
 * @property {number} step_size - 最小数量变动单位
 */

/**
 * 市场数据
 * @typedef {Object} MarketData
 * @property {string} symbol - 交易对
 * @property {number} price - 当前价格
 * @property {number} price_change - 价格变化
 * @property {number} price_change_percent - 价格变化百分比
 * @property {number} high_24h - 24小时最高价
 * @property {number} low_24h - 24小时最低价
 * @property {number} volume_24h - 24小时成交量
 */

/**
 * K线数据
 * @typedef {Object} KlineData
 * @property {string} time - 时间
 * @property {number} open - 开盘价
 * @property {number} high - 最高价
 * @property {number} low - 最低价
 * @property {number} close - 收盘价
 * @property {number} volume - 成交量
 */

/**
 * 订单簿数据
 * @typedef {Object} OrderBookEntry
 * @property {number} price - 价格
 * @property {number} quantity - 数量
 */

/**
 * 订单簿
 * @typedef {Object} OrderBook
 * @property {Array<OrderBookEntry>} bids - 买单列表
 * @property {Array<OrderBookEntry>} asks - 卖单列表
 * @property {number} timestamp - 时间戳
 */

// =================================
// 条件订单相关类型
// =================================

/**
 * 条件订单状态枚举
 * @typedef {'PENDING'|'TRIGGERED'|'CANCELLED'|'EXPIRED'} ConditionalOrderStatus
 */

/**
 * 价格操作符枚举
 * @typedef {'gt'|'lt'|'gte'|'lte'} PriceOperator
 */

/**
 * 触发条件
 * @typedef {Object} TriggerCondition
 * @property {string} symbol - 交易对
 * @property {PriceOperator} operator - 价格操作符
 * @property {number} price - 触发价格
 */

/**
 * 行动计划
 * @typedef {Object} ActionPlan
 * @property {string} symbol - 交易对
 * @property {TradeSide} side - 交易方向
 * @property {OrderType} order_type - 订单类型
 * @property {number} quantity - 数量
 * @property {number} quantity_usd - 美元金额
 * @property {number} [price] - 价格（限价单）
 */

/**
 * 条件订单
 * @typedef {Object} ConditionalOrder
 * @property {string} id - 条件订单ID
 * @property {number} user_id - 用户ID
 * @property {ConditionalOrderStatus} status - 状态
 * @property {TriggerCondition} trigger_condition - 触发条件
 * @property {ActionPlan} action_plan - 行动计划
 * @property {string} created_at - 创建时间
 * @property {string} [expires_at] - 过期时间
 * @property {string} [triggered_at] - 触发时间
 * @property {string} [result_order_id] - 关联订单ID
 */

/**
 * 条件订单请求
 * @typedef {Object} ConditionalOrderRequest
 * @property {TriggerCondition} trigger_condition - 触发条件
 * @property {ActionPlan} action_plan - 行动计划
 * @property {number} expiry_hours - 有效期（小时）
 */

/**
 * 条件订单列表响应
 * @typedef {Object} ConditionalOrderListResponse
 * @property {Array<ConditionalOrder>} items - 条件订单列表
 * @property {PaginationResponse} pagination - 分页信息
 */

// =================================
// 配置相关类型
// =================================

/**
 * 交易所配置
 * @typedef {Object} ExchangeConfig
 * @property {string} [id] - 配置ID（更新时需要）
 * @property {string} exchange_name - 交易所名称
 * @property {string} api_key - API密钥
 * @property {string} api_secret - API密钥
 * @property {string} [passphrase] - 通行证（部分交易所需要）
 * @property {boolean} sandbox - 是否使用沙箱环境
 */

/**
 * 风控配置
 * @typedef {Object} RiskConfig
 * @property {number} max_concurrent_orders - 最大并发订单数
 * @property {number} max_total_position_value_usd - 最大总持仓价值（USD）
 * @property {number} default_position_size_usd - 默认下单金额（USD）
 * @property {number} max_position_size_usd - 单笔最大下单金额（USD）
 * @property {Array<string>} allowed_symbols - 允许交易的币种列表
 */

/**
 * 信号源配置
 * @typedef {Object} SignalConfig
 * @property {string} [id] - 配置ID（更新时需要）
 * @property {string} source_type - 信号源类型（Discord, Telegram等）
 * @property {string} name - 配置名称
 * @property {Object} credentials - 认证凭据
 * @property {Object} settings - 设置
 * @property {boolean} enabled - 是否启用
 */

/**
 * 系统配置
 * @typedef {Object} SystemConfig
 * @property {boolean} auto_trading_enabled - 是否启用自动交易
 * @property {number} max_concurrent_trades - 最大并发交易数
 * @property {number} order_timeout_seconds - 订单超时时间（秒）
 * @property {string} log_level - 日志级别
 */

// =================================
// 信号相关类型
// =================================

/**
 * 平台类型枚举
 * @typedef {'discord'|'telegram'|'manual'} PlatformType
 */

/**
 * 消息类型枚举
 * @typedef {'text'|'embed'|'attachment'|'reply'} MessageType
 */

/**
 * AI解析状态枚举
 * @typedef {'pending'|'success'|'failed'|'partial'} AIParseStatus
 */

/**
 * AI识别的消息类型枚举
 * @typedef {'normal_message'|'trading_signal'|'market_analysis'|'price_alert'|'ambiguous'} MessageTypeAI
 */

/**
 * LLM服务枚举
 * @typedef {'deepseek'|'gemini'|'chatgpt'|'claude'} LLMService
 */

/**
 * 信号信息
 * @typedef {Object} Signal
 * @property {string} id - 信号ID
 * @property {PlatformType} platform - 消息来源平台
 * @property {string} [channel_name] - 频道/群组名称
 * @property {string} [author_name] - 消息发送者名称
 * @property {string} content - 处理后的消息内容
 * @property {string} [raw_content] - 原始消息内容
 * @property {MessageType} message_type - 消息类型
 * @property {number} [confidence] - AI解析置信度 (0-1)
 * @property {AIParseStatus} ai_parse_status - AI解析状态
 * @property {MessageTypeAI} message_type_ai - AI识别的消息类型
 * @property {LLMService} [llm_service] - 使用的LLM服务
 * @property {boolean} is_processed - 是否已被AI处理
 * @property {string} created_at - 创建时间
 * @property {string} [processed_at] - 处理时间
 * @property {Object} [metadata] - 平台特定元数据
 * @property {string} [platform_message_id] - 平台原始消息ID
 * @property {string} [channel_id] - 频道/群组ID
 * @property {string} [author_id] - 消息发送者ID
 * @property {number} [signal_strength] - 已废弃，请使用confidence
 */

/**
 * AI分析结果
 * @typedef {Object} AIAnalysis
 * @property {Object} [order_data] - 订单数据
 * @property {string} [order_data.symbol] - 交易对
 * @property {TradeSide} [order_data.side] - 交易方向
 * @property {number} [order_data.quantity] - 数量
 * @property {number} [order_data.price] - 价格
 * @property {number} [order_data.stop_loss] - 止损价格
 * @property {number} [order_data.take_profit] - 止盈价格
 * @property {Object} [parsed_content] - 解析后的内容
 * @property {string} [parsed_content.original] - 原始内容
 * @property {Object} [parsed_content.structured] - 结构化数据
 * @property {Object} [analysis_metadata] - 分析元数据
 * @property {number} [analysis_metadata.processing_time_ms] - 处理时间（毫秒）
 * @property {string} [analysis_metadata.model_version] - 模型版本
 * @property {Object} [analysis_metadata.confidence_breakdown] - 置信度分解
 */

/**
 * 信号查询参数
 * @typedef {Object} SignalQueryParams
 * @property {PlatformType} [platform] - 平台筛选
 * @property {string} [channel_id] - 频道ID筛选
 * @property {boolean} [is_processed] - 处理状态筛选
 * @property {number} [confidence_min] - 最小置信度
 * @property {number} [confidence_max] - 最大置信度
 * @property {AIParseStatus} [ai_parse_status] - AI解析状态筛选
 * @property {MessageTypeAI} [message_type_ai] - AI消息类型筛选
 * @property {LLMService} [llm_service] - LLM服务筛选
 * @property {string} [date_from] - 开始日期
 * @property {string} [date_to] - 结束日期
 * @property {number} [page] - 页码
 * @property {number} [size] - 每页大小
 * @property {string} [sort_by] - 排序字段
 * @property {string} [sort_order] - 排序方向
 * @property {number} [signal_strength_min] - 已废弃，请使用confidence_min
 */

/**
 * 创建信号请求
 * @typedef {Object} CreateSignalRequest
 * @property {PlatformType} platform - 消息来源平台
 * @property {string} content - 消息内容
 * @property {string} [channel_name] - 频道/群组名称
 * @property {string} [author_name] - 消息发送者名称
 * @property {string} [raw_content] - 原始消息内容
 * @property {Object} [metadata] - 平台特定元数据
 */

/**
 * 更新信号请求
 * @typedef {Object} UpdateSignalRequest
 * @property {boolean} [is_processed] - 是否已处理
 * @property {number} [confidence] - AI解析置信度
 * @property {AIParseStatus} [ai_parse_status] - AI解析状态
 * @property {MessageTypeAI} [message_type_ai] - AI识别的消息类型
 * @property {LLMService} [llm_service] - LLM服务
 * @property {Object} [metadata] - 平台特定元数据
 * @property {AIAnalysis} [ai_analysis] - AI分析结果
 * @property {number} [signal_strength] - 已废弃，请使用confidence
 */

/**
 * 信号列表响应
 * @typedef {Object} SignalListResponse
 * @property {Array<Signal>} items - 信号列表
 * @property {PaginationResponse} pagination - 分页信息
 */

/**
 * 信号统计响应
 * @typedef {Object} SignalStatsResponse
 * @property {number} total_signals - 总信号数
 * @property {number} processed_signals - 已处理信号数
 * @property {Object<string, number>} platform_breakdown - 平台分布
 * @property {number} [avg_confidence] - 平均置信度
 * @property {Object<string, number>} ai_parse_status_breakdown - AI解析状态分布
 * @property {Object<string, number>} message_type_breakdown - 消息类型分布
 * @property {Object<string, number>} llm_service_breakdown - LLM服务分布
 * @property {Array<Object>} recent_activity - 最近活动
 * @property {number} [avg_signal_strength] - 已废弃，请使用avg_confidence
 */

// =================================
// Agent相关类型
// =================================

/**
 * Agent状态枚举
 * @typedef {'idle'|'processing'|'waiting_confirmation'|'completed'|'failed'} AgentState
 */

/**
 * 待处理动作类型枚举
 * @typedef {'TRADE_CONFIRMATION'|'CLARIFICATION_NEEDED'|'RISK_APPROVAL'|'PARAMETER_CONFIRMATION'} PendingActionType
 */

/**
 * 优先级枚举
 * @typedef {'high'|'medium'|'low'} Priority
 */

/**
 * Agent状态响应
 * @typedef {Object} AgentStatusResponse
 * @property {AgentState} state - 当前状态
 * @property {string} [current_task_id] - 当前任务ID
 * @property {string} [current_node] - 当前节点
 * @property {boolean} is_connected - 是否连接
 */

/**
 * Agent日志条目
 * @typedef {Object} AgentLogEntry
 * @property {string} id - 日志ID
 * @property {string} level - 日志级别
 * @property {string} message - 日志消息
 * @property {string} timestamp - 时间戳
 * @property {Object} [details] - 详细信息
 */

/**
 * Agent日志响应
 * @typedef {Object} AgentLogsResponse
 * @property {Array<AgentLogEntry>} items - 日志条目列表
 * @property {PaginationResponse} pagination - 分页信息
 */

/**
 * 任务信息
 * @typedef {Object} TaskInfo
 * @property {string} id - 任务ID
 * @property {string} raw_input - 原始输入
 * @property {AgentState} state - 当前状态
 * @property {string} created_at - 创建时间
 * @property {string} [completed_at] - 完成时间
 * @property {boolean} success - 是否成功
 */

/**
 * 任务历史响应
 * @typedef {Object} TaskHistoryResponse
 * @property {Array<TaskInfo>} items - 任务列表
 * @property {PaginationResponse} pagination - 分页信息
 */

/**
 * 待处理动作
 * @typedef {Object} PendingAction
 * @property {string} id - 动作ID
 * @property {string} task_id - 任务ID
 * @property {PendingActionType} type - 动作类型
 * @property {Priority} priority - 优先级
 * @property {string} status - 状态
 * @property {string} raw_text - 原始文本
 * @property {Object} parsed_intent - 解析后的意图
 * @property {string} [clarification_needed] - 需要澄清的问题
 * @property {Object} [risk_assessment] - 风险评估
 * @property {string} created_at - 创建时间
 * @property {string} [expires_at] - 过期时间
 */

/**
 * 待处理动作列表响应
 * @typedef {Object} PendingActionListResponse
 * @property {Array<PendingAction>} items - 待处理动作列表
 */

/**
 * 动作响应请求
 * @typedef {Object} ActionResponseRequest
 * @property {boolean} approved - 是否批准
 * @property {Object} [additional_data] - 附加数据
 */

/**
 * 交易信号请求
 * @typedef {Object} TradingSignalRequest
 * @property {string} text - 信号文本
 * @property {string} [source] - 信号来源
 */

/**
 * Agent配置
 * @typedef {Object} AgentConfig
 * @property {string} model_name - 模型名称
 * @property {number} temperature - 温度
 * @property {number} max_tokens - 最大令牌数
 * @property {Object} tools_config - 工具配置
 */

/**
 * Agent健康状态
 * @typedef {Object} AgentHealth
 * @property {boolean} healthy - 是否健康
 * @property {Object} components - 组件状态
 * @property {number} uptime - 运行时间（秒）
 * @property {Object} resource_usage - 资源使用情况
 */

/**
 * Agent版本信息
 * @typedef {Object} AgentVersion
 * @property {string} version - 版本号
 * @property {string} build_date - 构建日期
 * @property {string} commit_hash - 提交哈希
 */

/**
 * Agent诊断信息
 * @typedef {Object} AgentDiagnostics
 * @property {Object} system_info - 系统信息
 * @property {Object} memory_usage - 内存使用情况
 * @property {Object} db_stats - 数据库统计
 * @property {Array<Object>} recent_errors - 最近错误
 * @property {Object} performance_metrics - 性能指标
 */

/**
 * 导出Agent数据请求
 * @typedef {Object} ExportAgentDataRequest
 * @property {string} data_type - 数据类型
 * @property {string} [start_date] - 开始日期
 * @property {string} [end_date] - 结束日期
 * @property {boolean} [include_logs] - 是否包含日志
 */

// 导出所有类型
export {}; 