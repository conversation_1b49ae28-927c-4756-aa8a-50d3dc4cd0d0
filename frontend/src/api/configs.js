/**
 * 配置相关 API
 * 封装了交易所配置、风控配置、信号源配置等相关操作的API请求
 */

import { get, post, put, del } from './client.ts'

/**
 * 获取所有配置
 * @returns {Promise<Object>} 返回所有配置
 */
export async function getConfigs() {
  return get('/configs')
}

/**
 * 获取交易所配置
 * @returns {Promise<Array<import('./types').ExchangeConfig>>} 返回交易所配置列表
 */
export async function getExchangeConfigs() {
  return get('/configs/exchanges')
}

/**
 * 创建交易所配置
 * @param {import('./types').ExchangeConfig} configData - 交易所配置数据
 * @returns {Promise<import('./types').ExchangeConfig>} 返回创建的配置
 */
export async function createExchangeConfig(configData) {
  return post('/configs/exchanges', configData)
}

/**
 * 更新交易所配置
 * @param {string} configId - 配置ID
 * @param {import('./types').ExchangeConfig} configData - 交易所配置数据
 * @returns {Promise<import('./types').ExchangeConfig>} 返回更新后的配置
 */
export async function updateExchangeConfig(configId, configData) {
  return put(`/configs/exchanges/${configId}`, configData)
}

/**
 * 删除交易所配置
 * @param {string} configId - 配置ID
 * @returns {Promise<import('./types').StatusResponse>} 返回删除状态
 */
export async function deleteExchangeConfig(configId) {
  return del(`/configs/exchanges/${configId}`)
}

/**
 * 测试交易所连接
 * @param {import('./types').ExchangeConfig} configData - 交易所配置数据
 * @returns {Promise<{success: boolean, message: string}>} 返回测试结果
 */
export async function testExchangeConnection(configData) {
  return post('/configs/exchanges/test', configData)
}

/**
 * 获取风控配置
 * @returns {Promise<import('./types').RiskConfig>} 返回风控配置
 */
export async function getRiskConfig() {
  return get('/configs/risk')
}

/**
 * 更新风控配置
 * @param {import('./types').RiskConfig} configData - 风控配置数据
 * @returns {Promise<import('./types').RiskConfig>} 返回更新后的配置
 */
export async function updateRiskConfig(configData) {
  return put('/configs/risk', configData)
}

/**
 * 获取信号源配置
 * @returns {Promise<Array<import('./types').SignalConfig>>} 返回信号源配置列表
 */
export async function getSignalConfigs() {
  return get('/configs/signals')
}

/**
 * 创建信号源配置
 * @param {import('./types').SignalConfig} configData - 信号源配置数据
 * @returns {Promise<import('./types').SignalConfig>} 返回创建的配置
 */
export async function createSignalConfig(configData) {
  return post('/configs/signals', configData)
}

/**
 * 更新信号源配置
 * @param {string} configId - 配置ID
 * @param {import('./types').SignalConfig} configData - 信号源配置数据
 * @returns {Promise<import('./types').SignalConfig>} 返回更新后的配置
 */
export async function updateSignalConfig(configId, configData) {
  return put(`/configs/signals/${configId}`, configData)
}

/**
 * 删除信号源配置
 * @param {string} configId - 配置ID
 * @returns {Promise<import('./types').StatusResponse>} 返回删除状态
 */
export async function deleteSignalConfig(configId) {
  return del(`/configs/signals/${configId}`)
}

/**
 * 测试信号源连接
 * @param {import('./types').SignalConfig} configData - 信号源配置数据
 * @returns {Promise<{success: boolean, message: string}>} 返回测试结果
 */
export async function testSignalConnection(configData) {
  return post('/configs/signals/test', configData)
}

/**
 * 获取系统配置
 * @returns {Promise<import('./types').SystemConfig>} 返回系统配置
 */
export async function getSystemConfig() {
  return get('/configs/system')
}

/**
 * 更新系统配置
 * @param {import('./types').SystemConfig} configData - 系统配置数据
 * @returns {Promise<import('./types').SystemConfig>} 返回更新后的配置
 */
export async function updateSystemConfig(configData) {
  return put('/configs/system', configData)
}

/**
 * 获取支持的交易所列表
 * @returns {Promise<Array<string>>} 返回支持的交易所名称列表
 */
export async function getSupportedExchanges() {
  return get('/configs/exchanges/supported')
}

/**
 * 获取支持的信号源列表
 * @returns {Promise<Array<string>>} 返回支持的信号源类型列表
 */
export async function getSupportedSignalSources() {
  return get('/configs/signals/supported')
}

/**
 * 导出配置
 * @returns {Promise<Object>} 返回所有配置的导出数据
 */
export async function exportConfigs() {
  return get('/configs/export')
}

/**
 * 导入配置
 * @param {Object} configData - 要导入的配置数据
 * @returns {Promise<import('./types').StatusResponse>} 返回导入状态
 */
export async function importConfigs(configData) {
  return post('/configs/import', configData)
}

/**
 * 重置配置到默认值
 * @returns {Promise<import('./types').StatusResponse>} 返回重置状态
 */
export async function resetConfigs() {
  return post('/configs/reset')
}

/**
 * 获取配置模板
 * @returns {Promise<Array<Object>>} 返回配置模板列表
 */
export async function getConfigTemplates() {
  return get('/configs/templates')
}

export default {
  getConfigs,
  getExchangeConfigs,
  createExchangeConfig,
  updateExchangeConfig,
  deleteExchangeConfig,
  testExchangeConnection,
  getRiskConfig,
  updateRiskConfig,
  getSignalConfigs,
  createSignalConfig,
  updateSignalConfig,
  deleteSignalConfig,
  testSignalConnection,
  getSystemConfig,
  updateSystemConfig,
  getSupportedExchanges,
  getSupportedSignalSources,
  exportConfigs,
  importConfigs,
  resetConfigs,
  getConfigTemplates
}