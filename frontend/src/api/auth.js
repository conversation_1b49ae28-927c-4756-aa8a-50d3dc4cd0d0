/**
 * 认证相关 API
 * 封装了用户认证、注册、登录等相关操作的API请求
 */

import { get, post, put, del } from './client.ts'

/**
 * 用户登录
 * @param {import('./types').LoginRequest} credentials - 登录凭据
 * @returns {Promise<import('./types').TokenResponse>} 返回登录令牌和用户信息
 */
export async function login(credentials) {
  return post('/auth/login', credentials)
}

/**
 * 用户注册
 * @param {import('./types').RegisterRequest} userData - 用户注册数据
 * @returns {Promise<import('./types').StatusResponse>} 返回注册状态
 */
export async function register(userData) {
  return post('/auth/register', userData)
}

/**
 * 刷新令牌
 * @param {string} refreshToken - 刷新令牌
 * @returns {Promise<import('./types').TokenResponse>} 返回新的访问令牌
 */
export async function refreshToken(refreshToken) {
  return post('/auth/refresh', { refresh_token: refreshToken })
}

/**
 * 用户登出
 * @returns {Promise<import('./types').StatusResponse>} 返回登出状态
 */
export async function logout() {
  return post('/auth/logout')
}

/**
 * 获取当前用户信息
 * @returns {Promise<import('./types').UserInfo>} 返回当前用户信息
 */
export async function getCurrentUser() {
  return get('/auth/me')
}

/**
 * 更新用户信息
 * @param {Partial<import('./types').UserInfo>} userData - 需要更新的用户信息
 * @returns {Promise<import('./types').UserInfo>} 返回更新后的用户信息
 */
export async function updateUser(userData) {
  return put('/auth/me', userData)
}

/**
 * 修改密码
 * @param {import('./types').ChangePasswordRequest} passwordData - 密码修改数据
 * @returns {Promise<import('./types').StatusResponse>} 返回密码修改状态
 */
export async function changePassword(passwordData) {
  return put('/auth/change-password', passwordData)
}

/**
 * 重置密码请求
 * @param {string} email - 用户邮箱
 * @returns {Promise<import('./types').StatusResponse>} 返回重置密码请求状态
 */
export async function requestPasswordReset(email) {
  return post('/auth/reset-password', { email })
}

/**
 * 确认密码重置
 * @param {string} token - 重置令牌
 * @param {string} newPassword - 新密码
 * @returns {Promise<import('./types').StatusResponse>} 返回密码重置确认状态
 */
export async function confirmPasswordReset(token, newPassword) {
  return post('/auth/reset-password/confirm', {
    token,
    new_password: newPassword
  })
}

/**
 * 验证令牌
 * @param {string} token - 需要验证的令牌
 * @returns {Promise<{valid: boolean}>} 返回令牌是否有效
 */
export async function verifyToken(token) {
  return post('/auth/verify-token', { token })
}

/**
 * 获取用户权限
 * @returns {Promise<{permissions: string[]}>} 返回用户权限列表
 */
export async function getUserPermissions() {
  return get('/auth/permissions')
}

export default {
  login,
  register,
  refreshToken,
  logout,
  getCurrentUser,
  updateUser,
  changePassword,
  requestPasswordReset,
  confirmPasswordReset,
  verifyToken,
  getUserPermissions
}