/**
 * 订单相关 API
 * 封装了订单管理、市场数据查询等相关操作的API请求
 */

import { get, post, put, del } from './client.ts'

/**
 * 获取订单列表
 * @param {Object} [params={}] - 查询参数，如分页、过滤条件等
 * @returns {Promise<import('./types').OrderListResponse>} 返回订单列表和分页信息
 */
export async function getOrders(params = {}) {
  return get('/orders', params)
}

/**
 * 获取订单详情
 * @param {string} orderId - 订单ID
 * @param {number} userId - 用户ID
 * @returns {Promise<import('./types').Order>} 返回订单详情
 */
export async function getOrder(orderId, userId) {
  return get(`/orders/${orderId}`, { user_id: userId })
}

/**
 * 创建订单
 * @param {import('./types').CreateOrderRequest} orderData - 订单数据
 * @returns {Promise<import('./types').Order>} 返回创建的订单
 */
export async function createOrder(orderData) {
  return post('/orders', orderData)
}

/**
 * 更新订单
 * @param {string} orderId - 订单ID
 * @param {import('./types').UpdateOrderRequest} orderData - 订单更新数据
 * @returns {Promise<import('./types').Order>} 返回更新后的订单
 */
export async function updateOrder(orderId, orderData) {
  return put(`/orders/${orderId}`, orderData)
}

/**
 * 取消订单
 * @param {string} orderId - 订单ID
 * @returns {Promise<import('./types').StatusResponse>} 返回取消状态
 */
export async function cancelOrder(orderId) {
  return post(`/orders/${orderId}/cancel`)
}

/**
 * 批量取消订单
 * @param {Array<string>} orderIds - 订单ID列表
 * @returns {Promise<import('./types').StatusResponse>} 返回批量取消状态
 */
export async function cancelMultipleOrders(orderIds) {
  return post('/orders/cancel-multiple', { order_ids: orderIds })
}

/**
 * 获取订单历史
 * @param {Object} [params={}] - 查询参数，如分页、时间范围等
 * @returns {Promise<import('./types').OrderListResponse>} 返回历史订单列表
 */
export async function getOrderHistory(params = {}) {
  return get('/orders/history', params)
}

/**
 * 获取订单统计
 * @param {Object} [params={}] - 查询参数，如时间范围等
 * @returns {Promise<Object>} 返回订单统计数据
 */
export async function getOrderStats(params = {}) {
  return get('/orders/stats', params)
}

/**
 * 获取持仓信息
 * @param {Object} [params={}] - 查询参数，如交易对筛选等
 * @returns {Promise<Array<Object>>} 返回持仓列表
 */
export async function getPositions(params = {}) {
  return get('/positions', params)
}

/**
 * 平仓
 * @param {string} positionId - 持仓ID
 * @param {Object} [data={}] - 平仓参数，如数量、价格等
 * @returns {Promise<import('./types').Order>} 返回平仓订单
 */
export async function closePosition(positionId, data = {}) {
  return post(`/positions/${positionId}/close`, data)
}

/**
 * 全部平仓
 * @param {Object} [data={}] - 平仓参数，如筛选条件等
 * @returns {Promise<Array<import('./types').Order>>} 返回平仓订单列表
 */
export async function closeAllPositions(data = {}) {
  return post('/positions/close-all', data)
}

/**
 * 获取交易对信息
 * @returns {Promise<Array<import('./types').TradingPair>>} 返回交易对列表
 */
export async function getTradingPairs() {
  return get('/trading-pairs')
}

/**
 * 获取市场数据
 * @param {string} symbol - 交易对，如 "BTC/USDT"
 * @returns {Promise<import('./types').MarketData>} 返回市场数据
 */
export async function getMarketData(symbol) {
  return get(`/market/${symbol}`)
}

/**
 * 获取K线数据
 * @param {string} symbol - 交易对，如 "BTC/USDT"
 * @param {string} interval - 时间间隔，如 "1m", "1h", "1d"
 * @param {Object} [params={}] - 查询参数，如开始时间、结束时间等
 * @returns {Promise<Array<import('./types').KlineData>>} 返回K线数据
 */
export async function getKlineData(symbol, interval, params = {}) {
  return get(`/market/${symbol}/klines`, {
    interval,
    ...params
  })
}

/**
 * 获取订单簿
 * @param {string} symbol - 交易对，如 "BTC/USDT"
 * @param {number} [depth=20] - 深度
 * @returns {Promise<import('./types').OrderBook>} 返回订单簿
 */
export async function getOrderBook(symbol, depth = 20) {
  return get(`/market/${symbol}/orderbook`, { depth })
}

/**
 * 获取最近成交
 * @param {string} symbol - 交易对，如 "BTC/USDT"
 * @param {number} [limit=50] - 获取数量
 * @returns {Promise<Array<Object>>} 返回最近成交列表
 */
export async function getRecentTrades(symbol, limit = 50) {
  return get(`/market/${symbol}/trades`, { limit })
}

/**
 * 获取24小时统计
 * @param {string} symbol - 交易对，如 "BTC/USDT"
 * @returns {Promise<Object>} 返回24小时统计数据
 */
export async function get24hrStats(symbol) {
  return get(`/market/${symbol}/24hr`)
}

export default {
  getOrders,
  getOrder,
  createOrder,
  updateOrder,
  cancelOrder,
  cancelMultipleOrders,
  getOrderHistory,
  getOrderStats,
  getPositions,
  closePosition,
  closeAllPositions,
  getTradingPairs,
  getMarketData,
  getKlineData,
  getOrderBook,
  getRecentTrades,
  get24hrStats
}