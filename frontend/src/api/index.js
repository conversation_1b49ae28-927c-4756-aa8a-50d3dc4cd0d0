/**
 * API 模块统一导出
 * 此模块集中导出所有API相关功能，便于在应用中统一导入和使用
 * @module api
 */

// 导入所有 API 模块
import * as authAPI from './auth.js'
import * as ordersAPI from './orders.js'
import * as configsAPI from './configs.js'
import * as agentAPI from './agent.js'
import client, { APIError, HTTP_STATUS } from './client.ts'

/**
 * 认证相关API
 * @typedef {Object} AuthAPI
 * @property {Function} login - 用户登录
 * @property {Function} register - 用户注册
 * @property {Function} refreshToken - 刷新令牌
 * @property {Function} logout - 用户登出
 * @property {Function} getCurrentUser - 获取当前用户信息
 * @property {Function} updateUser - 更新用户信息
 * @property {Function} changePassword - 修改密码
 * @property {Function} requestPasswordReset - 重置密码请求
 * @property {Function} confirmPasswordReset - 确认密码重置
 * @property {Function} verifyToken - 验证令牌
 * @property {Function} getUserPermissions - 获取用户权限
 */

/**
 * 订单相关API
 * @typedef {Object} OrdersAPI
 * @property {Function} getOrders - 获取订单列表
 * @property {Function} getOrder - 获取订单详情
 * @property {Function} createOrder - 创建订单
 * @property {Function} updateOrder - 更新订单
 * @property {Function} cancelOrder - 取消订单
 * @property {Function} cancelMultipleOrders - 批量取消订单
 * @property {Function} getOrderHistory - 获取订单历史
 * @property {Function} getOrderStats - 获取订单统计
 * @property {Function} getPositions - 获取持仓信息
 * @property {Function} closePosition - 平仓
 * @property {Function} closeAllPositions - 全部平仓
 * @property {Function} getTradingPairs - 获取交易对信息
 * @property {Function} getMarketData - 获取市场数据
 * @property {Function} getKlineData - 获取K线数据
 * @property {Function} getOrderBook - 获取订单簿
 * @property {Function} getRecentTrades - 获取最近成交
 * @property {Function} get24hrStats - 获取24小时统计
 */

/**
 * 配置相关API
 * @typedef {Object} ConfigsAPI
 * @property {Function} getConfigs - 获取所有配置
 * @property {Function} getExchangeConfigs - 获取交易所配置
 * @property {Function} createExchangeConfig - 创建交易所配置
 * @property {Function} updateExchangeConfig - 更新交易所配置
 * @property {Function} deleteExchangeConfig - 删除交易所配置
 * @property {Function} testExchangeConnection - 测试交易所连接
 * @property {Function} getRiskConfig - 获取风控配置
 * @property {Function} updateRiskConfig - 更新风控配置
 * @property {Function} getSignalConfigs - 获取信号源配置
 * @property {Function} createSignalConfig - 创建信号源配置
 * @property {Function} updateSignalConfig - 更新信号源配置
 * @property {Function} deleteSignalConfig - 删除信号源配置
 * @property {Function} testSignalConnection - 测试信号源连接
 * @property {Function} getSystemConfig - 获取系统配置
 * @property {Function} updateSystemConfig - 更新系统配置
 * @property {Function} getSupportedExchanges - 获取支持的交易所列表
 * @property {Function} getSupportedSignalSources - 获取支持的信号源列表
 * @property {Function} exportConfigs - 导出配置
 * @property {Function} importConfigs - 导入配置
 * @property {Function} resetConfigs - 重置配置到默认值
 * @property {Function} getConfigTemplates - 获取配置模板
 */

/**
 * Agent相关API
 * @typedef {Object} AgentAPI
 * @property {Function} getAgentStatus - 获取Agent状态
 * @property {Function} startAgent - 启动Agent
 * @property {Function} stopAgent - 停止Agent
 * @property {Function} restartAgent - 重启Agent
 * @property {Function} getAgentLogs - 获取Agent日志
 * @property {Function} clearAgentLogs - 清除Agent日志
 * @property {Function} getCurrentTask - 获取当前任务
 * @property {Function} getTaskHistory - 获取任务历史
 * @property {Function} cancelCurrentTask - 取消当前任务
 * @property {Function} getPendingActions - 获取待处理动作
 * @property {Function} respondToPendingAction - 响应待处理动作
 * @property {Function} sendTradingSignal - 发送交易信号
 * @property {Function} getSignalHistory - 获取信号历史
 * @property {Function} getAgentStats - 获取Agent性能统计
 * @property {Function} getAgentConfig - 获取Agent配置
 * @property {Function} updateAgentConfig - 更新Agent配置
 * @property {Function} getAgentHealth - 获取Agent健康状态
 * @property {Function} syncAgentState - 强制同步Agent状态
 * @property {Function} getAgentVersion - 获取Agent版本信息
 * @property {Function} updateAgent - 更新Agent
 * @property {Function} getAgentDiagnostics - 获取Agent诊断信息
 * @property {Function} exportAgentData - 导出Agent数据
 */

// 统一导出
export {
  authAPI,
  ordersAPI,
  configsAPI,
  agentAPI,
  client,
  APIError,
  HTTP_STATUS
}

// 默认导出
export default {
  auth: authAPI,
  orders: ordersAPI,
  configs: configsAPI,
  agent: agentAPI,
  client,
  APIError,
  HTTP_STATUS
}