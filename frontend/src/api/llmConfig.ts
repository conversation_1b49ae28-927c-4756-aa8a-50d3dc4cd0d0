/**
 * LLM配置相关API
 * 封装LLM配置管理的所有API调用
 */
import { get, post, put, del } from './client'
import type { 
  LLMConfig, 
  LLMConfigRequest, 
  LLMConfigTestRequest, 
  LLMConfigTestResponse 
} from '@/types/llm.types'

/**
 * 获取LLM配置列表
 * @returns {Promise<LLMConfig[]>} LLM配置列表
 */
export async function getLLMConfigs(): Promise<LLMConfig[]> {
  return get('/api/v1/llm-configs')
}

/**
 * 获取单个LLM配置
 * @param {string} id - 配置ID
 * @returns {Promise<LLMConfig>} LLM配置详情
 */
export async function getLLMConfig(id: string): Promise<LLMConfig> {
  return get(`/api/v1/llm-configs/${id}`)
}

/**
 * 创建LLM配置
 * @param {LLMConfigRequest} configData - 配置数据
 * @returns {Promise<LLMConfig>} 创建的配置
 */
export async function createLLMConfig(configData: LLMConfigRequest): Promise<LLMConfig> {
  return post('/api/v1/llm-configs', configData)
}

/**
 * 更新LLM配置
 * @param {string} id - 配置ID
 * @param {LLMConfigRequest} configData - 更新的配置数据
 * @returns {Promise<LLMConfig>} 更新后的配置
 */
export async function updateLLMConfig(id: string, configData: LLMConfigRequest): Promise<LLMConfig> {
  return put(`/api/v1/llm-configs/${id}`, configData)
}

/**
 * 删除LLM配置
 * @param {string} id - 配置ID
 * @returns {Promise<{deleted_id: string}>} 删除结果
 */
export async function deleteLLMConfig(id: string): Promise<{deleted_id: string}> {
  return del(`/api/v1/llm-configs/${id}`)
}

/**
 * 设置为默认LLM配置
 * @param {string} id - 配置ID
 * @returns {Promise<{config_id: string}>} 设置结果
 */
export async function setDefaultLLMConfig(id: string): Promise<{config_id: string}> {
  return post(`/api/v1/llm-configs/${id}/set-default`, {})
}

/**
 * 测试LLM配置连接
 * 使用统一的默认测试消息
 * @param {string} id - 配置ID
 * @returns {Promise<LLMConfigTestResponse>} 测试结果
 */
export async function testLLMConfig(
  id: string
): Promise<LLMConfigTestResponse> {
  return post(`/api/v1/llm-configs/${id}/test`, {})
}

/**
 * 批量操作：启用/禁用多个LLM配置
 * @param {string[]} ids - 配置ID列表
 * @param {boolean} enabled - 是否启用
 * @returns {Promise<LLMConfig[]>} 更新后的配置列表
 */
export async function batchToggleLLMConfigs(ids: string[], enabled: boolean): Promise<LLMConfig[]> {
  const promises = ids.map(id => 
    updateLLMConfig(id, { enabled } as LLMConfigRequest)
  )
  return Promise.all(promises)
}

/**
 * 获取默认LLM配置
 * @returns {Promise<LLMConfig | null>} 默认配置或null
 */
export async function getDefaultLLMConfig(): Promise<LLMConfig | null> {
  const configs = await getLLMConfigs()
  return configs.find(config => config.is_default) || null
}

/**
 * 获取启用的LLM配置列表
 * @returns {Promise<LLMConfig[]>} 启用的配置列表
 */
export async function getEnabledLLMConfigs(): Promise<LLMConfig[]> {
  const configs = await getLLMConfigs()
  return configs.filter(config => config.enabled)
}

/**
 * 按提供商分组获取LLM配置
 * @returns {Promise<Record<string, LLMConfig[]>>} 按提供商分组的配置
 */
export async function getLLMConfigsByProvider(): Promise<Record<string, LLMConfig[]>> {
  const configs = await getLLMConfigs()
  return configs.reduce((groups, config) => {
    const provider = config.provider
    if (!groups[provider]) {
      groups[provider] = []
    }
    groups[provider].push(config)
    return groups
  }, {} as Record<string, LLMConfig[]>)
}

/**
 * 验证配置名称是否唯一
 * @param {string} name - 配置名称
 * @param {string} excludeId - 排除的配置ID（用于编辑时验证）
 * @returns {Promise<boolean>} 是否唯一
 */
export async function isLLMConfigNameUnique(name: string, excludeId?: string): Promise<boolean> {
  try {
    const configs = await getLLMConfigs()
    return !configs.some(config => 
      config.config_name === name && config.id !== excludeId
    )
  } catch (error) {
    console.error('验证配置名称唯一性失败:', error)
    return true // 发生错误时假设唯一，让服务器端验证
  }
}

/**
 * 复制LLM配置（创建副本）
 * @param {string} id - 源配置ID
 * @param {string} newName - 新配置名称
 * @returns {Promise<LLMConfig>} 复制的配置
 */
export async function duplicateLLMConfig(id: string, newName: string): Promise<LLMConfig> {
  const sourceConfig = await getLLMConfig(id)
  
  const duplicateData: LLMConfigRequest = {
    config_name: newName,
    provider: sourceConfig.provider as any,
    enabled: false, // 复制的配置默认禁用
    is_default: false, // 复制的配置不能是默认配置
    api_key: '', // 需要用户重新输入API密钥
    api_base_url: sourceConfig.api_base_url,
    model_name: sourceConfig.model_name,
    max_tokens: sourceConfig.max_tokens,
    temperature: sourceConfig.temperature,
    timeout_seconds: sourceConfig.timeout_seconds,
    max_retries: sourceConfig.max_retries
  }
  
  return createLLMConfig(duplicateData)
}

/**
 * 导出LLM配置（不包含敏感信息）
 * @param {string} id - 配置ID
 * @returns {Promise<Partial<LLMConfig>>} 导出的配置数据
 */
export async function exportLLMConfig(id: string): Promise<Partial<LLMConfig>> {
  const config = await getLLMConfig(id)
  
  // 移除敏感信息
  const { api_key_masked, id: configId, created_at, updated_at, ...exportData } = config
  
  return exportData
}

/**
 * 批量导出LLM配置
 * @param {string[]} ids - 配置ID列表
 * @returns {Promise<Partial<LLMConfig>[]>} 导出的配置数据列表
 */
export async function exportLLMConfigs(ids: string[]): Promise<Partial<LLMConfig>[]> {
  const promises = ids.map(id => exportLLMConfig(id))
  return Promise.all(promises)
}

/**
 * 获取LLM配置统计信息
 * @returns {Promise<{total: number, enabled: number, byProvider: Record<string, number>}>} 统计信息
 */
export async function getLLMConfigStats(): Promise<{
  total: number
  enabled: number
  byProvider: Record<string, number>
}> {
  const configs = await getLLMConfigs()
  
  const stats = {
    total: configs.length,
    enabled: configs.filter(config => config.enabled).length,
    byProvider: configs.reduce((counts, config) => {
      counts[config.provider] = (counts[config.provider] || 0) + 1
      return counts
    }, {} as Record<string, number>)
  }
  
  return stats
}
