/**
 * 无障碍功能样式
 * 包含屏幕阅读器支持、高对比度模式和键盘导航样式
 */

/* 屏幕阅读器专用内容 */
.sr-only {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}

/* 跳转链接 */
.skip-links {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 10000;
}

.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: #000;
  color: #fff;
  padding: 8px 16px;
  text-decoration: none;
  border-radius: 0 0 4px 4px;
  font-size: 14px;
  font-weight: 500;
  transition: top 0.3s ease;
}

.skip-link:focus {
  top: 0;
}

/* 键盘导航指示器 */
.keyboard-navigation *:focus {
  outline: 2px solid #2196f3 !important;
  outline-offset: 2px !important;
}

.keyboard-navigation button:focus,
.keyboard-navigation input:focus,
.keyboard-navigation select:focus,
.keyboard-navigation textarea:focus {
  box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.3) !important;
}

/* 高对比度模式 */
.high-contrast {
  --v-theme-background: #000000;
  --v-theme-surface: #000000;
  --v-theme-primary: #ffff00;
  --v-theme-secondary: #00ffff;
  --v-theme-accent: #ff00ff;
  --v-theme-error: #ff0000;
  --v-theme-warning: #ffaa00;
  --v-theme-info: #0088ff;
  --v-theme-success: #00ff00;
  --v-theme-on-background: #ffffff;
  --v-theme-on-surface: #ffffff;
  --v-theme-on-primary: #000000;
  --v-theme-on-secondary: #000000;
  --v-theme-on-accent: #000000;
  --v-theme-on-error: #ffffff;
  --v-theme-on-warning: #000000;
  --v-theme-on-info: #ffffff;
  --v-theme-on-success: #000000;
}

.high-contrast * {
  border-color: #ffffff !important;
}

.high-contrast .v-card,
.high-contrast .v-sheet {
  background: #000000 !important;
  color: #ffffff !important;
  border: 1px solid #ffffff !important;
}

.high-contrast .v-btn {
  border: 2px solid #ffffff !important;
}

.high-contrast .v-btn--variant-elevated {
  background: #ffff00 !important;
  color: #000000 !important;
}

.high-contrast .v-btn--variant-outlined {
  background: transparent !important;
  color: #ffffff !important;
}

.high-contrast .v-data-table {
  background: #000000 !important;
}

.high-contrast .v-data-table th,
.high-contrast .v-data-table td {
  border-bottom: 1px solid #ffffff !important;
  color: #ffffff !important;
}

.high-contrast .v-data-table tr:hover {
  background: #333333 !important;
}

/* 焦点陷阱容器 */
.focus-trap {
  position: relative;
}

.focus-trap::before,
.focus-trap::after {
  content: '';
  position: absolute;
  width: 1px;
  height: 1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
}

/* 可访问的表单标签 */
.form-field {
  position: relative;
}

.form-field .required::after {
  content: ' *';
  color: #f44336;
  font-weight: bold;
}

.form-field .error-message {
  color: #f44336;
  font-size: 12px;
  margin-top: 4px;
  display: block;
}

.form-field .help-text {
  color: #666;
  font-size: 12px;
  margin-top: 4px;
  display: block;
}

/* 可访问的按钮 */
.btn-accessible {
  position: relative;
  min-height: 44px; /* 触摸目标最小尺寸 */
  min-width: 44px;
}

.btn-accessible:focus {
  outline: 2px solid #2196f3;
  outline-offset: 2px;
}

.btn-accessible[aria-pressed="true"] {
  background: #1976d2;
  color: white;
}

/* 可访问的链接 */
.link-accessible {
  color: #1976d2;
  text-decoration: underline;
}

.link-accessible:hover,
.link-accessible:focus {
  color: #0d47a1;
  text-decoration: none;
}

.link-accessible:visited {
  color: #7b1fa2;
}

/* 可访问的表格 */
.table-accessible {
  border-collapse: collapse;
  width: 100%;
}

.table-accessible th,
.table-accessible td {
  border: 1px solid #ddd;
  padding: 8px;
  text-align: left;
}

.table-accessible th {
  background: #f5f5f5;
  font-weight: bold;
}

.table-accessible caption {
  font-weight: bold;
  margin-bottom: 8px;
  text-align: left;
}

/* 可访问的模态框 */
.modal-accessible {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-accessible .modal-content {
  background: white;
  border-radius: 4px;
  padding: 24px;
  max-width: 90vw;
  max-height: 90vh;
  overflow: auto;
  position: relative;
}

.modal-accessible .modal-close {
  position: absolute;
  top: 8px;
  right: 8px;
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  padding: 8px;
  line-height: 1;
}

/* 可访问的进度指示器 */
.progress-accessible {
  position: relative;
}

.progress-accessible .progress-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 12px;
  font-weight: bold;
}

/* 可访问的标签页 */
.tabs-accessible {
  border-bottom: 1px solid #ddd;
}

.tabs-accessible .tab-list {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
}

.tabs-accessible .tab {
  border: none;
  background: none;
  padding: 12px 16px;
  cursor: pointer;
  border-bottom: 2px solid transparent;
}

.tabs-accessible .tab:hover,
.tabs-accessible .tab:focus {
  background: #f5f5f5;
  outline: none;
}

.tabs-accessible .tab[aria-selected="true"] {
  border-bottom-color: #1976d2;
  color: #1976d2;
  font-weight: bold;
}

.tabs-accessible .tab-panel {
  padding: 16px 0;
}

.tabs-accessible .tab-panel:focus {
  outline: none;
}

/* 响应式无障碍 */
@media (max-width: 768px) {
  .skip-link {
    left: 4px;
    padding: 6px 12px;
    font-size: 12px;
  }
  
  .btn-accessible {
    min-height: 48px; /* 移动设备上更大的触摸目标 */
    min-width: 48px;
  }
}

/* 减少动画偏好 */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* 高对比度偏好 */
@media (prefers-contrast: high) {
  :root {
    --v-theme-primary: #0066cc;
    --v-theme-secondary: #cc6600;
  }
  
  .v-btn,
  .v-card,
  .v-sheet {
    border: 1px solid currentColor;
  }
}
