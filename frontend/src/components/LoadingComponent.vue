<template>
  <div class="loading-component" :class="{ 'full-screen': fullScreen }">
    <!-- 骨架屏模式 -->
    <div v-if="type === 'skeleton'" class="skeleton-container">
      <div v-for="n in skeletonRows" :key="n" class="skeleton-row">
        <div class="skeleton-item" :style="{ width: getSkeletonWidth(n) }"></div>
      </div>
    </div>

    <!-- 卡片骨架屏 -->
    <div v-else-if="type === 'card-skeleton'" class="card-skeleton-container">
      <v-card v-for="n in cardCount" :key="n" class="skeleton-card" elevation="2">
        <v-card-title>
          <div class="skeleton-item skeleton-title"></div>
        </v-card-title>
        <v-card-text>
          <div class="skeleton-item skeleton-text mb-2"></div>
          <div class="skeleton-item skeleton-text" style="width: 70%"></div>
        </v-card-text>
      </v-card>
    </div>

    <!-- 表格骨架屏 -->
    <div v-else-if="type === 'table-skeleton'" class="table-skeleton-container">
      <v-data-table
        :headers="skeletonHeaders"
        :items="skeletonTableItems"
        :loading="false"
        hide-default-footer
      >
        <template v-for="header in skeletonHeaders" :key="header.key" #[`item.${header.key}`]>
          <div class="skeleton-item skeleton-cell"></div>
        </template>
      </v-data-table>
    </div>

    <!-- 传统加载指示器 -->
    <div v-else class="loading-container">
      <v-progress-circular
        v-if="type === 'circular'"
        :size="size"
        :width="width"
        :color="color"
        indeterminate
      />
      
      <v-progress-linear
        v-else-if="type === 'linear'"
        :color="color"
        :height="height"
        indeterminate
      />
      
      <div v-else-if="type === 'dots'" class="dots-loading">
        <div class="dot" v-for="n in 3" :key="n"></div>
      </div>
      
      <div v-else-if="type === 'pulse'" class="pulse-loading">
        <div class="pulse-circle"></div>
      </div>

      <!-- 加载消息 -->
      <div v-if="message" class="loading-message">
        {{ message }}
      </div>
      
      <!-- 进度信息 -->
      <div v-if="showProgress && progress !== undefined" class="progress-info">
        {{ Math.round(progress) }}%
      </div>
    </div>

    <!-- 超时提示 -->
    <div v-if="showTimeout" class="timeout-message">
      <v-icon color="warning" class="mr-2">mdi-clock-alert</v-icon>
      加载时间较长，请稍候...
      <v-btn 
        v-if="onRetry" 
        size="small" 
        color="primary" 
        variant="text" 
        @click="onRetry"
        class="ml-2"
      >
        重试
      </v-btn>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'

interface Props {
  type?: 'circular' | 'linear' | 'dots' | 'pulse' | 'skeleton' | 'card-skeleton' | 'table-skeleton'
  size?: number
  width?: number
  height?: number
  color?: string
  message?: string
  fullScreen?: boolean
  showProgress?: boolean
  progress?: number
  skeletonRows?: number
  cardCount?: number
  skeletonHeaders?: Array<{ key: string; title: string }>
  timeout?: number // 超时时间（毫秒）
  onRetry?: () => void
}

const props = withDefaults(defineProps<Props>(), {
  type: 'circular',
  size: 40,
  width: 4,
  height: 4,
  color: 'primary',
  fullScreen: false,
  showProgress: false,
  skeletonRows: 5,
  cardCount: 3,
  timeout: 10000 // 10秒超时
})

const showTimeout = ref(false)
let timeoutTimer: NodeJS.Timeout | null = null

// 骨架屏表格数据
const skeletonTableItems = computed(() => {
  return Array.from({ length: props.skeletonRows }, (_, index) => ({
    id: index,
    ...Object.fromEntries(
      (props.skeletonHeaders || []).map(header => [header.key, ''])
    )
  }))
})

// 生成骨架屏宽度
function getSkeletonWidth(index: number): string {
  const widths = ['100%', '85%', '92%', '78%', '95%']
  return widths[index % widths.length]
}

// 设置超时检测
onMounted(() => {
  if (props.timeout > 0) {
    timeoutTimer = setTimeout(() => {
      showTimeout.value = true
    }, props.timeout)
  }
})

onUnmounted(() => {
  if (timeoutTimer) {
    clearTimeout(timeoutTimer)
  }
})
</script>

<style scoped>
.loading-component {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  min-height: 100px;
}

.loading-component.full-screen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(var(--v-theme-surface), 0.9);
  z-index: 9999;
  min-height: 100vh;
}

/* 骨架屏样式 */
.skeleton-container {
  width: 100%;
  max-width: 600px;
}

.skeleton-row {
  margin-bottom: 12px;
}

.skeleton-item {
  height: 20px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 4px;
}

.skeleton-title {
  height: 24px;
  width: 60%;
}

.skeleton-text {
  height: 16px;
  width: 100%;
}

.skeleton-cell {
  height: 16px;
  width: 80%;
}

/* 卡片骨架屏 */
.card-skeleton-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
  width: 100%;
}

.skeleton-card {
  min-height: 150px;
}

/* 表格骨架屏 */
.table-skeleton-container {
  width: 100%;
}

/* 加载容器 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.loading-message {
  color: rgba(var(--v-theme-on-surface), 0.7);
  font-size: 14px;
  text-align: center;
}

.progress-info {
  color: rgba(var(--v-theme-on-surface), 0.8);
  font-weight: 500;
  font-size: 16px;
}

/* 点状加载动画 */
.dots-loading {
  display: flex;
  gap: 4px;
}

.dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: rgb(var(--v-theme-primary));
  animation: dots-bounce 1.4s infinite ease-in-out both;
}

.dot:nth-child(1) { animation-delay: -0.32s; }
.dot:nth-child(2) { animation-delay: -0.16s; }

/* 脉冲加载动画 */
.pulse-loading {
  position: relative;
}

.pulse-circle {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgb(var(--v-theme-primary));
  animation: pulse-scale 1.5s infinite ease-in-out;
}

/* 超时消息 */
.timeout-message {
  display: flex;
  align-items: center;
  color: rgb(var(--v-theme-warning));
  font-size: 14px;
  margin-top: 16px;
  padding: 12px;
  border-radius: 4px;
  background: rgba(var(--v-theme-warning), 0.1);
}

/* 动画定义 */
@keyframes skeleton-loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

@keyframes dots-bounce {
  0%, 80%, 100% { transform: scale(0); }
  40% { transform: scale(1); }
}

@keyframes pulse-scale {
  0% { transform: scale(0); opacity: 1; }
  100% { transform: scale(1); opacity: 0; }
}

/* 深色主题支持 */
.v-theme--dark .skeleton-item {
  background: linear-gradient(90deg, #424242 25%, #616161 50%, #424242 75%);
  background-size: 200% 100%;
}
</style>
