<template>
  <v-dialog
    v-model="dialogOpen"
    max-width="600px"
    persistent
    @update:modelValue="$emit('update:open', $event)"
  >
    <v-card>
      <v-card-title class="pb-2">
        <v-icon class="mr-2" color="primary">mdi-creation</v-icon>
        创建条件订单
      </v-card-title>
      <v-card-text>
        <v-form ref="form" v-model="isFormValid">
          <!-- 触发条件部分 -->
          <div class="mb-4">
            <div class="text-subtitle-1 font-weight-bold mb-2">触发条件</div>
            
            <v-row>
              <v-col cols="12" sm="6">
                <v-select
                  v-model="orderData.trigger_condition.symbol"
                  :items="availableSymbols"
                  label="交易对"
                  variant="outlined"
                  density="comfortable"
                  :rules="[v => !!v || '请选择交易对']"
                ></v-select>
              </v-col>
              
              <v-col cols="12" sm="6">
                <v-select
                  v-model="orderData.trigger_condition.operator"
                  :items="operators"
                  label="条件"
                  variant="outlined"
                  density="comfortable"
                  :rules="[v => !!v || '请选择条件']"
                ></v-select>
              </v-col>
            </v-row>
            
            <v-row>
              <v-col cols="12">
                <v-text-field
                  v-model="orderData.trigger_condition.price"
                  label="价格"
                  variant="outlined"
                  density="comfortable"
                  type="number"
                  :rules="[
                    v => !!v || '请输入价格',
                    v => Number(v) > 0 || '价格必须大于0'
                  ]"
                ></v-text-field>
              </v-col>
            </v-row>
          </div>
          
          <!-- 行动计划部分 -->
          <div>
            <div class="text-subtitle-1 font-weight-bold mb-2">行动计划</div>
            
            <v-row>
              <v-col cols="12" sm="6">
                <v-select
                  v-model="orderData.action_plan.side"
                  :items="sides"
                  label="方向"
                  variant="outlined"
                  density="comfortable"
                  :rules="[v => !!v || '请选择交易方向']"
                ></v-select>
              </v-col>
              
              <v-col cols="12" sm="6">
                <v-select
                  v-model="orderData.action_plan.order_type"
                  :items="orderTypes"
                  label="订单类型"
                  variant="outlined"
                  density="comfortable"
                  :rules="[v => !!v || '请选择订单类型']"
                ></v-select>
              </v-col>
            </v-row>
            
            <v-row>
              <v-col cols="12" sm="6">
                <v-text-field
                  v-model="orderData.action_plan.quantity_usd"
                  label="金额 (USD)"
                  variant="outlined"
                  density="comfortable"
                  type="number"
                  :rules="[
                    v => !!v || '请输入金额',
                    v => Number(v) > 0 || '金额必须大于0'
                  ]"
                ></v-text-field>
              </v-col>
              
              <v-col cols="12" sm="6">
                <v-text-field
                  v-model="orderData.action_plan.price"
                  label="限价 (可选)"
                  variant="outlined"
                  density="comfortable"
                  type="number"
                  hint="市价单可不填"
                  :disabled="orderData.action_plan.order_type === 'market'"
                ></v-text-field>
              </v-col>
            </v-row>
            
            <v-row>
              <v-col cols="12">
                <v-text-field
                  v-model="orderData.expiry_hours"
                  label="有效期 (小时)"
                  variant="outlined"
                  density="comfortable"
                  type="number"
                  hint="0表示永不过期"
                  :rules="[v => Number(v) >= 0 || '有效期不能为负']"
                ></v-text-field>
              </v-col>
            </v-row>
          </div>
        </v-form>
      </v-card-text>
      
      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn
          variant="outlined"
          @click="closeDialog"
        >
          取消
        </v-btn>
        <v-btn
          color="primary"
          variant="flat"
          :disabled="!isFormValid"
          :loading="submitting"
          @click="submitOrder"
        >
          创建
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script setup>
import { ref, reactive, defineProps, defineEmits, watch } from 'vue';

const props = defineProps({
  open: {
    type: Boolean,
    default: false
  },
  submitting: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:open', 'submit']);

// 本地状态
const dialogOpen = ref(props.open);
const isFormValid = ref(false);
const form = ref(null);

// 监听外部更新
watch(() => props.open, (newVal) => {
  dialogOpen.value = newVal;
});

// 数据模型
const orderData = reactive({
  trigger_condition: {
    symbol: 'BTC/USDT',
    operator: 'gte',
    price: null
  },
  action_plan: {
    symbol: '',
    side: 'buy',
    order_type: 'market',
    quantity_usd: 100,
    price: null
  },
  expiry_hours: 24
});

// 可用选项
const availableSymbols = [
  'BTC/USDT',
  'ETH/USDT',
  'XRP/USDT',
  'SOL/USDT',
  'DOGE/USDT'
];

const operators = [
  { title: '大于等于', value: 'gte' },
  { title: '小于等于', value: 'lte' },
  { title: '大于', value: 'gt' },
  { title: '小于', value: 'lt' }
];

const sides = [
  { title: '做多', value: 'buy' },
  { title: '做空', value: 'sell' }
];

const orderTypes = [
  { title: '市价单', value: 'market' },
  { title: '限价单', value: 'limit' }
];

// 方法
function closeDialog() {
  emit('update:open', false);
}

function submitOrder() {
  if (!isFormValid.value) return;

  // 设置action_plan的symbol与触发条件相同
  orderData.action_plan.symbol = orderData.trigger_condition.symbol;

  // 准备提交数据，移除后端不支持的字段
  const submitData = {
    symbol: orderData.trigger_condition.symbol,
    trigger_condition: orderData.trigger_condition,
    action_plan: orderData.action_plan
  };

  // 提交表单
  emit('submit', submitData);
}

// 监听订单类型变化，市价单时清除限价
watch(
  () => orderData.action_plan.order_type,
  (newType) => {
    if (newType === 'market') {
      orderData.action_plan.price = null;
    }
  }
);
</script> 