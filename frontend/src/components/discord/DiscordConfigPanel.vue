<template>
  <div class="discord-config-panel" data-testid="discord-config-panel">
    <!-- 头部操作栏 -->
    <v-card class="mb-4" elevation="1">
      <v-card-text>
        <div class="d-flex align-center justify-space-between">
          <div class="d-flex align-center">
            <v-icon class="me-2" color="primary">mdi-discord</v-icon>
            <div>
              <h3 class="text-h6">Discord信号源配置</h3>
              <p class="text-body-2 text-medium-emphasis mb-0">
                管理Discord Bot配置，监听指定频道的交易信号
              </p>
            </div>
          </div>

          <v-btn
            color="primary"
            prepend-icon="mdi-plus"
            @click="openCreateDialog"
            data-testid="create-config-btn"
          >
            新建配置
          </v-btn>
        </div>
      </v-card-text>
    </v-card>

    <!-- 统计信息 -->
    <v-row class="mb-4">
      <v-col cols="12" sm="6" md="3">
        <v-card>
          <v-card-text>
            <div class="d-flex align-center">
              <v-icon class="me-3" color="primary" size="large">mdi-cog</v-icon>
              <div>
                <div class="text-h5" data-testid="total-configs">{{ discordStore.configCount }}</div>
                <div class="text-body-2 text-medium-emphasis">总配置数</div>
              </div>
            </div>
          </v-card-text>
        </v-card>
      </v-col>
      
      <v-col cols="12" sm="6" md="3">
        <v-card>
          <v-card-text>
            <div class="d-flex align-center">
              <v-icon class="me-3" color="success" size="large">mdi-check-circle</v-icon>
              <div>
                <div class="text-h5" data-testid="enabled-configs">{{ discordStore.enabledConfigs.length }}</div>
                <div class="text-body-2 text-medium-emphasis">已启用</div>
              </div>
            </div>
          </v-card-text>
        </v-card>
      </v-col>
      
      <v-col cols="12" sm="6" md="3">
        <v-card>
          <v-card-text>
            <div class="d-flex align-center">
              <v-icon class="me-3" color="warning" size="large">mdi-pause-circle</v-icon>
              <div>
                <div class="text-h5" data-testid="disabled-configs">{{ discordStore.configCount - discordStore.enabledConfigs.length }}</div>
                <div class="text-body-2 text-medium-emphasis">已禁用</div>
              </div>
            </div>
          </v-card-text>
        </v-card>
      </v-col>
      
      <v-col cols="12" sm="6" md="3">
        <v-card>
          <v-card-text>
            <div class="d-flex align-center">
              <v-icon class="me-3" color="info" size="large">mdi-refresh</v-icon>
              <div>
                <div class="d-flex align-center" style="height: 32px;">
                  <v-btn
                    variant="text"
                    size="small"
                    :loading="discordStore.loading"
                    @click="refreshConfigs"
                    data-testid="refresh-btn"
                    class="pa-2"
                  >
                    刷新
                  </v-btn>
                </div>
                <div class="text-body-2 text-medium-emphasis">点击更新数据</div>
              </div>
            </div>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <!-- 配置列表 -->
    <div v-if="discordStore.loading && discordStore.configs.length === 0" class="text-center py-8">
      <v-progress-circular indeterminate color="primary" />
      <p class="mt-4 text-body-1">加载配置中...</p>
    </div>

    <div v-else-if="discordStore.configs.length === 0" class="text-center py-8">
      <v-icon size="64" color="grey-lighten-1">mdi-discord-outline</v-icon>
      <h3 class="text-h6 mt-4 mb-2">暂无Discord配置</h3>
      <p class="text-body-2 text-medium-emphasis mb-4">
        创建第一个Discord配置来开始监听交易信号
      </p>
      <v-btn color="primary" @click="openCreateDialog">
        创建配置
      </v-btn>
    </div>

    <v-row v-else>
      <v-col
        v-for="config in discordStore.configs"
        :key="config.id"
        cols="12"
        md="6"
        lg="4"
      >
        <DiscordConfigCard
          :config="config"
          @edit="openEditDialog"
          @toggle="toggleConfig"
          @delete="deleteConfig"
        />
      </v-col>
    </v-row>

    <!-- 错误提示 -->
    <v-alert
      v-if="discordStore.error"
      type="error"
      class="mt-4"
      closable
      @click:close="discordStore.clearError()"
    >
      {{ discordStore.error }}
    </v-alert>

    <!-- 配置对话框 -->
    <DiscordConfigDialog
      v-model="dialogVisible"
      :config="editingConfig"
      :loading="discordStore.loading"
      @save="saveConfig"
    />

    <!-- 删除确认对话框 -->
    <v-dialog v-model="deleteDialogVisible" max-width="400px">
      <v-card>
        <v-card-title>确认删除</v-card-title>
        <v-card-text>
          确定要删除这个Discord配置吗？此操作不可撤销。
        </v-card-text>
        <v-card-actions>
          <v-spacer />
          <v-btn @click="deleteDialogVisible = false">取消</v-btn>
          <v-btn
            color="error"
            :loading="discordStore.loading"
            @click="confirmDelete"
          >
            删除
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useDiscordConfigStore } from '@/stores/discordConfig'
import type { DiscordConfig, DiscordConfigRequest } from '@/stores/discordConfig'
import DiscordConfigCard from './DiscordConfigCard.vue'
import DiscordConfigDialog from './DiscordConfigDialog.vue'

// Store
const discordStore = useDiscordConfigStore()

// 响应式数据
const dialogVisible = ref(false)
const deleteDialogVisible = ref(false)
const editingConfig = ref<DiscordConfig | undefined>()
const deletingConfigId = ref<string>('')

// 生命周期
onMounted(() => {
  discordStore.fetchConfigs()
})

// 方法
const refreshConfigs = () => {
  discordStore.fetchConfigs()
}

const openCreateDialog = () => {
  editingConfig.value = undefined
  dialogVisible.value = true
}

const openEditDialog = (config: DiscordConfig) => {
  editingConfig.value = config
  dialogVisible.value = true
}

const saveConfig = async (configData: DiscordConfigRequest) => {
  let success = false
  
  if (editingConfig.value) {
    // 编辑模式
    success = await discordStore.updateConfig(editingConfig.value.id, configData)
  } else {
    // 创建模式
    success = await discordStore.createConfig(configData)
  }
  
  if (success) {
    dialogVisible.value = false
    editingConfig.value = undefined
  }
}

const toggleConfig = async (id: string) => {
  await discordStore.toggleConfigEnabled(id)
}

const deleteConfig = (id: string) => {
  deletingConfigId.value = id
  deleteDialogVisible.value = true
}

const confirmDelete = async () => {
  if (deletingConfigId.value) {
    const success = await discordStore.deleteConfig(deletingConfigId.value)
    if (success) {
      deleteDialogVisible.value = false
      deletingConfigId.value = ''
    }
  }
}
</script>

<style scoped>
.discord-config-panel {
  padding: 16px;
}
</style>
