<template>
  <v-dialog v-model="dialog" max-width="800px" persistent>
    <v-card data-testid="discord-config-dialog">
      <v-card-title class="d-flex align-center">
        <v-icon class="me-2">mdi-discord</v-icon>
        {{ isEdit ? '编辑Discord配置' : '新建Discord配置' }}
      </v-card-title>

      <v-card-text>
        <v-form ref="form" v-model="valid">
          <!-- 基础配置 -->
          <v-row>
            <v-col cols="12" md="6">
              <v-text-field
                v-model="formData.source_name"
                label="配置名称"
                :rules="nameRules"
                required
                prepend-icon="mdi-tag"
                hint="为此Discord配置起一个易识别的名称"
                persistent-hint
                data-testid="config-source-name"
              />
            </v-col>
            
            <v-col cols="12" md="6">
              <v-switch
                v-model="formData.enabled"
                label="启用配置"
                color="success"
                inset
                hide-details
                data-testid="config-enabled"
              />
            </v-col>
          </v-row>

          <!-- Discord Token -->
          <v-row>
            <v-col cols="12">
              <v-text-field
                v-model="formData.token"
                label="Discord Bot Token"
                :type="showToken ? 'text' : 'password'"
                :rules="tokenRules"
                :required="!isEdit"
                prepend-icon="mdi-key"
                :append-icon="showToken ? 'mdi-eye' : 'mdi-eye-off'"
                @click:append="showToken = !showToken"
                :hint="isEdit ? '留空保持原有Token，输入新Token则更新' : '请输入Discord Bot的Token'"
                persistent-hint
                data-testid="config-token"
              />
            </v-col>
          </v-row>

          <!-- 过滤配置 -->
          <v-expansion-panels class="mt-4">
            <v-expansion-panel>
              <v-expansion-panel-title>
                <v-icon class="me-2">mdi-filter</v-icon>
                过滤设置
              </v-expansion-panel-title>
              
              <v-expansion-panel-text>
                <!-- 服务器ID -->
                <v-row>
                  <v-col cols="12">
                    <v-combobox
                      v-model="formData.server_ids"
                      label="服务器ID列表"
                      multiple
                      chips
                      clearable
                      prepend-icon="mdi-server"
                      hint="留空表示监听所有服务器，输入服务器ID后按回车添加"
                      persistent-hint
                    />
                  </v-col>
                </v-row>

                <!-- 频道ID -->
                <v-row>
                  <v-col cols="12">
                    <v-combobox
                      v-model="formData.channel_ids"
                      label="频道ID列表"
                      multiple
                      chips
                      clearable
                      prepend-icon="mdi-pound"
                      hint="留空表示监听所有频道，输入频道ID后按回车添加"
                      persistent-hint
                    />
                  </v-col>
                </v-row>

                <!-- 作者ID -->
                <v-row>
                  <v-col cols="12">
                    <v-combobox
                      v-model="formData.author_ids"
                      label="作者ID列表"
                      multiple
                      chips
                      clearable
                      prepend-icon="mdi-account"
                      hint="留空表示监听所有用户，输入用户ID后按回车添加"
                      persistent-hint
                    />
                  </v-col>
                </v-row>

                <!-- 消息类型 -->
                <v-row>
                  <v-col cols="12">
                    <v-select
                      v-model="formData.allowed_message_types"
                      :items="messageTypeOptions"
                      label="允许的消息类型"
                      multiple
                      chips
                      prepend-icon="mdi-message"
                      hint="选择要监听的消息类型"
                      persistent-hint
                    />
                  </v-col>
                </v-row>
              </v-expansion-panel-text>
            </v-expansion-panel>
          </v-expansion-panels>
        </v-form>
      </v-card-text>

      <v-card-actions>
        <v-spacer />
        <v-btn @click="closeDialog" data-testid="cancel-btn">取消</v-btn>
        <v-btn
          color="primary"
          :loading="loading"
          :disabled="!valid"
          @click="saveConfig"
          data-testid="save-btn"
        >
          {{ isEdit ? '更新' : '创建' }}
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue'
import type { DiscordConfig, DiscordConfigRequest } from '@/stores/discordConfig'

interface Props {
  modelValue: boolean
  config?: DiscordConfig
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  save: [data: DiscordConfigRequest]
}>()

// 响应式数据
const dialog = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const form = ref()
const valid = ref(false)
const showToken = ref(false)

const isEdit = computed(() => !!props.config)

// 表单数据
const formData = ref<DiscordConfigRequest>({
  source_name: '',
  enabled: false,
  token: '',
  server_ids: [],
  channel_ids: [],
  author_ids: [],
  allowed_message_types: ['text']
})

// 消息类型选项
const messageTypeOptions = [
  { title: '文本消息', value: 'text' },
  { title: '嵌入消息', value: 'embed' },
  { title: '附件消息', value: 'attachment' },
  { title: '回复消息', value: 'reply' }
]

// 验证规则
const nameRules = [
  (v: string) => !!v || '配置名称不能为空',
  (v: string) => v.length <= 100 || '配置名称不能超过100个字符'
]

const tokenRules = [
  (v: string) => {
    // 编辑模式下，如果没有输入新token，则不验证
    if (isEdit.value && !v) {
      return true
    }
    return !!v || 'Discord Token不能为空'
  }
]

// 重置表单
const resetForm = () => {
  formData.value = {
    source_name: '',
    enabled: false,
    token: '',
    server_ids: [],
    channel_ids: [],
    author_ids: [],
    allowed_message_types: ['text']
  }
  if (form.value) {
    form.value.resetValidation()
  }
}

// 监听配置变化
watch(() => props.config, (newConfig) => {
  if (newConfig) {
    formData.value = {
      source_name: newConfig.source_name,
      enabled: newConfig.enabled,
      token: '', // 不显示已有token
      server_ids: [...newConfig.server_ids],
      channel_ids: [...newConfig.channel_ids],
      author_ids: [...newConfig.author_ids],
      allowed_message_types: [...newConfig.allowed_message_types]
    }
  } else {
    resetForm()
  }
}, { immediate: true })



// 关闭对话框
const closeDialog = () => {
  dialog.value = false
  resetForm()
}

// 保存配置
const saveConfig = async () => {
  const { valid: isValid } = await form.value.validate()
  if (isValid) {
    // 确保数组字段不为null
    const configData: any = {
      source_name: formData.value.source_name,
      enabled: formData.value.enabled,
      server_ids: formData.value.server_ids || [],
      channel_ids: formData.value.channel_ids || [],
      author_ids: formData.value.author_ids || [],
      allowed_message_types: formData.value.allowed_message_types.length > 0
        ? formData.value.allowed_message_types
        : ['text']
    }
    
    // 在编辑模式下，只有当用户输入了新token时才发送token字段
    if (!isEdit.value || formData.value.token) {
      configData.token = formData.value.token
    }
    
    emit('save', configData)
  }
}
</script>

<style scoped>
.v-expansion-panel-text {
  padding-top: 16px;
}
</style>
