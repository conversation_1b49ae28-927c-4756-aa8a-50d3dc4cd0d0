<template>
  <div class="recent-orders-table">
    <v-data-table
      :headers="headers"
      :items="orders"
      :loading="loading"
      item-value="id"
      class="elevation-0"
      density="compact"
      :items-per-page="itemsPerPage"
      hide-default-footer
    >
      <!-- 交易对列 -->
      <template v-slot:item.symbol="{ item }">
        <div class="d-flex align-center">
          <v-avatar size="24" class="mr-2">
            <v-img :src="getCryptoIcon(item.symbol)" :alt="item.symbol" />
          </v-avatar>
          <span class="font-weight-medium">{{ item.symbol }}</span>
        </div>
      </template>

      <!-- 方向列 -->
      <template v-slot:item.side="{ item }">
        <v-chip
          :color="item.side === 'buy' ? 'success' : 'error'"
          size="small"
          variant="flat"
        >
          <v-icon start size="16">
            {{ item.side === 'buy' ? 'mdi-trending-up' : 'mdi-trending-down' }}
          </v-icon>
          {{ item.side === 'buy' ? '做多' : '做空' }}
        </v-chip>
      </template>

      <!-- 数量列 -->
      <template v-slot:item.quantity="{ item }">
        <span class="font-mono">{{ formatQuantity(item.quantity) }}</span>
      </template>

      <!-- 开仓价列 -->
      <template v-slot:item.entry_price="{ item }">
        <span class="font-mono">${{ formatPrice(item.entry_price) }}</span>
      </template>

      <!-- 当前价列 -->
      <template v-slot:item.current_price="{ item }">
        <div class="d-flex align-center">
          <span class="font-mono mr-1">${{ formatPrice(item.current_price) }}</span>
          <v-icon 
            v-if="item.price_change !== undefined"
            size="16" 
            :color="item.price_change >= 0 ? 'success' : 'error'"
          >
            {{ item.price_change >= 0 ? 'mdi-trending-up' : 'mdi-trending-down' }}
          </v-icon>
        </div>
      </template>

      <!-- 浮动盈亏列 -->
      <template v-slot:item.pnl="{ item }">
        <div class="d-flex align-center">
          <span 
            class="font-mono font-weight-medium"
            :class="getPnLColor(item.pnl)"
          >
            {{ formatPnL(item.pnl) }}
          </span>
          <span 
            v-if="item.pnl_percentage !== undefined"
            class="text-caption ml-1"
            :class="getPnLColor(item.pnl)"
          >
            ({{ formatPercentage(item.pnl_percentage) }})
          </span>
        </div>
      </template>

      <!-- 状态列 -->
      <template v-slot:item.status="{ item }">
        <v-chip
          :color="getStatusColor(item.status)"
          size="small"
          variant="flat"
        >
          {{ getStatusText(item.status) }}
        </v-chip>
      </template>

      <!-- 创建时间列 -->
      <template v-slot:item.created_at="{ item }">
        <div class="text-caption">
          {{ formatDateTime(item.created_at) }}
        </div>
      </template>

      <!-- 操作列 -->
      <template v-slot:item.actions="{ item }">
        <div class="d-flex gap-1">
          <v-btn
            v-if="item.status === 'ACTIVE'"
            icon="mdi-close"
            size="small"
            variant="text"
            color="error"
            @click="closeOrder(item)"
            :loading="item.closing"
          >
            <v-icon>mdi-close</v-icon>
            <v-tooltip activator="parent" location="top">
              平仓
            </v-tooltip>
          </v-btn>
          
          <v-btn
            icon="mdi-information"
            size="small"
            variant="text"
            color="info"
            @click="showOrderDetails(item)"
          >
            <v-icon>mdi-information</v-icon>
            <v-tooltip activator="parent" location="top">
              详情
            </v-tooltip>
          </v-btn>
        </div>
      </template>

      <!-- 空状态 -->
      <template v-slot:no-data>
        <div class="text-center py-8">
          <v-icon size="64" color="grey-lighten-1" class="mb-4">
            mdi-format-list-bulleted-square
          </v-icon>
          <div class="text-h6 text-medium-emphasis">暂无订单数据</div>
          <div class="text-body-2 text-medium-emphasis">
            开始交易后，订单信息将在这里显示
          </div>
        </div>
      </template>
    </v-data-table>

    <!-- 订单详情对话框 -->
    <v-dialog v-model="showDetailsDialog" max-width="600">
      <v-card v-if="selectedOrder">
        <v-card-title class="d-flex align-center">
          <v-icon class="mr-2" color="primary">mdi-information</v-icon>
          订单详情
        </v-card-title>
        
        <v-card-text>
          <v-row>
            <v-col cols="6">
              <div class="text-subtitle-2 text-medium-emphasis">订单ID</div>
              <div class="font-mono text-body-2">{{ selectedOrder.id }}</div>
            </v-col>
            <v-col cols="6">
              <div class="text-subtitle-2 text-medium-emphasis">交易所订单ID</div>
              <div class="font-mono text-body-2">{{ selectedOrder.exchange_order_id || 'N/A' }}</div>
            </v-col>
            <v-col cols="6">
              <div class="text-subtitle-2 text-medium-emphasis">交易对</div>
              <div class="text-body-2">{{ selectedOrder.symbol }}</div>
            </v-col>
            <v-col cols="6">
              <div class="text-subtitle-2 text-medium-emphasis">方向</div>
              <v-chip 
                :color="selectedOrder.side === 'buy' ? 'success' : 'error'"
                size="small"
              >
                {{ selectedOrder.side === 'buy' ? '做多' : '做空' }}
              </v-chip>
            </v-col>
            <v-col cols="6">
              <div class="text-subtitle-2 text-medium-emphasis">数量</div>
              <div class="text-body-2">{{ formatQuantity(selectedOrder.quantity) }}</div>
            </v-col>
            <v-col cols="6">
              <div class="text-subtitle-2 text-medium-emphasis">开仓价</div>
              <div class="text-body-2">${{ formatPrice(selectedOrder.entry_price) }}</div>
            </v-col>
            <v-col cols="6">
              <div class="text-subtitle-2 text-medium-emphasis">当前价</div>
              <div class="text-body-2">${{ formatPrice(selectedOrder.current_price) }}</div>
            </v-col>
            <v-col cols="6">
              <div class="text-subtitle-2 text-medium-emphasis">浮动盈亏</div>
              <div class="text-body-2" :class="getPnLColor(selectedOrder.pnl)">
                {{ formatPnL(selectedOrder.pnl) }}
              </div>
            </v-col>
            <v-col cols="12">
              <div class="text-subtitle-2 text-medium-emphasis">创建时间</div>
              <div class="text-body-2">{{ formatDateTime(selectedOrder.created_at) }}</div>
            </v-col>
            <v-col cols="12" v-if="selectedOrder.agent_log">
              <div class="text-subtitle-2 text-medium-emphasis">Agent日志</div>
              <v-code class="text-body-2">
                {{ JSON.stringify(selectedOrder.agent_log, null, 2) }}
              </v-code>
            </v-col>
          </v-row>
        </v-card-text>
        
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn 
            variant="text" 
            @click="showDetailsDialog = false"
          >
            关闭
          </v-btn>
          <v-btn
            v-if="selectedOrder.status === 'ACTIVE'"
            color="error"
            variant="outlined"
            @click="closeOrder(selectedOrder)"
            :loading="selectedOrder.closing"
          >
            平仓
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useOrderStore } from '@/stores/order'
import { useUIStore } from '@/stores/ui'

// Props
const props = defineProps({
  orders: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  },
  itemsPerPage: {
    type: Number,
    default: 5
  }
})

// Stores
const orderStore = useOrderStore()
const uiStore = useUIStore()

// 响应式状态
const showDetailsDialog = ref(false)
const selectedOrder = ref(null)

// 表格头部配置
const headers = [
  { title: '交易对', key: 'symbol', sortable: true, width: '120px' },
  { title: '方向', key: 'side', sortable: true, width: '100px' },
  { title: '数量', key: 'quantity', sortable: true, width: '100px' },
  { title: '开仓价', key: 'entry_price', sortable: true, width: '100px' },
  { title: '当前价', key: 'current_price', sortable: true, width: '100px' },
  { title: '浮动盈亏', key: 'pnl', sortable: true, width: '120px' },
  { title: '状态', key: 'status', sortable: true, width: '100px' },
  { title: '创建时间', key: 'created_at', sortable: true, width: '120px' },
  { title: '操作', key: 'actions', sortable: false, width: '100px' }
]

// 方法
function getCryptoIcon(symbol) {
  // 提取基础货币符号
  const baseCurrency = symbol.split('/')[0].toLowerCase()
  return `https://cryptoicons.org/api/icon/${baseCurrency}/32`
}

function formatQuantity(quantity) {
  if (quantity === null || quantity === undefined) return '0'
  return parseFloat(quantity).toFixed(6)
}

function formatPrice(price) {
  if (price === null || price === undefined) return '0.00'
  return parseFloat(price).toFixed(2)
}

function formatPnL(pnl) {
  if (pnl === null || pnl === undefined) return '$0.00'
  const value = parseFloat(pnl)
  const sign = value >= 0 ? '+' : ''
  return `${sign}$${value.toFixed(2)}`
}

function formatPercentage(percentage) {
  if (percentage === null || percentage === undefined) return '0.00%'
  const value = parseFloat(percentage)
  const sign = value >= 0 ? '+' : ''
  return `${sign}${value.toFixed(2)}%`
}

function formatDateTime(dateTime) {
  if (!dateTime) return 'N/A'
  return new Date(dateTime).toLocaleString('zh-CN', {
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

function getPnLColor(pnl) {
  if (pnl === null || pnl === undefined) return ''
  const value = parseFloat(pnl)
  if (value > 0) return 'text-success'
  if (value < 0) return 'text-error'
  return ''
}

function getStatusColor(status) {
  const colorMap = {
    'ACTIVE': 'success',
    'CLOSED': 'info',
    'FAILED': 'error',
    'CANCELLED': 'warning',
    'PENDING': 'orange'
  }
  return colorMap[status] || 'grey'
}

function getStatusText(status) {
  const textMap = {
    'ACTIVE': '活跃',
    'CLOSED': '已关闭',
    'FAILED': '失败',
    'CANCELLED': '已取消',
    'PENDING': '待处理'
  }
  return textMap[status] || status
}

async function closeOrder(order) {
  const confirmed = await uiStore.showConfirmDialog({
    title: '确认平仓',
    message: `确定要平仓 ${order.symbol} ${order.side === 'buy' ? '多' : '空'}单吗？`,
    confirmText: '平仓',
    cancelText: '取消'
  })
  
  if (!confirmed) return
  
  try {
    order.closing = true
    await orderStore.closeOrder(order.id)
    uiStore.showSuccess('平仓指令已提交')
    showDetailsDialog.value = false
  } catch (error) {
    console.error('Failed to close order:', error)
    uiStore.showError('平仓失败: ' + error.message)
  } finally {
    order.closing = false
  }
}

function showOrderDetails(order) {
  selectedOrder.value = order
  showDetailsDialog.value = true
}
</script>

<style scoped>
.font-mono {
  font-family: 'Roboto Mono', monospace;
}

.gap-1 {
  gap: 4px;
}

:deep(.v-data-table__td) {
  padding: 8px 12px !important;
}

:deep(.v-data-table-header__content) {
  font-weight: 600;
}
</style>