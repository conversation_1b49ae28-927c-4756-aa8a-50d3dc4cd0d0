<template>
  <div v-if="loading" class="skeleton-container">
    <div v-for="n in rows" :key="n" class="skeleton-row">
      <div class="skeleton-item" :style="{ width: getRandomWidth() }"></div>
    </div>
  </div>
  <slot v-else />
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  loading: {
    type: Boolean,
    default: true
  },
  rows: {
    type: Number,
    default: 3
  }
})

function getRandomWidth() {
  return Math.floor(Math.random() * 40 + 60) + '%'
}
</script>

<style scoped>
.skeleton-container {
  padding: 16px;
}

.skeleton-row {
  margin-bottom: 12px;
}

.skeleton-item {
  height: 20px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: 4px;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Dark theme support */
.v-theme--dark .skeleton-item {
  background: linear-gradient(90deg, #424242 25%, #616161 50%, #424242 75%);
  background-size: 200% 100%;
}
</style>
