<template>
  <v-chip-group
    v-model="selectedLocal"
    column
    multiple
    @update:modelValue="updateSelection"
  >
    <v-chip 
      filter
      variant="elevated"
      :value="'PENDING'"
      color="warning"
    >
      等待触发
    </v-chip>
    <v-chip 
      filter
      variant="elevated"
      :value="'TRIGGERED'"
      color="success"
    >
      已触发
    </v-chip>
    <v-chip 
      filter
      variant="elevated"
      :value="'CANCELLED'"
      color="error"
    >
      已取消
    </v-chip>
    <v-chip 
      filter
      variant="elevated"
      :value="'EXPIRED'"
      color="grey"
    >
      已过期
    </v-chip>
  </v-chip-group>
</template>

<script setup>
import { ref, defineProps, defineEmits, watch } from 'vue';

const props = defineProps({
  selected: {
    type: Array,
    default: () => []
  }
});

const emit = defineEmits(['update:selected']);

// 本地状态
const selectedLocal = ref(props.selected);

// 监听外部更新
watch(() => props.selected, (newVal) => {
  selectedLocal.value = newVal;
}, { deep: true });

// 更新事件
function updateSelection(value) {
  emit('update:selected', value);
}
</script> 