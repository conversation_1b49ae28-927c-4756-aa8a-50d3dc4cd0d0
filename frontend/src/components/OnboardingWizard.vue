<template>
  <v-dialog 
    v-model="isOpen" 
    max-width="800" 
    persistent 
    no-click-animation
    class="onboarding-dialog"
  >
    <v-card class="onboarding-card">
      <!-- 进度指示器 -->
      <v-card-title class="px-6 pt-6 pb-4">
        <div class="d-flex align-center justify-space-between w-100">
          <div>
            <h2 class="text-h5 font-weight-bold">欢迎使用 Crypto Trader</h2>
            <p class="text-body-2 text-medium-emphasis mt-1">
              让我们快速设置您的交易环境
            </p>
          </div>
          
          <v-chip 
            color="primary" 
            variant="outlined" 
            size="small"
          >
            {{ currentStep + 1 }} / {{ totalSteps }}
          </v-chip>
        </div>
        
        <v-progress-linear
          :model-value="progress"
          color="primary"
          height="4"
          rounded
          class="mt-4"
        />
      </v-card-title>

      <v-card-text class="px-6 py-4">
        <v-window v-model="currentStep" class="onboarding-window">
          <!-- 步骤1: 欢迎 -->
          <v-window-item :value="0">
            <div class="text-center py-8">
              <v-avatar size="120" color="primary" class="mb-6">
                <v-icon size="60" color="white">mdi-robot</v-icon>
              </v-avatar>
              
              <h3 class="text-h4 font-weight-bold mb-4">
                智能加密货币交易助手
              </h3>
              
              <p class="text-h6 text-medium-emphasis mb-6 mx-auto" style="max-width: 500px;">
                通过自然语言指令进行交易，让AI帮您管理投资组合
              </p>
              
              <v-row class="mb-6">
                <v-col cols="12" md="4">
                  <div class="feature-item">
                    <v-icon size="48" color="success" class="mb-3">mdi-message-text</v-icon>
                    <h4 class="text-h6 mb-2">自然语言交易</h4>
                    <p class="text-body-2 text-medium-emphasis">
                      用简单的中文指令执行复杂的交易策略
                    </p>
                  </div>
                </v-col>
                
                <v-col cols="12" md="4">
                  <div class="feature-item">
                    <v-icon size="48" color="warning" class="mb-3">mdi-shield-check</v-icon>
                    <h4 class="text-h6 mb-2">智能风控</h4>
                    <p class="text-body-2 text-medium-emphasis">
                      自动风险评估和止损保护
                    </p>
                  </div>
                </v-col>
                
                <v-col cols="12" md="4">
                  <div class="feature-item">
                    <v-icon size="48" color="info" class="mb-3">mdi-chart-line</v-icon>
                    <h4 class="text-h6 mb-2">实时监控</h4>
                    <p class="text-body-2 text-medium-emphasis">
                      24/7监控市场和持仓状态
                    </p>
                  </div>
                </v-col>
              </v-row>
              
              <v-alert
                type="info"
                variant="tonal"
                class="text-start"
              >
                <strong>注意：</strong> 本系统仅供学习和测试使用，请谨慎进行实盘交易。
              </v-alert>
            </div>
          </v-window-item>

          <!-- 步骤2: 交易所配置 -->
          <v-window-item :value="1">
            <div class="py-4">
              <h3 class="text-h5 font-weight-bold mb-4">
                <v-icon class="mr-2" color="primary">mdi-bank</v-icon>
                配置交易所API
              </h3>
              
              <p class="text-body-1 text-medium-emphasis mb-6">
                请选择并配置您要使用的交易所。建议先使用测试环境进行验证。
              </p>
              
              <v-select
                v-model="selectedExchange"
                :items="availableExchanges"
                label="选择交易所"
                variant="outlined"
                density="comfortable"
                class="mb-4"
              >
                <template #prepend-item>
                  <v-list-item>
                    <v-list-item-title class="text-caption text-medium-emphasis">
                      支持的交易所
                    </v-list-item-title>
                  </v-list-item>
                  <v-divider></v-divider>
                </template>
              </v-select>
              
              <v-form v-if="selectedExchange" ref="exchangeForm">
                <v-row>
                  <v-col cols="12">
                    <v-text-field
                      v-model="exchangeConfig.api_key"
                      label="API Key"
                      :type="showApiKey ? 'text' : 'password'"
                      variant="outlined"
                      density="comfortable"
                      :rules="[v => !!v || 'API Key是必填项']"
                      :append-inner-icon="showApiKey ? 'mdi-eye' : 'mdi-eye-off'"
                      @click:append-inner="showApiKey = !showApiKey"
                    />
                  </v-col>
                  
                  <v-col cols="12">
                    <v-text-field
                      v-model="exchangeConfig.api_secret"
                      label="API Secret"
                      :type="showApiSecret ? 'text' : 'password'"
                      variant="outlined"
                      density="comfortable"
                      :rules="[v => !!v || 'API Secret是必填项']"
                      :append-inner-icon="showApiSecret ? 'mdi-eye' : 'mdi-eye-off'"
                      @click:append-inner="showApiSecret = !showApiSecret"
                    />
                  </v-col>
                  
                  <v-col cols="12" v-if="selectedExchange === 'OKX'">
                    <v-text-field
                      v-model="exchangeConfig.passphrase"
                      label="Passphrase"
                      variant="outlined"
                      density="comfortable"
                      :rules="[v => !!v || 'Passphrase是必填项']"
                    />
                  </v-col>
                </v-row>
                
                <v-switch
                  v-model="exchangeConfig.sandbox"
                  label="使用测试环境 (推荐)"
                  color="warning"
                  hide-details
                  class="mb-4"
                />
                
                <v-btn
                  color="primary"
                  variant="outlined"
                  block
                  @click="testConnection"
                  :loading="testing"
                  :disabled="!exchangeConfig.api_key || !exchangeConfig.api_secret"
                >
                  <v-icon start>mdi-connection</v-icon>
                  测试连接
                </v-btn>
                
                <v-alert
                  v-if="connectionStatus"
                  :type="connectionStatus.type"
                  variant="tonal"
                  class="mt-4"
                >
                  {{ connectionStatus.message }}
                </v-alert>
              </v-form>
              
              <v-alert
                type="warning"
                variant="tonal"
                class="mt-4"
              >
                <strong>安全提示：</strong> 
                <ul class="mt-2">
                  <li>请确保API Key只有交易权限，不要开启提现权限</li>
                  <li>建议使用IP白名单限制API访问</li>
                  <li>定期更换API密钥</li>
                </ul>
              </v-alert>
            </div>
          </v-window-item>

          <!-- 步骤3: 账户验证 -->
          <v-window-item :value="2">
            <div class="py-4">
              <h3 class="text-h5 font-weight-bold mb-4">
                <v-icon class="mr-2" color="success">mdi-account-check</v-icon>
                账户验证
              </h3>

              <p class="text-body-1 text-medium-emphasis mb-6">
                验证您的交易所连接并获取账户信息。
              </p>

              <v-card variant="outlined" class="mb-4">
                <v-card-text>
                  <div class="d-flex align-center justify-space-between">
                    <div>
                      <h4 class="text-h6">{{ selectedExchange }}</h4>
                      <p class="text-body-2 text-medium-emphasis">
                        {{ exchangeConfig.sandbox ? '测试环境' : '生产环境' }}
                      </p>
                    </div>
                    <v-chip
                      :color="accountStatus.connected ? 'success' : 'error'"
                      variant="flat"
                    >
                      {{ accountStatus.connected ? '已连接' : '未连接' }}
                    </v-chip>
                  </div>
                </v-card-text>
              </v-card>

              <v-btn
                v-if="!accountStatus.connected"
                color="primary"
                variant="flat"
                block
                @click="verifyAccount"
                :loading="verifying"
                class="mb-4"
              >
                <v-icon start>mdi-account-check</v-icon>
                验证账户
              </v-btn>

              <div v-if="accountStatus.connected" class="mb-4">
                <v-alert type="success" variant="tonal" class="mb-4">
                  <strong>账户验证成功！</strong>
                </v-alert>

                <v-card variant="outlined">
                  <v-card-title class="text-h6">账户信息</v-card-title>
                  <v-card-text>
                    <v-row>
                      <v-col cols="6">
                        <div class="text-caption text-medium-emphasis">账户类型</div>
                        <div class="text-body-1">{{ accountInfo.accountType || 'Spot' }}</div>
                      </v-col>
                      <v-col cols="6">
                        <div class="text-caption text-medium-emphasis">权限</div>
                        <div class="text-body-1">
                          <v-chip
                            v-for="permission in accountInfo.permissions"
                            :key="permission"
                            size="small"
                            class="mr-1"
                          >
                            {{ permission }}
                          </v-chip>
                        </div>
                      </v-col>
                    </v-row>

                    <v-divider class="my-3"></v-divider>

                    <div class="text-caption text-medium-emphasis mb-2">主要余额</div>
                    <v-row>
                      <v-col
                        v-for="balance in accountInfo.balances"
                        :key="balance.asset"
                        cols="6"
                        md="4"
                      >
                        <div class="text-body-2">
                          <strong>{{ balance.asset }}:</strong> {{ balance.free }}
                        </div>
                      </v-col>
                    </v-row>
                  </v-card-text>
                </v-card>
              </div>

              <v-alert
                v-if="accountStatus.error"
                type="error"
                variant="tonal"
                class="mt-4"
              >
                {{ accountStatus.error }}
              </v-alert>
            </div>
          </v-window-item>

          <!-- 步骤4: 风控设置 -->
          <v-window-item :value="3">
            <div class="py-4">
              <h3 class="text-h5 font-weight-bold mb-4">
                <v-icon class="mr-2" color="error">mdi-shield-alert</v-icon>
                风险控制设置
              </h3>

              <p class="text-body-1 text-medium-emphasis mb-6">
                设置合理的风控参数可以有效保护您的资金安全。
              </p>
              
              <v-form ref="riskForm">
                <v-row>
                  <v-col cols="12" md="6">
                    <v-text-field
                      v-model.number="riskSettings.max_position_size_usd"
                      label="单笔最大仓位 (USD)"
                      type="number"
                      variant="outlined"
                      density="comfortable"
                      prefix="$"
                      :rules="[v => v > 0 || '必须大于0']"
                      hint="建议不超过总资金的10%"
                    />
                  </v-col>
                  
                  <v-col cols="12" md="6">
                    <v-text-field
                      v-model.number="riskSettings.max_daily_loss_usd"
                      label="日最大亏损 (USD)"
                      type="number"
                      variant="outlined"
                      density="comfortable"
                      prefix="$"
                      :rules="[v => v > 0 || '必须大于0']"
                      hint="建议不超过总资金的5%"
                    />
                  </v-col>
                  
                  <v-col cols="12" md="6">
                    <v-text-field
                      v-model.number="riskSettings.max_open_positions"
                      label="最大同时持仓数"
                      type="number"
                      variant="outlined"
                      density="comfortable"
                      :rules="[v => v > 0 || '必须大于0']"
                      hint="建议3-5个"
                    />
                  </v-col>
                  
                  <v-col cols="12" md="6">
                    <v-text-field
                      v-model.number="riskSettings.default_stop_loss_pct"
                      label="默认止损百分比"
                      type="number"
                      variant="outlined"
                      density="comfortable"
                      suffix="%"
                      :rules="[v => v > 0 && v <= 50 || '必须在0-50%之间']"
                      hint="建议3-10%"
                    />
                  </v-col>
                </v-row>
                
                <v-divider class="my-6"></v-divider>
                
                <h4 class="text-h6 mb-4">高级设置</h4>
                
                <v-row>
                  <v-col cols="12" md="6">
                    <v-switch
                      v-model="riskSettings.enable_stop_loss"
                      label="启用自动止损"
                      color="error"
                      hide-details
                    />
                  </v-col>
                  
                  <v-col cols="12" md="6">
                    <v-switch
                      v-model="riskSettings.enable_take_profit"
                      label="启用自动止盈"
                      color="success"
                      hide-details
                    />
                  </v-col>
                </v-row>
              </v-form>
              
              <v-alert
                type="info"
                variant="tonal"
                class="mt-4"
              >
                <strong>风控建议：</strong> 
                <ul class="mt-2">
                  <li>新手建议从小额资金开始</li>
                  <li>设置严格的止损线</li>
                  <li>分散投资，不要把鸡蛋放在一个篮子里</li>
                  <li>定期回顾和调整风控参数</li>
                </ul>
              </v-alert>
            </div>
          </v-window-item>

          <!-- 步骤5: 用户偏好设置 -->
          <v-window-item :value="4">
            <div class="py-4">
              <h3 class="text-h5 font-weight-bold mb-4">
                <v-icon class="mr-2" color="info">mdi-account-cog</v-icon>
                个人偏好设置
              </h3>

              <p class="text-body-1 text-medium-emphasis mb-6">
                自定义您的使用体验和通知偏好。
              </p>

              <v-form ref="preferencesForm">
                <v-row>
                  <v-col cols="12" md="6">
                    <v-select
                      v-model="userPreferences.language"
                      :items="[
                        { title: '简体中文', value: 'zh-CN' },
                        { title: 'English', value: 'en-US' }
                      ]"
                      label="界面语言"
                      variant="outlined"
                      density="comfortable"
                    />
                  </v-col>

                  <v-col cols="12" md="6">
                    <v-select
                      v-model="userPreferences.timezone"
                      :items="[
                        { title: '北京时间 (UTC+8)', value: 'Asia/Shanghai' },
                        { title: '东京时间 (UTC+9)', value: 'Asia/Tokyo' },
                        { title: '纽约时间 (UTC-5)', value: 'America/New_York' },
                        { title: '伦敦时间 (UTC+0)', value: 'Europe/London' }
                      ]"
                      label="时区设置"
                      variant="outlined"
                      density="comfortable"
                    />
                  </v-col>
                </v-row>

                <v-divider class="my-6"></v-divider>

                <h4 class="text-h6 mb-4">通知设置</h4>

                <v-row>
                  <v-col cols="12" md="4">
                    <v-switch
                      v-model="userPreferences.notifications.email"
                      label="邮件通知"
                      color="primary"
                      hide-details
                    />
                    <p class="text-caption text-medium-emphasis mt-1">
                      重要交易事件和系统通知
                    </p>
                  </v-col>

                  <v-col cols="12" md="4">
                    <v-switch
                      v-model="userPreferences.notifications.push"
                      label="推送通知"
                      color="primary"
                      hide-details
                    />
                    <p class="text-caption text-medium-emphasis mt-1">
                      浏览器推送通知
                    </p>
                  </v-col>

                  <v-col cols="12" md="4">
                    <v-switch
                      v-model="userPreferences.notifications.sms"
                      label="短信通知"
                      color="primary"
                      hide-details
                    />
                    <p class="text-caption text-medium-emphasis mt-1">
                      紧急情况短信提醒
                    </p>
                  </v-col>
                </v-row>

                <v-divider class="my-6"></v-divider>

                <h4 class="text-h6 mb-4">交易偏好</h4>

                <v-row>
                  <v-col cols="12" md="6">
                    <v-switch
                      v-model="userPreferences.trading.confirmBeforeExecution"
                      label="执行前确认"
                      color="warning"
                      hide-details
                    />
                    <p class="text-caption text-medium-emphasis mt-1">
                      每次交易前都需要手动确认
                    </p>
                  </v-col>

                  <v-col cols="12" md="6">
                    <v-switch
                      v-model="userPreferences.trading.autoStopLoss"
                      label="自动止损"
                      color="error"
                      hide-details
                    />
                    <p class="text-caption text-medium-emphasis mt-1">
                      自动设置止损订单
                    </p>
                  </v-col>

                  <v-col cols="12">
                    <v-select
                      v-model="userPreferences.trading.riskLevel"
                      :items="[
                        { title: '保守型 - 低风险低收益', value: 'conservative' },
                        { title: '平衡型 - 中等风险收益', value: 'balanced' },
                        { title: '激进型 - 高风险高收益', value: 'aggressive' }
                      ]"
                      label="风险偏好"
                      variant="outlined"
                      density="comfortable"
                    />
                  </v-col>
                </v-row>
              </v-form>
            </div>
          </v-window-item>

          <!-- 步骤6: Discord集成 (可选) -->
          <v-window-item :value="5">
            <div class="py-4">
              <h3 class="text-h5 font-weight-bold mb-4">
                <v-icon class="mr-2" color="deep-purple">mdi-discord</v-icon>
                Discord信号集成 (可选)
              </h3>

              <p class="text-body-1 text-medium-emphasis mb-6">
                连接Discord频道以自动接收和处理交易信号。
              </p>

              <v-switch
                v-model="userPreferences.discord.enabled"
                label="启用Discord集成"
                color="deep-purple"
                hide-details
                class="mb-4"
              />

              <div v-if="userPreferences.discord.enabled">
                <v-alert type="info" variant="tonal" class="mb-4">
                  <strong>设置说明：</strong>
                  <ol class="mt-2">
                    <li>在Discord中创建或加入交易信号频道</li>
                    <li>获取频道ID（右键频道 → 复制ID）</li>
                    <li>在下方添加要监控的频道</li>
                  </ol>
                </v-alert>

                <v-card variant="outlined" class="mb-4">
                  <v-card-title class="text-h6">监控频道</v-card-title>
                  <v-card-text>
                    <div v-if="userPreferences.discord.channels.length === 0" class="text-center py-4">
                      <v-icon size="48" color="grey" class="mb-2">mdi-discord</v-icon>
                      <p class="text-body-2 text-medium-emphasis">暂无监控频道</p>
                    </div>

                    <v-list v-else>
                      <v-list-item
                        v-for="(channel, index) in userPreferences.discord.channels"
                        :key="index"
                      >
                        <v-list-item-title>{{ channel.name }}</v-list-item-title>
                        <v-list-item-subtitle>ID: {{ channel.id }}</v-list-item-subtitle>

                        <template #append>
                          <v-btn
                            icon="mdi-delete"
                            size="small"
                            variant="text"
                            color="error"
                            @click="removeDiscordChannel(index)"
                          />
                        </template>
                      </v-list-item>
                    </v-list>

                    <v-btn
                      color="deep-purple"
                      variant="outlined"
                      block
                      @click="addDiscordChannel"
                      class="mt-2"
                    >
                      <v-icon start>mdi-plus</v-icon>
                      添加频道
                    </v-btn>
                  </v-card-text>
                </v-card>

                <v-alert type="warning" variant="tonal">
                  <strong>注意：</strong> Discord集成需要管理员配置机器人权限。如需帮助，请联系系统管理员。
                </v-alert>
              </div>

              <div v-else>
                <v-alert type="info" variant="tonal">
                  您可以稍后在设置页面中启用Discord集成功能。
                </v-alert>
              </div>
            </div>
          </v-window-item>

          <!-- 步骤7: 完成设置 -->
          <v-window-item :value="6">
            <div class="text-center py-8">
              <v-icon size="120" color="success" class="mb-6">mdi-check-circle</v-icon>
              
              <h3 class="text-h4 font-weight-bold mb-4">设置完成！</h3>
              
              <p class="text-h6 text-medium-emphasis mb-6">
                您的交易环境已经配置完成，现在可以开始使用了。
              </p>
              
              <v-card variant="outlined" class="mb-6">
                <v-card-title class="text-start">
                  <v-icon class="mr-2" color="primary">mdi-information</v-icon>
                  配置摘要
                </v-card-title>
                <v-card-text class="text-start">
                  <v-row>
                    <v-col cols="6">
                      <strong>交易所:</strong>
                    </v-col>
                    <v-col cols="6">
                      {{ selectedExchange || '未配置' }}
                    </v-col>
                    
                    <v-col cols="6">
                      <strong>测试环境:</strong>
                    </v-col>
                    <v-col cols="6">
                      {{ exchangeConfig.sandbox ? '是' : '否' }}
                    </v-col>
                    
                    <v-col cols="6">
                      <strong>最大仓位:</strong>
                    </v-col>
                    <v-col cols="6">
                      ${{ riskSettings.max_position_size_usd }}
                    </v-col>
                    
                    <v-col cols="6">
                      <strong>日最大亏损:</strong>
                    </v-col>
                    <v-col cols="6">
                      ${{ riskSettings.max_daily_loss_usd }}
                    </v-col>
                  </v-row>
                </v-card-text>
              </v-card>
              
              <div class="mb-6">
                <h4 class="text-h6 mb-3">快速开始指南</h4>
                <v-list class="text-start">
                  <v-list-item>
                    <template #prepend>
                      <v-icon color="primary">mdi-numeric-1-circle</v-icon>
                    </template>
                    <v-list-item-title>在仪表盘输入交易指令，如"买入100美元的比特币"</v-list-item-title>
                  </v-list-item>
                  
                  <v-list-item>
                    <template #prepend>
                      <v-icon color="primary">mdi-numeric-2-circle</v-icon>
                    </template>
                    <v-list-item-title>查看AI解析的交易计划并确认执行</v-list-item-title>
                  </v-list-item>
                  
                  <v-list-item>
                    <template #prepend>
                      <v-icon color="primary">mdi-numeric-3-circle</v-icon>
                    </template>
                    <v-list-item-title>在订单页面监控交易状态和盈亏</v-list-item-title>
                  </v-list-item>
                </v-list>
              </div>
              
              <v-alert
                type="success"
                variant="tonal"
              >
                <strong>恭喜！</strong> 您已经成功完成了初始设置。记住，投资有风险，请谨慎交易。
              </v-alert>
            </div>
          </v-window-item>
        </v-window>
      </v-card-text>

      <v-card-actions class="px-6 pb-6">
        <v-btn
          v-if="currentStep > 0"
          variant="outlined"
          @click="previousStep"
          :disabled="loading"
        >
          <v-icon start>mdi-chevron-left</v-icon>
          上一步
        </v-btn>
        
        <v-spacer></v-spacer>
        
        <v-btn
          v-if="currentStep < totalSteps - 1"
          color="primary"
          variant="flat"
          @click="nextStep"
          :disabled="!canProceed"
          :loading="loading"
        >
          下一步
          <v-icon end>mdi-chevron-right</v-icon>
        </v-btn>
        
        <v-btn
          v-else
          color="success"
          variant="flat"
          @click="completeOnboarding"
          :loading="loading"
        >
          开始使用
          <v-icon end>mdi-rocket-launch</v-icon>
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { useConfigStore } from '@/stores/config'
import { useUIStore } from '@/stores/ui'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'completed'])

// Stores
const authStore = useAuthStore()
const configStore = useConfigStore()
const uiStore = useUIStore()

// 响应式状态
const currentStep = ref(0)
const totalSteps = 7  // 增加到7个步骤
const loading = ref(false)
const testing = ref(false)
const verifying = ref(false)
const showApiKey = ref(false)
const showApiSecret = ref(false)

// 表单引用
const exchangeForm = ref(null)
const riskForm = ref(null)
const preferencesForm = ref(null)

// 配置数据
const selectedExchange = ref('')
const exchangeConfig = reactive({
  api_key: '',
  api_secret: '',
  passphrase: '',
  sandbox: true
})

const riskSettings = reactive({
  max_position_size_usd: 100,
  max_daily_loss_usd: 50,
  max_open_positions: 3,
  default_stop_loss_pct: 5,
  enable_stop_loss: true,
  enable_take_profit: true,
  max_total_exposure_usd: 500,
  max_volatility_threshold: 0.05,
  max_trades_per_hour: 10
})

const connectionStatus = ref(null)

// 账户状态
const accountStatus = reactive({
  connected: false,
  error: null
})

const accountInfo = reactive({
  accountType: '',
  permissions: [],
  balances: []
})

// 用户偏好设置
const userPreferences = reactive({
  language: 'zh-CN',
  timezone: 'Asia/Shanghai',
  notifications: {
    email: true,
    push: true,
    sms: false
  },
  trading: {
    confirmBeforeExecution: true,
    autoStopLoss: true,
    riskLevel: 'conservative'
  },
  discord: {
    enabled: false,
    channels: []
  }
})

// 选项数据
const availableExchanges = [
  { title: 'Binance', value: 'Binance' },
  { title: 'OKX', value: 'OKX' },
  { title: 'Bybit', value: 'Bybit' },
  { title: 'Bitget', value: 'Bitget' },
  { title: 'Gate.io', value: 'Gate.io' }
]

// 计算属性
const isOpen = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const progress = computed(() => {
  return ((currentStep.value + 1) / totalSteps) * 100
})

const canProceed = computed(() => {
  switch (currentStep.value) {
    case 0: // 欢迎页面
      return true
    case 1: // 交易所配置
      return selectedExchange.value &&
             exchangeConfig.api_key &&
             exchangeConfig.api_secret &&
             connectionStatus.value?.type === 'success'
    case 2: // 账户验证
      return accountStatus.connected
    case 3: // 风控设置
      return riskSettings.max_position_size_usd > 0 &&
             riskSettings.max_daily_loss_usd > 0 &&
             riskSettings.max_open_positions > 0 &&
             riskSettings.default_stop_loss_pct > 0
    case 4: // 用户偏好设置
      return true // 偏好设置都是可选的
    case 5: // Discord集成
      return true // Discord集成是可选的
    case 6: // 完成设置
      return true
    default:
      return false
  }
})

// 方法
function nextStep() {
  if (currentStep.value < totalSteps - 1) {
    currentStep.value++
  }
}

function previousStep() {
  if (currentStep.value > 0) {
    currentStep.value--
  }
}

async function testConnection() {
  if (!selectedExchange.value || !exchangeConfig.api_key || !exchangeConfig.api_secret) {
    return
  }

  testing.value = true
  connectionStatus.value = null

  try {
    // 模拟API连接测试
    await new Promise(resolve => setTimeout(resolve, 2000))

    // 这里应该调用实际的API测试
    const success = Math.random() > 0.2 // 模拟80%成功率

    if (success) {
      connectionStatus.value = {
        type: 'success',
        message: `${selectedExchange.value} 连接测试成功！API配置正确。`
      }
    } else {
      connectionStatus.value = {
        type: 'error',
        message: `${selectedExchange.value} 连接测试失败，请检查API配置。`
      }
    }
  } catch (error) {
    connectionStatus.value = {
      type: 'error',
      message: `连接测试失败: ${error.message}`
    }
  } finally {
    testing.value = false
  }
}

async function verifyAccount() {
  verifying.value = true
  accountStatus.error = null

  try {
    // 首先保存交易所配置
    const exchangeData = {
      exchange_name: selectedExchange.value,
      api_key: exchangeConfig.api_key,
      api_secret: exchangeConfig.api_secret,
      passphrase: exchangeConfig.passphrase,
      sandbox: exchangeConfig.sandbox,
      is_active: true
    }

    // 调用API保存配置并测试连接
    const response = await configStore.createExchangeConfig(exchangeData)

    if (response.success) {
      // 测试连接并获取账户信息
      const testResponse = await configStore.testExchangeConnection(response.data.id)

      if (testResponse.success) {
        accountStatus.connected = true

        // 获取账户信息
        const accountData = testResponse.data
        accountInfo.accountType = accountData.accountType || 'Spot'
        accountInfo.permissions = accountData.permissions || ['SPOT']
        accountInfo.balances = (accountData.balances || [])
          .filter(b => parseFloat(b.free) > 0)
          .slice(0, 6) // 只显示前6个有余额的币种

        uiStore.showSuccess('账户验证成功！')
      } else {
        throw new Error(testResponse.message || '账户验证失败')
      }
    } else {
      throw new Error(response.message || '保存配置失败')
    }
  } catch (error) {
    accountStatus.error = error.message || '账户验证失败，请检查API配置'
    accountStatus.connected = false
  } finally {
    verifying.value = false
  }
}

function addDiscordChannel() {
  // 这里应该打开一个对话框来添加Discord频道
  const channelId = prompt('请输入Discord频道ID:')
  const channelName = prompt('请输入频道名称:')

  if (channelId && channelName) {
    userPreferences.discord.channels.push({
      id: channelId,
      name: channelName,
      enabled: true
    })
  }
}

function removeDiscordChannel(index) {
  userPreferences.discord.channels.splice(index, 1)
}

async function completeOnboarding() {
  loading.value = true

  try {
    // 保存完整配置
    const configs = {
      exchanges: [{
        name: selectedExchange.value,
        enabled: true,
        ...exchangeConfig
      }],
      risk: {
        ...riskSettings,
        // 添加新的风控参数
        max_total_exposure_usd: riskSettings.max_total_exposure_usd || 500,
        max_volatility_threshold: riskSettings.max_volatility_threshold || 0.05,
        max_trades_per_hour: riskSettings.max_trades_per_hour || 10
      },
      preferences: userPreferences,
      signals: [],
      system: {
        auto_trading_enabled: true,
        max_concurrent_trades: riskSettings.max_open_positions,
        order_timeout_seconds: 30,
        log_level: 'INFO',
        language: userPreferences.language,
        timezone: userPreferences.timezone
      },
      discord: {
        enabled: userPreferences.discord.enabled,
        channels: userPreferences.discord.channels
      }
    }

    // 分别保存不同类型的配置
    await configStore.saveConfigs(configs)

    // 如果启用了Discord，保存Discord配置
    if (userPreferences.discord.enabled && userPreferences.discord.channels.length > 0) {
      try {
        await configStore.saveDiscordConfig({
          enabled: true,
          channels: userPreferences.discord.channels
        })
      } catch (error) {
      console.warn('Failed to save Discord config:', error)
      // Discord配置失败不应该阻止整个设置流程
    }
    }

    // 保存用户偏好设置
    await authStore.updateUserPreferences(userPreferences)

    // 标记用户已完成首次设置
    await authStore.markFirstTimeSetupComplete()

    // 关闭向导
    isOpen.value = false

    // 发出完成事件
    emit('completed')

    uiStore.showSuccess('欢迎使用 Crypto Trader！所有设置已保存。')
  } catch (error) {
    uiStore.showError('设置保存失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

// 监听选择的交易所变化，重置连接状态
watch(selectedExchange, () => {
  connectionStatus.value = null
  exchangeConfig.api_key = ''
  exchangeConfig.api_secret = ''
  exchangeConfig.passphrase = ''
})

// 监听API配置变化，重置连接状态
watch([() => exchangeConfig.api_key, () => exchangeConfig.api_secret], () => {
  connectionStatus.value = null
})

// 自动检测新用户并显示向导
onMounted(async () => {
  // 检查是否是已认证的用户并且是首次登录
  if (authStore.isAuthenticated) {
    try {
      // 先尝试初始化用户信息以确保我们有最新数据
      await authStore.initializeUser();
      
      if (authStore.isFirstTimeLogin) {
        isOpen.value = true;
      }
    } catch (error) {
      // 静默处理错误，不影响用户体验
    }
  }
})
</script>

<style scoped>
.onboarding-dialog {
  z-index: 2000;
}

.onboarding-card {
  border-radius: 16px;
}

.onboarding-window {
  min-height: 400px;
}

.feature-item {
  text-align: center;
  padding: 16px;
}

:deep(.v-window__container) {
  height: auto;
}

:deep(.v-card-title) {
  word-break: normal;
}

:deep(.v-list-item) {
  padding-left: 0;
  padding-right: 0;
}

:deep(.v-list-item__prepend) {
  margin-right: 16px;
}

/* 添加一个关闭按钮的样式 */
.close-button {
  position: absolute;
  top: 12px;
  right: 12px;
  z-index: 10;
}
</style>