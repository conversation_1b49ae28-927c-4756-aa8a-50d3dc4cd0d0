<template>
  <div v-if="hasError" class="error-boundary">
    <v-container class="fill-height">
      <v-row class="fill-height" align="center" justify="center">
        <v-col cols="12" sm="8" md="6">
          <v-card elevation="4" class="text-center pa-6">
            <v-icon size="64" color="error" class="mb-4">mdi-alert-circle</v-icon>
            <h2 class="text-h5 mb-4">页面加载出错</h2>
            <p class="text-body-1 mb-4">
              抱歉，页面遇到了一些问题。请尝试刷新页面或联系技术支持。
            </p>
            <div class="d-flex gap-3 justify-center">
              <v-btn 
                color="primary" 
                variant="flat"
                @click="handleRefresh"
                prepend-icon="mdi-refresh"
              >
                刷新页面
              </v-btn>
              <v-btn 
                color="secondary" 
                variant="outlined"
                @click="handleGoHome"
                prepend-icon="mdi-home"
              >
                返回首页
              </v-btn>
            </div>
            
            <!-- 开发模式下显示错误详情 -->
            <v-expansion-panels v-if="isDev && errorInfo" class="mt-4">
              <v-expansion-panel>
                <v-expansion-panel-title>
                  <v-icon class="mr-2">mdi-bug</v-icon>
                  错误详情 (开发模式)
                </v-expansion-panel-title>
                <v-expansion-panel-text>
                  <pre class="text-left text-caption">{{ errorInfo }}</pre>
                </v-expansion-panel-text>
              </v-expansion-panel>
            </v-expansion-panels>
          </v-card>
        </v-col>
      </v-row>
    </v-container>
  </div>
  <div v-else>
    <slot />
  </div>
</template>

<script setup>
import { ref, onErrorCaptured, computed } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

// 状态
const hasError = ref(false)
const errorInfo = ref('')

// 计算属性
const isDev = computed(() => import.meta.env.DEV)

// 错误捕获
onErrorCaptured((error, instance, info) => {
  console.error('Vue Error Boundary caught an error:', error)
  console.error('Component instance:', instance)
  console.error('Error info:', info)
  
  hasError.value = true
  errorInfo.value = `
错误: ${error.message}
组件: ${instance?.$options.name || 'Unknown'}
错误信息: ${info}
堆栈: ${error.stack}
  `.trim()
  
  // 阻止错误继续传播
  return false
})

// 方法
function handleRefresh() {
  window.location.reload()
}

function handleGoHome() {
  hasError.value = false
  router.push('/')
}

// 重置错误状态的方法（供父组件调用）
function resetError() {
  hasError.value = false
  errorInfo.value = ''
}

// 暴露方法给父组件
defineExpose({
  resetError
})
</script>

<style scoped>
.error-boundary {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

pre {
  background: rgba(0, 0, 0, 0.05);
  padding: 12px;
  border-radius: 4px;
  overflow-x: auto;
  max-height: 200px;
}
</style>