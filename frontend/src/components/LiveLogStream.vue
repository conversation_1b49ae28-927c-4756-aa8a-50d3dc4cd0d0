<template>
  <div class="live-log-stream">
    <div class="log-header d-flex align-center justify-space-between mb-3">
      <div class="d-flex align-center">
        <v-icon color="primary" class="mr-2">mdi-console-line</v-icon>
        <span class="text-subtitle-1 font-weight-medium">实时日志</span>
        <v-chip 
          v-if="logs.length > 0" 
          size="small" 
          color="primary" 
          variant="tonal" 
          class="ml-2"
        >
          {{ logs.length }}
        </v-chip>
      </div>
      
      <div class="d-flex align-center gap-2">
        <v-btn
          icon="mdi-pause"
          size="small"
          variant="text"
          :color="isPaused ? 'warning' : 'default'"
          @click="togglePause"
        >
          <v-icon>{{ isPaused ? 'mdi-play' : 'mdi-pause' }}</v-icon>
          <v-tooltip activator="parent" location="top">
            {{ isPaused ? '恢复' : '暂停' }}
          </v-tooltip>
        </v-btn>
        
        <v-btn
          icon="mdi-delete-sweep"
          size="small"
          variant="text"
          @click="clearLogs"
        >
          <v-icon>mdi-delete-sweep</v-icon>
          <v-tooltip activator="parent" location="top">
            清空日志
          </v-tooltip>
        </v-btn>
        
        <v-btn
          icon="mdi-download"
          size="small"
          variant="text"
          @click="downloadLogs"
        >
          <v-icon>mdi-download</v-icon>
          <v-tooltip activator="parent" location="top">
            下载日志
          </v-tooltip>
        </v-btn>
      </div>
    </div>

    <v-card 
      class="log-container" 
      variant="outlined"
      :style="{ height: containerHeight + 'px' }"
    >
      <div 
        ref="logContent"
        class="log-content"
        :class="{ 'paused': isPaused }"
      >
        <div 
          v-for="(log, index) in displayLogs" 
          :key="log.id || index"
          class="log-entry"
          :class="getLogClass(log)"
        >
          <div class="log-timestamp">
            {{ formatTimestamp(log.timestamp) }}
          </div>
          <div class="log-level">
            <v-chip 
              :color="getLogLevelColor(log.level)"
              size="x-small"
              variant="flat"
            >
              {{ log.level }}
            </v-chip>
          </div>
          <div class="log-message">
            {{ log.message }}
          </div>
          <div v-if="log.details" class="log-details">
            <v-expansion-panels variant="accordion" density="compact">
              <v-expansion-panel>
                <v-expansion-panel-title class="text-caption">
                  详细信息
                </v-expansion-panel-title>
                <v-expansion-panel-text>
                  <pre class="log-details-content">{{ formatDetails(log.details) }}</pre>
                </v-expansion-panel-text>
              </v-expansion-panel>
            </v-expansion-panels>
          </div>
        </div>
        
        <div v-if="displayLogs.length === 0" class="empty-state">
          <v-icon size="48" color="grey-lighten-1">mdi-console-line</v-icon>
          <div class="text-body-2 text-medium-emphasis mt-2">
            暂无日志数据
          </div>
        </div>
      </div>
    </v-card>

    <!-- 日志级别过滤器 -->
    <div class="log-filters mt-3">
      <v-chip-group 
        v-model="selectedLevels" 
        multiple 
        selected-class="text-primary"
      >
        <v-chip 
          v-for="level in logLevels" 
          :key="level.value"
          :value="level.value"
          size="small"
          variant="outlined"
          filter
        >
          <v-icon :color="level.color" size="16" class="mr-1">
            {{ level.icon }}
          </v-icon>
          {{ level.label }}
        </v-chip>
      </v-chip-group>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, nextTick, onMounted, onUnmounted } from 'vue'

const props = defineProps({
  logs: {
    type: Array,
    default: () => []
  },
  maxHeight: {
    type: Number,
    default: 400
  },
  autoScroll: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['clear-logs'])

// 响应式数据
const logContent = ref(null)
const isPaused = ref(false)
const selectedLevels = ref(['INFO', 'WARN', 'ERROR', 'DEBUG'])
const containerHeight = ref(props.maxHeight)

// 日志级别配置
const logLevels = [
  { value: 'DEBUG', label: '调试', color: 'grey', icon: 'mdi-bug' },
  { value: 'INFO', label: '信息', color: 'blue', icon: 'mdi-information' },
  { value: 'WARN', label: '警告', color: 'orange', icon: 'mdi-alert' },
  { value: 'ERROR', label: '错误', color: 'red', icon: 'mdi-alert-circle' }
]

// 计算属性
const displayLogs = computed(() => {
  if (!props.logs) return []
  
  return props.logs
    .filter(log => selectedLevels.value.includes(log.level))
    .slice(-1000) // 限制显示最近1000条日志
})

// 方法
function getLogClass(log) {
  return {
    [`log-${log.level.toLowerCase()}`]: true,
    'log-new': log.isNew
  }
}

function getLogLevelColor(level) {
  const levelConfig = logLevels.find(l => l.value === level)
  return levelConfig ? levelConfig.color : 'grey'
}

function formatTimestamp(timestamp) {
  if (!timestamp) return ''
  
  const date = new Date(timestamp)
  return date.toLocaleTimeString('zh-CN', {
    hour12: false,
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    fractionalSecondDigits: 3
  })
}

function formatDetails(details) {
  if (typeof details === 'string') return details
  return JSON.stringify(details, null, 2)
}

function togglePause() {
  isPaused.value = !isPaused.value
}

function clearLogs() {
  emit('clear-logs')
}

function downloadLogs() {
  const logText = displayLogs.value
    .map(log => `[${formatTimestamp(log.timestamp)}] ${log.level}: ${log.message}`)
    .join('\n')
  
  const blob = new Blob([logText], { type: 'text/plain' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `crypto-trader-logs-${new Date().toISOString().slice(0, 19)}.txt`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
}

function scrollToBottom() {
  if (!props.autoScroll || isPaused.value || !logContent.value) return
  
  nextTick(() => {
    const container = logContent.value
    container.scrollTop = container.scrollHeight
  })
}

// 监听日志变化，自动滚动到底部
watch(() => props.logs, () => {
  scrollToBottom()
}, { deep: true })

// 监听暂停状态变化
watch(isPaused, (newVal) => {
  if (!newVal) {
    scrollToBottom()
  }
})

onMounted(() => {
  scrollToBottom()
})
</script>

<style scoped>
.live-log-stream {
  width: 100%;
}

.log-container {
  position: relative;
  overflow: hidden;
}

.log-content {
  height: 100%;
  overflow-y: auto;
  padding: 12px;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.4;
  background-color: rgb(var(--v-theme-surface-variant));
}

.log-content.paused {
  border-left: 4px solid rgb(var(--v-theme-warning));
}

.log-entry {
  display: grid;
  grid-template-columns: auto auto 1fr;
  gap: 8px;
  padding: 4px 0;
  border-bottom: 1px solid rgba(var(--v-border-color), 0.12);
  align-items: start;
}

.log-entry:last-child {
  border-bottom: none;
}

.log-entry.log-new {
  animation: highlight 1s ease-out;
}

.log-timestamp {
  color: rgb(var(--v-theme-on-surface-variant));
  font-size: 11px;
  white-space: nowrap;
  padding-top: 2px;
}

.log-level {
  white-space: nowrap;
}

.log-message {
  word-break: break-word;
  color: rgb(var(--v-theme-on-surface));
}

.log-details {
  grid-column: 1 / -1;
  margin-top: 4px;
}

.log-details-content {
  font-size: 11px;
  color: rgb(var(--v-theme-on-surface-variant));
  background-color: rgb(var(--v-theme-surface));
  padding: 8px;
  border-radius: 4px;
  overflow-x: auto;
  white-space: pre-wrap;
  word-break: break-word;
}

.log-error .log-message {
  color: rgb(var(--v-theme-error));
}

.log-warn .log-message {
  color: rgb(var(--v-theme-warning));
}

.log-info .log-message {
  color: rgb(var(--v-theme-info));
}

.log-debug .log-message {
  color: rgb(var(--v-theme-on-surface-variant));
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: rgb(var(--v-theme-on-surface-variant));
}

.log-filters {
  display: flex;
  align-items: center;
  gap: 8px;
}

.gap-2 {
  gap: 8px;
}

@keyframes highlight {
  0% {
    background-color: rgba(var(--v-theme-primary), 0.2);
  }
  100% {
    background-color: transparent;
  }
}

/* 自定义滚动条 */
.log-content::-webkit-scrollbar {
  width: 6px;
}

.log-content::-webkit-scrollbar-track {
  background: rgba(var(--v-border-color), 0.1);
}

.log-content::-webkit-scrollbar-thumb {
  background: rgba(var(--v-border-color), 0.3);
  border-radius: 3px;
}

.log-content::-webkit-scrollbar-thumb:hover {
  background: rgba(var(--v-border-color), 0.5);
}
</style>