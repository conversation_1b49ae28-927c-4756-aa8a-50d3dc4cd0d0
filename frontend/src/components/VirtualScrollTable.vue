<template>
  <div class="virtual-scroll-container" ref="containerRef">
    <div 
      class="virtual-scroll-content" 
      :style="{ height: totalHeight + 'px' }"
    >
      <div 
        class="virtual-scroll-viewport"
        :style="{ transform: `translateY(${offsetY}px)` }"
      >
        <v-data-table
          :headers="headers"
          :items="visibleItems"
          :loading="loading"
          hide-default-footer
          class="virtual-table"
          density="compact"
        >
          <template v-for="(_, slot) of $slots" v-slot:[slot]="scope">
            <slot :name="slot" v-bind="scope" />
          </template>
        </v-data-table>
      </div>
    </div>
    
    <!-- 滚动指示器 -->
    <div v-if="showScrollIndicator" class="scroll-indicator">
      {{ startIndex + 1 }} - {{ endIndex }} / {{ totalItems }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'

interface Props {
  items: any[]
  headers: any[]
  itemHeight?: number
  containerHeight?: number
  loading?: boolean
  showScrollIndicator?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  itemHeight: 60,
  containerHeight: 400,
  loading: false,
  showScrollIndicator: true
})

const containerRef = ref<HTMLElement>()
const scrollTop = ref(0)

// 计算属性
const totalItems = computed(() => props.items.length)
const visibleCount = computed(() => Math.ceil(props.containerHeight / props.itemHeight) + 2)
const totalHeight = computed(() => totalItems.value * props.itemHeight)

const startIndex = computed(() => {
  const index = Math.floor(scrollTop.value / props.itemHeight)
  return Math.max(0, index - 1)
})

const endIndex = computed(() => {
  const index = startIndex.value + visibleCount.value
  return Math.min(totalItems.value, index)
})

const visibleItems = computed(() => {
  return props.items.slice(startIndex.value, endIndex.value).map((item, index) => ({
    ...item,
    _virtualIndex: startIndex.value + index
  }))
})

const offsetY = computed(() => startIndex.value * props.itemHeight)

// 滚动处理
function handleScroll(event: Event) {
  const target = event.target as HTMLElement
  scrollTop.value = target.scrollTop
}

// 滚动到指定项目
function scrollToItem(index: number) {
  if (containerRef.value) {
    const targetScrollTop = index * props.itemHeight
    containerRef.value.scrollTop = targetScrollTop
  }
}

// 滚动到顶部
function scrollToTop() {
  scrollToItem(0)
}

// 滚动到底部
function scrollToBottom() {
  scrollToItem(totalItems.value - 1)
}

// 生命周期
onMounted(() => {
  if (containerRef.value) {
    containerRef.value.addEventListener('scroll', handleScroll, { passive: true })
  }
})

onUnmounted(() => {
  if (containerRef.value) {
    containerRef.value.removeEventListener('scroll', handleScroll)
  }
})

// 监听items变化，重置滚动位置
watch(() => props.items.length, (newLength, oldLength) => {
  if (newLength !== oldLength && newLength === 0) {
    scrollTop.value = 0
  }
})

// 暴露方法给父组件
defineExpose({
  scrollToItem,
  scrollToTop,
  scrollToBottom
})
</script>

<style scoped>
.virtual-scroll-container {
  position: relative;
  overflow-y: auto;
  height: v-bind('props.containerHeight + "px"');
  border: 1px solid rgba(var(--v-border-color), var(--v-border-opacity));
  border-radius: 4px;
}

.virtual-scroll-content {
  position: relative;
}

.virtual-scroll-viewport {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
}

.virtual-table {
  background: transparent !important;
}

.virtual-table :deep(.v-data-table__wrapper) {
  overflow: visible;
}

.scroll-indicator {
  position: absolute;
  top: 8px;
  right: 8px;
  background: rgba(var(--v-theme-surface), 0.9);
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  color: rgba(var(--v-theme-on-surface), 0.7);
  pointer-events: none;
  z-index: 1;
}

/* 优化滚动性能 */
.virtual-scroll-container {
  will-change: scroll-position;
}

.virtual-scroll-viewport {
  will-change: transform;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .scroll-indicator {
    font-size: 10px;
    padding: 2px 6px;
  }
}
</style>
