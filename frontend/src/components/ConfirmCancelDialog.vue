<template>
  <v-dialog
    v-model="dialogOpen"
    max-width="400px"
    @update:modelValue="$emit('update:open', $event)"
  >
    <v-card>
      <v-card-title class="text-error">
        <v-icon class="mr-2" color="error">mdi-alert-circle</v-icon>
        确认取消订单
      </v-card-title>
      
      <v-card-text>
        确定要取消这个条件订单吗？此操作无法撤销。
      </v-card-text>
      
      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn
          variant="text"
          @click="closeDialog"
        >
          保留
        </v-btn>
        <v-btn
          color="error"
          variant="flat"
          :loading="loading"
          @click="confirm"
        >
          取消订单
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script setup>
import { ref, defineProps, defineEmits, watch } from 'vue';

const props = defineProps({
  open: {
    type: Boolean,
    default: false
  },
  loading: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:open', 'confirm']);

// 本地状态
const dialogOpen = ref(props.open);

// 监听外部更新
watch(() => props.open, (newVal) => {
  dialogOpen.value = newVal;
});

// 方法
function closeDialog() {
  emit('update:open', false);
}

function confirm() {
  emit('confirm');
}
</script> 