<template>
  <v-row>
    <!-- 总资产价值 -->
    <v-col cols="12" sm="6" md="3">
      <v-card class="stats-card" elevation="2">
        <v-card-text class="pb-2">
          <div class="d-flex align-center justify-space-between">
            <div>
              <div class="text-body-2 text-medium-emphasis mb-1">总资产价值</div>
              <div class="text-h5 font-weight-bold">
                ${{ formatNumber(stats.totalValue) }}
              </div>
              <div class="d-flex align-center mt-1">
                <v-icon 
                  :color="stats.totalValueChange >= 0 ? 'success' : 'error'" 
                  size="16" 
                  class="mr-1"
                >
                  {{ stats.totalValueChange >= 0 ? 'mdi-trending-up' : 'mdi-trending-down' }}
                </v-icon>
                <span 
                  :class="stats.totalValueChange >= 0 ? 'text-success' : 'text-error'"
                  class="text-caption"
                >
                  {{ formatPercentage(stats.totalValueChange) }}%
                </span>
              </div>
            </div>
            <v-avatar color="primary" variant="tonal" size="48">
              <v-icon>mdi-wallet</v-icon>
            </v-avatar>
          </div>
        </v-card-text>
      </v-card>
    </v-col>

    <!-- 活跃订单数 -->
    <v-col cols="12" sm="6" md="3">
      <v-card class="stats-card" elevation="2">
        <v-card-text class="pb-2">
          <div class="d-flex align-center justify-space-between">
            <div>
              <div class="text-body-2 text-medium-emphasis mb-1">活跃订单</div>
              <div class="text-h5 font-weight-bold">
                {{ stats.activeOrders }}
              </div>
              <div class="text-caption text-medium-emphasis mt-1">
                {{ stats.totalOrders }} 总订单
              </div>
            </div>
            <v-avatar color="info" variant="tonal" size="48">
              <v-icon>mdi-chart-line</v-icon>
            </v-avatar>
          </div>
        </v-card-text>
      </v-card>
    </v-col>

    <!-- 今日盈亏 -->
    <v-col cols="12" sm="6" md="3">
      <v-card class="stats-card" elevation="2">
        <v-card-text class="pb-2">
          <div class="d-flex align-center justify-space-between">
            <div>
              <div class="text-body-2 text-medium-emphasis mb-1">今日盈亏</div>
              <div 
                class="text-h5 font-weight-bold"
                :class="stats.todayPnl >= 0 ? 'text-success' : 'text-error'"
              >
                {{ stats.todayPnl >= 0 ? '+' : '' }}${{ formatNumber(Math.abs(stats.todayPnl)) }}
              </div>
              <div class="d-flex align-center mt-1">
                <v-icon 
                  :color="stats.todayPnlPercentage >= 0 ? 'success' : 'error'" 
                  size="16" 
                  class="mr-1"
                >
                  {{ stats.todayPnlPercentage >= 0 ? 'mdi-trending-up' : 'mdi-trending-down' }}
                </v-icon>
                <span 
                  :class="stats.todayPnlPercentage >= 0 ? 'text-success' : 'text-error'"
                  class="text-caption"
                >
                  {{ formatPercentage(stats.todayPnlPercentage) }}%
                </span>
              </div>
            </div>
            <v-avatar 
              :color="stats.todayPnl >= 0 ? 'success' : 'error'" 
              variant="tonal" 
              size="48"
            >
              <v-icon>
                {{ stats.todayPnl >= 0 ? 'mdi-trending-up' : 'mdi-trending-down' }}
              </v-icon>
            </v-avatar>
          </div>
        </v-card-text>
      </v-card>
    </v-col>

    <!-- 条件订单 -->
    <v-col cols="12" sm="6" md="3">
      <v-card class="stats-card" elevation="2">
        <v-card-text class="pb-2">
          <div class="d-flex align-center justify-space-between">
            <div>
              <div class="text-body-2 text-medium-emphasis mb-1">条件订单</div>
              <div class="text-h5 font-weight-bold">
                {{ stats.conditionalOrders }}
              </div>
              <div class="text-caption text-medium-emphasis mt-1">
                {{ stats.triggeredToday }} 今日触发
              </div>
            </div>
            <v-avatar color="warning" variant="tonal" size="48">
              <v-icon>mdi-clock-alert</v-icon>
            </v-avatar>
          </div>
        </v-card-text>
      </v-card>
    </v-col>
  </v-row>
</template>

<script setup>
import { computed } from 'vue'

// Props
const props = defineProps({
  stats: {
    type: Object,
    default: () => ({
      totalValue: 0,
      totalValueChange: 0,
      activeOrders: 0,
      totalOrders: 0,
      todayPnl: 0,
      todayPnlPercentage: 0,
      conditionalOrders: 0,
      triggeredToday: 0
    })
  },
  loading: {
    type: Boolean,
    default: false
  }
})

// 格式化数字
const formatNumber = (value) => {
  if (value === null || value === undefined) return '0'
  
  const num = Number(value)
  if (isNaN(num)) return '0'
  
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  } else {
    return num.toFixed(2)
  }
}

// 格式化百分比
const formatPercentage = (value) => {
  if (value === null || value === undefined) return '0.00'
  
  const num = Number(value)
  if (isNaN(num)) return '0.00'
  
  return Math.abs(num).toFixed(2)
}
</script>

<style scoped>
.stats-card {
  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
  border-radius: 12px;
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
}

.text-success {
  color: rgb(var(--v-theme-success)) !important;
}

.text-error {
  color: rgb(var(--v-theme-error)) !important;
}

.text-medium-emphasis {
  color: rgba(var(--v-theme-on-surface), 0.6) !important;
}
</style>