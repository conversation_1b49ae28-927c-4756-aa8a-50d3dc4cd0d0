/**
 * LLMConfigCard组件测试
 */

import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { createVuetify } from 'vuetify'
import { createPinia, setActivePinia } from 'pinia'
import LLMConfigCard from '../LLMConfigCard.vue'
import type { LLMConfig } from '@/types/llm.types'
import { useLLMConfigStore } from '@/stores/llmConfig'

// Mock store
vi.mock('@/stores/llmConfig')

const vuetify = createVuetify()

const mockConfig: LLMConfig = {
  id: 'test-config-id',
  config_name: '测试配置',
  provider: 'chatgpt',
  enabled: true,
  is_default: false,
  api_base_url: 'https://api.openai.com/v1',
  model_name: 'gpt-4',
  max_tokens: 4096,
  temperature: 0.7,
  timeout_seconds: 60,
  max_retries: 3,
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z',
  api_key_masked: 'sk-1***abc'
}

describe('LLMConfigCard', () => {
  let wrapper: any
  let mockStore: any

  beforeEach(() => {
    setActivePinia(createPinia())
    
    mockStore = {
      testConfig: vi.fn(),
      setDefaultConfig: vi.fn(),
      toggleConfig: vi.fn(),
      getTestResult: vi.fn().mockReturnValue(null)
    }
    
    vi.mocked(useLLMConfigStore).mockReturnValue(mockStore as any)

    wrapper = mount(LLMConfigCard, {
      props: {
        config: mockConfig
      },
      global: {
        plugins: [vuetify]
      }
    })
  })

  it('应该正确渲染配置信息', () => {
    expect(wrapper.find('.text-h6').text()).toBe('测试配置')
    expect(wrapper.text()).toContain('ChatGPT')
    expect(wrapper.text()).toContain('gpt-4')
    expect(wrapper.text()).toContain('4,096')
    expect(wrapper.text()).toContain('0.7')
    expect(wrapper.text()).toContain('60秒')
    expect(wrapper.text()).toContain('3次')
    expect(wrapper.text()).toContain('sk-1***abc')
  })

  it('应该显示启用状态', () => {
    const statusChip = wrapper.find('.v-chip:contains("已启用")')
    expect(statusChip.exists()).toBe(true)
  })

  it('应该显示默认配置标签', async () => {
    await wrapper.setProps({
      config: { ...mockConfig, is_default: true }
    })
    
    const defaultChip = wrapper.find('.v-chip:contains("默认")')
    expect(defaultChip.exists()).toBe(true)
  })

  it('应该显示禁用状态', async () => {
    await wrapper.setProps({
      config: { ...mockConfig, enabled: false }
    })
    
    const statusChip = wrapper.find('.v-chip:contains("已禁用")')
    expect(statusChip.exists()).toBe(true)
  })

  it('应该调用测试连接', async () => {
    const testButton = wrapper.find('button:contains("测试连接")')
    await testButton.trigger('click')
    
    expect(mockStore.testConfig).toHaveBeenCalledWith('test-config-id')
  })

  it('应该触发编辑事件', async () => {
    const editButton = wrapper.find('button:contains("编辑")')
    await editButton.trigger('click')
    
    expect(wrapper.emitted('edit')).toBeTruthy()
    expect(wrapper.emitted('edit')[0]).toEqual([mockConfig])
  })

  it('应该显示测试结果', async () => {
    const testResult = {
      success: true,
      response_text: '测试成功',
      response_time_ms: 150
    }
    
    mockStore.getTestResult.mockReturnValue(testResult)
    
    await wrapper.vm.$nextTick()
    
    expect(wrapper.text()).toContain('测试成功')
    expect(wrapper.text()).toContain('150ms')
  })

  it('应该显示测试失败结果', async () => {
    const testResult = {
      success: false,
      error_message: '连接失败'
    }
    
    mockStore.getTestResult.mockReturnValue(testResult)
    
    await wrapper.vm.$nextTick()
    
    expect(wrapper.text()).toContain('测试失败')
    expect(wrapper.text()).toContain('连接失败')
  })

  it('应该调用设为默认', async () => {
    // 打开菜单
    const menuButton = wrapper.find('[data-testid="menu-button"]')
    await menuButton.trigger('click')
    
    // 点击设为默认
    const setDefaultItem = wrapper.find('[data-testid="set-default"]')
    await setDefaultItem.trigger('click')
    
    expect(mockStore.setDefaultConfig).toHaveBeenCalledWith('test-config-id')
  })

  it('应该调用切换启用状态', async () => {
    // 打开菜单
    const menuButton = wrapper.find('[data-testid="menu-button"]')
    await menuButton.trigger('click')
    
    // 点击禁用
    const toggleItem = wrapper.find('[data-testid="toggle"]')
    await toggleItem.trigger('click')
    
    expect(mockStore.toggleConfig).toHaveBeenCalledWith('test-config-id')
  })

  it('应该触发复制事件', async () => {
    // 打开菜单
    const menuButton = wrapper.find('[data-testid="menu-button"]')
    await menuButton.trigger('click')
    
    // 点击复制
    const duplicateItem = wrapper.find('[data-testid="duplicate"]')
    await duplicateItem.trigger('click')
    
    expect(wrapper.emitted('duplicate')).toBeTruthy()
    expect(wrapper.emitted('duplicate')[0]).toEqual([mockConfig])
  })

  it('应该触发删除事件', async () => {
    // 打开菜单
    const menuButton = wrapper.find('[data-testid="menu-button"]')
    await menuButton.trigger('click')
    
    // 点击删除
    const deleteItem = wrapper.find('[data-testid="delete"]')
    await deleteItem.trigger('click')
    
    expect(wrapper.emitted('delete')).toBeTruthy()
    expect(wrapper.emitted('delete')[0]).toEqual([mockConfig])
  })

  it('应该正确格式化时间', () => {
    const timeText = wrapper.find('.time-info').text()
    expect(timeText).toContain('创建时间')
    expect(timeText).toContain('2024')
  })

  it('应该根据提供商显示正确的图标和颜色', () => {
    const icon = wrapper.find('.v-icon')
    expect(icon.exists()).toBe(true)
  })

  it('应该在测试时显示加载状态', async () => {
    const testButton = wrapper.find('button:contains("测试连接")')
    
    // 模拟测试中状态
    await wrapper.setData({ testing: true })
    
    expect(testButton.classes()).toContain('v-btn--loading')
  })

  it('应该处理不同的LLM提供商', async () => {
    const providers = ['deepseek', 'gemini', 'claude']
    
    for (const provider of providers) {
      await wrapper.setProps({
        config: { ...mockConfig, provider }
      })
      
      // 验证提供商特定的显示
      expect(wrapper.text()).toContain(provider)
    }
  })

  it('应该显示API基础URL', async () => {
    await wrapper.setProps({
      config: { ...mockConfig, api_base_url: 'https://custom.api.com' }
    })
    
    // 验证自定义API URL的显示或处理
    expect(wrapper.vm.config.api_base_url).toBe('https://custom.api.com')
  })

  it('应该处理空的API基础URL', async () => {
    await wrapper.setProps({
      config: { ...mockConfig, api_base_url: undefined }
    })
    
    expect(wrapper.vm.config.api_base_url).toBeUndefined()
  })

  it('应该正确显示参数范围', () => {
    // 验证各种参数的显示
    expect(wrapper.text()).toContain('4,096') // max_tokens
    expect(wrapper.text()).toContain('0.7')   // temperature
    expect(wrapper.text()).toContain('60秒')   // timeout
    expect(wrapper.text()).toContain('3次')    // retries
  })

  it('应该在默认配置时隐藏设为默认选项', async () => {
    await wrapper.setProps({
      config: { ...mockConfig, is_default: true }
    })

    // 打开菜单
    const menuButton = wrapper.find('[data-testid="menu-button"]')
    await menuButton.trigger('click')

    // 验证设为默认选项不存在
    const setDefaultItem = wrapper.find('[data-testid="set-default"]')
    expect(setDefaultItem.exists()).toBe(false)
  })

  it('应该处理长配置名称', async () => {
    const longName = '这是一个非常长的配置名称用来测试组件如何处理长文本内容的显示和布局'
    await wrapper.setProps({
      config: { ...mockConfig, config_name: longName }
    })

    expect(wrapper.find('.text-h6').text()).toBe(longName)
  })

  it('应该处理极端参数值', async () => {
    await wrapper.setProps({
      config: {
        ...mockConfig,
        max_tokens: 1,
        temperature: 0.0,
        timeout_seconds: 1,
        max_retries: 1
      }
    })

    expect(wrapper.text()).toContain('1')
    expect(wrapper.text()).toContain('0.0')
  })
})
