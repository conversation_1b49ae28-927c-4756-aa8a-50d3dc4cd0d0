/**
 * LLMConfigPanel组件测试
 */

import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { createVuetify } from 'vuetify'
import { createPinia, setActivePinia } from 'pinia'
import LLMConfigPanel from '../LLMConfigPanel.vue'
import type { LLMConfig } from '@/types/llm.types'
import { useLLMConfigStore } from '@/stores/llmConfig'

// Mock子组件
vi.mock('../LLMConfigCard.vue', () => ({
  default: {
    name: 'LLMConfigCard',
    template: '<div class="mock-llm-config-card">{{ config.config_name }}</div>',
    props: ['config'],
    emits: ['edit', 'delete', 'duplicate']
  }
}))

vi.mock('../LLMConfigDialog.vue', () => ({
  default: {
    name: 'LLMConfigDialog',
    template: '<div class="mock-llm-config-dialog"></div>',
    props: ['modelValue', 'config'],
    emits: ['update:modelValue', 'success']
  }
}))

// Mock store
vi.mock('@/stores/llmConfig')

const vuetify = createVuetify()

const mockConfigs: LLMConfig[] = [
  {
    id: 'config-1',
    config_name: 'ChatGPT主配置',
    provider: 'chatgpt',
    enabled: true,
    is_default: true,
    api_base_url: 'https://api.openai.com/v1',
    model_name: 'gpt-4',
    max_tokens: 4096,
    temperature: 0.7,
    timeout_seconds: 60,
    max_retries: 3,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
    api_key_masked: 'sk-1***abc'
  },
  {
    id: 'config-2',
    config_name: 'DeepSeek备用',
    provider: 'deepseek',
    enabled: false,
    is_default: false,
    model_name: 'deepseek-chat',
    max_tokens: 2048,
    temperature: 0.8,
    timeout_seconds: 30,
    max_retries: 2,
    created_at: '2024-01-02T00:00:00Z',
    updated_at: '2024-01-02T00:00:00Z',
    api_key_masked: 'ds-1***xyz'
  }
]

describe('LLMConfigPanel', () => {
  let wrapper: any
  let mockStore: any

  beforeEach(() => {
    setActivePinia(createPinia())
    
    mockStore = {
      configs: mockConfigs,
      loading: false,
      configStats: {
        total: 2,
        enabled: 1,
        disabled: 1
      },
      configsByProvider: {
        chatgpt: [mockConfigs[0]],
        deepseek: [mockConfigs[1]]
      },
      defaultConfig: mockConfigs[0],
      fetchConfigs: vi.fn(),
      deleteConfig: vi.fn(),
      duplicateConfig: vi.fn()
    }
    
    vi.mocked(useLLMConfigStore).mockReturnValue(mockStore as any)

    wrapper = mount(LLMConfigPanel, {
      global: {
        plugins: [vuetify]
      }
    })
  })

  it('应该正确渲染面板头部', () => {
    expect(wrapper.find('h2').text()).toBe('LLM配置管理')
    expect(wrapper.text()).toContain('管理您的大语言模型服务配置')
    expect(wrapper.find('button:contains("添加配置")').exists()).toBe(true)
  })

  it('应该显示统计信息', () => {
    expect(wrapper.text()).toContain('2') // 总配置数
    expect(wrapper.text()).toContain('1') // 已启用
    expect(wrapper.text()).toContain('2') // 服务商数
    expect(wrapper.text()).toContain('1') // 默认配置
  })

  it('应该显示搜索和筛选工具', () => {
    expect(wrapper.find('input[placeholder*="搜索"]').exists()).toBe(true)
    expect(wrapper.find('label:contains("筛选提供商")').exists()).toBe(true)
    expect(wrapper.find('label:contains("筛选状态")').exists()).toBe(true)
  })

  it('应该渲染配置卡片', () => {
    const cards = wrapper.findAll('.mock-llm-config-card')
    expect(cards).toHaveLength(2)
    expect(cards[0].text()).toContain('ChatGPT主配置')
    expect(cards[1].text()).toContain('DeepSeek备用')
  })

  it('应该在加载时显示加载状态', async () => {
    mockStore.loading = true
    await wrapper.vm.$nextTick()
    
    expect(wrapper.find('.v-progress-circular').exists()).toBe(true)
    expect(wrapper.text()).toContain('加载配置中')
  })

  it('应该在无配置时显示空状态', async () => {
    mockStore.configs = []
    await wrapper.vm.$nextTick()
    
    expect(wrapper.text()).toContain('暂无LLM配置')
    expect(wrapper.text()).toContain('点击上方按钮创建您的第一个LLM配置')
  })

  it('应该支持搜索功能', async () => {
    const searchInput = wrapper.find('input[placeholder*="搜索"]')
    await searchInput.setValue('ChatGPT')
    
    // 验证过滤逻辑
    expect(wrapper.vm.filteredConfigs).toHaveLength(1)
    expect(wrapper.vm.filteredConfigs[0].config_name).toBe('ChatGPT主配置')
  })

  it('应该支持按提供商筛选', async () => {
    await wrapper.setData({ filterProvider: 'deepseek' })
    
    expect(wrapper.vm.filteredConfigs).toHaveLength(1)
    expect(wrapper.vm.filteredConfigs[0].provider).toBe('deepseek')
  })

  it('应该支持按状态筛选', async () => {
    await wrapper.setData({ filterStatus: 'enabled' })
    
    expect(wrapper.vm.filteredConfigs).toHaveLength(1)
    expect(wrapper.vm.filteredConfigs[0].enabled).toBe(true)
  })

  it('应该支持按默认状态筛选', async () => {
    await wrapper.setData({ filterStatus: 'default' })
    
    expect(wrapper.vm.filteredConfigs).toHaveLength(1)
    expect(wrapper.vm.filteredConfigs[0].is_default).toBe(true)
  })

  it('应该打开创建对话框', async () => {
    const addButton = wrapper.find('button:contains("添加配置")')
    await addButton.trigger('click')
    
    expect(wrapper.vm.showCreateDialog).toBe(true)
  })

  it('应该处理编辑配置', async () => {
    const mockConfig = mockConfigs[0]
    await wrapper.vm.handleEditConfig(mockConfig)
    
    expect(wrapper.vm.editingConfig).toBe(mockConfig)
    expect(wrapper.vm.showCreateDialog).toBe(true)
  })

  it('应该处理删除配置', async () => {
    const mockConfig = mockConfigs[0]
    await wrapper.vm.handleDeleteConfig(mockConfig)
    
    expect(wrapper.vm.deletingConfig).toBe(mockConfig)
    expect(wrapper.vm.showDeleteDialog).toBe(true)
  })

  it('应该处理复制配置', async () => {
    const mockConfig = mockConfigs[0]
    await wrapper.vm.handleDuplicateConfig(mockConfig)
    
    expect(wrapper.vm.duplicatingConfig).toBe(mockConfig)
    expect(wrapper.vm.duplicateName).toBe('ChatGPT主配置 - 副本')
    expect(wrapper.vm.showDuplicateDialog).toBe(true)
  })

  it('应该确认删除配置', async () => {
    wrapper.vm.deletingConfig = mockConfigs[0]
    await wrapper.vm.confirmDelete()
    
    expect(mockStore.deleteConfig).toHaveBeenCalledWith('config-1')
  })

  it('应该确认复制配置', async () => {
    wrapper.vm.duplicatingConfig = mockConfigs[0]
    wrapper.vm.duplicateName = '新配置名称'
    await wrapper.vm.confirmDuplicate()
    
    expect(mockStore.duplicateConfig).toHaveBeenCalledWith('config-1', '新配置名称')
  })

  it('应该在组件挂载时获取配置', () => {
    expect(mockStore.fetchConfigs).toHaveBeenCalled()
  })

  it('应该显示提供商筛选选项', () => {
    const providerOptions = wrapper.vm.providerFilterOptions
    expect(providerOptions).toHaveLength(2)
    expect(providerOptions.map((o: any) => o.value)).toContain('chatgpt')
    expect(providerOptions.map((o: any) => o.value)).toContain('deepseek')
  })

  it('应该处理配置成功事件', async () => {
    wrapper.vm.editingConfig = mockConfigs[0]
    await wrapper.vm.handleConfigSuccess()
    
    expect(wrapper.vm.editingConfig).toBeUndefined()
  })

  it('应该支持组合搜索和筛选', async () => {
    await wrapper.setData({ 
      searchQuery: 'Chat',
      filterProvider: 'chatgpt',
      filterStatus: 'enabled'
    })
    
    expect(wrapper.vm.filteredConfigs).toHaveLength(1)
    expect(wrapper.vm.filteredConfigs[0].config_name).toBe('ChatGPT主配置')
  })

  it('应该在搜索无结果时显示提示', async () => {
    await wrapper.setData({ searchQuery: '不存在的配置' })
    
    expect(wrapper.vm.filteredConfigs).toHaveLength(0)
    // 在实际渲染中会显示"没有找到匹配的配置"
  })

  it('应该正确计算统计信息', () => {
    expect(wrapper.vm.configStats.total).toBe(2)
    expect(wrapper.vm.configStats.enabled).toBe(1)
  })

  it('应该正确分组配置', () => {
    const grouped = wrapper.vm.configsByProvider
    expect(Object.keys(grouped)).toHaveLength(2)
    expect(grouped.chatgpt).toHaveLength(1)
    expect(grouped.deepseek).toHaveLength(1)
  })

  it('应该处理删除对话框取消', async () => {
    wrapper.vm.showDeleteDialog = true
    wrapper.vm.deletingConfig = mockConfigs[0]
    
    await wrapper.setData({ showDeleteDialog: false })
    
    expect(wrapper.vm.showDeleteDialog).toBe(false)
  })

  it('应该处理复制对话框取消', async () => {
    wrapper.vm.showDuplicateDialog = true
    wrapper.vm.duplicatingConfig = mockConfigs[0]
    
    await wrapper.setData({ showDuplicateDialog: false })
    
    expect(wrapper.vm.showDuplicateDialog).toBe(false)
  })

  it('应该验证复制名称不为空', async () => {
    wrapper.vm.duplicatingConfig = mockConfigs[0]
    wrapper.vm.duplicateName = ''
    
    // 复制按钮应该被禁用
    expect(wrapper.vm.duplicateName).toBe('')
  })

  it('应该处理响应式布局', async () => {
    // 验证在不同屏幕尺寸下的列数
    const cards = wrapper.findAll('.mock-llm-config-card')
    expect(cards.length).toBeGreaterThan(0)
  })
})
