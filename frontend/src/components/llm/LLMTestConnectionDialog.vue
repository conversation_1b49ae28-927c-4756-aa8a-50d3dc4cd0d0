<template>
  <v-dialog
    v-model="dialogVisible"
    max-width="600px"
    persistent
    :retain-focus="false"
  >
    <!-- 调试信息 -->
    <div v-if="false" style="position: fixed; top: 10px; left: 10px; background: red; color: white; padding: 10px; z-index: 9999;">
      Dialog Visible: {{ dialogVisible }}, ModelValue: {{ modelValue }}
    </div>
    <v-card>
      <!-- 对话框头部 -->
      <v-card-title class="d-flex align-center">
        <v-icon 
          :color="getHeaderIconColor()" 
          class="me-2"
        >
          {{ getHeaderIcon() }}
        </v-icon>
        <span>{{ getHeaderTitle() }}</span>
        <v-spacer />
        <v-btn
          v-if="!isLoading"
          icon="mdi-close"
          variant="text"
          size="small"
          @click="closeDialog"
        />
      </v-card-title>

      <v-divider />

      <!-- 对话框内容 -->
      <v-card-text class="pa-6">
        <!-- 加载状态 -->
        <div v-if="isLoading" class="text-center">
          <v-progress-circular
            color="primary"
            indeterminate
            size="64"
            class="mb-4"
          />
          <div class="text-h6 mb-2">正在测试连接...</div>
          <div class="text-body-2 text-medium-emphasis">
            正在向 {{ configName }} 发送测试消息，请稍候...
          </div>
        </div>

        <!-- 成功状态 -->
        <div v-else-if="testResult && testResult.success" class="text-center">
          <v-icon
            color="success"
            size="64"
            class="mb-4"
          >
            mdi-check-circle
          </v-icon>
          
          <div class="text-h6 text-success mb-3">连接测试成功！</div>
          
          <!-- 响应时间 -->
          <v-chip
            color="success"
            variant="outlined"
            size="small"
            class="mb-4"
          >
            <v-icon start size="small">mdi-timer</v-icon>
            响应时间: {{ testResult.response_time_ms }}ms
          </v-chip>

          <!-- LLM响应内容 -->
          <v-card
            variant="outlined"
            class="mt-4"
          >
            <v-card-subtitle class="pb-2">
              <v-icon start size="small">mdi-robot</v-icon>
              LLM响应内容
            </v-card-subtitle>
            <v-card-text class="pt-0">
              <div class="response-content">
                {{ testResult.response_text }}
              </div>
            </v-card-text>
          </v-card>
        </div>

        <!-- 失败状态 -->
        <div v-else-if="testResult && !testResult.success" class="text-center">
          <v-icon
            color="error"
            size="64"
            class="mb-4"
          >
            mdi-alert-circle
          </v-icon>
          
          <div class="text-h6 text-error mb-3">连接测试失败</div>
          
          <!-- 响应时间（如果有） -->
          <v-chip
            v-if="testResult.response_time_ms"
            color="error"
            variant="outlined"
            size="small"
            class="mb-4"
          >
            <v-icon start size="small">mdi-timer</v-icon>
            响应时间: {{ testResult.response_time_ms }}ms
          </v-chip>

          <!-- 错误信息 -->
          <v-alert
            type="error"
            variant="outlined"
            class="text-start mb-4"
          >
            <div class="text-subtitle-2 mb-2">错误详情：</div>
            <div class="text-body-2">{{ testResult.error_message }}</div>
          </v-alert>

          <!-- 故障排除建议 -->
          <v-card
            variant="outlined"
            class="text-start"
          >
            <v-card-subtitle class="pb-2">
              <v-icon start size="small">mdi-lightbulb-outline</v-icon>
              故障排除建议
            </v-card-subtitle>
            <v-card-text class="pt-0">
              <v-list density="compact" class="pa-0">
                <v-list-item
                  v-for="suggestion in getTroubleshootingSuggestions()"
                  :key="suggestion"
                  class="px-0"
                >
                  <template v-slot:prepend>
                    <v-icon size="small" color="info">mdi-chevron-right</v-icon>
                  </template>
                  <v-list-item-title class="text-body-2">
                    {{ suggestion }}
                  </v-list-item-title>
                </v-list-item>
              </v-list>
            </v-card-text>
          </v-card>
        </div>
      </v-card-text>

      <!-- 对话框操作 -->
      <v-card-actions class="pa-4">
        <v-spacer />
        
        <!-- 加载状态下的按钮 -->
        <template v-if="isLoading">
          <v-btn
            variant="outlined"
            @click="cancelTest"
            :disabled="!canCancel"
          >
            取消
          </v-btn>
        </template>

        <!-- 成功状态下的按钮 -->
        <template v-else-if="testResult && testResult.success">
          <v-btn
            color="primary"
            variant="flat"
            @click="closeDialog"
          >
            确定
          </v-btn>
        </template>

        <!-- 失败状态下的按钮 -->
        <template v-else-if="testResult && !testResult.success">
          <v-btn
            variant="outlined"
            @click="closeDialog"
          >
            取消
          </v-btn>
          <v-btn
            color="primary"
            variant="flat"
            @click="retryTest"
            :loading="isRetrying"
          >
            重试
          </v-btn>
        </template>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import type { LLMConfigTestResponse } from '@/types/llm.types'

interface Props {
  modelValue: boolean
  configId?: string
  configName?: string
  provider?: string
  testResult?: LLMConfigTestResponse | null
  loading?: boolean
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'test', configId: string): void
  (e: 'retry', configId: string): void
  (e: 'cancel'): void
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  configName: '未知配置',
  provider: 'unknown'
})

const emit = defineEmits<Emits>()

// 状态
const isRetrying = ref(false)
const canCancel = ref(true)

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const isLoading = computed(() => props.loading || isRetrying.value)

// 方法
function getHeaderIcon(): string {
  if (isLoading.value) return 'mdi-loading'
  if (props.testResult?.success) return 'mdi-check-circle'
  if (props.testResult && !props.testResult.success) return 'mdi-alert-circle'
  return 'mdi-test-tube'
}

function getHeaderIconColor(): string {
  if (isLoading.value) return 'primary'
  if (props.testResult?.success) return 'success'
  if (props.testResult && !props.testResult.success) return 'error'
  return 'primary'
}

function getHeaderTitle(): string {
  if (isLoading.value) return '测试连接中'
  if (props.testResult?.success) return '测试成功'
  if (props.testResult && !props.testResult.success) return '测试失败'
  return '测试连接'
}

function getTroubleshootingSuggestions(): string[] {
  const errorMessage = props.testResult?.error_message?.toLowerCase() || ''
  const suggestions: string[] = []

  // 根据错误类型提供建议
  if (errorMessage.includes('api key') || errorMessage.includes('authentication') || errorMessage.includes('unauthorized')) {
    suggestions.push('检查API密钥是否正确且有效')
    suggestions.push('确认API密钥具有所需的权限')
    suggestions.push('检查API密钥是否已过期')
  }

  if (errorMessage.includes('timeout') || errorMessage.includes('超时')) {
    suggestions.push('检查网络连接是否稳定')
    suggestions.push('尝试增加超时时间设置')
    suggestions.push('检查防火墙设置是否阻止了连接')
  }

  if (errorMessage.includes('network') || errorMessage.includes('connection') || errorMessage.includes('网络')) {
    suggestions.push('检查网络连接是否正常')
    suggestions.push('确认能够访问外部网络')
    suggestions.push('检查代理设置（如果使用代理）')
  }

  if (errorMessage.includes('quota') || errorMessage.includes('limit') || errorMessage.includes('rate')) {
    suggestions.push('检查API配额是否已用完')
    suggestions.push('等待一段时间后重试')
    suggestions.push('联系服务提供商增加配额')
  }

  if (errorMessage.includes('model') || errorMessage.includes('模型')) {
    suggestions.push('检查模型名称是否正确')
    suggestions.push('确认该模型在当前API密钥下可用')
    suggestions.push('尝试使用其他可用的模型')
  }

  if (errorMessage.includes('url') || errorMessage.includes('endpoint')) {
    suggestions.push('检查API基础URL是否正确')
    suggestions.push('确认服务提供商的API地址')
  }

  // 通用建议
  if (suggestions.length === 0) {
    suggestions.push('检查所有配置参数是否正确')
    suggestions.push('确认网络连接正常')
    suggestions.push('稍后重试或联系技术支持')
  }

  return suggestions
}

function closeDialog(): void {
  emit('update:modelValue', false)
}

function retryTest(): void {
  if (!props.configId) return
  
  isRetrying.value = true
  emit('retry', props.configId)
}

function cancelTest(): void {
  emit('cancel')
}

// 监听重试状态
watch(() => props.loading, (newLoading) => {
  if (!newLoading) {
    isRetrying.value = false
  }
})
</script>

<style scoped>
.response-content {
  white-space: pre-wrap;
  word-break: break-word;
  line-height: 1.5;
  font-family: 'Roboto', sans-serif;
}

.v-progress-circular {
  animation: spin 2s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
