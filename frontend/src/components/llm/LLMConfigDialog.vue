<template>
  <v-dialog v-model="dialogVisible" max-width="800px" persistent>
    <v-card>
      <v-card-title class="d-flex align-center">
        <v-icon class="me-2">mdi-brain</v-icon>
        {{ isEditing ? '编辑LLM配置' : '新建LLM配置' }}
      </v-card-title>

      <v-card-text>
        <v-form ref="formRef" v-model="formValid">
          <v-row>

            <v-col cols="12" md="6">
              <v-text-field
                v-model="formData.config_name"
                label="配置名称"
                :rules="validationRules.config_name"
                required
                prepend-icon="mdi-tag"
                hint="为此LLM配置起一个易识别的名称"
                persistent-hint
              />
            </v-col>

            <v-col cols="12" md="6">
              <v-switch
                v-model="formData.enabled"
                label="启用配置"
                color="success"
                inset
                hide-details
              />
            </v-col>

            <v-col cols="12" md="6">
              <v-select
                v-model="formData.provider"
                label="LLM服务提供商"
                :items="providerOptions"
                :rules="validationRules.provider"
                required
                prepend-icon="mdi-cloud"
                @update:model-value="handleProviderChange"
              />
            </v-col>

            <v-col cols="12" md="6">
              <v-switch
                v-model="formData.is_default"
                label="设为默认配置"
                color="primary"
                hide-details
              />
            </v-col>

            <!-- API配置 -->
            <v-col cols="12">
              <div class="text-h6 mb-4 mt-4 d-flex align-center">
                <v-icon icon="mdi-key" class="me-2" />
                API配置
              </div>
            </v-col>

            <v-col cols="12">
              <v-text-field
                v-model="formData.api_key"
                label="API密钥"
                :type="showApiKey ? 'text' : 'password'"
                :rules="validationRules.api_key"
                variant="outlined"
                density="comfortable"
                required
                :placeholder="isEditing ? '留空则保持原密钥不变' : '请输入API密钥'"
              >
                <template #append-inner>
                  <v-btn
                    :icon="showApiKey ? 'mdi-eye-off' : 'mdi-eye'"
                    variant="text"
                    size="small"
                    @click="showApiKey = !showApiKey"
                  />
                </template>
              </v-text-field>
            </v-col>

            <v-col cols="12" md="6">
              <v-text-field
                v-model="formData.api_base_url"
                label="API基础URL（可选）"
                :placeholder="selectedProviderInfo?.defaultApiBaseUrl || '使用默认URL'"
                variant="outlined"
                density="comfortable"
              />
            </v-col>

            <v-col cols="12" md="6">
              <v-combobox
                v-model="formData.model_name"
                label="模型名称"
                :items="availableModels"
                :rules="validationRules.model_name"
                variant="outlined"
                density="comfortable"
                required
              />
            </v-col>

            <!-- 请求参数配置 -->
            <v-col cols="12">
              <div class="text-h6 mb-4 mt-4 d-flex align-center">
                <v-icon icon="mdi-tune" class="me-2" />
                请求参数
              </div>
            </v-col>

            <v-col cols="12" md="6">
              <v-text-field
                v-model.number="formData.max_tokens"
                label="最大Token数"
                type="number"
                :rules="validationRules.max_tokens"
                variant="outlined"
                density="comfortable"
                min="1"
                max="32768"
              />
            </v-col>

            <v-col cols="12" md="6">
              <v-text-field
                v-model.number="formData.temperature"
                label="温度参数"
                type="number"
                :rules="validationRules.temperature"
                variant="outlined"
                density="comfortable"
                min="0"
                max="2"
                step="0.1"
              />
            </v-col>

            <v-col cols="12" md="6">
              <v-text-field
                v-model.number="formData.timeout_seconds"
                label="超时时间（秒）"
                type="number"
                :rules="validationRules.timeout_seconds"
                variant="outlined"
                density="comfortable"
                min="1"
                max="300"
              />
            </v-col>

            <v-col cols="12" md="6">
              <v-text-field
                v-model.number="formData.max_retries"
                label="最大重试次数"
                type="number"
                :rules="validationRules.max_retries"
                variant="outlined"
                density="comfortable"
                min="0"
                max="10"
              />
            </v-col>
          </v-row>
        </v-form>
      </v-card-text>

      <v-divider />

      <!-- 对话框操作 -->
      <v-card-actions class="pa-6">
        <v-spacer />
        <v-btn
          variant="text"
          @click="handleCancel"
        >
          取消
        </v-btn>
        <v-btn
          color="primary"
          variant="flat"
          :loading="loading"
          :disabled="!formValid"
          @click="handleSubmit"
        >
          {{ isEditing ? '更新' : '创建' }}
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import type { 
  LLMConfig, 
  LLMConfigFormData, 
  LLMProvider,
  LLMProviderInfo
} from '@/types/llm.types'
import { 
  createDefaultLLMConfigFormData,
  createLLMConfigFormDataFromConfig,
  getLLMProviderOptions,
  getLLMProviderInfo,
  validateLLMConfigFormData
} from '@/types/llm.types'
import { useLLMConfigStore } from '@/stores/llmConfig'

// Props
interface Props {
  modelValue: boolean
  config?: LLMConfig
}

const props = defineProps<Props>()

// Emits
interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'success', config: LLMConfig): void
}

const emit = defineEmits<Emits>()

// Store
const llmConfigStore = useLLMConfigStore()

// 状态
const formRef = ref()
const formValid = ref(false)
const loading = ref(false)
const showApiKey = ref(false)
const formData = ref<LLMConfigFormData>(createDefaultLLMConfigFormData())

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const isEditing = computed(() => !!props.config)

const providerOptions = computed(() => getLLMProviderOptions())

const selectedProviderInfo = computed((): LLMProviderInfo | null => {
  if (!formData.value.provider) return null
  return getLLMProviderInfo(formData.value.provider as LLMProvider)
})

const availableModels = computed(() => {
  return selectedProviderInfo.value?.defaultModels || []
})

// 验证规则
const validationRules = computed(() => ({
  config_name: [
    (v: string) => !!v || '配置名称不能为空',
    (v: string) => v.length <= 100 || '配置名称不能超过100个字符'
  ],
  provider: [
    (v: string) => !!v || '请选择LLM服务提供商'
  ],
  api_key: [
    (v: string) => {
      if (isEditing.value && !v) return true // 编辑时允许为空
      return !!v || 'API密钥不能为空'
    }
  ],
  model_name: [
    (v: string) => !!v || '模型名称不能为空',
    (v: string) => v.length <= 100 || '模型名称不能超过100个字符'
  ],
  max_tokens: [
    (v: number) => v >= 1 || '最大Token数必须大于0',
    (v: number) => v <= 32768 || '最大Token数不能超过32768'
  ],
  temperature: [
    (v: number) => v >= 0 || '温度参数不能小于0',
    (v: number) => v <= 2 || '温度参数不能大于2'
  ],
  timeout_seconds: [
    (v: number) => v >= 1 || '超时时间必须大于0秒',
    (v: number) => v <= 300 || '超时时间不能超过300秒'
  ],
  max_retries: [
    (v: number) => v >= 0 || '重试次数不能小于0',
    (v: number) => v <= 10 || '重试次数不能超过10'
  ]
}))

// 监听器
watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    resetForm()
  }
})

// 方法
function resetForm() {
  if (props.config) {
    formData.value = createLLMConfigFormDataFromConfig(props.config)
  } else {
    formData.value = createDefaultLLMConfigFormData()
  }
  
  nextTick(() => {
    formRef.value?.resetValidation()
  })
}

function handleProviderChange(provider: LLMProvider) {
  if (!provider) return
  
  const providerInfo = getLLMProviderInfo(provider)
  
  // 自动填充默认值
  if (!formData.value.model_name || !isEditing.value) {
    formData.value.model_name = providerInfo.defaultModel
  }
  
  if (!formData.value.api_base_url) {
    formData.value.api_base_url = providerInfo.defaultApiBaseUrl || ''
  }
  
  if (!isEditing.value) {
    formData.value.max_tokens = Math.min(formData.value.max_tokens, providerInfo.maxTokens)
  }
}

async function handleSubmit() {
  const { valid } = await formRef.value.validate()
  if (!valid) return

  // 客户端验证
  const errors = validateLLMConfigFormData(formData.value, isEditing.value)
  if (errors.length > 0) {
    console.error('表单验证失败:', errors)
    console.error('表单数据:', formData.value)
    console.error('编辑模式:', isEditing.value)
    // 显示第一个错误给用户
    llmConfigStore.showSnackbar(errors[0], 'error')
    return
  }

  loading.value = true

  try {
    const requestData = {
      config_name: formData.value.config_name,
      provider: formData.value.provider as LLMProvider,
      enabled: formData.value.enabled,
      is_default: formData.value.is_default,
      api_key: formData.value.api_key,
      api_base_url: formData.value.api_base_url || undefined,
      model_name: formData.value.model_name,
      max_tokens: formData.value.max_tokens,
      temperature: formData.value.temperature,
      timeout_seconds: formData.value.timeout_seconds,
      max_retries: formData.value.max_retries
    }

    let result: LLMConfig
    if (isEditing.value && props.config) {
      // 编辑时如果API密钥为空，则不更新
      if (!requestData.api_key) {
        delete (requestData as any).api_key
      }
      result = await llmConfigStore.updateConfig(props.config.id, requestData)
    } else {
      result = await llmConfigStore.createConfig(requestData)
    }

    emit('success', result)
    dialogVisible.value = false
  } catch (error) {
    // 错误已在store中处理
  } finally {
    loading.value = false
  }
}

function handleCancel() {
  dialogVisible.value = false
}
</script>

<style scoped>
.v-form .v-row {
  margin: 0;
}

.v-form .v-col {
  padding: 8px 12px;
}
</style>
