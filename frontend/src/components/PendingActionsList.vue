<template>
  <div class="pending-actions-list">
    <!-- 紧急控制按钮 -->
    <v-fade-transition>
      <v-btn
        v-if="actions.length > 0"
        color="error"
        block
        size="large"
        variant="flat"
        class="mb-4 emergency-button"
        elevation="4"
        @click="emergencyCancelAll"
        :loading="emergencyCancelling"
        prepend-icon="mdi-alert"
      >
        <v-icon start>mdi-shield-alert</v-icon>
        紧急取消所有待处理动作
      </v-btn>
    </v-fade-transition>

    <v-card 
      v-for="action in actions" 
      :key="action.id"
      class="mb-4 pending-action-card"
      :class="{ 'urgent': action.priority === 'high' }"
      elevation="2"
    >
      <v-card-title class="d-flex align-center pb-2">
        <v-icon 
          :color="getActionIcon(action.type).color" 
          class="mr-2"
          size="20"
        >
          {{ getActionIcon(action.type).icon }}
        </v-icon>
        <span class="text-subtitle-1">{{ getActionTitle(action.type) }}</span>
        <v-spacer></v-spacer>
        <v-chip 
          :color="getPriorityColor(action.priority)"
          size="small"
          variant="flat"
        >
          {{ getPriorityText(action.priority) }}
        </v-chip>
      </v-card-title>

      <v-card-text class="pt-0">
        <!-- 原始指令 -->
        <div class="mb-3">
          <div class="text-caption text-medium-emphasis mb-1">原始指令</div>
          <v-card 
            variant="outlined" 
            class="pa-3 bg-grey-lighten-5"
          >
            <div class="text-body-2">{{ action.raw_text }}</div>
          </v-card>
        </div>

        <!-- 解析结果 -->
        <div class="mb-3" v-if="action.parsed_intent">
          <div class="text-caption text-medium-emphasis mb-1">解析结果</div>
          <v-row dense>
            <v-col cols="6" v-if="action.parsed_intent.symbol">
              <div class="text-caption">交易对</div>
              <div class="text-body-2 font-weight-medium">
                {{ action.parsed_intent.symbol }}
              </div>
            </v-col>
            <v-col cols="6" v-if="action.parsed_intent.side">
              <div class="text-caption">方向</div>
              <v-chip 
                :color="action.parsed_intent.side === 'buy' ? 'success' : 'error'"
                size="small"
                variant="flat"
              >
                {{ action.parsed_intent.side === 'buy' ? '做多' : '做空' }}
              </v-chip>
            </v-col>
            <v-col cols="6" v-if="action.parsed_intent.quantity_usd">
              <div class="text-caption">金额</div>
              <div class="text-body-2 font-weight-medium">
                ${{ formatNumber(action.parsed_intent.quantity_usd) }}
              </div>
            </v-col>
            <v-col cols="6" v-if="action.parsed_intent.confidence !== undefined">
              <div class="text-caption">置信度</div>
              <v-progress-linear
                :model-value="action.parsed_intent.confidence * 100"
                :color="getConfidenceColor(action.parsed_intent.confidence)"
                height="8"
                rounded
                class="mt-1"
              >
                <template v-slot:default>
                  <span class="text-caption">
                    {{ Math.round(action.parsed_intent.confidence * 100) }}%
                  </span>
                </template>
              </v-progress-linear>
            </v-col>
          </v-row>
        </div>

        <!-- 需要澄清的问题 -->
        <div class="mb-3" v-if="action.clarification_needed">
          <div class="text-caption text-medium-emphasis mb-1">需要澄清</div>
          <v-alert
            type="warning"
            variant="tonal"
            density="compact"
            class="text-body-2"
          >
            {{ action.clarification_needed }}
          </v-alert>
        </div>

        <!-- 风险评估 -->
        <div class="mb-3" v-if="action.risk_assessment">
          <div class="text-caption text-medium-emphasis mb-1">风险评估</div>
          <v-alert
            :type="getRiskAlertType(action.risk_assessment.level)"
            variant="tonal"
            density="compact"
            class="text-body-2"
          >
            <div class="font-weight-medium">{{ action.risk_assessment.level }}</div>
            <div>{{ action.risk_assessment.reason }}</div>
          </v-alert>
        </div>

        <!-- 时间戳 -->
        <div class="text-caption text-medium-emphasis">
          <v-icon size="14" class="mr-1">mdi-clock-outline</v-icon>
          {{ formatDateTime(action.created_at) }}
        </div>
      </v-card-text>

      <v-card-actions class="pt-0">
        <v-spacer></v-spacer>
        
        <!-- 拒绝按钮 -->
        <v-btn
          variant="outlined"
          color="error"
          size="small"
          @click="rejectAction(action)"
          :loading="action.rejecting"
          prepend-icon="mdi-close"
        >
          拒绝
        </v-btn>

        <!-- 针对方向确认的专用按钮 -->
        <template v-if="isDirectionConfirmation(action)">
          <v-btn
            variant="flat"
            color="success"
            size="small"
            @click="confirmDirection(action, 'buy')"
            :loading="action.approving"
            prepend-icon="mdi-arrow-up-bold"
            class="ml-2"
          >
            做多
          </v-btn>
          <v-btn
            variant="flat"
            color="error"
            size="small"
            @click="confirmDirection(action, 'sell')"
            :loading="action.approving"
            prepend-icon="mdi-arrow-down-bold"
            class="ml-2"
          >
            做空
          </v-btn>
        </template>
        
        <!-- 一般性批准按钮 (非方向确认场景) -->
        <v-btn
          v-else
          variant="flat"
          color="success"
          size="small"
          @click="approveAction(action)"
          :loading="action.approving"
          prepend-icon="mdi-check"
          class="ml-2"
        >
          批准
        </v-btn>
      </v-card-actions>
    </v-card>

    <!-- 空状态 -->
    <div v-if="actions.length === 0" class="text-center py-8">
      <v-icon size="64" color="success" class="mb-4">
        mdi-check-circle-outline
      </v-icon>
      <div class="text-h6 text-medium-emphasis">暂无待处理动作</div>
      <div class="text-body-2 text-medium-emphasis">
        所有指令都已处理完成
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useAgentStore } from '@/stores/agent'
import { useUIStore } from '@/stores/ui'

// Props
const props = defineProps({
  actions: {
    type: Array,
    default: () => []
  }
})

// Stores
const agentStore = useAgentStore()
const uiStore = useUIStore()

// 状态
const emergencyCancelling = ref(false)

// 方法
function getActionIcon(type) {
  const iconMap = {
    'TRADE_CONFIRMATION': { icon: 'mdi-swap-horizontal', color: 'primary' },
    'CLARIFICATION_NEEDED': { icon: 'mdi-help-circle', color: 'warning' },
    'RISK_APPROVAL': { icon: 'mdi-shield-alert', color: 'error' },
    'PARAMETER_CONFIRMATION': { icon: 'mdi-tune', color: 'info' }
  }
  return iconMap[type] || { icon: 'mdi-alert-circle', color: 'grey' }
}

function getActionTitle(type) {
  const titleMap = {
    'TRADE_CONFIRMATION': '交易确认',
    'CLARIFICATION_NEEDED': '需要澄清',
    'RISK_APPROVAL': '风险批准',
    'PARAMETER_CONFIRMATION': '参数确认'
  }
  return titleMap[type] || '待处理动作'
}

function getPriorityColor(priority) {
  const colorMap = {
    'high': 'error',
    'medium': 'warning',
    'low': 'info'
  }
  return colorMap[priority] || 'grey'
}

function getPriorityText(priority) {
  const textMap = {
    'high': '高优先级',
    'medium': '中优先级',
    'low': '低优先级'
  }
  return textMap[priority] || priority
}

function getConfidenceColor(confidence) {
  if (confidence >= 0.8) return 'success'
  if (confidence >= 0.6) return 'warning'
  return 'error'
}

function getRiskAlertType(level) {
  const typeMap = {
    'HIGH': 'error',
    'MEDIUM': 'warning',
    'LOW': 'info'
  }
  return typeMap[level] || 'info'
}

function formatNumber(value) {
  if (value === null || value === undefined) return '0'
  return parseFloat(value).toLocaleString()
}

function formatDateTime(dateTime) {
  if (!dateTime) return 'N/A'
  return new Date(dateTime).toLocaleString('zh-CN', {
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

async function approveAction(action) {
  const confirmed = await uiStore.showConfirmDialog({
    title: '确认批准',
    message: `确定要批准这个${getActionTitle(action.type)}吗？`,
    confirmText: '批准',
    cancelText: '取消'
  })
  
  if (!confirmed) return
  
  try {
    action.approving = true
    await agentStore.respondToPendingAction(action.id, 'approve')
    uiStore.showSuccess('动作已批准')
  } catch (error) {
    console.error('Failed to approve action:', error)
    uiStore.showError('批准失败: ' + error.message)
  } finally {
    action.approving = false
  }
}

async function rejectAction(action) {
  const confirmed = await uiStore.showConfirmDialog({
    title: '确认拒绝',
    message: `确定要拒绝这个${getActionTitle(action.type)}吗？`,
    confirmText: '拒绝',
    cancelText: '取消'
  })
  
  if (!confirmed) return
  
  try {
    action.rejecting = true
    await agentStore.respondToPendingAction(action.id, 'reject')
    uiStore.showSuccess('动作已拒绝')
  } catch (error) {
    console.error('Failed to reject action:', error)
    uiStore.showError('拒绝失败: ' + error.message)
  } finally {
    action.rejecting = false
  }
}

function isDirectionConfirmation(action) {
  // 检查是否是方向确认类型的动作
  return action.clarification_needed?.includes('交易方向') || 
         action.clarification_needed?.includes('做多还是做空') ||
         (action.details?.clarification_needed?.includes('方向') || 
         action.details?.clarification_needed?.includes('side')) ||
         (action.parsed_intent?.side === null && 
          action.parsed_intent?.intent_type === 'CREATE_ORDER');
}

async function confirmDirection(action, side) {
  const confirmed = await uiStore.showConfirmDialog({
    title: '确认方向',
    message: `确定要确认这个${side}吗？`,
    confirmText: '确认',
    cancelText: '取消'
  })
  
  if (!confirmed) return
  
  try {
    action.approving = true
    await agentStore.respondToPendingAction(action.id, 'approve', { side: side })
    uiStore.showSuccess('方向已确认')
  } catch (error) {
    console.error('Failed to confirm direction:', error)
    uiStore.showError('确认方向失败: ' + error.message)
  } finally {
    action.approving = false
  }
}

async function emergencyCancelAll() {
  const confirmed = await uiStore.showConfirmDialog({
    title: '紧急取消',
    message: '确定要紧急取消所有待处理的动作吗？这将拒绝所有未处理的交易确认和其他动作。',
    confirmText: '紧急取消',
    cancelText: '返回',
    confirmColor: 'error',
    icon: 'mdi-shield-alert'
  })
  
  if (!confirmed) return
  
  try {
    emergencyCancelling.value = true
    
    // 批量拒绝所有动作
    const actionIds = props.actions.map(action => action.id)
    await agentStore.rejectAllPendingActions(actionIds)
    
    uiStore.showSuccess('所有待处理动作已被取消', '紧急取消成功')
  } catch (error) {
    console.error('紧急取消失败:', error)
    uiStore.showError('紧急取消失败: ' + error.message)
  } finally {
    emergencyCancelling.value = false
  }
}
</script>

<style scoped>
.pending-action-card {
  transition: all 0.3s ease;
}

.pending-action-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

.pending-action-card.urgent {
  border-left: 4px solid rgb(var(--v-theme-error));
}

.font-weight-medium {
  font-weight: 500;
}

.emergency-button {
  border: 2px solid rgb(var(--v-theme-error));
  font-weight: bold;
  letter-spacing: 0.5px;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(var(--v-theme-error), 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(var(--v-theme-error), 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(var(--v-theme-error), 0);
  }
}
</style>