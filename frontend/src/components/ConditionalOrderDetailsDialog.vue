<template>
  <v-dialog
    v-model="dialogOpen"
    max-width="600px"
    @update:modelValue="$emit('update:open', $event)"
  >
    <v-card v-if="order">
      <v-card-title class="d-flex justify-space-between align-center">
        <div>
          <v-chip
            :color="getStatusColor(order.status)"
            size="small"
            variant="flat"
            class="mr-2"
          >
            {{ getStatusText(order.status) }}
          </v-chip>
          条件订单详情
        </div>
        <v-btn
          icon="mdi-close"
          variant="text"
          size="small"
          @click="closeDialog"
        ></v-btn>
      </v-card-title>
      
      <v-card-text>
        <v-list>
          <v-list-item>
            <template v-slot:prepend>
              <v-icon color="primary">mdi-identifier</v-icon>
            </template>
            <v-list-item-title>订单ID</v-list-item-title>
            <v-list-item-subtitle>{{ order.id }}</v-list-item-subtitle>
          </v-list-item>
          
          <v-divider></v-divider>
          
          <v-list-item>
            <template v-slot:prepend>
              <v-icon color="warning">mdi-alarm</v-icon>
            </template>
            <v-list-item-title>触发条件</v-list-item-title>
            <v-list-item-subtitle>
              <v-chip
                size="small"
                variant="outlined"
                class="mr-1"
              >
                {{ order.trigger_condition.symbol }}
              </v-chip>
              {{ getConditionText(order.trigger_condition) }}
            </v-list-item-subtitle>
          </v-list-item>
          
          <v-divider></v-divider>
          
          <v-list-item>
            <template v-slot:prepend>
              <v-icon color="info">mdi-play-circle</v-icon>
            </template>
            <v-list-item-title>行动计划</v-list-item-title>
            <v-list-item-subtitle>
              <v-chip
                :color="order.action_plan.side === 'buy' ? 'success' : 'error'"
                size="small"
                variant="flat"
                class="mr-1"
              >
                {{ order.action_plan.side === 'buy' ? '做多' : '做空' }}
              </v-chip>
              {{ order.action_plan.quantity }} {{ order.action_plan.symbol }} @ {{ order.action_plan.order_type }}
              {{ order.action_plan.price ? `价格: ${order.action_plan.price}` : '' }}
            </v-list-item-subtitle>
          </v-list-item>
          
          <v-divider></v-divider>
          
          <v-list-item>
            <template v-slot:prepend>
              <v-icon color="grey">mdi-calendar</v-icon>
            </template>
            <v-list-item-title>创建时间</v-list-item-title>
            <v-list-item-subtitle>{{ formatDateTime(order.created_at) }}</v-list-item-subtitle>
          </v-list-item>
          
          <v-divider></v-divider>
          
          <v-list-item v-if="order.expires_at">
            <template v-slot:prepend>
              <v-icon color="grey">mdi-clock-end</v-icon>
            </template>
            <v-list-item-title>过期时间</v-list-item-title>
            <v-list-item-subtitle>{{ formatDateTime(order.expires_at) }}</v-list-item-subtitle>
          </v-list-item>
          
          <v-divider v-if="order.triggered_at"></v-divider>
          
          <v-list-item v-if="order.triggered_at">
            <template v-slot:prepend>
              <v-icon color="success">mdi-bell-ring</v-icon>
            </template>
            <v-list-item-title>触发时间</v-list-item-title>
            <v-list-item-subtitle>{{ formatDateTime(order.triggered_at) }}</v-list-item-subtitle>
          </v-list-item>
          
          <v-divider v-if="order.result_order_id"></v-divider>
          
          <v-list-item v-if="order.result_order_id">
            <template v-slot:prepend>
              <v-icon color="success">mdi-link-variant</v-icon>
            </template>
            <v-list-item-title>关联订单</v-list-item-title>
            <v-list-item-subtitle class="d-flex align-center">
              {{ order.result_order_id }}
              <v-btn
                icon="mdi-open-in-new"
                variant="text"
                size="small"
                class="ml-2"
                @click="$emit('view-linked-order', order.result_order_id)"
              ></v-btn>
            </v-list-item-subtitle>
          </v-list-item>
        </v-list>
      </v-card-text>
      
      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn
          color="error"
          variant="outlined"
          :disabled="order.status !== 'PENDING'"
          @click="$emit('cancel', order)"
        >
          取消订单
        </v-btn>
        <v-btn
          color="primary"
          variant="outlined"
          @click="closeDialog"
        >
          关闭
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script setup>
import { ref, defineProps, defineEmits, watch } from 'vue';

const props = defineProps({
  open: {
    type: Boolean,
    default: false
  },
  order: {
    type: Object,
    default: null
  }
});

const emit = defineEmits(['update:open', 'cancel', 'view-linked-order']);

// 本地状态
const dialogOpen = ref(props.open);

// 监听外部更新
watch(() => props.open, (newVal) => {
  dialogOpen.value = newVal;
});

// 辅助方法
function getStatusColor(status) {
  const colors = {
    'PENDING': 'warning',
    'TRIGGERED': 'success',
    'CANCELLED': 'error',
    'EXPIRED': 'grey'
  };
  return colors[status] || 'grey';
}

function getStatusText(status) {
  const texts = {
    'PENDING': '等待触发',
    'TRIGGERED': '已触发',
    'CANCELLED': '已取消',
    'EXPIRED': '已过期'
  };
  return texts[status] || status;
}

function getConditionText(condition) {
  if (!condition) return '';
  
  const opText = {
    'gte': '≥',
    'lte': '≤',
    'gt': '>',
    'lt': '<'
  };
  
  return `价格 ${opText[condition.operator] || condition.operator} ${condition.price}`;
}

function formatDateTime(dateString) {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toLocaleString('zh-CN');
}

function closeDialog() {
  emit('update:open', false);
}
</script> 