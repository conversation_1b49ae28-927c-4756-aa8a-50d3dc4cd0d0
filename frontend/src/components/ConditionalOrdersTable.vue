<template>
  <v-card>
    <v-data-table
      :headers="headers"
      :items="orders"
      :search="search"
      :loading="loading"
      item-value="id"
      show-select
    >
      <!-- 状态列 -->
      <template v-slot:item.status="{ item }">
        <v-chip
          :color="getStatusColor(item.status)"
          size="small"
          variant="flat"
        >
          {{ getStatusText(item.status) }}
        </v-chip>
      </template>

      <!-- 触发条件列 -->
      <template v-slot:item.trigger_condition="{ item }">
        <div class="condition-display">
          <v-chip
            size="small"
            variant="outlined"
            class="mr-1"
          >
            {{ item.trigger_condition.symbol }}
          </v-chip>
          {{ getConditionText(item.trigger_condition) }}
        </div>
      </template>

      <!-- 行动计划列 -->
      <template v-slot:item.action_plan="{ item }">
        <v-chip
          :color="item.action_plan.side === 'buy' ? 'success' : 'error'"
          size="small"
          variant="flat"
          class="mr-1"
        >
          {{ item.action_plan.side === 'buy' ? '做多' : '做空' }}
        </v-chip>
        {{ item.action_plan.quantity }} {{ item.action_plan.symbol }} @ {{ item.action_plan.order_type }}
      </template>

      <!-- 创建时间列 -->
      <template v-slot:item.created_at="{ item }">
        {{ formatDate(item.created_at) }}
      </template>

      <!-- 操作列 -->
      <template v-slot:item.actions="{ item }">
        <div class="d-flex gap-2">
          <v-tooltip text="详情">
            <template v-slot:activator="{ props }">
              <v-btn
                icon="mdi-information"
                size="small"
                variant="text"
                v-bind="props"
                @click="$emit('view-details', item)"
              ></v-btn>
            </template>
          </v-tooltip>

          <v-tooltip text="取消订单">
            <template v-slot:activator="{ props }">
              <v-btn
                icon="mdi-cancel"
                size="small"
                variant="text"
                color="error"
                v-bind="props"
                @click="$emit('cancel-order', item)"
                :disabled="item.status !== 'PENDING'"
              ></v-btn>
            </template>
          </v-tooltip>
        </div>
      </template>
      
      <!-- 空数据 -->
      <template v-slot:no-data>
        <div class="text-center py-6">
          <v-icon size="48" color="grey" class="mb-4">mdi-clipboard-text-off</v-icon>
          <p class="text-h6 text-medium-emphasis">暂无条件订单</p>
          <v-btn
            color="primary"
            variant="outlined"
            prepend-icon="mdi-plus"
            class="mt-4"
            @click="$emit('create-new')"
          >
            创建条件订单
          </v-btn>
        </div>
      </template>
    </v-data-table>
  </v-card>
</template>

<script setup>
import { defineProps, defineEmits } from 'vue';

const props = defineProps({
  orders: {
    type: Array,
    required: true,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  },
  search: {
    type: String,
    default: ''
  }
});

const emit = defineEmits(['view-details', 'cancel-order', 'create-new']);

// 表头配置
const headers = [
  { title: '状态', key: 'status', width: '110px', sortable: true },
  { title: '交易对', key: 'trigger_condition.symbol', width: '120px', sortable: true },
  { title: '触发条件', key: 'trigger_condition', sortable: false },
  { title: '行动计划', key: 'action_plan', sortable: false },
  { title: '创建时间', key: 'created_at', width: '150px', sortable: true },
  { title: '操作', key: 'actions', width: '120px', sortable: false, align: 'end' }
];

// 辅助方法
function getStatusColor(status) {
  const colors = {
    'PENDING': 'warning',
    'TRIGGERED': 'success',
    'CANCELLED': 'error',
    'EXPIRED': 'grey'
  };
  return colors[status] || 'grey';
}

function getStatusText(status) {
  const texts = {
    'PENDING': '等待触发',
    'TRIGGERED': '已触发',
    'CANCELLED': '已取消',
    'EXPIRED': '已过期'
  };
  return texts[status] || status;
}

function getConditionText(condition) {
  if (!condition) return '';
  
  const opText = {
    'gte': '≥',
    'lte': '≤',
    'gt': '>',
    'lt': '<'
  };
  
  return `价格 ${opText[condition.operator] || condition.operator} ${condition.price}`;
}

function formatDate(dateString) {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toLocaleDateString('zh-CN');
}
</script> 