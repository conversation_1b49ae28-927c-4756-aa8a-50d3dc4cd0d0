<template>
  <div class="discord-card-content">
    <!-- 固定头部区域 -->
    <div class="discord-card-header">
      <!-- 消息头部 -->
      <div class="discord-header">
        <v-avatar size="40" class="discord-avatar">
          <img
            v-if="authorAvatar"
            :src="authorAvatar"
            :alt="signal.author_name"
            @error="handleAvatarError"
          >
          <v-icon v-else color="white" size="20">mdi-account-circle</v-icon>
        </v-avatar>

        <div class="discord-author-info">
          <div class="discord-author-line">
            <span class="discord-author-name">{{ signal.author_name || 'Unknown User' }}</span>
            <span v-if="isBot" class="discord-bot-tag">BOT</span>
            <span class="discord-timestamp">{{ formatTimestamp(signal.created_at) }}</span>
          </div>
          <div v-if="signal.channel_name" class="discord-channel-info">
            <v-icon size="12" class="mr-1">mdi-pound</v-icon>
            <span class="discord-channel-name">{{ signal.channel_name }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 可滚动内容区域 -->
    <div class="discord-card-scrollable">
      <!-- 消息内容 -->
      <div class="discord-content">
        <!-- 文本内容 -->
        <div v-if="signal.content" class="discord-text" v-html="formatDiscordText(signal.content)"></div>

        <!-- Embeds 预览 -->
        <div v-if="hasEmbeds" class="discord-embeds-preview">
          <div
            v-for="(embed, index) in embedsPreview"
            :key="index"
            class="discord-embed-preview"
            :style="{ borderLeftColor: embed.color || '#202225' }"
          >
            <div v-if="embed.author" class="discord-embed-author">
              <img v-if="embed.author.icon_url" :src="embed.author.icon_url" class="discord-embed-author-icon">
              <span class="discord-embed-author-name">{{ embed.author.name }}</span>
            </div>

            <div v-if="embed.title" class="discord-embed-title">{{ embed.title }}</div>

            <div v-if="embed.description" class="discord-embed-description">
              {{ truncateText(embed.description, 100) }}
            </div>

            <div v-if="embed.fields && embed.fields.length" class="discord-embed-fields">
              <div
                v-for="(field, fieldIndex) in embed.fields.slice(0, 2)"
                :key="fieldIndex"
                class="discord-embed-field"
              >
                <div class="discord-field-name">{{ field.name }}</div>
                <div class="discord-field-value">{{ truncateText(field.value, 50) }}</div>
              </div>
            </div>

            <div v-if="embed.image" class="discord-embed-image">
              <img :src="embed.image.url" class="discord-embed-image-content" @click="openImageModal(embed.image.url)">
            </div>
          </div>

          <!-- 更多embeds指示器 -->
          <div v-if="embedsCount > embedsPreview.length" class="discord-more-embeds">
            <v-chip size="x-small" color="indigo" variant="outlined">
              <v-icon left size="x-small">mdi-plus</v-icon>
              还有 {{ embedsCount - embedsPreview.length }} 个嵌入
            </v-chip>
          </div>
        </div>

        <!-- 附件预览 -->
        <div v-if="hasAttachments" class="discord-attachments-preview">
          <div
            v-for="(attachment, index) in attachmentsPreview"
            :key="index"
            class="discord-attachment-preview"
          >
            <v-icon size="16" class="mr-2">{{ getFileIcon(attachment.filename) }}</v-icon>
            <span class="discord-attachment-name">{{ attachment.filename }}</span>
            <span class="discord-attachment-size">({{ formatFileSize(attachment.size) }})</span>
          </div>

          <!-- 更多附件指示器 -->
          <div v-if="attachmentsCount > attachmentsPreview.length" class="discord-more-attachments">
            <v-chip size="x-small" color="blue" variant="outlined">
              <v-icon left size="x-small">mdi-plus</v-icon>
              还有 {{ attachmentsCount - attachmentsPreview.length }} 个附件
            </v-chip>
          </div>
        </div>

        <!-- 反应预览 -->
        <div v-if="hasReactions" class="discord-reactions-preview">
          <v-chip
            v-for="(reaction, index) in reactionsPreview"
            :key="index"
            size="x-small"
            variant="outlined"
            class="discord-reaction-chip"
          >
            <span class="discord-reaction-emoji">{{ reaction.emoji }}</span>
            <span class="discord-reaction-count">{{ reaction.count }}</span>
          </v-chip>

          <!-- 更多反应指示器 -->
          <div v-if="reactionsCount > reactionsPreview.length" class="discord-more-reactions">
            <v-chip size="x-small" color="orange" variant="outlined">
              <v-icon left size="x-small">mdi-plus</v-icon>
              还有 {{ reactionsCount - reactionsPreview.length }} 个反应
            </v-chip>
          </div>
        </div>

        <!-- 统计信息 -->
        <div class="discord-stats">
          <div class="discord-stats-item">
            <v-icon size="12" class="mr-1">mdi-clock-outline</v-icon>
            <span class="discord-stats-text">{{ formatRelativeTime(signal.created_at) }}</span>
          </div>

          <div v-if="signal.confidence !== null" class="discord-stats-item">
            <v-icon size="12" class="mr-1">mdi-chart-line</v-icon>
            <span class="discord-stats-text">{{ (signal.confidence * 100).toFixed(0) }}%</span>
          </div>
        </div>

        <!-- 状态标签区域 -->
        <div class="discord-status-tags">
          <!-- 平台标签 -->
          <v-chip
            size="x-small"
            :color="getPlatformColor(signal.platform)"
            variant="outlined"
            class="discord-status-tag"
          >
            <v-icon left size="x-small">{{ getPlatformIcon(signal.platform) }}</v-icon>
            {{ getPlatformName(signal.platform) }}
          </v-chip>

          <!-- AI解析状态标签 -->
          <v-chip
            v-if="signal.ai_parse_status"
            size="x-small"
            :color="getAIStatusColor(signal.ai_parse_status)"
            variant="outlined"
            class="discord-status-tag"
          >
            <v-icon left size="x-small">{{ getAIStatusIcon(signal.ai_parse_status) }}</v-icon>
            {{ getAIStatusText(signal.ai_parse_status) }}
          </v-chip>

          <!-- 消息类型标签 -->
          <v-chip
            v-if="signal.message_type_ai"
            size="x-small"
            :color="getMessageTypeColor(signal.message_type_ai)"
            variant="outlined"
            class="discord-status-tag"
          >
            <v-icon left size="x-small">{{ getMessageTypeIcon(signal.message_type_ai) }}</v-icon>
            {{ getMessageTypeText(signal.message_type_ai) }}
          </v-chip>

          <!-- LLM服务标签 -->
          <v-chip
            v-if="signal.llm_service"
            size="x-small"
            :color="getLLMServiceColor(signal.llm_service)"
            variant="outlined"
            class="discord-status-tag"
          >
            <v-icon left size="x-small">{{ getLLMServiceIcon(signal.llm_service) }}</v-icon>
            {{ getLLMServiceText(signal.llm_service) }}
          </v-chip>

          <!-- 处理状态标签 -->
          <v-chip
            size="x-small"
            :color="signal.is_processed ? 'success' : 'warning'"
            variant="outlined"
            class="discord-status-tag"
          >
            <v-icon left size="x-small">
              {{ signal.is_processed ? 'mdi-check-circle' : 'mdi-clock-outline' }}
            </v-icon>
            {{ signal.is_processed ? '已处理' : '未处理' }}
          </v-chip>

          <!-- 置信度标签 -->
          <v-chip
            v-if="signal.confidence !== null"
            size="x-small"
            :color="getConfidenceColor(signal.confidence)"
            variant="outlined"
            class="discord-status-tag"
          >
            <v-icon left size="x-small">mdi-chart-line</v-icon>
            {{ (signal.confidence * 100).toFixed(0) }}%
          </v-chip>
        </div>
      </div>
    </div>

    <!-- 图片模态框 -->
    <v-dialog v-model="imageModal" max-width="800">
      <v-card>
        <v-card-text class="pa-0">
          <img :src="selectedImage" style="width: 100%; height: auto;">
        </v-card-text>
        <v-card-actions>
          <v-spacer />
          <v-btn @click="imageModal = false">关闭</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'

// Props
const props = defineProps({
  signal: {
    type: Object,
    required: true
  }
})

// 响应式数据
const imageModal = ref(false)
const selectedImage = ref('')
const avatarError = ref(false)

// 计算属性
const discordMetadata = computed(() => {
  return props.signal.metadata?.discord || {}
})

const authorAvatar = computed(() => {
  if (avatarError.value) return null
  return discordMetadata.value.author_avatar || null
})

const isBot = computed(() => {
  return discordMetadata.value.is_bot || false
})

const hasEmbeds = computed(() => {
  return discordMetadata.value.embeds && discordMetadata.value.embeds.length > 0
})

const embedsPreview = computed(() => {
  return discordMetadata.value.embeds?.slice(0, 2) || []
})

const embedsCount = computed(() => {
  return discordMetadata.value.embeds?.length || 0
})

const hasAttachments = computed(() => {
  return discordMetadata.value.attachments && discordMetadata.value.attachments.length > 0
})

const attachmentsPreview = computed(() => {
  return discordMetadata.value.attachments?.slice(0, 3) || []
})

const attachmentsCount = computed(() => {
  return discordMetadata.value.attachments?.length || 0
})

const hasReactions = computed(() => {
  return discordMetadata.value.reactions && discordMetadata.value.reactions.length > 0
})

const reactionsPreview = computed(() => {
  return discordMetadata.value.reactions?.slice(0, 5) || []
})

const reactionsCount = computed(() => {
  return discordMetadata.value.reactions?.length || 0
})

// 方法
const formatTimestamp = (timestamp) => {
  return new Date(timestamp).toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit'
  })
}

const formatRelativeTime = (timestamp) => {
  const now = new Date()
  const date = new Date(timestamp)
  const diffInSeconds = Math.floor((now - date) / 1000)

  if (diffInSeconds < 60) {
    return '刚刚'
  } else if (diffInSeconds < 3600) {
    const minutes = Math.floor(diffInSeconds / 60)
    return `${minutes}分钟前`
  } else if (diffInSeconds < 86400) {
    const hours = Math.floor(diffInSeconds / 3600)
    return `${hours}小时前`
  } else if (diffInSeconds < 2592000) {
    const days = Math.floor(diffInSeconds / 86400)
    return `${days}天前`
  } else {
    return date.toLocaleDateString('zh-CN')
  }
}

const formatDiscordText = (content) => {
  if (!content) return ''

  let formatted = content

  // Discord markdown 格式化
  // 粗体 **text** 或 __text__
  formatted = formatted.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
  formatted = formatted.replace(/__(.*?)__/g, '<strong>$1</strong>')

  // 斜体 *text* 或 _text_
  formatted = formatted.replace(/\*(.*?)\*/g, '<em>$1</em>')
  formatted = formatted.replace(/_(.*?)_/g, '<em>$1</em>')

  // 删除线 ~~text~~
  formatted = formatted.replace(/~~(.*?)~~/g, '<del>$1</del>')

  // 代码块 `code`
  formatted = formatted.replace(/`([^`]+)`/g, '<code>$1</code>')

  // 代码块 ```code```
  formatted = formatted.replace(/```([\s\S]*?)```/g, '<pre><code>$1</code></pre>')

  // 链接 [text](url)
  formatted = formatted.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" target="_blank">$1</a>')

  // 用户提及 <@userid>
  formatted = formatted.replace(/<@!?(\d+)>/g, '<span class="mention">@用户</span>')

  // 频道提及 <#channelid>
  formatted = formatted.replace(/<#(\d+)>/g, '<span class="mention">#频道</span>')

  // 角色提及 <@&roleid>
  formatted = formatted.replace(/<@&(\d+)>/g, '<span class="mention">@角色</span>')

  // 表情符号 <:name:id> - 使用真实的Discord CDN图片
  formatted = formatted.replace(/<:(.*?):(\d+)>/g, '<img src="https://cdn.discordapp.com/emojis/$2.png" alt=":$1:" class="emoji">')

  // 动画表情符号 <a:name:id> - 使用真实的Discord CDN GIF
  formatted = formatted.replace(/<a:(.*?):(\d+)>/g, '<img src="https://cdn.discordapp.com/emojis/$2.gif" alt=":$1:" class="emoji animated">')

  // 换行
  formatted = formatted.replace(/\n/g, '<br>')

  return formatted
}

const truncateText = (text, maxLength) => {
  if (!text) return ''
  if (text.length <= maxLength) return text
  return text.substring(0, maxLength) + '...'
}

const getFileIcon = (filename) => {
  const extension = filename.toLowerCase().split('.').pop()
  
  const iconMap = {
    pdf: 'mdi-file-pdf-box',
    doc: 'mdi-file-word-box',
    docx: 'mdi-file-word-box',
    xls: 'mdi-file-excel-box',
    xlsx: 'mdi-file-excel-box',
    jpg: 'mdi-file-image',
    jpeg: 'mdi-file-image',
    png: 'mdi-file-image',
    gif: 'mdi-file-image',
    mp4: 'mdi-file-video',
    mp3: 'mdi-file-music',
    txt: 'mdi-file-document'
  }
  
  return iconMap[extension] || 'mdi-file'
}

const formatFileSize = (bytes) => {
  if (!bytes) return '0 B'
  
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(1024))
  
  return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
}

const openImageModal = (imageUrl) => {
  selectedImage.value = imageUrl
  imageModal.value = true
}

const handleAvatarError = () => {
  avatarError.value = true
}

// 状态标签相关方法
const getPlatformColor = (platform) => {
  const colors = {
    discord: 'indigo',
    telegram: 'blue',
    manual: 'green'
  }
  return colors[platform] || 'default'
}

const getPlatformIcon = (platform) => {
  const icons = {
    discord: 'mdi-discord',
    telegram: 'mdi-telegram',
    manual: 'mdi-pencil'
  }
  return icons[platform] || 'mdi-help-circle'
}

const getPlatformName = (platform) => {
  const names = {
    discord: 'Discord',
    telegram: 'Telegram',
    manual: '手动'
  }
  return names[platform] || platform
}

const getAIStatusColor = (status) => {
  const colors = {
    pending: 'warning',
    success: 'success',
    failed: 'error',
    partial: 'info'
  }
  return colors[status] || 'default'
}

const getAIStatusIcon = (status) => {
  const icons = {
    pending: 'mdi-clock-outline',
    success: 'mdi-check-circle',
    failed: 'mdi-alert-circle',
    partial: 'mdi-progress-check'
  }
  return icons[status] || 'mdi-help-circle'
}

const getAIStatusText = (status) => {
  const texts = {
    pending: '待解析',
    success: '解析成功',
    failed: '解析失败',
    partial: '部分解析'
  }
  return texts[status] || status
}

const getMessageTypeColor = (type) => {
  const colors = {
    normal_message: 'grey',
    trading_signal: 'success',
    market_analysis: 'info',
    price_alert: 'warning',
    ambiguous: 'orange'
  }
  return colors[type] || 'default'
}

const getMessageTypeIcon = (type) => {
  const icons = {
    normal_message: 'mdi-message-text',
    trading_signal: 'mdi-chart-line',
    market_analysis: 'mdi-chart-areaspline',
    price_alert: 'mdi-bell-alert',
    ambiguous: 'mdi-help-circle'
  }
  return icons[type] || 'mdi-message'
}

const getMessageTypeText = (type) => {
  const texts = {
    normal_message: '普通消息',
    trading_signal: '交易信号',
    market_analysis: '市场分析',
    price_alert: '价格提醒',
    ambiguous: '模糊信号'
  }
  return texts[type] || type
}

const getLLMServiceColor = (service) => {
  const colors = {
    deepseek: 'deep-purple',
    gemini: 'blue',
    chatgpt: 'green',
    claude: 'orange'
  }
  return colors[service] || 'default'
}

const getLLMServiceIcon = (service) => {
  const icons = {
    deepseek: 'mdi-brain',
    gemini: 'mdi-google',
    chatgpt: 'mdi-robot',
    claude: 'mdi-account-circle'
  }
  return icons[service] || 'mdi-robot-outline'
}

const getLLMServiceText = (service) => {
  const texts = {
    deepseek: 'DeepSeek',
    gemini: 'Gemini',
    chatgpt: 'ChatGPT',
    claude: 'Claude'
  }
  return texts[service] || service
}

const getConfidenceColor = (confidence) => {
  if (confidence >= 0.8) return 'success'
  if (confidence >= 0.6) return 'warning'
  if (confidence >= 0.4) return 'orange'
  return 'error'
}
</script>

<style scoped>
/* Discord 字体导入 */
@import url('https://fonts.googleapis.com/css2?family=Whitney:wght@400;500;600;700&display=swap');

/* Discord 卡片内容样式 */
.discord-card-content {
  font-family: 'Whitney', 'gg sans', 'Noto Sans', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  font-size: 16px;
  line-height: 1.375;
  border-radius: 8px;
  border-left: 4px solid #5865f2;
  position: relative;
  height: 220px; /* 固定高度 220px */
  display: flex;
  flex-direction: column;
  overflow: hidden;
  /* Discord 深色主题（默认） */
  color: #dcddde;
  background: #36393f;
  border: 1px solid #40444b;
}

/* 卡片头部 - 固定不滚动 */
.discord-card-header {
  flex-shrink: 0;
  padding: 12px 16px 8px 16px;
  border-bottom: 1px solid rgba(79, 84, 92, 0.48);
}

/* 可滚动内容区域 */
.discord-card-scrollable {
  flex: 1;
  overflow-y: auto;
  padding: 8px 16px 16px 16px;
}

/* Discord风格滚动条 - 优化版本 */
.discord-card-scrollable {
  /* 滚动条容器优化 */
  scrollbar-width: thin; /* Firefox 支持 */
  scrollbar-color: rgba(32, 34, 37, 0.6) transparent; /* Firefox 滚动条颜色 */
}

.discord-card-scrollable::-webkit-scrollbar {
  width: 8px; /* 减少宽度，更加精细 */
}

.discord-card-scrollable::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 8px; /* 与卡片圆角保持一致 */
}

.discord-card-scrollable::-webkit-scrollbar-thumb {
  background: rgba(32, 34, 37, 0.4); /* 默认状态更加低调 */
  border-radius: 8px; /* 与卡片设计语言一致 */
  border: 2px solid transparent; /* 减少边框宽度 */
  background-clip: content-box; /* 边框不显示背景 */
  min-height: 32px; /* 减少最小高度 */
  transition: all 0.2s ease; /* 添加平滑过渡 */
}

.discord-card-scrollable::-webkit-scrollbar-thumb:hover {
  background: rgba(32, 34, 37, 0.7); /* hover时增加可见度 */
  transform: scaleX(1.2); /* 轻微放大效果 */
}

.discord-card-scrollable::-webkit-scrollbar-thumb:active {
  background: rgba(32, 34, 37, 0.9); /* active时最高可见度 */
}

/* Discord 深色主题（默认） - 合并到主样式中 */

/* Discord 浅色主题适配 */
.v-theme--light .discord-card-content {
  color: #2e3338;
  background: #ffffff;
  border: 1px solid #e3e5e8;
  border-left-color: #5865f2;
}

/* 浅色主题滚动条样式 */
.v-theme--light .discord-card-scrollable {
  scrollbar-color: rgba(0, 0, 0, 0.2) transparent; /* Firefox 浅色主题 */
}

.v-theme--light .discord-card-scrollable::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.1); /* 浅色主题默认状态更加低调 */
}

.v-theme--light .discord-card-scrollable::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.2); /* hover时增加可见度 */
}

.v-theme--light .discord-card-scrollable::-webkit-scrollbar-thumb:active {
  background: rgba(0, 0, 0, 0.3); /* active时最高可见度 */
}

/* 消息头部 */
.discord-header {
  display: flex;
  align-items: flex-start;
  margin-bottom: 8px;
  position: relative;
}

.discord-avatar {
  margin-right: 16px;
  flex-shrink: 0;
  position: relative;
}

.discord-avatar .v-avatar {
  border-radius: 50%;
  overflow: hidden;
}

.discord-author-info {
  flex: 1;
  min-width: 0;
}

.discord-author-line {
  display: flex;
  align-items: baseline;
  margin-bottom: 4px;
  flex-wrap: wrap;
  gap: 8px;
}

.discord-author-name {
  font-weight: 600;
  font-size: 16px;
  color: #ffffff;
  margin-right: 8px;
  cursor: pointer;
  transition: text-decoration 0.1s ease;
}

.discord-author-name:hover {
  text-decoration: underline;
}

.v-theme--light .discord-author-name {
  color: #060607;
}

.discord-bot-tag {
  background-color: #5865f2;
  color: #ffffff;
  font-size: 10px;
  font-weight: 500;
  padding: 2px 4px;
  border-radius: 3px;
  text-transform: uppercase;
  margin-right: 8px;
  vertical-align: baseline;
  line-height: 1.2;
}

.discord-timestamp {
  font-size: 12px;
  color: #a3a6aa;
  font-weight: 500;
  margin-left: auto;
  white-space: nowrap;
  cursor: default;
}

.discord-timestamp:hover {
  color: #dcddde;
}

.v-theme--light .discord-timestamp {
  color: #747f8d;
}

.v-theme--light .discord-timestamp:hover {
  color: #2e3338;
}

.discord-channel-info {
  display: flex;
  align-items: center;
  font-size: 11px;
  color: #b9bbbe;
}

.v-theme--light .discord-channel-info {
  color: #4e5058;
}

.discord-channel-name {
  color: #ffffff;
  font-weight: 500;
}

.v-theme--light .discord-channel-name {
  color: #060607;
}

/* 消息内容 */
.discord-content {
  margin-left: 56px; /* 对齐40px头像右侧 (40px + 16px margin) */
  margin-bottom: 8px;
  position: relative;
}

.discord-text {
  font-size: 16px;
  line-height: 1.375;
  color: #dcddde;
  margin-bottom: 8px;
  word-wrap: break-word;
  white-space: pre-wrap;
  font-weight: 400;
}

.v-theme--light .discord-text {
  color: #2e3338;
}

/* Discord 格式化样式 */
.discord-text :deep(strong) {
  font-weight: 700;
}

.discord-text :deep(em) {
  font-style: italic;
}

.discord-text :deep(del) {
  text-decoration: line-through;
}

.discord-text :deep(code) {
  background-color: #2f3136;
  color: #dcddde;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 14px;
}

.v-theme--light .discord-text :deep(code) {
  background-color: #f2f3f5;
  color: #2e3338;
}

/* Discord 提及和表情样式 - 简化版本 */
.discord-text :deep(.mention) {
  background-color: rgba(88, 101, 242, 0.3);
  color: #dee0fc;
  padding: 0 2px;
  border-radius: 3px;
  font-weight: 500;
}

.discord-text :deep(.emoji) {
  width: 22px;
  height: 22px;
  vertical-align: -0.2em;
  margin: 0 1px;
}

.discord-text :deep(.emoji.animated) {
  /* 动画表情符号的特殊样式 */
}

/* 浅色主题适配 */
.v-theme--light .discord-text :deep(.mention) {
  background-color: rgba(88, 101, 242, 0.15);
  color: #5865f2;
}

/* Embeds 预览 */
.discord-embeds-preview {
  margin: 6px 0;
}

.discord-embed-preview {
  background-color: #2f3136;
  border-left: 4px solid #5865f2;
  border-radius: 4px;
  padding: 16px;
  margin: 8px 0;
  max-width: 520px;
  position: relative;
}

.v-theme--light .discord-embed-preview {
  background-color: #f2f3f5;
  border-left-color: #5865f2;
}

.discord-embed-author {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
}

.discord-embed-author-icon {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  margin-right: 4px;
}

.discord-embed-author-name {
  font-size: 13px;
  font-weight: 600;
  color: #ffffff;
  cursor: pointer;
}

.discord-embed-author-name:hover {
  text-decoration: underline;
}

.discord-embed-title {
  font-size: 16px;
  font-weight: 600;
  color: #00aff4;
  margin-bottom: 8px;
  cursor: pointer;
  line-height: 1.2;
}

.discord-embed-title:hover {
  text-decoration: underline;
}

.discord-embed-description {
  font-size: 14px;
  line-height: 1.375;
  color: #dcddde;
  margin-bottom: 8px;
  word-wrap: break-word;
}

.discord-embed-fields {
  display: grid;
  grid-template-columns: 1fr;
  gap: 8px;
  margin-bottom: 8px;
}

.discord-embed-field {
  min-width: 0;
}

.discord-field-name {
  font-size: 14px;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 2px;
}

.discord-field-value {
  font-size: 14px;
  color: #dcddde;
  line-height: 1.375;
  word-wrap: break-word;
}

/* 嵌入图片样式 */
.discord-embed-image {
  margin-top: 8px;
}

.discord-embed-image-content {
  max-width: 100%;
  max-height: 300px;
  border-radius: 4px;
  cursor: pointer;
  transition: opacity 0.2s ease;
}

.discord-embed-image-content:hover {
  opacity: 0.9;
}

/* 浅色主题嵌入样式 */
.v-theme--light .discord-embed-author-name,
.v-theme--light .discord-field-name {
  color: #2e3338;
}

.v-theme--light .discord-embed-title {
  color: #0099cc;
}

.v-theme--light .discord-embed-description,
.v-theme--light .discord-field-value {
  color: #4e5058;
}

/* 附件预览 */
.discord-attachments-preview {
  margin: 8px 0;
}

.discord-attachment-preview {
  display: flex;
  align-items: center;
  background-color: #2f3136;
  border: 1px solid #40444b;
  border-radius: 4px;
  padding: 8px;
  margin: 4px 0;
  transition: background-color 0.2s ease;
  cursor: pointer;
}

.discord-attachment-preview:hover {
  background-color: #36393f;
}

.discord-attachment-name {
  color: #00aff4;
  margin-right: 8px;
  font-size: 14px;
  font-weight: 500;
  text-decoration: none;
}

.discord-attachment-name:hover {
  text-decoration: underline;
}

.discord-attachment-size {
  color: #a3a6aa;
  font-size: 12px;
  margin-left: auto;
}

/* 浅色主题附件样式 */
.v-theme--light .discord-attachment-preview {
  background-color: #f2f3f5;
  border-color: #e3e5e8;
}

.v-theme--light .discord-attachment-preview:hover {
  background-color: #e8eaed;
}

.v-theme--light .discord-attachment-name {
  color: #0099cc;
}

.v-theme--light .discord-attachment-size {
  color: #747f8d;
}

/* 反应预览 */
.discord-reactions-preview {
  margin: 8px 0;
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.discord-reaction-chip {
  background-color: rgba(79, 84, 92, 0.16) !important;
  border: 1px solid rgba(79, 84, 92, 0.3) !important;
  border-radius: 8px !important;
  padding: 2px 6px !important;
  height: auto !important;
  min-height: 24px !important;
  transition: all 0.1s ease !important;
  cursor: pointer !important;
}

.discord-reaction-chip:hover {
  background-color: rgba(79, 84, 92, 0.3) !important;
  border-color: rgba(79, 84, 92, 0.5) !important;
}

.discord-reaction-emoji {
  margin-right: 4px;
  font-size: 14px;
}

.discord-reaction-count {
  font-size: 12px;
  color: #b9bbbe;
  font-weight: 500;
}

/* 浅色主题反应样式 */
.v-theme--light .discord-reaction-chip {
  background-color: rgba(116, 127, 141, 0.1) !important;
  border-color: rgba(116, 127, 141, 0.2) !important;
}

.v-theme--light .discord-reaction-chip:hover {
  background-color: rgba(116, 127, 141, 0.2) !important;
  border-color: rgba(116, 127, 141, 0.3) !important;
}

.v-theme--light .discord-reaction-count {
  color: #4e5058;
}

/* 更多指示器 */
.discord-more-embeds,
.discord-more-attachments,
.discord-more-reactions {
  margin: 4px 0;
}

/* 统计信息 */
.discord-stats {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 8px;
  padding-top: 6px;
  border-top: 1px solid rgba(79, 84, 92, 0.3);
}

.discord-stats-item {
  display: flex;
  align-items: center;
  font-size: 11px;
  color: #72767d;
}

.discord-stats-text {
  font-weight: 500;
}

/* 状态标签区域 */
.discord-status-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid #40444b;
}

.v-theme--light .discord-status-tags {
  border-top-color: #e3e5e8;
}

.discord-status-tag {
  font-size: 10px !important;
  height: 20px !important;
  background-color: rgba(79, 84, 92, 0.3) !important;
  border-color: #5865f2 !important;
}

.v-theme--light .discord-status-tag {
  background-color: rgba(227, 229, 232, 0.3) !important;
}

.discord-status-tag :deep(.v-chip__content) {
  font-weight: 500;
  color: #dcddde !important;
}

.v-theme--light .discord-status-tag :deep(.v-chip__content) {
  color: #2e3338 !important;
}

.discord-status-tag :deep(.v-icon) {
  color: #dcddde !important;
}

.v-theme--light .discord-status-tag :deep(.v-icon) {
  color: #2e3338 !important;
}

/* 响应式设计 - 简化版本 */
@media (max-width: 768px) {
  .discord-content {
    margin-left: 0; /* 移动端不缩进 */
  }

  .discord-embed-preview {
    max-width: 100%;
  }
}
</style>
