// 简化的主题选择器 - 只保留实际使用的功能

/**
 * 根据平台获取对应的内容组件
 * @param {string} platform - 平台名称
 * @returns {string} 组件名称
 */
export function getContentComponent(platform) {
  switch (platform) {
    case 'discord':
      return 'DiscordCardContent'
    default:
      return 'GenericCardContent'
  }
}

/**
 * 根据平台获取主题样式类
 * @param {string} platform - 平台名称
 * @returns {string} CSS类名
 */
export function getThemeClass(platform) {
  switch (platform) {
    case 'discord':
      return 'signal-card--discord'
    default:
      return 'signal-card--generic'
  }
}
