<template>
  <div class="message-renderer">
    <!-- Discord消息渲染 -->
    <DiscordMessage
      v-if="signal.platform === 'discord'"
      :signal="signal"
      :initial-render-mode="renderMode"
    />

    <!-- Telegram消息渲染 (未来扩展) -->
    <TelegramMessage
      v-else-if="signal.platform === 'telegram'"
      :signal="signal"
      :initial-render-mode="renderMode"
    />

    <!-- 通用消息渲染 -->
    <GenericMessage
      v-else
      :signal="signal"
      :render-mode="renderMode"
    />
  </div>
</template>

<script setup>
import { computed } from 'vue'
import DiscordMessage from './DiscordMessage.vue'
// import TelegramMessage from './TelegramMessage.vue' // 未来实现
import GenericMessage from './GenericMessage.vue'

// Props
const props = defineProps({
  signal: {
    type: Object,
    required: true
  },
  renderMode: {
    type: String,
    default: 'auto' // auto, discord, telegram, raw
  }
})

// 计算属性
const actualRenderMode = computed(() => {
  if (props.renderMode === 'auto') {
    // 根据平台自动选择渲染模式
    switch (props.signal.platform) {
      case 'discord':
        return 'discord'
      case 'telegram':
        return 'telegram'
      default:
        return 'generic'
    }
  }
  return props.renderMode
})
</script>

<style scoped>
.message-renderer {
  width: 100%;
}
</style>