<template>
  <div class="discord-message">
    <!-- 原生Discord样式模式 -->
    <div v-if="renderMode === 'discord'" class="discord-native">
      <!-- 回复消息 -->
      <div v-if="replyTo" class="reply-reference">
        <div class="reply-line"></div>
        <div class="reply-content">
          <v-icon size="small" class="reply-icon">mdi-reply</v-icon>
          <span class="reply-author">{{ replyTo.author }}</span>
          <span class="reply-text">{{ truncateText(replyTo.content, 50) }}</span>
        </div>
      </div>

      <!-- 消息头部 -->
      <div class="message-header">
        <v-avatar size="40" class="mr-3">
          <img
            v-if="authorAvatar"
            :src="authorAvatar"
            :alt="signal.author_name"
            @error="handleAvatarError"
          >
          <v-icon v-else color="primary">mdi-account-circle</v-icon>
        </v-avatar>
        <div class="header-content">
          <div class="author-line">
            <span class="author-name">{{ signal.author_name || 'Unknown User' }}</span>
            <span v-if="isBot" class="bot-tag">BOT</span>
            <span class="timestamp">{{ formatTimestamp(signal.created_at) }}</span>
          </div>
          <div v-if="guildInfo" class="guild-info">
            <v-icon size="small" class="mr-1">mdi-pound</v-icon>
            <span class="channel-name">{{ signal.channel_name }}</span>
            <span class="guild-separator">•</span>
            <span class="guild-name">{{ guildInfo.guild_name }}</span>
          </div>
        </div>
      </div>

      <!-- 消息内容 -->
      <div class="message-content">
        <!-- 文本内容 -->
        <div v-if="signal.content" class="text-content">
          <div v-html="formatDiscordContent(signal.content)"></div>
        </div>

        <!-- Discord Embeds -->
        <div v-if="discordEmbeds.length" class="embeds">
          <div
            v-for="(embed, index) in discordEmbeds"
            :key="`embed-${index}`"
            class="embed"
            :style="{ borderLeftColor: getEmbedColor(embed.color) }"
          >
            <!-- Embed 作者 -->
            <div v-if="embed.author" class="embed-author">
              <img
                v-if="embed.author.icon_url"
                :src="embed.author.icon_url"
                :alt="embed.author.name"
                class="embed-author-icon"
              >
              <a
                v-if="embed.author.url"
                :href="embed.author.url"
                target="_blank"
                class="embed-author-name"
              >
                {{ embed.author.name }}
              </a>
              <span v-else class="embed-author-name">{{ embed.author.name }}</span>
            </div>

            <!-- Embed 标题 -->
            <div v-if="embed.title" class="embed-title">
              <a
                v-if="embed.url"
                :href="embed.url"
                target="_blank"
                class="embed-title-link"
              >
                {{ embed.title }}
              </a>
              <span v-else>{{ embed.title }}</span>
            </div>

            <!-- Embed 描述 -->
            <div v-if="embed.description" class="embed-description">
              <div v-html="formatDiscordContent(embed.description)"></div>
            </div>

            <!-- Embed 字段 -->
            <div v-if="embed.fields?.length" class="embed-fields">
              <div
                v-for="(field, fieldIndex) in embed.fields"
                :key="`field-${fieldIndex}`"
                class="embed-field"
                :class="{ 'inline': field.inline }"
              >
                <div class="field-name">{{ field.name }}</div>
                <div class="field-value" v-html="formatDiscordContent(field.value)"></div>
              </div>
            </div>

            <!-- Embed 图片 -->
            <div v-if="embed.image" class="embed-image">
              <img
                :src="embed.image.url"
                :alt="embed.title || 'Embed image'"
                class="embed-image-content"
                @click="openImageModal(embed.image.url)"
              >
            </div>

            <!-- Embed 缩略图 -->
            <div v-if="embed.thumbnail" class="embed-thumbnail">
              <img
                :src="embed.thumbnail.url"
                :alt="embed.title || 'Embed thumbnail'"
                class="embed-thumbnail-content"
              >
            </div>

            <!-- Embed 页脚 -->
            <div v-if="embed.footer" class="embed-footer">
              <img
                v-if="embed.footer.icon_url"
                :src="embed.footer.icon_url"
                :alt="embed.footer.text"
                class="embed-footer-icon"
              >
              <span class="embed-footer-text">{{ embed.footer.text }}</span>
              <span v-if="embed.timestamp" class="embed-timestamp">
                • {{ formatTimestamp(embed.timestamp) }}
              </span>
            </div>
          </div>
        </div>

        <!-- 附件 -->
        <div v-if="discordAttachments.length" class="attachments">
          <div
            v-for="attachment in discordAttachments"
            :key="attachment.id"
            class="attachment"
          >
            <!-- 图片附件 -->
            <div v-if="isImage(attachment.filename)" class="attachment-image">
              <img
                :src="attachment.url"
                :alt="attachment.filename"
                class="attachment-image-content"
                @click="openImageModal(attachment.url)"
              >
              <div class="attachment-info">
                <span class="attachment-name">{{ attachment.filename }}</span>
                <span class="attachment-size">{{ formatFileSize(attachment.size) }}</span>
              </div>
            </div>

            <!-- 视频附件 -->
            <div v-else-if="isVideo(attachment.filename)" class="attachment-video">
              <video
                :src="attachment.url"
                controls
                class="attachment-video-content"
              >
                您的浏览器不支持视频播放
              </video>
              <div class="attachment-info">
                <span class="attachment-name">{{ attachment.filename }}</span>
                <span class="attachment-size">{{ formatFileSize(attachment.size) }}</span>
              </div>
            </div>

            <!-- 其他文件附件 -->
            <div v-else class="attachment-file">
              <v-card variant="outlined" class="file-card">
                <v-card-text class="d-flex align-center">
                  <v-icon class="mr-3" size="large">{{ getFileIcon(attachment.filename) }}</v-icon>
                  <div class="file-info">
                    <div class="file-name">{{ attachment.filename }}</div>
                    <div class="file-size text-caption">{{ formatFileSize(attachment.size) }}</div>
                  </div>
                  <v-spacer />
                  <v-btn
                    icon="mdi-download"
                    variant="text"
                    size="small"
                    :href="attachment.url"
                    target="_blank"
                  />
                </v-card-text>
              </v-card>
            </div>
          </div>
        </div>

        <!-- 反应 -->
        <div v-if="discordReactions.length" class="reactions">
          <v-chip
            v-for="reaction in discordReactions"
            :key="reaction.emoji"
            size="small"
            variant="outlined"
            class="reaction-chip"
          >
            <span class="reaction-emoji">{{ reaction.emoji }}</span>
            <span class="reaction-count">{{ reaction.count }}</span>
          </v-chip>
        </div>
      </div>
    </div>

    <!-- 原始消息模式 -->
    <div v-else class="raw-message">
      <v-card variant="outlined">
        <v-card-text>
          <pre class="raw-content">{{ signal.raw_content || signal.content }}</pre>
        </v-card-text>
      </v-card>
    </div>

    <!-- 切换按钮 -->
    <div class="render-toggle">
      <v-btn-toggle v-model="renderMode" mandatory density="compact">
        <v-btn value="discord" size="small">
          <v-icon left size="small">mdi-discord</v-icon>
          Discord样式
        </v-btn>
        <v-btn value="raw" size="small">
          <v-icon left size="small">mdi-code-tags</v-icon>
          原始格式
        </v-btn>
      </v-btn-toggle>
    </div>

    <!-- 图片模态框 -->
    <v-dialog v-model="imageModal" max-width="90vw">
      <v-card>
        <v-card-text class="pa-0">
          <img
            v-if="selectedImage"
            :src="selectedImage"
            alt="放大图片"
            style="width: 100%; height: auto;"
          >
        </v-card-text>
        <v-card-actions>
          <v-spacer />
          <v-btn variant="text" @click="imageModal = false">关闭</v-btn>
          <v-btn
            variant="text"
            :href="selectedImage"
            target="_blank"
          >
            在新窗口打开
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'

// Props
const props = defineProps({
  signal: {
    type: Object,
    required: true
  },
  initialRenderMode: {
    type: String,
    default: 'discord'
  }
})

// 响应式数据
const renderMode = ref(props.initialRenderMode)
const imageModal = ref(false)
const selectedImage = ref('')
const avatarError = ref(false)

// 计算属性
const discordMetadata = computed(() => {
  return props.signal.metadata?.discord || {}
})

const discordEmbeds = computed(() => {
  return discordMetadata.value.embeds || []
})

const discordAttachments = computed(() => {
  return discordMetadata.value.attachments || []
})

const discordReactions = computed(() => {
  return discordMetadata.value.reactions || []
})

const replyTo = computed(() => {
  return discordMetadata.value.reply_to || null
})

const guildInfo = computed(() => {
  const metadata = discordMetadata.value
  if (metadata.guild_id && metadata.guild_name) {
    return {
      guild_id: metadata.guild_id,
      guild_name: metadata.guild_name
    }
  }
  return null
})

const authorAvatar = computed(() => {
  if (avatarError.value) return null

  // 尝试从元数据获取头像
  const authorId = props.signal.author_id
  if (authorId) {
    // Discord CDN 头像URL格式
    return `https://cdn.discordapp.com/avatars/${authorId}/avatar.png?size=128`
  }

  return null
})

const isBot = computed(() => {
  // 检查是否为机器人（可以从元数据或用户名判断）
  const authorName = props.signal.author_name || ''
  return authorName.toLowerCase().includes('bot') ||
         discordMetadata.value.is_bot === true
})

// 方法
const formatTimestamp = (timestamp) => {
  const date = new Date(timestamp)
  const now = new Date()
  const diff = now - date

  // 如果是今天，显示时间
  if (diff < 24 * 60 * 60 * 1000) {
    return date.toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  // 如果是昨天，显示"昨天"
  if (diff < 48 * 60 * 60 * 1000) {
    return '昨天 ' + date.toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  // 其他情况显示完整日期
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const formatDiscordContent = (content) => {
  if (!content) return ''

  let formatted = content

  // Discord markdown 格式化
  // 粗体 **text** 或 __text__
  formatted = formatted.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
  formatted = formatted.replace(/__(.*?)__/g, '<strong>$1</strong>')

  // 斜体 *text* 或 _text_
  formatted = formatted.replace(/\*(.*?)\*/g, '<em>$1</em>')
  formatted = formatted.replace(/_(.*?)_/g, '<em>$1</em>')

  // 删除线 ~~text~~
  formatted = formatted.replace(/~~(.*?)~~/g, '<del>$1</del>')

  // 代码块 `code`
  formatted = formatted.replace(/`([^`]+)`/g, '<code>$1</code>')

  // 代码块 ```code```
  formatted = formatted.replace(/```([\s\S]*?)```/g, '<pre><code>$1</code></pre>')

  // 链接 [text](url)
  formatted = formatted.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" target="_blank">$1</a>')

  // 用户提及 <@userid>
  formatted = formatted.replace(/<@!?(\d+)>/g, '<span class="mention">@用户</span>')

  // 频道提及 <#channelid>
  formatted = formatted.replace(/<#(\d+)>/g, '<span class="mention">#频道</span>')

  // 角色提及 <@&roleid>
  formatted = formatted.replace(/<@&(\d+)>/g, '<span class="mention">@角色</span>')

  // 表情符号 <:name:id>
  formatted = formatted.replace(/<:(.*?):(\d+)>/g, '<img src="https://cdn.discordapp.com/emojis/$2.png" alt=":$1:" class="emoji">')

  // 换行
  formatted = formatted.replace(/\n/g, '<br>')

  return formatted
}

const getEmbedColor = (color) => {
  if (!color) return '#202225'

  if (typeof color === 'number') {
    return `#${color.toString(16).padStart(6, '0')}`
  }

  return color
}

const truncateText = (text, maxLength) => {
  if (!text) return ''
  if (text.length <= maxLength) return text
  return text.substring(0, maxLength) + '...'
}

const isImage = (filename) => {
  const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp']
  return imageExtensions.some(ext => filename.toLowerCase().endsWith(ext))
}

const isVideo = (filename) => {
  const videoExtensions = ['.mp4', '.webm', '.mov', '.avi', '.mkv']
  return videoExtensions.some(ext => filename.toLowerCase().endsWith(ext))
}

const getFileIcon = (filename) => {
  const extension = filename.toLowerCase().split('.').pop()

  const iconMap = {
    pdf: 'mdi-file-pdf-box',
    doc: 'mdi-file-word-box',
    docx: 'mdi-file-word-box',
    xls: 'mdi-file-excel-box',
    xlsx: 'mdi-file-excel-box',
    ppt: 'mdi-file-powerpoint-box',
    pptx: 'mdi-file-powerpoint-box',
    txt: 'mdi-file-document',
    zip: 'mdi-folder-zip',
    rar: 'mdi-folder-zip',
    '7z': 'mdi-folder-zip',
    mp3: 'mdi-file-music',
    wav: 'mdi-file-music',
    flac: 'mdi-file-music'
  }

  return iconMap[extension] || 'mdi-file'
}

const formatFileSize = (bytes) => {
  if (!bytes) return '0 B'

  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(1024))

  return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
}

const openImageModal = (imageUrl) => {
  selectedImage.value = imageUrl
  imageModal.value = true
}

const handleAvatarError = () => {
  avatarError.value = true
}
</script>

<style scoped>
/* Discord 主题色彩 */
.discord-message {
  font-family: 'Whitney', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  background-color: #36393f;
  color: #dcddde;
  border-radius: 8px;
  padding: 16px;
  margin: 8px 0;
}

/* Discord 原生样式 */
.discord-native {
  position: relative;
}

/* 回复引用 */
.reply-reference {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  padding-left: 56px;
}

.reply-line {
  width: 2px;
  height: 16px;
  background-color: #4f545c;
  margin-right: 8px;
  border-radius: 1px;
}

.reply-content {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #b9bbbe;
}

.reply-icon {
  margin-right: 4px;
  color: #b9bbbe;
}

.reply-author {
  font-weight: 500;
  margin-right: 4px;
  color: #ffffff;
}

.reply-text {
  color: #b9bbbe;
}

/* 消息头部 */
.message-header {
  display: flex;
  align-items: flex-start;
  margin-bottom: 8px;
}

.header-content {
  flex: 1;
}

.author-line {
  display: flex;
  align-items: center;
  margin-bottom: 2px;
}

.author-name {
  font-weight: 600;
  font-size: 16px;
  color: #ffffff;
  margin-right: 8px;
}

.bot-tag {
  background-color: #5865f2;
  color: #ffffff;
  font-size: 10px;
  font-weight: 600;
  padding: 1px 4px;
  border-radius: 3px;
  margin-right: 8px;
  text-transform: uppercase;
}

.timestamp {
  font-size: 12px;
  color: #72767d;
  font-weight: 500;
}

.guild-info {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #b9bbbe;
}

.channel-name {
  color: #ffffff;
  font-weight: 500;
}

.guild-separator {
  margin: 0 6px;
  color: #72767d;
}

.guild-name {
  color: #b9bbbe;
}

/* 消息内容 */
.message-content {
  padding-left: 56px;
}

.text-content {
  font-size: 16px;
  line-height: 1.375;
  color: #dcddde;
  margin-bottom: 8px;
  word-wrap: break-word;
}

/* Discord 格式化样式 */
.text-content :deep(strong) {
  font-weight: 700;
}

.text-content :deep(em) {
  font-style: italic;
}

.text-content :deep(del) {
  text-decoration: line-through;
}

.text-content :deep(code) {
  background-color: #2f3136;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Consolas', 'Monaco', monospace;
  font-size: 14px;
}

.text-content :deep(pre) {
  background-color: #2f3136;
  border: 1px solid #202225;
  border-radius: 4px;
  padding: 8px;
  margin: 8px 0;
  overflow-x: auto;
}

.text-content :deep(pre code) {
  background: none;
  padding: 0;
}

.text-content :deep(a) {
  color: #00b0f4;
  text-decoration: none;
}

.text-content :deep(a:hover) {
  text-decoration: underline;
}

.text-content :deep(.mention) {
  background-color: rgba(88, 101, 242, 0.3);
  color: #dee0fc;
  padding: 0 2px;
  border-radius: 3px;
  font-weight: 500;
}

.text-content :deep(.emoji) {
  width: 22px;
  height: 22px;
  vertical-align: middle;
}

/* Embeds */
.embeds {
  margin: 8px 0;
}

.embed {
  background-color: #2f3136;
  border-left: 4px solid #202225;
  border-radius: 4px;
  padding: 16px;
  margin: 8px 0;
  max-width: 520px;
}

.embed-author {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.embed-author-icon {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  margin-right: 8px;
}

.embed-author-name {
  font-size: 14px;
  font-weight: 600;
  color: #ffffff;
  text-decoration: none;
}

.embed-title {
  font-size: 16px;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 8px;
}

.embed-title-link {
  color: #00b0f4;
  text-decoration: none;
}

.embed-title-link:hover {
  text-decoration: underline;
}

.embed-description {
  font-size: 14px;
  line-height: 1.375;
  color: #dcddde;
  margin-bottom: 8px;
}

.embed-fields {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 8px;
  margin-bottom: 8px;
}

.embed-field.inline {
  display: inline-block;
  margin-right: 16px;
  margin-bottom: 8px;
  min-width: 150px;
}

.field-name {
  font-size: 14px;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 2px;
}

.field-value {
  font-size: 14px;
  color: #dcddde;
  line-height: 1.375;
}

.embed-image {
  margin: 8px 0;
}

.embed-image-content {
  max-width: 400px;
  max-height: 300px;
  border-radius: 4px;
  cursor: pointer;
  transition: opacity 0.2s;
}

.embed-image-content:hover {
  opacity: 0.8;
}

.embed-thumbnail {
  float: right;
  margin-left: 16px;
  margin-bottom: 8px;
}

.embed-thumbnail-content {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: 4px;
}

.embed-footer {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #72767d;
  margin-top: 8px;
}

.embed-footer-icon {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  margin-right: 8px;
}

.embed-footer-text {
  font-weight: 500;
}

.embed-timestamp {
  margin-left: 4px;
}

/* 附件 */
.attachments {
  margin: 8px 0;
}

.attachment {
  margin: 8px 0;
}

.attachment-image {
  position: relative;
  display: inline-block;
}

.attachment-image-content {
  max-width: 400px;
  max-height: 300px;
  border-radius: 4px;
  cursor: pointer;
  transition: opacity 0.2s;
}

.attachment-image-content:hover {
  opacity: 0.8;
}

.attachment-info {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  color: #ffffff;
  padding: 8px;
  border-radius: 0 0 4px 4px;
  font-size: 12px;
}

.attachment-name {
  display: block;
  font-weight: 500;
}

.attachment-size {
  color: #b9bbbe;
}

.attachment-video {
  position: relative;
}

.attachment-video-content {
  max-width: 400px;
  max-height: 300px;
  border-radius: 4px;
}

.attachment-file {
  max-width: 400px;
}

.file-card {
  background-color: #2f3136 !important;
  border-color: #40444b !important;
}

.file-info {
  flex: 1;
}

.file-name {
  font-size: 14px;
  font-weight: 500;
  color: #ffffff;
  word-break: break-all;
}

.file-size {
  color: #b9bbbe;
}

/* 反应 */
.reactions {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  margin: 8px 0;
}

.reaction-chip {
  background-color: #2f3136 !important;
  border-color: #40444b !important;
  color: #dcddde !important;
  font-size: 12px;
  height: 24px;
  padding: 0 6px;
}

.reaction-emoji {
  margin-right: 4px;
}

.reaction-count {
  font-weight: 500;
}

/* 原始消息模式 */
.raw-message {
  margin: 8px 0;
}

.raw-content {
  background-color: #2f3136;
  color: #dcddde;
  font-family: 'Consolas', 'Monaco', monospace;
  font-size: 14px;
  line-height: 1.4;
  padding: 16px;
  margin: 0;
  white-space: pre-wrap;
  word-break: break-word;
  border-radius: 4px;
}

/* 切换按钮 */
.render-toggle {
  margin-top: 12px;
  display: flex;
  justify-content: center;
}

.v-btn-toggle {
  background-color: #2f3136;
  border-radius: 4px;
}

.v-btn-toggle .v-btn {
  color: #b9bbbe;
  border-color: #40444b;
}

.v-btn-toggle .v-btn.v-btn--active {
  background-color: #5865f2 !important;
  color: #ffffff !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .discord-message {
    padding: 12px;
  }

  .message-content {
    padding-left: 0;
  }

  .message-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .message-header .v-avatar {
    margin-bottom: 8px;
  }

  .embed {
    max-width: 100%;
  }

  .embed-fields {
    grid-template-columns: 1fr;
  }

  .embed-field.inline {
    display: block;
    margin-right: 0;
  }

  .attachment-image-content,
  .attachment-video-content {
    max-width: 100%;
  }

  .attachment-file {
    max-width: 100%;
  }
}

/* 深色主题适配 */
@media (prefers-color-scheme: dark) {
  .discord-message {
    background-color: #36393f;
    color: #dcddde;
  }
}

/* 浅色主题适配 */
@media (prefers-color-scheme: light) {
  .discord-message {
    background-color: #ffffff;
    color: #2e3338;
    border: 1px solid #e3e5e8;
  }

  .discord-native .author-name {
    color: #2e3338;
  }

  .discord-native .text-content {
    color: #2e3338;
  }

  .embed {
    background-color: #f2f3f5;
    border-left-color: #e3e5e8;
  }

  .embed-title,
  .embed-author-name,
  .field-name {
    color: #2e3338;
  }

  .embed-description,
  .field-value {
    color: #4e5058;
  }

  .raw-content {
    background-color: #f2f3f5;
    color: #2e3338;
  }

  .file-card {
    background-color: #f2f3f5 !important;
    border-color: #e3e5e8 !important;
  }

  .file-name {
    color: #2e3338;
  }

  .reaction-chip {
    background-color: #f2f3f5 !important;
    border-color: #e3e5e8 !important;
    color: #2e3338 !important;
  }
}

/* 动画效果 */
.discord-message {
  transition: all 0.2s ease;
}

.attachment-image-content,
.embed-image-content {
  transition: transform 0.2s ease, opacity 0.2s ease;
}

.attachment-image-content:hover,
.embed-image-content:hover {
  transform: scale(1.02);
}

.reaction-chip {
  transition: all 0.2s ease;
}

.reaction-chip:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

/* 滚动条样式 */
.raw-content::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.raw-content::-webkit-scrollbar-track {
  background: #2f3136;
  border-radius: 4px;
}

.raw-content::-webkit-scrollbar-thumb {
  background: #40444b;
  border-radius: 4px;
}

.raw-content::-webkit-scrollbar-thumb:hover {
  background: #4f545c;
}
</style>