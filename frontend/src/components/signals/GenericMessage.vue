<template>
  <div class="generic-message">
    <!-- 原始消息模式 -->
    <div v-if="renderMode === 'raw'" class="raw-message">
      <v-card variant="outlined">
        <v-card-text>
          <pre class="raw-content">{{ signal.raw_content || signal.content }}</pre>
        </v-card-text>
      </v-card>
    </div>

    <!-- 格式化消息模式 -->
    <div v-else class="formatted-message">
      <v-card variant="outlined">
        <!-- 消息头部 -->
        <v-card-title class="pb-2">
          <div class="d-flex align-center">
            <v-chip
              :color="getPlatformColor(signal.platform)"
              variant="outlined"
              size="small"
              class="mr-2"
            >
              <v-icon left size="small">{{ getPlatformIcon(signal.platform) }}</v-icon>
              {{ getPlatformName(signal.platform) }}
            </v-chip>

            <span v-if="signal.author_name" class="author-name">
              {{ signal.author_name }}
            </span>

            <v-spacer />

            <span class="timestamp">
              {{ formatTimestamp(signal.created_at) }}
            </span>
          </div>

          <div v-if="signal.channel_name" class="channel-info">
            <v-icon size="small" class="mr-1">mdi-pound</v-icon>
            <span>{{ signal.channel_name }}</span>
          </div>
        </v-card-title>

        <!-- 消息内容 -->
        <v-card-text>
          <div class="message-content">
            <div v-html="formatContent(signal.content)"></div>
          </div>

          <!-- 元数据显示 -->
          <div v-if="hasMetadata" class="metadata-section mt-3">
            <v-expansion-panels variant="accordion">
              <v-expansion-panel>
                <v-expansion-panel-title>
                  <v-icon left size="small">mdi-information</v-icon>
                  附加信息
                </v-expansion-panel-title>
                <v-expansion-panel-text>
                  <pre class="metadata-content">{{ formatJSON(signal.metadata) }}</pre>
                </v-expansion-panel-text>
              </v-expansion-panel>
            </v-expansion-panels>
          </div>
        </v-card-text>
      </v-card>
    </div>

    <!-- 切换按钮 -->
    <div class="render-toggle">
      <v-btn-toggle v-model="renderMode" mandatory density="compact">
        <v-btn value="formatted" size="small">
          <v-icon left size="small">mdi-format-text</v-icon>
          格式化
        </v-btn>
        <v-btn value="raw" size="small">
          <v-icon left size="small">mdi-code-tags</v-icon>
          原始格式
        </v-btn>
      </v-btn-toggle>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'

// Props
const props = defineProps({
  signal: {
    type: Object,
    required: true
  },
  initialRenderMode: {
    type: String,
    default: 'formatted'
  }
})

// 响应式数据
const renderMode = ref(props.initialRenderMode)

// 计算属性
const hasMetadata = computed(() => {
  return props.signal.metadata && Object.keys(props.signal.metadata).length > 0
})

// 方法
const getPlatformColor = (platform) => {
  const colors = {
    telegram: 'blue',
    manual: 'green',
    email: 'orange',
    webhook: 'purple'
  }
  return colors[platform] || 'grey'
}

const getPlatformIcon = (platform) => {
  const icons = {
    telegram: 'mdi-telegram',
    manual: 'mdi-pencil',
    email: 'mdi-email',
    webhook: 'mdi-webhook'
  }
  return icons[platform] || 'mdi-message'
}

const getPlatformName = (platform) => {
  const names = {
    telegram: 'Telegram',
    manual: '手动',
    email: '邮件',
    webhook: 'Webhook'
  }
  return names[platform] || platform
}

const formatTimestamp = (timestamp) => {
  const date = new Date(timestamp)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const formatContent = (content) => {
  if (!content) return ''

  let formatted = content

  // 基础格式化
  // 换行
  formatted = formatted.replace(/\n/g, '<br>')

  // 链接
  formatted = formatted.replace(
    /(https?:\/\/[^\s]+)/g,
    '<a href="$1" target="_blank" rel="noopener noreferrer">$1</a>'
  )

  // 邮箱
  formatted = formatted.replace(
    /([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/g,
    '<a href="mailto:$1">$1</a>'
  )

  return formatted
}

const formatJSON = (obj) => {
  return JSON.stringify(obj, null, 2)
}
</script>

<style scoped>
.generic-message {
  margin: 8px 0;
}

.formatted-message .v-card {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
}

.author-name {
  font-weight: 600;
  color: #495057;
}

.timestamp {
  font-size: 12px;
  color: #6c757d;
}

.channel-info {
  font-size: 14px;
  color: #6c757d;
  margin-top: 4px;
}

.message-content {
  font-size: 16px;
  line-height: 1.5;
  color: #212529;
  word-wrap: break-word;
}

.message-content :deep(a) {
  color: #0d6efd;
  text-decoration: none;
}

.message-content :deep(a:hover) {
  text-decoration: underline;
}

.metadata-content {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 12px;
  font-family: 'Consolas', 'Monaco', monospace;
  font-size: 12px;
  color: #495057;
  margin: 0;
  overflow-x: auto;
}

.raw-message .raw-content {
  background-color: #f8f9fa;
  color: #495057;
  font-family: 'Consolas', 'Monaco', monospace;
  font-size: 14px;
  line-height: 1.4;
  padding: 16px;
  margin: 0;
  white-space: pre-wrap;
  word-break: break-word;
  border-radius: 4px;
}

.render-toggle {
  margin-top: 12px;
  display: flex;
  justify-content: center;
}

/* 深色主题适配 */
@media (prefers-color-scheme: dark) {
  .formatted-message .v-card {
    background-color: #2d3748;
    border-color: #4a5568;
  }

  .author-name {
    color: #e2e8f0;
  }

  .timestamp,
  .channel-info {
    color: #a0aec0;
  }

  .message-content {
    color: #e2e8f0;
  }

  .metadata-content,
  .raw-content {
    background-color: #2d3748;
    border-color: #4a5568;
    color: #e2e8f0;
  }
}

/* 响应式设计 */
@media (max-width: 600px) {
  .v-card-title {
    flex-direction: column;
    align-items: flex-start;
  }

  .timestamp {
    margin-top: 4px;
  }
}
</style>