<template>
  <div class="signals-list" data-testid="signals-list">
    <!-- 列表头部工具栏 -->
    <v-card-text class="pb-0">
      <v-row align="center">
        <v-col cols="12" md="6">
          <div class="d-flex align-center">
            <!-- 批量操作 -->
            <v-checkbox
              v-model="selectAll"
              :indeterminate="someSelected && !allSelected"
              @change="toggleSelectAll"
              :disabled="loading || !signals.length"
              hide-details
              class="mr-2"
            />
            <span class="text-body-2 text-medium-emphasis">
              {{ selectedSignals.length > 0 ? `已选择 ${selectedSignals.length} 项` : `共 ${pagination.total} 条记录` }}
            </span>

            <!-- 批量操作按钮 -->
            <div v-if="selectedSignals.length > 0" class="ml-4" data-testid="batch-actions">
              <v-btn
                size="small"
                variant="outlined"
                color="success"
                @click="batchMarkProcessed"
                :loading="batchLoading"
                class="mr-2"
                data-testid="batch-mark-processed"
              >
                <v-icon left size="small">mdi-check-circle</v-icon>
                标记已处理
              </v-btn>
              <v-btn
                size="small"
                variant="outlined"
                color="error"
                @click="batchDelete"
                :loading="batchLoading"
              >
                <v-icon left size="small">mdi-delete</v-icon>
                批量删除
              </v-btn>
            </div>
          </div>
        </v-col>

        <v-col cols="12" md="6">
          <div class="d-flex align-center justify-end">
            <!-- 视图切换 -->
            <v-btn-toggle
              v-model="viewMode"
              mandatory
              variant="outlined"
              size="small"
              class="mr-4"
            >
              <v-btn value="table" icon="mdi-table" data-testid="view-toggle-table" />
              <v-btn value="cards" icon="mdi-view-grid" data-testid="view-toggle-card" />
            </v-btn-toggle>

            <!-- 排序选择 -->
            <v-select
              v-model="currentSort"
              :items="sortOptions"
              label="排序"
              variant="outlined"
              density="compact"
              style="max-width: 200px;"
              @update:model-value="handleSortChange"
              :loading="loading"
            />
          </div>
        </v-col>
      </v-row>
    </v-card-text>

    <!-- 表格视图 -->
    <div v-if="viewMode === 'table'" data-testid="table-view">
      <v-data-table
        v-model="selectedSignals"
        :headers="tableHeaders"
        :items="signals"
        :loading="loading"
        item-value="id"
        show-select
        :items-per-page="-1"
        hide-default-footer
        class="signals-table"
        @click:row="handleRowClick"
      >
        <!-- 平台列 -->
        <template v-slot:item.platform="{ item }">
          <v-chip
            size="small"
            :color="getPlatformColor(item.platform)"
            variant="outlined"
          >
            <v-icon left size="small">{{ getPlatformIcon(item.platform) }}</v-icon>
            {{ getPlatformName(item.platform) }}
          </v-chip>
        </template>

        <!-- 内容列 -->
        <template v-slot:item.content="{ item }">
          <div class="content-cell">
            <!-- Discord消息预览 -->
            <div v-if="item.platform === 'discord' && hasDiscordEmbeds(item)" class="discord-preview">
              <div class="content-preview">{{ truncateContent(item.content) }}</div>
              <div class="embed-preview">
                <v-chip size="x-small" color="indigo" variant="outlined">
                  <v-icon left size="x-small">mdi-card-text</v-icon>
                  {{ getEmbedCount(item) }} 个嵌入
                </v-chip>
              </div>
            </div>
            <!-- 普通消息预览 -->
            <div v-else class="content-preview">{{ truncateContent(item.content) }}</div>

            <div v-if="item.channel_name" class="text-caption text-medium-emphasis">
              <v-icon size="small" class="mr-1">mdi-pound</v-icon>
              {{ item.channel_name }}
            </div>
          </div>
        </template>

        <!-- 置信度列 -->
        <template v-slot:item.confidence="{ item }">
          <div v-if="item.confidence !== null" class="d-flex align-center">
            <v-progress-linear
              :model-value="item.confidence * 100"
              :color="getConfidenceColor(item.confidence)"
              height="6"
              rounded
              class="mr-2"
              style="min-width: 60px;"
            />
            <span class="text-caption">{{ (item.confidence * 100).toFixed(0) }}%</span>
          </div>
          <span v-else class="text-caption text-medium-emphasis">-</span>
        </template>

        <!-- AI解析状态列 -->
        <template v-slot:item.ai_parse_status="{ item }">
          <v-chip
            size="small"
            :color="getAIStatusColor(item.ai_parse_status)"
            variant="outlined"
          >
            <v-icon left size="small">{{ getAIStatusIcon(item.ai_parse_status) }}</v-icon>
            {{ getAIStatusText(item.ai_parse_status) }}
          </v-chip>
        </template>

        <!-- AI消息类型列 -->
        <template v-slot:item.message_type_ai="{ item }">
          <v-chip
            size="small"
            :color="getMessageTypeColor(item.message_type_ai)"
            variant="outlined"
          >
            <v-icon left size="small">{{ getMessageTypeIcon(item.message_type_ai) }}</v-icon>
            {{ getMessageTypeText(item.message_type_ai) }}
          </v-chip>
        </template>

        <!-- LLM服务列 -->
        <template v-slot:item.llm_service="{ item }">
          <v-chip
            v-if="item.llm_service"
            size="small"
            :color="getLLMServiceColor(item.llm_service)"
            variant="outlined"
          >
            <v-icon left size="small">{{ getLLMServiceIcon(item.llm_service) }}</v-icon>
            {{ getLLMServiceText(item.llm_service) }}
          </v-chip>
          <span v-else class="text-caption text-medium-emphasis">-</span>
        </template>

        <!-- 处理状态列 -->
        <template v-slot:item.is_processed="{ item }">
          <v-chip
            size="small"
            :color="item.is_processed ? 'success' : 'warning'"
            variant="outlined"
          >
            <v-icon left size="small">
              {{ item.is_processed ? 'mdi-check-circle' : 'mdi-clock-outline' }}
            </v-icon>
            {{ item.is_processed ? '已处理' : '未处理' }}
          </v-chip>
        </template>

        <!-- 创建时间列 -->
        <template v-slot:item.created_at="{ item }">
          <div class="text-body-2">{{ formatDateTime(item.created_at) }}</div>
          <div class="text-caption text-medium-emphasis">{{ formatRelativeTime(item.created_at) }}</div>
        </template>

        <!-- 操作列 -->
        <template v-slot:item.actions="{ item }">
          <div class="d-flex align-center">
            <v-btn
              icon="mdi-eye"
              size="small"
              variant="text"
              @click.stop="viewSignal(item)"
              :loading="loading"
            />
            <v-btn
              :icon="item.is_processed ? 'mdi-undo' : 'mdi-check'"
              size="small"
              variant="text"
              :color="item.is_processed ? 'warning' : 'success'"
              @click.stop="toggleProcessed(item)"
              :loading="loading"
            />
            <v-btn
              icon="mdi-delete"
              size="small"
              variant="text"
              color="error"
              @click.stop="deleteSignal(item)"
              :loading="loading"
            />
          </div>
        </template>

        <!-- 空状态 -->
        <template v-slot:no-data>
          <div class="text-center py-8">
            <v-icon size="64" color="grey-lighten-2">mdi-signal-off</v-icon>
            <div class="text-h6 text-medium-emphasis mt-4">暂无信号数据</div>
            <div class="text-body-2 text-medium-emphasis">
              尝试调整筛选条件或创建新的信号
            </div>
          </div>
        </template>
      </v-data-table>
    </div>

    <!-- 卡片视图 -->
    <div v-else class="cards-view" data-testid="card-view">
      <v-card-text>
        <v-row>
          <v-col
            v-for="signal in signals"
            :key="signal.id"
            cols="12"
            md="6"
            lg="4"
          >
            <SignalCard
              :signal="signal"
              :theme="cardTheme"
              :selected="selectedSignals.includes(signal.id)"
              :selectable="true"
              :hover="true"
              :show-actions="true"
              :show-delete-action="true"
              :show-theme-toggle="false"
              @click="handleCardClick"
              @selection-change="handleCardSelectionChange"
              @view-details="viewSignal"
              @toggle-processed="toggleProcessed"
              @delete="deleteSignal"
              @theme-change="handleThemeChange"
            />
          </v-col>
        </v-row>

        <!-- 卡片视图空状态 -->
        <div v-if="!signals.length && !loading" class="text-center py-12">
          <v-icon size="64" color="grey-lighten-2">mdi-signal-off</v-icon>
          <div class="text-h6 text-medium-emphasis mt-4">暂无信号数据</div>
          <div class="text-body-2 text-medium-emphasis">
            尝试调整筛选条件或创建新的信号
          </div>
        </div>
      </v-card-text>
    </div>

    <!-- 分页控件 -->
    <v-card-actions v-if="pagination.total > 0" class="justify-center">
      <v-pagination
        v-model="currentPage"
        :length="totalPages"
        :total-visible="7"
        @update:model-value="handlePageChange"
        :disabled="loading"
      />

      <div class="ml-4 text-caption text-medium-emphasis">
        显示第 {{ startItem }} - {{ endItem }} 项，共 {{ pagination.total }} 项
      </div>
    </v-card-actions>

    <!-- 批量操作确认对话框 -->
    <v-dialog v-model="showBatchDialog" max-width="400">
      <v-card>
        <v-card-title>{{ batchAction === 'delete' ? '确认删除' : '确认操作' }}</v-card-title>
        <v-card-text>
          {{ batchAction === 'delete'
            ? `确定要删除选中的 ${selectedSignals.length} 个信号吗？此操作不可撤销。`
            : `确定要将选中的 ${selectedSignals.length} 个信号标记为已处理吗？`
          }}
        </v-card-text>
        <v-card-actions>
          <v-spacer />
          <v-btn variant="text" @click="showBatchDialog = false">取消</v-btn>
          <v-btn
            :color="batchAction === 'delete' ? 'error' : 'primary'"
            @click="confirmBatchAction"
            :loading="batchLoading"
          >
            确定
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useSnackbar } from '@/composables/useSnackbar'
import { signalApi } from '@/api/signals'
import SignalCard from './SignalCard.vue'

// Props
const props = defineProps({
  signals: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  },
  pagination: {
    type: Object,
    default: () => ({
      page: 1,
      size: 20,
      total: 0,
      has_next: false,
      has_prev: false
    })
  }
})

// Emits
const emit = defineEmits([
  'signal-click',
  'signal-update',
  'page-change',
  'sort-change'
])

// 组合式API
const { showSnackbar } = useSnackbar()

// 响应式数据
const viewMode = ref('table')
const selectedSignals = ref([])
const currentSort = ref('created_at_desc')
const batchLoading = ref(false)
const showBatchDialog = ref(false)
const batchAction = ref('')
const cardTheme = ref('auto') // 卡片主题：auto, discord, telegram, generic

// 表格头部配置
const tableHeaders = [
  { title: '平台', key: 'platform', sortable: false, width: '100px' },
  { title: '内容', key: 'content', sortable: false, width: '30%' },
  { title: '置信度', key: 'confidence', sortable: true, width: '120px' },
  { title: 'AI状态', key: 'ai_parse_status', sortable: true, width: '100px' },
  { title: '消息类型', key: 'message_type_ai', sortable: true, width: '120px' },
  { title: 'LLM', key: 'llm_service', sortable: false, width: '80px' },
  { title: '处理状态', key: 'is_processed', sortable: true, width: '100px' },
  { title: '创建时间', key: 'created_at', sortable: true, width: '150px' },
  { title: '操作', key: 'actions', sortable: false, width: '120px' }
]

// 排序选项
const sortOptions = [
  { title: '创建时间 ↓', value: 'created_at_desc' },
  { title: '创建时间 ↑', value: 'created_at_asc' },
  { title: '置信度 ↓', value: 'confidence_desc' },
  { title: '置信度 ↑', value: 'confidence_asc' },
  { title: 'AI状态', value: 'ai_parse_status_asc' },
  { title: '处理状态', value: 'is_processed_asc' },
  // 向后兼容
  { title: '信号强度 ↓', value: 'signal_strength_desc' },
  { title: '信号强度 ↑', value: 'signal_strength_asc' }
]

// 计算属性
const currentPage = computed({
  get: () => props.pagination.page,
  set: (value) => emit('page-change', value)
})

const totalPages = computed(() => {
  return Math.ceil(props.pagination.total / props.pagination.size)
})

const startItem = computed(() => {
  return (props.pagination.page - 1) * props.pagination.size + 1
})

const endItem = computed(() => {
  const end = props.pagination.page * props.pagination.size
  return Math.min(end, props.pagination.total)
})

const selectAll = computed({
  get: () => allSelected.value,
  set: (value) => {
    if (value) {
      selectedSignals.value = props.signals.map(s => s.id)
    } else {
      selectedSignals.value = []
    }
  }
})

const allSelected = computed(() => {
  return props.signals.length > 0 && selectedSignals.value.length === props.signals.length
})

const someSelected = computed(() => {
  return selectedSignals.value.length > 0 && selectedSignals.value.length < props.signals.length
})

// 方法
const getPlatformColor = (platform) => {
  const colors = {
    discord: 'indigo',
    telegram: 'blue',
    manual: 'green'
  }
  return colors[platform] || 'default'
}

const getPlatformIcon = (platform) => {
  const icons = {
    discord: 'mdi-discord',
    telegram: 'mdi-telegram',
    manual: 'mdi-pencil'
  }
  return icons[platform] || 'mdi-help-circle'
}

const getPlatformName = (platform) => {
  const names = {
    discord: 'Discord',
    telegram: 'Telegram',
    manual: '手动'
  }
  return names[platform] || platform
}

const getConfidenceColor = (confidence) => {
  if (confidence >= 0.8) return 'success'
  if (confidence >= 0.6) return 'warning'
  if (confidence >= 0.4) return 'orange'
  return 'error'
}

// 向后兼容
const getStrengthColor = getConfidenceColor

// AI解析状态相关方法
const getAIStatusColor = (status) => {
  const colors = {
    pending: 'warning',
    success: 'success',
    failed: 'error',
    partial: 'info'
  }
  return colors[status] || 'default'
}

const getAIStatusIcon = (status) => {
  const icons = {
    pending: 'mdi-clock-outline',
    success: 'mdi-check-circle',
    failed: 'mdi-alert-circle',
    partial: 'mdi-progress-check'
  }
  return icons[status] || 'mdi-help-circle'
}

const getAIStatusText = (status) => {
  const texts = {
    pending: '待解析',
    success: '解析成功',
    failed: '解析失败',
    partial: '部分解析'
  }
  return texts[status] || status
}

// 消息类型相关方法
const getMessageTypeColor = (type) => {
  const colors = {
    normal_message: 'grey',
    trading_signal: 'success',
    market_analysis: 'info',
    price_alert: 'warning',
    ambiguous: 'orange'
  }
  return colors[type] || 'default'
}

const getMessageTypeIcon = (type) => {
  const icons = {
    normal_message: 'mdi-message-text',
    trading_signal: 'mdi-chart-line',
    market_analysis: 'mdi-chart-areaspline',
    price_alert: 'mdi-bell-alert',
    ambiguous: 'mdi-help-circle'
  }
  return icons[type] || 'mdi-message'
}

const getMessageTypeText = (type) => {
  const texts = {
    normal_message: '普通消息',
    trading_signal: '交易信号',
    market_analysis: '市场分析',
    price_alert: '价格提醒',
    ambiguous: '模糊信号'
  }
  return texts[type] || type
}

// LLM服务相关方法
const getLLMServiceColor = (service) => {
  const colors = {
    deepseek: 'deep-purple',
    gemini: 'blue',
    chatgpt: 'green',
    claude: 'orange'
  }
  return colors[service] || 'default'
}

const getLLMServiceIcon = (service) => {
  const icons = {
    deepseek: 'mdi-brain',
    gemini: 'mdi-google',
    chatgpt: 'mdi-robot',
    claude: 'mdi-account-circle'
  }
  return icons[service] || 'mdi-robot-outline'
}

const getLLMServiceText = (service) => {
  const texts = {
    deepseek: 'DeepSeek',
    gemini: 'Gemini',
    chatgpt: 'ChatGPT',
    claude: 'Claude'
  }
  return texts[service] || service
}

const truncateContent = (content, maxLength = 80) => {
  if (!content) return ''
  if (content.length <= maxLength) return content
  return content.substring(0, maxLength) + '...'
}

const formatDateTime = (dateString) => {
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const formatRelativeTime = (dateString) => {
  const date = new Date(dateString)
  const now = new Date()
  const diff = now - date

  const minutes = Math.floor(diff / 60000)
  const hours = Math.floor(diff / 3600000)
  const days = Math.floor(diff / ********)

  if (minutes < 1) return '刚刚'
  if (minutes < 60) return `${minutes}分钟前`
  if (hours < 24) return `${hours}小时前`
  if (days < 7) return `${days}天前`
  return formatDateTime(dateString)
}

// Discord相关辅助方法
const hasDiscordEmbeds = (signal) => {
  return signal.platform === 'discord' &&
         signal.metadata?.discord?.embeds?.length > 0
}

const hasDiscordAttachments = (signal) => {
  return signal.platform === 'discord' &&
         signal.metadata?.discord?.attachments?.length > 0
}

const hasDiscordReactions = (signal) => {
  return signal.platform === 'discord' &&
         signal.metadata?.discord?.reactions?.length > 0
}

const getEmbedCount = (signal) => {
  return signal.metadata?.discord?.embeds?.length || 0
}

const getAttachmentCount = (signal) => {
  return signal.metadata?.discord?.attachments?.length || 0
}

const getReactionCount = (signal) => {
  const reactions = signal.metadata?.discord?.reactions || []
  return reactions.reduce((total, reaction) => total + (reaction.count || 0), 0)
}

// 事件处理
const handleRowClick = (event, { item }) => {
  emit('signal-click', item)
}

const handleCardClick = (signal) => {
  emit('signal-click', signal)
}

const handleCardSelectionChange = (signalId, selected) => {
  if (selected) {
    if (!selectedSignals.value.includes(signalId)) {
      selectedSignals.value.push(signalId)
    }
  } else {
    const index = selectedSignals.value.indexOf(signalId)
    if (index > -1) {
      selectedSignals.value.splice(index, 1)
    }
  }
}

const handleThemeChange = (theme, signal) => {
  cardTheme.value = theme
  showSnackbar(`已切换到${theme}主题`, 'info')
}

const handlePageChange = (page) => {
  emit('page-change', page)
}

const handleSortChange = () => {
  const [sortBy, sortOrder] = currentSort.value.split('_')
  emit('sort-change', sortBy, sortOrder)
}

const toggleSelectAll = () => {
  // selectAll computed property 会自动处理
}

const toggleSignalSelection = (signalId) => {
  const index = selectedSignals.value.indexOf(signalId)
  if (index > -1) {
    selectedSignals.value.splice(index, 1)
  } else {
    selectedSignals.value.push(signalId)
  }
}

const viewSignal = (signal) => {
  emit('signal-click', signal)
}

const toggleProcessed = async (signal) => {
  try {
    const response = await signalApi.updateSignal(signal.id, {
      is_processed: !signal.is_processed
    })

    if (response.success) {
      emit('signal-update', response.data)
      showSnackbar(
        `信号已${signal.is_processed ? '标记为未处理' : '标记为已处理'}`,
        'success'
      )
    } else {
      showSnackbar(response.message || '更新失败', 'error')
    }
  } catch (error) {
    console.error('更新信号状态失败:', error)
    showSnackbar('更新信号状态失败', 'error')
  }
}

const deleteSignal = async (signal) => {
  if (!confirm(`确定要删除这个信号吗？`)) return

  try {
    const response = await signalApi.deleteSignal(signal.id)

    if (response.success) {
      emit('signal-update', null) // 触发列表刷新
      showSnackbar('信号删除成功', 'success')
    } else {
      showSnackbar(response.message || '删除失败', 'error')
    }
  } catch (error) {
    console.error('删除信号失败:', error)
    showSnackbar('删除信号失败', 'error')
  }
}

// 批量操作
const batchMarkProcessed = () => {
  batchAction.value = 'processed'
  showBatchDialog.value = true
}

const batchDelete = () => {
  batchAction.value = 'delete'
  showBatchDialog.value = true
}

const confirmBatchAction = async () => {
  try {
    batchLoading.value = true

    if (batchAction.value === 'delete') {
      // 批量删除
      const promises = selectedSignals.value.map(id => signalApi.deleteSignal(id))
      const results = await Promise.allSettled(promises)

      const successful = results.filter(r => r.status === 'fulfilled').length
      const failed = results.filter(r => r.status === 'rejected').length

      if (successful > 0) {
        showSnackbar(`成功删除 ${successful} 个信号${failed > 0 ? `，${failed} 个失败` : ''}`, 'success')
        emit('signal-update', null) // 触发列表刷新
      } else {
        showSnackbar('批量删除失败', 'error')
      }
    } else if (batchAction.value === 'processed') {
      // 批量标记已处理
      const promises = selectedSignals.value.map(id =>
        signalApi.updateSignal(id, { is_processed: true })
      )
      const results = await Promise.allSettled(promises)

      const successful = results.filter(r => r.status === 'fulfilled').length
      const failed = results.filter(r => r.status === 'rejected').length

      if (successful > 0) {
        showSnackbar(`成功处理 ${successful} 个信号${failed > 0 ? `，${failed} 个失败` : ''}`, 'success')
        emit('signal-update', null) // 触发列表刷新
      } else {
        showSnackbar('批量处理失败', 'error')
      }
    }

    selectedSignals.value = []
    showBatchDialog.value = false
  } catch (error) {
    console.error('批量操作失败:', error)
    showSnackbar('批量操作失败', 'error')
  } finally {
    batchLoading.value = false
  }
}

// 监听信号列表变化，清除无效的选择
watch(
  () => props.signals,
  (newSignals) => {
    const validIds = newSignals.map(s => s.id)
    selectedSignals.value = selectedSignals.value.filter(id => validIds.includes(id))
  },
  { deep: true }
)
</script>

<style scoped>
.signals-list {
  min-height: 400px;
}

.signals-table {
  border-radius: 0;
}

.content-cell {
  max-width: 300px;
}

.content-preview {
  word-break: break-word;
  line-height: 1.4;
}

.cards-view .signal-card {
  transition: all 0.2s ease;
  cursor: pointer;
}

.cards-view .signal-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.cards-view .signal-card.selected {
  border: 2px solid rgb(var(--v-theme-primary));
  background-color: rgba(var(--v-theme-primary), 0.05);
}

.signal-content {
  min-height: 60px;
}

.v-progress-linear {
  border-radius: 3px;
}

.v-chip {
  font-weight: 500;
}

/* 状态标签水平滚动容器 */
.status-tags-container {
  width: 100%;
  overflow: hidden;
}

.status-tags-scroll {
  display: flex;
  align-items: center;
  gap: 6px;
  overflow-x: auto;
  white-space: nowrap;
  padding: 2px 0;
  /* 隐藏滚动条但保持滚动功能 */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

.status-tags-scroll::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

.status-tag {
  flex-shrink: 0;
  font-size: 0.75rem;
}

/* 响应式设计 */
@media (max-width: 960px) {
  .content-cell {
    max-width: 200px;
  }

  .cards-view .v-col {
    padding: 8px;
  }
}

@media (max-width: 600px) {
  .v-card-text .v-row {
    margin: 0;
  }

  .v-card-text .v-col {
    padding: 4px;
  }

  .content-cell {
    max-width: 150px;
  }
}

/* 表格样式优化 */
.v-data-table :deep(.v-data-table__wrapper) {
  border-radius: 0;
}

.v-data-table :deep(.v-data-table-header) {
  background-color: rgba(var(--v-theme-surface), 0.8);
}

.v-data-table :deep(.v-data-table-rows-no-data) {
  padding: 40px 20px;
}

/* 批量操作工具栏 */
.v-card-text.pb-0 {
  border-bottom: 1px solid rgba(0, 0, 0, 0.12);
}

/* 分页样式 */
.v-card-actions {
  border-top: 1px solid rgba(0, 0, 0, 0.12);
  padding: 16px 24px;
}
</style>