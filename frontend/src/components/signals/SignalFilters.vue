<template>
  <v-card-text data-testid="signal-filters">
    <v-row>
      <!-- 平台选择器 -->
      <v-col cols="12" md="2">
        <v-select
          :model-value="localFilters.platform"
          :items="platformOptions"
          label="平台"
          clearable
          dense
          variant="outlined"
          :loading="loading"
          data-testid="platform-filter"
          @update:model-value="(value) => updateFilter('platform', value)"
        >
          <template v-slot:selection="{ item }">
            <v-chip size="small" :color="getPlatformColor(item.value)">
              <v-icon left size="small">{{ getPlatformIcon(item.value) }}</v-icon>
              {{ item.title }}
            </v-chip>
          </template>
          <template v-slot:item="{ props, item }">
            <v-list-item v-bind="props">
              <template v-slot:prepend>
                <v-icon :color="getPlatformColor(item.value)">
                  {{ getPlatformIcon(item.value) }}
                </v-icon>
              </template>
            </v-list-item>
          </template>
        </v-select>
      </v-col>

      <!-- 处理状态筛选 -->
      <v-col cols="12" md="2">
        <v-select
          :model-value="localFilters.status"
          :items="processedOptions"
          label="处理状态"
          clearable
          dense
          variant="outlined"
          :loading="loading"
          @update:model-value="(value) => updateFilter('status', value)"
        >
          <template v-slot:selection="{ item }">
            <v-chip size="small" :color="item.value === 'processed' ? 'success' : item.value === 'unprocessed' ? 'warning' : 'default'">
              <v-icon left size="small">
                {{ item.value === 'processed' ? 'mdi-check-circle' : item.value === 'unprocessed' ? 'mdi-clock-outline' : 'mdi-help-circle' }}
              </v-icon>
              {{ item.title }}
            </v-chip>
          </template>
        </v-select>
      </v-col>

      <!-- 频道ID输入框 -->
      <v-col cols="12" md="2">
        <v-text-field
          :model-value="localFilters.channel_id"
          label="频道ID"
          clearable
          dense
          variant="outlined"
          :loading="loading"
          @update:model-value="(value) => updateFilter('channel_id', value)"
          placeholder="输入频道ID"
        >
          <template v-slot:prepend-inner>
            <v-icon size="small">mdi-pound</v-icon>
          </template>
        </v-text-field>
      </v-col>

      <!-- AI解析状态筛选 -->
      <v-col cols="12" md="2">
        <v-select
          :model-value="localFilters.ai_parsed"
          :items="aiParseStatusOptions"
          label="AI解析状态"
          clearable
          dense
          variant="outlined"
          :loading="loading"
          @update:model-value="(value) => updateFilter('ai_parsed', value)"
        >
          <template v-slot:selection="{ item }">
            <v-chip size="small" :color="getAIStatusColor(item.value)">
              <v-icon left size="small">{{ getAIStatusIcon(item.value) }}</v-icon>
              {{ item.title }}
            </v-chip>
          </template>
        </v-select>
      </v-col>

      <!-- AI消息类型筛选 -->
      <v-col cols="12" md="2">
        <v-select
          :model-value="localFilters.ai_message_type"
          :items="messageTypeAIOptions"
          label="消息类型"
          clearable
          dense
          variant="outlined"
          :loading="loading"
          @update:model-value="(value) => updateFilter('ai_message_type', value)"
        >
          <template v-slot:selection="{ item }">
            <v-chip size="small" :color="getMessageTypeColor(item.value)">
              <v-icon left size="small">{{ getMessageTypeIcon(item.value) }}</v-icon>
              {{ item.title }}
            </v-chip>
          </template>
        </v-select>
      </v-col>

      <!-- LLM服务筛选 -->
      <v-col cols="12" md="2">
        <v-select
          :model-value="localFilters.llm_service"
          :items="llmServiceOptions"
          label="LLM服务"
          clearable
          dense
          variant="outlined"
          :loading="loading"
          @update:model-value="(value) => updateFilter('llm_service', value)"
        >
          <template v-slot:selection="{ item }">
            <v-chip size="small" :color="getLLMServiceColor(item.value)">
              <v-icon left size="small">{{ getLLMServiceIcon(item.value) }}</v-icon>
              {{ item.title }}
            </v-chip>
          </template>
        </v-select>
      </v-col>
    </v-row>

    <!-- 第二行：置信度等级、日期范围和搜索 -->
    <v-row>
      <!-- 置信度等级选择 -->
      <v-col cols="12" md="3">
        <v-select
          :model-value="localFilters.confidence_level"
          :items="confidenceLevelOptions"
          label="置信度等级"
          clearable
          dense
          variant="outlined"
          :loading="loading"
          @update:model-value="(value) => updateFilter('confidence_level', value)"
        >
          <template v-slot:prepend-inner>
            <v-icon size="small">mdi-chart-line</v-icon>
          </template>
        </v-select>
      </v-col>

      <!-- 日期范围选择器 -->
      <v-col cols="12" md="3">
        <v-menu
          v-model="dateMenu"
          :close-on-content-click="false"
          transition="scale-transition"
          offset-y
          min-width="auto"
        >
          <template v-slot:activator="{ props }">
            <v-text-field
              v-model="dateRangeText"
              label="日期范围"
              readonly
              clearable
              dense
              variant="outlined"
              :loading="loading"
              v-bind="props"
              @click:clear="clearDateRange"
            >
              <template v-slot:prepend-inner>
                <v-icon size="small">mdi-calendar</v-icon>
              </template>
            </v-text-field>
          </template>
          <v-date-picker
            v-model="localFilters.date_range"
            range
            color="primary"
            @update:model-value="handleDateChange"
          >
            <template v-slot:actions>
              <v-btn
                variant="text"
                @click="dateMenu = false"
              >
                取消
              </v-btn>
              <v-btn
                color="primary"
                variant="text"
                @click="confirmDateRange"
              >
                确定
              </v-btn>
            </template>
          </v-date-picker>
        </v-menu>
      </v-col>

      <!-- 搜索输入框 -->
      <v-col cols="12" md="6">
        <div class="d-flex align-center gap-2">
          <v-text-field
            :model-value="localFilters.search"
            label="搜索信号内容"
            clearable
            dense
            variant="outlined"
            :loading="loading"
            id="search-input"
            data-testid="search-input"
            @update:model-value="(value) => updateFilter('search', value)"
            @keyup.enter="triggerSearch"
            placeholder="输入关键词搜索..."
          >
            <template v-slot:prepend-inner>
              <v-icon size="small">mdi-magnify</v-icon>
            </template>
          </v-text-field>
          <v-btn
            color="primary"
            variant="outlined"
            size="small"
            :loading="loading"
            data-testid="search-btn"
            @click="triggerSearch"
          >
            <v-icon>mdi-magnify</v-icon>
          </v-btn>
        </div>
      </v-col>
    </v-row>

    <!-- 快速筛选按钮 -->
    <v-row class="mt-2">
      <v-col cols="12">
        <div class="d-flex align-center flex-wrap gap-2">
          <v-label class="text-caption text-medium-emphasis mr-2">快速筛选:</v-label>

          <v-chip
            size="small"
            variant="outlined"
            :color="isQuickFilterActive('today') ? 'primary' : 'default'"
            @click="applyQuickFilter('today')"
            :loading="loading"
          >
            <v-icon left size="small">mdi-calendar-today</v-icon>
            今天
          </v-chip>

          <v-chip
            size="small"
            variant="outlined"
            :color="isQuickFilterActive('week') ? 'primary' : 'default'"
            @click="applyQuickFilter('week')"
            :loading="loading"
          >
            <v-icon left size="small">mdi-calendar-week</v-icon>
            本周
          </v-chip>

          <v-chip
            size="small"
            variant="outlined"
            :color="isQuickFilterActive('unprocessed') ? 'warning' : 'default'"
            @click="applyQuickFilter('unprocessed')"
            :loading="loading"
          >
            <v-icon left size="small">mdi-clock-outline</v-icon>
            未处理
          </v-chip>

          <v-chip
            size="small"
            variant="outlined"
            :color="isQuickFilterActive('high_confidence') ? 'success' : 'default'"
            @click="applyQuickFilter('high_confidence')"
            :loading="loading"
          >
            <v-icon left size="small">mdi-trending-up</v-icon>
            高置信度
          </v-chip>

          <v-chip
            size="small"
            variant="outlined"
            :color="isQuickFilterActive('trading_signals') ? 'info' : 'default'"
            @click="applyQuickFilter('trading_signals')"
            :loading="loading"
          >
            <v-icon left size="small">mdi-chart-line</v-icon>
            交易信号
          </v-chip>

          <v-chip
            size="small"
            variant="outlined"
            :color="isQuickFilterActive('ai_failed') ? 'error' : 'default'"
            @click="applyQuickFilter('ai_failed')"
            :loading="loading"
          >
            <v-icon left size="small">mdi-alert-circle</v-icon>
            解析失败
          </v-chip>

          <v-spacer></v-spacer>

          <!-- 重置按钮 -->
          <v-btn
            size="small"
            variant="outlined"
            color="grey"
            @click="resetFilters"
            :loading="loading"
            data-testid="clear-filters-btn"
          >
            <v-icon left size="small">mdi-refresh</v-icon>
            重置筛选
          </v-btn>
        </div>
      </v-col>
    </v-row>
  </v-card-text>
</template>

<script setup>
import { ref, reactive, computed, watch, nextTick, onMounted } from 'vue'

// Props
const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  },
  loading: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'filter-change'])

// 响应式数据
const dateMenu = ref(false)

// 本地筛选器状态 - 简化的响应式对象
const localFilters = reactive({
  platform: '',
  status: '',
  channel_id: '',
  ai_parsed: '',
  ai_message_type: '',
  llm_service: '',
  confidence_level: '',
  confidence_range: [0, 100],
  date_range: null,
  search: ''
})

// 初始化时同步props值
const initializeFilters = () => {
  if (props.modelValue && typeof props.modelValue === 'object') {
    Object.keys(localFilters).forEach(key => {
      if (props.modelValue[key] !== undefined) {
        localFilters[key] = props.modelValue[key]
      }
    })
  }
}

// 组件挂载时初始化
onMounted(() => {
  console.log('🔍 [SignalFilters] 组件挂载，初始化筛选器')
  initializeFilters()
  console.log('🔍 [SignalFilters] 初始化后的localFilters:', JSON.stringify(localFilters))
})

// 平台选项
const platformOptions = [
  { title: 'Discord', value: 'discord' },
  { title: 'Telegram', value: 'telegram' },
  { title: '手动输入', value: 'manual' }
]

// 处理状态选项
const processedOptions = [
  { title: '已处理', value: 'processed' },
  { title: '未处理', value: 'unprocessed' }
]

// AI解析状态选项 - 与后端AIParseStatus枚举保持一致
const aiParseStatusOptions = [
  { title: '待解析', value: 'pending' },
  { title: '解析成功', value: 'success' },
  { title: '解析失败', value: 'failed' },
  { title: '部分解析', value: 'partial' }
]

// AI消息类型选项 - 与后端MessageTypeAI枚举保持一致
const messageTypeAIOptions = [
  { title: '普通消息', value: 'normal_message' },
  { title: '交易信号', value: 'trading_signal' },
  { title: '市场分析', value: 'market_analysis' },
  { title: '价格提醒', value: 'price_alert' },
  { title: '模糊信号', value: 'ambiguous' }
]

// LLM服务选项 - 与后端LLMService枚举保持一致
const llmServiceOptions = [
  { title: 'DeepSeek', value: 'deepseek' },
  { title: 'Gemini', value: 'gemini' },
  { title: 'ChatGPT', value: 'chatgpt' },
  { title: 'Claude', value: 'claude' }
]

// 置信度等级选项
const confidenceLevelOptions = [
  { title: '全部', value: '' },
  { title: '高 (80%-100%)', value: 'high' },
  { title: '中 (50%-79%)', value: 'medium' },
  { title: '低 (0%-49%)', value: 'low' }
]

// 计算属性
const dateRangeText = computed(() => {
  if (!localFilters.date_range || localFilters.date_range.length !== 2) {
    return ''
  }
  return `${localFilters.date_range[0]} ~ ${localFilters.date_range[1]}`
})

// 方法
const getPlatformColor = (platform) => {
  const colors = {
    discord: 'indigo',
    telegram: 'blue',
    manual: 'green'
  }
  return colors[platform] || 'default'
}

const getPlatformIcon = (platform) => {
  const icons = {
    discord: 'mdi-discord',
    telegram: 'mdi-telegram',
    manual: 'mdi-pencil'
  }
  return icons[platform] || 'mdi-help-circle'
}

const formatConfidenceRange = (range) => {
  if (!range || range.length !== 2) return '0% - 100%'
  return `${range[0]}% - ${range[1]}%`
}

// 向后兼容
const formatStrengthRange = formatConfidenceRange

// AI解析状态相关方法
const getAIStatusColor = (status) => {
  const colors = {
    pending: 'warning',
    success: 'success',
    failed: 'error',
    partial: 'info'
  }
  return colors[status] || 'default'
}

const getAIStatusIcon = (status) => {
  const icons = {
    pending: 'mdi-clock-outline',
    success: 'mdi-check-circle',
    failed: 'mdi-alert-circle',
    partial: 'mdi-progress-check'
  }
  return icons[status] || 'mdi-help-circle'
}

// 消息类型相关方法
const getMessageTypeColor = (type) => {
  const colors = {
    normal_message: 'grey',
    trading_signal: 'success',
    market_analysis: 'info',
    price_alert: 'warning',
    ambiguous: 'orange'
  }
  return colors[type] || 'default'
}

const getMessageTypeIcon = (type) => {
  const icons = {
    normal_message: 'mdi-message-text',
    trading_signal: 'mdi-chart-line',
    market_analysis: 'mdi-chart-areaspline',
    price_alert: 'mdi-bell-alert',
    ambiguous: 'mdi-help-circle'
  }
  return icons[type] || 'mdi-message'
}

// LLM服务相关方法
const getLLMServiceColor = (service) => {
  const colors = {
    deepseek: 'deep-purple',
    gemini: 'blue',
    chatgpt: 'green',
    claude: 'orange'
  }
  return colors[service] || 'default'
}

const getLLMServiceIcon = (service) => {
  const icons = {
    deepseek: 'mdi-brain',
    gemini: 'mdi-google',
    chatgpt: 'mdi-robot',
    claude: 'mdi-account-circle'
  }
  return icons[service] || 'mdi-robot-outline'
}

// 防抖处理输入框变化
const debounce = (func, wait) => {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

// 置信度等级到数值范围的转换
const convertConfidenceLevelToRange = (level) => {
  switch (level) {
    case 'high':
      return [80, 100]
    case 'medium':
      return [50, 79]
    case 'low':
      return [0, 49]
    default:
      return [0, 100] // 全部
  }
}

// 统一的筛选器更新处理函数 - 修复版本
const updateFilter = (field, value) => {
  console.log(`🔍 [SignalFilters] 更新字段 ${field}:`, value)

  // 直接更新本地状态
  localFilters[field] = value

  // 特殊处理：置信度等级转换为数值范围
  if (field === 'confidence_level') {
    localFilters.confidence_range = convertConfidenceLevelToRange(value)
    console.log(`🔍 [SignalFilters] 置信度等级 ${value} 转换为范围:`, localFilters.confidence_range)
  }

  // 创建完整的筛选器对象副本 - 保持原始值，不强制转换
  const updatedFilters = { ...localFilters }

  console.log(`🔍 [SignalFilters] 即将emit:`, JSON.stringify(updatedFilters))

  // 使用nextTick确保DOM更新后再emit
  nextTick(() => {
    emit('update:modelValue', updatedFilters)
    emit('filter-change')
    console.log(`🔍 [SignalFilters] emit完成`)
  })
}

// 搜索触发函数
const triggerSearch = () => {
  console.log('🔍 [SignalFilters] 触发搜索')
  updateFilter('search', localFilters.search)
}

const handleDateChange = () => {
  // 日期变化时不立即触发，等用户确认
  console.log('🔍 [SignalFilters] 日期选择变化')
}

const confirmDateRange = () => {
  console.log('🔍 [SignalFilters] 确认日期范围')
  dateMenu.value = false
  updateFilter('date_range', localFilters.date_range)
}

const clearDateRange = () => {
  console.log('🔍 [SignalFilters] 清除日期范围')
  updateFilter('date_range', null)
}

// 快速筛选功能
const isQuickFilterActive = (filterType) => {
  const today = new Date().toISOString().split('T')[0]
  const weekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]

  switch (filterType) {
    case 'today':
      return localFilters.date_range &&
             localFilters.date_range.length === 2 &&
             localFilters.date_range[0] === today &&
             localFilters.date_range[1] === today
    case 'week':
      return localFilters.date_range &&
             localFilters.date_range.length === 2 &&
             localFilters.date_range[0] === weekAgo &&
             localFilters.date_range[1] === today
    case 'unprocessed':
      return localFilters.status === 'unprocessed'
    case 'high_confidence':
      return localFilters.confidence_level === 'high'
    case 'trading_signals':
      return localFilters.ai_message_type === 'trading_signal'
    case 'ai_failed':
      return localFilters.ai_parsed === 'failed'
    // 向后兼容
    case 'high_strength':
      return localFilters.confidence_level === 'high'
    default:
      return false
  }
}

const applyQuickFilter = (filterType) => {
  const today = new Date().toISOString().split('T')[0]
  const weekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]

  switch (filterType) {
    case 'today':
      if (isQuickFilterActive('today')) {
        updateFilter('date_range', null)
      } else {
        updateFilter('date_range', [today, today])
      }
      break
    case 'week':
      if (isQuickFilterActive('week')) {
        updateFilter('date_range', null)
      } else {
        updateFilter('date_range', [weekAgo, today])
      }
      break
    case 'unprocessed':
      if (isQuickFilterActive('unprocessed')) {
        updateFilter('status', '')
      } else {
        updateFilter('status', 'unprocessed')
      }
      break
    case 'high_confidence':
      if (isQuickFilterActive('high_confidence')) {
        updateFilter('confidence_level', '')
      } else {
        updateFilter('confidence_level', 'high')
      }
      break
    case 'trading_signals':
      if (isQuickFilterActive('trading_signals')) {
        updateFilter('ai_message_type', '')
      } else {
        updateFilter('ai_message_type', 'trading_signal')
      }
      break
    case 'ai_failed':
      if (isQuickFilterActive('ai_failed')) {
        updateFilter('ai_parsed', '')
      } else {
        updateFilter('ai_parsed', 'failed')
      }
      break
    // 向后兼容
    case 'high_strength':
      if (isQuickFilterActive('high_strength')) {
        updateFilter('confidence_level', '')
      } else {
        updateFilter('confidence_level', 'high')
      }
      break
  }
}

const resetFilters = () => {
  console.log('🔍 重置所有筛选器')

  Object.assign(localFilters, {
    platform: '',
    status: '',
    channel_id: '',
    ai_parsed: '',
    ai_message_type: '',
    llm_service: '',
    confidence_level: '',
    confidence_range: [0, 100],
    date_range: null,
    search: ''
  })

  // 立即同步到父组件
  emit('update:modelValue', { ...localFilters })
  emit('filter-change')
}

// 监听props变化，同步到localFilters（最简化版本）
watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue && typeof newValue === 'object') {
      console.log('🔍 [SignalFilters] props变化，同步到本地:', JSON.stringify(newValue))
      // 简单的一次性同步，避免复杂的比较逻辑
      Object.assign(localFilters, newValue)
    }
  },
  { immediate: true } // 移除deep监听，避免过度触发
)

// 组件挂载时的初始化
onMounted(() => {
  console.log('🔍 SignalFilters组件已挂载')
  console.log('🔍 初始props.modelValue:', JSON.stringify(props.modelValue))
  console.log('🔍 初始localFilters:', JSON.stringify(localFilters))
})

// 平台选择变化处理 - 重构版本
const handlePlatformChange = (value) => {
  console.log('🔍 [SignalFilters] 平台选择变化:', value)
  updateFilter('platform', value)
}

// 搜索输入变化处理 - 重构版本
const handleSearchChange = (value) => {
  console.log('🔍 [SignalFilters] 搜索输入变化:', value)
  updateFilter('search', value)
}

// 搜索提交处理
const handleSearchSubmit = () => {
  console.log('🔍 [SignalFilters] 搜索提交')
  updateFilter('search', localFilters.search)
}

// 通用筛选器变化处理（保留用于其他地方调用）
const handleFilterChange = () => {
  console.log('🔍 [SignalFilters] 手动触发筛选器变化事件')
  emit('filter-change')
}
</script>

<style scoped>
.v-card-text {
  padding: 16px 24px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.12);
}

.gap-2 {
  gap: 8px;
}

.v-range-slider {
  margin-top: 8px;
}

.v-chip {
  transition: all 0.2s ease;
}

.v-chip:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.v-label {
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 960px) {
  .v-col {
    padding: 4px 8px;
  }

  .gap-2 {
    gap: 4px;
  }

  .v-chip {
    margin: 2px;
  }
}

/* 日期选择器样式优化 */
.v-date-picker {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 滑块样式优化 */
.v-slider-thumb {
  transition: all 0.2s ease;
}

.v-slider-thumb:hover {
  transform: scale(1.1);
}
</style>