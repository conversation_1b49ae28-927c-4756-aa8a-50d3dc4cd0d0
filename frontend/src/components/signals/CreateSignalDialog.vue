<template>
  <v-dialog
    v-model="dialogVisible"
    max-width="600"
    persistent
    scrollable
    data-testid="create-signal-dialog"
  >
    <v-card>
      <!-- 对话框头部 -->
      <v-card-title class="d-flex align-center">
        <v-icon left color="primary">mdi-plus-circle</v-icon>
        <span>创建新信号</span>
        <v-spacer />
        <v-btn
          icon="mdi-close"
          variant="text"
          @click="closeDialog"
          :disabled="submitting"
        />
      </v-card-title>

      <v-divider />

      <!-- 表单内容 -->
      <v-card-text>
        <v-form ref="formRef" v-model="formValid" @submit.prevent="submitForm">
          <v-container fluid>
            <!-- 平台选择 -->
            <v-row>
              <v-col cols="12">
                <v-select
                  v-model="formData.platform"
                  :items="platformOptions"
                  label="平台 *"
                  variant="outlined"
                  :rules="[rules.required]"
                  :disabled="submitting"
                  data-testid="create-signal-platform"
                >
                  <template v-slot:selection="{ item }">
                    <v-chip :color="getPlatformColor(item.value)" variant="outlined">
                      <v-icon left size="small">{{ getPlatformIcon(item.value) }}</v-icon>
                      {{ item.title }}
                    </v-chip>
                  </template>
                  <template v-slot:item="{ props, item }">
                    <v-list-item v-bind="props">
                      <template v-slot:prepend>
                        <v-icon :color="getPlatformColor(item.value)">
                          {{ getPlatformIcon(item.value) }}
                        </v-icon>
                      </template>
                    </v-list-item>
                  </template>
                </v-select>
              </v-col>
            </v-row>

            <!-- 信号内容 -->
            <v-row>
              <v-col cols="12">
                <v-textarea
                  v-model="formData.content"
                  label="信号内容 *"
                  variant="outlined"
                  :rules="[rules.required, rules.contentLength]"
                  :disabled="submitting"
                  rows="6"
                  counter="4000"
                  placeholder="请输入信号内容..."
                  auto-grow
                  data-testid="create-signal-content"
                />
              </v-col>
            </v-row>

            <!-- 可选字段 -->
            <v-expansion-panels v-model="expandedPanels" multiple>
              <v-expansion-panel value="optional">
                <v-expansion-panel-title>
                  <v-icon left>mdi-cog</v-icon>
                  可选字段
                </v-expansion-panel-title>
                <v-expansion-panel-text>
                  <v-row>
                    <!-- 频道名称 -->
                    <v-col cols="12" sm="6">
                      <v-text-field
                        v-model="formData.channel_name"
                        label="频道名称"
                        variant="outlined"
                        :disabled="submitting"
                        placeholder="例如: vip-signals"
                      >
                        <template v-slot:prepend-inner>
                          <v-icon size="small">mdi-pound</v-icon>
                        </template>
                      </v-text-field>
                    </v-col>

                    <!-- 作者名称 -->
                    <v-col cols="12" sm="6">
                      <v-text-field
                        v-model="formData.author_name"
                        label="作者名称"
                        variant="outlined"
                        :disabled="submitting"
                        placeholder="例如: TraderBot"
                      >
                        <template v-slot:prepend-inner>
                          <v-icon size="small">mdi-account</v-icon>
                        </template>
                      </v-text-field>
                    </v-col>

                    <!-- 原始内容 -->
                    <v-col cols="12">
                      <v-textarea
                        v-model="formData.raw_content"
                        label="原始内容"
                        variant="outlined"
                        :disabled="submitting"
                        rows="3"
                        placeholder="如果有原始格式的内容，可以在这里输入..."
                        auto-grow
                      />
                    </v-col>
                  </v-row>
                </v-expansion-panel-text>
              </v-expansion-panel>

              <!-- 元数据 -->
              <v-expansion-panel value="metadata">
                <v-expansion-panel-title>
                  <v-icon left>mdi-code-json</v-icon>
                  元数据 (JSON)
                </v-expansion-panel-title>
                <v-expansion-panel-text>
                  <v-textarea
                    v-model="metadataText"
                    label="元数据 (JSON格式)"
                    variant="outlined"
                    :disabled="submitting"
                    :rules="[rules.validJSON]"
                    rows="4"
                    placeholder='{"key": "value"}'
                    auto-grow
                  />
                  <v-alert
                    v-if="metadataError"
                    type="error"
                    variant="tonal"
                    class="mt-2"
                  >
                    {{ metadataError }}
                  </v-alert>
                </v-expansion-panel-text>
              </v-expansion-panel>
            </v-expansion-panels>

            <!-- 预览 -->
            <v-row class="mt-4">
              <v-col cols="12">
                <v-card variant="outlined">
                  <v-card-title class="text-h6">
                    <v-icon left>mdi-eye</v-icon>
                    预览
                  </v-card-title>
                  <v-card-text>
                    <div class="preview-content">
                      <div class="d-flex align-center mb-2">
                        <v-chip
                          v-if="formData.platform"
                          :color="getPlatformColor(formData.platform)"
                          variant="outlined"
                          size="small"
                          class="mr-2"
                        >
                          <v-icon left size="small">{{ getPlatformIcon(formData.platform) }}</v-icon>
                          {{ getPlatformName(formData.platform) }}
                        </v-chip>
                        <span v-if="formData.channel_name" class="text-caption text-medium-emphasis">
                          <v-icon size="small" class="mr-1">mdi-pound</v-icon>
                          {{ formData.channel_name }}
                        </span>
                      </div>
                      <div v-if="formData.author_name" class="text-caption text-medium-emphasis mb-2">
                        作者: {{ formData.author_name }}
                      </div>
                      <div class="content-preview">
                        {{ formData.content || '请输入信号内容...' }}
                      </div>
                    </div>
                  </v-card-text>
                </v-card>
              </v-col>
            </v-row>
          </v-container>
        </v-form>
      </v-card-text>

      <!-- 对话框底部 -->
      <v-card-actions>
        <v-spacer />
        <v-btn
          variant="text"
          @click="closeDialog"
          :disabled="submitting"
        >
          取消
        </v-btn>
        <v-btn
          color="primary"
          @click="submitForm"
          :loading="submitting"
          :disabled="!formValid"
          data-testid="create-signal-submit"
        >
          <v-icon left>mdi-content-save</v-icon>
          创建信号
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { useSnackbar } from '@/composables/useSnackbar'
import { signalApi } from '@/api/signals'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'created'])

// 组合式API
const { showSnackbar } = useSnackbar()

// 响应式数据
const formRef = ref(null)
const formValid = ref(false)
const submitting = ref(false)
const expandedPanels = ref(['optional'])
const metadataText = ref('')
const metadataError = ref('')

// 表单数据
const formData = reactive({
  platform: '',
  content: '',
  channel_name: '',
  author_name: '',
  raw_content: '',
  metadata: null
})

// 平台选项
const platformOptions = [
  { title: 'Discord', value: 'discord' },
  { title: 'Telegram', value: 'telegram' },
  { title: '手动输入', value: 'manual' }
]

// 表单验证规则
const rules = {
  required: (value) => !!value || '此字段为必填项',
  contentLength: (value) => {
    if (!value) return true
    return value.length <= 4000 || '内容长度不能超过4000字符'
  },
  validJSON: (value) => {
    if (!value) return true
    try {
      JSON.parse(value)
      metadataError.value = ''
      return true
    } catch (error) {
      metadataError.value = `JSON格式错误: ${error.message}`
      return false
    }
  }
}

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 方法
const getPlatformColor = (platform) => {
  const colors = {
    discord: 'indigo',
    telegram: 'blue',
    manual: 'green'
  }
  return colors[platform] || 'default'
}

const getPlatformIcon = (platform) => {
  const icons = {
    discord: 'mdi-discord',
    telegram: 'mdi-telegram',
    manual: 'mdi-pencil'
  }
  return icons[platform] || 'mdi-help-circle'
}

const getPlatformName = (platform) => {
  const names = {
    discord: 'Discord',
    telegram: 'Telegram',
    manual: '手动'
  }
  return names[platform] || platform
}

const resetForm = () => {
  formData.platform = ''
  formData.content = ''
  formData.channel_name = ''
  formData.author_name = ''
  formData.raw_content = ''
  formData.metadata = null
  metadataText.value = ''
  metadataError.value = ''
  expandedPanels.value = ['optional']

  if (formRef.value) {
    formRef.value.resetValidation()
  }
}

const closeDialog = () => {
  if (!submitting.value) {
    dialogVisible.value = false
    resetForm()
  }
}

const submitForm = async () => {
  if (!formRef.value) return

  const { valid } = await formRef.value.validate()
  if (!valid) return

  try {
    submitting.value = true

    // 处理元数据
    let metadata = null
    if (metadataText.value.trim()) {
      try {
        metadata = JSON.parse(metadataText.value)
      } catch (error) {
        showSnackbar('元数据JSON格式错误', 'error')
        return
      }
    }

    // 构建请求数据
    const requestData = {
      platform: formData.platform,
      content: formData.content.trim(),
      channel_name: formData.channel_name.trim() || null,
      author_name: formData.author_name.trim() || null,
      raw_content: formData.raw_content.trim() || null,
      metadata: metadata
    }

    // 发送请求
    const response = await signalApi.createSignal(requestData)

    if (response.success) {
      showSnackbar('信号创建成功', 'success')
      emit('created', response.data)
      closeDialog()
    } else {
      showSnackbar(response.message || '创建失败', 'error')
    }
  } catch (error) {
    console.error('创建信号失败:', error)
    showSnackbar('创建信号失败', 'error')
  } finally {
    submitting.value = false
  }
}

// 监听元数据文本变化，更新formData
watch(
  () => metadataText.value,
  (newValue) => {
    if (!newValue.trim()) {
      formData.metadata = null
      return
    }

    try {
      formData.metadata = JSON.parse(newValue)
      metadataError.value = ''
    } catch (error) {
      formData.metadata = null
      metadataError.value = `JSON格式错误: ${error.message}`
    }
  }
)

// 监听对话框关闭，重置表单
watch(
  () => props.modelValue,
  (newValue) => {
    if (!newValue) {
      // 延迟重置，避免关闭动画时看到表单重置
      setTimeout(() => {
        resetForm()
      }, 300)
    }
  }
)
</script>

<style scoped>
.v-dialog .v-card {
  max-height: 90vh;
}

.preview-content {
  min-height: 80px;
  padding: 16px;
  background-color: rgba(0, 0, 0, 0.02);
  border: 1px solid rgba(0, 0, 0, 0.12);
  border-radius: 4px;
}

.content-preview {
  white-space: pre-wrap;
  word-break: break-word;
  line-height: 1.5;
  color: rgba(0, 0, 0, 0.87);
}

.v-expansion-panels {
  margin: 16px 0;
}

.v-expansion-panel-text {
  padding: 16px;
}

.v-textarea >>> .v-field__input {
  font-family: 'Roboto Mono', monospace;
  font-size: 14px;
}

.v-form {
  width: 100%;
}

/* 响应式设计 */
@media (max-width: 600px) {
  .v-dialog .v-card {
    margin: 16px;
    max-height: calc(100vh - 32px);
  }

  .v-container {
    padding: 12px;
  }

  .preview-content {
    padding: 12px;
  }

  .v-card-title {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .v-card-title .v-spacer {
    display: none;
  }
}

/* 表单样式优化 */
.v-text-field,
.v-textarea,
.v-select {
  margin-bottom: 8px;
}

.v-expansion-panel-title {
  font-weight: 500;
}

.v-chip {
  font-weight: 500;
}

/* 预览卡片样式 */
.v-card.v-card--variant-outlined {
  border: 1px solid rgba(0, 0, 0, 0.12);
}

/* 按钮样式 */
.v-card-actions {
  padding: 16px 24px;
  border-top: 1px solid rgba(0, 0, 0, 0.12);
}

/* 错误提示样式 */
.v-alert {
  font-size: 14px;
}
</style>