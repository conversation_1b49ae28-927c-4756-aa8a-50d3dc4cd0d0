<template>
  <v-card
    class="signal-card"
    :class="[
      themeClass,
      { 'signal-card--selected': selected },
      { 'signal-card--hover': hover }
    ]"
    @click="handleClick"
    :hover="hover"
  >
    <!-- 卡片头部 -->
    <v-card-title class="signal-card-header">
      <!-- 平台标识和选择框 -->
      <div class="signal-card-header-row">
        <v-chip
          size="small"
          :color="getPlatformColor(signal.platform)"
          variant="outlined"
          class="platform-chip"
        >
          <v-icon left size="small">{{ getPlatformIcon(signal.platform) }}</v-icon>
          {{ getPlatformName(signal.platform) }}
        </v-chip>

        <div class="signal-card-actions">
          <v-checkbox
            v-if="selectable"
            :model-value="selected"
            @change="handleSelectionChange"
            @click.stop
            hide-details
            density="compact"
            class="selection-checkbox"
          />
          
          <v-btn
            v-if="showThemeToggle"
            icon
            size="x-small"
            variant="text"
            @click.stop="toggleTheme"
            class="theme-toggle"
          >
            <v-icon size="16">{{ getThemeIcon() }}</v-icon>
          </v-btn>
        </div>
      </div>
    </v-card-title>

    <!-- 卡片内容 -->
    <v-card-text class="signal-card-content">
      <!-- 动态渲染主题内容组件 -->
      <component
        :is="contentComponent"
        :signal="signal"
      />
    </v-card-text>

    <!-- 卡片操作 -->
    <v-card-actions v-if="showActions" class="signal-card-actions-bar">
      <v-btn
        size="small"
        variant="text"
        @click.stop="handleViewDetails"
      >
        <v-icon left size="small">mdi-eye</v-icon>
        查看详情
      </v-btn>

      <v-spacer />

      <v-btn
        size="small"
        variant="text"
        :color="signal.is_processed ? 'warning' : 'success'"
        @click.stop="handleToggleProcessed"
      >
        <v-icon size="small">
          {{ signal.is_processed ? 'mdi-undo' : 'mdi-check' }}
        </v-icon>
      </v-btn>

      <v-btn
        v-if="showDeleteAction"
        size="small"
        variant="text"
        color="error"
        @click.stop="handleDelete"
      >
        <v-icon size="small">mdi-delete</v-icon>
      </v-btn>
    </v-card-actions>
  </v-card>
</template>

<script setup>
import { computed, ref } from 'vue'
import { getContentComponent, getThemeClass } from './themes'
import DiscordCardContent from './themes/DiscordCardContent.vue'
import GenericCardContent from './themes/GenericCardContent.vue'

// Props
const props = defineProps({
  signal: {
    type: Object,
    required: true
  },
  theme: {
    type: String,
    default: 'auto' // auto, discord, telegram, generic
  },
  selected: {
    type: Boolean,
    default: false
  },
  selectable: {
    type: Boolean,
    default: true
  },
  hover: {
    type: Boolean,
    default: true
  },
  showActions: {
    type: Boolean,
    default: true
  },
  showDeleteAction: {
    type: Boolean,
    default: true
  },
  showThemeToggle: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits([
  'click',
  'selection-change',
  'view-details',
  'toggle-processed',
  'delete',
  'theme-change'
])

// 响应式数据
const currentTheme = ref(props.theme)

// 计算属性
const contentComponent = computed(() => {
  const platform = props.signal.platform
  const componentName = getContentComponent(platform)

  // 映射组件名称到实际组件
  const componentMap = {
    'DiscordCardContent': DiscordCardContent,
    'GenericCardContent': GenericCardContent
  }

  return componentMap[componentName] || GenericCardContent
})

const themeClass = computed(() => {
  return getThemeClass(props.signal.platform)
})

// 方法
const handleClick = () => {
  emit('click', props.signal)
}

const handleSelectionChange = (value) => {
  emit('selection-change', props.signal.id, value)
}

const getThemeIcon = () => {
  const platform = props.signal.platform
  return platform === 'discord' ? 'mdi-discord' : 'mdi-card-outline'
}

const handleViewDetails = () => {
  emit('view-details', props.signal)
}

const handleToggleProcessed = () => {
  emit('toggle-processed', props.signal)
}

const handleDelete = () => {
  emit('delete', props.signal)
}

const toggleTheme = () => {
  // 循环切换主题
  const availableThemes = ['auto', 'discord', 'generic']
  const currentIndex = availableThemes.indexOf(currentTheme.value)
  const nextIndex = (currentIndex + 1) % availableThemes.length
  currentTheme.value = availableThemes[nextIndex]
  
  emit('theme-change', currentTheme.value, props.signal)
}

const getPlatformColor = (platform) => {
  const colors = {
    discord: 'indigo',
    telegram: 'blue',
    manual: 'green'
  }
  return colors[platform] || 'default'
}

const getPlatformIcon = (platform) => {
  const icons = {
    discord: 'mdi-discord',
    telegram: 'mdi-telegram',
    manual: 'mdi-pencil'
  }
  return icons[platform] || 'mdi-help-circle'
}

const getPlatformName = (platform) => {
  const names = {
    discord: 'Discord',
    telegram: 'Telegram',
    manual: '手动'
  }
  return names[platform] || platform
}
</script>

<style scoped>
/* 基础卡片样式 */
.signal-card {
  transition: all 0.2s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.signal-card--hover:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.signal-card--selected {
  border: 2px solid rgb(var(--v-theme-primary));
  background-color: rgba(var(--v-theme-primary), 0.05);
}

/* 卡片头部 */
.signal-card-header {
  padding: 12px 16px 8px 16px;
}

.signal-card-header-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.platform-chip {
  flex-shrink: 0;
}

.signal-card-actions {
  display: flex;
  align-items: center;
  gap: 4px;
}

.selection-checkbox {
  flex-shrink: 0;
}

.theme-toggle {
  opacity: 0.7;
  transition: opacity 0.2s ease;
}

.theme-toggle:hover {
  opacity: 1;
}

/* 卡片内容 */
.signal-card-content {
  padding: 0 16px 12px 16px;
}

/* 卡片操作栏 */
.signal-card-actions-bar {
  padding: 8px 16px 12px 16px;
  border-top: 1px solid rgba(var(--v-theme-outline-variant), 0.5);
}

/* Discord 主题样式 */
.signal-card--discord {
  background: linear-gradient(135deg, #36393f 0%, #2f3136 100%);
  color: #dcddde;
  border: 1px solid #4f545c;
}

.signal-card--discord .signal-card-header {
  border-bottom: 1px solid rgba(79, 84, 92, 0.3);
}

.signal-card--discord .signal-card-actions-bar {
  border-top-color: rgba(79, 84, 92, 0.3);
  background-color: rgba(47, 49, 54, 0.5);
}

.signal-card--discord:hover {
  box-shadow: 0 4px 16px rgba(88, 101, 242, 0.3);
}

/* Telegram 主题样式 */
.signal-card--telegram {
  background: #ffffff;
  color: #000000;
  border: 1px solid #e1e5e9;
  border-radius: 12px;
}

.signal-card--telegram .signal-card-header {
  border-bottom: 1px solid #e1e5e9;
}

.signal-card--telegram .signal-card-actions-bar {
  border-top-color: #e1e5e9;
  background-color: rgba(245, 245, 245, 0.5);
}

.signal-card--telegram:hover {
  box-shadow: 0 2px 8px rgba(0, 136, 204, 0.15);
}

/* 通用主题样式 */
.signal-card--generic {
  background: rgb(var(--v-theme-surface));
  color: rgb(var(--v-theme-on-surface));
  border: 1px solid rgb(var(--v-theme-outline-variant));
}

.signal-card--generic .signal-card-header {
  border-bottom: 1px solid rgb(var(--v-theme-outline-variant));
}

.signal-card--generic .signal-card-actions-bar {
  border-top-color: rgb(var(--v-theme-outline-variant));
  background-color: rgba(var(--v-theme-surface-variant), 0.5);
}

.signal-card--generic:hover {
  box-shadow: 0 4px 8px rgba(var(--v-theme-shadow), 0.15);
}

/* 响应式设计 */
@media (max-width: 600px) {
  .signal-card-header {
    padding: 8px 12px 6px 12px;
  }
  
  .signal-card-content {
    padding: 0 12px 8px 12px;
  }
  
  .signal-card-actions-bar {
    padding: 6px 12px 8px 12px;
  }
  
  .platform-chip {
    font-size: 11px;
  }
}
</style>
