<template>
  <v-card class="alert-notification-panel" elevation="2">
    <v-card-title class="d-flex align-center justify-space-between">
      <div class="d-flex align-center">
        <v-icon 
          class="mr-2" 
          :color="hasAlerts ? 'error' : 'success'"
        >
          {{ hasAlerts ? 'mdi-alert-circle' : 'mdi-shield-check' }}
        </v-icon>
        <span>告警通知</span>
        <v-chip
          v-if="alerts.length > 0"
          size="small"
          :color="criticalCount > 0 ? 'error' : 'warning'"
          variant="tonal"
          class="ml-2"
        >
          {{ alerts.length }}
        </v-chip>
      </div>
      
      <!-- 操作按钮 -->
      <div class="d-flex align-center">
        <v-btn
          v-if="hasAlerts"
          icon
          size="small"
          variant="text"
          @click="$emit('clear-all')"
        >
          <v-icon>mdi-notification-clear-all</v-icon>
          <v-tooltip activator="parent">清除所有告警</v-tooltip>
        </v-btn>
        
        <v-btn
          icon
          size="small"
          variant="text"
          @click="$emit('refresh')"
          :loading="loading"
        >
          <v-icon>mdi-refresh</v-icon>
          <v-tooltip activator="parent">刷新告警</v-tooltip>
        </v-btn>
      </div>
    </v-card-title>
    
    <v-divider />
    
    <!-- 无告警状态 -->
    <div v-if="!loading && alerts.length === 0" class="pa-6 text-center">
      <v-icon size="48" color="success" class="mb-3">
        mdi-shield-check-outline
      </v-icon>
      <div class="text-h6 text-success mb-2">系统运行正常</div>
      <div class="text-body-2 text-medium-emphasis">
        当前没有活跃的告警信息
      </div>
    </div>
    
    <!-- 加载状态 -->
    <div v-else-if="loading" class="pa-4">
      <v-skeleton-loader
        v-for="i in 3"
        :key="i"
        type="list-item-two-line"
        class="mb-2"
      />
    </div>
    
    <!-- 告警列表 -->
    <v-list v-else class="pa-0">
      <template v-for="(alert, index) in sortedAlerts" :key="alert.id">
        <v-list-item
          class="alert-item"
          :class="getAlertItemClass(alert)"
        >
          <!-- 严重程度指示器 -->
          <template #prepend>
            <v-avatar
              size="36"
              :color="getSeverityColor(alert.severity)"
              variant="tonal"
            >
              <v-icon :color="getSeverityColor(alert.severity)">
                {{ getSeverityIcon(alert.severity) }}
              </v-icon>
            </v-avatar>
          </template>
          
          <!-- 告警内容 -->
          <v-list-item-title class="text-wrap">
            {{ alert.title || alert.message }}
          </v-list-item-title>
          
          <v-list-item-subtitle class="mt-1">
            <div class="d-flex align-center justify-space-between">
              <div class="d-flex align-center">
                <!-- 严重程度标签 -->
                <v-chip
                  size="x-small"
                  :color="getSeverityColor(alert.severity)"
                  variant="tonal"
                  class="mr-2"
                >
                  {{ getSeverityText(alert.severity) }}
                </v-chip>
                
                <!-- 告警类型 -->
                <span class="text-caption">{{ getAlertTypeText(alert.type) }}</span>
                
                <!-- 数量指示器 -->
                <span v-if="alert.count" class="text-caption ml-2">
                  ({{ alert.count }})
                </span>
              </div>
              
              <!-- 时间 -->
              <span class="text-caption text-disabled">
                {{ formatTime(alert.created_at) }}
              </span>
            </div>
          </v-list-item-subtitle>
          
          <!-- 操作按钮 -->
          <template #append>
            <div class="d-flex flex-column">
              <!-- 操作菜单 -->
              <v-menu v-if="alert.actions && alert.actions.length > 0">
                <template #activator="{ props }">
                  <v-btn
                    icon
                    size="small"
                    variant="text"
                    v-bind="props"
                  >
                    <v-icon>mdi-dots-vertical</v-icon>
                  </v-btn>
                </template>
                
                <v-list>
                  <v-list-item
                    v-for="action in alert.actions"
                    :key="action.action"
                    @click="handleAlertAction(alert, action)"
                  >
                    <v-list-item-title>{{ action.label }}</v-list-item-title>
                  </v-list-item>
                </v-list>
              </v-menu>
              
              <!-- 关闭按钮 -->
              <v-btn
                icon
                size="small"
                variant="text"
                @click="$emit('dismiss-alert', alert.id)"
              >
                <v-icon>mdi-close</v-icon>
              </v-btn>
            </div>
          </template>
        </v-list-item>
        
        <v-divider v-if="index < sortedAlerts.length - 1" />
      </template>
    </v-list>
    
    <!-- 查看历史告警 -->
    <v-card-actions v-if="hasAlerts">
      <v-spacer />
      <v-btn
        variant="text"
        color="primary"
        size="small"
        @click="$emit('view-history')"
      >
        查看历史告警
        <v-icon end>mdi-history</v-icon>
      </v-btn>
    </v-card-actions>
  </v-card>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  alerts: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits([
  'dismiss-alert',
  'clear-all',
  'refresh',
  'view-history',
  'alert-action'
])

// 计算属性
const hasAlerts = computed(() => props.alerts.length > 0)

const criticalCount = computed(() => 
  props.alerts.filter(alert => alert.severity === 'critical').length
)

// 按严重程度排序的告警列表
const sortedAlerts = computed(() => {
  const severityOrder = { 'critical': 0, 'high': 1, 'medium': 2, 'low': 3 }
  
  return [...props.alerts].sort((a, b) => {
    const severityDiff = (severityOrder[a.severity] || 99) - (severityOrder[b.severity] || 99)
    if (severityDiff !== 0) return severityDiff
    
    // 相同严重程度按时间排序（最新的在前）
    return new Date(b.created_at) - new Date(a.created_at)
  })
})

// 获取严重程度颜色
const getSeverityColor = (severity) => {
  const colorMap = {
    'critical': 'error',
    'high': 'error',
    'medium': 'warning',
    'low': 'info'
  }
  return colorMap[severity] || 'grey'
}

// 获取严重程度图标
const getSeverityIcon = (severity) => {
  const iconMap = {
    'critical': 'mdi-alert-octagon',
    'high': 'mdi-alert-circle',
    'medium': 'mdi-alert',
    'low': 'mdi-information'
  }
  return iconMap[severity] || 'mdi-help-circle'
}

// 获取严重程度文本
const getSeverityText = (severity) => {
  const textMap = {
    'critical': '严重',
    'high': '高',
    'medium': '中',
    'low': '低'
  }
  return textMap[severity] || '未知'
}

// 获取告警类型文本
const getAlertTypeText = (type) => {
  const typeMap = {
    'stuck_signals': '信号卡住',
    'high_failure_rate': '失败率过高',
    'slow_processing': '处理缓慢',
    'system_error': '系统错误',
    'resource_limit': '资源限制'
  }
  return typeMap[type] || type
}

// 获取告警项样式类
const getAlertItemClass = (alert) => {
  return {
    'alert-item--critical': alert.severity === 'critical',
    'alert-item--high': alert.severity === 'high',
    'alert-item--medium': alert.severity === 'medium',
    'alert-item--low': alert.severity === 'low'
  }
}

// 格式化时间
const formatTime = (timeString) => {
  const date = new Date(timeString)
  const now = new Date()
  const diff = now - date
  
  if (diff < 60000) { // 1分钟内
    return '刚刚'
  } else if (diff < 3600000) { // 1小时内
    return `${Math.floor(diff / 60000)}分钟前`
  } else if (diff < 86400000) { // 24小时内
    return `${Math.floor(diff / 3600000)}小时前`
  } else {
    return date.toLocaleDateString()
  }
}

// 处理告警操作
const handleAlertAction = (alert, action) => {
  emit('alert-action', { alert, action })
}
</script>

<style scoped>
.alert-notification-panel {
  height: 100%;
}

.alert-item {
  transition: background-color 0.2s ease;
}

.alert-item:hover {
  background-color: rgba(var(--v-theme-primary), 0.04);
}

.alert-item--critical {
  border-left: 4px solid rgb(var(--v-theme-error));
  background-color: rgba(var(--v-theme-error), 0.02);
}

.alert-item--high {
  border-left: 4px solid rgb(var(--v-theme-error));
}

.alert-item--medium {
  border-left: 4px solid rgb(var(--v-theme-warning));
}

.alert-item--low {
  border-left: 4px solid rgb(var(--v-theme-info));
}

/* 动画效果 */
.alert-item {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 响应式设计 */
@media (max-width: 600px) {
  .alert-item .v-list-item-title {
    font-size: 0.875rem;
  }
  
  .alert-item .v-list-item-subtitle {
    font-size: 0.75rem;
  }
}
</style>
