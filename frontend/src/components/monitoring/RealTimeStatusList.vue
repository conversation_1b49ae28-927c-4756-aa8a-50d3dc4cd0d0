<template>
  <v-card class="real-time-status-list" elevation="2">
    <v-card-title class="d-flex align-center justify-space-between">
      <div class="d-flex align-center">
        <v-icon class="mr-2" color="primary">mdi-pulse</v-icon>
        <span>实时信号状态</span>
      </div>
      
      <div class="d-flex align-center">
        <!-- 刷新按钮 -->
        <v-btn
          icon
          size="small"
          variant="text"
          @click="$emit('refresh')"
          :loading="loading"
        >
          <v-icon>mdi-refresh</v-icon>
        </v-btn>
        
        <!-- 筛选按钮 -->
        <v-menu>
          <template #activator="{ props }">
            <v-btn
              icon
              size="small"
              variant="text"
              v-bind="props"
            >
              <v-icon>mdi-filter-variant</v-icon>
            </v-btn>
          </template>
          
          <v-list>
            <v-list-item
              v-for="filter in statusFilters"
              :key="filter.value"
              @click="selectedFilter = filter.value"
            >
              <template #prepend>
                <v-icon :color="filter.color">{{ filter.icon }}</v-icon>
              </template>
              <v-list-item-title>{{ filter.label }}</v-list-item-title>
              <template #append>
                <v-icon v-if="selectedFilter === filter.value">mdi-check</v-icon>
              </template>
            </v-list-item>
          </v-list>
        </v-menu>
      </div>
    </v-card-title>
    
    <v-divider />
    
    <!-- 加载状态 -->
    <div v-if="loading && signals.length === 0" class="pa-4">
      <v-skeleton-loader
        v-for="i in 5"
        :key="i"
        type="list-item-three-line"
        class="mb-2"
      />
    </div>
    
    <!-- 空状态 -->
    <div v-else-if="!loading && filteredSignals.length === 0" class="pa-8 text-center">
      <v-icon size="64" color="grey-lighten-1" class="mb-4">
        mdi-inbox-outline
      </v-icon>
      <div class="text-h6 text-medium-emphasis mb-2">暂无信号数据</div>
      <div class="text-body-2 text-disabled">
        {{ selectedFilter === 'all' ? '系统中还没有信号记录' : '当前筛选条件下没有信号' }}
      </div>
    </div>
    
    <!-- 信号列表 -->
    <v-list v-else class="pa-0">
      <template v-for="(signal, index) in filteredSignals" :key="signal.id">
        <v-list-item
          class="signal-item"
          :class="getSignalItemClass(signal)"
          @click="handleSignalClick(signal)"
        >
          <!-- 状态指示器 -->
          <template #prepend>
            <v-avatar
              size="40"
              :color="signal.status_color"
              variant="tonal"
            >
              <v-icon :color="signal.status_color">
                {{ getStatusIcon(signal.status) }}
              </v-icon>
            </v-avatar>
          </template>
          
          <!-- 信号内容 -->
          <v-list-item-title class="text-wrap">
            <div class="d-flex align-center justify-space-between">
              <span class="signal-content">{{ signal.content }}</span>
              <v-chip
                size="small"
                :color="signal.status_color"
                variant="tonal"
                class="ml-2"
              >
                {{ signal.status_text }}
              </v-chip>
            </div>
          </v-list-item-title>
          
          <v-list-item-subtitle class="mt-1">
            <div class="d-flex align-center justify-space-between">
              <div class="d-flex align-center">
                <!-- 平台标识 -->
                <v-chip
                  size="x-small"
                  variant="outlined"
                  class="mr-2"
                >
                  {{ signal.platform }}
                </v-chip>
                
                <!-- 置信度 -->
                <span v-if="signal.confidence" class="text-caption mr-2">
                  置信度: {{ (signal.confidence * 100).toFixed(0) }}%
                </span>
                
                <!-- 处理时间 -->
                <span v-if="signal.processing_duration" class="text-caption">
                  耗时: {{ formatDuration(signal.processing_duration) }}
                </span>
              </div>
              
              <!-- 创建时间 -->
              <span class="text-caption text-disabled">
                {{ formatTime(signal.created_at) }}
              </span>
            </div>
          </v-list-item-subtitle>
          
          <!-- 进度条（处理中状态） -->
          <div v-if="signal.status === 'processing'" class="mt-2">
            <v-progress-linear
              indeterminate
              :color="signal.status_color"
              height="2"
            />
          </div>
        </v-list-item>
        
        <v-divider v-if="index < filteredSignals.length - 1" />
      </template>
    </v-list>
    
    <!-- 查看更多按钮 -->
    <v-card-actions v-if="signals.length >= limit">
      <v-spacer />
      <v-btn
        variant="text"
        color="primary"
        @click="$emit('load-more')"
      >
        查看更多
        <v-icon end>mdi-chevron-down</v-icon>
      </v-btn>
    </v-card-actions>
  </v-card>
  
  <!-- 信号详情对话框 -->
  <v-dialog v-model="showDetailDialog" max-width="600">
    <v-card v-if="selectedSignal">
      <v-card-title class="d-flex align-center">
        <v-icon class="mr-2" :color="selectedSignal.status_color">
          {{ getStatusIcon(selectedSignal.status) }}
        </v-icon>
        信号详情
      </v-card-title>
      
      <v-card-text>
        <div class="mb-4">
          <div class="text-subtitle-2 mb-2">信号内容</div>
          <div class="text-body-2 pa-3 bg-grey-lighten-4 rounded">
            {{ selectedSignal.full_content || selectedSignal.content }}
          </div>
        </div>
        
        <v-row>
          <v-col cols="6">
            <div class="text-subtitle-2 mb-1">平台</div>
            <div class="text-body-2">{{ selectedSignal.platform }}</div>
          </v-col>
          <v-col cols="6">
            <div class="text-subtitle-2 mb-1">状态</div>
            <v-chip
              size="small"
              :color="selectedSignal.status_color"
              variant="tonal"
            >
              {{ selectedSignal.status_text }}
            </v-chip>
          </v-col>
        </v-row>
        
        <v-row v-if="selectedSignal.confidence">
          <v-col cols="6">
            <div class="text-subtitle-2 mb-1">置信度</div>
            <div class="text-body-2">{{ (selectedSignal.confidence * 100).toFixed(1) }}%</div>
          </v-col>
          <v-col cols="6" v-if="selectedSignal.processing_duration">
            <div class="text-subtitle-2 mb-1">处理时间</div>
            <div class="text-body-2">{{ formatDuration(selectedSignal.processing_duration) }}</div>
          </v-col>
        </v-row>
        
        <v-row>
          <v-col cols="6">
            <div class="text-subtitle-2 mb-1">创建时间</div>
            <div class="text-body-2">{{ formatFullTime(selectedSignal.created_at) }}</div>
          </v-col>
          <v-col cols="6" v-if="selectedSignal.updated_at">
            <div class="text-subtitle-2 mb-1">更新时间</div>
            <div class="text-body-2">{{ formatFullTime(selectedSignal.updated_at) }}</div>
          </v-col>
        </v-row>
      </v-card-text>
      
      <v-card-actions>
        <v-spacer />
        <v-btn @click="showDetailDialog = false">关闭</v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script setup>
import { ref, computed } from 'vue'
import { monitoringUtils } from '@/api/monitoring'

const props = defineProps({
  signals: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  },
  limit: {
    type: Number,
    default: 10
  }
})

const emit = defineEmits(['refresh', 'load-more', 'signal-click'])

// 状态筛选
const selectedFilter = ref('all')
const statusFilters = [
  { value: 'all', label: '全部', icon: 'mdi-all-inclusive', color: 'grey' },
  { value: 'processing', label: '处理中', icon: 'mdi-cog', color: 'warning' },
  { value: 'completed', label: '已完成', icon: 'mdi-check-circle', color: 'success' },
  { value: 'failed', label: '失败', icon: 'mdi-alert-circle', color: 'error' },
  { value: 'pending', label: '等待中', icon: 'mdi-clock-outline', color: 'info' }
]

// 信号详情对话框
const showDetailDialog = ref(false)
const selectedSignal = ref(null)

// 筛选后的信号列表
const filteredSignals = computed(() => {
  if (selectedFilter.value === 'all') {
    return props.signals
  }
  return props.signals.filter(signal => signal.status === selectedFilter.value)
})

// 获取状态图标
const getStatusIcon = (status) => {
  return monitoringUtils.getStatusIcon(status)
}

// 获取信号项样式类
const getSignalItemClass = (signal) => {
  return {
    'signal-item--processing': signal.status === 'processing',
    'signal-item--failed': signal.status === 'failed',
    'signal-item--completed': signal.status === 'completed'
  }
}

// 格式化时间
const formatTime = (timeString) => {
  const date = new Date(timeString)
  const now = new Date()
  const diff = now - date
  
  if (diff < 60000) { // 1分钟内
    return '刚刚'
  } else if (diff < 3600000) { // 1小时内
    return `${Math.floor(diff / 60000)}分钟前`
  } else if (diff < 86400000) { // 24小时内
    return `${Math.floor(diff / 3600000)}小时前`
  } else {
    return date.toLocaleDateString()
  }
}

// 格式化完整时间
const formatFullTime = (timeString) => {
  return new Date(timeString).toLocaleString()
}

// 格式化持续时间
const formatDuration = (seconds) => {
  return monitoringUtils.formatDuration(seconds)
}

// 处理信号点击
const handleSignalClick = (signal) => {
  selectedSignal.value = signal
  showDetailDialog.value = true
  emit('signal-click', signal)
}
</script>

<style scoped>
.real-time-status-list {
  height: 100%;
}

.signal-item {
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.signal-item:hover {
  background-color: rgba(var(--v-theme-primary), 0.04);
}

.signal-item--processing {
  border-left: 3px solid rgb(var(--v-theme-warning));
}

.signal-item--failed {
  border-left: 3px solid rgb(var(--v-theme-error));
}

.signal-item--completed {
  border-left: 3px solid rgb(var(--v-theme-success));
}

.signal-content {
  font-weight: 500;
  line-height: 1.4;
}

/* 响应式设计 */
@media (max-width: 600px) {
  .signal-content {
    font-size: 0.875rem;
  }
}
</style>
