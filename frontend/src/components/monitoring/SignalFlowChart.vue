<template>
  <v-card class="signal-flow-chart" elevation="2">
    <v-card-title class="d-flex align-center justify-space-between">
      <div class="d-flex align-center">
        <v-icon class="mr-2" color="primary">mdi-graph-outline</v-icon>
        <span>信号处理流程</span>
      </div>
      
      <div class="d-flex align-center">
        <!-- 图例按钮 -->
        <v-btn
          icon
          size="small"
          variant="text"
          @click="showLegend = !showLegend"
        >
          <v-icon>mdi-information-outline</v-icon>
          <v-tooltip activator="parent">显示图例</v-tooltip>
        </v-btn>
        
        <!-- 全屏按钮 -->
        <v-btn
          icon
          size="small"
          variant="text"
          @click="toggleFullscreen"
        >
          <v-icon>{{ isFullscreen ? 'mdi-fullscreen-exit' : 'mdi-fullscreen' }}</v-icon>
          <v-tooltip activator="parent">{{ isFullscreen ? '退出全屏' : '全屏显示' }}</v-tooltip>
        </v-btn>
      </div>
    </v-card-title>
    
    <v-divider />
    
    <!-- 图例 -->
    <v-expand-transition>
      <div v-if="showLegend" class="pa-4 bg-grey-lighten-5">
        <div class="text-subtitle-2 mb-2">状态说明</div>
        <div class="d-flex flex-wrap gap-3">
          <div
            v-for="status in statusLegend"
            :key="status.key"
            class="d-flex align-center"
          >
            <div
              class="legend-dot mr-2"
              :style="{ backgroundColor: status.color }"
            />
            <span class="text-caption">{{ status.label }}</span>
          </div>
        </div>
      </div>
    </v-expand-transition>
    
    <!-- 流程图容器 -->
    <div 
      ref="chartContainer" 
      class="chart-container"
      :class="{ 'chart-container--fullscreen': isFullscreen }"
    >
      <!-- SVG画布 -->
      <svg
        ref="chartSvg"
        :width="chartWidth"
        :height="chartHeight"
        class="flow-chart-svg"
        @click="handleCanvasClick"
      >
        <!-- 定义箭头标记 -->
        <defs>
          <marker
            id="arrowhead"
            markerWidth="10"
            markerHeight="7"
            refX="9"
            refY="3.5"
            orient="auto"
          >
            <polygon
              points="0 0, 10 3.5, 0 7"
              fill="#666"
            />
          </marker>
        </defs>
        
        <!-- 连接线 -->
        <g class="connections">
          <path
            v-for="connection in connections"
            :key="connection.id"
            :d="connection.path"
            class="connection-line"
            :class="getConnectionClass(connection)"
            marker-end="url(#arrowhead)"
          />
        </g>
        
        <!-- 流程节点 -->
        <g class="nodes">
          <g
            v-for="node in nodes"
            :key="node.id"
            :transform="`translate(${node.x}, ${node.y})`"
            class="flow-node"
            :class="getNodeClass(node)"
            @click="handleNodeClick(node, $event)"
            @mouseenter="showNodeTooltip(node, $event)"
            @mouseleave="hideNodeTooltip"
          >
            <!-- 节点背景圆圈 -->
            <circle
              :r="nodeRadius"
              :fill="getNodeColor(node.status)"
              :stroke="getNodeBorderColor(node.status)"
              stroke-width="3"
              class="node-background"
            />
            
            <!-- 节点图标 -->
            <text
              :font-size="iconSize"
              text-anchor="middle"
              dominant-baseline="central"
              :fill="getIconColor(node.status)"
              class="node-icon"
            >
              {{ getNodeIcon(node.type) }}
            </text>
            
            <!-- 节点标签 -->
            <text
              :y="nodeRadius + 25"
              text-anchor="middle"
              class="node-label"
              font-size="12"
              font-weight="500"
            >
              {{ node.label }}
            </text>
            
            <!-- 处理数量指示器 -->
            <g v-if="node.count > 0" class="count-indicator">
              <circle
                :cx="nodeRadius - 15"
                :cy="-nodeRadius + 15"
                r="12"
                fill="#ff5722"
                stroke="white"
                stroke-width="2"
              />
              <text
                :x="nodeRadius - 15"
                :y="-nodeRadius + 15"
                text-anchor="middle"
                dominant-baseline="central"
                fill="white"
                font-size="10"
                font-weight="bold"
              >
                {{ node.count > 99 ? '99+' : node.count }}
              </text>
            </g>
            
            <!-- 状态指示器（处理中的动画） -->
            <g v-if="node.status === 'processing'" class="processing-indicator">
              <circle
                :r="nodeRadius + 5"
                fill="none"
                :stroke="getNodeColor(node.status)"
                stroke-width="2"
                opacity="0.6"
                class="processing-ring"
              />
            </g>
          </g>
        </g>
      </svg>
      
      <!-- 加载覆盖层 -->
      <v-overlay
        v-if="loading"
        contained
        class="d-flex align-center justify-center"
      >
        <v-progress-circular
          indeterminate
          size="48"
          color="primary"
        />
      </v-overlay>
    </div>
    
    <!-- 节点工具提示 -->
    <v-tooltip
      v-model="showTooltip"
      :location="tooltipPosition"
      :text="tooltipText"
      max-width="300"
    />
  </v-card>
  
  <!-- 节点详情对话框 -->
  <v-dialog v-model="showNodeDialog" max-width="600">
    <v-card v-if="selectedNode">
      <v-card-title class="d-flex align-center">
        <div
          class="node-status-dot mr-3"
          :style="{ backgroundColor: getNodeColor(selectedNode.status) }"
        />
        {{ selectedNode.label }} - 详细信息
      </v-card-title>
      
      <v-card-text>
        <div v-if="nodeDetails">
          <div class="mb-4">
            <div class="text-subtitle-2 mb-2">节点描述</div>
            <div class="text-body-2">{{ nodeDetails.description || selectedNode.description }}</div>
          </div>
          
          <div class="mb-4">
            <div class="text-subtitle-2 mb-2">当前状态</div>
            <v-chip
              :color="getNodeColor(selectedNode.status)"
              variant="tonal"
              size="small"
            >
              {{ getStatusText(selectedNode.status) }}
            </v-chip>
          </div>
          
          <div v-if="nodeDetails.metrics" class="mb-4">
            <div class="text-subtitle-2 mb-2">统计指标</div>
            <v-row>
              <v-col
                v-for="(value, key) in nodeDetails.metrics"
                :key="key"
                cols="6"
              >
                <div class="text-caption text-medium-emphasis">{{ formatMetricKey(key) }}</div>
                <div class="text-body-1 font-weight-medium">{{ formatMetricValue(key, value) }}</div>
              </v-col>
            </v-row>
          </div>
        </div>
        
        <div v-else-if="loadingNodeDetails">
          <v-skeleton-loader type="paragraph" />
        </div>
        
        <div v-else>
          <div class="text-body-2 text-medium-emphasis">暂无详细信息</div>
        </div>
      </v-card-text>
      
      <v-card-actions>
        <v-spacer />
        <v-btn @click="showNodeDialog = false">关闭</v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { useMonitoringStore } from '@/stores/monitoring'

const props = defineProps({
  nodes: {
    type: Array,
    default: () => []
  },
  connections: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['node-click'])

const monitoringStore = useMonitoringStore()

// 组件状态
const chartContainer = ref(null)
const chartSvg = ref(null)
const showLegend = ref(false)
const isFullscreen = ref(false)

// 图表尺寸
const chartWidth = ref(800)
const chartHeight = ref(400)
const nodeRadius = 35
const iconSize = 20

// 工具提示
const showTooltip = ref(false)
const tooltipPosition = ref('top')
const tooltipText = ref('')

// 节点详情
const showNodeDialog = ref(false)
const selectedNode = ref(null)
const nodeDetails = ref(null)
const loadingNodeDetails = ref(false)

// 状态图例
const statusLegend = [
  { key: 'pending', label: '等待中', color: '#9E9E9E' },
  { key: 'processing', label: '处理中', color: '#FF9800' },
  { key: 'success', label: '成功', color: '#4CAF50' },
  { key: 'failed', label: '失败', color: '#F44336' },
  { key: 'completed', label: '已完成', color: '#2196F3' }
]

// 节点颜色映射
const nodeColors = {
  pending: '#9E9E9E',
  processing: '#FF9800',
  success: '#4CAF50',
  failed: '#F44336',
  completed: '#2196F3',
  idle: '#E0E0E0'
}

// 获取节点颜色
const getNodeColor = (status) => nodeColors[status] || nodeColors.pending

// 获取节点边框颜色
const getNodeBorderColor = (status) => {
  if (status === 'processing') return '#F57C00'
  return getNodeColor(status)
}

// 获取图标颜色
const getIconColor = (status) => {
  return status === 'pending' || status === 'idle' ? '#666' : 'white'
}

// 获取节点图标
const getNodeIcon = (type) => {
  const icons = {
    create: '📝',
    parse: '🔍',
    context: '📊',
    plan: '📋',
    risk: '⚠️',
    execute: '⚡',
    confirm: '✅'
  }
  return icons[type] || '⭕'
}

// 获取节点样式类
const getNodeClass = (node) => ({
  'flow-node--clickable': true,
  'flow-node--processing': node.status === 'processing',
  'flow-node--failed': node.status === 'failed',
  'flow-node--success': node.status === 'success'
})

// 获取连接线样式类
const getConnectionClass = (connection) => ({
  'connection-line--active': connection.status === 'active',
  'connection-line--inactive': connection.status === 'inactive'
})

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    pending: '等待中',
    processing: '处理中',
    success: '成功',
    failed: '失败',
    completed: '已完成',
    idle: '空闲'
  }
  return statusMap[status] || status
}

// 格式化指标键名
const formatMetricKey = (key) => {
  const keyMap = {
    total_created: '总创建数',
    recent_created: '最近创建',
    creation_rate: '创建速率',
    pending_count: '等待数量',
    success_count: '成功数量',
    failed_count: '失败数量',
    success_rate: '成功率',
    avg_parse_time: '平均解析时间'
  }
  return keyMap[key] || key
}

// 格式化指标值
const formatMetricValue = (key, value) => {
  if (key.includes('rate') && typeof value === 'number') {
    return `${value}%`
  }
  if (key.includes('time') && typeof value === 'number') {
    return `${value}秒`
  }
  return value.toString()
}

// 处理节点点击
const handleNodeClick = async (node, event) => {
  event.stopPropagation()
  selectedNode.value = node
  showNodeDialog.value = true
  
  // 加载节点详情
  loadingNodeDetails.value = true
  nodeDetails.value = null
  
  try {
    nodeDetails.value = await monitoringStore.getNodeDetails(node.id)
  } catch (error) {
    console.error('Failed to load node details:', error)
  } finally {
    loadingNodeDetails.value = false
  }
  
  emit('node-click', node)
}

// 处理画布点击
const handleCanvasClick = () => {
  hideNodeTooltip()
}

// 显示节点工具提示
const showNodeTooltip = (node, event) => {
  tooltipText.value = `${node.label}\n状态: ${getStatusText(node.status)}\n数量: ${node.count}`
  showTooltip.value = true
}

// 隐藏节点工具提示
const hideNodeTooltip = () => {
  showTooltip.value = false
}

// 切换全屏
const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value
  nextTick(() => {
    updateChartSize()
  })
}

// 更新图表尺寸
const updateChartSize = () => {
  if (chartContainer.value) {
    const rect = chartContainer.value.getBoundingClientRect()
    chartWidth.value = rect.width
    chartHeight.value = isFullscreen.value ? window.innerHeight - 100 : 400
  }
}

// 窗口大小变化处理
const handleResize = () => {
  updateChartSize()
}

onMounted(() => {
  updateChartSize()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
.signal-flow-chart {
  width: 100%;
}

.chart-container {
  position: relative;
  width: 100%;
  height: 400px;
  overflow: hidden;
}

.chart-container--fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 1000;
  background: white;
}

.flow-chart-svg {
  width: 100%;
  height: 100%;
  background: #fafafa;
  border-radius: 8px;
}

.flow-node {
  cursor: pointer;
  transition: all 0.3s ease;
}

.flow-node:hover {
  transform: scale(1.05);
}

.flow-node--processing .node-background {
  filter: drop-shadow(0 0 8px rgba(255, 152, 0, 0.6));
}

.flow-node--failed .node-background {
  filter: drop-shadow(0 0 8px rgba(244, 67, 54, 0.6));
}

.flow-node--success .node-background {
  filter: drop-shadow(0 0 8px rgba(76, 175, 80, 0.6));
}

.connection-line {
  stroke: #bdbdbd;
  stroke-width: 2;
  fill: none;
  transition: all 0.3s ease;
}

.connection-line--active {
  stroke: #4CAF50;
  stroke-width: 3;
}

.connection-line--inactive {
  stroke: #e0e0e0;
  stroke-width: 1;
}

.node-label {
  fill: #424242;
  font-family: 'Roboto', sans-serif;
}

.node-icon {
  font-family: 'Apple Color Emoji', 'Segoe UI Emoji', sans-serif;
}

.processing-ring {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    opacity: 0.6;
    transform: scale(1);
  }
  50% {
    opacity: 0.3;
    transform: scale(1.1);
  }
  100% {
    opacity: 0.6;
    transform: scale(1);
  }
}

.legend-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.node-status-dot {
  width: 16px;
  height: 16px;
  border-radius: 50%;
}

.count-indicator {
  animation: bounce 1s infinite;
}

@keyframes bounce {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chart-container {
    height: 300px;
  }
  
  .flow-chart-svg {
    font-size: 10px;
  }
}
</style>
