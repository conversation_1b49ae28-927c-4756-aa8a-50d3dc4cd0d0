<template>
  <v-app>
    <ErrorBoundary>
      <!-- 应用栏 -->
      <v-app-bar app elevation="2" color="primary">
        <v-app-bar-title class="text-h6 font-weight-bold">
          <v-icon class="mr-2">mdi-robot</v-icon>
          Crypto Trader - 测试HMR
        </v-app-bar-title>
        
        <v-spacer></v-spacer>
      
      <!-- 导航按钮 -->
      <template v-if="authStore.isAuthenticated">
        <v-btn
          :to="{ name: 'dashboard' }"
          variant="text"
          prepend-icon="mdi-view-dashboard"
          :class="{ 'v-btn--active': $route.name === 'dashboard' }"
          data-testid="nav-dashboard"
        >
          仪表盘
        </v-btn>

        <v-btn
          :to="{ name: 'orders' }"
          variant="text"
          prepend-icon="mdi-format-list-bulleted"
          :class="{ 'v-btn--active': $route.name === 'orders' }"
          data-testid="nav-orders"
        >
          订单
        </v-btn>

        <v-btn
          :to="{ name: 'conditional-orders' }"
          variant="text"
          prepend-icon="mdi-clock-time-eight"
          :class="{ 'v-btn--active': $route.name === 'conditional-orders' }"
          data-testid="nav-conditional-orders"
        >
          条件订单
        </v-btn>

        <v-btn
          :to="{ name: 'signals' }"
          variant="text"
          prepend-icon="mdi-signal"
          :class="{ 'v-btn--active': $route.name === 'signals' }"
          data-testid="nav-signals"
        >
          信号
        </v-btn>

        <v-btn
          :to="{ name: 'monitoring' }"
          variant="text"
          prepend-icon="mdi-monitor-dashboard"
          :class="{ 'v-btn--active': $route.name === 'monitoring' }"
          data-testid="nav-monitoring"
        >
          监控
        </v-btn>

        <v-btn
          :to="{ name: 'configs' }"
          variant="text"
          prepend-icon="mdi-cog"
          :class="{ 'v-btn--active': $route.name === 'configs' }"
          data-testid="nav-configs"
        >
          配置
        </v-btn>
        
        <!-- 连接状态指示器 -->
        <v-chip
          :color="websocketStore.isConnected ? 'success' : 'error'"
          size="small"
          variant="flat"
          class="ml-4"
          data-testid="ws-status"
          :aria-label="`WebSocket连接状态: ${websocketStore.isConnected ? '已连接' : '未连接'}`"
        >
          <v-icon size="16" class="mr-1" aria-hidden="true">
            {{ websocketStore.isConnected ? 'mdi-wifi' : 'mdi-wifi-off' }}
          </v-icon>
          {{ websocketStore.isConnected ? '已连接' : '未连接' }}
        </v-chip>
        
        <!-- 用户菜单 -->
        <v-menu data-testid="user-menu">
          <template v-slot:activator="{ props }">
            <v-btn
              icon
              v-bind="props"
              class="ml-2"
              data-testid="user-menu-button"
            >
              <v-avatar size="32">
                <v-icon>mdi-account</v-icon>
              </v-avatar>
            </v-btn>
          </template>
          
          <v-list>
            <v-list-item v-if="authStore.user">
              <v-list-item-title>{{ authStore.user.username || '用户' }}</v-list-item-title>
              <v-list-item-subtitle>{{ authStore.user.email || '' }}</v-list-item-subtitle>
            </v-list-item>
            <v-list-item v-else>
              <v-list-item-title>用户</v-list-item-title>
              <v-list-item-subtitle>未登录</v-list-item-subtitle>
            </v-list-item>
            
            <v-divider></v-divider>
            
            <v-list-item @click="toggleTheme">
              <template v-slot:prepend>
                <v-icon>{{ uiStore.theme === 'dark' ? 'mdi-weather-sunny' : 'mdi-weather-night' }}</v-icon>
              </template>
              <v-list-item-title>
                {{ uiStore.theme === 'dark' ? '浅色主题' : '深色主题' }}
              </v-list-item-title>
            </v-list-item>
            
            <v-list-item @click="logout" data-testid="logout-button">
              <template v-slot:prepend>
                <v-icon>mdi-logout</v-icon>
              </template>
              <v-list-item-title>退出登录</v-list-item-title>
            </v-list-item>
          </v-list>
        </v-menu>
      </template>
    </v-app-bar>

    <!-- 主内容区域 -->
    <v-main>
      <v-container fluid>
        <router-view />
      </v-container>
    </v-main>

    <!-- 全局加载遮罩 -->
    <v-overlay 
      v-model="uiStore.isLoading" 
      class="align-center justify-center"
      persistent
    >
      <v-progress-circular
        color="primary"
        indeterminate
        size="64"
      ></v-progress-circular>
      <div class="text-h6 mt-4">{{ uiStore.loadingMessage || '加载中...' }}</div>
    </v-overlay>

    <!-- 通知系统 -->
    <div class="notification-container">
      <v-snackbar
        v-for="notification in uiStore.notifications"
        :key="notification.id"
        v-model="notification.show"
        :color="notification.color || notification.type"
        :timeout="notification.autoClose !== false ? 5000 : -1"
        location="top right"
        :style="{ 'z-index': 9999 + notification.id }"
        @update:model-value="(val) => !val && uiStore.removeNotification(notification.id)"
      >
        <div class="d-flex align-center">
          <v-icon class="mr-2">
            {{ getNotificationIcon(notification.type) }}
          </v-icon>
          <div>
            <div v-if="notification.title" class="font-weight-bold">
              {{ notification.title }}
            </div>
            <div>{{ notification.message }}</div>
          </div>
        </div>
        
        <template v-slot:actions>
          <v-btn
            variant="text"
            @click="uiStore.removeNotification(notification.id)"
          >
            关闭
          </v-btn>
        </template>
      </v-snackbar>
    </div>

    <!-- 引导向导 -->
    <OnboardingWizard />
    </ErrorBoundary>
  </v-app>
</template>

<script setup>
import { computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useTheme } from 'vuetify'
import { useAuthStore } from '@/stores/auth'
import { useUIStore } from '@/stores/ui'
import { useWebSocketStore } from '@/stores/websocket'
import OnboardingWizard from '@/components/OnboardingWizard.vue'
import ErrorBoundary from '@/components/ErrorBoundary.vue'

const router = useRouter()
const theme = useTheme()
const authStore = useAuthStore()
const uiStore = useUIStore()
const websocketStore = useWebSocketStore()

// 方法
function toggleTheme() {
  const newTheme = uiStore.theme === 'dark' ? 'light' : 'dark'
  uiStore.setTheme(newTheme)
  theme.global.name.value = newTheme
}

function logout() {
  authStore.logout()
  websocketStore.disconnect()
  router.push('/login')
}

function getNotificationIcon(type) {
  const iconMap = {
    success: 'mdi-check-circle',
    error: 'mdi-alert-circle',
    warning: 'mdi-alert',
    info: 'mdi-information'
  }
  return iconMap[type] || 'mdi-information'
}

// 生命周期
onMounted(() => {
  // 设置主题
  theme.global.name.value = uiStore.theme

  // 如果用户已登录，连接WebSocket
  if (authStore.isAuthenticated && authStore.token) {
    websocketStore.connect()
  }
})
</script>

<style>
/* 全局样式 */
.v-btn--active {
  background-color: rgba(255, 255, 255, 0.1) !important;
}

.notification-container {
  position: fixed;
  top: 80px;
  right: 16px;
  z-index: 9999;
  pointer-events: none;
}

.notification-container .v-snackbar {
  pointer-events: auto;
  position: relative !important;
  margin-bottom: 8px;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(var(--v-border-color), 0.1);
}

::-webkit-scrollbar-thumb {
  background: rgba(var(--v-border-color), 0.3);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(var(--v-border-color), 0.5);
}

/* 响应式设计 */
@media (max-width: 960px) {
  .v-app-bar-title {
    font-size: 1rem !important;
  }
  
  .v-btn {
    min-width: auto !important;
    padding: 0 8px !important;
  }
}
</style>