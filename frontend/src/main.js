import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import pinia from './stores'

// 全局样式
import './assets/styles/index.css'
import './styles/accessibility.css'

// Vuetify
import 'vuetify/styles'
import { createVuetify } from 'vuetify'
import * as components from 'vuetify/components'
import * as directives from 'vuetify/directives'

const vuetify = createVuetify({
  components,
  directives,
  theme: {
    defaultTheme: 'dark',
    themes: {
      dark: {
        dark: true,
        colors: {
          // 主要背景和表面
          background: '#0a0a0a',        // 更深的背景色，提高对比度
          surface: '#1a1a1a',          // 主要表面色
          'surface-bright': '#262626',  // 明亮表面色
          'surface-light': '#212121',   // 浅色表面
          'surface-variant': '#2d2d2d', // 表面变体

          // 主色调 - 针对深色主题优化
          primary: '#2196f3',           // 更适合深色背景的蓝色
          'primary-darken-1': '#1976d2',
          'primary-lighten-1': '#42a5f5',
          'primary-lighten-2': '#64b5f6',

          // 辅助色
          secondary: '#424242',
          'secondary-darken-1': '#1f1f1f',
          'secondary-lighten-1': '#616161',

          // 强调色
          accent: '#82b1ff',

          // 状态色 - 深色主题优化
          success: '#4caf50',
          'success-darken-1': '#388e3c',
          'success-lighten-1': '#66bb6a',

          warning: '#ff9800',
          'warning-darken-1': '#f57c00',
          'warning-lighten-1': '#ffb74d',

          error: '#f44336',
          'error-darken-1': '#d32f2f',
          'error-lighten-1': '#ef5350',

          info: '#2196f3',
          'info-darken-1': '#1976d2',
          'info-lighten-1': '#42a5f5',

          // 文本颜色 - 提高对比度
          'on-background': '#ffffff',
          'on-surface': '#ffffff',
          'on-surface-variant': '#e0e0e0',
          'on-primary': '#ffffff',
          'on-secondary': '#ffffff',
          'on-success': '#ffffff',
          'on-warning': '#000000',
          'on-error': '#ffffff',
          'on-info': '#ffffff',

          // 边框和分割线 - 增强可见性
          outline: 'rgba(255, 255, 255, 0.2)',
          'outline-variant': 'rgba(255, 255, 255, 0.12)',

          // 交易专用色彩
          profit: '#00e676',            // 盈利绿 - 更鲜艳
          loss: '#ff5252',              // 亏损红 - 更鲜艳
          buy: '#4caf50',               // 买入绿
          sell: '#f44336',              // 卖出红
        }
      },
      light: {
        dark: false,
        colors: {
          // 保持原有浅色主题配置
          background: '#ffffff',
          surface: '#ffffff',
          'surface-variant': '#f5f5f5',
          primary: '#1976d2',
          secondary: '#424242',
          accent: '#82b1ff',
          success: '#4caf50',
          warning: '#ff9800',
          error: '#f44336',
          info: '#2196f3',
        }
      }
    }
  }
})

const app = createApp(App)

app.use(pinia)
app.use(router)
app.use(vuetify)

app.mount('#app')
