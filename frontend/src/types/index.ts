/**
 * 基础类型定义
 */

// 用户相关类型
export interface User {
  id: number
  username: string
  email: string
  is_first_time: boolean
  email_verified?: boolean
  two_factor_enabled?: boolean
  permissions?: string[]
  roles?: string[]
}

export interface LoginCredentials {
  username: string
  password: string
}

export interface RegisterData {
  username: string
  email: string
  password: string
}

// API响应类型
export interface ApiResponse<T = any> {
  data: T
  message?: string
  status: number
}

export interface ApiError {
  message: string
  status: number
  data?: any
}

// 订单相关类型 (金融数值使用string确保精度)
export interface Order {
  id: string
  symbol: string
  side: 'buy' | 'sell'
  status: 'pending' | 'filled' | 'cancelled' | 'failed' | 'partially_filled'
  quantity: string  // 使用string接收Decimal数据
  price: string     // 使用string接收Decimal数据
  filled_quantity?: string
  pnl?: string      // 使用string接收Decimal数据
  pnl_usd?: string  // 使用string接收Decimal数据
  created_at: string
  updated_at?: string
  filled_at?: string
}

export interface ConditionalOrder {
  id: string
  symbol: string
  side: 'buy' | 'sell'
  condition_type: 'price_above' | 'price_below' | 'rsi_above' | 'rsi_below'
  condition_value: string  // 使用string接收Decimal数据
  quantity: string         // 使用string接收Decimal数据
  price?: string          // 使用string接收Decimal数据
  status: 'active' | 'triggered' | 'cancelled'
  created_at: string
}

// WebSocket消息类型
export interface WebSocketMessage {
  event_type?: 'ORDER_UPDATE' | 'AGENT_STATE_TRANSITION' | 'NOTIFICATION' | 'HEARTBEAT' | 'CONNECTION_ESTABLISHED' | 'ERROR' | 'PING' | 'PONG'
  type?: 'error' | 'ping' | 'pong' | string
  message?: string
  payload?: any
  timestamp?: string
}

export interface OrderUpdatePayload {
  id: string
  symbol: string
  side: 'buy' | 'sell'
  status: 'pending' | 'filled' | 'cancelled' | 'failed'
  quantity: string  // 使用string接收Decimal数据
  price: string     // 使用string接收Decimal数据
}

// 订单过滤器类型
export interface OrderFilters {
  status: string
  symbol: string
  side: string
  dateRange: [string, string] | null
}

// 订单统计类型
export interface OrderStats {
  total: number
  pending: number
  filled: number
  cancelled: number
  failed: number           // 与后端保持一致
  partially_filled: number
  totalVolume: string      // 使用string接收Decimal数据
  totalPnL: string         // 使用string接收Decimal数据
}

// 订单状态映射类型 (与后端保持一致)
export type OrderStatus = 'pending' | 'filled' | 'cancelled' | 'failed' | 'partially_filled' | 'expired'
export type OrderSide = 'buy' | 'sell'
export type OrderType = 'market' | 'limit' | 'stop' | 'stop_limit'

// Agent相关类型
export interface AgentState {
  status: 'idle' | 'analyzing' | 'trading' | 'error'
  current_action?: string
  last_update: string
}

export interface AgentConfig {
  risk_level: 'low' | 'medium' | 'high'
  max_position_size: number
  stop_loss_percentage: number
  take_profit_percentage: number
  trading_pairs: string[]
}

// Agent任务类型
export interface AgentTask {
  id: string
  type: string
  status: 'pending' | 'processing' | 'completed' | 'failed'
  created_at: string
  updated_at?: string
  details?: any
}

// Agent待处理动作类型
export interface PendingAction {
  id: string
  details: any
  expiresAt: string
  status: 'pending' | 'responded' | 'rejected'
}

// Agent通知类型
export interface AgentNotification {
  id: number
  type: 'success' | 'error' | 'warning' | 'info'
  title: string
  message: string
  timestamp: string
  read?: boolean
}

// Agent日志类型
export interface AgentLog {
  id: number
  level: 'info' | 'warn' | 'error' | 'debug'
  message: string
  timestamp: string
  details?: any
}

// 交易信号类型
export interface TradingSignal {
  symbol: string
  action: 'buy' | 'sell'
  quantity: number
  price?: number
  signal_type: 'market' | 'limit'
  confidence: number
  reason?: string
}

// 信号功能模块类型定义
export type PlatformType = 'discord' | 'telegram' | 'manual'
export type MessageType = 'text' | 'embed' | 'attachment' | 'reply'

export interface Signal {
  id: string
  user_id: string
  platform: PlatformType
  platform_message_id?: string
  channel_id?: string
  channel_name?: string
  author_id?: string
  author_name?: string
  content: string
  raw_content?: string
  message_type: MessageType
  metadata?: Record<string, any>
  signal_strength?: number
  is_processed: boolean
  processed_at?: string
  created_at: string
  updated_at: string
}

export interface SignalResponse {
  id: string
  platform: string
  channel_name?: string
  author_name?: string
  content: string
  message_type: string
  signal_strength?: number
  is_processed: boolean
  created_at: string
}

export interface SignalDetailResponse extends SignalResponse {
  raw_content?: string
  metadata?: Record<string, any>
  processed_at?: string
  platform_message_id?: string
  channel_id?: string
  author_id?: string
}

export interface CreateSignalRequest {
  platform: PlatformType
  content: string
  channel_name?: string
  author_name?: string
  raw_content?: string
  metadata?: Record<string, any>
}

export interface UpdateSignalRequest {
  is_processed?: boolean
  signal_strength?: number
  metadata?: Record<string, any>
}

export interface SignalQueryParams {
  platform?: string
  channel_id?: string
  is_processed?: boolean
  signal_strength_min?: number
  date_from?: string
  date_to?: string
  page?: number
  size?: number
  sort_by?: string
  sort_order?: 'asc' | 'desc'
}

export interface SignalStatsResponse {
  total_signals: number
  processed_signals: number
  platform_breakdown: Record<string, number>
  avg_signal_strength?: number
  recent_activity: Array<Record<string, any>>
}

// 配置相关类型
export interface ExchangeConfig {
  id: string
  name: string
  enabled: boolean
  api_key: string
  api_secret: string
  sandbox: boolean
  trading_pairs: string[]
  created_at: string
  updated_at?: string
}

export interface RiskConfig {
  id?: string
  max_position_size?: string        // 使用string接收Decimal数据
  max_position_size_usd?: string    // 使用string接收Decimal数据
  stop_loss_percentage?: string     // 使用string接收Decimal数据
  take_profit_percentage?: string   // 使用string接收Decimal数据
  max_daily_loss?: string          // 使用string接收Decimal数据
  max_daily_loss_usd?: string      // 使用string接收Decimal数据
  max_open_positions?: number      // 整数保持number类型
  default_stop_loss_pct?: string   // 使用string接收Decimal数据
  enable_stop_loss?: boolean
  enable_take_profit?: boolean
  risk_level?: 'low' | 'medium' | 'high'
  created_at?: string
  updated_at?: string
}

export interface SystemConfig {
  auto_trading_enabled: boolean
  max_concurrent_trades: number        // 整数保持number类型
  default_trade_amount: string         // 使用string接收Decimal数据
  order_timeout_seconds?: number       // 整数保持number类型
  log_level?: string
  notification_settings: {
    email: boolean
    sms: boolean
    push: boolean
  }
}

export interface SignalConfig {
  id: string
  name: string
  enabled: boolean
  parameters: Record<string, any>
  created_at: string
  updated_at?: string
}

export interface AppConfig {
  exchanges: ExchangeConfig[]
  risk: RiskConfig
  signals: SignalConfig[]
  system: SystemConfig
}

// UI相关类型
export interface NotificationOptions {
  type: 'success' | 'error' | 'warning' | 'info'
  title?: string
  message: string
  duration?: number
  color?: string
  autoClose?: boolean
  actions?: Array<{
    text: string
    handler: () => void
  }>
}

export interface ConfirmDialog {
  show: boolean
  title: string
  message: string
  confirmText: string
  cancelText: string
  onConfirm: (() => void) | null
  onCancel: (() => void) | null
}

// 表单验证类型
export interface ValidationRule {
  required?: boolean
  min?: number
  max?: number
  pattern?: RegExp
  message: string
}

export interface FormField {
  value: any
  rules: ValidationRule[]
  error?: string
}
