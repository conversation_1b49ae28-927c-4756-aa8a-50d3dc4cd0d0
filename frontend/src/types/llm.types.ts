/**
 * LLM配置相关的TypeScript类型定义
 * 
 * 与后端API保持一致的类型定义，用于前端组件和API调用
 */

/**
 * LLM服务提供商枚举
 */
export enum LLMProvider {
  DEEPSEEK = 'deepseek',
  GEMINI = 'gemini',
  CHATGPT = 'chatgpt',
  CLAUDE = 'claude'
}

/**
 * LLM提供商显示信息
 */
export interface LLMProviderInfo {
  name: string
  displayName: string
  icon: string
  color: string
  defaultModels: string[]
  defaultModel: string
  defaultApiBaseUrl?: string
  maxTokens: number
  description: string
}

/**
 * LLM提供商配置映射
 */
export const LLM_PROVIDER_CONFIG: Record<LLMProvider, LLMProviderInfo> = {
  [LLMProvider.DEEPSEEK]: {
    name: 'deepseek',
    displayName: 'DeepSeek',
    icon: 'mdi-brain',
    color: 'blue',
    defaultModels: ['deepseek-chat', 'deepseek-coder'],
    defaultModel: 'deepseek-chat',
    defaultApiBaseUrl: 'https://api.deepseek.com/v1',
    maxTokens: 4096,
    description: '国产大模型服务，专注于代码和对话'
  },
  [LLMProvider.GEMINI]: {
    name: 'gemini',
    displayName: 'Gemini',
    icon: 'mdi-google',
    color: 'green',
    defaultModels: ['gemini-pro', 'gemini-pro-vision'],
    defaultModel: 'gemini-pro',
    defaultApiBaseUrl: 'https://generativelanguage.googleapis.com/v1',
    maxTokens: 8192,
    description: 'Google的多模态AI模型'
  },
  [LLMProvider.CHATGPT]: {
    name: 'chatgpt',
    displayName: 'ChatGPT',
    icon: 'mdi-robot',
    color: 'teal',
    defaultModels: ['gpt-4', 'gpt-4-turbo', 'gpt-3.5-turbo'],
    defaultModel: 'gpt-4',
    defaultApiBaseUrl: 'https://api.openai.com/v1',
    maxTokens: 4096,
    description: 'OpenAI的GPT系列模型'
  },
  [LLMProvider.CLAUDE]: {
    name: 'claude',
    displayName: 'Claude',
    icon: 'mdi-account-tie',
    color: 'purple',
    defaultModels: ['claude-3-opus', 'claude-3-sonnet', 'claude-3-haiku'],
    defaultModel: 'claude-3-sonnet',
    defaultApiBaseUrl: 'https://api.anthropic.com/v1',
    maxTokens: 4096,
    description: 'Anthropic的Claude系列模型'
  }
}

/**
 * LLM配置请求模型
 */
export interface LLMConfigRequest {
  config_name: string
  provider: LLMProvider
  enabled: boolean
  is_default: boolean
  api_key?: string
  api_base_url?: string
  model_name: string
  max_tokens: number
  temperature: number
  timeout_seconds: number
  max_retries: number
}

/**
 * LLM配置响应模型
 */
export interface LLMConfig {
  id: string
  config_name: string
  provider: string
  enabled: boolean
  is_default: boolean
  api_base_url?: string
  model_name: string
  max_tokens: number
  temperature: number
  timeout_seconds: number
  max_retries: number
  created_at: string
  updated_at: string
  api_key_masked: string
}

/**
 * LLM配置测试请求模型
 * 使用统一的默认测试消息，不再支持自定义消息
 */
export interface LLMConfigTestRequest {
  // 空接口，使用统一的默认测试消息
}

/**
 * LLM配置测试响应模型
 */
export interface LLMConfigTestResponse {
  success: boolean
  response_text?: string
  error_message?: string
  response_time_ms?: number
}

/**
 * LLM配置表单数据
 */
export interface LLMConfigFormData {
  config_name: string
  provider: LLMProvider | ''
  enabled: boolean
  is_default: boolean
  api_key: string
  api_base_url: string
  model_name: string
  max_tokens: number
  temperature: number
  timeout_seconds: number
  max_retries: number
}

/**
 * LLM配置验证规则
 */
export interface LLMConfigValidationRules {
  config_name: Array<(v: string) => boolean | string>
  provider: Array<(v: string) => boolean | string>
  api_key: Array<(v: string) => boolean | string>
  model_name: Array<(v: string) => boolean | string>
  max_tokens: Array<(v: number) => boolean | string>
  temperature: Array<(v: number) => boolean | string>
  timeout_seconds: Array<(v: number) => boolean | string>
  max_retries: Array<(v: number) => boolean | string>
}

/**
 * 创建默认的LLM配置表单数据
 */
export function createDefaultLLMConfigFormData(): LLMConfigFormData {
  return {
    config_name: '',
    provider: '',
    enabled: false,
    is_default: false,
    api_key: '',
    api_base_url: '',
    model_name: '',
    max_tokens: 4096,
    temperature: 0.7,
    timeout_seconds: 60,
    max_retries: 3
  }
}

/**
 * 从LLM配置创建表单数据
 */
export function createLLMConfigFormDataFromConfig(config: LLMConfig): LLMConfigFormData {
  return {
    config_name: config.config_name,
    provider: config.provider as LLMProvider,
    enabled: config.enabled,
    is_default: config.is_default,
    api_key: '', // 不预填充API密钥，用户需要重新输入
    api_base_url: config.api_base_url || '',
    model_name: config.model_name,
    max_tokens: config.max_tokens,
    temperature: config.temperature,
    timeout_seconds: config.timeout_seconds,
    max_retries: config.max_retries
  }
}

/**
 * 获取LLM提供商信息
 */
export function getLLMProviderInfo(provider: LLMProvider): LLMProviderInfo {
  return LLM_PROVIDER_CONFIG[provider]
}

/**
 * 获取所有LLM提供商选项
 */
export function getLLMProviderOptions() {
  return Object.values(LLMProvider).map(provider => ({
    value: provider,
    title: LLM_PROVIDER_CONFIG[provider].displayName,
    subtitle: LLM_PROVIDER_CONFIG[provider].description,
    icon: LLM_PROVIDER_CONFIG[provider].icon,
    color: LLM_PROVIDER_CONFIG[provider].color
  }))
}

/**
 * 验证LLM配置表单数据
 */
export function validateLLMConfigFormData(data: LLMConfigFormData, isEditing: boolean = false): string[] {
  const errors: string[] = []

  if (!data.config_name.trim()) {
    errors.push('配置名称不能为空')
  }

  if (!data.provider) {
    errors.push('请选择LLM服务提供商')
  }

  // 创建时API密钥是必需的，编辑时允许为空（表示不更改）
  if (!isEditing && (!data.api_key || !data.api_key.trim())) {
    errors.push('API密钥不能为空')
  }

  if (!data.model_name.trim()) {
    errors.push('模型名称不能为空')
  }

  if (data.max_tokens < 1 || data.max_tokens > 32768) {
    errors.push('最大token数必须在1-32768之间')
  }

  if (data.temperature < 0 || data.temperature > 2) {
    errors.push('温度参数必须在0-2之间')
  }

  if (data.timeout_seconds < 1 || data.timeout_seconds > 300) {
    errors.push('超时时间必须在1-300秒之间')
  }

  if (data.max_retries < 0 || data.max_retries > 10) {
    errors.push('最大重试次数必须在0-10之间')
  }

  return errors
}

/**
 * 格式化API密钥显示
 */
export function formatApiKeyMask(apiKey: string): string {
  if (!apiKey || apiKey.length <= 8) {
    return '***'
  }
  return `${apiKey.slice(0, 4)}***${apiKey.slice(-4)}`
}

/**
 * 获取LLM提供商颜色
 */
export function getLLMProviderColor(provider: string): string {
  const providerInfo = LLM_PROVIDER_CONFIG[provider as LLMProvider]
  return providerInfo?.color || 'grey'
}

/**
 * 获取LLM提供商图标
 */
export function getLLMProviderIcon(provider: string): string {
  const providerInfo = LLM_PROVIDER_CONFIG[provider as LLMProvider]
  return providerInfo?.icon || 'mdi-robot'
}

/**
 * 获取LLM提供商显示名称
 */
export function getLLMProviderDisplayName(provider: string): string {
  const providerInfo = LLM_PROVIDER_CONFIG[provider as LLMProvider]
  return providerInfo?.displayName || provider
}
