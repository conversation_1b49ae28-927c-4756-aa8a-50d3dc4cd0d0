/**
 * 工具函数 Composable
 * 提供常用的工具函数和格式化方法
 */

import { ref, computed, watch, onMounted, onUnmounted } from 'vue'

/**
 * 防抖 Hook
 */
export function useDebounce(value, delay = 300) {
  const debouncedValue = ref(value.value || value)
  let timeoutId

  const updateDebouncedValue = (newValue) => {
    clearTimeout(timeoutId)
    timeoutId = setTimeout(() => {
      debouncedValue.value = newValue
    }, delay)
  }

  if (typeof value === 'object' && 'value' in value) {
    watch(value, updateDebouncedValue, { immediate: true })
  } else {
    updateDebouncedValue(value)
  }

  onUnmounted(() => {
    clearTimeout(timeoutId)
  })

  return debouncedValue
}

/**
 * 节流 Hook
 */
export function useThrottle(fn, delay = 300) {
  let lastExecTime = 0
  let timeoutId

  const throttledFn = (...args) => {
    const currentTime = Date.now()
    
    if (currentTime - lastExecTime > delay) {
      fn(...args)
      lastExecTime = currentTime
    } else {
      clearTimeout(timeoutId)
      timeoutId = setTimeout(() => {
        fn(...args)
        lastExecTime = Date.now()
      }, delay - (currentTime - lastExecTime))
    }
  }

  onUnmounted(() => {
    clearTimeout(timeoutId)
  })

  return throttledFn
}

/**
 * 本地存储 Hook
 */
export function useLocalStorage(key, defaultValue = null) {
  const storedValue = ref(defaultValue)

  // 读取存储的值
  const readValue = () => {
    try {
      const item = localStorage.getItem(key)
      return item ? JSON.parse(item) : defaultValue
    } catch (error) {
      console.warn(`Error reading localStorage key "${key}":`, error)
      return defaultValue
    }
  }

  // 设置值
  const setValue = (value) => {
    try {
      storedValue.value = value
      localStorage.setItem(key, JSON.stringify(value))
    } catch (error) {
      console.warn(`Error setting localStorage key "${key}":`, error)
    }
  }

  // 删除值
  const removeValue = () => {
    try {
      localStorage.removeItem(key)
      storedValue.value = defaultValue
    } catch (error) {
      console.warn(`Error removing localStorage key "${key}":`, error)
    }
  }

  // 初始化
  onMounted(() => {
    storedValue.value = readValue()
  })

  // 监听值变化
  watch(storedValue, (newValue) => {
    setValue(newValue)
  }, { deep: true })

  return [storedValue, setValue, removeValue]
}

/**
 * 会话存储 Hook
 */
export function useSessionStorage(key, defaultValue = null) {
  const storedValue = ref(defaultValue)

  const readValue = () => {
    try {
      const item = sessionStorage.getItem(key)
      return item ? JSON.parse(item) : defaultValue
    } catch (error) {
      console.warn(`Error reading sessionStorage key "${key}":`, error)
      return defaultValue
    }
  }

  const setValue = (value) => {
    try {
      storedValue.value = value
      sessionStorage.setItem(key, JSON.stringify(value))
    } catch (error) {
      console.warn(`Error setting sessionStorage key "${key}":`, error)
    }
  }

  const removeValue = () => {
    try {
      sessionStorage.removeItem(key)
      storedValue.value = defaultValue
    } catch (error) {
      console.warn(`Error removing sessionStorage key "${key}":`, error)
    }
  }

  onMounted(() => {
    storedValue.value = readValue()
  })

  watch(storedValue, (newValue) => {
    setValue(newValue)
  }, { deep: true })

  return [storedValue, setValue, removeValue]
}

/**
 * 复制到剪贴板 Hook
 */
export function useClipboard() {
  const isSupported = computed(() => 
    navigator && 'clipboard' in navigator
  )
  
  const copy = async (text) => {
    if (!isSupported.value) {
      throw new Error('Clipboard API not supported')
    }
    
    try {
      await navigator.clipboard.writeText(text)
      return true
    } catch (error) {
      console.error('Failed to copy to clipboard:', error)
      throw error
    }
  }

  const read = async () => {
    if (!isSupported.value) {
      throw new Error('Clipboard API not supported')
    }
    
    try {
      return await navigator.clipboard.readText()
    } catch (error) {
      console.error('Failed to read from clipboard:', error)
      throw error
    }
  }

  return {
    isSupported,
    copy,
    read
  }
}

/**
 * 时间格式化 Hook
 */
export function useTimeFormat() {
  const formatTime = (timestamp, format = 'YYYY-MM-DD HH:mm:ss') => {
    if (!timestamp) return ''
    
    const date = new Date(timestamp)
    if (isNaN(date.getTime())) return ''

    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')
    const seconds = String(date.getSeconds()).padStart(2, '0')

    return format
      .replace('YYYY', year)
      .replace('MM', month)
      .replace('DD', day)
      .replace('HH', hours)
      .replace('mm', minutes)
      .replace('ss', seconds)
  }

  const formatRelativeTime = (timestamp) => {
    if (!timestamp) return ''
    
    const now = new Date()
    const date = new Date(timestamp)
    const diffMs = now - date
    const diffSecs = Math.floor(diffMs / 1000)
    const diffMins = Math.floor(diffSecs / 60)
    const diffHours = Math.floor(diffMins / 60)
    const diffDays = Math.floor(diffHours / 24)

    if (diffSecs < 60) return '刚刚'
    if (diffMins < 60) return `${diffMins}分钟前`
    if (diffHours < 24) return `${diffHours}小时前`
    if (diffDays < 7) return `${diffDays}天前`
    
    return formatTime(timestamp, 'MM-DD HH:mm')
  }

  const formatDuration = (ms) => {
    if (!ms || ms < 0) return '0秒'
    
    const seconds = Math.floor(ms / 1000) % 60
    const minutes = Math.floor(ms / (1000 * 60)) % 60
    const hours = Math.floor(ms / (1000 * 60 * 60)) % 24
    const days = Math.floor(ms / (1000 * 60 * 60 * 24))

    const parts = []
    if (days > 0) parts.push(`${days}天`)
    if (hours > 0) parts.push(`${hours}小时`)
    if (minutes > 0) parts.push(`${minutes}分钟`)
    if (seconds > 0 || parts.length === 0) parts.push(`${seconds}秒`)

    return parts.join('')
  }

  return {
    formatTime,
    formatRelativeTime,
    formatDuration
  }
}

/**
 * 数字格式化 Hook
 */
export function useNumberFormat() {
  const formatNumber = (num, decimals = 2) => {
    if (num === null || num === undefined || isNaN(num)) return '0'
    return Number(num).toFixed(decimals)
  }

  const formatCurrency = (amount, currency = 'USDT', decimals = 2) => {
    if (amount === null || amount === undefined || isNaN(amount)) return `0 ${currency}`
    return `${Number(amount).toFixed(decimals)} ${currency}`
  }

  const formatPercentage = (value, decimals = 2) => {
    if (value === null || value === undefined || isNaN(value)) return '0%'
    return `${Number(value).toFixed(decimals)}%`
  }

  const formatLargeNumber = (num) => {
    if (num === null || num === undefined || isNaN(num)) return '0'
    
    const absNum = Math.abs(num)
    const sign = num < 0 ? '-' : ''
    
    if (absNum >= 1e9) {
      return `${sign}${(absNum / 1e9).toFixed(1)}B`
    }
    if (absNum >= 1e6) {
      return `${sign}${(absNum / 1e6).toFixed(1)}M`
    }
    if (absNum >= 1e3) {
      return `${sign}${(absNum / 1e3).toFixed(1)}K`
    }
    
    return `${sign}${absNum.toFixed(2)}`
  }

  const formatBytes = (bytes) => {
    if (bytes === 0) return '0 B'
    
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    
    return `${(bytes / Math.pow(k, i)).toFixed(2)} ${sizes[i]}`
  }

  return {
    formatNumber,
    formatCurrency,
    formatPercentage,
    formatLargeNumber,
    formatBytes
  }
}

/**
 * 颜色工具 Hook
 */
export function useColorUtils() {
  const getStatusColor = (status) => {
    const colorMap = {
      success: 'success',
      error: 'error',
      warning: 'warning',
      info: 'info',
      pending: 'orange',
      processing: 'blue',
      completed: 'green',
      cancelled: 'grey',
      failed: 'red'
    }
    return colorMap[status] || 'grey'
  }

  const getPriceChangeColor = (change) => {
    if (change > 0) return 'success'
    if (change < 0) return 'error'
    return 'grey'
  }

  const hexToRgb = (hex) => {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
    return result ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16)
    } : null
  }

  const rgbToHex = (r, g, b) => {
    return `#${((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1)}`
  }

  return {
    getStatusColor,
    getPriceChangeColor,
    hexToRgb,
    rgbToHex
  }
}

export default {
  useDebounce,
  useThrottle,
  useLocalStorage,
  useSessionStorage,
  useClipboard,
  useTimeFormat,
  useNumberFormat,
  useColorUtils
}