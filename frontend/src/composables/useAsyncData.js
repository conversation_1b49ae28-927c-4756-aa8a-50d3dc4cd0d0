/**
 * 异步数据获取 Composable
 * 提供加载状态、错误处理和数据缓存功能
 */

import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { useUIStore } from '@/stores/ui'

/**
 * 使用异步数据
 * @param {Function} fetchFn - 数据获取函数
 * @param {Object} options - 配置选项
 * @returns {Object} 响应式数据和方法
 */
export function useAsyncData(fetchFn, options = {}) {
  const {
    immediate = true,
    resetOnExecute = true,
    shallow = true,
    throwOnFailed = false,
    initialData = null,
    delay = 0,
    timeout = 0,
    retryCount = 0,
    retryDelay = 1000,
    onSuccess = null,
    onError = null,
    showErrorNotification = true,
    showLoadingNotification = false
  } = options

  const uiStore = useUIStore()

  // 状态
  const data = ref(initialData)
  const error = ref(null)
  const isLoading = ref(false)
  const isFinished = ref(false)
  const canAbort = computed(() => typeof abortController !== 'undefined')

  let abortController
  let timeoutId
  let retryTimeoutId

  // 计算属性
  const isReady = computed(() => isFinished.value && !isLoading.value)
  const hasError = computed(() => error.value !== null)
  const hasData = computed(() => data.value !== null && data.value !== undefined)

  /**
   * 执行数据获取
   */
  async function execute(throwOnFailed = false, ...args) {
    if (resetOnExecute) {
      data.value = initialData
    }
    
    error.value = null
    isFinished.value = false
    isLoading.value = true

    // 显示加载通知
    if (showLoadingNotification) {
      uiStore.showLoading('正在加载数据...')
    }

    // 创建 AbortController
    abortController = new AbortController()

    try {
      // 延迟执行
      if (delay > 0) {
        await new Promise(resolve => setTimeout(resolve, delay))
      }

      // 设置超时
      if (timeout > 0) {
        timeoutId = setTimeout(() => {
          abortController.abort()
        }, timeout)
      }

      // 执行数据获取
      const result = await fetchFn(abortController.signal, ...args)
      
      // 清除超时
      if (timeoutId) {
        clearTimeout(timeoutId)
      }

      data.value = result
      isFinished.value = true

      // 成功回调
      if (onSuccess) {
        onSuccess(result)
      }

      return result

    } catch (err) {
      // 清除超时
      if (timeoutId) {
        clearTimeout(timeoutId)
      }

      // 如果是取消操作，不处理错误
      if (err.name === 'AbortError') {
        return
      }

      error.value = err
      isFinished.value = true

      // 错误回调
      if (onError) {
        onError(err)
      }

      // 显示错误通知
      if (showErrorNotification) {
        uiStore.showError(err.message || '数据获取失败')
      }

      // 重试逻辑
      if (retryCount > 0) {
        await retry(retryCount, throwOnFailed, ...args)
        return
      }

      // 抛出错误
      if (throwOnFailed) {
        throw err
      }

    } finally {
      isLoading.value = false
      
      // 隐藏加载通知
      if (showLoadingNotification) {
        uiStore.hideLoading()
      }
    }
  }

  /**
   * 重试逻辑
   */
  async function retry(count, throwOnFailed, ...args) {
    if (count <= 0) return

    return new Promise((resolve, reject) => {
      retryTimeoutId = setTimeout(async () => {
        try {
          const result = await execute(throwOnFailed, ...args)
          resolve(result)
        } catch (err) {
          if (count > 1) {
            retry(count - 1, throwOnFailed, ...args).then(resolve).catch(reject)
          } else {
            reject(err)
          }
        }
      }, retryDelay)
    })
  }

  /**
   * 取消请求
   */
  function abort() {
    if (abortController) {
      abortController.abort()
    }
    
    if (timeoutId) {
      clearTimeout(timeoutId)
    }
    
    if (retryTimeoutId) {
      clearTimeout(retryTimeoutId)
    }
    
    isLoading.value = false
  }

  /**
   * 刷新数据
   */
  function refresh(...args) {
    return execute(throwOnFailed, ...args)
  }

  /**
   * 重置状态
   */
  function reset() {
    abort()
    data.value = initialData
    error.value = null
    isLoading.value = false
    isFinished.value = false
  }

  // 立即执行
  if (immediate) {
    execute(throwOnFailed)
  }

  // 清理
  onUnmounted(() => {
    abort()
  })

  return {
    // 状态
    data,
    error,
    isLoading,
    isFinished,
    isReady,
    hasError,
    hasData,
    canAbort,
    
    // 方法
    execute,
    refresh,
    abort,
    reset
  }
}

/**
 * 使用分页数据
 */
export function usePaginatedData(fetchFn, options = {}) {
  const {
    pageSize = 20,
    initialPage = 1,
    ...asyncOptions
  } = options

  const currentPage = ref(initialPage)
  const totalItems = ref(0)
  const totalPages = computed(() => Math.ceil(totalItems.value / pageSize))
  const hasNextPage = computed(() => currentPage.value < totalPages.value)
  const hasPrevPage = computed(() => currentPage.value > 1)

  // 包装获取函数以支持分页
  const wrappedFetchFn = async (signal, ...args) => {
    const result = await fetchFn(signal, {
      page: currentPage.value,
      page_size: pageSize,
      ...args[0]
    })
    
    if (result && typeof result === 'object') {
      totalItems.value = result.total || 0
      return result.items || result.data || result
    }
    
    return result
  }

  const asyncData = useAsyncData(wrappedFetchFn, asyncOptions)

  // 分页方法
  function nextPage() {
    if (hasNextPage.value) {
      currentPage.value++
      asyncData.refresh()
    }
  }

  function prevPage() {
    if (hasPrevPage.value) {
      currentPage.value--
      asyncData.refresh()
    }
  }

  function goToPage(page) {
    if (page >= 1 && page <= totalPages.value) {
      currentPage.value = page
      asyncData.refresh()
    }
  }

  function resetPagination() {
    currentPage.value = initialPage
    totalItems.value = 0
  }

  return {
    ...asyncData,
    
    // 分页状态
    currentPage,
    totalItems,
    totalPages,
    hasNextPage,
    hasPrevPage,
    pageSize,
    
    // 分页方法
    nextPage,
    prevPage,
    goToPage,
    resetPagination
  }
}

export default {
  useAsyncData,
  usePaginatedData
}