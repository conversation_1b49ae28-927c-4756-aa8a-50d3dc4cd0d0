/**
 * 表单处理 Composable
 * 提供表单验证、提交处理和状态管理功能
 */

import { ref, reactive, computed, watch, nextTick } from 'vue'
import { useUIStore } from '@/stores/ui'

/**
 * 使用表单
 * @param {Object} initialData - 初始表单数据
 * @param {Object} options - 配置选项
 * @returns {Object} 表单状态和方法
 */
export function useForm(initialData = {}, options = {}) {
  const {
    validationRules = {},
    validateOnChange = true,
    validateOnBlur = true,
    resetOnSubmit = false,
    showSuccessNotification = true,
    showErrorNotification = true,
    onSubmit = null,
    onSuccess = null,
    onError = null,
    onReset = null
  } = options

  const uiStore = useUIStore()

  // 表单数据
  const formData = reactive({ ...initialData })
  const originalData = { ...initialData }

  // 表单状态
  const errors = ref({})
  const touched = ref({})
  const isSubmitting = ref(false)
  const isValidating = ref(false)
  const submitCount = ref(0)

  // 计算属性
  const hasErrors = computed(() => Object.keys(errors.value).length > 0)
  const isDirty = computed(() => {
    return JSON.stringify(formData) !== JSON.stringify(originalData)
  })
  const isValid = computed(() => !hasErrors.value)
  const canSubmit = computed(() => isValid.value && !isSubmitting.value)

  /**
   * 验证单个字段
   */
  function validateField(fieldName, value = formData[fieldName]) {
    const rules = validationRules[fieldName]
    if (!rules) return true

    const fieldErrors = []

    // 执行验证规则
    for (const rule of Array.isArray(rules) ? rules : [rules]) {
      if (typeof rule === 'function') {
        const result = rule(value, formData)
        if (result !== true) {
          fieldErrors.push(result || `${fieldName} 验证失败`)
        }
      } else if (typeof rule === 'object') {
        const { validator, message } = rule
        const result = validator(value, formData)
        if (result !== true) {
          fieldErrors.push(message || `${fieldName} 验证失败`)
        }
      }
    }

    // 更新错误状态
    if (fieldErrors.length > 0) {
      errors.value[fieldName] = fieldErrors
      return false
    } else {
      delete errors.value[fieldName]
      return true
    }
  }

  /**
   * 验证所有字段
   */
  function validateForm() {
    isValidating.value = true
    
    const fieldNames = Object.keys(validationRules)
    let isFormValid = true

    for (const fieldName of fieldNames) {
      const isFieldValid = validateField(fieldName)
      if (!isFieldValid) {
        isFormValid = false
      }
    }

    isValidating.value = false
    return isFormValid
  }

  /**
   * 设置字段值
   */
  function setFieldValue(fieldName, value) {
    formData[fieldName] = value
    
    if (validateOnChange && touched.value[fieldName]) {
      nextTick(() => validateField(fieldName, value))
    }
  }

  /**
   * 设置字段错误
   */
  function setFieldError(fieldName, error) {
    if (error) {
      errors.value[fieldName] = Array.isArray(error) ? error : [error]
    } else {
      delete errors.value[fieldName]
    }
  }

  /**
   * 设置多个字段错误
   */
  function setErrors(errorObj) {
    Object.keys(errorObj).forEach(fieldName => {
      setFieldError(fieldName, errorObj[fieldName])
    })
  }

  /**
   * 标记字段为已触摸
   */
  function setFieldTouched(fieldName, isTouched = true) {
    touched.value[fieldName] = isTouched
    
    if (isTouched && validateOnBlur) {
      validateField(fieldName)
    }
  }

  /**
   * 重置表单
   */
  function resetForm(newData = originalData) {
    Object.keys(formData).forEach(key => {
      delete formData[key]
    })
    Object.assign(formData, { ...newData })
    
    errors.value = {}
    touched.value = {}
    isSubmitting.value = false
    submitCount.value = 0

    if (onReset) {
      onReset(formData)
    }
  }

  /**
   * 清除错误
   */
  function clearErrors() {
    errors.value = {}
  }

  /**
   * 清除字段错误
   */
  function clearFieldError(fieldName) {
    delete errors.value[fieldName]
  }

  /**
   * 提交表单
   */
  async function submitForm(submitFn = onSubmit) {
    if (!submitFn) {
      console.warn('No submit function provided')
      return false
    }

    submitCount.value++
    
    // 标记所有字段为已触摸
    Object.keys(validationRules).forEach(fieldName => {
      touched.value[fieldName] = true
    })

    // 验证表单
    const isFormValid = validateForm()
    if (!isFormValid) {
      if (showErrorNotification) {
        uiStore.showError('请检查表单中的错误')
      }
      return false
    }

    isSubmitting.value = true

    try {
      const result = await submitFn(formData)
      
      if (onSuccess) {
        onSuccess(result, formData)
      }

      if (showSuccessNotification) {
        uiStore.showSuccess('操作成功')
      }

      if (resetOnSubmit) {
        resetForm()
      }

      return result

    } catch (error) {
      if (onError) {
        onError(error, formData)
      }

      // 处理服务器验证错误
      if (error.data && error.data.errors) {
        setErrors(error.data.errors)
      }

      if (showErrorNotification) {
        uiStore.showError(error.message || '操作失败')
      }

      throw error

    } finally {
      isSubmitting.value = false
    }
  }

  /**
   * 获取字段属性
   */
  function getFieldProps(fieldName) {
    return {
      modelValue: formData[fieldName],
      'onUpdate:modelValue': (value) => setFieldValue(fieldName, value),
      onBlur: () => setFieldTouched(fieldName),
      error: !!errors.value[fieldName],
      errorMessages: errors.value[fieldName] || []
    }
  }

  // 监听表单数据变化
  if (validateOnChange) {
    watch(
      () => formData,
      () => {
        Object.keys(touched.value).forEach(fieldName => {
          if (touched.value[fieldName]) {
            validateField(fieldName)
          }
        })
      },
      { deep: true }
    )
  }

  return {
    // 表单数据
    formData,
    
    // 表单状态
    errors,
    touched,
    isSubmitting,
    isValidating,
    submitCount,
    hasErrors,
    isDirty,
    isValid,
    canSubmit,
    
    // 表单方法
    validateField,
    validateForm,
    setFieldValue,
    setFieldError,
    setErrors,
    setFieldTouched,
    resetForm,
    clearErrors,
    clearFieldError,
    submitForm,
    getFieldProps
  }
}

/**
 * 常用验证规则
 */
export const validationRules = {
  required: (message = '此字段为必填项') => (value) => {
    if (value === null || value === undefined || value === '') {
      return message
    }
    return true
  },

  email: (message = '请输入有效的邮箱地址') => (value) => {
    if (!value) return true
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(value) || message
  },

  minLength: (min, message) => (value) => {
    if (!value) return true
    const msg = message || `最少需要 ${min} 个字符`
    return value.length >= min || msg
  },

  maxLength: (max, message) => (value) => {
    if (!value) return true
    const msg = message || `最多允许 ${max} 个字符`
    return value.length <= max || msg
  },

  min: (min, message) => (value) => {
    if (value === null || value === undefined || value === '') return true
    const msg = message || `最小值为 ${min}`
    return Number(value) >= min || msg
  },

  max: (max, message) => (value) => {
    if (value === null || value === undefined || value === '') return true
    const msg = message || `最大值为 ${max}`
    return Number(value) <= max || msg
  },

  pattern: (regex, message = '格式不正确') => (value) => {
    if (!value) return true
    return regex.test(value) || message
  },

  confirmed: (confirmField, message = '两次输入不一致') => (value, formData) => {
    return value === formData[confirmField] || message
  },

  numeric: (message = '请输入数字') => (value) => {
    if (!value) return true
    return !isNaN(Number(value)) || message
  },

  url: (message = '请输入有效的URL') => (value) => {
    if (!value) return true
    try {
      new URL(value)
      return true
    } catch {
      return message
    }
  }
}

export default {
  useForm,
  validationRules
}