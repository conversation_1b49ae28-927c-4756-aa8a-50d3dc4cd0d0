/**
 * Composables 统一导出
 */

export { useAsyncData, usePaginatedData } from './useAsyncData.js'
export { useForm, validationRules } from './useForm.js'
export { useSnackbar } from './useSnackbar.js'
export {
  useDebounce,
  useThrottle,
  useLocalStorage,
  useSessionStorage,
  useClipboard,
  useTimeFormat,
  useNumberFormat,
  useColorUtils
} from './useUtils.js'

export default {
  useAsyncData: () => import('./useAsyncData.js'),
  useForm: () => import('./useForm.js'),
  useUtils: () => import('./useUtils.js')
}