/**
 * 无障碍功能增强组合式函数
 * 提供键盘导航、屏幕阅读器支持和焦点管理
 */

import { ref, onMounted, onUnmounted, nextTick } from 'vue'

export interface AccessibilityOptions {
  announcePageChanges?: boolean
  trapFocus?: boolean
  skipLinks?: boolean
  highContrast?: boolean
}

export function useAccessibility(options: AccessibilityOptions = {}) {
  const {
    announcePageChanges = true,
    skipLinks = true,
    highContrast = false
  } = options

  const isHighContrast = ref(highContrast)
  const currentFocus = ref<HTMLElement | null>(null)
  const focusableElements = ref<HTMLElement[]>([])

  /**
   * 向屏幕阅读器宣布消息
   */
  function announceToScreenReader(message: string, priority: 'polite' | 'assertive' = 'polite') {
    const announcement = document.createElement('div')
    announcement.setAttribute('aria-live', priority)
    announcement.setAttribute('aria-atomic', 'true')
    announcement.className = 'sr-only'
    announcement.textContent = message

    document.body.appendChild(announcement)

    // 清理
    setTimeout(() => {
      document.body.removeChild(announcement)
    }, 1000)
  }

  /**
   * 设置焦点到指定元素
   */
  function setFocus(element: HTMLElement | string, options?: FocusOptions) {
    nextTick(() => {
      let targetElement: HTMLElement | null = null

      if (typeof element === 'string') {
        targetElement = document.querySelector(element)
      } else {
        targetElement = element
      }

      if (targetElement) {
        targetElement.focus(options)
        currentFocus.value = targetElement
      }
    })
  }

  /**
   * 获取所有可聚焦元素
   */
  function getFocusableElements(container: HTMLElement = document.body): HTMLElement[] {
    const focusableSelectors = [
      'a[href]',
      'button:not([disabled])',
      'input:not([disabled])',
      'select:not([disabled])',
      'textarea:not([disabled])',
      '[tabindex]:not([tabindex="-1"])',
      '[contenteditable="true"]'
    ].join(', ')

    return Array.from(container.querySelectorAll(focusableSelectors))
  }

  /**
   * 焦点陷阱 - 将焦点限制在指定容器内
   */
  function trapFocusInContainer(container: HTMLElement) {
    const focusableEls = getFocusableElements(container)
    const firstFocusable = focusableEls[0]
    const lastFocusable = focusableEls[focusableEls.length - 1]

    function handleTabKey(e: KeyboardEvent) {
      if (e.key !== 'Tab') return

      if (e.shiftKey) {
        // Shift + Tab
        if (document.activeElement === firstFocusable) {
          e.preventDefault()
          lastFocusable?.focus()
        }
      } else {
        // Tab
        if (document.activeElement === lastFocusable) {
          e.preventDefault()
          firstFocusable?.focus()
        }
      }
    }

    container.addEventListener('keydown', handleTabKey)

    // 返回清理函数
    return () => {
      container.removeEventListener('keydown', handleTabKey)
    }
  }

  /**
   * 键盘导航处理
   */
  function handleKeyboardNavigation(e: KeyboardEvent) {
    const { key, ctrlKey, altKey } = e

    // 跳转到主要内容 (Alt + M)
    if (altKey && key === 'm') {
      e.preventDefault()
      const mainContent = document.querySelector('main, [role="main"], #main-content')
      if (mainContent) {
        setFocus(mainContent as HTMLElement)
        announceToScreenReader('跳转到主要内容')
      }
    }

    // 跳转到导航 (Alt + N)
    if (altKey && key === 'n') {
      e.preventDefault()
      const navigation = document.querySelector('nav, [role="navigation"]')
      if (navigation) {
        setFocus(navigation as HTMLElement)
        announceToScreenReader('跳转到导航菜单')
      }
    }

    // 切换高对比度模式 (Ctrl + Alt + H)
    if (ctrlKey && altKey && key === 'h') {
      e.preventDefault()
      toggleHighContrast()
    }

    // ESC键关闭模态框或返回
    if (key === 'Escape') {
      const modal = document.querySelector('[role="dialog"][aria-modal="true"]')
      if (modal) {
        const closeButton = modal.querySelector('[aria-label*="关闭"], [aria-label*="close"]')
        if (closeButton) {
          (closeButton as HTMLElement).click()
        }
      }
    }
  }

  /**
   * 切换高对比度模式
   */
  function toggleHighContrast() {
    isHighContrast.value = !isHighContrast.value
    
    if (isHighContrast.value) {
      document.documentElement.classList.add('high-contrast')
      announceToScreenReader('高对比度模式已开启')
    } else {
      document.documentElement.classList.remove('high-contrast')
      announceToScreenReader('高对比度模式已关闭')
    }

    // 保存用户偏好
    localStorage.setItem('high-contrast', isHighContrast.value.toString())
  }

  /**
   * 创建跳转链接
   */
  function createSkipLinks() {
    if (!skipLinks) return

    const skipLinksContainer = document.createElement('div')
    skipLinksContainer.className = 'skip-links'
    skipLinksContainer.innerHTML = `
      <a href="#main-content" class="skip-link">跳转到主要内容</a>
      <a href="#navigation" class="skip-link">跳转到导航</a>
    `

    document.body.insertBefore(skipLinksContainer, document.body.firstChild)
  }

  /**
   * 页面变化宣布
   */
  function announcePageChange(pageName: string) {
    if (announcePageChanges) {
      announceToScreenReader(`已导航到 ${pageName} 页面`, 'assertive')
    }
  }

  /**
   * 表单验证错误宣布
   */
  function announceFormErrors(errors: string[]) {
    if (errors.length > 0) {
      const errorMessage = `表单包含 ${errors.length} 个错误: ${errors.join(', ')}`
      announceToScreenReader(errorMessage, 'assertive')
    }
  }

  /**
   * 动态内容更新宣布
   */
  function announceContentUpdate(message: string) {
    announceToScreenReader(message, 'polite')
  }

  /**
   * 初始化无障碍功能
   */
  function initializeAccessibility() {
    // 从本地存储恢复高对比度设置
    const savedHighContrast = localStorage.getItem('high-contrast')
    if (savedHighContrast === 'true') {
      isHighContrast.value = true
      document.documentElement.classList.add('high-contrast')
    }

    // 创建跳转链接
    createSkipLinks()

    // 添加键盘事件监听
    document.addEventListener('keydown', handleKeyboardNavigation)

    // 检测用户是否使用键盘导航
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Tab') {
        document.body.classList.add('keyboard-navigation')
      }
    })

    document.addEventListener('mousedown', () => {
      document.body.classList.remove('keyboard-navigation')
    })
  }

  /**
   * 清理无障碍功能
   */
  function cleanupAccessibility() {
    document.removeEventListener('keydown', handleKeyboardNavigation)
  }

  // 生命周期
  onMounted(() => {
    initializeAccessibility()
  })

  onUnmounted(() => {
    cleanupAccessibility()
  })

  return {
    // 状态
    isHighContrast,
    currentFocus,
    focusableElements,

    // 方法
    announceToScreenReader,
    setFocus,
    getFocusableElements,
    trapFocusInContainer,
    toggleHighContrast,
    announcePageChange,
    announceFormErrors,
    announceContentUpdate
  }
}
