/**
 * Snackbar 通知组合式函数
 *
 * 提供统一的消息通知功能
 */

import { ref, reactive } from 'vue'

// 全局状态
const snackbarState = reactive({
  show: false,
  message: '',
  color: 'info',
  timeout: 4000,
  multiLine: false,
  vertical: false,
  actions: []
})

/**
 * Snackbar 组合式函数
 */
export function useSnackbar() {
  /**
   * 显示通知消息
   * @param {string} message - 消息内容
   * @param {string} type - 消息类型 (success, error, warning, info)
   * @param {Object} options - 可选配置
   */
  const showSnackbar = (message, type = 'info', options = {}) => {
    const config = {
      timeout: 4000,
      multiLine: false,
      vertical: false,
      actions: [],
      ...options
    }

    // 颜色映射
    const colorMap = {
      success: 'success',
      error: 'error',
      warning: 'warning',
      info: 'info',
      primary: 'primary',
      secondary: 'secondary'
    }

    snackbarState.message = message
    snackbarState.color = colorMap[type] || 'info'
    snackbarState.timeout = config.timeout
    snackbarState.multiLine = config.multiLine
    snackbarState.vertical = config.vertical
    snackbarState.actions = config.actions
    snackbarState.show = true
  }

  /**
   * 显示成功消息
   * @param {string} message - 消息内容
   * @param {Object} options - 可选配置
   */
  const showSuccess = (message, options = {}) => {
    showSnackbar(message, 'success', options)
  }

  /**
   * 显示错误消息
   * @param {string} message - 消息内容
   * @param {Object} options - 可选配置
   */
  const showError = (message, options = {}) => {
    showSnackbar(message, 'error', { timeout: 6000, ...options })
  }

  /**
   * 显示警告消息
   * @param {string} message - 消息内容
   * @param {Object} options - 可选配置
   */
  const showWarning = (message, options = {}) => {
    showSnackbar(message, 'warning', options)
  }

  /**
   * 显示信息消息
   * @param {string} message - 消息内容
   * @param {Object} options - 可选配置
   */
  const showInfo = (message, options = {}) => {
    showSnackbar(message, 'info', options)
  }

  /**
   * 隐藏通知
   */
  const hideSnackbar = () => {
    snackbarState.show = false
  }

  /**
   * 显示确认操作的通知
   * @param {string} message - 消息内容
   * @param {Function} onConfirm - 确认回调
   * @param {Object} options - 可选配置
   */
  const showConfirm = (message, onConfirm, options = {}) => {
    const config = {
      timeout: 0, // 不自动关闭
      actions: [
        {
          text: '取消',
          color: 'grey',
          onClick: hideSnackbar
        },
        {
          text: '确定',
          color: 'primary',
          onClick: () => {
            onConfirm()
            hideSnackbar()
          }
        }
      ],
      ...options
    }

    showSnackbar(message, 'info', config)
  }

  /**
   * 显示带操作按钮的通知
   * @param {string} message - 消息内容
   * @param {Array} actions - 操作按钮配置
   * @param {Object} options - 可选配置
   */
  const showWithActions = (message, actions, options = {}) => {
    const config = {
      timeout: 0, // 不自动关闭
      actions: actions.map(action => ({
        text: action.text,
        color: action.color || 'primary',
        onClick: () => {
          if (action.onClick) {
            action.onClick()
          }
          if (action.autoClose !== false) {
            hideSnackbar()
          }
        }
      })),
      ...options
    }

    showSnackbar(message, options.type || 'info', config)
  }

  return {
    // 状态
    snackbarState,

    // 方法
    showSnackbar,
    showSuccess,
    showError,
    showWarning,
    showInfo,
    hideSnackbar,
    showConfirm,
    showWithActions
  }
}