/**
 * 组件样式库
 * 提供可复用的组件样式类
 */

/* 按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-sm) var(--spacing-md);
  border: none;
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-md);
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all var(--transition-fast);
  user-select: none;
  white-space: nowrap;
  min-height: 40px;
  gap: var(--spacing-xs);
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background-color: var(--primary-dark);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-secondary {
  background-color: var(--secondary-color);
  color: white;
}

.btn-secondary:hover:not(:disabled) {
  background-color: var(--secondary-dark);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-success {
  background-color: var(--success-color);
  color: white;
}

.btn-success:hover:not(:disabled) {
  background-color: #45a049;
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-warning {
  background-color: var(--warning-color);
  color: white;
}

.btn-warning:hover:not(:disabled) {
  background-color: #f57c00;
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-error {
  background-color: var(--error-color);
  color: white;
}

.btn-error:hover:not(:disabled) {
  background-color: #d32f2f;
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-outline {
  background-color: transparent;
  border: 1px solid var(--border-color);
  color: var(--text-primary);
}

.btn-outline:hover:not(:disabled) {
  background-color: var(--surface-variant);
  border-color: var(--primary-color);
  color: var(--primary-color);
}

.btn-text {
  background-color: transparent;
  color: var(--primary-color);
  padding: var(--spacing-xs) var(--spacing-sm);
}

.btn-text:hover:not(:disabled) {
  background-color: rgba(25, 118, 210, 0.08);
}

/* 按钮尺寸 */
.btn-sm {
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: var(--font-size-sm);
  min-height: 32px;
}

.btn-lg {
  padding: var(--spacing-md) var(--spacing-lg);
  font-size: var(--font-size-lg);
  min-height: 48px;
}

.btn-block {
  width: 100%;
}

/* 卡片样式 */
.card {
  background-color: var(--surface-color);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  transition: box-shadow var(--transition-normal);
}

.card:hover {
  box-shadow: var(--shadow-md);
}

.card-header {
  padding: var(--spacing-md) var(--spacing-lg);
  border-bottom: 1px solid var(--divider-color);
  background-color: var(--surface-variant);
}

.card-body {
  padding: var(--spacing-lg);
}

.card-footer {
  padding: var(--spacing-md) var(--spacing-lg);
  border-top: 1px solid var(--divider-color);
  background-color: var(--surface-variant);
}

.card-title {
  margin: 0 0 var(--spacing-sm) 0;
  font-size: var(--font-size-xl);
  font-weight: 500;
  color: var(--text-primary);
}

.card-subtitle {
  margin: 0 0 var(--spacing-md) 0;
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

/* 表单样式 */
.form-group {
  margin-bottom: var(--spacing-md);
}

.form-label {
  display: block;
  margin-bottom: var(--spacing-xs);
  font-size: var(--font-size-sm);
  font-weight: 500;
  color: var(--text-primary);
}

.form-label.required::after {
  content: ' *';
  color: var(--error-color);
}

.form-control {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-md);
  background-color: var(--surface-color);
  color: var(--text-primary);
  transition: all var(--transition-fast);
}

.form-control:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.2);
}

.form-control:disabled {
  background-color: var(--surface-variant);
  color: var(--text-disabled);
  cursor: not-allowed;
}

.form-control.error {
  border-color: var(--error-color);
}

.form-control.error:focus {
  box-shadow: 0 0 0 2px rgba(244, 67, 54, 0.2);
}

.form-error {
  margin-top: var(--spacing-xs);
  font-size: var(--font-size-xs);
  color: var(--error-color);
}

.form-help {
  margin-top: var(--spacing-xs);
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
}

/* 选择框样式 */
.form-select {
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right var(--spacing-sm) center;
  background-repeat: no-repeat;
  background-size: 16px 12px;
  padding-right: calc(var(--spacing-md) + 20px);
}

/* 复选框和单选框样式 */
.form-check {
  display: flex;
  align-items: center;
  margin-bottom: var(--spacing-sm);
}

.form-check-input {
  margin-right: var(--spacing-sm);
  width: 16px;
  height: 16px;
  accent-color: var(--primary-color);
}

.form-check-label {
  font-size: var(--font-size-sm);
  color: var(--text-primary);
  cursor: pointer;
}

/* 表格样式 */
.table {
  width: 100%;
  border-collapse: collapse;
  background-color: var(--surface-color);
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.table th,
.table td {
  padding: var(--spacing-sm) var(--spacing-md);
  text-align: left;
  border-bottom: 1px solid var(--divider-color);
}

.table th {
  background-color: var(--surface-variant);
  font-weight: 500;
  color: var(--text-primary);
  font-size: var(--font-size-sm);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.table td {
  color: var(--text-primary);
  font-size: var(--font-size-sm);
}

.table tbody tr:hover {
  background-color: var(--surface-variant);
}

.table-striped tbody tr:nth-child(odd) {
  background-color: rgba(0, 0, 0, 0.02);
}

[data-theme="dark"] .table-striped tbody tr:nth-child(odd) {
  background-color: rgba(255, 255, 255, 0.02);
}

/* 徽章样式 */
.badge {
  display: inline-flex;
  align-items: center;
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: var(--font-size-xs);
  font-weight: 500;
  border-radius: var(--border-radius-xl);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.badge-primary {
  background-color: var(--primary-color);
  color: white;
}

.badge-secondary {
  background-color: var(--secondary-color);
  color: white;
}

.badge-success {
  background-color: var(--success-color);
  color: white;
}

.badge-warning {
  background-color: var(--warning-color);
  color: white;
}

.badge-error {
  background-color: var(--error-color);
  color: white;
}

.badge-info {
  background-color: var(--info-color);
  color: white;
}

.badge-outline {
  background-color: transparent;
  border: 1px solid var(--border-color);
  color: var(--text-secondary);
}

/* 进度条样式 */
.progress {
  width: 100%;
  height: 8px;
  background-color: var(--surface-variant);
  border-radius: var(--border-radius-xl);
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background-color: var(--primary-color);
  transition: width var(--transition-normal);
  border-radius: var(--border-radius-xl);
}

.progress-bar.success {
  background-color: var(--success-color);
}

.progress-bar.warning {
  background-color: var(--warning-color);
}

.progress-bar.error {
  background-color: var(--error-color);
}

/* 加载动画 */
.spinner {
  width: 20px;
  height: 20px;
  border: 2px solid var(--border-color);
  border-top: 2px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.spinner-lg {
  width: 40px;
  height: 40px;
  border-width: 4px;
}

.spinner-sm {
  width: 16px;
  height: 16px;
  border-width: 1px;
}

/* 分割线样式 */
.divider {
  height: 1px;
  background-color: var(--divider-color);
  margin: var(--spacing-md) 0;
}

.divider-vertical {
  width: 1px;
  height: 100%;
  background-color: var(--divider-color);
  margin: 0 var(--spacing-md);
}

/* 提示框样式 */
.tooltip {
  position: relative;
  display: inline-block;
}

.tooltip .tooltip-text {
  visibility: hidden;
  width: max-content;
  max-width: 200px;
  background-color: var(--grey-800);
  color: white;
  text-align: center;
  border-radius: var(--border-radius-md);
  padding: var(--spacing-xs) var(--spacing-sm);
  position: absolute;
  z-index: var(--z-index-tooltip);
  bottom: 125%;
  left: 50%;
  transform: translateX(-50%);
  opacity: 0;
  transition: opacity var(--transition-fast);
  font-size: var(--font-size-xs);
  white-space: nowrap;
}

.tooltip .tooltip-text::after {
  content: "";
  position: absolute;
  top: 100%;
  left: 50%;
  margin-left: -5px;
  border-width: 5px;
  border-style: solid;
  border-color: var(--grey-800) transparent transparent transparent;
}

.tooltip:hover .tooltip-text {
  visibility: visible;
  opacity: 1;
}

/* 模态框遮罩 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: var(--z-index-modal-backdrop);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-md);
}

.modal-content {
  background-color: var(--surface-color);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-xl);
  max-width: 90vw;
  max-height: 90vh;
  overflow: auto;
  z-index: var(--z-index-modal);
}

/* 响应式网格 */
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
}

.row {
  display: flex;
  flex-wrap: wrap;
  margin: 0 calc(-1 * var(--spacing-sm));
}

.col {
  flex: 1;
  padding: 0 var(--spacing-sm);
}

.col-auto {
  flex: 0 0 auto;
  width: auto;
}

/* 响应式列 */
@media (min-width: 576px) {
  .col-sm-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
  .col-sm-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
  .col-sm-3 { flex: 0 0 25%; max-width: 25%; }
  .col-sm-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
  .col-sm-6 { flex: 0 0 50%; max-width: 50%; }
  .col-sm-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
  .col-sm-9 { flex: 0 0 75%; max-width: 75%; }
  .col-sm-12 { flex: 0 0 100%; max-width: 100%; }
}

@media (min-width: 768px) {
  .col-md-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
  .col-md-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
  .col-md-3 { flex: 0 0 25%; max-width: 25%; }
  .col-md-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
  .col-md-6 { flex: 0 0 50%; max-width: 50%; }
  .col-md-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
  .col-md-9 { flex: 0 0 75%; max-width: 75%; }
  .col-md-12 { flex: 0 0 100%; max-width: 100%; }
}

@media (min-width: 992px) {
  .col-lg-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
  .col-lg-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
  .col-lg-3 { flex: 0 0 25%; max-width: 25%; }
  .col-lg-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
  .col-lg-6 { flex: 0 0 50%; max-width: 50%; }
  .col-lg-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
  .col-lg-9 { flex: 0 0 75%; max-width: 75%; }
  .col-lg-12 { flex: 0 0 100%; max-width: 100%; }
}

/* 动画类 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity var(--transition-normal);
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-up-enter-active,
.slide-up-leave-active {
  transition: all var(--transition-normal);
}

.slide-up-enter-from,
.slide-up-leave-to {
  transform: translateY(20px);
  opacity: 0;
}

.slide-left-enter-active,
.slide-left-leave-active {
  transition: all var(--transition-normal);
}

.slide-left-enter-from,
.slide-left-leave-to {
  transform: translateX(-20px);
  opacity: 0;
}

.scale-enter-active,
.scale-leave-active {
  transition: all var(--transition-normal);
}

.scale-enter-from,
.scale-leave-to {
  transform: scale(0.9);
  opacity: 0;
}