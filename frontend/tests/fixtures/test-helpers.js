/**
 * Test Helper Functions
 * Reusable utility functions for testing
 */

import { expect } from '@playwright/test'
import { DEMO_CREDENTIALS, API_ENDPOINTS, SELECTORS, TIMEOUTS, MOCK_RESPONSES } from './test-data.js'

/**
 * Authentication helpers
 */
export class AuthHelpers {
  /**
   * Login via UI with improved error handling and retries
   */
  static async loginViaUI(page) {
    // Navigate with retry mechanism
    await UIHelpers.navigateWithRetry(page, API_ENDPOINTS.FRONTEND_URL)

    // Wait for page to be ready
    await UIHelpers.waitForPageReady(page)

    // 确保前端使用正确的API URL（非Docker环境）
    await page.evaluate(() => {
      // 强制设置正确的API基础URL
      if (window.location.hostname === 'localhost') {
        // 在localStorage中设置正确的API URL，覆盖任何错误的设置
        localStorage.setItem('api_base_url_override', 'http://localhost:8000')
      }
    })

    // Fill username and password using Vuetify-compatible selectors
    await UIHelpers.fillVuetifyInput(page, SELECTORS.AUTH.USERNAME_INPUT, DEMO_CREDENTIALS.username)
    await UIHelpers.fillVuetifyInput(page, SELECTORS.AUTH.PASSWORD_INPUT, DEMO_CREDENTIALS.password)

    // Click login button with multiple selector fallbacks
    await UIHelpers.clickWithFallback(page, SELECTORS.AUTH.LOGIN_BUTTON)

    // Wait for navigation with extended timeout and retry mechanism
    try {
      await page.waitForURL('**/dashboard', { timeout: TIMEOUTS.NAVIGATION })
      await expect(page).toHaveURL(/.*dashboard/)
    } catch (error) {
      console.log(`⚠️ 导航到仪表盘失败: ${error.message}`)
      // 尝试手动导航到仪表盘
      try {
        await page.goto(`${API_ENDPOINTS.FRONTEND_URL}/dashboard`, {
          waitUntil: 'networkidle',
          timeout: TIMEOUTS.NAVIGATION
        })
        await expect(page).toHaveURL(/.*dashboard/)
      } catch (retryError) {
        console.log(`⚠️ 手动导航也失败: ${retryError.message}`)
        // 最后尝试：检查是否已经在正确页面
        const currentUrl = page.url()
        if (!currentUrl.includes('dashboard')) {
          throw new Error(`无法导航到仪表盘页面。当前URL: ${currentUrl}`)
        }
      }
    }
  }

  /**
   * Login via API and return token
   */
  static async loginViaAPI(request) {
    const response = await request.post(`${API_ENDPOINTS.BASE_URL}${API_ENDPOINTS.AUTH.LOGIN}`, {
      data: DEMO_CREDENTIALS
    })
    
    if (!response.ok()) {
      throw new Error(`Login failed: ${response.status()} ${response.statusText()}`)
    }
    
    const data = await response.json()
    return data.access_token
  }

  /**
   * Logout via UI
   */
  static async logoutViaUI(page) {
    const userMenu = page.locator(SELECTORS.NAVIGATION.USER_MENU).first()
    await userMenu.click()
    await page.click(SELECTORS.AUTH.LOGOUT_BUTTON)
    await page.waitForURL('**/login', { timeout: TIMEOUTS.MEDIUM })
    await expect(page).toHaveURL(/.*login/)
  }

  /**
   * Check if user is authenticated
   */
  static async isAuthenticated(page) {
    const currentUrl = page.url()
    return !currentUrl.includes('login') && currentUrl.includes('dashboard')
  }
}

/**
 * Navigation helpers
 */
export class NavigationHelpers {
  /**
   * Navigate to orders page with improved error handling
   */
  static async goToOrders(page) {
    try {
      // Check if already on orders page
      const currentUrl = page.url()
      if (currentUrl.includes('/orders')) {
        console.log('✅ 已经在订单页面')
        return
      }

      // Try to find and click orders link
      const ordersSelectors = [
        SELECTORS.NAVIGATION.ORDERS_LINK,
        '[data-testid="nav-orders"]',
        'text=订单',
        'text=Orders',
        '.nav-orders'
      ]

      let linkFound = false
      for (const selector of ordersSelectors) {
        if (await page.locator(selector).isVisible({ timeout: 3000 }).catch(() => false)) {
          await page.locator(selector).first().click()
          await page.waitForURL('**/orders', { timeout: TIMEOUTS.MEDIUM })
          linkFound = true
          console.log(`✅ 导航到订单页面: ${selector}`)
          break
        }
      }

      if (!linkFound) {
        // Fallback: direct navigation
        console.log('⚠️ 未找到订单链接，尝试直接导航')
        await page.goto(`${API_ENDPOINTS.FRONTEND_URL}/orders`, {
          waitUntil: 'networkidle',
          timeout: TIMEOUTS.NAVIGATION
        })
      }
    } catch (error) {
      console.log(`⚠️ 导航到订单页面失败: ${error.message}`)
      // Final fallback: direct navigation
      await page.goto(`${API_ENDPOINTS.FRONTEND_URL}/orders`, {
        waitUntil: 'load',
        timeout: TIMEOUTS.NAVIGATION
      })
    }
  }

  /**
   * Navigate to dashboard with improved error handling
   */
  static async goToDashboard(page) {
    try {
      // Check if already on dashboard
      const currentUrl = page.url()
      if (currentUrl.includes('/dashboard')) {
        console.log('✅ 已经在仪表板页面')
        return
      }

      // Try to find and click dashboard link
      const dashboardLink = page.locator(SELECTORS.NAVIGATION.DASHBOARD_LINK)
      const linkCount = await dashboardLink.count()

      if (linkCount > 0) {
        await dashboardLink.first().click()
        await page.waitForURL('**/dashboard', { timeout: TIMEOUTS.MEDIUM })
      } else {
        // Fallback: direct navigation
        console.log('⚠️ 未找到仪表板链接，尝试直接导航')
        await page.goto(`${API_ENDPOINTS.FRONTEND_URL}/dashboard`, {
          waitUntil: 'networkidle',
          timeout: TIMEOUTS.NAVIGATION
        })
      }
    } catch (error) {
      console.log(`⚠️ 导航到仪表板失败: ${error.message}`)
      // Final fallback: direct navigation
      await page.goto(`${API_ENDPOINTS.FRONTEND_URL}/dashboard`, {
        waitUntil: 'load',
        timeout: TIMEOUTS.NAVIGATION
      })
    }
  }

  /**
   * Navigate to configs
   */
  static async goToConfigs(page) {
    const configsLink = page.locator(SELECTORS.NAVIGATION.CONFIGS_LINK)
    if (await configsLink.isVisible()) {
      await configsLink.click()
      await page.waitForTimeout(TIMEOUTS.SHORT)
    }
  }
}

/**
 * API mocking helpers
 */
export class MockHelpers {
  /**
   * Mock successful login
   */
  static async mockLoginSuccess(page) {
    await page.route(`**${API_ENDPOINTS.AUTH.LOGIN}`, route => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify(MOCK_RESPONSES.LOGIN_SUCCESS)
      })
    })
  }

  /**
   * Mock login failure
   */
  static async mockLoginFailure(page) {
    await page.route(`**${API_ENDPOINTS.AUTH.LOGIN}`, route => {
      route.fulfill({
        status: 401,
        contentType: 'application/json',
        body: JSON.stringify(MOCK_RESPONSES.LOGIN_ERROR)
      })
    })
  }

  /**
   * Mock orders API with custom data
   */
  static async mockOrdersAPI(page, ordersData, total = null) {
    await page.route(`**${API_ENDPOINTS.ORDERS}*`, route => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          orders: ordersData,
          total: total || ordersData.length,
          limit: 100,
          offset: 0
        })
      })
    })
  }

  /**
   * Mock empty orders response
   */
  static async mockEmptyOrders(page) {
    await page.route(`**${API_ENDPOINTS.ORDERS}*`, route => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify(MOCK_RESPONSES.ORDERS_EMPTY)
      })
    })
  }

  /**
   * Mock API error
   */
  static async mockAPIError(page, endpoint, statusCode = 500) {
    await page.route(`**${endpoint}*`, route => {
      route.fulfill({
        status: statusCode,
        contentType: 'application/json',
        body: JSON.stringify(MOCK_RESPONSES.SERVER_ERROR)
      })
    })
  }

  /**
   * Mock network failure
   */
  static async mockNetworkFailure(page, endpoint) {
    await page.route(`**${endpoint}*`, route => {
      route.abort('failed')
    })
  }

  /**
   * Mock slow API response
   */
  static async mockSlowResponse(page, endpoint, delay = 5000) {
    await page.route(`**${endpoint}*`, async route => {
      await new Promise(resolve => setTimeout(resolve, delay))
      route.continue()
    })
  }

  /**
   * Mock malformed JSON response
   */
  static async mockMalformedResponse(page, endpoint) {
    await page.route(`**${endpoint}*`, route => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: 'invalid json response'
      })
    })
  }
}

// Old UIHelpers class removed - using enhanced version below



/**
 * Assertion helpers
 */
export class AssertionHelpers {
  /**
   * Assert page URL contains path
   */
  static async assertURLContains(page, path) {
    await expect(page).toHaveURL(new RegExp(`.*${path}`))
  }

  /**
   * Assert element contains text
   */
  static async assertElementContainsText(page, selector, text) {
    await expect(page.locator(selector).first()).toContainText(text)
  }

  /**
   * Assert table has data
   */
  static async assertTableHasData(page) {
    // 首先检查表格是否存在
    const tableSelectors = [
      SELECTORS.ORDERS.TABLE,
      '.v-data-table',
      '.orders-table',
      '[data-testid="data-table"]',
      'table'
    ]

    let table = null
    for (const selector of tableSelectors) {
      const element = page.locator(selector).first()
      if (await element.isVisible({ timeout: 3000 }).catch(() => false)) {
        table = element
        console.log(`✅ 找到表格: ${selector}`)
        break
      }
    }

    if (!table) {
      console.log('⚠️ 未找到表格，检查是否显示空状态')
      // 检查是否显示空状态
      const emptyStateSelectors = [
        SELECTORS.ORDERS.EMPTY_STATE,
        'text=暂无数据',
        'text=No data',
        'text=暂无订单',
        '.empty-state',
        '.no-data'
      ]

      for (const selector of emptyStateSelectors) {
        const element = page.locator(selector).first()
        if (await element.isVisible({ timeout: 2000 }).catch(() => false)) {
          console.log(`✅ 找到空状态: ${selector}`)
          return // 空状态也是有效的
        }
      }

      // 如果既没有表格也没有空状态，检查页面基本内容
      const pageContent = page.locator('.v-main, .main-content, body')
      await expect(pageContent.first()).toBeVisible()
      return
    }

    await expect(table).toBeVisible()

    // 检查表格行数
    const rowSelectors = [
      `${SELECTORS.ORDERS.TABLE} tbody tr`,
      '.v-data-table tbody tr',
      'table tbody tr',
      '[data-testid="table-row"]'
    ]

    let rowCount = 0
    for (const selector of rowSelectors) {
      const rows = page.locator(selector)
      rowCount = await rows.count()
      if (rowCount > 0) {
        console.log(`✅ 找到 ${rowCount} 行数据: ${selector}`)
        break
      }
    }

    // 如果没有数据行，检查是否有加载状态或错误状态
    if (rowCount === 0) {
      console.log('⚠️ 表格无数据，检查加载或错误状态')

      // 检查加载状态
      const loadingSelectors = [
        SELECTORS.ORDERS.LOADING_INDICATOR,
        '.v-progress-circular',
        '.loading',
        '.skeleton'
      ]

      for (const selector of loadingSelectors) {
        const element = page.locator(selector).first()
        if (await element.isVisible({ timeout: 1000 }).catch(() => false)) {
          console.log(`✅ 发现加载状态: ${selector}`)
          return // 加载状态是可接受的
        }
      }

      // 检查是否有表格但显示空状态消息
      const emptyRowSelectors = [
        'tbody tr td:has-text("暂无数据")',
        'tbody tr td:has-text("No data")',
        'tbody tr.empty-row',
        'tbody tr[data-testid="empty-row"]'
      ]

      for (const selector of emptyRowSelectors) {
        const element = page.locator(selector).first()
        if (await element.isVisible({ timeout: 1000 }).catch(() => false)) {
          console.log(`✅ 发现空数据行: ${selector}`)
          return // 空数据行也是有效的
        }
      }

      console.log('ℹ️ 表格存在但无数据，这可能是正常状态')
    } else {
      expect(rowCount).toBeGreaterThan(0)
    }
  }

  /**
   * Assert error message is displayed
   */
  static async assertErrorDisplayed(page) {
    // 尝试多种错误消息选择器
    const errorSelectors = [
      SELECTORS.ORDERS.ERROR_MESSAGE,
      '.v-alert--error',
      '.v-alert[type="error"]',
      '.error-message',
      '.error-banner',
      '.v-snackbar--error',
      '[role="alert"]',
      'text=错误',
      'text=Error',
      'text=失败',
      'text=Failed'
    ]

    let errorFound = false
    for (const selector of errorSelectors) {
      const errorElement = page.locator(selector).first()
      if (await errorElement.isVisible({ timeout: 3000 }).catch(() => false)) {
        errorFound = true
        console.log(`✅ 找到错误消息: ${selector}`)
        break
      }
    }

    if (!errorFound) {
      // 如果没有找到错误消息，检查页面是否至少还能正常显示
      console.log('⚠️ 未找到错误消息，检查页面基本状态')
      const pageContent = page.locator('.v-main, .main-content, body')
      await expect(pageContent.first()).toBeVisible()

      // 检查是否有网络错误或其他问题指示器
      const networkErrorIndicators = [
        'text=网络错误',
        'text=连接失败',
        'text=请求失败',
        'text=服务不可用',
        '.network-error',
        '.connection-error'
      ]

      for (const indicator of networkErrorIndicators) {
        const element = page.locator(indicator).first()
        if (await element.isVisible({ timeout: 1000 }).catch(() => false)) {
          console.log(`✅ 找到网络错误指示器: ${indicator}`)
          errorFound = true
          break
        }
      }
    }

    // 如果仍然没有找到错误，这可能意味着错误处理是静默的或使用了不同的方式
    if (!errorFound) {
      console.log('ℹ️ 未找到明显的错误消息，可能使用了静默错误处理')
    }
  }

  /**
   * Assert loading state
   */
  static async assertLoadingState(page) {
    const loadingElement = page.locator(SELECTORS.ORDERS.LOADING_INDICATOR).first()
    await expect(loadingElement).toBeVisible()
  }
}

/**
 * Test data helpers
 */
export class DataHelpers {
  /**
   * Generate random order data
   */
  static generateRandomOrder(overrides = {}) {
    return {
      id: `order-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
      user_id: 1,
      client_order_id: `CLIENT-${Date.now()}`,
      symbol: 'BTC/USDT',
      side: 'BUY',
      quantity: Math.random() * 10,
      status: 'ACTIVE',
      entry_price: Math.random() * 50000,
      created_at: new Date().toISOString(),
      ...overrides
    }
  }

  /**
   * Generate multiple orders
   */
  static generateMultipleOrders(count = 10, overrides = {}) {
    return Array.from({ length: count }, (_, i) =>
      this.generateRandomOrder({
        id: `order-${i}`,
        client_order_id: `CLIENT-${i}`,
        ...overrides
      })
    )
  }
}

/**
 * Enhanced UI interaction helpers with improved reliability
 */
export class UIHelpers {
  /**
   * Navigate to URL with retry mechanism
   */
  static async navigateWithRetry(page, url, maxRetries = 3) {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`Navigation attempt ${attempt}/${maxRetries} to ${url}`)

        // Try different wait strategies based on attempt
        let waitUntil = 'load'
        let timeout = TIMEOUTS.NAVIGATION

        if (attempt === 1) {
          waitUntil = 'networkidle'
          timeout = TIMEOUTS.NAVIGATION
        } else if (attempt === 2) {
          waitUntil = 'domcontentloaded'
          timeout = TIMEOUTS.NAVIGATION * 0.8
        } else {
          waitUntil = 'load'
          timeout = TIMEOUTS.NAVIGATION * 0.6
        }

        await page.goto(url, {
          timeout: timeout,
          waitUntil: waitUntil
        })

        // Verify page loaded correctly
        await page.waitForTimeout(1000)

        // Check if page is accessible
        const title = await page.title().catch(() => '')
        const bodyVisible = await page.locator('body').isVisible().catch(() => false)

        if (title || bodyVisible) {
          console.log(`✓ Navigation successful on attempt ${attempt}`)
          return
        } else {
          throw new Error('Page did not load correctly - no title or body')
        }
      } catch (error) {
        console.log(`Navigation attempt ${attempt} failed: ${error.message}`)

        if (attempt === maxRetries) {
          // Try one last time with minimal requirements
          try {
            console.log('Final attempt with minimal requirements...')
            await page.goto(url, { waitUntil: 'commit', timeout: 10000 })
            try {
              await page.waitForTimeout(3000)
            } catch (timeoutError) {
              console.log('⚠️ 最终尝试中的等待超时，但继续执行')
            }
            console.log('✓ Final navigation attempt succeeded')
            return
          } catch (finalError) {
            throw new Error(`Failed to navigate to ${url} after ${maxRetries} attempts. Last error: ${error.message}`)
          }
        }

        // Progressive delay before retry
        try {
          await page.waitForTimeout(1000 * attempt)
        } catch (timeoutError) {
          // Page might be closed, skip delay
          console.log(`⚠️ 无法等待重试延迟: ${timeoutError.message}`)
        }
      }
    }
  }

  /**
   * Wait for page to be ready (DOM loaded + no loading indicators)
   */
  static async waitForPageReady(page) {
    // Wait for DOM to be ready
    await page.waitForLoadState('domcontentloaded')

    // Wait for any loading indicators to disappear
    try {
      await page.waitForSelector(SELECTORS.ORDERS.LOADING_INDICATOR, {
        state: 'hidden',
        timeout: TIMEOUTS.MEDIUM
      })
    } catch (error) {
      // Loading indicator might not exist, which is fine
    }

    // Additional wait for dynamic content
    await page.waitForTimeout(500)
  }

  /**
   * Click element with fallback selectors and overlay handling
   */
  static async clickWithFallback(page, selectorString, options = {}) {
    const selectors = selectorString.split(', ')

    for (const selector of selectors) {
      try {
        const element = page.locator(selector.trim()).first()
        await element.waitFor({ state: 'visible', timeout: TIMEOUTS.ELEMENT_WAIT })

        // Check for and dismiss any overlays that might block the click
        await this.dismissOverlays(page)

        await element.click(options)
        return
      } catch (error) {
        console.log(`Selector "${selector.trim()}" failed: ${error.message}`)
        continue
      }
    }

    throw new Error(`All selectors failed for: ${selectorString}`)
  }

  /**
   * Dismiss any blocking overlays
   */
  static async dismissOverlays(page) {
    console.log('🔄 Attempting to dismiss overlays...')

    // Wait for any transitions to complete
    await page.waitForTimeout(1500)

    const overlaySelectors = [
      '.v-overlay__scrim',
      '.v-dialog .v-overlay__scrim',
      '.fade-transition-enter-active',
      '.fade-transition-enter-to',
      '.modal-backdrop',
      '.overlay'
    ]

    let overlaysFound = false

    for (const selector of overlaySelectors) {
      try {
        const overlays = page.locator(selector)
        const count = await overlays.count()

        if (count > 0) {
          overlaysFound = true
          console.log(`Found ${count} overlays with selector: ${selector}`)
        }

        for (let i = 0; i < count; i++) {
          const overlay = overlays.nth(i)
          if (await overlay.isVisible().catch(() => false)) {
            console.log(`Dismissing overlay ${i + 1}/${count}`)

            // Method 1: Press Escape multiple times
            for (let escAttempt = 0; escAttempt < 3; escAttempt++) {
              await page.keyboard.press('Escape')
              await page.waitForTimeout(200)
            }

            // Method 2: Click outside at multiple positions
            const positions = [
              { x: 10, y: 10 },
              { x: 50, y: 50 },
              { x: 100, y: 100 }
            ]

            for (const pos of positions) {
              try {
                await page.click('body', { position: pos, force: true })
                await page.waitForTimeout(200)
              } catch (e) {}
            }

            // Method 3: Try to find and click close buttons
            const closeSelectors = [
              '.v-dialog__close',
              '.close',
              '[aria-label*="关闭"]',
              '[aria-label*="close"]',
              '[aria-label*="Close"]',
              'button[aria-label="close"]',
              '.v-btn--icon[aria-label*="close"]'
            ]

            for (const closeSelector of closeSelectors) {
              try {
                const closeButtons = page.locator(closeSelector)
                const closeCount = await closeButtons.count()
                if (closeCount > 0) {
                  await closeButtons.first().click({ force: true })
                  await page.waitForTimeout(300)
                  console.log(`Clicked close button: ${closeSelector}`)
                }
              } catch (e) {}
            }

            // Method 4: Force click the overlay itself as last resort
            try {
              await overlay.click({ force: true })
              await page.waitForTimeout(300)
            } catch (e) {}
          }
        }
      } catch (error) {
        // Ignore errors when trying to dismiss overlays
        continue
      }
    }

    if (overlaysFound) {
      console.log('✓ Overlay dismissal attempts completed')
      // Extra wait for animations to complete
      await page.waitForTimeout(1000)
    } else {
      console.log('ℹ No overlays found to dismiss')
    }
  }

  /**
   * Aggressively dismiss overlays using multiple strategies
   */
  static async dismissOverlaysAggressively(page) {
    console.log('🔄 Aggressive overlay dismissal started...')

    // Strategy 1: JavaScript removal
    await page.evaluate(() => {
      const overlaySelectors = [
        '.v-overlay__scrim',
        '.v-overlay',
        '.v-dialog .v-overlay__scrim',
        '.modal-backdrop',
        '.overlay',
        '.v-overlay-container'
      ]

      overlaySelectors.forEach(selector => {
        const elements = document.querySelectorAll(selector)
        elements.forEach(el => {
          el.style.display = 'none'
          el.style.pointerEvents = 'none'
          el.style.visibility = 'hidden'
          el.style.opacity = '0'
          el.remove()
        })
      })
    })

    // Strategy 2: Multiple escape key presses
    for (let i = 0; i < 5; i++) {
      await page.keyboard.press('Escape')
      await page.waitForTimeout(100)
    }

    // Strategy 3: Click outside at multiple positions
    const positions = [
      { x: 10, y: 10 },
      { x: 50, y: 50 },
      { x: 100, y: 100 },
      { x: 200, y: 200 }
    ]

    for (const pos of positions) {
      try {
        await page.click('body', { position: pos, force: true })
        await page.waitForTimeout(100)
      } catch (e) {}
    }

    console.log('✓ Aggressive overlay dismissal completed')
    await page.waitForTimeout(1000)
  }

  /**
   * Fill input with fallback selectors - Enhanced for Vuetify compatibility
   */
  static async fillWithFallback(page, selectorString, value, options = {}) {
    // Use the enhanced Vuetify input filling method
    return await UIHelpers.fillVuetifyInput(page, selectorString, value, options)
  }

  /**
   * Wait for element with fallback selectors
   */
  static async waitForElementWithFallback(page, selectorString, options = {}) {
    const selectors = selectorString.split(', ')

    for (const selector of selectors) {
      try {
        const element = page.locator(selector.trim()).first()
        await element.waitFor({
          state: options.state || 'visible',
          timeout: options.timeout || TIMEOUTS.ELEMENT_WAIT
        })
        return element
      } catch (error) {
        console.log(`Selector "${selector.trim()}" failed: ${error.message}`)
        continue
      }
    }

    throw new Error(`All selectors failed for: ${selectorString}`)
  }

  /**
   * Fill Vuetify input field - handles both direct input and v-text-field wrapper
   */
  static async fillVuetifyInput(page, selectorString, value, options = {}) {
    console.log(`🔤 Attempting to fill input with value: "${value}"`)
    const selectors = selectorString.split(', ')

    for (const selector of selectors) {
      try {
        console.log(`Trying selector: ${selector.trim()}`)

        // Strategy 1: Try to find the actual input element within the Vuetify component
        if (selector.includes('data-testid')) {
          const wrapper = page.locator(selector.trim()).first()
          await wrapper.waitFor({ state: 'visible', timeout: TIMEOUTS.ELEMENT_WAIT })

          // Try multiple approaches to find the input
          const inputStrategies = [
            wrapper.locator('input').first(),
            wrapper.locator('textarea').first(),
            wrapper.locator('[contenteditable]').first(),
            wrapper.locator('input[type="text"]').first(),
            wrapper.locator('input[type="password"]').first()
          ]

          for (const inputElement of inputStrategies) {
            try {
              if (await inputElement.isVisible().catch(() => false)) {
                console.log(`✓ Found input element using strategy`)
                await inputElement.fill(value, options)
                console.log(`✓ Successfully filled input with: "${value}"`)
                return
              }
            } catch (e) {
              continue
            }
          }

          // Strategy 2: Try clicking the wrapper and then typing
          try {
            await wrapper.click()
            await page.waitForTimeout(500)

            // Clear existing content
            await page.keyboard.press('Control+a')
            await page.waitForTimeout(100)

            // Type the new value
            await page.keyboard.type(value)
            console.log(`✓ Successfully typed value using keyboard: "${value}"`)
            return
          } catch (e) {
            console.log(`Keyboard typing failed: ${e.message}`)
          }
        } else {
          // Strategy 3: For non-data-testid selectors, use them directly
          const inputElement = page.locator(selector.trim()).first()
          await inputElement.waitFor({ state: 'visible', timeout: TIMEOUTS.ELEMENT_WAIT })
          await inputElement.fill(value, options)
          console.log(`✓ Successfully filled direct selector: "${value}"`)
          return
        }
      } catch (error) {
        console.log(`Vuetify input selector "${selector.trim()}" failed: ${error.message}`)
        continue
      }
    }

    throw new Error(`All Vuetify input selectors failed for: ${selectorString}. Tried selectors: ${selectors.join(', ')}`)
  }

  /**
   * Set viewport size with common presets
   */
  static async setViewport(page, viewport) {
    if (typeof viewport === 'object' && viewport.width && viewport.height) {
      await page.setViewportSize(viewport)
    } else if (typeof viewport === 'string') {
      // Handle preset viewport names
      const presets = {
        mobile: { width: 375, height: 667 },
        tablet: { width: 768, height: 1024 },
        desktop: { width: 1920, height: 1080 }
      }
      const preset = presets[viewport.toLowerCase()]
      if (preset) {
        await page.setViewportSize(preset)
      } else {
        throw new Error(`Unknown viewport preset: ${viewport}`)
      }
    }

    // Allow layout to settle
    await page.waitForTimeout(1000)
  }

  /**
   * Wait for loading indicators to disappear
   */
  static async waitForLoadingComplete(page, timeout = TIMEOUTS.MEDIUM) {
    const loadingSelectors = [
      '.v-progress-circular',
      '.v-progress-linear',
      '.loading',
      '.spinner',
      '.v-skeleton-loader',
      '[data-testid="loading"]',
      '[aria-label*="加载"]'
    ]

    // Wait for any loading indicators to disappear
    for (const selector of loadingSelectors) {
      try {
        const element = page.locator(selector).first()
        if (await element.isVisible().catch(() => false)) {
          await element.waitFor({ state: 'hidden', timeout })
        }
      } catch (error) {
        // Ignore timeout errors for loading indicators
        continue
      }
    }

    // Additional wait for any pending network requests
    await page.waitForTimeout(500)
  }

  /**
   * Force click an element even if overlays are blocking it
   */
  static async forceClick(page, selector, options = {}) {
    console.log(`🎯 Force clicking: ${selector}`)
    const element = page.locator(selector).first()
    await element.waitFor({ state: 'visible', timeout: TIMEOUTS.ELEMENT_WAIT })

    // Wait for any animations/transitions to complete
    await page.waitForTimeout(1000)

    // Strategy 1: Normal click
    try {
      await element.click({ ...options, timeout: 3000 })
      console.log(`✓ Normal click succeeded`)
      return
    } catch (error) {
      console.log(`Normal click failed: ${error.message}`)
    }

    // Strategy 2: Aggressive overlay dismissal
    console.log(`🔄 Attempting aggressive overlay dismissal...`)
    await this.dismissOverlaysAggressively(page)

    try {
      await element.click({ ...options, timeout: 3000 })
      console.log(`✓ Click after aggressive overlay dismissal succeeded`)
      return
    } catch (error) {
      console.log(`Click after dismissing overlays failed: ${error.message}`)
    }

    // Strategy 3: JavaScript click (bypasses overlay)
    try {
      await element.evaluate(el => {
        el.scrollIntoView({ behavior: 'instant', block: 'center' })
        el.click()
      })
      console.log(`✓ JavaScript click succeeded`)
      return
    } catch (error) {
      console.log(`JavaScript click failed: ${error.message}`)
    }

    // Strategy 4: Remove overlays and coordinate click
    try {
      // Remove all overlays using JavaScript
      await page.evaluate(() => {
        const overlays = document.querySelectorAll('.v-overlay__scrim, .v-overlay, .modal-backdrop, .v-overlay-container')
        overlays.forEach(overlay => {
          overlay.style.display = 'none'
          overlay.style.pointerEvents = 'none'
        })
      })

      const box = await element.boundingBox()
      if (box) {
        await page.mouse.click(box.x + box.width / 2, box.y + box.height / 2)
        console.log(`✓ Coordinate click after overlay removal succeeded`)
        return
      }
    } catch (error) {
      console.log(`Coordinate click failed: ${error.message}`)
    }

    // Strategy 5: Force click as last resort
    try {
      await element.click({ ...options, force: true, timeout: 3000 })
      console.log(`✓ Force click succeeded`)
      return
    } catch (error) {
      console.log(`Force click failed: ${error.message}`)
      throw new Error(`All force click strategies failed for: ${selector}. Last error: ${error.message}`)
    }
  }
}
