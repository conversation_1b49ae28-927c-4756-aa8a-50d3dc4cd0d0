/**
 * Test Data Fixtures
 * Centralized test data for consistent testing across all test suites
 */

// Demo user credentials
export const DEMO_CREDENTIALS = {
  username: 'demo',
  password: 'password123'
}

// API endpoints
export const API_ENDPOINTS = {
  BASE_URL: process.env.API_BASE_URL || 'http://localhost:8000',
  FRONTEND_URL: process.env.FRONTEND_URL || 'http://localhost:5173',
  AUTH: {
    LOGIN: '/api/v1/auth/login',
    REGISTER: '/api/v1/auth/register',
    ME: '/api/v1/auth/me'
  },
  ORDERS: '/api/v1/orders',
  PENDING_ACTIONS: '/api/v1/pending-actions',
  CONFIGS: '/api/v1/configs',
  HEALTH: '/health'
}

// Mock order data
export const MOCK_ORDERS = {
  SINGLE_ORDER: {
    id: 'test-order-1',
    user_id: 1,
    client_order_id: 'CLIENT-001',
    symbol: 'BTC/USDT',
    side: 'BUY',
    quantity: 0.001,
    status: 'ACTIVE',
    entry_price: 50000.0,
    created_at: '2024-01-01T10:00:00Z',
    updated_at: '2024-01-01T10:00:00Z'
  },
  
  MULTIPLE_ORDERS: [
    {
      id: 'test-order-1',
      user_id: 1,
      client_order_id: 'CLIENT-001',
      symbol: 'BTC/USDT',
      side: 'BUY',
      quantity: 0.001,
      status: 'ACTIVE',
      entry_price: 50000.0,
      created_at: '2024-01-01T10:00:00Z'
    },
    {
      id: 'test-order-2',
      user_id: 1,
      client_order_id: 'CLIENT-002',
      symbol: 'ETH/USDT',
      side: 'SELL',
      quantity: 0.1,
      status: 'FILLED',
      entry_price: 3000.0,
      created_at: '2024-01-01T11:00:00Z'
    },
    {
      id: 'test-order-3',
      user_id: 1,
      client_order_id: 'CLIENT-003',
      symbol: 'ADA/USDT',
      side: 'BUY',
      quantity: 100,
      status: 'CANCELLED',
      entry_price: 0.5,
      created_at: '2024-01-01T12:00:00Z'
    }
  ],

  LARGE_DATASET: Array.from({ length: 1000 }, (_, i) => ({
    id: `order-${i}`,
    user_id: 1,
    client_order_id: `CLIENT-${i}`,
    symbol: ['BTC/USDT', 'ETH/USDT', 'ADA/USDT'][i % 3],
    side: i % 2 === 0 ? 'BUY' : 'SELL',
    quantity: Math.random() * 10,
    status: ['ACTIVE', 'FILLED', 'CANCELLED', 'PENDING'][i % 4],
    entry_price: Math.random() * 50000,
    created_at: new Date(Date.now() - i * 60000).toISOString()
  })),

  EDGE_CASE_DATA: {
    NULL_VALUES: {
      id: 'test-order-null',
      user_id: 1,
      client_order_id: null,
      symbol: 'BTC/USDT',
      side: 'BUY',
      quantity: null,
      status: undefined,
      entry_price: null,
      created_at: '2024-01-01T10:00:00Z'
    },
    
    SPECIAL_CHARACTERS: {
      id: 'test-order-special',
      user_id: 1,
      client_order_id: 'CLIENT-<script>alert("xss")</script>',
      symbol: 'BTC/USDT',
      side: 'BUY',
      quantity: 0.001,
      status: 'ACTIVE',
      entry_price: 50000.0,
      created_at: '2024-01-01T10:00:00Z',
      notes: '特殊字符测试: !@#$%^&*()_+{}|:"<>?[]\\;\',./'
    },
    
    INVALID_DATES: {
      id: 'test-order-invalid-date',
      user_id: 1,
      client_order_id: 'CLIENT-001',
      symbol: 'BTC/USDT',
      side: 'BUY',
      quantity: 0.001,
      status: 'ACTIVE',
      entry_price: 50000.0,
      created_at: 'invalid-date-format',
      closed_at: '2024-13-45T25:70:90Z'
    }
  }
}

// Mock API responses
export const MOCK_RESPONSES = {
  LOGIN_SUCCESS: {
    access_token: 'mock-jwt-token-12345',
    token_type: 'bearer',
    expires_in: 3600
  },
  
  LOGIN_ERROR: {
    success: false,
    error: {
      code: 'INVALID_CREDENTIALS',
      message: 'Invalid username or password'
    }
  },
  
  ORDERS_LIST_SUCCESS: {
    orders: MOCK_ORDERS.MULTIPLE_ORDERS,
    total: 3,
    limit: 100,
    offset: 0
  },
  
  ORDERS_EMPTY: {
    orders: [],
    total: 0,
    limit: 100,
    offset: 0
  },
  
  SERVER_ERROR: {
    success: false,
    error: {
      code: 'INTERNAL_SERVER_ERROR',
      message: 'Internal server error'
    }
  },
  
  NETWORK_ERROR: {
    success: false,
    error: {
      code: 'NETWORK_ERROR',
      message: 'Network connection failed'
    }
  }
}

// Test selectors - Optimized for stability and reliability
export const SELECTORS = {
  AUTH: {
    // Enhanced selectors with Vuetify support and more fallback options
    USERNAME_INPUT: '[data-testid="username-input"], [data-testid="username-input"] input, input[autocomplete="username"], input[name="username"], input[placeholder*="用户名"], .v-text-field input[type="text"]',
    PASSWORD_INPUT: '[data-testid="password-input"], [data-testid="password-input"] input, input[autocomplete="current-password"], input[name="password"], input[placeholder*="密码"], .v-text-field input[type="password"]',
    LOGIN_BUTTON: '[data-testid="login-button"], button[type="submit"], button:has-text("登录"), .login-btn, .btn-primary, button.primary',
    LOGOUT_BUTTON: '[data-testid="logout-button"], button:has-text("退出登录"), button:has-text("退出"), [aria-label="退出登录"], .logout-btn',
    ERROR_MESSAGE: '[data-testid="error-message"], .text-error, .error-message, [role="alert"], .alert-danger, .v-alert--error, .v-alert[type="error"], .error, .validation-error, .v-messages--active .v-messages__message, .error-boundary, .auth-error, .unauthorized',
    SUCCESS_MESSAGE: '[data-testid="success-message"], .success-message, .text-success, .alert-success, .v-alert--success, .v-alert[type="success"], .success',
    LOADING_INDICATOR: '[data-testid="loading"], .loading, .v-progress-circular, .v-progress-linear, .spinner, .v-skeleton-loader, .v-data-table--loading',
    PASSWORD_TOGGLE: '[data-testid="password-toggle"], [aria-label*="显示密码"], .mdi-eye-off, .mdi-eye, .password-toggle'
  },

  NAVIGATION: {
    // Enhanced navigation selectors with more fallbacks
    DASHBOARD_LINK: '[data-testid="dashboard-link"], a[href="/dashboard"], nav a:has-text("仪表板"), a:has-text("首页"), .nav-dashboard',
    ORDERS_LINK: '[data-testid="orders-link"], a[href="/orders"], nav a:has-text("订单"), a:has-text("交易"), .nav-orders',
    CONFIGS_LINK: '[data-testid="configs-link"], a[href*="config"], nav a:has-text("配置"), a:has-text("设置"), .nav-configs',
    USER_MENU: '[data-testid="user-menu-button"], [data-testid="user-menu"], [aria-label="用户菜单"], .user-menu, .v-avatar, .user-dropdown, .profile-menu',
    MENU_TOGGLE: '[data-testid="menu-toggle"], .menu-toggle, .hamburger, [aria-label="菜单"]'
  },
  
  ORDERS: {
    // Enhanced table selectors with comprehensive fallbacks - more specific to avoid conflicts
    TABLE: '[data-testid="orders-table"]',
    TABLE_HEADERS: '[data-testid="table-header"], th, [role="columnheader"], .v-data-table-header th',
    TABLE_ROWS: '[data-testid="table-row"], tr, [role="row"], .v-data-table tbody tr',
    CREATE_ORDER_BUTTON: '[data-testid="create-order-button"], button:has-text("创建订单"), .create-order-btn, button.primary',
    REFRESH_BUTTON: '[data-testid="refresh-button"], button[aria-label*="刷新"], button:has-text("刷新"), .refresh-btn',
    PAGINATION: '[data-testid="pagination"], .v-pagination, [role="navigation"], .pagination',
    LOADING_INDICATOR: '[data-testid="loading"], [aria-label*="加载"], .v-progress-circular, .loading, .spinner, .v-skeleton-loader',
    ERROR_MESSAGE: '[data-testid="error-message"], [role="alert"], .error, .v-alert--error, .alert-danger',
    EMPTY_STATE: '[data-testid="empty-state"], [aria-label*="暂无数据"], text=暂无订单数据, .empty-state, .no-data',
    DETAILS_BUTTON: '[data-testid="details-button"], button[aria-label*="详情"], button:has-text("详情"), .details-btn',
    EXPORT_BUTTON: '[data-testid="export-button"], button[aria-label*="导出"], button:has-text("导出"), .export-btn',
    CANCEL_ORDER_BUTTON: '[data-testid="cancel-order-button"], button:has-text("取消订单"), .cancel-order-btn'
  },

  FILTERS: {
    // Enhanced filter selectors with more options
    STATUS_FILTER: '[data-testid="status-filter"], select[name="status"], .status-filter, .v-select[label*="状态"]',
    SYMBOL_FILTER: '[data-testid="symbol-filter"], select[name="symbol"], .symbol-filter, .v-select[label*="交易对"]',
    SEARCH_INPUT: '[data-testid="search-input"], input[type="search"], input[placeholder*="搜索"], .search-input, .v-text-field input',
    DATE_FILTER: '[data-testid="date-filter"], input[type="date"], .date-filter, .v-date-picker',
    CLEAR_FILTERS: '[data-testid="clear-filters"], button:has-text("清除"), .clear-filters-btn'
  },

  DIALOGS: {
    // Enhanced dialog selectors with comprehensive fallbacks
    MODAL: '[data-testid="modal"], [role="dialog"], .v-dialog, .modal, .dialog-container',
    OVERLAY: '[data-testid="overlay"], .v-overlay, .modal-backdrop, .dialog-overlay',
    CLOSE_BUTTON: '[data-testid="close-button"], button[aria-label*="关闭"], .v-dialog__close, .modal-close, .close-btn',
    CONFIRM_BUTTON: '[data-testid="confirm-button"], button[aria-label*="确认"], button:has-text("确认"), .confirm-btn, button.primary',
    CANCEL_BUTTON: '[data-testid="cancel-button"], button[aria-label*="取消"], button:has-text("取消"), .cancel-btn, button.secondary',
    SAVE_BUTTON: '[data-testid="save-button"], button:has-text("保存"), .save-btn',
    DELETE_BUTTON: '[data-testid="delete-button"], button:has-text("删除"), .delete-btn, button.danger'
  },

  FORMS: {
    // Form-specific selectors
    SYMBOL_INPUT: '[data-testid="symbol-input"], input[name="symbol"], .symbol-input',
    QUANTITY_INPUT: '[data-testid="quantity-input"], input[name="quantity"], .quantity-input, input[type="number"]',
    PRICE_INPUT: '[data-testid="price-input"], input[name="price"], .price-input',
    SIDE_SELECT: '[data-testid="side-select"], select[name="side"], .side-select',
    SUBMIT_BUTTON: '[data-testid="submit-button"], button[type="submit"], .submit-btn, button.primary'
  }
}

// Test timeouts - Optimized for reliability and stability
export const TIMEOUTS = {
  SHORT: 5000,        // Increased from 3s to 5s for better reliability
  MEDIUM: 12000,      // Increased from 8s to 12s for network delays
  LONG: 25000,        // Increased from 15s to 25s for heavy operations
  NAVIGATION: 45000,  // Increased from 20s to 45s for page loads
  API_REQUEST: 60000, // Increased timeout for API calls
  ELEMENT_WAIT: 10000 // Increased timeout for element visibility
}

// Browser viewport sizes
export const VIEWPORTS = {
  MOBILE: { width: 375, height: 667 },
  TABLET: { width: 768, height: 1024 },
  DESKTOP: { width: 1920, height: 1080 },
  SMALL_DESKTOP: { width: 1280, height: 720 }
}

// Error scenarios
export const ERROR_SCENARIOS = {
  HTTP_STATUS_CODES: [400, 401, 403, 404, 422, 500, 502, 503],
  NETWORK_CONDITIONS: ['offline', 'slow', 'failed'],
  MALFORMED_RESPONSES: [
    'invalid json response',
    '{"incomplete": json',
    '',
    null,
    undefined
  ]
}
