/**
 * useSnackbar 组合式函数测试
 *
 * 测试通知功能：
 * - 基本通知显示
 * - 不同类型的通知
 * - 通知配置选项
 * - 确认对话框
 * - 带操作按钮的通知
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { useSnackbar } from '@/composables/useSnackbar'

describe('useSnackbar', () => {
  let snackbar

  beforeEach(() => {
    // 重置状态
    snackbar = useSnackbar()
    snackbar.hideSnackbar()
    vi.clearAllMocks()
  })

  it('应该正确初始化', () => {
    // Act
    const { snackbarState } = useSnackbar()

    // Assert
    expect(snackbarState).toBeDefined()
    expect(snackbarState.show).toBe(false)
    expect(snackbarState.message).toBe('')
    expect(snackbarState.color).toBe('info')
    expect(snackbarState.timeout).toBe(4000)
  })

  it('应该显示基本通知', () => {
    // Act
    snackbar.showSnackbar('测试消息', 'info')

    // Assert
    expect(snackbar.snackbarState.show).toBe(true)
    expect(snackbar.snackbarState.message).toBe('测试消息')
    expect(snackbar.snackbarState.color).toBe('info')
    expect(snackbar.snackbarState.timeout).toBe(4000)
  })

  it('应该显示成功通知', () => {
    // Act
    snackbar.showSuccess('操作成功')

    // Assert
    expect(snackbar.snackbarState.show).toBe(true)
    expect(snackbar.snackbarState.message).toBe('操作成功')
    expect(snackbar.snackbarState.color).toBe('success')
    expect(snackbar.snackbarState.timeout).toBe(4000)
  })

  it('应该显示错误通知', () => {
    // Act
    snackbar.showError('操作失败')

    // Assert
    expect(snackbar.snackbarState.show).toBe(true)
    expect(snackbar.snackbarState.message).toBe('操作失败')
    expect(snackbar.snackbarState.color).toBe('error')
    expect(snackbar.snackbarState.timeout).toBe(6000) // 错误消息显示时间更长
  })

  it('应该显示警告通知', () => {
    // Act
    snackbar.showWarning('警告信息')

    // Assert
    expect(snackbar.snackbarState.show).toBe(true)
    expect(snackbar.snackbarState.message).toBe('警告信息')
    expect(snackbar.snackbarState.color).toBe('warning')
    expect(snackbar.snackbarState.timeout).toBe(4000)
  })

  it('应该显示信息通知', () => {
    // Act
    snackbar.showInfo('提示信息')

    // Assert
    expect(snackbar.snackbarState.show).toBe(true)
    expect(snackbar.snackbarState.message).toBe('提示信息')
    expect(snackbar.snackbarState.color).toBe('info')
    expect(snackbar.snackbarState.timeout).toBe(4000)
  })

  it('应该隐藏通知', () => {
    // Arrange
    snackbar.showSuccess('测试消息')
    expect(snackbar.snackbarState.show).toBe(true)

    // Act
    snackbar.hideSnackbar()

    // Assert
    expect(snackbar.snackbarState.show).toBe(false)
  })

  it('应该支持自定义配置选项', () => {
    // Act
    snackbar.showSnackbar('自定义消息', 'primary', {
      timeout: 8000,
      multiLine: true,
      vertical: true
    })

    // Assert
    expect(snackbar.snackbarState.show).toBe(true)
    expect(snackbar.snackbarState.message).toBe('自定义消息')
    expect(snackbar.snackbarState.color).toBe('primary')
    expect(snackbar.snackbarState.timeout).toBe(8000)
    expect(snackbar.snackbarState.multiLine).toBe(true)
    expect(snackbar.snackbarState.vertical).toBe(true)
  })

  it('应该显示确认对话框', () => {
    // Arrange
    const onConfirm = vi.fn()

    // Act
    snackbar.showConfirm('确认删除吗？', onConfirm)

    // Assert
    expect(snackbar.snackbarState.show).toBe(true)
    expect(snackbar.snackbarState.message).toBe('确认删除吗？')
    expect(snackbar.snackbarState.timeout).toBe(0) // 不自动关闭
    expect(snackbar.snackbarState.actions).toHaveLength(2)

    // 检查操作按钮
    const actions = snackbar.snackbarState.actions
    expect(actions[0].text).toBe('取消')
    expect(actions[1].text).toBe('确定')
  })

  it('应该处理确认对话框的确定操作', () => {
    // Arrange
    const onConfirm = vi.fn()
    snackbar.showConfirm('确认删除吗？', onConfirm)

    // Act - 点击确定
    const confirmAction = snackbar.snackbarState.actions[1]
    confirmAction.onClick()

    // Assert
    expect(onConfirm).toHaveBeenCalled()
    expect(snackbar.snackbarState.show).toBe(false)
  })

  it('应该处理确认对话框的取消操作', () => {
    // Arrange
    const onConfirm = vi.fn()
    snackbar.showConfirm('确认删除吗？', onConfirm)

    // Act - 点击取消
    const cancelAction = snackbar.snackbarState.actions[0]
    cancelAction.onClick()

    // Assert
    expect(onConfirm).not.toHaveBeenCalled()
    expect(snackbar.snackbarState.show).toBe(false)
  })

  it('应该显示带操作按钮的通知', () => {
    // Arrange
    const action1 = { text: '重试', color: 'primary', onClick: vi.fn() }
    const action2 = { text: '忽略', color: 'grey', onClick: vi.fn() }

    // Act
    snackbar.showWithActions('操作失败', [action1, action2], { type: 'error' })

    // Assert
    expect(snackbar.snackbarState.show).toBe(true)
    expect(snackbar.snackbarState.message).toBe('操作失败')
    expect(snackbar.snackbarState.color).toBe('error')
    expect(snackbar.snackbarState.timeout).toBe(0) // 不自动关闭
    expect(snackbar.snackbarState.actions).toHaveLength(2)

    // 检查操作按钮
    const actions = snackbar.snackbarState.actions
    expect(actions[0].text).toBe('重试')
    expect(actions[0].color).toBe('primary')
    expect(actions[1].text).toBe('忽略')
    expect(actions[1].color).toBe('grey')
  })

  it('应该处理带操作按钮通知的点击事件', () => {
    // Arrange
    const onRetry = vi.fn()
    const action = { text: '重试', onClick: onRetry }
    snackbar.showWithActions('操作失败', [action])

    // Act - 点击重试按钮
    const retryAction = snackbar.snackbarState.actions[0]
    retryAction.onClick()

    // Assert
    expect(onRetry).toHaveBeenCalled()
    expect(snackbar.snackbarState.show).toBe(false) // 默认自动关闭
  })

  it('应该支持不自动关闭的操作按钮', () => {
    // Arrange
    const onAction = vi.fn()
    const action = { text: '操作', onClick: onAction, autoClose: false }
    snackbar.showWithActions('测试消息', [action])

    // Act - 点击操作按钮
    const actionButton = snackbar.snackbarState.actions[0]
    actionButton.onClick()

    // Assert
    expect(onAction).toHaveBeenCalled()
    expect(snackbar.snackbarState.show).toBe(true) // 不自动关闭
  })

  it('应该正确映射颜色', () => {
    // Test all color mappings
    const colorTests = [
      { input: 'success', expected: 'success' },
      { input: 'error', expected: 'error' },
      { input: 'warning', expected: 'warning' },
      { input: 'info', expected: 'info' },
      { input: 'primary', expected: 'primary' },
      { input: 'secondary', expected: 'secondary' },
      { input: 'unknown', expected: 'info' } // 默认值
    ]

    colorTests.forEach(({ input, expected }) => {
      snackbar.showSnackbar('测试', input)
      expect(snackbar.snackbarState.color).toBe(expected)
    })
  })

  it('应该支持多个实例共享状态', () => {
    // Arrange
    const snackbar1 = useSnackbar()
    const snackbar2 = useSnackbar()

    // Act
    snackbar1.showSuccess('测试消息')

    // Assert - 两个实例应该共享相同的状态
    expect(snackbar1.snackbarState.show).toBe(true)
    expect(snackbar2.snackbarState.show).toBe(true)
    expect(snackbar1.snackbarState.message).toBe('测试消息')
    expect(snackbar2.snackbarState.message).toBe('测试消息')
  })
})