/**
 * 并行测试全局清理
 * 清理并行测试产生的临时数据和资源
 */

import fs from 'fs'
import path from 'path'

async function parallelGlobalTeardown() {
  console.log('🧹 开始并行测试全局清理...')
  
  const startTime = Date.now()
  
  try {
    // 读取设置信息
    const tempDir = '../temp/frontend/playwright'
    const setupInfoPath = path.join(tempDir, 'parallel-setup-info.json')
    
    let setupInfo = {}
    if (fs.existsSync(setupInfoPath)) {
      setupInfo = JSON.parse(fs.readFileSync(setupInfoPath, 'utf8'))
      console.log(`📊 测试执行信息:`)
      console.log(`   - 设置时间: ${setupInfo.setupTime}ms`)
      console.log(`   - Worker数量: ${setupInfo.workerCount}`)
      console.log(`   - 环境: ${setupInfo.isDocker ? 'Docker' : '本地'}`)
    }
    
    // 清理临时文件
    await cleanupTempFiles()
    
    // 生成并行测试报告摘要
    await generateParallelTestSummary()
    
    // 清理测试数据（如果需要）
    await cleanupTestData()
    
    const cleanupTime = Date.now() - startTime
    console.log(`✅ 并行测试全局清理完成 (耗时: ${cleanupTime}ms)`)
    
  } catch (error) {
    console.error('❌ 并行测试清理失败:', error.message)
    // 清理失败不应该阻断测试流程
  }
}

async function cleanupTempFiles() {
  console.log('🗑️ 清理临时文件...')
  
  try {
    const tempDir = '../temp/frontend/playwright'
    
    if (fs.existsSync(tempDir)) {
      // 清理旧的截图和视频文件（保留最近的）
      const artifactsDir = path.join(tempDir, 'parallel-artifacts')
      if (fs.existsSync(artifactsDir)) {
        const files = fs.readdirSync(artifactsDir)
        const now = Date.now()
        const oneHourAgo = now - (60 * 60 * 1000) // 1小时前
        
        files.forEach(file => {
          const filePath = path.join(artifactsDir, file)
          const stats = fs.statSync(filePath)
          
          // 删除1小时前的文件
          if (stats.mtime.getTime() < oneHourAgo) {
            fs.unlinkSync(filePath)
          }
        })
      }
    }
    
    console.log('✅ 临时文件清理完成')
    
  } catch (error) {
    console.warn('⚠️ 临时文件清理失败:', error.message)
  }
}

async function generateParallelTestSummary() {
  console.log('📊 生成并行测试摘要...')
  
  try {
    const tempDir = '../temp/frontend/playwright'
    const resultsPath = path.join(tempDir, 'parallel-results.json')
    
    if (!fs.existsSync(resultsPath)) {
      console.log('⚠️ 未找到测试结果文件')
      return
    }
    
    const results = JSON.parse(fs.readFileSync(resultsPath, 'utf8'))
    
    const summary = {
      timestamp: new Date().toISOString(),
      totalTests: results.stats?.total || 0,
      passed: results.stats?.passed || 0,
      failed: results.stats?.failed || 0,
      skipped: results.stats?.skipped || 0,
      duration: results.stats?.duration || 0,
      parallelEfficiency: calculateParallelEfficiency(results),
      projectResults: summarizeProjectResults(results)
    }
    
    // 保存摘要
    const summaryPath = path.join(tempDir, 'parallel-test-summary.json')
    fs.writeFileSync(summaryPath, JSON.stringify(summary, null, 2))
    
    // 输出摘要到控制台
    console.log('📈 并行测试执行摘要:')
    console.log(`   - 总测试数: ${summary.totalTests}`)
    console.log(`   - 通过: ${summary.passed}`)
    console.log(`   - 失败: ${summary.failed}`)
    console.log(`   - 跳过: ${summary.skipped}`)
    console.log(`   - 总耗时: ${Math.round(summary.duration / 1000)}秒`)
    console.log(`   - 并行效率: ${summary.parallelEfficiency}%`)
    
    console.log('✅ 测试摘要生成完成')
    
  } catch (error) {
    console.warn('⚠️ 测试摘要生成失败:', error.message)
  }
}

function calculateParallelEfficiency(results) {
  try {
    // 计算并行效率：实际耗时 vs 串行预估耗时
    const totalDuration = results.stats?.duration || 0
    const testCount = results.stats?.total || 0
    
    if (testCount === 0) return 0
    
    // 假设平均每个测试30秒（串行执行）
    const estimatedSerialTime = testCount * 30000
    const efficiency = Math.round((estimatedSerialTime / totalDuration) * 100)
    
    return Math.min(efficiency, 100) // 最大100%
  } catch (error) {
    return 0
  }
}

function summarizeProjectResults(results) {
  try {
    const projects = {}
    
    if (results.suites) {
      results.suites.forEach(suite => {
        const projectName = suite.title || 'unknown'
        if (!projects[projectName]) {
          projects[projectName] = {
            total: 0,
            passed: 0,
            failed: 0,
            duration: 0
          }
        }
        
        // 这里需要根据实际的结果结构来解析
        // 简化处理
        projects[projectName].total += 1
      })
    }
    
    return projects
  } catch (error) {
    return {}
  }
}

async function cleanupTestData() {
  console.log('🗑️ 清理测试数据...')

  try {
    // 导入chromium用于API调用
    const { chromium } = await import('@playwright/test')

    const browser = await chromium.launch({ headless: true })
    const context = await browser.newContext()
    const page = await context.newPage()

    const backendUrl = process.env.BACKEND_URL || 'http://localhost:8000'

    // 清理每个测试组的数据
    const testGroups = ['api-fast', 'core-features', 'business-flows', 'system-tests', 'error-handling', 'ui-ux-tests']

    for (const group of testGroups) {
      await cleanupGroupTestData(page, backendUrl, group)
    }

    await browser.close()

    console.log('✅ 测试数据清理完成')

  } catch (error) {
    console.warn('⚠️ 测试数据清理失败:', error.message)
  }
}

async function cleanupGroupTestData(page, backendUrl, groupName) {
  try {
    // 使用对应的测试用户登录
    const userIndex = Math.abs(groupName.split('').reduce((a, b) => a + b.charCodeAt(0), 0)) % 6
    const testUsers = ['demo', 'test1', 'test2', 'test3', 'test4', 'test5']
    const username = testUsers[userIndex]

    const loginResponse = await page.request.post(`${backendUrl}/api/v1/auth/login`, {
      data: {
        username: username,
        password: 'password123'
      },
      timeout: 10000
    })

    if (!loginResponse.ok()) {
      console.warn(`⚠️ 用户 ${username} 登录失败，跳过数据清理`)
      return
    }

    const loginData = await loginResponse.json()
    const token = loginData.access_token

    // 清理测试数据（只清理带有test_group标记的数据）
    const cleanupEndpoints = [
      '/api/v1/orders',
      '/api/v1/signals',
      '/api/v1/config'
    ]

    for (const endpoint of cleanupEndpoints) {
      try {
        // 获取数据列表
        const listResponse = await page.request.get(`${backendUrl}${endpoint}`, {
          headers: { 'Authorization': `Bearer ${token}` },
          timeout: 5000
        })

        if (listResponse.ok()) {
          const data = await listResponse.json()
          const items = Array.isArray(data) ? data : (data.items || [])

          // 删除属于当前测试组的数据
          for (const item of items) {
            if (item.test_group === groupName) {
              await page.request.delete(`${backendUrl}${endpoint}/${item.id}`, {
                headers: { 'Authorization': `Bearer ${token}` },
                timeout: 5000
              })
            }
          }
        }
      } catch (error) {
        // 忽略清理错误，不影响整体流程
      }
    }

    console.log(`✅ 测试组 ${groupName} 数据清理完成`)

  } catch (error) {
    console.warn(`⚠️ 测试组 ${groupName} 数据清理失败:`, error.message)
  }
}

export default parallelGlobalTeardown