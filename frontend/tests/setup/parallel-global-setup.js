/**
 * 并行测试全局设置
 * 专门为并行执行优化的设置脚本
 */

import { chromium } from '@playwright/test'
import fs from 'fs'
import os from 'os'

async function parallelGlobalSetup() {
  console.log('🚀 启动并行测试全局设置...')
  
  const startTime = Date.now()
  const isDocker = process.env.DOCKER_ENV === 'true' || fs.existsSync('/.dockerenv')
  const workerCount = process.env.PLAYWRIGHT_WORKERS || 'auto'
  
  console.log(`📊 并行配置信息:`)
  console.log(`   - 环境: ${isDocker ? 'Docker容器' : '本地开发'}`)
  console.log(`   - Worker数量: ${workerCount}`)
  console.log(`   - CPU核心数: ${os.cpus().length}`)
  console.log(`   - 内存: ${Math.floor(os.totalmem() / (1024 * 1024 * 1024))}GB`)
  
  // 并行测试的浏览器启动选项
  const launchOptions = {
    headless: true,
    args: [
      '--no-sandbox',
      '--disable-setuid-sandbox',
      '--disable-dev-shm-usage',
      '--disable-gpu',
      '--disable-background-timer-throttling',
      '--disable-backgrounding-occluded-windows',
      '--disable-renderer-backgrounding',
      '--memory-pressure-off',
      '--max_old_space_size=4096'
    ]
  }
  
  if (isDocker) {
    launchOptions.executablePath = '/usr/bin/chromium-browser'
  }
  
  let browser, context, page
  
  try {
    console.log('🔍 检查服务可用性...')
    
    browser = await chromium.launch(launchOptions)
    context = await browser.newContext({
      // 并行测试时减少资源使用
      viewport: { width: 1280, height: 720 },
      ignoreHTTPSErrors: true,
      acceptDownloads: false
    })
    page = await context.newPage()
    
    // 检查后端服务
    const backendUrl = process.env.API_BASE_URL || 
                      (isDocker ? 'http://backend-test:8000' : 'http://localhost:8000')
    
    console.log(`🔍 检查后端服务: ${backendUrl}`)
    
    const healthCheck = await Promise.race([
      page.goto(`${backendUrl}/api/v1/health`, { 
        waitUntil: 'networkidle',
        timeout: 10000 
      }),
      new Promise((_, reject) => 
        setTimeout(() => reject(new Error('Health check timeout')), 15000)
      )
    ])
    
    if (!healthCheck.ok()) {
      throw new Error(`Backend health check failed: ${healthCheck.status()}`)
    }
    
    console.log('✅ 后端服务正常')
    
    // 检查前端服务
    const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:5173'
    console.log(`🔍 检查前端服务: ${frontendUrl}`)
    
    const frontendCheck = await Promise.race([
      page.goto(frontendUrl, { 
        waitUntil: 'domcontentloaded',
        timeout: 10000 
      }),
      new Promise((_, reject) => 
        setTimeout(() => reject(new Error('Frontend check timeout')), 15000)
      )
    ])
    
    if (!frontendCheck.ok()) {
      throw new Error(`Frontend check failed: ${frontendCheck.status()}`)
    }
    
    console.log('✅ 前端服务正常')
    
    // 并行测试数据准备
    await setupParallelTestData(page, backendUrl)
    
    const setupTime = Date.now() - startTime
    console.log(`✅ 并行测试全局设置完成 (耗时: ${setupTime}ms)`)
    
    // 保存设置信息供测试使用
    const setupInfo = {
      timestamp: new Date().toISOString(),
      setupTime,
      backendUrl,
      frontendUrl,
      workerCount,
      isDocker
    }
    
    const tempDir = '../temp/frontend/playwright'
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true })
    }
    
    fs.writeFileSync(
      `${tempDir}/parallel-setup-info.json`, 
      JSON.stringify(setupInfo, null, 2)
    )
    
  } catch (error) {
    console.error('❌ 并行测试全局设置失败:', error.message)
    console.error('')
    console.error('🔧 故障排除建议:')
    console.error('1. 检查服务是否启动:')
    console.error('   docker-compose -f docker-compose.test.yml ps')
    console.error('2. 重启测试服务:')
    console.error('   docker-compose -f docker-compose.test.yml restart')
    console.error('3. 检查端口占用:')
    console.error('   lsof -i :8000 && lsof -i :5173')
    console.error('')
    throw error
  } finally {
    if (context) await context.close()
    if (browser) await browser.close()
  }
}

async function setupParallelTestData(page, backendUrl) {
  console.log('📝 准备并行测试数据...')
  
  try {
    // 确保demo用户存在
    const loginResponse = await page.request.post(`${backendUrl}/api/v1/auth/login`, {
      data: {
        username: 'demo',
        password: 'password123'
      },
      timeout: 10000
    })
    
    if (!loginResponse.ok()) {
      console.log('🔧 创建demo用户...')
      await createDemoUser(page, backendUrl)
    } else {
      console.log('✅ Demo用户验证成功')
    }
    
    // 为并行测试创建隔离的测试数据
    await createIsolatedTestData(page, backendUrl)
    
  } catch (error) {
    console.warn('⚠️ 测试数据准备失败:', error.message)
    // 不阻断测试执行，只是警告
  }
}

async function createDemoUser(page, backendUrl) {
  console.log('🔧 创建测试用户...')

  // 创建多个测试用户，支持并行测试
  const testUsers = [
    { username: 'demo', password: 'password123', email: '<EMAIL>' },
    { username: 'test1', password: 'password123', email: '<EMAIL>' },
    { username: 'test2', password: 'password123', email: '<EMAIL>' },
    { username: 'test3', password: 'password123', email: '<EMAIL>' },
    { username: 'test4', password: 'password123', email: '<EMAIL>' },
    { username: 'test5', password: 'password123', email: '<EMAIL>' }
  ]

  let createdUsers = 0
  let existingUsers = 0

  for (const user of testUsers) {
    try {
      const response = await page.request.post(`${backendUrl}/api/v1/auth/register`, {
        data: user,
        timeout: 10000
      })

      if (response.ok()) {
        createdUsers++
        console.log(`✅ 用户 ${user.username} 创建成功`)
      } else {
        const error = await response.json()
        if (error.detail?.includes('already exists') || error.error?.code === 'USERNAME_EXISTS') {
          existingUsers++
          console.log(`ℹ️ 用户 ${user.username} 已存在，跳过创建`)
        } else {
          console.warn(`⚠️ 用户 ${user.username} 创建失败:`, error)
        }
      }
    } catch (error) {
      console.warn(`⚠️ 用户 ${user.username} 创建异常:`, error.message)
    }
  }

  console.log(`📊 用户创建统计: 新建 ${createdUsers} 个，已存在 ${existingUsers} 个`)
}

async function createIsolatedTestData(page, backendUrl) {
  console.log('🔧 创建隔离测试数据...')

  // 为每个测试组创建独立的测试数据
  const testGroups = ['api-fast', 'core-features', 'business-flows', 'system-tests', 'error-handling', 'ui-ux-tests']

  for (const group of testGroups) {
    try {
      await createGroupTestData(page, backendUrl, group)
    } catch (error) {
      console.warn(`⚠️ 测试组 ${group} 数据创建失败:`, error.message)
    }
  }
}

async function createGroupTestData(page, backendUrl, groupName) {
  console.log(`📦 为测试组 ${groupName} 创建数据...`)

  try {
    // 使用不同的测试用户登录
    const userIndex = Math.abs(groupName.split('').reduce((a, b) => a + b.charCodeAt(0), 0)) % 6
    const testUsers = ['demo', 'test1', 'test2', 'test3', 'test4', 'test5']
    const username = testUsers[userIndex]

    const loginResponse = await page.request.post(`${backendUrl}/api/v1/auth/login`, {
      data: {
        username: username,
        password: 'password123'
      },
      timeout: 10000
    })

    if (!loginResponse.ok()) {
      console.warn(`⚠️ 用户 ${username} 登录失败，跳过数据创建`)
      return
    }

    const loginData = await loginResponse.json()
    const token = loginData.access_token

    // 根据测试组类型创建相应的测试数据
    const testDataSets = getTestDataForGroup(groupName)

    for (const dataSet of testDataSets) {
      try {
        const checkResponse = await page.request.get(`${backendUrl}${dataSet.endpoint}`, {
          headers: { 'Authorization': `Bearer ${token}` },
          timeout: 5000
        })

        if (checkResponse.ok()) {
          const data = await checkResponse.json()
          if (data.total === 0 || (Array.isArray(data) && data.length === 0)) {
            console.log(`📝 为 ${groupName} 创建 ${dataSet.name} 测试数据...`)

            // 创建测试数据
            const createResponse = await page.request.post(`${backendUrl}${dataSet.endpoint}`, {
              headers: { 'Authorization': `Bearer ${token}` },
              data: dataSet.createData(groupName),
              timeout: 10000
            })

            if (createResponse.ok()) {
              console.log(`✅ ${dataSet.name} 数据创建成功`)
            } else {
              console.warn(`⚠️ ${dataSet.name} 数据创建失败`)
            }
          } else {
            console.log(`ℹ️ ${groupName} 的 ${dataSet.name} 数据已存在`)
          }
        }
      } catch (error) {
        console.warn(`⚠️ ${groupName} 的 ${dataSet.name} 数据处理失败:`, error.message)
      }
    }
  } catch (error) {
    console.warn(`⚠️ 测试组 ${groupName} 数据创建失败:`, error.message)
  }
}

function getTestDataForGroup(groupName) {
  const baseDataSets = [
    {
      name: 'orders',
      endpoint: '/api/v1/orders',
      createData: (group) => ({
        symbol: `${group.toUpperCase()}_${Date.now()}/USDT`,
        side: 'buy',
        quantity: 0.001,
        entry_price: 50000.0,
        test_group: group
      })
    }
  ]

  // 根据测试组添加特定的数据集
  switch (groupName) {
    case 'business-flows':
      baseDataSets.push({
        name: 'signals',
        endpoint: '/api/v1/signals',
        createData: (group) => ({
          name: `Test Signal ${group}`,
          description: `Test signal for ${group}`,
          test_group: group
        })
      })
      break
    case 'system-tests':
      baseDataSets.push({
        name: 'configs',
        endpoint: '/api/v1/config',
        createData: (group) => ({
          key: `test_config_${group}`,
          value: `test_value_${Date.now()}`,
          test_group: group
        })
      })
      break
  }

  return baseDataSets
}

export default parallelGlobalSetup