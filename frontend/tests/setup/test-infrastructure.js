/**
 * 测试基础设施优化配置
 * 解决Playwright fixture重用警告和测试清理机制
 */

import { test as base } from '@playwright/test'
import { createTestDataFactory } from '../api-unified/test-data-factory.js'

/**
 * 扩展的测试fixture，避免重用警告
 */
export const test = base.extend({
  // 为每个测试创建独立的testDataFactory
  testDataFactory: async ({ request }, use) => {
    const factory = createTestDataFactory(request)
    await use(factory)
    // 测试结束后自动清理（如果有认证token）
    if (factory.authToken) {
      try {
        await factory.smartCleanup(factory.authToken)
      } catch (error) {
        console.warn('⚠️ 自动清理失败:', error.message)
      }
    }
  },

  // 认证用户fixture
  authenticatedUser: async ({ testDataFactory }, use) => {
    const user = await testDataFactory.createRealUser({
      username: `test_user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      password: 'TestPassword123!'
    })
    
    const loginResult = await testDataFactory.loginRealUser({
      username: user.username,
      password: 'TestPassword123!'
    })
    
    // 将token存储到factory中以便自动清理
    testDataFactory.authToken = loginResult.token
    
    await use({
      user,
      token: loginResult.token,
      factory: testDataFactory
    })
  },

  // 页面fixture，带有优化的超时和错误处理
  optimizedPage: async ({ page }, use) => {
    // 设置更长的默认超时
    page.setDefaultTimeout(30000)
    page.setDefaultNavigationTimeout(30000)
    
    // 添加全局错误处理
    page.on('pageerror', (error) => {
      console.warn('⚠️ 页面错误:', error.message)
    })
    
    page.on('requestfailed', (request) => {
      console.warn('⚠️ 请求失败:', request.url(), request.failure()?.errorText)
    })
    
    await use(page)
  }
})

/**
 * 测试清理工具类
 */
export class TestCleanupManager {
  constructor() {
    this.cleanupTasks = []
  }

  /**
   * 注册清理任务
   */
  registerCleanup(task) {
    this.cleanupTasks.push(task)
  }

  /**
   * 执行所有清理任务
   */
  async executeCleanup() {
    const results = {
      success: [],
      failed: []
    }

    for (const task of this.cleanupTasks.reverse()) {
      try {
        await task()
        results.success.push('清理任务执行成功')
      } catch (error) {
        results.failed.push(`清理任务失败: ${error.message}`)
      }
    }

    this.cleanupTasks = []
    return results
  }
}

/**
 * 测试重试配置
 */
export const retryConfig = {
  // 网络相关错误重试
  shouldRetry: (error) => {
    const retryableErrors = [
      'TimeoutError',
      'NetworkError',
      'Connection refused',
      'ECONNRESET',
      'ENOTFOUND'
    ]
    
    return retryableErrors.some(errorType => 
      error.message.includes(errorType)
    )
  },

  // 重试延迟（指数退避）
  getRetryDelay: (attempt) => {
    return Math.min(1000 * Math.pow(2, attempt), 10000)
  }
}

/**
 * 测试环境检查工具
 */
export class TestEnvironmentChecker {
  static async checkServices() {
    const services = [
      { name: '后端API', url: 'http://localhost:8000/api/v1/health' },
      { name: '前端服务', url: 'http://localhost:5173' }
    ]

    const results = []
    
    for (const service of services) {
      try {
        const response = await fetch(service.url)
        results.push({
          name: service.name,
          status: response.ok ? 'available' : 'error',
          statusCode: response.status
        })
      } catch (error) {
        results.push({
          name: service.name,
          status: 'unavailable',
          error: error.message
        })
      }
    }

    return results
  }

  static logServiceStatus(results) {
    console.log('🔍 服务状态检查:')
    results.forEach(result => {
      const icon = result.status === 'available' ? '✅' : '❌'
      console.log(`${icon} ${result.name}: ${result.status}`)
      if (result.error) {
        console.log(`   错误: ${result.error}`)
      }
    })
  }
}

/**
 * 测试数据隔离工具
 */
export class TestDataIsolation {
  constructor() {
    this.testId = `test_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 生成唯一的测试用户名
   */
  generateUsername(prefix = 'test_user') {
    return `${prefix}_${this.testId}`
  }

  /**
   * 生成唯一的测试数据标识
   */
  generateTestId(prefix = 'test') {
    return `${prefix}_${this.testId}`
  }
}

export { expect } from '@playwright/test'
