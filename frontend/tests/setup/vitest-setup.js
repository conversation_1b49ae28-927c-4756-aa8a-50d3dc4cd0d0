import { vi, beforeEach } from 'vitest'
import { config } from '@vue/test-utils'
import { createVuetify } from 'vuetify'
import * as components from 'vuetify/components'
import * as directives from 'vuetify/directives'

// 创建 Vuetify 实例用于测试
const vuetify = createVuetify({
  components,
  directives,
})

// 全局配置 Vue Test Utils
config.global.plugins = [vuetify]

// Mock global objects
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(), // deprecated
    removeListener: vi.fn(), // deprecated
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
})

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
}
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
})

// Mock sessionStorage
const sessionStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
}
Object.defineProperty(window, 'sessionStorage', {
  value: sessionStorageMock
})

// Mock WebSocket
const MockWebSocket = vi.fn().mockImplementation(() => ({
  close: vi.fn(),
  send: vi.fn(),
  addEventListener: vi.fn(),
  removeEventListener: vi.fn(),
  readyState: 0, // CONNECTING
}))

// Add static properties
MockWebSocket.CONNECTING = 0
MockWebSocket.OPEN = 1
MockWebSocket.CLOSING = 2
MockWebSocket.CLOSED = 3

global.WebSocket = MockWebSocket

// Mock fetch
global.fetch = vi.fn()

// Mock ResizeObserver
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}))

// Mock visualViewport
Object.defineProperty(window, 'visualViewport', {
  writable: true,
  value: {
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    width: 1024,
    height: 768,
    offsetLeft: 0,
    offsetTop: 0,
    pageLeft: 0,
    pageTop: 0,
    scale: 1
  }
})

// Mock CSS imports
vi.mock('*.css', () => ({}))
vi.mock('*.scss', () => ({}))
vi.mock('*.sass', () => ({}))

// Polyfill for Array.prototype.toReversed (ES2023) for Node.js 18
if (!Array.prototype.toReversed) {
  Array.prototype.toReversed = function() {
    return [...this].reverse()
  }
}

// Mock API modules
vi.mock('@/api/signals', () => ({
  signalApi: {
    getSignals: vi.fn(),
    getStats: vi.fn(),
    createSignal: vi.fn(),
    updateSignal: vi.fn(),
    deleteSignal: vi.fn(),
    batchUpdateSignals: vi.fn(),
    searchSignals: vi.fn()
  }
}))

vi.mock('@/api/auth', () => ({
  authApi: {
    login: vi.fn(),
    logout: vi.fn(),
    getCurrentUser: vi.fn(),
    refreshToken: vi.fn()
  }
}))

vi.mock('@/api/discordConfig', () => ({
  getDiscordConfigs: vi.fn(),
  getDiscordConfig: vi.fn(),
  createDiscordConfig: vi.fn(),
  updateDiscordConfig: vi.fn(),
  deleteDiscordConfig: vi.fn()
}))

// Reset all mocks before each test
beforeEach(() => {
  vi.clearAllMocks()
})
