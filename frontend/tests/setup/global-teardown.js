/**
 * Global Test Teardown
 * Cleans up after all tests have completed
 */

import fs from 'fs'
import path from 'path'

async function globalTeardown() {
  console.log('🧹 Starting global test teardown...')
  
  try {
    // Generate test summary
    await generateTestSummary()
    
    // Clean up temporary files
    await cleanupTempFiles()
    
    // Archive test results if in CI
    if (process.env.CI) {
      await archiveTestResults()
    }
    
    console.log('✅ Global teardown completed successfully')
    
  } catch (error) {
    console.error('❌ Global teardown failed:', error.message)
  }
}

async function generateTestSummary() {
  try {
    const resultsPath = 'test-results/results.json'
    
    if (!fs.existsSync(resultsPath)) {
      console.log('📊 No test results found to summarize')
      return
    }
    
    const results = JSON.parse(fs.readFileSync(resultsPath, 'utf8'))
    
    const summary = {
      timestamp: new Date().toISOString(),
      total: results.stats?.total || 0,
      passed: results.stats?.passed || 0,
      failed: results.stats?.failed || 0,
      skipped: results.stats?.skipped || 0,
      duration: results.stats?.duration || 0,
      environment: {
        ci: !!process.env.CI,
        node_version: process.version,
        platform: process.platform
      },
      suites: results.suites?.map(suite => ({
        title: suite.title,
        tests: suite.tests?.length || 0,
        passed: suite.tests?.filter(t => t.outcome === 'passed').length || 0,
        failed: suite.tests?.filter(t => t.outcome === 'failed').length || 0
      })) || []
    }
    
    // Write summary
    const summaryPath = 'test-results/summary.json'
    fs.writeFileSync(summaryPath, JSON.stringify(summary, null, 2))
    
    // Generate markdown report
    const markdownReport = generateMarkdownReport(summary)
    fs.writeFileSync('test-results/summary.md', markdownReport)
    
    console.log('📊 Test summary generated')
    console.log(`   Total: ${summary.total}`)
    console.log(`   Passed: ${summary.passed}`)
    console.log(`   Failed: ${summary.failed}`)
    console.log(`   Duration: ${Math.round(summary.duration / 1000)}s`)
    
  } catch (error) {
    console.warn('⚠️  Could not generate test summary:', error.message)
  }
}

function generateMarkdownReport(summary) {
  const passRate = summary.total > 0 ? ((summary.passed / summary.total) * 100).toFixed(1) : 0
  const status = summary.failed === 0 ? '✅ PASSED' : '❌ FAILED'
  
  return `# Frontend Test Report

## Summary
- **Status**: ${status}
- **Total Tests**: ${summary.total}
- **Passed**: ${summary.passed}
- **Failed**: ${summary.failed}
- **Skipped**: ${summary.skipped}
- **Pass Rate**: ${passRate}%
- **Duration**: ${Math.round(summary.duration / 1000)}s
- **Timestamp**: ${summary.timestamp}

## Environment
- **CI**: ${summary.environment.ci}
- **Node Version**: ${summary.environment.node_version}
- **Platform**: ${summary.environment.platform}

## Test Suites
${summary.suites.map(suite => `
### ${suite.title}
- Tests: ${suite.tests}
- Passed: ${suite.passed}
- Failed: ${suite.failed}
`).join('')}

## Files
- [Detailed HTML Report](./html-report/index.html)
- [JSON Results](./results.json)
- [JUnit XML](./junit.xml)
`
}

async function cleanupTempFiles() {
  try {
    const tempDirs = [
      'test-results/artifacts/temp',
      'test-results/downloads'
    ]
    
    for (const dir of tempDirs) {
      if (fs.existsSync(dir)) {
        fs.rmSync(dir, { recursive: true, force: true })
        console.log(`🗑️  Cleaned up ${dir}`)
      }
    }
    
  } catch (error) {
    console.warn('⚠️  Could not clean up temp files:', error.message)
  }
}

async function archiveTestResults() {
  try {
    const archiveDir = `test-archives/${new Date().toISOString().split('T')[0]}`
    
    if (!fs.existsSync('test-archives')) {
      fs.mkdirSync('test-archives', { recursive: true })
    }
    
    if (!fs.existsSync(archiveDir)) {
      fs.mkdirSync(archiveDir, { recursive: true })
    }
    
    // Copy important files to archive
    const filesToArchive = [
      'test-results/summary.json',
      'test-results/summary.md',
      'test-results/junit.xml'
    ]
    
    for (const file of filesToArchive) {
      if (fs.existsSync(file)) {
        const fileName = path.basename(file)
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
        const archivePath = path.join(archiveDir, `${timestamp}-${fileName}`)
        fs.copyFileSync(file, archivePath)
      }
    }
    
    console.log(`📦 Test results archived to ${archiveDir}`)
    
  } catch (error) {
    console.warn('⚠️  Could not archive test results:', error.message)
  }
}

export default globalTeardown
