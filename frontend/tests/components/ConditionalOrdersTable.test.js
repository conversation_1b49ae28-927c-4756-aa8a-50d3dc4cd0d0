/**
 * ConditionalOrdersTable 组件测试
 * 测试条件订单表格组件的核心功能
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import { createVuetify } from 'vuetify'
import ConditionalOrdersTable from '@/components/ConditionalOrdersTable.vue'

// 创建 Vuetify 实例
const vuetify = createVuetify()

// 模拟条件订单数据
const mockConditionalOrders = [
  {
    id: 'cond_order_1',
    status: 'PENDING',
    trigger_condition: {
      symbol: 'BTC/USDT',
      operator: 'gte',
      price: 55000
    },
    action_plan: {
      symbol: 'BTC/USDT',
      side: 'buy',
      order_type: 'market',
      quantity_usd: 1000
    },
    created_at: '2024-01-01T10:00:00Z'
  },
  {
    id: 'cond_order_2',
    status: 'TRIGGERED',
    trigger_condition: {
      symbol: 'ETH/USDT',
      operator: 'lte',
      price: 2800
    },
    action_plan: {
      symbol: 'ETH/USDT',
      side: 'sell',
      order_type: 'limit',
      quantity_usd: 500,
      price: 2750
    },
    created_at: '2024-01-01T11:00:00Z'
  },
  {
    id: 'cond_order_3',
    status: 'CANCELLED',
    trigger_condition: {
      symbol: 'XRP/USDT',
      operator: 'gt',
      price: 0.6
    },
    action_plan: {
      symbol: 'XRP/USDT',
      side: 'buy',
      order_type: 'market',
      quantity_usd: 200
    },
    created_at: '2024-01-01T12:00:00Z'
  }
]

describe('ConditionalOrdersTable', () => {
  let wrapper
  let pinia

  beforeEach(() => {
    // 创建新的Pinia实例
    pinia = createPinia()
    setActivePinia(pinia)

    wrapper = mount(ConditionalOrdersTable, {
      props: {
        orders: mockConditionalOrders,
        loading: false,
        search: ''
      },
      global: {
        plugins: [vuetify, pinia]
      }
    })
  })

  afterEach(() => {
    wrapper?.unmount()
    vi.clearAllMocks()
  })

  describe('组件渲染', () => {
    it('应该正确渲染组件', () => {
      expect(wrapper.exists()).toBe(true)
      expect(wrapper.findComponent({ name: 'VCard' }).exists()).toBe(true)
    })

    it('应该渲染数据表格', () => {
      const dataTable = wrapper.findComponent({ name: 'VDataTable' })
      expect(dataTable.exists()).toBe(true)
    })

    it('应该显示正确的表头', () => {
      const headers = wrapper.vm.headers
      expect(headers).toHaveLength(6)
      expect(headers[0].title).toBe('状态')
      expect(headers[1].title).toBe('交易对')
      expect(headers[2].title).toBe('触发条件')
      expect(headers[3].title).toBe('行动计划')
      expect(headers[4].title).toBe('创建时间')
      expect(headers[5].title).toBe('操作')
    })

    it('应该支持多选功能', () => {
      const dataTable = wrapper.findComponent({ name: 'VDataTable' })
      expect(dataTable.props('showSelect')).toBe(true)
    })
  })

  describe('数据显示', () => {
    it('应该正确显示订单状态', () => {
      const statusChips = wrapper.findAll('.v-chip')
      expect(statusChips.length).toBeGreaterThan(0)
      
      // 检查状态文本
      const pendingChip = statusChips.find(chip => chip.text().includes('等待触发'))
      expect(pendingChip).toBeTruthy()
    })

    it('应该正确显示触发条件', () => {
      const conditionDisplays = wrapper.findAll('.condition-display')
      expect(conditionDisplays.length).toBeGreaterThan(0)
      
      // 检查条件文本格式
      const firstCondition = conditionDisplays[0]
      expect(firstCondition.text()).toContain('BTC/USDT')
      expect(firstCondition.text()).toContain('≥')
    })

    it('应该正确显示行动计划', () => {
      // 检查行动计划的显示
      const actionPlans = wrapper.findAll('[data-testid="action-plan"]')
      if (actionPlans.length > 0) {
        expect(actionPlans[0].text()).toContain('做多')
        expect(actionPlans[0].text()).toContain('1000')
      }
    })
  })

  describe('状态处理', () => {
    it('应该显示状态信息', () => {
      // 检查状态芯片
      const statusChips = wrapper.findAll('.v-chip')
      expect(statusChips.length).toBeGreaterThan(0)
    })

    it('应该显示状态文本', () => {
      // 检查是否包含状态相关文本
      const content = wrapper.text()
      expect(content).toMatch(/(等待触发|已触发|已取消|已过期)/)
    })
  })

  describe('条件处理', () => {
    it('应该显示触发条件信息', () => {
      // 检查条件显示区域
      const conditionDisplays = wrapper.findAll('.condition-display')
      expect(conditionDisplays.length).toBeGreaterThan(0)
    })

    it('应该显示操作符信息', () => {
      // 检查是否包含操作符符号
      const content = wrapper.text()
      expect(content).toMatch(/[≥≤><]/)
    })
  })

  describe('用户交互', () => {
    it('应该能够渲染操作按钮', () => {
      // 检查是否有操作按钮
      const actionButtons = wrapper.findAll('button')
      expect(actionButtons.length).toBeGreaterThan(0)
    })

    it('应该能够发射事件', () => {
      // 测试事件发射功能
      wrapper.vm.$emit('view-details', mockConditionalOrders[0])
      expect(wrapper.emitted('view-details')).toBeTruthy()
    })
  })

  describe('搜索功能', () => {
    it('应该支持搜索', async () => {
      await wrapper.setProps({ search: 'BTC' })
      const dataTable = wrapper.findComponent({ name: 'VDataTable' })
      expect(dataTable.props('search')).toBe('BTC')
    })
  })

  describe('加载状态', () => {
    it('应该显示加载状态', async () => {
      await wrapper.setProps({ loading: true })
      const dataTable = wrapper.findComponent({ name: 'VDataTable' })
      expect(dataTable.props('loading')).toBe(true)
    })
  })

  describe('空状态处理', () => {
    it('应该显示空状态', async () => {
      await wrapper.setProps({ orders: [] })
      
      const emptyState = wrapper.find('.text-center.py-6')
      expect(emptyState.exists()).toBe(true)
      expect(emptyState.text()).toContain('暂无条件订单')
    })

    it('应该在空状态下显示创建按钮', async () => {
      await wrapper.setProps({ orders: [] })
      
      const createButton = wrapper.find('button')
      if (createButton.exists() && createButton.text().includes('创建条件订单')) {
        await createButton.trigger('click')
        expect(wrapper.emitted('create-new')).toBeTruthy()
      }
    })
  })

  describe('日期格式化', () => {
    it('应该正确格式化日期', () => {
      const dateString = '2024-01-01T10:00:00Z'
      const formatted = wrapper.vm.formatDate(dateString)
      expect(formatted).toMatch(/\d{4}\/\d{1,2}\/\d{1,2}/)
    })

    it('应该处理空日期', () => {
      expect(wrapper.vm.formatDate('')).toBe('')
      expect(wrapper.vm.formatDate(null)).toBe('')
      expect(wrapper.vm.formatDate(undefined)).toBe('')
    })
  })

  describe('事件发射', () => {
    it('应该正确发射view-details事件', async () => {
      const testOrder = mockConditionalOrders[0]
      wrapper.vm.$emit('view-details', testOrder)
      
      expect(wrapper.emitted('view-details')).toBeTruthy()
      expect(wrapper.emitted('view-details')[0][0]).toEqual(testOrder)
    })

    it('应该正确发射cancel-order事件', async () => {
      const testOrder = mockConditionalOrders[0]
      wrapper.vm.$emit('cancel-order', testOrder)
      
      expect(wrapper.emitted('cancel-order')).toBeTruthy()
      expect(wrapper.emitted('cancel-order')[0][0]).toEqual(testOrder)
    })

    it('应该正确发射create-new事件', async () => {
      wrapper.vm.$emit('create-new')
      
      expect(wrapper.emitted('create-new')).toBeTruthy()
    })
  })

  describe('响应式行为', () => {
    it('应该响应orders prop变化', async () => {
      const newOrders = [...mockConditionalOrders, {
        id: 'cond_order_4',
        status: 'PENDING',
        trigger_condition: {
          symbol: 'ADA/USDT',
          operator: 'gte',
          price: 1.2
        },
        action_plan: {
          symbol: 'ADA/USDT',
          side: 'buy',
          order_type: 'market',
          quantity_usd: 300
        },
        created_at: '2024-01-01T13:00:00Z'
      }]
      
      await wrapper.setProps({ orders: newOrders })
      expect(wrapper.props('orders')).toHaveLength(4)
    })

    it('应该响应loading prop变化', async () => {
      await wrapper.setProps({ loading: true })
      expect(wrapper.props('loading')).toBe(true)
      
      await wrapper.setProps({ loading: false })
      expect(wrapper.props('loading')).toBe(false)
    })

    it('应该响应search prop变化', async () => {
      await wrapper.setProps({ search: 'ETH' })
      expect(wrapper.props('search')).toBe('ETH')
    })
  })
})
