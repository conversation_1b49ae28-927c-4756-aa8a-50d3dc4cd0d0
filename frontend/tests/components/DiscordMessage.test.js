/**
 * DiscordMessage 组件测试
 *
 * 测试Discord消息渲染组件的功能：
 * - 基本消息渲染
 * - Discord样式模式
 * - 原始消息模式
 * - Embeds渲染
 * - 附件处理
 * - 反应显示
 * - 样式切换
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { createVuetify } from 'vuetify'
import DiscordMessage from '@/components/signals/DiscordMessage.vue'

describe('DiscordMessage', () => {
  let wrapper
  let vuetify

  // 测试数据
  const basicSignal = {
    id: '1',
    platform: 'discord',
    content: '这是一个测试Discord消息',
    channel_name: 'test-channel',
    author_name: 'TestBot',
    author_id: '123456789',
    created_at: '2024-01-01T12:00:00Z',
    raw_content: '原始消息内容',
    metadata: {
      discord: {
        guild_id: '444555666',
        guild_name: 'Test Server',
        embeds: [],
        attachments: [],
        reactions: []
      }
    }
  }

  const signalWithEmbeds = {
    ...basicSignal,
    metadata: {
      discord: {
        guild_id: '444555666',
        guild_name: 'Test Server',
        embeds: [
          {
            title: '交易信号',
            description: 'BTC买入信号',
            color: 65280,
            fields: [
              { name: '入场价', value: '$50,000', inline: true },
              { name: '止损价', value: '$48,000', inline: true },
              { name: '目标价', value: '$55,000', inline: true }
            ],
            author: {
              name: 'TradingBot',
              icon_url: 'https://example.com/bot-avatar.png'
            },
            footer: {
              text: '交易提醒',
              icon_url: 'https://example.com/footer-icon.png'
            },
            timestamp: '2024-01-01T12:00:00Z'
          }
        ],
        attachments: [
          {
            id: '987654321',
            filename: 'chart.png',
            url: 'https://example.com/chart.png',
            size: 1024000
          }
        ],
        reactions: [
          { emoji: '🚀', count: 15 },
          { emoji: '💎', count: 8 }
        ]
      }
    }
  }

  beforeEach(() => {
    vuetify = createVuetify()
    vi.clearAllMocks()
  })

  const createWrapper = (props = {}) => {
    return mount(DiscordMessage, {
      props: {
        signal: basicSignal,
        ...props
      },
      global: {
        plugins: [vuetify]
      }
    })
  }

  it('应该正确渲染基本Discord消息', () => {
    // Act
    wrapper = createWrapper()

    // Assert
    expect(wrapper.exists()).toBe(true)
    expect(wrapper.find('.discord-message').exists()).toBe(true)
    expect(wrapper.find('.discord-native').exists()).toBe(true)
    expect(wrapper.text()).toContain('这是一个测试Discord消息')
    expect(wrapper.text()).toContain('TestBot')
    expect(wrapper.text()).toContain('test-channel')
  })

  it('应该显示消息头部信息', () => {
    // Act
    wrapper = createWrapper()

    // Assert
    const header = wrapper.find('.message-header')
    expect(header.exists()).toBe(true)

    // 检查作者信息
    expect(wrapper.find('.author-name').text()).toBe('TestBot')

    // 检查时间戳
    expect(wrapper.find('.timestamp').exists()).toBe(true)

    // 检查频道信息
    expect(wrapper.find('.guild-info').exists()).toBe(true)
    expect(wrapper.text()).toContain('test-channel')
    expect(wrapper.text()).toContain('Test Server')
  })

  it('应该正确渲染Discord Embeds', () => {
    // Act
    wrapper = createWrapper({ signal: signalWithEmbeds })

    // Assert
    const embeds = wrapper.find('.embeds')
    expect(embeds.exists()).toBe(true)

    // 检查embed标题
    expect(wrapper.find('.embed-title').text()).toBe('交易信号')

    // 检查embed描述
    expect(wrapper.find('.embed-description').text()).toBe('BTC买入信号')

    // 检查embed字段
    const fields = wrapper.findAll('.embed-field')
    expect(fields).toHaveLength(3)
    expect(fields[0].find('.field-name').text()).toBe('入场价')
    expect(fields[0].find('.field-value').text()).toBe('$50,000')

    // 检查embed作者
    expect(wrapper.find('.embed-author-name').text()).toBe('TradingBot')

    // 检查embed页脚
    expect(wrapper.find('.embed-footer-text').text()).toBe('交易提醒')
  })

  it('应该正确渲染附件', () => {
    // Act
    wrapper = createWrapper({ signal: signalWithEmbeds })

    // Assert
    const attachments = wrapper.find('.attachments')
    expect(attachments.exists()).toBe(true)

    // 检查图片附件
    const imageAttachment = wrapper.find('.attachment-image')
    expect(imageAttachment.exists()).toBe(true)

    const img = imageAttachment.find('img')
    expect(img.attributes('src')).toBe('https://example.com/chart.png')
    expect(img.attributes('alt')).toBe('chart.png')

    // 检查附件信息
    expect(wrapper.find('.attachment-name').text()).toBe('chart.png')
    expect(wrapper.find('.attachment-size').text()).toBe('1000 KB')
  })

  it('应该正确渲染反应', () => {
    // Act
    wrapper = createWrapper({ signal: signalWithEmbeds })

    // Assert
    const reactions = wrapper.find('.reactions')
    expect(reactions.exists()).toBe(true)

    const reactionChips = wrapper.findAll('.reaction-chip')
    expect(reactionChips).toHaveLength(2)

    expect(reactionChips[0].find('.reaction-emoji').text()).toBe('🚀')
    expect(reactionChips[0].find('.reaction-count').text()).toBe('15')

    expect(reactionChips[1].find('.reaction-emoji').text()).toBe('💎')
    expect(reactionChips[1].find('.reaction-count').text()).toBe('8')
  })

  it('应该支持样式切换', async () => {
    // Act
    wrapper = createWrapper()

    // Assert - 默认Discord样式
    expect(wrapper.find('.discord-native').exists()).toBe(true)
    expect(wrapper.find('.raw-message').exists()).toBe(false)

    // Act - 切换到原始格式
    const toggleButtons = wrapper.findAll('.v-btn')
    const rawButton = toggleButtons.find(btn => btn.text().includes('原始格式'))
    await rawButton.trigger('click')

    // Assert - 原始格式模式
    expect(wrapper.find('.discord-native').exists()).toBe(false)
    expect(wrapper.find('.raw-message').exists()).toBe(true)
    expect(wrapper.find('.raw-content').text()).toBe('原始消息内容')
  })

  it('应该处理Discord markdown格式化', () => {
    // Arrange
    const signalWithMarkdown = {
      ...basicSignal,
      content: '**粗体文本** *斜体文本* ~~删除线~~ `代码` [链接](https://example.com)'
    }

    // Act
    wrapper = createWrapper({ signal: signalWithMarkdown })

    // Assert
    const content = wrapper.find('.text-content')
    expect(content.html()).toContain('<strong>粗体文本</strong>')
    expect(content.html()).toContain('<em>斜体文本</em>')
    expect(content.html()).toContain('<del>删除线</del>')
    expect(content.html()).toContain('<code>代码</code>')
    expect(content.html()).toContain('<a href="https://example.com" target="_blank">链接</a>')
  })

  it('应该处理图片点击放大', async () => {
    // Act
    wrapper = createWrapper({ signal: signalWithEmbeds })

    // Assert - 初始状态
    expect(wrapper.vm.imageModal).toBe(false)

    // Act - 点击图片
    const img = wrapper.find('.attachment-image-content')
    await img.trigger('click')

    // Assert - 模态框打开
    expect(wrapper.vm.imageModal).toBe(true)
    expect(wrapper.vm.selectedImage).toBe('https://example.com/chart.png')
  })

  it('应该处理头像加载错误', async () => {
    // Act
    wrapper = createWrapper()

    // Assert - 初始状态
    expect(wrapper.vm.avatarError).toBe(false)

    // Act - 触发头像错误
    const avatar = wrapper.find('.message-header img')
    if (avatar.exists()) {
      await avatar.trigger('error')
      expect(wrapper.vm.avatarError).toBe(true)
    }
  })

  it('应该正确格式化时间戳', () => {
    // Act
    wrapper = createWrapper()

    // Assert
    const timestamp = wrapper.vm.formatTimestamp('2024-01-01T12:00:00Z')
    expect(timestamp).toMatch(/\d{4}\/\d{2}\/\d{2}/)
  })

  it('应该正确识别文件类型', () => {
    // Act
    wrapper = createWrapper()

    // Assert
    expect(wrapper.vm.isImage('test.png')).toBe(true)
    expect(wrapper.vm.isImage('test.jpg')).toBe(true)
    expect(wrapper.vm.isImage('test.gif')).toBe(true)
    expect(wrapper.vm.isImage('test.txt')).toBe(false)

    expect(wrapper.vm.isVideo('test.mp4')).toBe(true)
    expect(wrapper.vm.isVideo('test.webm')).toBe(true)
    expect(wrapper.vm.isVideo('test.png')).toBe(false)
  })

  it('应该正确格式化文件大小', () => {
    // Act
    wrapper = createWrapper()

    // Assert
    expect(wrapper.vm.formatFileSize(1024)).toBe('1 KB')
    expect(wrapper.vm.formatFileSize(1048576)).toBe('1 MB')
    expect(wrapper.vm.formatFileSize(1073741824)).toBe('1 GB')
    expect(wrapper.vm.formatFileSize(0)).toBe('0 B')
  })

  it('应该处理空元数据', () => {
    // Arrange
    const signalWithoutMetadata = {
      ...basicSignal,
      metadata: null
    }

    // Act
    wrapper = createWrapper({ signal: signalWithoutMetadata })

    // Assert
    expect(wrapper.find('.embeds').exists()).toBe(false)
    expect(wrapper.find('.attachments').exists()).toBe(false)
    expect(wrapper.find('.reactions').exists()).toBe(false)
  })
})