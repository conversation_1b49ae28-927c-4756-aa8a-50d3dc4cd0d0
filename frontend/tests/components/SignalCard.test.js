// SignalCard 组件测试文件
import { describe, it, expect } from 'vitest'
import { mount } from '@vue/test-utils'
import { createVuetify } from 'vuetify'
import SignalCard from '@/components/signals/SignalCard.vue'

// 创建 Vuetify 实例
const vuetify = createVuetify()

// 模拟信号数据
const mockSignal = {
  id: 'test-signal-1',
  platform: 'discord',
  author_name: 'Test User',
  channel_name: 'test-channel',
  content: 'This is a test signal message',
  created_at: '2024-01-01T12:00:00Z',
  ai_parse_status: 'completed',
  message_type_ai: 'signal',
  is_processed: false,
  confidence: 0.85,
  llm_service: 'openai',
  metadata: {
    discord: {
      author_avatar: 'https://example.com/avatar.png',
      is_bot: false,
      embeds: [
        {
          title: 'Test Embed',
          description: 'This is a test embed',
          color: '#5865f2'
        }
      ],
      attachments: [],
      reactions: [
        { emoji: '👍', count: 5 },
        { emoji: '🚀', count: 3 }
      ]
    }
  }
}

describe('SignalCard', () => {
  it('应该正确渲染信号卡片', () => {
    const wrapper = mount(SignalCard, {
      props: {
        signal: mockSignal,
        theme: 'discord'
      },
      global: {
        plugins: [vuetify]
      }
    })

    expect(wrapper.exists()).toBe(true)
    expect(wrapper.find('.signal-card').exists()).toBe(true)
  })

  it('应该显示正确的平台标识', () => {
    const wrapper = mount(SignalCard, {
      props: {
        signal: mockSignal,
        theme: 'discord'
      },
      global: {
        plugins: [vuetify]
      }
    })

    const platformChip = wrapper.find('.platform-chip')
    expect(platformChip.exists()).toBe(true)
    expect(platformChip.text()).toContain('Discord')
  })

  it('应该在选中时应用正确的样式', () => {
    const wrapper = mount(SignalCard, {
      props: {
        signal: mockSignal,
        theme: 'discord',
        selected: true
      },
      global: {
        plugins: [vuetify]
      }
    })

    expect(wrapper.find('.signal-card--selected').exists()).toBe(true)
  })

  it('应该在点击时发出事件', async () => {
    const wrapper = mount(SignalCard, {
      props: {
        signal: mockSignal,
        theme: 'discord'
      },
      global: {
        plugins: [vuetify]
      }
    })

    await wrapper.find('.signal-card').trigger('click')
    expect(wrapper.emitted('click')).toBeTruthy()
    expect(wrapper.emitted('click')[0]).toEqual([mockSignal])
  })

  it('应该根据主题应用正确的CSS类', () => {
    const wrapper = mount(SignalCard, {
      props: {
        signal: mockSignal,
        theme: 'discord'
      },
      global: {
        plugins: [vuetify]
      }
    })

    expect(wrapper.find('.signal-card--discord').exists()).toBe(true)
  })

  it('应该在通用主题下正确渲染', () => {
    const wrapper = mount(SignalCard, {
      props: {
        signal: mockSignal,
        theme: 'generic'
      },
      global: {
        plugins: [vuetify]
      }
    })

    expect(wrapper.find('.signal-card--generic').exists()).toBe(true)
  })
})

// 主题管理器测试
describe('ThemeManager', () => {
  it('应该正确导入主题管理器', async () => {
    const { themeManager, getThemeStrategy } = await import('@/components/signals/themes/index.js')
    
    expect(themeManager).toBeDefined()
    expect(getThemeStrategy).toBeDefined()
  })

  it('应该为Discord平台返回正确的主题策略', async () => {
    const { getThemeStrategy } = await import('@/components/signals/themes/index.js')
    
    const strategy = getThemeStrategy('discord')
    expect(strategy.getName()).toBe('discord')
    expect(strategy.supports('discord')).toBe(true)
  })

  it('应该为未知平台返回默认主题策略', async () => {
    const { getThemeStrategy } = await import('@/components/signals/themes/index.js')
    
    const strategy = getThemeStrategy('unknown-platform')
    expect(strategy.getName()).toBe('generic')
    expect(strategy.supports('unknown-platform')).toBe(true)
  })
})
