import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import SignalFilters from '@/components/signals/SignalFilters.vue'

describe('SignalFilters', () => {
  let wrapper

  beforeEach(() => {
    setActivePinia(createPinia())

    wrapper = mount(SignalFilters, {
      global: {
        plugins: [createPinia()]
      },
      props: {
        modelValue: {
          platform: null,
          channel_id: null,
          is_processed: null,
          signal_strength_range: [0, 1],
          date_range: null
        },
        loading: false
      }
    })
  })

  it('应该正确渲染筛选组件', () => {
    // 测试组件是否正确挂载
    expect(wrapper.exists()).toBe(true)

    // 测试组件的基本结构
    expect(wrapper.find('.v-card-text').exists()).toBe(true)
    expect(wrapper.find('.v-row').exists()).toBe(true)

    // 测试组件的响应式数据是否正确初始化
    const component = wrapper.vm
    expect(component.localFilters).toBeDefined()
    expect(component.localFilters.platform).toBe(null)
    expect(component.localFilters.signal_strength_range).toEqual([0, 1])
  })

  it('应该正确处理平台筛选', async () => {
    // 直接测试组件的内部状态变化和事件发射
    const component = wrapper.vm

    // 模拟平台选择变化
    component.localFilters.platform = 'discord'
    component.handleFilterChange()

    // 验证事件被触发
    expect(wrapper.emitted('filter-change')).toBeTruthy()
    expect(wrapper.emitted('update:modelValue')).toBeTruthy()

    const updateEvents = wrapper.emitted('update:modelValue')
    expect(updateEvents[updateEvents.length - 1][0]).toMatchObject({
      platform: 'discord'
    })
  })

  it('应该正确处理日期范围筛选', async () => {
    const component = wrapper.vm

    // 测试设置日期范围
    component.localFilters.date_range = ['2024-01-01', '2024-01-31']
    component.handleFilterChange()

    // 验证事件被触发
    expect(wrapper.emitted('filter-change')).toBeTruthy()
    expect(wrapper.emitted('update:modelValue')).toBeTruthy()

    const updateEvents = wrapper.emitted('update:modelValue')
    expect(updateEvents[updateEvents.length - 1][0]).toMatchObject({
      date_range: ['2024-01-01', '2024-01-31']
    })
  })

  it('应该正确处理信号强度筛选', async () => {
    const component = wrapper.vm

    // 测试设置强度范围
    component.localFilters.signal_strength_range = [0.7, 1]
    component.handleFilterChange()

    // 验证事件被触发
    expect(wrapper.emitted('filter-change')).toBeTruthy()
    expect(wrapper.emitted('update:modelValue')).toBeTruthy()

    const updateEvents = wrapper.emitted('update:modelValue')
    expect(updateEvents[updateEvents.length - 1][0]).toMatchObject({
      signal_strength_range: [0.7, 1]
    })
  })

  it('应该正确处理频道ID筛选', async () => {
    const component = wrapper.vm

    // 测试频道ID输入
    component.localFilters.channel_id = 'test-channel'
    component.handleFilterChange()

    // 验证事件被触发
    expect(wrapper.emitted('filter-change')).toBeTruthy()
    expect(wrapper.emitted('update:modelValue')).toBeTruthy()

    const updateEvents = wrapper.emitted('update:modelValue')
    expect(updateEvents[updateEvents.length - 1][0]).toMatchObject({
      channel_id: 'test-channel'
    })
  })

  it('应该正确处理筛选重置', async () => {
    const component = wrapper.vm

    // 先设置一些筛选条件
    component.localFilters.platform = 'discord'
    component.localFilters.date_range = ['2024-01-01', '2024-01-31']
    component.localFilters.signal_strength_range = [0.5, 1]
    component.localFilters.channel_id = 'test-channel'

    // 调用重置方法
    component.resetFilters()

    // 验证筛选条件被重置
    expect(component.localFilters).toMatchObject({
      platform: null,
      channel_id: null,
      is_processed: null,
      signal_strength_range: [0, 1],
      date_range: null
    })

    // 验证事件被触发
    expect(wrapper.emitted('filter-change')).toBeTruthy()
  })

  it('应该正确处理处理状态筛选', async () => {
    const component = wrapper.vm

    // 测试设置处理状态
    component.localFilters.is_processed = false
    component.handleFilterChange()

    // 验证事件被触发
    expect(wrapper.emitted('filter-change')).toBeTruthy()
    expect(wrapper.emitted('update:modelValue')).toBeTruthy()

    const updateEvents = wrapper.emitted('update:modelValue')
    expect(updateEvents[updateEvents.length - 1][0]).toMatchObject({
      is_processed: false
    })
  })

  it('应该正确处理快速筛选功能', async () => {
    const component = wrapper.vm

    // 测试今日快速筛选
    component.applyQuickFilter('today')

    // 验证日期范围被设置为今天
    const today = new Date().toISOString().split('T')[0]
    expect(component.localFilters.date_range).toEqual([today, today])

    // 验证事件被触发
    expect(wrapper.emitted('filter-change')).toBeTruthy()
  })

  it('应该正确处理筛选条件的组合', async () => {
    const component = wrapper.vm

    // 设置多个筛选条件
    component.localFilters.platform = 'discord'
    component.localFilters.channel_id = 'test-channel'
    component.localFilters.is_processed = false
    component.localFilters.signal_strength_range = [0.7, 1]
    component.handleFilterChange()

    // 验证所有筛选条件都被正确设置
    expect(component.localFilters.platform).toBe('discord')
    expect(component.localFilters.channel_id).toBe('test-channel')
    expect(component.localFilters.is_processed).toBe(false)
    expect(component.localFilters.signal_strength_range).toEqual([0.7, 1])

    // 验证事件被触发
    expect(wrapper.emitted('filter-change')).toBeTruthy()
  })

  it('应该正确格式化强度范围显示', () => {
    const component = wrapper.vm

    // 测试格式化函数
    expect(component.formatStrengthRange([0, 1])).toBe('0% - 100%')
    expect(component.formatStrengthRange([0.3, 0.8])).toBe('30% - 80%')
    expect(component.formatStrengthRange(null)).toBe('0% - 100%')
  })

  it('应该正确处理平台颜色和图标', () => {
    const component = wrapper.vm

    // 测试平台颜色
    expect(component.getPlatformColor('discord')).toBe('indigo')
    expect(component.getPlatformColor('telegram')).toBe('blue')
    expect(component.getPlatformColor('manual')).toBe('green')
    expect(component.getPlatformColor('unknown')).toBe('default')

    // 测试平台图标
    expect(component.getPlatformIcon('discord')).toBe('mdi-discord')
    expect(component.getPlatformIcon('telegram')).toBe('mdi-telegram')
    expect(component.getPlatformIcon('manual')).toBe('mdi-pencil')
    expect(component.getPlatformIcon('unknown')).toBe('mdi-help-circle')
  })
})
