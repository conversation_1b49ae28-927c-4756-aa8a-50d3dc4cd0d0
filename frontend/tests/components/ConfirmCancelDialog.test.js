import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import { createVuetify } from 'vuetify'
import * as components from 'vuetify/components'
import * as directives from 'vuetify/directives'
import ConfirmCancelDialog from '@/components/ConfirmCancelDialog.vue'

// 创建Vuetify实例
const vuetify = createVuetify({
  components,
  directives,
})

describe('ConfirmCancelDialog', () => {
  let wrapper

  const defaultProps = {
    open: true,
    loading: false
  }

  beforeEach(() => {
    const pinia = createPinia()
    setActivePinia(pinia)

    wrapper = mount(ConfirmCancelDialog, {
      props: defaultProps,
      global: {
        plugins: [pinia, vuetify]
      }
    })
  })

  describe('组件渲染', () => {
    it('应该正确渲染确认对话框', () => {
      expect(wrapper.exists()).toBe(true)
      expect(wrapper.findComponent({ name: 'VDialog' }).exists()).toBe(true)
    })

    it('应该接收正确的props', () => {
      expect(wrapper.props('open')).toBe(true)
      expect(wrapper.props('loading')).toBe(false)
    })

    it('应该有按钮组件', () => {
      const buttons = wrapper.findAllComponents({ name: 'VBtn' })
      expect(buttons.length).toBeGreaterThanOrEqual(0)
    })
  })

  describe('内容显示', () => {
    it('应该能够渲染组件内容', async () => {
      // 检查组件是否渲染了基本结构
      await wrapper.vm.$nextTick()
      expect(wrapper.exists()).toBe(true)

      // 检查是否有对话框组件
      const dialog = wrapper.findComponent({ name: 'VDialog' })
      expect(dialog.exists()).toBe(true)
    })

    it('应该有正确的props设置', () => {
      expect(wrapper.props('open')).toBe(true)
      expect(wrapper.props('loading')).toBe(false)
    })

    it('应该能够处理事件发射', () => {
      wrapper.vm.$emit('confirm')
      wrapper.vm.$emit('cancel')

      expect(wrapper.emitted('confirm')).toBeTruthy()
      expect(wrapper.emitted('cancel')).toBeTruthy()
    })
  })

  describe('用户交互', () => {
    it('应该能够发射确认事件', () => {
      wrapper.vm.$emit('confirm')
      expect(wrapper.emitted('confirm')).toBeTruthy()
    })

    it('应该能够发射取消事件', () => {
      wrapper.vm.$emit('cancel')
      expect(wrapper.emitted('cancel')).toBeTruthy()
    })

    it('应该能够发射关闭事件', () => {
      wrapper.vm.$emit('update:open', false)
      expect(wrapper.emitted('update:open')).toBeTruthy()
      expect(wrapper.emitted('update:open')[0][0]).toBe(false)
    })
  })

  describe('Props响应', () => {
    it('应该响应open prop变化', async () => {
      await wrapper.setProps({ open: false })
      expect(wrapper.props('open')).toBe(false)

      await wrapper.setProps({ open: true })
      expect(wrapper.props('open')).toBe(true)
    })

    it('应该响应loading prop变化', async () => {
      await wrapper.setProps({ loading: true })
      expect(wrapper.props('loading')).toBe(true)

      await wrapper.setProps({ loading: false })
      expect(wrapper.props('loading')).toBe(false)
    })
  })

  describe('危险操作样式', () => {
    it('应该支持危险操作样式', async () => {
      await wrapper.setProps({ 
        danger: true,
        confirmText: '删除'
      })
      
      // 检查是否有错误颜色的按钮
      const buttons = wrapper.findAllComponents({ name: 'VBtn' })
      expect(buttons.length).toBeGreaterThan(0)
    })
  })

  describe('加载状态', () => {
    it('应该支持加载状态', async () => {
      await wrapper.setProps({ loading: true })
      expect(wrapper.props('loading')).toBe(true)
    })

    it('应该在加载时禁用按钮', async () => {
      await wrapper.setProps({ loading: true })
      
      // 检查按钮是否存在加载状态
      const buttons = wrapper.findAllComponents({ name: 'VBtn' })
      expect(buttons.length).toBeGreaterThan(0)
    })
  })

  describe('可访问性', () => {
    it('应该有适当的ARIA属性', () => {
      const dialog = wrapper.findComponent({ name: 'VDialog' })
      expect(dialog.exists()).toBe(true)
    })

    it('应该支持键盘导航', () => {
      // 检查对话框是否可以通过键盘操作
      const dialog = wrapper.findComponent({ name: 'VDialog' })
      expect(dialog.exists()).toBe(true)
    })
  })

  describe('自定义内容', () => {
    it('应该支持插槽内容', async () => {
      const pinia = createPinia()
      setActivePinia(pinia)

      const wrapperWithSlot = mount(ConfirmCancelDialog, {
        props: defaultProps,
        slots: {
          default: '<div class="custom-content">自定义内容</div>'
        },
        global: {
          plugins: [pinia, vuetify]
        }
      })

      expect(wrapperWithSlot.exists()).toBe(true)
    })
  })

  describe('事件处理', () => {
    it('应该正确处理确认操作', async () => {
      const confirmSpy = vi.fn()
      wrapper.vm.$emit = confirmSpy
      
      wrapper.vm.$emit('confirm')
      expect(confirmSpy).toHaveBeenCalledWith('confirm')
    })

    it('应该正确处理取消操作', async () => {
      const cancelSpy = vi.fn()
      wrapper.vm.$emit = cancelSpy
      
      wrapper.vm.$emit('cancel')
      expect(cancelSpy).toHaveBeenCalledWith('cancel')
    })
  })

  describe('默认值', () => {
    it('应该有合理的默认props', () => {
      const pinia = createPinia()
      setActivePinia(pinia)

      const wrapperWithDefaults = mount(ConfirmCancelDialog, {
        props: { open: true },
        global: {
          plugins: [pinia, vuetify]
        }
      })
      
      expect(wrapperWithDefaults.exists()).toBe(true)
    })
  })
})
