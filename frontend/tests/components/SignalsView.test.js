/**
 * SignalsView 组件测试
 *
 * 测试信号页面的主要功能：
 * - 组件渲染
 * - 信号列表展示
 * - 筛选功能
 * - 创建信号对话框
 * - 信号详情对话框
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { createVuetify } from 'vuetify'
import { createPinia } from 'pinia'
import SignalsView from '@/views/SignalsView.vue'

// Mock API
vi.mock('@/api/signals', () => ({
  signalApi: {
    getSignals: vi.fn(),
    getStats: vi.fn(),
    createSignal: vi.fn(),
    updateSignal: vi.fn(),
    deleteSignal: vi.fn(),
    batchUpdateSignals: vi.fn(),
    searchSignals: vi.fn()
  }
}))

// Import the mocked API
import { signalApi } from '@/api/signals'

// Mock useSnackbar
vi.mock('@/composables/useSnackbar', () => ({
  useSnackbar: () => ({
    showSnackbar: vi.fn(),
    showSuccess: vi.fn(),
    showError: vi.fn()
  })
}))

describe('SignalsView', () => {
  let wrapper
  let vuetify
  let pinia

  // 测试数据
  const mockSignals = [
    {
      id: '1',
      platform: 'discord',
      content: '测试Discord信号',
      channel_name: 'test-channel',
      author_name: 'TestBot',
      is_processed: false,
      signal_strength: 0.8,
      created_at: '2024-01-01T00:00:00Z',
      metadata: {
        discord: {
          embeds: [{ title: '测试嵌入' }],
          attachments: [],
          reactions: []
        }
      }
    },
    {
      id: '2',
      platform: 'telegram',
      content: '测试Telegram信号',
      channel_name: 'test_channel',
      author_name: 'TestUser',
      is_processed: true,
      signal_strength: 0.6,
      created_at: '2024-01-02T00:00:00Z',
      metadata: {
        telegram: {
          chat_type: 'channel'
        }
      }
    },
    {
      id: '3',
      platform: 'manual',
      content: '手动创建的信号',
      channel_name: null,
      author_name: 'Admin',
      is_processed: false,
      signal_strength: null,
      created_at: '2024-01-03T00:00:00Z',
      metadata: null
    }
  ]

  const mockStats = {
    total_signals: 3,
    processed_signals: 1,
    platform_breakdown: {
      discord: 1,
      telegram: 1,
      manual: 1
    },
    avg_signal_strength: 0.7,
    recent_activity: []
  }

  beforeEach(() => {
    // 创建Vuetify实例
    vuetify = createVuetify()

    // 创建Pinia实例
    pinia = createPinia()

    // 重置所有mock
    vi.clearAllMocks()

    // Mock API响应
    signalApi.getSignals.mockResolvedValue({
      success: true,
      data: {
        items: mockSignals,
        total: mockSignals.length,
        page: 1,
        size: 10
      }
    })

    signalApi.getStats.mockResolvedValue({
      success: true,
      data: mockStats
    })
  })

  const createWrapper = (props = {}) => {
    return mount(SignalsView, {
      props,
      global: {
        plugins: [vuetify, pinia],
        stubs: {
          // 存根子组件以简化测试
          SignalFilters: true,
          SignalsList: true,
          SignalDetailsDialog: true,
          CreateSignalDialog: true
        }
      }
    })
  }

  it('应该正确渲染组件', async () => {
    // Act
    wrapper = createWrapper()
    await wrapper.vm.$nextTick()

    // Assert
    expect(wrapper.exists()).toBe(true)
    expect(wrapper.find('[data-testid="signals-view"]').exists()).toBe(true)
  })

  it('应该在挂载时加载信号数据', async () => {
    // Act
    wrapper = createWrapper()
    await wrapper.vm.$nextTick()

    // Assert
    expect(signalApi.getSignals).toHaveBeenCalledWith({
      page: 1,
      size: 20,
      sort_by: 'created_at',
      sort_order: 'desc',
      platform: null,
      channel_id: null,
      is_processed: null,
      signal_strength_min: null,
      date_range: null,
      search: ''
    })
    expect(signalApi.getStats).toHaveBeenCalled()
  })

  it('应该显示页面标题和统计信息', async () => {
    // Act
    wrapper = createWrapper()
    await wrapper.vm.$nextTick()

    // Assert
    expect(wrapper.text()).toContain('信号管理')
    // 等待数据加载完成
    await new Promise(resolve => setTimeout(resolve, 100))
    expect(wrapper.vm.stats).toEqual(mockStats)
  })

  it('应该处理筛选条件变化', async () => {
    // Arrange
    wrapper = createWrapper()
    await wrapper.vm.$nextTick()

    const newFilters = {
      platform: 'discord',
      is_processed: false,
      signal_strength_min: 0.5,
      signal_strength_max: 1.0
    }

    // Act
    wrapper.vm.filters.platform = newFilters.platform
    await wrapper.vm.handleFilterChange()

    // Assert

    expect(signalApi.getSignals).toHaveBeenLastCalledWith({
      page: 1,
      size: 20,
      sort_by: 'created_at',
      sort_order: 'desc',
      platform: 'discord',
      channel_id: null,
      is_processed: null,
      signal_strength_min: null,
      date_range: null,
      search: ''
    })
  })

  it('应该处理分页变化', async () => {
    // Arrange
    wrapper = createWrapper()
    await wrapper.vm.$nextTick()

    // Act
    await wrapper.vm.handlePageChange(2)

    // Assert
    expect(signalApi.getSignals).toHaveBeenLastCalledWith({
      page: 2,
      size: 20,
      sort_by: 'created_at',
      sort_order: 'desc',
      platform: null,
      channel_id: null,
      is_processed: null,
      signal_strength_min: null,
      date_range: null,
      search: ''
    })
  })

  it('应该处理排序变化', async () => {
    // Arrange
    wrapper = createWrapper()
    await wrapper.vm.$nextTick()

    // Act
    await wrapper.vm.handleSortChange('signal_strength', 'desc')

    // Assert
    expect(signalApi.getSignals).toHaveBeenLastCalledWith({
      page: 1,
      size: 20,
      sort_by: 'signal_strength',
      sort_order: 'desc',
      platform: null,
      channel_id: null,
      is_processed: null,
      signal_strength_min: null,
      date_range: null,
      search: ''
    })
  })

  it('应该打开信号详情对话框', async () => {
    // Arrange
    wrapper = createWrapper()
    await wrapper.vm.$nextTick()

    // Act
    await wrapper.vm.showSignalDetails(mockSignals[0])

    // Assert
    expect(wrapper.vm.selectedSignal).toEqual(mockSignals[0])
    expect(wrapper.vm.showDetails).toBe(true)
  })

  it('应该打开创建信号对话框', async () => {
    // Arrange
    wrapper = createWrapper()
    await wrapper.vm.$nextTick()

    // Act
    await wrapper.vm.openCreateDialog()

    // Assert
    expect(wrapper.vm.showCreateDialog).toBe(true)
  })

  it('应该处理信号创建成功', async () => {
    // Arrange
    wrapper = createWrapper()
    await wrapper.vm.$nextTick()

    const newSignal = {
      id: '4',
      platform: 'manual',
      content: '新创建的信号',
      is_processed: false
    }

    // Act
    await wrapper.vm.handleSignalCreated(newSignal)

    // Assert
    expect(wrapper.vm.showCreateDialog).toBe(false)
    // 应该重新加载数据
    expect(signalApi.getSignals).toHaveBeenCalledTimes(2) // 初始加载 + 创建后重新加载
  })

  it('应该处理信号更新', async () => {
    // Arrange
    wrapper = createWrapper()
    await wrapper.vm.$nextTick()

    const updatedSignal = {
      ...mockSignals[0],
      is_processed: true
    }

    // Act
    await wrapper.vm.handleSignalUpdate(updatedSignal)

    // Assert
    expect(wrapper.vm.showDetails).toBe(false)
    // 应该重新加载统计信息
    expect(signalApi.getStats).toHaveBeenCalledTimes(2) // 初始加载 + 更新后重新加载
  })

  it('应该处理API错误', async () => {
    // Arrange
    signalApi.getSignals.mockRejectedValue(new Error('API错误'))

    // Act
    wrapper = createWrapper()
    await wrapper.vm.$nextTick()
    await new Promise(resolve => setTimeout(resolve, 100))

    // Assert
    expect(wrapper.vm.loading).toBe(false)
    // 组件会显示错误消息，但不会设置error状态
  })

  it('应该处理空数据状态', async () => {
    // Arrange
    signalApi.getSignals.mockResolvedValue({
      success: true,
      data: {
        items: [],
        total: 0,
        page: 1,
        size: 10
      }
    })

    // Act
    wrapper = createWrapper()
    await wrapper.vm.$nextTick()
    await new Promise(resolve => setTimeout(resolve, 100))

    // Assert
    expect(wrapper.vm.signals).toEqual([])
    expect(wrapper.vm.pagination.total).toBe(0)
  })

  it('应该正确处理加载状态', async () => {
    // Arrange
    let resolvePromise
    const promise = new Promise(resolve => {
      resolvePromise = resolve
    })
    signalApi.getSignals.mockReturnValue(promise)

    // Act
    wrapper = createWrapper()
    await wrapper.vm.$nextTick()

    // Assert - 加载中状态
    expect(wrapper.vm.loading).toBe(true)

    // Act - 完成加载
    resolvePromise({
      success: true,
      data: {
        items: mockSignals,
        total: mockSignals.length,
        page: 1,
        size: 10
      }
    })
    await promise
    await wrapper.vm.$nextTick()

    // Assert - 加载完成状态
    expect(wrapper.vm.loading).toBe(false)
  })



  it('应该处理网络错误', async () => {
    // Arrange
    signalApi.getSignals.mockRejectedValue(new Error('网络连接失败'))
    signalApi.getStats.mockRejectedValue(new Error('网络连接失败'))

    // Act
    wrapper = createWrapper()
    await wrapper.vm.$nextTick()
    await new Promise(resolve => setTimeout(resolve, 100))

    // Assert
    expect(wrapper.vm.loading).toBe(false)
    // 组件会显示错误消息，但不会设置error状态
  })

  it('应该正确处理权限错误', async () => {
    // Arrange
    const unauthorizedError = new Error('Unauthorized')
    unauthorizedError.response = { status: 401 }
    signalApi.getSignals.mockRejectedValue(unauthorizedError)

    // Act
    wrapper = createWrapper()
    await wrapper.vm.$nextTick()
    await new Promise(resolve => setTimeout(resolve, 100))

    // Assert
    expect(wrapper.vm.loading).toBe(false)
    // 组件会显示错误消息，但不会设置error状态
  })

  it('应该处理信号强度筛选', async () => {
    // Arrange
    wrapper = createWrapper()
    await wrapper.vm.$nextTick()

    // Act
    wrapper.vm.filters.signal_strength_min = 0.5
    await wrapper.vm.handleFilterChange()

    // Assert
    expect(signalApi.getSignals).toHaveBeenLastCalledWith({
      page: 1,
      size: 20,
      sort_by: 'created_at',
      sort_order: 'desc',
      signal_strength_min: 0.5,
      platform: null,
      channel_id: null,
      is_processed: null,
      date_range: null,
      search: ''
    })
  })

  it('应该处理日期范围筛选', async () => {
    // Arrange
    wrapper = createWrapper()
    await wrapper.vm.$nextTick()

    // Act
    wrapper.vm.filters.date_range = ['2024-01-01', '2024-01-31']
    await wrapper.vm.handleFilterChange()

    // Assert
    expect(signalApi.getSignals).toHaveBeenLastCalledWith({
      page: 1,
      size: 20,
      sort_by: 'created_at',
      sort_order: 'desc',
      date_from: '2024-01-01T00:00:00',
      date_to: '2024-01-31T23:59:59',
      platform: null,
      channel_id: null,
      is_processed: null,
      signal_strength_min: null,
      date_range: ['2024-01-01', '2024-01-31'],
      search: ''
    })
  })
})