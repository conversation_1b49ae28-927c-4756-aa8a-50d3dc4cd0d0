/**
 * Discord配置面板组件测试
 * 测试Discord配置管理界面的所有功能
 * 
 * 按照项目测试规范：
 * - 测试组件渲染和交互
 * - 测试与store的集成
 * - 使用mock隔离外部依赖
 * - 使用中文描述测试用例
 */
import { describe, test, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import { createVuetify } from 'vuetify'
import DiscordConfigPanel from '@/components/discord/DiscordConfigPanel.vue'

// Mock子组件
vi.mock('@/components/discord/DiscordConfigCard.vue', () => ({
  default: {
    name: 'DiscordConfigCard',
    template: '<div class="mock-discord-config-card" :data-config-id="config.id">{{ config.source_name }}</div>',
    props: ['config'],
    emits: ['edit', 'toggle', 'delete']
  }
}))

vi.mock('@/components/discord/DiscordConfigDialog.vue', () => ({
  default: {
    name: 'DiscordConfigDialog',
    template: '<div class="mock-discord-config-dialog" v-if="modelValue">Dialog</div>',
    props: ['modelValue', 'config', 'loading'],
    emits: ['update:modelValue', 'save']
  }
}))

// Mock Discord配置store
const mockDiscordStore = {
  configs: [],
  loading: false,
  error: null,
  configCount: 0,
  enabledConfigs: [],
  fetchConfigs: vi.fn(),
  createConfig: vi.fn(),
  updateConfig: vi.fn(),
  deleteConfig: vi.fn(),
  getConfigById: vi.fn(),
  clearError: vi.fn()
}

vi.mock('@/stores/discordConfig', () => ({
  useDiscordConfigStore: () => mockDiscordStore
}))

describe('Discord配置面板组件测试', () => {
  let wrapper
  let vuetify

  beforeEach(() => {
    // 设置Pinia和Vuetify
    setActivePinia(createPinia())
    vuetify = createVuetify()
    
    // 重置mock
    vi.clearAllMocks()
    mockDiscordStore.configs = []
    mockDiscordStore.loading = false
    mockDiscordStore.error = null
    mockDiscordStore.configCount = 0
    mockDiscordStore.enabledConfigs = []
  })

  const createWrapper = (props = {}) => {
    return mount(DiscordConfigPanel, {
      props,
      global: {
        plugins: [vuetify]
      }
    })
  }

  describe('组件渲染测试', () => {
    test('应该正确渲染基本结构', () => {
      wrapper = createWrapper()

      // 验证标题
      expect(wrapper.text()).toContain('Discord信号源配置')
      expect(wrapper.text()).toContain('管理Discord Bot配置，监听指定频道的交易信号')
      
      // 验证新建按钮
      const createButton = wrapper.find('[data-testid="create-config-btn"]')
      expect(createButton.exists()).toBe(true)
      expect(createButton.text()).toContain('新建配置')
    })

    test('应该正确显示统计信息', () => {
      mockDiscordStore.configCount = 5
      mockDiscordStore.enabledConfigs = [
        { id: '1', enabled: true },
        { id: '2', enabled: true }
      ]

      wrapper = createWrapper()

      // 验证统计数据
      expect(wrapper.text()).toContain('5') // 总配置数
      expect(wrapper.text()).toContain('2') // 已启用数
      expect(wrapper.text()).toContain('3') // 已禁用数 (5-2)
    })

    test('应该在无配置时显示空状态', () => {
      mockDiscordStore.configs = []
      wrapper = createWrapper()

      expect(wrapper.text()).toContain('暂无Discord配置')
      expect(wrapper.text()).toContain('创建第一个Discord配置来开始监听交易信号')
      
      // 查找所有按钮，空状态的按钮应该是最后一个
      const buttons = wrapper.findAll('button')
      const emptyCreateButton = buttons[buttons.length - 1]
      expect(emptyCreateButton.text()).toContain('创建配置')
    })

    test('应该在加载时显示加载状态', () => {
      mockDiscordStore.loading = true
      mockDiscordStore.configs = []
      
      wrapper = createWrapper()

      expect(wrapper.text()).toContain('加载配置中...')
      expect(wrapper.find('.v-progress-circular').exists()).toBe(true)
    })

    test('应该显示配置列表', () => {
      const mockConfigs = [
        {
          id: 'config-1',
          source_name: '测试配置1',
          enabled: true
        },
        {
          id: 'config-2',
          source_name: '测试配置2',
          enabled: false
        }
      ]
      
      mockDiscordStore.configs = mockConfigs
      wrapper = createWrapper()

      // 验证配置卡片渲染
      const configCards = wrapper.findAll('.mock-discord-config-card')
      expect(configCards).toHaveLength(2)
      expect(configCards[0].text()).toContain('测试配置1')
      expect(configCards[1].text()).toContain('测试配置2')
    })
  })

  describe('用户交互测试', () => {
    test('应该在点击新建按钮时打开对话框', async () => {
      wrapper = createWrapper()

      const createButton = wrapper.find('button')
      await createButton.trigger('click')

      // 验证对话框状态
      expect(wrapper.vm.dialogVisible).toBe(true)
      expect(wrapper.vm.editingConfig).toBeUndefined()
    })

    test('应该在点击刷新按钮时获取配置', async () => {
      wrapper = createWrapper()

      // 清除之前的调用记录（组件创建时可能自动调用了fetchConfigs）
      mockDiscordStore.fetchConfigs.mockClear()

      const refreshButton = wrapper.find('[data-testid="refresh-btn"]')
      await refreshButton.trigger('click')

      expect(mockDiscordStore.fetchConfigs).toHaveBeenCalledOnce()
    })

    test('应该处理配置编辑', async () => {
      const mockConfig = {
        id: 'config-1',
        source_name: '测试配置',
        enabled: true
      }

      mockDiscordStore.configs = [mockConfig]
      wrapper = createWrapper()

      // 模拟编辑事件
      await wrapper.vm.openEditDialog(mockConfig)

      expect(wrapper.vm.dialogVisible).toBe(true)
      expect(wrapper.vm.editingConfig).toEqual(mockConfig)
    })

    test('应该处理配置删除', async () => {
      wrapper = createWrapper()

      // 模拟删除事件
      await wrapper.vm.deleteConfig('config-1')

      expect(wrapper.vm.deleteDialogVisible).toBe(true)
      expect(wrapper.vm.deletingConfigId).toBe('config-1')
    })

    test('应该确认删除配置', async () => {
      mockDiscordStore.deleteConfig.mockResolvedValue(true)
      wrapper = createWrapper()
      
      wrapper.vm.deletingConfigId = 'config-1'
      wrapper.vm.deleteDialogVisible = true

      await wrapper.vm.confirmDelete()

      expect(mockDiscordStore.deleteConfig).toHaveBeenCalledWith('config-1')
      expect(wrapper.vm.deleteDialogVisible).toBe(false)
      expect(wrapper.vm.deletingConfigId).toBe('')
    })
  })

  describe('配置保存测试', () => {
    test('应该成功创建新配置', async () => {
      mockDiscordStore.createConfig.mockResolvedValue(true)
      wrapper = createWrapper()

      const configData = {
        source_name: '新建配置',
        enabled: true,
        token: 'test_token',
        server_ids: ['123456789'],
        channel_ids: ['111111111'],
        author_ids: [],
        allowed_message_types: ['text']
      }

      wrapper.vm.editingConfig = undefined
      wrapper.vm.dialogVisible = true

      await wrapper.vm.saveConfig(configData)

      expect(mockDiscordStore.createConfig).toHaveBeenCalledWith(configData)
      expect(wrapper.vm.dialogVisible).toBe(false)
      expect(wrapper.vm.editingConfig).toBeUndefined()
    })

    test('应该成功更新现有配置', async () => {
      mockDiscordStore.updateConfig.mockResolvedValue(true)
      wrapper = createWrapper()

      const existingConfig = { id: 'config-1', source_name: '原配置' }
      const updateData = {
        source_name: '更新后的配置',
        enabled: false,
        token: 'updated_token',
        server_ids: [],
        channel_ids: [],
        author_ids: [],
        allowed_message_types: ['text']
      }

      wrapper.vm.editingConfig = existingConfig
      wrapper.vm.dialogVisible = true

      await wrapper.vm.saveConfig(updateData)

      expect(mockDiscordStore.updateConfig).toHaveBeenCalledWith('config-1', updateData)
      expect(wrapper.vm.dialogVisible).toBe(false)
      expect(wrapper.vm.editingConfig).toBeUndefined()
    })

    test('应该处理保存失败', async () => {
      mockDiscordStore.createConfig.mockResolvedValue(false)
      wrapper = createWrapper()

      const configData = {
        source_name: '失败的配置',
        enabled: true,
        token: 'test_token',
        server_ids: [],
        channel_ids: [],
        author_ids: [],
        allowed_message_types: ['text']
      }

      wrapper.vm.editingConfig = undefined
      wrapper.vm.dialogVisible = true

      await wrapper.vm.saveConfig(configData)

      expect(mockDiscordStore.createConfig).toHaveBeenCalledWith(configData)
      // 对话框应该保持打开状态
      expect(wrapper.vm.dialogVisible).toBe(true)
    })
  })

  describe('错误处理测试', () => {
    test('应该显示错误信息', () => {
      mockDiscordStore.error = '测试错误信息'
      wrapper = createWrapper()

      expect(wrapper.text()).toContain('测试错误信息')
      
      const errorAlert = wrapper.find('.v-alert')
      expect(errorAlert.exists()).toBe(true)
    })

    test('应该能够关闭错误信息', async () => {
      mockDiscordStore.error = '测试错误信息'
      wrapper = createWrapper()

      const closeButton = wrapper.find('.v-alert .v-btn')
      await closeButton.trigger('click')

      expect(mockDiscordStore.clearError).toHaveBeenCalledOnce()
    })
  })

  describe('生命周期测试', () => {
    test('应该在组件挂载时获取配置', () => {
      wrapper = createWrapper()
      
      expect(mockDiscordStore.fetchConfigs).toHaveBeenCalledOnce()
    })
  })

  describe('配置切换测试', () => {
    test('应该处理配置启用/禁用切换', async () => {
      const mockConfig = {
        id: 'config-1',
        source_name: '测试配置',
        enabled: true
      }

      mockDiscordStore.getConfigById.mockReturnValue(mockConfig)
      wrapper = createWrapper()

      await wrapper.vm.toggleConfig('config-1')

      // 验证打开编辑对话框（因为需要重新输入token）
      expect(mockDiscordStore.getConfigById).toHaveBeenCalledWith('config-1')
      expect(wrapper.vm.editingConfig).toEqual(mockConfig)
      expect(wrapper.vm.dialogVisible).toBe(true)
    })
  })
})
