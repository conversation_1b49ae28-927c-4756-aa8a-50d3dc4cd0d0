/**
 * RecentOrdersTable 组件测试
 * 测试最近订单表格组件的核心功能
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import { createVuetify } from 'vuetify'
import RecentOrdersTable from '@/components/RecentOrdersTable.vue'
import { useOrderStore } from '@/stores/order'
import { useUIStore } from '@/stores/ui'

// 创建 Vuetify 实例
const vuetify = createVuetify()

// 模拟订单数据
const mockOrders = [
  {
    id: 'order_1',
    symbol: 'BTC/USDT',
    side: 'buy',
    quantity: 0.001,
    entry_price: 50000,
    current_price: 51000,
    pnl: 1.0,
    status: 'ACTIVE',
    created_at: '2024-01-01T10:00:00Z'
  },
  {
    id: 'order_2',
    symbol: 'ETH/USDT',
    side: 'sell',
    quantity: 0.1,
    entry_price: 3000,
    current_price: 2950,
    pnl: 5.0,
    status: 'FILLED',
    created_at: '2024-01-01T11:00:00Z'
  },
  {
    id: 'order_3',
    symbol: 'XRP/USDT',
    side: 'buy',
    quantity: 100,
    entry_price: 0.5,
    current_price: 0.52,
    pnl: 2.0,
    status: 'CANCELLED',
    created_at: '2024-01-01T12:00:00Z'
  }
]

describe('RecentOrdersTable', () => {
  let wrapper
  let pinia
  let orderStore
  let uiStore

  beforeEach(() => {
    // 创建新的Pinia实例
    pinia = createPinia()
    setActivePinia(pinia)

    // 获取stores
    orderStore = useOrderStore()
    uiStore = useUIStore()

    // Mock store方法
    vi.spyOn(orderStore, 'closePosition').mockResolvedValue()
    vi.spyOn(uiStore, 'showConfirmDialog').mockResolvedValue(true)
    vi.spyOn(uiStore, 'showSuccess').mockImplementation(() => {})
    vi.spyOn(uiStore, 'showError').mockImplementation(() => {})

    wrapper = mount(RecentOrdersTable, {
      props: {
        orders: mockOrders,
        loading: false,
        itemsPerPage: 5
      },
      global: {
        plugins: [vuetify, pinia]
      }
    })
  })

  afterEach(() => {
    wrapper?.unmount()
    vi.clearAllMocks()
  })

  describe('组件渲染', () => {
    it('应该正确渲染组件', () => {
      expect(wrapper.exists()).toBe(true)
      expect(wrapper.find('.recent-orders-table').exists()).toBe(true)
    })

    it('应该渲染数据表格', () => {
      const dataTable = wrapper.findComponent({ name: 'VDataTable' })
      expect(dataTable.exists()).toBe(true)
    })

    it('应该显示正确的表头', () => {
      const headers = wrapper.vm.headers
      expect(headers).toHaveLength(9)
      expect(headers[0].title).toBe('交易对')
      expect(headers[1].title).toBe('方向')
      expect(headers[2].title).toBe('数量')
      expect(headers[8].title).toBe('操作')
    })

    it('应该渲染订单数据', () => {
      // 检查是否有数据表格内容
      const dataTable = wrapper.findComponent({ name: 'VDataTable' })
      expect(dataTable.props('items')).toEqual(mockOrders)
    })
  })

  describe('数据显示', () => {
    it('应该正确显示交易对信息', () => {
      const symbolCells = wrapper.findAll('.font-weight-medium')
      expect(symbolCells[0].text()).toContain('BTC/USDT')
    })

    it('应该正确显示交易方向', () => {
      // 检查买入方向的显示
      const buyChips = wrapper.findAll('.v-chip')
      const buyChip = buyChips.find(chip => chip.text().includes('做多'))
      expect(buyChip).toBeTruthy()
    })

    it('应该正确显示盈亏状态', () => {
      // 检查盈亏显示
      const pnlElements = wrapper.findAll('.font-mono')
      expect(pnlElements.length).toBeGreaterThan(0)
    })

    it('应该根据状态显示不同的颜色', () => {
      const statusChips = wrapper.findAll('.v-chip')
      expect(statusChips.length).toBeGreaterThan(0)
    })
  })

  describe('用户交互', () => {
    it('应该能够渲染操作按钮', () => {
      // 检查是否有操作按钮
      const actionButtons = wrapper.findAll('button')
      expect(actionButtons.length).toBeGreaterThan(0)
    })

    it('应该能够显示订单详情对话框', () => {
      // 检查是否有对话框组件
      const dialog = wrapper.findComponent({ name: 'VDialog' })
      expect(dialog.exists()).toBe(true)
    })
  })

  describe('加载状态', () => {
    it('应该显示加载状态', async () => {
      await wrapper.setProps({ loading: true })
      const dataTable = wrapper.findComponent({ name: 'VDataTable' })
      expect(dataTable.props('loading')).toBe(true)
    })

    it('应该正确处理加载状态变化', async () => {
      await wrapper.setProps({ loading: false })
      const dataTable = wrapper.findComponent({ name: 'VDataTable' })
      expect(dataTable.props('loading')).toBe(false)
    })
  })

  describe('空状态处理', () => {
    it('应该显示空状态', async () => {
      await wrapper.setProps({ orders: [] })
      
      const emptyState = wrapper.find('.text-center.py-8')
      expect(emptyState.exists()).toBe(true)
      expect(emptyState.text()).toContain('暂无订单数据')
    })
  })

  describe('工具函数', () => {
    it('应该正确格式化交易方向', () => {
      // 通过检查渲染的内容来验证格式化函数
      const buyChips = wrapper.findAll('.v-chip').filter(chip => chip.text().includes('做多'))
      expect(buyChips.length).toBeGreaterThan(0)
    })

    it('应该正确显示状态信息', () => {
      // 检查状态显示
      const statusElements = wrapper.findAll('.v-chip')
      expect(statusElements.length).toBeGreaterThan(0)
    })

    it('应该正确显示盈亏信息', () => {
      // 检查盈亏显示
      const pnlElements = wrapper.findAll('.font-mono')
      expect(pnlElements.length).toBeGreaterThan(0)
    })
  })

  describe('错误处理', () => {
    it('应该正确处理空数据', async () => {
      await wrapper.setProps({ orders: [] })
      expect(wrapper.find('.text-center.py-8').exists()).toBe(true)
    })

    it('应该正确处理加载状态', async () => {
      await wrapper.setProps({ loading: true })
      const dataTable = wrapper.findComponent({ name: 'VDataTable' })
      expect(dataTable.props('loading')).toBe(true)
    })
  })

  describe('响应式行为', () => {
    it('应该响应props变化', async () => {
      const newOrders = [...mockOrders, {
        id: 'order_4',
        symbol: 'ADA/USDT',
        side: 'buy',
        quantity: 50,
        entry_price: 1.0,
        current_price: 1.1,
        pnl: 5.0,
        status: 'ACTIVE',
        created_at: '2024-01-01T13:00:00Z'
      }]
      
      await wrapper.setProps({ orders: newOrders })
      expect(wrapper.props('orders')).toHaveLength(4)
    })

    it('应该响应itemsPerPage变化', async () => {
      await wrapper.setProps({ itemsPerPage: 10 })
      const dataTable = wrapper.findComponent({ name: 'VDataTable' })
      expect(dataTable.props('itemsPerPage')).toBe(10)
    })
  })
})
