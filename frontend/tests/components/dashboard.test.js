/**
 * DashboardView 组件测试
 * 测试仪表盘视图的基本功能
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import { createVuetify } from 'vuetify'
import DashboardView from '@/views/DashboardView.vue'
import { useOrderStore } from '@/stores/order'
import { useAgentStore } from '@/stores/agent'
import { useWebSocketStore } from '@/stores/websocket'
import { useAuthStore } from '@/stores/auth'

// 创建 Vuetify 实例
const vuetify = createVuetify()

// 模拟API响应
const mockApiResponses = {
  orders: [
    {
      id: 'order_1',
      symbol: 'BTC/USDT',
      side: 'buy',
      quantity: 0.001,
      status: 'filled',
      created_at: '2024-01-01T10:00:00Z'
    }
  ],
  agentTasks: [
    {
      id: 'task_1',
      type: 'PARSE_SIGNAL',
      status: 'PROCESSING',
      created_at: '2024-01-01T12:00:00Z'
    }
  ]
}

describe('DashboardView', () => {
  let wrapper
  let pinia

  beforeEach(() => {
    // 创建新的Pinia实例
    pinia = createPinia()
    setActivePinia(pinia)

    // 模拟stores
    const authStore = useAuthStore()
    const orderStore = useOrderStore()
    const agentStore = useAgentStore()
    const wsStore = useWebSocketStore()

    // 模拟认证状态
    authStore.user = { id: 'test-user-id', username: 'testuser' }
    vi.spyOn(authStore, 'isAuthenticated', 'get').mockReturnValue(true)

    // 模拟store方法
    vi.spyOn(orderStore, 'fetchOrders').mockResolvedValue(mockApiResponses.orders)
    vi.spyOn(agentStore, 'fetchPendingActions').mockResolvedValue(mockApiResponses.agentTasks)
    vi.spyOn(wsStore, 'connect').mockResolvedValue(true)

    wrapper = mount(DashboardView, {
      global: {
        plugins: [vuetify, pinia],
        stubs: {
          'router-link': true,
          'router-view': true,
          'LiveLogStream': true,
          'PendingActionsList': true
        }
      }
    })
  })

  afterEach(() => {
    wrapper?.unmount()
    vi.clearAllMocks()
  })

  it('应该正确渲染仪表盘组件', () => {
    expect(wrapper.exists()).toBe(true)
  })

  it('应该显示统计卡片', () => {
    // 检查是否有统计卡片区域
    const statsSection = wrapper.find('[data-testid="stats-cards"]')
    expect(statsSection.exists()).toBe(true)
  })

  it('应该在组件挂载时获取数据', async () => {
    const orderStore = useOrderStore()
    const agentStore = useAgentStore()

    await wrapper.vm.$nextTick()

    expect(orderStore.fetchOrders).toHaveBeenCalled()
    expect(agentStore.fetchPendingActions).toHaveBeenCalled()
  })

  it('应该建立WebSocket连接', async () => {
    const wsStore = useWebSocketStore()

    await wrapper.vm.$nextTick()

    expect(wsStore.connect).toHaveBeenCalled()
  })

  it('应该处理基本的用户交互', async () => {
    // 验证组件能够正常加载
    expect(wrapper.exists()).toBe(true)

    // 检查是否有主要的容器元素
    const mainContainer = wrapper.find('.v-container')
    expect(mainContainer.exists()).toBe(true)
  })
})