/**
 * 信号筛选器功能测试
 * 测试 SignalsView 页面的筛选器功能是否正常工作
 */

import { test, expect } from '@playwright/test'

const API_BASE_URL = 'http://localhost:8000'
const FRONTEND_URL = 'http://localhost:5174'

// 测试用户凭据
const TEST_USER = {
  username: 'testuser',
  password: 'testpass123'
}

test.describe('信号筛选器功能测试', () => {
  let authHeaders = {}

  test.beforeAll(async ({ request }) => {
    // 登录获取认证token
    const loginResponse = await request.post(`${API_BASE_URL}/api/v1/auth/login`, {
      data: {
        username: TEST_USER.username,
        password: TEST_USER.password
      }
    })

    if (loginResponse.ok()) {
      const loginData = await loginResponse.json()
      authHeaders = {
        'Authorization': `Bearer ${loginData.data.access_token}`
      }
    }
  })

  test('筛选器组件应该正确渲染', async ({ page }) => {
    await page.goto(`${FRONTEND_URL}/signals`)
    
    // 等待页面加载
    await page.waitForSelector('[data-testid="signals-view"]')
    
    // 检查筛选器组件是否存在
    await expect(page.locator('[data-testid="signal-filters"]')).toBeVisible()
    
    // 检查各个筛选器控件
    await expect(page.locator('[data-testid="platform-filter"]')).toBeVisible()
    await expect(page.locator('[data-testid="search-input"]')).toBeVisible()
    await expect(page.locator('[data-testid="search-btn"]')).toBeVisible()
    await expect(page.locator('[data-testid="clear-filters-btn"]')).toBeVisible()
  })

  test('平台筛选器应该正常工作', async ({ page }) => {
    await page.goto(`${FRONTEND_URL}/signals`)
    await page.waitForSelector('[data-testid="signals-view"]')
    
    // 选择Discord平台
    await page.locator('[data-testid="platform-filter"]').click()
    await page.locator('text=Discord').click()
    
    // 等待筛选结果
    await page.waitForTimeout(1000)
    
    // 验证URL参数或者检查API调用
    const currentUrl = page.url()
    console.log('当前URL:', currentUrl)
  })

  test('搜索功能应该正常工作', async ({ page }) => {
    await page.goto(`${FRONTEND_URL}/signals`)
    await page.waitForSelector('[data-testid="signals-view"]')
    
    // 输入搜索关键词
    const searchInput = page.locator('[data-testid="search-input"]')
    await searchInput.fill('BTC')
    
    // 点击搜索按钮
    await page.locator('[data-testid="search-btn"]').click()
    
    // 等待搜索结果
    await page.waitForTimeout(1000)
    
    // 验证搜索结果
    console.log('搜索功能测试完成')
  })

  test('处理状态筛选器应该正常工作', async ({ page }) => {
    await page.goto(`${FRONTEND_URL}/signals`)
    await page.waitForSelector('[data-testid="signals-view"]')
    
    // 选择处理状态筛选器
    const statusFilter = page.locator('label:has-text("处理状态")').locator('..').locator('input')
    await statusFilter.click()
    
    // 选择"未处理"选项
    await page.locator('text=未处理').click()
    
    // 等待筛选结果
    await page.waitForTimeout(1000)
    
    console.log('处理状态筛选测试完成')
  })

  test('快速筛选按钮应该正常工作', async ({ page }) => {
    await page.goto(`${FRONTEND_URL}/signals`)
    await page.waitForSelector('[data-testid="signals-view"]')
    
    // 点击"今天"快速筛选
    await page.locator('text=今天').click()
    await page.waitForTimeout(1000)
    
    // 点击"未处理"快速筛选
    await page.locator('text=未处理').click()
    await page.waitForTimeout(1000)
    
    // 点击"高置信度"快速筛选
    await page.locator('text=高置信度').click()
    await page.waitForTimeout(1000)
    
    console.log('快速筛选测试完成')
  })

  test('重置筛选器应该正常工作', async ({ page }) => {
    await page.goto(`${FRONTEND_URL}/signals`)
    await page.waitForSelector('[data-testid="signals-view"]')
    
    // 设置一些筛选条件
    await page.locator('[data-testid="platform-filter"]').click()
    await page.locator('text=Discord').click()
    await page.waitForTimeout(500)
    
    const searchInput = page.locator('[data-testid="search-input"]')
    await searchInput.fill('test')
    await page.waitForTimeout(500)
    
    // 点击重置按钮
    await page.locator('[data-testid="clear-filters-btn"]').click()
    await page.waitForTimeout(1000)
    
    // 验证筛选器已重置
    await expect(searchInput).toHaveValue('')
    
    console.log('重置筛选器测试完成')
  })

  test('置信度范围滑块应该正常工作', async ({ page }) => {
    await page.goto(`${FRONTEND_URL}/signals`)
    await page.waitForSelector('[data-testid="signals-view"]')
    
    // 查找置信度滑块
    const rangeSlider = page.locator('.v-range-slider')
    await expect(rangeSlider).toBeVisible()
    
    // 模拟滑块操作（这里只是检查滑块存在）
    console.log('置信度滑块测试完成')
  })

  test('日期范围选择器应该正常工作', async ({ page }) => {
    await page.goto(`${FRONTEND_URL}/signals`)
    await page.waitForSelector('[data-testid="signals-view"]')
    
    // 查找日期范围输入框
    const dateInput = page.locator('label:has-text("日期范围")').locator('..').locator('input')
    await expect(dateInput).toBeVisible()
    
    // 点击日期输入框
    await dateInput.click()
    await page.waitForTimeout(500)
    
    console.log('日期范围选择器测试完成')
  })

  test('AI解析状态筛选器应该正常工作', async ({ page }) => {
    await page.goto(`${FRONTEND_URL}/signals`)
    await page.waitForSelector('[data-testid="signals-view"]')
    
    // 查找AI解析状态筛选器
    const aiStatusFilter = page.locator('label:has-text("AI解析状态")').locator('..').locator('input')
    await aiStatusFilter.click()
    
    // 选择"解析成功"选项
    await page.locator('text=解析成功').click()
    await page.waitForTimeout(1000)
    
    console.log('AI解析状态筛选测试完成')
  })

  test('消息类型筛选器应该正常工作', async ({ page }) => {
    await page.goto(`${FRONTEND_URL}/signals`)
    await page.waitForSelector('[data-testid="signals-view"]')
    
    // 查找消息类型筛选器
    const messageTypeFilter = page.locator('label:has-text("消息类型")').locator('..').locator('input')
    await messageTypeFilter.click()
    
    // 选择"交易信号"选项
    await page.locator('text=交易信号').click()
    await page.waitForTimeout(1000)
    
    console.log('消息类型筛选测试完成')
  })

  test('LLM服务筛选器应该正常工作', async ({ page }) => {
    await page.goto(`${FRONTEND_URL}/signals`)
    await page.waitForSelector('[data-testid="signals-view"]')
    
    // 查找LLM服务筛选器
    const llmServiceFilter = page.locator('label:has-text("LLM服务")').locator('..').locator('input')
    await llmServiceFilter.click()
    
    // 选择"DeepSeek"选项
    await page.locator('text=DeepSeek').click()
    await page.waitForTimeout(1000)
    
    console.log('LLM服务筛选测试完成')
  })
})
