/**
 * 优化的测试示例
 * 展示如何使用改进的测试基础设施避免fixture重用警告
 */

import { test, expect, TestCleanupManager, TestDataIsolation } from '../setup/test-infrastructure.js'

test.describe('优化的测试示例', () => {
  let cleanupManager
  let dataIsolation

  test.beforeEach(async () => {
    cleanupManager = new TestCleanupManager()
    dataIsolation = new TestDataIsolation()
  })

  test.afterEach(async () => {
    await cleanupManager.executeCleanup()
  })

  test('使用认证用户fixture的示例', async ({ authenticatedUser, optimizedPage }) => {
    const { user, token, factory } = authenticatedUser
    
    // 注册清理任务
    cleanupManager.registerCleanup(async () => {
      console.log(`🧹 清理用户: ${user.username}`)
    })

    // 导航到应用
    await optimizedPage.goto('/')
    
    // 验证用户已登录
    await expect(optimizedPage.locator('[data-testid="user-menu"]')).toBeVisible()
    
    console.log(`✅ 测试完成，用户: ${user.username}`)
  })

  test('使用testDataFactory的示例', async ({ testDataFactory, optimizedPage }) => {
    // 创建测试数据
    const username = dataIsolation.generateUsername('example_user')
    const user = await testDataFactory.createRealUser({
      username,
      password: 'ExamplePassword123!'
    })

    // 注册清理任务
    cleanupManager.registerCleanup(async () => {
      console.log(`🧹 清理示例用户: ${username}`)
    })

    // 登录用户
    const loginResult = await testDataFactory.loginRealUser({
      username,
      password: 'ExamplePassword123!'
    })

    // 导航并验证
    await optimizedPage.goto('/dashboard')
    await expect(optimizedPage).toHaveURL('/dashboard')
    
    console.log(`✅ 示例测试完成，用户: ${username}`)
  })

  test('错误处理和重试示例', async ({ optimizedPage }) => {
    // 使用重试机制的网络请求
    let attempts = 0
    const maxAttempts = 3

    while (attempts < maxAttempts) {
      try {
        await optimizedPage.goto('/', { timeout: 10000 })
        break
      } catch (error) {
        attempts++
        if (attempts >= maxAttempts) {
          throw error
        }
        console.log(`⚠️ 重试第 ${attempts} 次...`)
        await optimizedPage.waitForTimeout(1000 * attempts)
      }
    }

    await expect(optimizedPage).toHaveURL('/')
  })

  test('服务状态检查示例', async ({ optimizedPage }) => {
    // 在测试开始前检查服务状态
    const { TestEnvironmentChecker } = await import('../setup/test-infrastructure.js')
    const serviceStatus = await TestEnvironmentChecker.checkServices()
    TestEnvironmentChecker.logServiceStatus(serviceStatus)

    // 确保所有服务都可用
    const unavailableServices = serviceStatus.filter(s => s.status !== 'available')
    if (unavailableServices.length > 0) {
      test.skip('跳过测试：必要服务不可用')
    }

    await optimizedPage.goto('/')
    await expect(optimizedPage).toHaveURL('/')
  })
})

/**
 * 传统测试的迁移示例
 * 展示如何从旧的fixture模式迁移到新的基础设施
 */
test.describe('迁移示例', () => {
  // ❌ 旧的方式（会产生fixture重用警告）
  /*
  let testDataFactory
  let authToken

  test.beforeAll(async ({ request }) => {
    testDataFactory = createTestDataFactory(request)
    // ... 创建用户和获取token
  })
  */

  // ✅ 新的方式（避免fixture重用警告）
  test('迁移后的测试', async ({ authenticatedUser, optimizedPage }) => {
    const { user, token } = authenticatedUser
    
    // 使用认证用户进行测试
    await optimizedPage.goto('/orders')
    await expect(optimizedPage).toHaveURL('/orders')
    
    // 验证用户特定的内容
    await expect(optimizedPage.locator('[data-testid="orders-table"]')).toBeVisible()
  })
})

/**
 * 性能优化示例
 */
test.describe('性能优化示例', () => {
  test('并行测试数据创建', async ({ testDataFactory }) => {
    const dataIsolation = new TestDataIsolation()
    
    // 并行创建多个测试用户
    const userPromises = Array.from({ length: 3 }, (_, i) => 
      testDataFactory.createRealUser({
        username: dataIsolation.generateUsername(`parallel_user_${i}`),
        password: 'ParallelPassword123!'
      })
    )

    const users = await Promise.all(userPromises)
    
    expect(users).toHaveLength(3)
    users.forEach(user => {
      expect(user.username).toMatch(/parallel_user_\d+_test_/)
    })
  })

  test('缓存测试数据', async ({ testDataFactory }) => {
    // 使用缓存避免重复创建相同的测试数据
    const cacheKey = 'cached_test_user'
    
    if (!testDataFactory.cache) {
      testDataFactory.cache = new Map()
    }

    let user = testDataFactory.cache.get(cacheKey)
    if (!user) {
      user = await testDataFactory.createRealUser({
        username: `cached_user_${Date.now()}`,
        password: 'CachedPassword123!'
      })
      testDataFactory.cache.set(cacheKey, user)
    }

    expect(user).toBeDefined()
    expect(user.username).toMatch(/cached_user_/)
  })
})
