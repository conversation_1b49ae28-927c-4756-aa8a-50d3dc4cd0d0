/**
 * 统一健康检查API测试
 * 
 * 根据PLAYWRIGHT_TESTING_STRATEGY_ANALYSIS文档建议创建
 * 统一前后端健康检查API测试
 */

import { test, expect } from '@playwright/test'
import { createTestDataFactory } from './test-data-factory.js'

let testDataFactory

test.describe('统一健康检查API测试套件', () => {
  test.beforeAll(async () => {
    // 避免在beforeAll中使用fixture，防止重用警告
    console.log('🏥 健康检查API测试初始化')
  })

  test.beforeEach(async ({ request }) => {
    if (!testDataFactory) {
      testDataFactory = createTestDataFactory(request)
    }
  })

  test.describe('基础健康检查', () => {
    test('应该返回系统健康状态', async ({ request }) => {
      const response = await request.get(`${testDataFactory.baseURL}/api/v1/health`)

      expect(response.ok()).toBeTruthy()
      
      const data = await response.json()

      // 验证新的标准化响应结构
      expect(data).toHaveProperty('success')
      expect(data.success).toBe(true)
      expect(data).toHaveProperty('message')
      expect(data).toHaveProperty('data')
      expect(data).toHaveProperty('error_code')
      expect(data.error_code).toBeNull()

      // 验证健康检查数据
      expect(data.data).toHaveProperty('status')
      expect(data.data).toHaveProperty('timestamp')

      // 验证健康状态值
      expect(['healthy', 'degraded', 'unhealthy']).toContain(data.data.status)

      // 验证时间戳格式
      expect(typeof data.data.timestamp).toBe('string')
      const timestamp = new Date(data.data.timestamp)
      expect(timestamp.getTime()).not.toBeNaN()
    })

    test('应该在合理时间内响应', async ({ request }) => {
      const startTime = Date.now()
      
      const response = await request.get(`${testDataFactory.baseURL}/api/v1/health`)
      
      const responseTime = Date.now() - startTime
      
      expect(response.ok()).toBeTruthy()
      expect(responseTime).toBeLessThan(5000) // 5秒内响应
    })

    test('应该处理并发健康检查请求', async ({ request }) => {
      const concurrentRequests = Array.from({ length: 10 }, () =>
        request.get(`${testDataFactory.baseURL}/api/v1/health`)
      )

      const responses = await Promise.all(concurrentRequests)

      responses.forEach(response => {
        expect(response.ok()).toBeTruthy()
      })
    })

    test('健康检查不应该要求认证', async ({ request }) => {
      // 健康检查通常是公开的，不需要认证
      const response = await request.get(`${testDataFactory.baseURL}/api/v1/health`)

      expect(response.ok()).toBeTruthy()
    })
  })

  test.describe('系统状态检查', () => {
    test('应该返回详细的系统状态', async ({ request }) => {
      const response = await request.get(`${testDataFactory.baseURL}/api/v1/status`)

      if (response.ok()) {
        const data = await response.json()
        
        // 验证新的标准化响应结构
        expect(data).toHaveProperty('success')
        expect(data.success).toBe(true)
        expect(data).toHaveProperty('data')
        
        // 验证系统状态字段
        const statusData = data.data
        expect(statusData).toHaveProperty('uptime')
        expect(statusData).toHaveProperty('memory_usage_mb')
        
        // 验证数据类型
        expect(typeof statusData.uptime).toBe('number')
        expect(typeof statusData.memory_usage_mb).toBe('number')
        
        // 验证合理的数值范围
        expect(statusData.uptime).toBeGreaterThan(0)
        expect(statusData.memory_usage_mb).toBeGreaterThan(0)
        expect(statusData.memory_usage_mb).toBeLessThan(10000) // 假设不超过10GB
      } else if (response.status() === 404) {
        // 如果状态接口未实现，跳过测试
        test.skip()
      } else {
        expect(response.ok()).toBeTruthy()
      }
    })

    test('应该包含版本信息', async ({ request }) => {
      const response = await request.get(`${testDataFactory.baseURL}/api/v1/status`)

      if (response.ok()) {
        const data = await response.json()
        
        // 可能包含版本信息
        if (data.data.version) {
          expect(typeof data.data.version).toBe('string')
          expect(data.data.version.length).toBeGreaterThan(0)
        }
        
        // 可能包含环境信息
        if (data.data.environment) {
          expect(['development', 'testing', 'staging', 'production']).toContain(data.data.environment)
        }
      } else if (response.status() === 404) {
        test.skip()
      }
    })

    test('应该监控关键组件状态', async ({ request }) => {
      const response = await request.get(`${testDataFactory.baseURL}/api/v1/status`)

      if (response.ok()) {
        const data = await response.json()
        
        // 可能包含数据库状态
        if (data.data.database) {
          expect(['connected', 'disconnected', 'error']).toContain(data.data.database.status)
        }
        
        // 可能包含Redis状态
        if (data.data.redis) {
          expect(['connected', 'disconnected', 'error']).toContain(data.data.redis.status)
        }
        
        // 可能包含外部API状态
        if (data.data.external_apis) {
          expect(typeof data.data.external_apis).toBe('object')
        }
      } else if (response.status() === 404) {
        test.skip()
      }
    })
  })

  test.describe('就绪状态检查', () => {
    test('应该检查服务就绪状态', async ({ request }) => {
      // 尝试访问就绪检查端点
      const endpoints = [
        '/api/v1/ready',
        '/api/v1/readiness',
        '/ready',
        '/readiness'
      ]

      let foundEndpoint = false

      for (const endpoint of endpoints) {
        const response = await request.get(`${testDataFactory.baseURL}${endpoint}`)
        
        if (response.ok()) {
          foundEndpoint = true
          const data = await response.json()
          
          // 验证就绪状态响应
          expect(data).toHaveProperty('status')
          
          if (data.ready !== undefined) {
            expect(typeof data.ready).toBe('boolean')
          }
          
          break
        }
      }

      if (!foundEndpoint) {
        // 如果没有专门的就绪检查端点，使用健康检查
        const response = await request.get(`${testDataFactory.baseURL}/api/v1/health`)
        expect(response.ok()).toBeTruthy()
      }
    })

    test('应该验证依赖服务可用性', async ({ request }) => {
      const response = await request.get(`${testDataFactory.baseURL}/api/v1/health`)

      if (response.ok()) {
        const data = await response.json()
        
        // 如果系统健康，依赖服务应该可用
        if (data.data.status === 'healthy') {
          // 验证可以访问主要API端点
          const authToken = await testDataFactory.loginUser()
          
          const ordersResponse = await request.get(`${testDataFactory.baseURL}/api/v1/orders`, {
            headers: testDataFactory.getAuthHeaders(authToken)
          })
          
          // 订单API应该可访问（即使返回空列表）
          // 200表示成功，401表示需要认证，403表示权限不足，404表示端点不存在，都是合理的响应
          expect([200, 401, 403, 404]).toContain(ordersResponse.status())
        }
      }
    })
  })

  test.describe('性能监控', () => {
    test('应该监控响应时间', async ({ request }) => {
      const measurements = []
      
      // 进行多次测量
      for (let i = 0; i < 5; i++) {
        const startTime = Date.now()
        
        const response = await request.get(`${testDataFactory.baseURL}/api/v1/health`)
        
        const responseTime = Date.now() - startTime
        measurements.push(responseTime)
        
        expect(response.ok()).toBeTruthy()
      }
      
      // 计算平均响应时间
      const averageTime = measurements.reduce((a, b) => a + b, 0) / measurements.length
      
      // 健康检查应该很快
      expect(averageTime).toBeLessThan(1000) // 平均1秒内
      
      // 响应时间应该相对稳定
      const maxTime = Math.max(...measurements)
      const minTime = Math.min(...measurements)
      const variance = maxTime - minTime
      
      expect(variance).toBeLessThan(5000) // 变化不超过5秒
    })

    test('应该处理高并发健康检查', async ({ request }) => {
      const concurrentCount = 50
      const startTime = Date.now()
      
      const concurrentRequests = Array.from({ length: concurrentCount }, () =>
        request.get(`${testDataFactory.baseURL}/api/v1/health`)
      )

      const responses = await Promise.all(concurrentRequests)
      const totalTime = Date.now() - startTime
      
      // 所有请求都应该成功
      responses.forEach(response => {
        expect(response.ok()).toBeTruthy()
      })
      
      // 并发处理应该高效
      const averageTimePerRequest = totalTime / concurrentCount
      expect(averageTimePerRequest).toBeLessThan(1000) // 平均每个请求1秒内
    })
  })

  test.describe('错误处理', () => {
    test('应该处理无效的健康检查请求', async ({ request }) => {
      // 测试无效的HTTP方法
      const invalidMethods = ['POST', 'PUT', 'DELETE', 'PATCH']
      
      for (const method of invalidMethods) {
        try {
          const response = await request.fetch(`${testDataFactory.baseURL}/api/v1/health`, {
            method
          })
          
          // 应该返回方法不允许或其他适当错误
          expect([405, 404, 400]).toContain(response.status())
        } catch (error) {
          // 某些方法可能直接被拒绝
          expect(error).toBeDefined()
        }
      }
    })

    test('应该处理恶意请求', async ({ request }) => {
      const edgeCaseData = testDataFactory.generateEdgeCaseData()
      
      // 测试带有恶意参数的请求
      const maliciousParams = [
        `?param=${encodeURIComponent(edgeCaseData.xssAttempt)}`,
        `?param=${encodeURIComponent(edgeCaseData.sqlInjection)}`,
        `?param=${encodeURIComponent(edgeCaseData.veryLongString)}`
      ]
      
      for (const param of maliciousParams) {
        const response = await request.get(`${testDataFactory.baseURL}/api/v1/health${param}`)
        
        // 应该安全处理恶意参数
        expect([200, 400, 404]).toContain(response.status())
        
        if (response.ok()) {
          const data = await response.json()
          // 验证新的标准化API响应格式
          expect(data).toHaveProperty('success')
          expect(data).toHaveProperty('data')
          expect(data.data).toHaveProperty('status')
        }
      }
    })

    test('应该在系统压力下保持可用', async ({ request }) => {
      // 模拟系统压力
      const heavyRequests = Array.from({ length: 100 }, () =>
        request.get(`${testDataFactory.baseURL}/api/v1/health`)
      )

      const responses = await Promise.allSettled(heavyRequests)
      
      // 大部分请求应该成功
      const successfulResponses = responses.filter(
        r => r.status === 'fulfilled' && r.value.ok()
      )
      
      // 至少80%的请求应该成功
      expect(successfulResponses.length).toBeGreaterThan(80)
    })
  })
})
