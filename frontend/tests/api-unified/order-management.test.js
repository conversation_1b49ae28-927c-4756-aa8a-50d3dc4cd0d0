/**
 * 订单管理API测试
 * 测试订单创建、查询、更新、关闭等API端点
 */

import { test, expect } from '@playwright/test'
import { createTestDataFactory } from './test-data-factory.js'

test.describe('订单管理API测试', () => {
  let testDataFactory
  let authToken
  let testUser

  test.beforeAll(async () => {
    // 避免在beforeAll中使用fixture，防止重用警告
    console.log('📊 订单管理API测试初始化')
  })

  test.beforeEach(async ({ request }) => {
    if (!testDataFactory) {
      testDataFactory = createTestDataFactory(request)

      try {
        // 使用demo用户进行登录，这是系统中已存在的用户
        const credentials = { username: 'demo', password: 'password123' }
        console.log('🔍 订单管理测试登录数据:', JSON.stringify(credentials, null, 2))

        // 后端使用OAuth2PasswordRequestForm，需要发送表单数据
        const formData = new URLSearchParams()
        formData.append('username', credentials.username)
        formData.append('password', credentials.password)

        const response = await request.post(`${testDataFactory.baseURL}/api/v1/auth/login`, {
          data: formData.toString(),
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
          }
        })

        if (response.ok()) {
          const data = await response.json()
          authToken = data.access_token || data.token
          testUser = { username: 'demo', id: 'demo-user-id' }
          if (!authToken) {
            throw new Error('登录响应中未找到有效token')
          }

          // 清理所有活跃订单，避免并发订单限制问题
          await testDataFactory.cleanupActiveOrders(authToken, request)

          // 等待一小段时间确保清理完成
          await new Promise(resolve => setTimeout(resolve, 100))
        } else {
          const errorData = await response.json().catch(() => ({}))
          console.error('订单管理测试登录失败:', JSON.stringify(errorData, null, 2))
          throw new Error(`登录失败: ${response.status()} ${response.statusText()}`)
        }
      } catch (error) {
        console.error('订单管理测试登录失败:', error.message)
        throw error
      }
    }
  })

  test.afterAll(async () => {
    if (testDataFactory && authToken) {
      await testDataFactory.smartCleanup(authToken)
    }
  })

  test.describe('订单基础操作', () => {
    test('POST /api/v1/orders - 创建市价订单', async ({ request }) => {
      // 确保测试开始前清理所有订单
      await testDataFactory.cleanupActiveOrders(authToken, request)

      const orderData = {
        symbol: 'BTC/USDT',
        side: 'BUY',
        quantity: 0.001,
        order_type: 'MARKET'
      }

      const response = await request.post(`${testDataFactory.baseURL}/api/v1/orders`, {
        data: orderData,
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        }
      })

      expect(response.status()).toBe(200)
      
      const responseData = await response.json()
      expect(responseData.success).toBe(true)
      expect(responseData.data).toBeDefined()
      expect(responseData.data.symbol).toBe('BTC/USDT')
      expect(responseData.data.side).toBe('BUY')
      expect(responseData.data.order_type).toBe('MARKET')

      // 保存订单ID用于后续测试
      testDataFactory.createdResources.orders.push(responseData.data)
    })

    test('POST /api/v1/orders - 创建限价订单', async ({ request }) => {
      // 先清理现有的活跃订单以避免并发限制
      await testDataFactory.cleanupActiveOrders(authToken, request)

      const orderData = {
        symbol: 'ETH/USDT',
        side: 'SELL',
        quantity: 0.1,
        order_type: 'LIMIT',
        price: 3000.00
      }

      const response = await request.post(`${testDataFactory.baseURL}/api/v1/orders`, {
        data: orderData,
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        }
      })

      expect(response.status()).toBe(200)
      
      const responseData = await response.json()
      expect(responseData.success).toBe(true)
      expect(responseData.data.symbol).toBe('ETH/USDT')
      expect(responseData.data.side).toBe('SELL')
      expect(responseData.data.order_type).toBe('LIMIT')
      expect(responseData.data.price).toBe(3000.0) // API返回数字格式，不是字符串

      testDataFactory.createdResources.orders.push(responseData.data)
    })

    test('GET /api/v1/orders - 获取订单列表', async ({ request }) => {
      const response = await request.get(`${testDataFactory.baseURL}/api/v1/orders`, {
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      })

      expect(response.status()).toBe(200)
      
      const responseData = await response.json()
      expect(responseData.success).toBe(true)

      // 处理不同的响应格式
      let orders = []
      if (responseData.data && Array.isArray(responseData.data.orders)) {
        orders = responseData.data.orders  // 新的API格式: {success: true, data: {orders: [...], total: 6}}
      } else if (Array.isArray(responseData.data)) {
        orders = responseData.data  // 旧格式: {success: true, data: [...]}
      } else if (Array.isArray(responseData)) {
        orders = responseData  // 直接数组格式
      } else {
        // 如果data不是数组，可能是空的或者格式不同
        console.log('订单列表响应格式:', JSON.stringify(responseData, null, 2))
        orders = []
      }

      expect(Array.isArray(orders)).toBe(true)

      // 验证订单结构
      if (orders.length > 0) {
        const order = orders[0]
        expect(order).toHaveProperty('id')
        expect(order).toHaveProperty('symbol')
        expect(order).toHaveProperty('side')
        expect(order).toHaveProperty('quantity')
        expect(order).toHaveProperty('status')
        expect(order).toHaveProperty('created_at')
      }
    })

    test('GET /api/v1/orders/{id} - 获取单个订单详情', async ({ request }) => {
      // 确保测试开始前清理所有订单
      await testDataFactory.cleanupActiveOrders(authToken, request)

      // 先创建一个订单
      const order = await testDataFactory.createRealOrder(authToken, testUser.id, {
        symbol: 'ADA/USDT',
        side: 'BUY',
        quantity: 100,
        order_type: 'LIMIT',
        price: 0.45
      })

      const response = await request.get(`${testDataFactory.baseURL}/api/v1/orders/${order.id}`, {
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      })

      expect(response.status()).toBe(200)
      
      const responseData = await response.json()
      expect(responseData.success).toBe(true)
      expect(responseData.data.id).toBe(order.id)
      expect(responseData.data.symbol).toBe('ADA/USDT')
      expect(responseData.data.side).toBe('BUY')
    })
  })

  test.describe('订单状态管理', () => {
    test('POST /api/v1/orders/{id}/close - 关闭订单', async ({ request }) => {
      // 先清理现有的活跃订单以避免并发限制
      await testDataFactory.cleanupActiveOrders(authToken, request)

      // 先创建一个订单，传递请求上下文
      const order = await testDataFactory.createRealOrder(authToken, testUser.id, {
        symbol: 'DOT/USDT',
        side: 'BUY',
        quantity: 10,
        order_type: 'LIMIT',
        price: 8.50
      }, request)

      const response = await request.post(`${testDataFactory.baseURL}/api/v1/orders/${order.id}/close`, {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        }
      })

      expect(response.status()).toBe(200)
      
      const responseData = await response.json()
      expect(responseData.success).toBe(true)
      expect(responseData.data.status).toBe('CLOSED')
    })

    test('PATCH /api/v1/orders/{id} - 更新订单状态', async ({ request }) => {
      // 先清理现有的活跃订单以避免并发限制
      await testDataFactory.cleanupActiveOrders(authToken, request)

      // 先创建一个订单，传递请求上下文
      const order = await testDataFactory.createRealOrder(authToken, testUser.id, {
        symbol: 'LINK/USDT',
        side: 'SELL',
        quantity: 5,
        order_type: 'LIMIT',
        price: 15.00
      }, request)

      // 后端PATCH API期望的是简单的状态字符串，使用数据库约束允许的状态
      const statusUpdate = 'closed'  // 使用允许的状态：'active', 'closed', 'failed', 'cancelled'

      const response = await request.patch(`${testDataFactory.baseURL}/api/v1/orders/${order.id}`, {
        data: statusUpdate,
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        }
      })

      expect(response.status()).toBe(200)

      const responseData = await response.json()
      expect(responseData.success).toBe(true)
      expect(responseData.data.status).toBe('CLOSED') // 后端返回大写状态
      // 注意：PATCH API只更新状态，closed状态会自动设置closed_at时间
    })

    test('DELETE /api/v1/orders/{id} - 删除订单', async ({ request }) => {
      // 确保测试开始前清理所有订单
      await testDataFactory.cleanupActiveOrders(authToken, request)

      // 先创建一个订单
      const order = await testDataFactory.createRealOrder(authToken, testUser.id, {
        symbol: 'UNI/USDT',
        side: 'BUY',
        quantity: 20,
        order_type: 'MARKET'
      })

      const response = await request.delete(`${testDataFactory.baseURL}/api/v1/orders/${order.id}`, {
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      })

      expect(response.status()).toBe(200)
      
      const responseData = await response.json()
      expect(responseData.success).toBe(true)

      // 验证订单已被删除
      const getResponse = await request.get(`${testDataFactory.baseURL}/api/v1/orders/${order.id}`, {
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      })
      expect(getResponse.status()).toBe(404)
    })
  })

  test.describe('订单查询和过滤', () => {
    test('GET /api/v1/orders?symbol=BTC/USDT - 按交易对过滤', async ({ request }) => {
      // 先清理现有的活跃订单以避免并发限制，传递请求上下文
      await testDataFactory.cleanupActiveOrders(authToken, request)

      // 创建不同交易对的订单，传递请求上下文
      await testDataFactory.createRealOrder(authToken, testUser.id, {
        symbol: 'BTC/USDT',
        side: 'BUY',
        quantity: 0.001,
        order_type: 'MARKET'
      }, request)

      await testDataFactory.createRealOrder(authToken, testUser.id, {
        symbol: 'ETH/USDT',
        side: 'BUY',
        quantity: 0.01,
        order_type: 'MARKET'
      }, request)

      const response = await request.get(`${testDataFactory.baseURL}/api/v1/orders?symbol=BTC/USDT`, {
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      })

      expect(response.status()).toBe(200)

      const responseData = await response.json()
      expect(responseData.success).toBe(true)

      // 验证返回的数据格式和内容
      expect(responseData.data).toBeDefined()
      expect(responseData.data.orders).toBeDefined()
      expect(Array.isArray(responseData.data.orders)).toBe(true)

      // 验证所有返回的订单都是BTC/USDT
      if (responseData.data.orders.length > 0) {
        responseData.data.orders.forEach(order => {
          expect(order.symbol).toBe('BTC/USDT')
        })
      }
    })

    test('GET /api/v1/orders?status=ACTIVE - 按状态过滤', async ({ request }) => {
      const response = await request.get(`${testDataFactory.baseURL}/api/v1/orders?status=ACTIVE`, {
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      })

      expect(response.status()).toBe(200)
      
      const responseData = await response.json()
      expect(responseData.success).toBe(true)
      
      // 验证所有返回的订单都是ACTIVE状态
      let orders = []
      if (responseData.data && Array.isArray(responseData.data.orders)) {
        orders = responseData.data.orders  // 新的API格式
      } else if (Array.isArray(responseData.data)) {
        orders = responseData.data  // 旧格式
      } else if (Array.isArray(responseData)) {
        orders = responseData  // 直接数组格式
      }

      orders.forEach(order => {
        expect(order.status).toBe('ACTIVE')
      })
    })

    test('GET /api/v1/orders?limit=5&offset=0 - 分页查询', async ({ request }) => {
      const response = await request.get(`${testDataFactory.baseURL}/api/v1/orders?limit=5&offset=0`, {
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      })

      expect(response.status()).toBe(200)
      
      const responseData = await response.json()
      expect(responseData.success).toBe(true)

      // 处理不同的响应格式
      let orders = []
      if (responseData.data && Array.isArray(responseData.data.orders)) {
        orders = responseData.data.orders  // 新的API格式
      } else if (Array.isArray(responseData.data)) {
        orders = responseData.data  // 旧格式
      } else if (Array.isArray(responseData)) {
        orders = responseData  // 直接数组格式
      }

      expect(orders.length).toBeLessThanOrEqual(5)
    })
  })

  test.describe('订单验证和错误处理', () => {
    test('创建订单时验证必需字段', async ({ request }) => {
      const invalidOrderData = {
        symbol: 'BTC/USDT',
        // 缺少side, quantity, order_type
      }

      const response = await request.post(`${testDataFactory.baseURL}/api/v1/orders`, {
        data: invalidOrderData,
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        }
      })

      expect(response.status()).toBe(422) // 验证错误
      
      const responseData = await response.json()
      expect(responseData.success).toBe(false)
      // 检查错误信息，可能在error.message或detail字段中
      const errorMessage = responseData.error?.message || responseData.detail || responseData.message || ''
      expect(errorMessage).toBeTruthy() // 确保有错误信息
    })

    test('限价订单必须提供价格', async ({ request }) => {
      const invalidOrderData = {
        symbol: 'BTC/USDT',
        side: 'BUY',
        quantity: 0.001,
        order_type: 'LIMIT'
        // 缺少price
      }

      const response = await request.post(`${testDataFactory.baseURL}/api/v1/orders`, {
        data: invalidOrderData,
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        }
      })

      // 后端可能返回400（请求错误）而不是422
      expect([400, 422]).toContain(response.status())

      const responseData = await response.json()
      expect(responseData.success).toBe(false)
    })

    test('无效订单ID返回404', async ({ request }) => {
      const response = await request.get(`${testDataFactory.baseURL}/api/v1/orders/invalid-order-id`, {
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      })

      // 后端可能返回400（格式错误）而不是404
      expect([400, 404]).toContain(response.status())
    })

    test('未认证用户无法访问订单API', async ({ request }) => {
      const response = await request.get(`${testDataFactory.baseURL}/api/v1/orders`)
      expect(response.status()).toBe(403) // 后端返回403而不是401
    })
  })
})
