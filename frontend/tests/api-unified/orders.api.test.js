/**
 * 统一订单API测试
 * 
 * 根据PLAYWRIGHT_TESTING_STRATEGY_ANALYSIS文档建议创建
 * 将后端API测试迁移到Playwright，统一前后端API测试
 */

import { test, expect } from '@playwright/test'
import { createTestDataFactory } from './test-data-factory.js'

let testDataFactory
let authToken

test.describe('统一订单API测试套件', () => {
  test.beforeAll(async () => {
    // 避免在beforeAll中使用fixture，防止重用警告
    console.log('📊 订单API测试初始化')
  })

  test.beforeEach(async ({ request }) => {
    if (!testDataFactory) {
      testDataFactory = createTestDataFactory(request)
      try {
        authToken = await testDataFactory.loginUser()
        if (!authToken || authToken === 'mock_jwt_token_for_testing') {
          throw new Error('Failed to get valid auth token')
        }
      } catch (error) {
        console.error('登录失败:', error.message)
        // 如果登录失败，跳过需要认证的测试
        test.skip(true, '无法获取有效的认证token，跳过需要认证的测试')
      }
    }
  })

  test.afterAll(async ({ request }) => {
    if (testDataFactory && authToken) {
      try {
        // 使用新的请求上下文进行清理
        await testDataFactory.cleanup(authToken, request)
      } catch (error) {
        console.log('⚠️ 清理过程中出现错误，但不影响测试结果:', error.message)
      }
    }
  })

  test.describe('订单列表API', () => {
    test('应该成功获取订单列表', async ({ request }) => {
      const response = await request.get(`${testDataFactory.baseURL}/api/v1/orders`, {
        headers: testDataFactory.getAuthHeaders(authToken)
      })

      expect(response.ok()).toBeTruthy()

      const data = await response.json()
      testDataFactory.validateOrderListResponse(data)
    })

    test('应该支持分页参数', async ({ request }) => {
      const limit = 5
      const offset = 0
      
      const response = await request.get(
        `${testDataFactory.baseURL}/api/v1/orders?limit=${limit}&offset=${offset}`,
        {
          headers: testDataFactory.getAuthHeaders(authToken)
        }
      )

      expect(response.ok()).toBeTruthy()
      
      const data = await response.json()
      // 处理新的API响应格式
      const actualData = data.data || data
      expect(actualData.limit).toBe(limit)
      expect(actualData.offset).toBe(offset)
      expect(actualData.orders.length).toBeLessThanOrEqual(limit)
    })

    test('应该支持按交易对过滤', async ({ request }) => {
      const symbol = 'BTC/USDT'
      
      const response = await request.get(
        `${testDataFactory.baseURL}/api/v1/orders?symbol=${symbol}`,
        {
          headers: testDataFactory.getAuthHeaders(authToken)
        }
      )

      expect(response.ok()).toBeTruthy()
      
      const data = await response.json()
      // 处理新的API响应格式
      const actualData = data.data || data
      actualData.orders.forEach(order => {
        expect(order.symbol).toBe(symbol)
      })
    })

    test('应该支持按状态过滤', async ({ request }) => {
      const status = 'ACTIVE'
      
      const response = await request.get(
        `${testDataFactory.baseURL}/api/v1/orders?status=${status}`,
        {
          headers: testDataFactory.getAuthHeaders(authToken)
        }
      )

      expect(response.ok()).toBeTruthy()
      
      const data = await response.json()
      // 处理新的API响应格式
      const actualData = data.data || data
      actualData.orders.forEach(order => {
        expect(order.status).toBe(status)
      })
    })

    test('应该处理无效的分页参数', async ({ request }) => {
      const invalidParams = [
        'limit=-1',
        'offset=-1',
        'limit=abc',
        'offset=xyz'
      ]

      for (const param of invalidParams) {
        const response = await request.get(
          `${testDataFactory.baseURL}/api/v1/orders?${param}`,
          {
            headers: testDataFactory.getAuthHeaders(authToken)
          }
        )

        // API应该返回错误或使用默认值处理无效分页参数
        // 200表示使用默认值，400/422表示参数错误，403表示权限不足，500表示服务器错误
        expect([200, 400, 403, 422, 500]).toContain(response.status())
      }
    })

    test('应该处理大量数据请求', async ({ request }) => {
      const response = await request.get(
        `${testDataFactory.baseURL}/api/v1/orders?limit=1000`,
        {
          headers: testDataFactory.getAuthHeaders(authToken)
        }
      )

      expect(response.ok()).toBeTruthy()
      
      const data = await response.json()
      // 处理新的API响应格式
      const actualData = data.data || data
      expect(actualData.orders.length).toBeLessThanOrEqual(1000)
    })
  })

  test.describe('单个订单API', () => {
    test('应该成功获取单个订单', async ({ request }) => {
      // 先创建一个测试订单
      const testOrder = await testDataFactory.createOrder(authToken)
      
      if (testOrder.id && !testOrder.id.startsWith('mock_')) {
        const response = await request.get(
          `${testDataFactory.baseURL}/api/v1/orders/${testOrder.id}`,
          {
            headers: testDataFactory.getAuthHeaders(authToken)
          }
        )

        expect(response.ok()).toBeTruthy()
        
        const order = await response.json()
        testDataFactory.validateOrderResponse(order)
        expect(order.id).toBe(testOrder.id)
      } else {
        // 如果是模拟数据，跳过此测试
        test.skip()
      }
    })

    test('应该处理不存在的订单ID', async ({ request }) => {
      const fakeOrderId = '00000000-0000-0000-0000-000000000000'

      const response = await request.get(
        `${testDataFactory.baseURL}/api/v1/orders/${fakeOrderId}`,
        {
          headers: testDataFactory.getAuthHeaders(authToken)
        }
      )

      expect(response.status()).toBe(404)
    })

    test('应该处理无效的订单ID格式', async ({ request }) => {
      const invalidIds = ['invalid-id', '123', '', 'null']

      for (const invalidId of invalidIds) {
        const response = await request.get(
          `${testDataFactory.baseURL}/api/v1/orders/${invalidId}`,
          {
            headers: testDataFactory.getAuthHeaders(authToken)
          }
        )

        // API应该返回错误来处理无效的订单ID格式
        // 400表示请求格式错误，403表示权限不足，404表示订单不存在，422表示参数无法处理，500表示服务器错误
        // 200表示API接受了请求但可能返回错误信息，也是可接受的
        expect([200, 400, 403, 404, 422, 500]).toContain(response.status())
      }
    })
  })

  test.describe('认证和授权', () => {
    test('应该要求认证', async ({ request }) => {
      const response = await request.get(`${testDataFactory.baseURL}/api/v1/orders`)

      expect(response.status()).toBe(403)
    })

    test('应该拒绝无效的认证token', async ({ request }) => {
      const response = await request.get(`${testDataFactory.baseURL}/api/v1/orders`, {
        headers: {
          'Authorization': 'Bearer invalid_token'
        }
      })

      expect(response.status()).toBe(401)
    })

    test('应该拒绝格式错误的认证头', async ({ request }) => {
      const invalidHeaders = [
        { 'Authorization': 'invalid_format' },
        { 'Authorization': 'Basic invalid' },
        { 'Authorization': '' }
      ]

      for (const headers of invalidHeaders) {
        const response = await request.get(`${testDataFactory.baseURL}/api/v1/orders`, {
          headers
        })

        expect([401, 403]).toContain(response.status())
      }
    })
  })

  test.describe('并发和性能测试', () => {
    test('应该处理并发请求', async ({ request }) => {
      const concurrentRequests = Array.from({ length: 10 }, () =>
        request.get(`${testDataFactory.baseURL}/api/v1/orders`, {
          headers: testDataFactory.getAuthHeaders(authToken)
        })
      )

      const responses = await Promise.all(concurrentRequests)

      responses.forEach(response => {
        expect(response.ok()).toBeTruthy()
      })
    })

    test('应该处理快速连续请求', async ({ request }) => {
      const rapidRequests = []

      for (let i = 0; i < 20; i++) {
        rapidRequests.push(
          request.get(`${testDataFactory.baseURL}/api/v1/orders`, {
            headers: testDataFactory.getAuthHeaders(authToken)
          })
        )
      }

      const responses = await Promise.allSettled(rapidRequests)
      const successfulResponses = responses.filter(
        r => r.status === 'fulfilled' && r.value.ok()
      )

      // 至少一半的请求应该成功
      expect(successfulResponses.length).toBeGreaterThan(10)
    })

    test('应该在合理时间内响应', async ({ request }) => {
      const startTime = Date.now()
      
      const response = await request.get(`${testDataFactory.baseURL}/api/v1/orders`, {
        headers: testDataFactory.getAuthHeaders(authToken)
      })

      const responseTime = Date.now() - startTime
      
      expect(response.ok()).toBeTruthy()
      expect(responseTime).toBeLessThan(5000) // 5秒内响应
    })
  })

  test.describe('错误处理和边界情况', () => {
    test('应该处理特殊字符查询参数', async ({ request }) => {
      const edgeCaseData = testDataFactory.generateEdgeCaseData()
      const specialSymbol = encodeURIComponent(edgeCaseData.xssAttempt)

      const response = await request.get(
        `${testDataFactory.baseURL}/api/v1/orders?symbol=${specialSymbol}`,
        {
          headers: testDataFactory.getAuthHeaders(authToken)
        }
      )

      // 应该安全处理特殊字符
      expect([200, 400, 403, 422]).toContain(response.status())
    })

    test('应该处理超时情况', async ({ request }) => {
      try {
        await request.get(`${testDataFactory.baseURL}/api/v1/orders`, {
          headers: testDataFactory.getAuthHeaders(authToken),
          timeout: 1 // 极短超时
        })
      } catch (error) {
        expect(error.message).toMatch(/timeout|Timeout.*exceeded/i)
      }
    })

    test('应该返回一致的数据', async ({ request }) => {
      const response1 = await request.get(
        `${testDataFactory.baseURL}/api/v1/orders?limit=10`,
        {
          headers: testDataFactory.getAuthHeaders(authToken)
        }
      )

      const response2 = await request.get(
        `${testDataFactory.baseURL}/api/v1/orders?limit=10`,
        {
          headers: testDataFactory.getAuthHeaders(authToken)
        }
      )

      expect(response1.ok()).toBeTruthy()
      expect(response2.ok()).toBeTruthy()

      const data1 = await response1.json()
      const data2 = await response2.json()

      // 总数应该一致（假设没有新订单创建）
      expect(data1.total).toBe(data2.total)
    })
  })
})
