/**
 * Discord配置API测试
 * 测试Discord配置相关的API调用功能
 * 
 * 按照项目测试规范：
 * - 使用真实API端点
 * - 测试完整的请求/响应流程
 * - 测试认证和错误处理
 * - 使用中文描述测试用例
 */
import { test, expect } from '@playwright/test'
import { TestDataFactory } from './test-data-factory.js'

let testDataFactory
let testUser
let authToken
let createdConfigs = []

test.beforeAll(async ({ request }) => {
  // 环境检测
  const backendUrl = process.env.API_BASE_URL || 'http://localhost:8000'

  try {
    // 检测后端是否可用
    const healthResponse = await request.get(`${backendUrl}/api/v1/health`)
    if (!healthResponse.ok()) {
      throw new Error(`Backend not available at ${backendUrl}`)
    }

    // 创建TestDataFactory实例
    testDataFactory = new TestDataFactory(request)

    // 创建测试用户并获取认证token
    testUser = await testDataFactory.createRealUser({
      username: `discord_api_test_${Date.now()}`,
      email: `discord_api_test_${Date.now()}@example.com`,
      password: 'test_password_123'
    })

    // 登录获取token
    authToken = await testDataFactory.loginUser({
      username: testUser.username,
      password: 'test_password_123'
    })

    console.log(`✅ API测试环境准备完成，用户: ${testUser.username}`)
  } catch (error) {
    console.error(`❌ API测试环境不可用: ${error.message}`)
    console.log(`💡 跳过API集成测试，需要后端服务运行在 ${backendUrl}`)
    test.skip()
  }
})

test.afterAll(async () => {
  if (!testDataFactory || !testUser) {
    console.log('🔄 跳过清理：测试环境未初始化')
    return
  }

  try {
    // 清理创建的配置
    for (const config of createdConfigs) {
      try {
        await testDataFactory.deleteDiscordConfig(config.id, authToken)
        console.log(`✅ 清理Discord配置: ${config.id}`)
      } catch (error) {
        console.warn(`⚠️ 清理配置失败: ${config.id}`, error.message)
      }
    }

    // 清理测试用户
    if (testUser.id) {
      await testDataFactory.deleteUser(testUser.id, authToken)
      console.log(`✅ 清理测试用户: ${testUser.username}`)
    }
  } catch (error) {
    console.warn(`⚠️ 清理过程中出现错误: ${error.message}`)
  }
})

test.describe('Discord配置API测试', () => {
  test.describe('获取配置列表', () => {
    test('应该成功获取空的配置列表', async ({ request }) => {
      const response = await request.get('/api/v1/discord-configs', {
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      })

      expect(response.status()).toBe(200)
      const configs = await response.json()
      expect(Array.isArray(configs)).toBe(true)
      expect(configs.length).toBe(0)
    })

    test('应该在未认证时返回403错误', async ({ request }) => {
      const response = await request.get('/api/v1/discord-configs')
      expect(response.status()).toBe(403)
    })

    test('应该在无效token时返回401错误', async ({ request }) => {
      const response = await request.get('/api/v1/discord-configs', {
        headers: {
          'Authorization': 'Bearer invalid_token'
        }
      })
      expect(response.status()).toBe(401)
    })
  })

  test.describe('创建Discord配置', () => {
    test('应该成功创建Discord配置', async ({ request }) => {
      const configData = {
        source_name: 'API测试Discord配置',
        enabled: true,
        token: 'test_discord_token_123',
        server_ids: ['123456789012345678'],
        channel_ids: ['987654321098765432'],
        author_ids: ['555666777888999000'],
        allowed_message_types: ['text']
      }

      const response = await request.post('/api/v1/discord-configs', {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        },
        data: configData
      })

      expect(response.status()).toBe(200)
      const createdConfig = await response.json()
      
      // 保存用于清理
      createdConfigs.push(createdConfig)

      // 验证响应数据
      expect(createdConfig.id).toBeDefined()
      expect(createdConfig.user_id).toBeDefined()
      expect(createdConfig.source_name).toBe(configData.source_name)
      expect(createdConfig.enabled).toBe(configData.enabled)
      expect(createdConfig.has_token).toBe(true)
      expect(createdConfig.server_ids).toEqual(configData.server_ids)
      expect(createdConfig.channel_ids).toEqual(configData.channel_ids)
      expect(createdConfig.author_ids).toEqual(configData.author_ids)
      expect(createdConfig.allowed_message_types).toEqual(configData.allowed_message_types)
      expect(createdConfig.created_at).toBeDefined()
      expect(createdConfig.updated_at).toBeDefined()
    })

    test('应该在缺少必需字段时返回422错误', async ({ request }) => {
      const invalidData = {
        enabled: true
        // 缺少 source_name 和 token
      }

      const response = await request.post('/api/v1/discord-configs', {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        },
        data: invalidData
      })

      expect(response.status()).toBe(422)
      const error = await response.json()
      expect(error.success).toBe(false)
      expect(error.error).toBeDefined()
      expect(error.error.code).toBe('VALIDATION_ERROR')
    })

    test('应该在source_name过长时返回422错误', async ({ request }) => {
      const invalidData = {
        source_name: 'a'.repeat(101), // 超过100字符限制
        enabled: true,
        token: 'test_token',
        server_ids: [],
        channel_ids: [],
        author_ids: [],
        allowed_message_types: ['text']
      }

      const response = await request.post('/api/v1/discord-configs', {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        },
        data: invalidData
      })

      expect(response.status()).toBe(422)
    })
  })

  test.describe('获取单个配置', () => {
    let testConfig

    test.beforeAll(async ({ request }) => {
      // 创建测试配置
      const configData = {
        source_name: '单个配置测试',
        enabled: true,
        token: 'single_config_token',
        server_ids: ['123456789'],
        channel_ids: ['987654321'],
        author_ids: [],
        allowed_message_types: ['text']
      }

      const response = await request.post('/api/v1/discord-configs', {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        },
        data: configData
      })

      testConfig = await response.json()
      createdConfigs.push(testConfig)
    })

    test('应该成功获取单个Discord配置', async ({ request }) => {
      const response = await request.get(`/api/v1/discord-configs/${testConfig.id}`, {
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      })

      expect(response.status()).toBe(200)
      const config = await response.json()
      expect(config.id).toBe(testConfig.id)
      expect(config.source_name).toBe(testConfig.source_name)
    })

    test('应该在配置不存在时返回404错误', async ({ request }) => {
      const nonExistentId = '00000000-0000-0000-0000-000000000000'
      
      const response = await request.get(`/api/v1/discord-configs/${nonExistentId}`, {
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      })

      expect(response.status()).toBe(404)
    })

    test('应该在无效UUID时返回422错误', async ({ request }) => {
      const response = await request.get('/api/v1/discord-configs/invalid-uuid', {
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      })

      expect(response.status()).toBe(422)
    })
  })

  test.describe('更新Discord配置', () => {
    let testConfig

    test.beforeAll(async ({ request }) => {
      // 创建测试配置
      const configData = {
        source_name: '更新测试配置',
        enabled: false,
        token: 'update_test_token',
        server_ids: ['111111111'],
        channel_ids: ['222222222'],
        author_ids: ['333333333'],
        allowed_message_types: ['text']
      }

      const response = await request.post('/api/v1/discord-configs', {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        },
        data: configData
      })

      testConfig = await response.json()
      createdConfigs.push(testConfig)
    })

    test('应该成功更新Discord配置', async ({ request }) => {
      const updateData = {
        source_name: '更新后的配置名称',
        enabled: true,
        token: 'updated_discord_token',
        server_ids: ['999888777'],
        channel_ids: ['666555444'],
        author_ids: [],
        allowed_message_types: ['text', 'embed']
      }

      const response = await request.put(`/api/v1/discord-configs/${testConfig.id}`, {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        },
        data: updateData
      })

      expect(response.status()).toBe(200)
      const updatedConfig = await response.json()
      
      expect(updatedConfig.id).toBe(testConfig.id)
      expect(updatedConfig.source_name).toBe(updateData.source_name)
      expect(updatedConfig.enabled).toBe(updateData.enabled)
      expect(updatedConfig.server_ids).toEqual(updateData.server_ids)
      expect(updatedConfig.channel_ids).toEqual(updateData.channel_ids)
      expect(updatedConfig.author_ids).toEqual(updateData.author_ids)
      expect(updatedConfig.allowed_message_types).toEqual(updateData.allowed_message_types)
      expect(updatedConfig.updated_at).not.toBe(testConfig.updated_at)
    })

    test('应该在更新不存在的配置时返回404错误', async ({ request }) => {
      const nonExistentId = '00000000-0000-0000-0000-000000000000'
      const updateData = {
        source_name: '不存在的配置',
        enabled: true,
        token: 'test_token',
        server_ids: [],
        channel_ids: [],
        author_ids: [],
        allowed_message_types: ['text']
      }

      const response = await request.put(`/api/v1/discord-configs/${nonExistentId}`, {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        },
        data: updateData
      })

      expect(response.status()).toBe(404)
    })
  })

  test.describe('删除Discord配置', () => {
    test('应该成功删除Discord配置', async ({ request }) => {
      // 创建要删除的配置
      const configData = {
        source_name: '删除测试配置',
        enabled: true,
        token: 'delete_test_token',
        server_ids: [],
        channel_ids: [],
        author_ids: [],
        allowed_message_types: ['text']
      }

      const createResponse = await request.post('/api/v1/discord-configs', {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        },
        data: configData
      })

      const createdConfig = await createResponse.json()

      // 删除配置
      const deleteResponse = await request.delete(`/api/v1/discord-configs/${createdConfig.id}`, {
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      })

      expect(deleteResponse.status()).toBe(200)
      const result = await deleteResponse.json()
      expect(result.message).toContain('配置已删除')

      // 验证配置已删除
      const getResponse = await request.get(`/api/v1/discord-configs/${createdConfig.id}`, {
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      })
      expect(getResponse.status()).toBe(404)
    })

    test('应该在删除不存在的配置时返回404错误', async ({ request }) => {
      const nonExistentId = '00000000-0000-0000-0000-000000000000'

      const response = await request.delete(`/api/v1/discord-configs/${nonExistentId}`, {
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      })

      expect(response.status()).toBe(404)
    })
  })

  test.describe('数据验证和边界测试', () => {
    test('应该正确处理空数组字段', async ({ request }) => {
      const configData = {
        source_name: '空数组测试配置',
        enabled: true,
        token: 'empty_arrays_token',
        server_ids: [],
        channel_ids: [],
        author_ids: [],
        allowed_message_types: []
      }

      const response = await request.post('/api/v1/discord-configs', {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        },
        data: configData
      })

      expect(response.status()).toBe(200)
      const createdConfig = await response.json()
      createdConfigs.push(createdConfig)

      expect(createdConfig.server_ids).toEqual([])
      expect(createdConfig.channel_ids).toEqual([])
      expect(createdConfig.author_ids).toEqual([])
      expect(createdConfig.allowed_message_types).toEqual(['text']) // 空数组时默认为['text']
    })

    test('应该正确处理最大长度的source_name', async ({ request }) => {
      const maxLengthName = 'a'.repeat(100) // 正好100字符

      const configData = {
        source_name: maxLengthName,
        enabled: true,
        token: 'max_length_token',
        server_ids: [],
        channel_ids: [],
        author_ids: [],
        allowed_message_types: ['text']
      }

      const response = await request.post('/api/v1/discord-configs', {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        },
        data: configData
      })

      expect(response.status()).toBe(200)
      const createdConfig = await response.json()
      createdConfigs.push(createdConfig)

      expect(createdConfig.source_name).toBe(maxLengthName)
      expect(createdConfig.source_name.length).toBe(100)
    })
  })
})
