# 前端测试指南

## 📋 概述

本目录包含AI加密货币交易系统前端的所有测试代码，采用Playwright和Vitest框架进行全面测试。

## 🏗️ 测试结构

```
tests/
├── e2e/                    # 端到端测试
│   ├── auth.test.js       # 认证流程测试
│   ├── user-journey.test.js # 用户旅程测试
│   ├── comprehensive-flow.test.js # 综合流程测试
│   └── optimized-auth.test.js # 优化的认证测试
├── api-unified/           # 统一API测试
│   ├── auth.api.test.js   # 认证API测试
│   ├── orders.api.test.js # 订单API测试
│   └── agent.api.test.js  # Agent API测试
├── components/            # 组件测试
│   ├── orders.test.js     # 订单组件测试
│   └── conditional-orders.test.js # 条件订单组件测试
├── fixtures/              # 测试数据和辅助函数
│   ├── test-data.js       # 测试数据定义
│   └── test-helpers.js    # 测试辅助函数
└── setup/                 # 测试环境设置
    ├── global-setup.js    # 全局设置
    └── global-teardown.js # 全局清理
```

## 🚀 快速开始

### 环境准备

1. **安装依赖**
```bash
cd frontend
npm install
```

2. **安装Playwright浏览器**
```bash
npm run install:playwright
# 或
npx playwright install
```

3. **环境变量设置**
```bash
export FRONTEND_URL=http://localhost:5173
export API_BASE_URL=http://localhost:8000
```

### 启动依赖服务

**重要**: E2E测试需要前端和后端服务同时运行

#### 方法1: 自动服务管理（推荐）
```bash
# 一键启动所有服务并运行测试
npm run test:e2e:full

# 或者分步操作
npm run services:start    # 启动所有服务
npm run test:e2e         # 运行测试
npm run services:stop    # 停止所有服务
```

#### 方法2: 手动服务管理
```bash
# 终端1: 启动后端服务
cd backend
python -m uvicorn app.main:app --reload --port 8000

# 终端2: 启动前端服务
cd frontend
npm run dev

# 终端3: 运行测试
npm run test:e2e
```

#### 方法3: 使用脚本管理
```bash
# 启动服务
../scripts/start-test-services.sh

# 运行测试
npm run test:e2e

# 停止服务
../scripts/stop-test-services.sh
```

## 🧪 测试类型和命令

### 🎯 推荐：统一委托模式
```bash
# 前端单元/组件测试（通过统一测试脚本）
python scripts/test.py --vitest unit          # 前端单元测试
python scripts/test.py --vitest components    # 前端组件测试
python scripts/test.py --vitest validate     # 验证前端测试环境

# 前端集成测试（通过统一测试脚本）
python scripts/test.py --playwright api       # API接口测试
python scripts/test.py --playwright e2e       # E2E UI测试
```

### 单元测试 (Vitest) - 直接调用
```bash
# 运行所有单元测试
npm run test

# 监听模式
npm run test:watch

# 覆盖率报告
npm run test:coverage

# UI界面
npm run test:ui

# 使用专门脚本
node scripts/test/frontend/run_unit_tests.js unit        # 单元测试
node scripts/test/frontend/run_unit_tests.js components  # 组件测试
```

### E2E测试 (Playwright)
```bash
# 运行所有E2E测试
npm run test:e2e

# 带UI界面运行
npm run test:e2e:ui

# 有头模式运行
npm run test:e2e:headed

# 调试模式
npm run test:e2e:debug

# 查看测试报告
npm run test:e2e:report
```

### 服务管理命令
```bash
# 启动所有测试服务
npm run services:start

# 停止所有测试服务
npm run services:stop

# 重启所有测试服务
npm run services:restart

# 完整测试流程（启动服务 + 测试 + 停止服务）
npm run test:e2e:full

# 开发模式测试（假设服务已运行）
npm run test:e2e:dev

# CI模式测试
npm run test:e2e:ci
```

### 特定测试运行
```bash
# 认证测试
npm run test:auth

# API测试
npm run test:api

# 组件测试
npm run test:components

# 用户旅程测试
npm run test:journey

# 综合流程测试
npm run test:comprehensive

# 配置管理测试
npm run test:config

# 条件订单测试
npm run test:conditional
```

### 测试分类运行
```bash
# 冒烟测试
npm run test:smoke

# 回归测试
npm run test:regression

# 关键路径测试
npm run test:critical

# 快速测试
npm run test:quick
```

### 浏览器特定测试
```bash
# 仅Chrome
npx playwright test --project=chromium

# 仅Firefox
npx playwright test --project=firefox

# 仅Safari
npx playwright test --project=webkit

# 移动端Chrome
npx playwright test --project=mobile-chrome
```

## 🎯 测试标记和过滤

### Playwright标记
```javascript
// 冒烟测试
test('登录功能 @smoke', async ({ page }) => {
  // 测试代码
});

// 回归测试
test('完整订单流程 @regression', async ({ page }) => {
  // 测试代码
});

// 关键路径
test('用户认证 @critical', async ({ page }) => {
  // 测试代码
});
```

### 运行特定标记的测试
```bash
# 运行冒烟测试
npm run test:smoke

# 运行回归测试
npm run test:regression

# 运行关键测试
npm run test:critical
```

## 🔧 测试配置

### Playwright配置亮点
- **多浏览器支持**: Chrome, Firefox, Safari, Mobile
- **并行执行**: 提高测试效率
- **自动重试**: CI环境自动重试失败的测试
- **视频录制**: 失败时自动录制
- **截图**: 失败时自动截图
- **HTML报告**: 详细的测试报告

### 超时配置
```javascript
// 优化的超时设置
const TIMEOUTS = {
  SHORT: 3000,        // 快速操作
  MEDIUM: 8000,       // 中等操作
  LONG: 15000,        // 长操作
  NAVIGATION: 20000,  // 页面导航
  API_REQUEST: 10000, // API请求
  ELEMENT_WAIT: 5000  // 元素等待
}
```

## 📊 测试最佳实践

### 选择器策略
```javascript
// 优先级: data-testid > 语义属性 > CSS选择器 > 文本内容
const SELECTORS = {
  LOGIN_BUTTON: '[data-testid="login-button"], button[type="submit"], button:has-text("登录")'
}
```

### 页面对象模式
```javascript
class LoginPage {
  constructor(page) {
    this.page = page;
  }
  
  async login(username, password) {
    await this.page.fill('[data-testid="username"]', username);
    await this.page.fill('[data-testid="password"]', password);
    await this.page.click('[data-testid="login-button"]');
  }
}
```

### 测试数据管理
```javascript
// 使用fixtures中的测试数据
import { DEMO_CREDENTIALS, MOCK_RESPONSES } from '../fixtures/test-data.js';

test('用户登录', async ({ page }) => {
  await loginPage.login(DEMO_CREDENTIALS.username, DEMO_CREDENTIALS.password);
});
```

## 🚨 故障排除

### 常见问题

1. **服务未启动**
```bash
# 确保前端和后端服务都在运行
curl http://localhost:5173  # 前端
curl http://localhost:8000/health  # 后端
```

2. **浏览器未安装**
```bash
# 重新安装Playwright浏览器
npx playwright install
```

3. **元素定位失败**
```bash
# 使用调试模式查看页面状态
npm run test:e2e:debug
```

4. **超时错误**
```bash
# 检查网络连接和服务响应时间
# 考虑增加超时设置
```

### 调试技巧
```bash
# 调试特定测试
npx playwright test auth.test.js --debug

# 显示浏览器界面
npx playwright test --headed

# 慢速执行
npx playwright test --slow-mo=1000

# 生成代码
npx playwright codegen http://localhost:5173
```

## 📊 测试覆盖率

### 当前覆盖率状态 (更新于 2025-07-24)

**总体覆盖率**: 14.77% (基础测试已建立)

**核心 Store 覆盖率**:
- `auth.ts`: 72.09% - 认证状态管理
- `order.ts`: 35.84% - 订单状态管理
- `websocket.ts`: 23.12% - WebSocket 连接管理

**组件测试覆盖率**:
- `DashboardView.vue`: 69.74% - 仪表盘主视图
- `LiveLogStream.vue`: 100% - 实时日志流组件
- `PendingActionsList.vue`: 100% - 待处理动作列表

**测试套件状态**:
- ✅ 28 个测试全部通过
- ✅ 单元测试: 21 个通过
- ✅ 组件测试: 5 个通过
- ✅ 基础测试: 3 个通过

### 覆盖率目标

- **短期目标**: 核心功能覆盖率达到 60%
- **中期目标**: 总体覆盖率达到 40%
- **长期目标**: 总体覆盖率达到 80%

### 查看覆盖率报告

```bash
# 生成覆盖率报告
npm run test:coverage

# 查看HTML报告
open coverage/index.html
```

## 📈 性能监控

### 测试执行时间
```bash
# 显示测试执行时间
npx playwright test --reporter=list

# 并行执行优化
npx playwright test --workers=4
```

### 网络监控
```javascript
// 监控网络请求
test('API响应时间', async ({ page }) => {
  const response = await page.waitForResponse('**/api/orders');
  expect(response.status()).toBe(200);
});
```

## 🔄 CI/CD集成

### GitHub Actions示例
```yaml
- name: Run E2E Tests
  run: |
    cd frontend
    npm run test:e2e --reporter=junit
```

### 本地CI模拟
```bash
# 模拟CI环境
CI=true npm run test:ci
```

## 📋 测试检查清单

### 新功能测试
- [x] 单元测试覆盖核心逻辑 (Order Store, Auth Store)
- [x] 组件测试验证UI交互 (DashboardView)
- [ ] API测试确保接口正常 (Playwright API 测试)
- [ ] E2E测试验证完整流程 (Playwright E2E 测试)
- [x] 错误场景测试 (Mock 错误处理)
- [ ] 性能测试

### 测试维护 (2025-07-24 更新)
- [x] 定期更新测试数据 (使用最新的 API 响应格式)
- [x] 清理过时的测试 (移除不兼容的 Playwright 组件测试)
- [x] 优化慢速测试 (修复测试配置问题)
- [x] 更新选择器策略 (使用 data-testid 和 CSS 类选择器)
- [x] 检查测试覆盖率 (建立覆盖率基线)

## 📚 相关文档

- [Playwright官方文档](https://playwright.dev/)
- [Vitest官方文档](https://vitest.dev/)
- [Vue Test Utils文档](https://test-utils.vuejs.org/)
- [E2E测试优化指南](./docs/E2E_TEST_OPTIMIZATION_GUIDE.md)

---

**维护者**: AI Crypto Trading Team
**最后更新**: 2025-07-24
**版本**: 1.1 - 前端测试套件修复和优化
