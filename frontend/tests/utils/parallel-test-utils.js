/**
 * 测试数据隔离工具
 * 为并行测试提供数据隔离和清理功能
 */

import fs from 'fs'
import path from 'path'

export class TestDataIsolation {
  constructor() {
    this.workerId = process.env.WORKER_INDEX || process.env.PLAYWRIGHT_WORKER_INDEX || '0'
    this.testRunId = `${Date.now()}_${this.workerId}`
    this.isolatedData = new Map()
  }

  /**
   * 创建隔离的测试用户
   */
  createIsolatedUser(baseUsername = 'test') {
    const isolatedUsername = `${baseUsername}_${this.testRunId}`
    return {
      username: isolatedUsername,
      password: 'test123',
      email: `${isolatedUsername}@test.com`
    }
  }

  /**
   * 创建隔离的测试数据
   */
  createIsolatedTestData(dataType, baseData = {}) {
    const isolatedData = {
      ...baseData,
      id: `${dataType}_${this.testRunId}`,
      name: `${dataType}_${this.testRunId}`,
      timestamp: Date.now(),
      workerId: this.workerId
    }
    
    this.isolatedData.set(dataType, isolatedData)
    return isolatedData
  }

  /**
   * 获取隔离的文件路径
   */
  getIsolatedFilePath(filename) {
    const ext = path.extname(filename)
    const name = path.basename(filename, ext)
    const dir = path.dirname(filename)
    
    return path.join(dir, `${name}_${this.workerId}${ext}`)
  }

  /**
   * 创建隔离的临时目录
   */
  createIsolatedTempDir(basePath = './temp') {
    const isolatedPath = path.join(basePath, `worker_${this.workerId}`)
    
    if (!fs.existsSync(isolatedPath)) {
      fs.mkdirSync(isolatedPath, { recursive: true })
    }
    
    return isolatedPath
  }

  /**
   * 清理隔离的测试数据
   */
  async cleanup() {
    console.log(`🧹 清理Worker ${this.workerId}的测试数据...`)
    
    try {
      // 清理临时文件
      const tempDir = `./temp/worker_${this.workerId}`
      if (fs.existsSync(tempDir)) {
        fs.rmSync(tempDir, { recursive: true, force: true })
      }
      
      // 清理其他隔离数据
      this.isolatedData.clear()
      
      console.log(`✅ Worker ${this.workerId}数据清理完成`)
    } catch (error) {
      console.warn(`⚠️ Worker ${this.workerId}数据清理失败:`, error.message)
    }
  }

  /**
   * 获取当前worker的统计信息
   */
  getWorkerStats() {
    return {
      workerId: this.workerId,
      testRunId: this.testRunId,
      isolatedDataCount: this.isolatedData.size,
      startTime: this.testRunId.split('_')[0]
    }
  }
}

/**
 * 并行测试助手类
 * 提供并行测试中常用的工具方法
 */
export class ParallelTestHelpers {
  static async waitForWorkerReady(page, maxWaitTime = 30000) {
    const startTime = Date.now()
    
    while (Date.now() - startTime < maxWaitTime) {
      try {
        // 检查页面是否响应
        await page.evaluate(() => document.readyState)
        
        // 检查是否有其他worker正在使用相同资源
        const isReady = await page.evaluate(() => {
          return !window.__testWorkerBusy
        })
        
        if (isReady) {
          // 标记当前worker正在使用
          await page.evaluate((workerId) => {
            window.__testWorkerId = workerId
            window.__testWorkerBusy = true
          }, process.env.WORKER_INDEX || '0')
          
          return true
        }
        
        await new Promise(resolve => setTimeout(resolve, 100))
      } catch (error) {
        await new Promise(resolve => setTimeout(resolve, 500))
      }
    }
    
    throw new Error(`Worker ${process.env.WORKER_INDEX || '0'} 等待就绪超时`)
  }

  static async releaseWorkerResources(page) {
    try {
      await page.evaluate(() => {
        window.__testWorkerBusy = false
        delete window.__testWorkerId
      })
    } catch (error) {
      // 忽略释放资源时的错误
    }
  }

  static async withWorkerIsolation(page, testFn) {
    const isolation = new TestDataIsolation()
    
    try {
      await ParallelTestHelpers.waitForWorkerReady(page)
      await testFn(isolation)
    } finally {
      await ParallelTestHelpers.releaseWorkerResources(page)
      await isolation.cleanup()
    }
  }

  /**
   * 获取worker专用的端口号
   */
  static getWorkerPort(basePort = 3000) {
    const workerId = parseInt(process.env.WORKER_INDEX || '0')
    return basePort + workerId
  }

  /**
   * 创建worker专用的数据库连接字符串
   */
  static getWorkerDatabaseUrl(baseDatabaseUrl) {
    const workerId = process.env.WORKER_INDEX || '0'
    
    // 为每个worker使用不同的数据库schema或表前缀
    if (baseDatabaseUrl.includes('postgresql')) {
      return baseDatabaseUrl.replace(
        /\/([^\/]+)(\?|$)/, 
        `/test_worker_${workerId}$2`
      )
    }
    
    return baseDatabaseUrl
  }

  /**
   * 等待所有worker完成
   */
  static async waitForAllWorkers(expectedWorkerCount, timeout = 60000) {
    const startTime = Date.now()
    const completedWorkers = new Set()
    
    while (Date.now() - startTime < timeout) {
      // 检查worker完成状态的逻辑
      // 这里可以通过文件系统、数据库或其他方式来协调
      
      if (completedWorkers.size >= expectedWorkerCount) {
        return true
      }
      
      await new Promise(resolve => setTimeout(resolve, 1000))
    }
    
    throw new Error(`等待所有worker完成超时，预期${expectedWorkerCount}个，实际完成${completedWorkers.size}个`)
  }
}

/**
 * 并行测试监控器
 * 监控并行测试的执行状态和性能
 */
export class ParallelTestMonitor {
  constructor() {
    this.startTime = Date.now()
    this.workerStats = new Map()
    this.testResults = []
  }

  recordWorkerStart(workerId) {
    this.workerStats.set(workerId, {
      startTime: Date.now(),
      testsRun: 0,
      testsPassed: 0,
      testsFailed: 0
    })
  }

  recordWorkerTest(workerId, testResult) {
    const stats = this.workerStats.get(workerId)
    if (stats) {
      stats.testsRun++
      if (testResult.passed) {
        stats.testsPassed++
      } else {
        stats.testsFailed++
      }
    }
    
    this.testResults.push({
      workerId,
      timestamp: Date.now(),
      ...testResult
    })
  }

  recordWorkerEnd(workerId) {
    const stats = this.workerStats.get(workerId)
    if (stats) {
      stats.endTime = Date.now()
      stats.duration = stats.endTime - stats.startTime
    }
  }

  generateReport() {
    const totalDuration = Date.now() - this.startTime
    const totalTests = this.testResults.length
    const passedTests = this.testResults.filter(r => r.passed).length
    const failedTests = totalTests - passedTests
    
    const workerReports = Array.from(this.workerStats.entries()).map(([workerId, stats]) => ({
      workerId,
      testsRun: stats.testsRun,
      testsPassed: stats.testsPassed,
      testsFailed: stats.testsFailed,
      duration: stats.duration || 0,
      efficiency: stats.duration ? (stats.testsRun / (stats.duration / 1000)).toFixed(2) : 0
    }))
    
    return {
      summary: {
        totalDuration,
        totalTests,
        passedTests,
        failedTests,
        successRate: ((passedTests / totalTests) * 100).toFixed(2),
        averageTestTime: (totalDuration / totalTests).toFixed(2)
      },
      workers: workerReports,
      timeline: this.testResults
    }
  }

  saveReport(filePath) {
    const report = this.generateReport()
    fs.writeFileSync(filePath, JSON.stringify(report, null, 2))
    return report
  }
}

// 导出单例实例
export const testDataIsolation = new TestDataIsolation()
export const parallelTestMonitor = new ParallelTestMonitor()