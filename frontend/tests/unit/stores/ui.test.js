/**
 * UI Store 测试
 * 测试用户界面状态管理功能
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { createPinia, setActivePinia } from 'pinia'
import { useUIStore } from '@/stores/ui'

describe('UI Store', () => {
  let uiStore
  let pinia

  beforeEach(() => {
    // 创建新的 Pinia 实例
    pinia = createPinia()
    setActivePinia(pinia)

    // 获取 store 实例
    uiStore = useUIStore()
  })

  describe('初始状态', () => {
    it('应该有正确的初始状态', () => {
      expect(uiStore.theme).toBe('dark') // 默认主题是 dark
      expect(uiStore.sidebarOpen).toBe(true)
      expect(uiStore.isLoading).toBe(false) // 正确的属性名
      expect(uiStore.notifications).toEqual([])
      expect(uiStore.loadingMessage).toBe('')
      expect(uiStore.showOnboardingWizard).toBe(false)
    })
  })

  describe('基本功能', () => {
    it('应该能够切换主题', () => {
      // 初始主题是 dark
      expect(uiStore.theme).toBe('dark')

      uiStore.toggleTheme()
      expect(uiStore.theme).toBe('light')

      uiStore.toggleTheme()
      expect(uiStore.theme).toBe('dark')
    })

    it('应该能够切换侧边栏', () => {
      expect(uiStore.sidebarOpen).toBe(true)

      uiStore.toggleSidebar()
      expect(uiStore.sidebarOpen).toBe(false)

      uiStore.toggleSidebar()
      expect(uiStore.sidebarOpen).toBe(true)
    })

    it('应该能够设置加载状态', () => {
      uiStore.setLoading(true, '加载中...')
      expect(uiStore.isLoading).toBe(true) // 正确的属性名
      expect(uiStore.loadingMessage).toBe('加载中...')

      uiStore.setLoading(false)
      expect(uiStore.isLoading).toBe(false)
      expect(uiStore.loadingMessage).toBe('')
    })

    it('应该能够设置主题', () => {
      uiStore.setTheme('light')
      expect(uiStore.theme).toBe('light')

      uiStore.setTheme('dark')
      expect(uiStore.theme).toBe('dark')
    })

    it('应该能够设置侧边栏状态', () => {
      uiStore.setSidebarOpen(false)
      expect(uiStore.sidebarOpen).toBe(false)

      uiStore.setSidebarOpen(true)
      expect(uiStore.sidebarOpen).toBe(true)
    })
  })

  describe('通知管理', () => {
    it('应该能够添加通知', () => {
      const notificationId = uiStore.addNotification({
        type: 'success',
        title: '成功',
        message: '操作成功',
        color: 'success'
      })

      expect(uiStore.notifications).toHaveLength(1)
      expect(uiStore.notifications[0].title).toBe('成功')
      expect(uiStore.notifications[0].message).toBe('操作成功')
      expect(uiStore.notifications[0].id).toBe(notificationId)
      expect(uiStore.hasNotifications).toBe(true)
      expect(uiStore.unreadNotifications).toBe(1)
    })

    it('应该能够移除通知', () => {
      const notificationId = uiStore.addNotification({
        type: 'info',
        title: '信息',
        message: '测试消息'
      })

      expect(uiStore.notifications).toHaveLength(1)

      uiStore.removeNotification(notificationId)
      expect(uiStore.notifications).toHaveLength(0)
      expect(uiStore.hasNotifications).toBe(false)
    })

    it('应该能够标记通知为已读', () => {
      const notificationId = uiStore.addNotification({
        type: 'warning',
        title: '警告',
        message: '测试警告'
      })

      expect(uiStore.unreadNotifications).toBe(1)

      uiStore.markNotificationAsRead(notificationId)
      expect(uiStore.unreadNotifications).toBe(0)
      expect(uiStore.notifications[0].read).toBe(true)
    })

    it('应该能够清除所有通知', () => {
      uiStore.addNotification({ type: 'info', title: '信息1', message: '消息1' })
      uiStore.addNotification({ type: 'info', title: '信息2', message: '消息2' })

      expect(uiStore.notifications).toHaveLength(2)

      uiStore.clearNotifications()
      expect(uiStore.notifications).toHaveLength(0)
    })

    it('应该能够使用快捷通知方法', () => {
      const successId = uiStore.showSuccess('成功消息')
      const errorId = uiStore.showError('错误消息')
      const warningId = uiStore.showWarning('警告消息')
      const infoId = uiStore.showInfo('信息消息')

      expect(uiStore.notifications).toHaveLength(4)
      expect(uiStore.notifications.find(n => n.id === successId)?.type).toBe('success')
      expect(uiStore.notifications.find(n => n.id === errorId)?.type).toBe('error')
      expect(uiStore.notifications.find(n => n.id === warningId)?.type).toBe('warning')
      expect(uiStore.notifications.find(n => n.id === infoId)?.type).toBe('info')
    })
  })

  describe('加载状态管理', () => {
    it('应该能够显示和隐藏加载状态', () => {
      uiStore.showLoading('正在加载数据...')
      expect(uiStore.isLoading).toBe(true)
      expect(uiStore.loadingMessage).toBe('正在加载数据...')

      uiStore.hideLoading()
      expect(uiStore.isLoading).toBe(false)
      expect(uiStore.loadingMessage).toBe('')
    })
  })

  describe('引导向导管理', () => {
    it('应该能够打开和关闭引导向导', () => {
      expect(uiStore.showOnboardingWizard).toBe(false)

      uiStore.openOnboardingWizard()
      expect(uiStore.showOnboardingWizard).toBe(true)

      uiStore.closeOnboardingWizard()
      expect(uiStore.showOnboardingWizard).toBe(false)
    })
  })

  describe('确认对话框', () => {
    it('应该能够显示和隐藏确认对话框', () => {
      expect(uiStore.confirmDialog.show).toBe(false)

      const promise = uiStore.showConfirmDialog({
        title: '确认删除',
        message: '确定要删除这个项目吗？'
      })

      expect(uiStore.confirmDialog.show).toBe(true)
      expect(uiStore.confirmDialog.title).toBe('确认删除')
      expect(uiStore.confirmDialog.message).toBe('确定要删除这个项目吗？')

      uiStore.hideConfirmDialog()
      expect(uiStore.confirmDialog.show).toBe(false)
    })
  })
})
