import { describe, it, expect, beforeEach, vi } from 'vitest'
import { setActive<PERSON><PERSON>, createP<PERSON> } from 'pinia'
import { useAuthStore } from '../../../src/stores/auth'

// Mock the API client
vi.mock('@/api/client', () => ({
  post: vi.fn(),
  get: vi.fn()
}))

describe('Auth Store', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
    vi.clearAllMocks()
  })

  it('should initialize with correct default state', () => {
    const authStore = useAuthStore()

    expect(authStore.user).toBeNull()
    expect(authStore.token).toBeNull()
    expect(authStore.isLoading).toBe(false)
    expect(authStore.error).toBeNull()
    expect(authStore.isAuthenticated).toBe(false)
    expect(authStore.isFirstTimeLogin).toBe(false)
  })

  it('should login successfully', async () => {
    const { post, get } = await import('@/api/client')
    const authStore = useAuthStore()

    const mockLoginResponse = {
      access_token: 'test-token'
    }

    const mockUserResponse = {
      id: 1,
      username: 'test',
      email: '<EMAIL>',
      is_first_time: false
    }

    vi.mocked(post).mockResolvedValue(mockLoginResponse)
    vi.mocked(get).mockResolvedValue(mockUserResponse)

    const credentials = { username: 'test', password: 'password' }
    const result = await authStore.login(credentials)

    // 验证FormData是否正确创建
    expect(post).toHaveBeenCalledWith('/api/v1/auth/login', expect.any(FormData))
    expect(get).toHaveBeenCalledWith('/api/v1/auth/me')
    expect(authStore.token).toBe('test-token')
    expect(authStore.user).toEqual(mockUserResponse)
    expect(authStore.isAuthenticated).toBe(true)
    expect(result).toEqual({ ...mockLoginResponse, user: mockUserResponse })
  })

  it('should handle login error', async () => {
    const { post } = await import('@/api/client')
    const authStore = useAuthStore()

    const mockError = new Error('Invalid credentials')
    vi.mocked(post).mockRejectedValue(mockError)

    const credentials = { username: 'test', password: 'wrong' }

    await expect(authStore.login(credentials)).rejects.toThrow('Invalid credentials')
    expect(authStore.error).toBe('Invalid credentials')
    expect(authStore.isAuthenticated).toBe(false)
  })

  it('should register successfully', async () => {
    const { post } = await import('@/api/client')
    const authStore = useAuthStore()

    const mockResponse = { message: 'Registration successful' }
    vi.mocked(post).mockResolvedValue(mockResponse)

    const userData = { username: 'test', email: '<EMAIL>', password: 'password' }
    const result = await authStore.register(userData)

    expect(post).toHaveBeenCalledWith('/api/v1/auth/register', userData)
    expect(result).toEqual(mockResponse)
  })

  it('should logout correctly', () => {
    const authStore = useAuthStore()
    
    // Set some initial state
    authStore.token = 'test-token'
    authStore.user = { id: 1, username: 'test', email: '<EMAIL>', is_first_time: false }

    authStore.logout()

    expect(authStore.token).toBeNull()
    expect(authStore.user).toBeNull()
    expect(authStore.isAuthenticated).toBe(false)
  })

  it('should initialize user successfully', async () => {
    const { get } = await import('@/api/client')
    const authStore = useAuthStore()

    const mockUser = { id: 1, username: 'test', email: '<EMAIL>', is_first_time: false }
    vi.mocked(get).mockResolvedValue(mockUser)

    // Set token first
    authStore.token = 'test-token'

    await authStore.initializeUser()

    expect(get).toHaveBeenCalledWith('/api/v1/auth/me')
    expect(authStore.user).toEqual(mockUser)
  })

  it('should logout on initialize user error', async () => {
    const { get } = await import('@/api/client')
    const authStore = useAuthStore()

    vi.mocked(get).mockRejectedValue(new Error('Unauthorized'))

    // Set token and user first
    authStore.token = 'test-token'
    authStore.user = { id: 1, username: 'test', email: '<EMAIL>', is_first_time: false }

    await authStore.initializeUser()

    // After error, token and user should be cleared
    expect(authStore.token).toBeNull()
    expect(authStore.user).toBeNull()
    expect(authStore.isAuthenticated).toBe(false)
  })

  it('should clear error', () => {
    const authStore = useAuthStore()
    authStore.error = 'Test error'

    authStore.clearError()

    expect(authStore.error).toBeNull()
  })

  it('should handle first time login flag', async () => {
    const { post, get } = await import('@/api/client')
    const authStore = useAuthStore()

    const mockLoginResponse = { access_token: 'test-token' }
    const mockUserResponse = {
      id: 1,
      username: 'test',
      email: '<EMAIL>',
      is_first_time: true
    }

    vi.mocked(post).mockResolvedValue(mockLoginResponse)
    vi.mocked(get).mockResolvedValue(mockUserResponse)

    const credentials = { username: 'test', password: 'password' }
    await authStore.login(credentials)

    expect(authStore.isFirstTimeLogin).toBe(true)
  })

  it('should update user profile successfully', async () => {
    const { post } = await import('@/api/client')
    const authStore = useAuthStore()

    const profileData = {
      firstName: 'Updated',
      lastName: 'Name',
      email: '<EMAIL>'
    }

    const mockUpdatedUser = {
      id: 1,
      username: 'test',
      email: '<EMAIL>',
      profile: profileData
    }

    vi.mocked(post).mockResolvedValue(mockUpdatedUser)

    const result = await authStore.updateProfile(profileData)

    expect(result).toEqual(mockUpdatedUser)
    expect(authStore.user).toEqual(mockUpdatedUser)
    expect(post).toHaveBeenCalledWith('/api/v1/auth/profile', profileData)
  })

  it('should change password successfully', async () => {
    const { post } = await import('@/api/client')
    const authStore = useAuthStore()

    const passwordData = {
      currentPassword: 'oldpass',
      newPassword: 'newpass',
      confirmPassword: 'newpass'
    }

    const mockResponse = { message: 'Password changed successfully' }
    vi.mocked(post).mockResolvedValue(mockResponse)

    const result = await authStore.changePassword(passwordData)

    expect(result).toEqual(mockResponse)
    expect(post).toHaveBeenCalledWith('/api/v1/auth/change-password', passwordData)
  })

  it('should request password reset successfully', async () => {
    const { post } = await import('@/api/client')
    const authStore = useAuthStore()

    const mockResponse = { message: 'Reset email sent' }
    vi.mocked(post).mockResolvedValue(mockResponse)

    const result = await authStore.requestPasswordReset('<EMAIL>')

    expect(result).toEqual(mockResponse)
    expect(post).toHaveBeenCalledWith('/api/v1/auth/forgot-password', { email: '<EMAIL>' })
  })

  it('should reset password successfully', async () => {
    const { post } = await import('@/api/client')
    const authStore = useAuthStore()

    const resetData = {
      token: 'reset-token',
      password: 'newpassword',
      confirmPassword: 'newpassword'
    }

    const mockResponse = { message: 'Password reset successful' }
    vi.mocked(post).mockResolvedValue(mockResponse)

    const result = await authStore.resetPassword(resetData)

    expect(result).toEqual(mockResponse)
    expect(post).toHaveBeenCalledWith('/api/v1/auth/reset-password', resetData)
  })

  it('should verify email successfully', async () => {
    const { post } = await import('@/api/client')
    const authStore = useAuthStore()

    const mockResponse = { message: 'Email verified' }
    vi.mocked(post).mockResolvedValue(mockResponse)

    const result = await authStore.verifyEmail('verification-token')

    expect(result).toEqual(mockResponse)
    expect(post).toHaveBeenCalledWith('/api/v1/auth/verify-email', { token: 'verification-token' })
  })

  it('should resend verification email successfully', async () => {
    const { post } = await import('@/api/client')
    const authStore = useAuthStore()

    const mockResponse = { message: 'Verification email sent' }
    vi.mocked(post).mockResolvedValue(mockResponse)

    const result = await authStore.resendVerificationEmail()

    expect(result).toEqual(mockResponse)
    expect(post).toHaveBeenCalledWith('/api/v1/auth/resend-verification')
  })

  it('should enable 2FA successfully', async () => {
    const { post } = await import('@/api/client')
    const authStore = useAuthStore()

    const mockResponse = {
      qr_code: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...',
      secret: 'JBSWY3DPEHPK3PXP'
    }

    vi.mocked(post).mockResolvedValue(mockResponse)

    const result = await authStore.enable2FA()

    expect(result).toEqual(mockResponse)
    expect(post).toHaveBeenCalledWith('/api/v1/auth/2fa/enable')
  })

  it('should verify 2FA successfully', async () => {
    const { post } = await import('@/api/client')
    const authStore = useAuthStore()

    const mockResponse = { message: '2FA enabled successfully' }
    vi.mocked(post).mockResolvedValue(mockResponse)

    const result = await authStore.verify2FA('123456')

    expect(result).toEqual(mockResponse)
    expect(post).toHaveBeenCalledWith('/api/v1/auth/2fa/verify', { code: '123456' })
  })

  it('should disable 2FA successfully', async () => {
    const { post } = await import('@/api/client')
    const authStore = useAuthStore()

    const mockResponse = { message: '2FA disabled successfully' }
    vi.mocked(post).mockResolvedValue(mockResponse)

    const result = await authStore.disable2FA('123456')

    expect(result).toEqual(mockResponse)
    expect(post).toHaveBeenCalledWith('/api/v1/auth/2fa/disable', { code: '123456' })
  })

  it('should check user permissions', () => {
    const authStore = useAuthStore()

    authStore.user = {
      id: 1,
      username: 'test',
      permissions: ['read:orders', 'write:orders', 'admin:users']
    }

    expect(authStore.userPermissions).toEqual(['read:orders', 'write:orders', 'admin:users'])
    expect(authStore.hasPermission('read:orders')).toBe(true)
    expect(authStore.hasPermission('delete:orders')).toBe(false)
  })

  it('should check user roles', () => {
    const authStore = useAuthStore()

    authStore.user = {
      id: 1,
      username: 'test',
      roles: ['trader', 'premium']
    }

    expect(authStore.userRoles).toEqual(['trader', 'premium'])
    expect(authStore.hasRole('trader')).toBe(true)
    expect(authStore.hasRole('admin')).toBe(false)
  })

  it('should handle token expiration', () => {
    const authStore = useAuthStore()

    authStore.token = 'expired-token'
    authStore.user = { id: 1, username: 'test' }

    authStore.handleTokenExpiration()

    expect(authStore.token).toBeNull()
    expect(authStore.user).toBeNull()
    expect(authStore.isAuthenticated).toBe(false)
  })

  it('should validate token format', () => {
    const authStore = useAuthStore()

    expect(authStore.isValidToken('valid.jwt.token')).toBe(true)
    expect(authStore.isValidToken('invalid-token')).toBe(false)
    expect(authStore.isValidToken('')).toBe(false)
    expect(authStore.isValidToken(null)).toBe(false)
  })

  it('should persist and restore authentication state', async () => {
    const { SecureStorage } = await import('@/utils/secureStorage')
    const authStore = useAuthStore()

    const mockSetToken = vi.spyOn(SecureStorage, 'setToken').mockImplementation(() => {})
    const mockSetUser = vi.spyOn(SecureStorage, 'setUser').mockImplementation(() => {})
    const mockGetToken = vi.spyOn(SecureStorage, 'getToken')
    const mockGetUser = vi.spyOn(SecureStorage, 'getUser')
    const mockClear = vi.spyOn(SecureStorage, 'clear').mockImplementation(() => {})

    // Test persist
    authStore.token = 'test-token'
    authStore.user = { id: 1, username: 'test' }
    authStore.persistAuth()

    expect(mockSetToken).toHaveBeenCalledWith('test-token')
    expect(mockSetUser).toHaveBeenCalledWith({ id: 1, username: 'test' })

    // Test restore
    mockGetToken.mockReturnValue('stored-token')
    mockGetUser.mockReturnValue({ id: 2, username: 'stored' })

    authStore.restoreAuth()

    expect(authStore.token).toBe('stored-token')
    expect(authStore.user).toEqual({ id: 2, username: 'stored' })

    // Test logout (which clears storage)
    authStore.logout()

    expect(authStore.token).toBeNull()
    expect(authStore.user).toBeNull()
    expect(mockClear).toHaveBeenCalled()

    // Cleanup
    mockSetToken.mockRestore()
    mockSetUser.mockRestore()
    mockGetToken.mockRestore()
    mockGetUser.mockRestore()
    mockClear.mockRestore()
  })

  it('should handle storage errors gracefully', () => {
    const authStore = useAuthStore()
    const mockGetItem = vi.spyOn(Storage.prototype, 'getItem')

    mockGetItem.mockImplementation(() => {
      throw new Error('Storage error')
    })

    // Should not throw error, use default state
    expect(() => authStore.restoreAuth()).not.toThrow()
    expect(authStore.token).toBeNull()
    expect(authStore.user).toBeNull()
  })
})
