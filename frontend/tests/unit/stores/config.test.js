/**
 * Config Store 测试
 * 测试配置管理、验证、持久化等核心功能
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { createPinia, setActivePinia } from 'pinia'
import { useConfigStore } from '@/stores/config'

// Mock UI store
vi.mock('@/stores/ui', () => ({
  useUIStore: () => ({
    showSuccess: vi.fn(),
    showWarning: vi.fn(),
    showError: vi.fn(),
    showInfo: vi.fn()
  })
}))

// Mock API client
const mockGet = vi.fn()
const mockPost = vi.fn()
const mockPut = vi.fn()
const mockDel = vi.fn()

vi.mock('@/api/client', () => ({
  get: mockGet,
  post: mockPost,
  put: mockPut,
  del: mockDel
}))

// Mock fetch for API calls
global.fetch = vi.fn()

describe('Config Store', () => {
  let configStore
  let pinia

  beforeEach(() => {
    // 重置 Mock
    vi.clearAllMocks()
    mockGet.mockClear()
    mockPost.mockClear()
    mockPut.mockClear()
    mockDel.mockClear()
    global.fetch.mockClear()

    // 创建新的 Pinia 实例
    pinia = createPinia()
    setActivePinia(pinia)

    // 获取 store 实例
    configStore = useConfigStore()
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('初始状态', () => {
    it('应该有正确的初始状态', () => {
      expect(configStore.exchangeConfigs).toEqual([])
      expect(configStore.riskConfig).toBe(null)
      expect(configStore.configs).toEqual({
        exchanges: [],
        risk: {},
        signals: [],
        system: {}
      })
      expect(configStore.loading).toBe(false)
      expect(configStore.error).toBe(null)
    })

    it('应该有正确的计算属性', () => {
      expect(configStore.hasExchangeConfig).toBe(false)
      expect(configStore.hasRiskConfig).toBe(false)
      expect(configStore.isConfigured).toBe(false)
      expect(configStore.enabledExchanges).toEqual([])
      expect(configStore.isAutoTradingEnabled).toBe(false)
    })
  })

  describe('交易所配置管理', () => {
    it('应该能够获取交易所配置', async () => {
      const mockConfigs = [
        { id: '1', name: 'Binance', enabled: true },
        { id: '2', name: 'OKX', enabled: false }
      ]

      global.fetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockConfigs
      })

      await configStore.fetchExchangeConfigs()

      expect(global.fetch).toHaveBeenCalledWith('/api/v1/configs/exchange')
      expect(configStore.exchangeConfigs).toEqual(mockConfigs)
      expect(configStore.hasExchangeConfig).toBe(true)
    })

    it('应该能够创建交易所配置', async () => {
      const newConfig = {
        name: 'Binance',
        api_key: 'test-key',
        api_secret: 'test-secret',
        enabled: true
      }

      const mockResponse = { id: '1', ...newConfig }

      // Mock fetch 而不是 API client，因为实际实现使用 fetch
      global.fetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse
      })

      const result = await configStore.createExchangeConfig(newConfig)

      expect(global.fetch).toHaveBeenCalledWith('/api/v1/configs/exchange', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(newConfig)
      })
      expect(result).toEqual(mockResponse)
      expect(configStore.exchangeConfigs).toEqual([mockResponse])
    })

    it('应该能够更新交易所配置', async () => {
      // 先设置一些初始配置
      configStore.exchangeConfigs = [
        { id: '1', name: 'Binance', enabled: true }
      ]

      const updatedConfig = {
        id: '1',
        name: 'Binance',
        enabled: false
      }

      // Mock fetch 而不是 API client
      global.fetch.mockResolvedValueOnce({
        ok: true,
        json: async () => updatedConfig
      })

      const result = await configStore.updateExchangeConfig('1', updatedConfig)

      expect(global.fetch).toHaveBeenCalledWith('/api/v1/exchanges/1', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(updatedConfig)
      })
      expect(result).toEqual(updatedConfig)
      expect(configStore.exchangeConfigs[0]).toEqual(updatedConfig)
    })

    it('应该能够删除交易所配置', async () => {
      // 先设置一些初始配置
      configStore.exchangeConfigs = [
        { id: '1', name: 'Binance', enabled: true },
        { id: '2', name: 'OKX', enabled: false }
      ]

      // Mock fetch 而不是 API client
      global.fetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({ success: true })
      })

      await configStore.deleteExchangeConfig('1')

      expect(global.fetch).toHaveBeenCalledWith('/api/v1/exchanges/1', {
        method: 'DELETE'
      })
      expect(configStore.exchangeConfigs).toHaveLength(1)
      expect(configStore.exchangeConfigs[0].id).toBe('2')
    })

    it('应该正确计算启用的交易所', () => {
      configStore.configs.exchanges = [
        { id: '1', name: 'Binance', enabled: true },
        { id: '2', name: 'OKX', enabled: false },
        { id: '3', name: 'Huobi', enabled: true }
      ]

      expect(configStore.enabledExchanges).toHaveLength(2)
      expect(configStore.enabledExchanges.map(e => e.name)).toEqual(['Binance', 'Huobi'])
    })
  })

  describe('风控配置管理', () => {
    it('应该能够获取风控配置', async () => {
      const mockRiskConfig = {
        max_position_size_usd: 1000,
        max_daily_loss_usd: 500,
        max_open_positions: 5,
        enable_stop_loss: true
      }

      global.fetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockRiskConfig
      })

      await configStore.fetchRiskConfig()

      expect(global.fetch).toHaveBeenCalledWith('/api/v1/configs/risk')
      expect(configStore.riskConfig).toEqual(mockRiskConfig)
      expect(configStore.hasRiskConfig).toBe(true)
    })

    it('应该能够更新风控配置', async () => {
      const updatedRiskConfig = {
        max_position_size_usd: 2000,
        max_daily_loss_usd: 1000,
        max_open_positions: 10,
        enable_stop_loss: false
      }

      // Mock fetch 而不是 API client
      global.fetch.mockResolvedValueOnce({
        ok: true,
        json: async () => updatedRiskConfig
      })

      const result = await configStore.updateRiskConfig(updatedRiskConfig)

      expect(global.fetch).toHaveBeenCalledWith('/api/v1/configs/risk', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(updatedRiskConfig)
      })
      expect(result).toEqual(updatedRiskConfig)
      expect(configStore.riskConfig).toEqual(updatedRiskConfig)
    })
  })

  describe('交易所连接测试', () => {
    it('应该能够测试交易所连接', async () => {
      const mockResult = {
        success: true,
        message: '连接成功',
        balance: { USDT: 1000 }
      }

      global.fetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockResult
      })

      const result = await configStore.testExchangeConnection('1')

      expect(global.fetch).toHaveBeenCalledWith('/api/v1/exchanges/1/test', {
        method: 'POST'
      })
      expect(result).toEqual(mockResult)
    })

    it('应该处理连接测试失败', async () => {
      global.fetch.mockResolvedValueOnce({
        ok: false
      })

      await expect(
        configStore.testExchangeConnection('1')
      ).rejects.toThrow('Failed to test exchange connection')

      expect(configStore.error).toBe('Failed to test exchange connection')
    })
  })

  describe('配置状态管理', () => {
    it('应该正确判断是否已配置', () => {
      // 初始状态未配置
      expect(configStore.isConfigured).toBe(false)

      // 只有交易所配置
      configStore.exchangeConfigs = [{ id: '1', name: 'Binance' }]
      expect(configStore.isConfigured).toBe(false)

      // 只有风控配置
      configStore.exchangeConfigs = []
      configStore.riskConfig = { max_position_size_usd: 1000 }
      expect(configStore.isConfigured).toBe(false)

      // 两者都有
      configStore.exchangeConfigs = [{ id: '1', name: 'Binance' }]
      configStore.riskConfig = { max_position_size_usd: 1000 }
      expect(configStore.isConfigured).toBe(true)
    })

    it('应该正确判断自动交易是否启用', () => {
      // 默认未启用
      expect(configStore.isAutoTradingEnabled).toBe(false)

      // 启用自动交易
      configStore.configs.system = { auto_trading_enabled: true }
      expect(configStore.isAutoTradingEnabled).toBe(true)

      // 禁用自动交易
      configStore.configs.system = { auto_trading_enabled: false }
      expect(configStore.isAutoTradingEnabled).toBe(false)
    })
  })

  describe('错误处理', () => {
    it('应该处理获取配置时的错误', async () => {
      global.fetch.mockResolvedValueOnce({
        ok: false
      })

      await configStore.fetchExchangeConfigs()

      expect(configStore.error).toBe('Failed to fetch exchange configs')
    })

    it('应该能够清除错误', () => {
      configStore.error = 'Some error'
      configStore.clearError()
      expect(configStore.error).toBe(null)
    })
  })

  describe('加载状态管理', () => {
    it('应该在API调用期间设置加载状态', async () => {
      // 创建一个延迟的 Promise
      let resolvePromise
      const delayedPromise = new Promise(resolve => {
        resolvePromise = resolve
      })

      global.fetch.mockReturnValueOnce(delayedPromise)

      // 开始API调用
      const fetchPromise = configStore.fetchExchangeConfigs()

      // 验证加载状态为 true
      expect(configStore.loading).toBe(true)

      // 完成API调用
      resolvePromise({
        ok: true,
        json: async () => []
      })
      await fetchPromise

      // 验证加载状态为 false
      expect(configStore.loading).toBe(false)
    })
  })

  describe('配置加载和保存', () => {
    it('应该能够加载所有配置', async () => {
      const mockExchangeConfigs = [{ id: '1', name: 'Binance' }]
      const mockRiskConfig = { max_position_size_usd: 1000 }

      global.fetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockExchangeConfigs
      })
      global.fetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockRiskConfig
      })

      await configStore.loadConfigs()

      expect(configStore.configs.exchanges).toEqual(mockExchangeConfigs)
      expect(configStore.configs.risk).toEqual(mockRiskConfig)
      // 验证加载成功
      expect(configStore.loading).toBe(false)
      expect(configStore.error).toBe(null)
    })

    it('应该在加载失败时使用默认配置', async () => {
      global.fetch.mockResolvedValue({
        ok: false
      })

      await configStore.loadConfigs()

      expect(configStore.configs.exchanges).toEqual([])
      // 验证错误被正确设置（可能是任何一个失败的请求）
      expect(configStore.error).toMatch(/Failed to fetch/)
      expect(configStore.loading).toBe(false)
    })

    it('应该能够保存所有配置', async () => {
      const newConfigs = {
        exchanges: [{ name: 'Binance', enabled: true }],
        risk: { max_position_size_usd: 2000 }
      }

      // Mock fetch 调用
      global.fetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({ id: '1', ...newConfigs.exchanges[0] })
      })
      global.fetch.mockResolvedValueOnce({
        ok: true,
        json: async () => newConfigs.risk
      })

      await configStore.saveConfigs(newConfigs)

      expect(global.fetch).toHaveBeenCalledTimes(2)
      expect(configStore.configs.exchanges).toEqual(newConfigs.exchanges)
      expect(configStore.configs.risk).toEqual(newConfigs.risk)
    })

    it('应该能够更新现有交易所配置', async () => {
      const newConfigs = {
        exchanges: [{ id: '1', name: 'Binance', enabled: false }]
      }

      global.fetch.mockResolvedValueOnce({
        ok: true,
        json: async () => newConfigs.exchanges[0]
      })

      await configStore.saveConfigs(newConfigs)

      expect(global.fetch).toHaveBeenCalledWith('/api/v1/exchanges/1', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(newConfigs.exchanges[0])
      })
    })

    it('应该处理保存配置时的错误', async () => {
      const newConfigs = {
        exchanges: [{ name: 'Binance' }]
      }

      global.fetch.mockResolvedValueOnce({
        ok: false
      })

      await expect(configStore.saveConfigs(newConfigs)).rejects.toThrow('Failed to create exchange config')
      expect(configStore.error).toBe('Failed to create exchange config')
    })
  })

  describe('配置初始化', () => {
    it('应该能够初始化配置', async () => {
      // Mock API 调用
      global.fetch.mockResolvedValue({
        ok: true,
        json: async () => []
      })

      await configStore.initializeConfigs()

      // 验证 API 被调用了
      expect(global.fetch).toHaveBeenCalled()
    })
  })

  describe('边界条件测试', () => {
    it('应该处理空的交易所配置数组', () => {
      configStore.configs.exchanges = []
      expect(configStore.enabledExchanges).toEqual([])
    })

    it('应该处理 null 的系统配置', () => {
      configStore.configs.system = null
      expect(configStore.isAutoTradingEnabled).toBe(false)
    })

    it('应该处理 undefined 的交易所配置', () => {
      configStore.configs.exchanges = undefined
      expect(configStore.enabledExchanges).toEqual([])
    })

    it('应该处理没有 enabled 字段的交易所配置', () => {
      configStore.configs.exchanges = [
        { id: '1', name: 'Binance' }, // 没有 enabled 字段
        { id: '2', name: 'OKX', enabled: true }
      ]
      expect(configStore.enabledExchanges).toHaveLength(1)
      expect(configStore.enabledExchanges[0].name).toBe('OKX')
    })
  })
})
