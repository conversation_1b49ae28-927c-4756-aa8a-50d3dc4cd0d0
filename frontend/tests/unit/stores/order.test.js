/**
 * Order Store 测试
 * 测试订单状态管理功能
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { createPinia, setActivePinia } from 'pinia'
import { useOrderStore } from '@/stores/order'

// Mock API client
vi.mock('@/api/client', () => ({
  get: vi.fn(),
  post: vi.fn(),
  del: vi.fn()
}))

// Mock auth store
vi.mock('@/stores/auth', () => ({
  useAuthStore: () => ({
    user: { id: 'test-user-id' },
    isAuthenticated: true,
    getAuthHeaders: () => ({ Authorization: 'Bearer test-token' })
  })
}))

// Mock UI store
vi.mock('@/stores/ui', () => ({
  useUIStore: () => ({
    showSuccess: vi.fn(),
    showError: vi.fn()
  })
}))

describe('Order Store', () => {
  let orderStore
  let pinia

  beforeEach(() => {
    // 创建新的 Pinia 实例
    pinia = createPinia()
    setActivePinia(pinia)

    // 获取 store 实例
    orderStore = useOrderStore()

    // 清除所有 mock
    vi.clearAllMocks()
  })

  describe('初始状态', () => {
    it('应该有正确的初始状态', () => {
      expect(orderStore.orders).toEqual([])
      expect(orderStore.loading).toBe(false)
      expect(orderStore.error).toBe(null)
      expect(orderStore.filters).toEqual({
        status: '',
        symbol: '',
        side: '',
        dateRange: null
      })
    })
  })

  describe('计算属性', () => {
    beforeEach(() => {
      // 设置测试数据 - 使用实际的状态值
      orderStore.orders = [
        {
          id: '1',
          symbol: 'BTC/USDT',
          side: 'buy',
          status: 'filled',
          created_at: '2024-01-01T10:00:00Z'
        },
        {
          id: '2',
          symbol: 'ETH/USDT',
          side: 'sell',
          status: 'pending',
          created_at: '2024-01-01T11:00:00Z'
        }
      ]
    })

    it('应该正确计算活跃订单', () => {
      expect(orderStore.activeOrders).toHaveLength(1)
      expect(orderStore.activeOrders[0].status).toBe('pending')
    })

    it('应该正确计算已完成订单', () => {
      expect(orderStore.completedOrders).toHaveLength(1)
      expect(orderStore.completedOrders[0].status).toBe('filled')
    })

    it('应该正确计算总订单数', () => {
      expect(orderStore.totalOrders).toBe(2)
    })

    it('应该正确计算活跃订单数量', () => {
      expect(orderStore.activeOrdersCount).toBe(1)
    })
  })

  describe('方法', () => {
    it('应该能够设置过滤器', () => {
      const newFilters = {
        status: 'FILLED',
        symbol: 'BTC',
        side: 'BUY',
        dateRange: ['2024-01-01', '2024-01-02']
      }

      orderStore.setFilters(newFilters)
      expect(orderStore.filters).toEqual(newFilters)
    })

    it('应该能够清除过滤器', () => {
      // 先设置一些过滤器
      orderStore.setFilters({ status: 'FILLED', symbol: 'BTC' })
      
      // 然后清除
      orderStore.clearFilters()
      
      expect(orderStore.filters).toEqual({
        status: '',
        symbol: '',
        side: '',
        dateRange: null
      })
    })

    it('应该能够清除错误', () => {
      orderStore.error = 'Test error'
      orderStore.clearError()
      expect(orderStore.error).toBe(null)
    })
  })

  describe('工具方法', () => {
    it('应该正确格式化订单状态文本', () => {
      expect(orderStore.getOrderStatusText('filled')).toBe('已成交')
      expect(orderStore.getOrderStatusText('pending')).toBe('待成交')
      expect(orderStore.getOrderStatusText('cancelled')).toBe('已取消')
    })

    it('应该正确格式化订单方向文本', () => {
      expect(orderStore.getOrderSideText('buy')).toBe('买入')
      expect(orderStore.getOrderSideText('sell')).toBe('卖出')
    })

    it('应该正确格式化订单类型文本', () => {
      expect(orderStore.getOrderTypeText('market')).toBe('市价单')
      expect(orderStore.getOrderTypeText('limit')).toBe('限价单')
      expect(orderStore.getOrderTypeText('stop')).toBe('止损单')
    })

    it('应该正确计算订单价值', () => {
      const order = {
        quantity: '1.5',
        price: '50000',
        side: 'buy'
      }
      expect(orderStore.calculateOrderValue(order)).toBe(75000)
    })

    it('应该正确计算手续费', () => {
      const order = {
        quantity: '1.0',
        price: '50000',
        side: 'buy'
      }
      const fee = orderStore.calculateFee(order, 0.001) // 0.1% 手续费
      expect(fee).toBe(50)
    })

    it('应该正确验证订单数据', () => {
      const validOrder = {
        symbol: 'BTC/USDT',
        side: 'buy',
        quantity: '1.0',
        price: '50000'
      }
      expect(orderStore.validateOrder(validOrder)).toBe(true)

      const invalidOrder = {
        symbol: '',
        side: 'buy',
        quantity: '0',
        price: '50000'
      }
      expect(orderStore.validateOrder(invalidOrder)).toBe(false)
    })
  })

  describe('订单获取', () => {
    it('应该能够获取订单列表', async () => {
      // Mock fetch API
      global.fetch = vi.fn().mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve([
          { id: '1', symbol: 'BTC/USDT', side: 'buy', status: 'filled' },
          { id: '2', symbol: 'ETH/USDT', side: 'sell', status: 'pending' }
        ])
      })

      await orderStore.fetchOrders()

      expect(orderStore.loading).toBe(false)
      expect(orderStore.error).toBe(null)
    })

    it('应该处理获取订单时的错误', async () => {
      const { get } = await import('@/api/client')
      vi.mocked(get).mockRejectedValueOnce(new Error('Network error'))

      try {
        await orderStore.fetchOrders()
      } catch (error) {
        // 期望抛出异常
      }

      expect(orderStore.loading).toBe(false)
      expect(orderStore.error).toBe('Network error')
    })

    it('应该能够获取条件订单', async () => {
      const { get } = await import('@/api/client')
      const mockData = [
        { id: '1', type: 'stop_loss', trigger_price: '45000', status: 'active' }
      ]
      vi.mocked(get).mockResolvedValueOnce(mockData)

      const result = await orderStore.fetchConditionalOrders()
      expect(result).toBeDefined()
      expect(result).toEqual(mockData)
    })

    it('应该能够获取单个订单详情', async () => {
      const { get } = await import('@/api/client')
      const mockData = { id: '1', symbol: 'BTC/USDT', status: 'filled' }
      vi.mocked(get).mockResolvedValueOnce(mockData)

      const result = await orderStore.fetchOrderById('1')
      expect(result).toBeDefined()
      expect(result).toEqual(mockData)
    })
  })

  describe('订单创建', () => {
    it('应该能够创建条件订单', async () => {
      const conditionalOrder = {
        symbol: 'BTC/USDT',
        type: 'stop_loss',
        trigger_price: '45000',
        quantity: '1.0'
      }

      const { post } = await import('@/api/client')
      const mockData = { id: '456', ...conditionalOrder, status: 'active' }
      vi.mocked(post).mockResolvedValueOnce(mockData)

      const result = await orderStore.createConditionalOrder(conditionalOrder)
      expect(result).toBeDefined()
      expect(result).toEqual(mockData)
    })

    it('应该处理创建条件订单时的错误', async () => {
      const { post } = await import('@/api/client')
      vi.mocked(post).mockRejectedValueOnce(new Error('Insufficient balance'))

      await expect(orderStore.createConditionalOrder({})).rejects.toThrow('Insufficient balance')
    })
  })

  describe('订单取消', () => {
    it('应该能够取消条件订单', async () => {
      const orderId = '456'

      global.fetch = vi.fn().mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({ success: true })
      })

      const result = await orderStore.cancelConditionalOrder(orderId)
      expect(result).toBeDefined()
    })

    it('应该处理取消条件订单时的错误', async () => {
      global.fetch = vi.fn().mockRejectedValueOnce(new Error('Order not found'))

      await expect(orderStore.cancelConditionalOrder('123')).rejects.toThrow()
    })
  })




})
