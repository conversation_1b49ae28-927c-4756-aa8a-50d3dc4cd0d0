/**
 * Discord配置Store单元测试
 * 测试Discord配置状态管理的所有功能
 * 
 * 按照项目测试规范：
 * - 测试store的状态管理
 * - 测试API调用和错误处理
 * - 使用mock隔离外部依赖
 * - 使用中文描述测试用例
 */
import { describe, test, expect, beforeEach, vi } from 'vitest'
import { setActivePinia, createPinia } from 'pinia'
import { useDiscordConfigStore } from '@/stores/discordConfig.ts'
import * as discordConfigApi from '@/api/discordConfig.ts'

// Mock API模块
vi.mock('@/api/discordConfig', () => ({
  getDiscordConfigs: vi.fn(),
  getDiscordConfig: vi.fn(),
  createDiscordConfig: vi.fn(),
  updateDiscordConfig: vi.fn(),
  deleteDiscordConfig: vi.fn()
}))

// Mock认证store
vi.mock('@/stores/auth', () => ({
  useAuthStore: () => ({
    token: 'mock-token',
    isAuthenticated: true
  })
}))

describe('Discord配置Store测试', () => {
  let store
  let mockApi

  beforeEach(() => {
    // 设置Pinia
    setActivePinia(createPinia())
    store = useDiscordConfigStore()

    // 获取mock的API函数
    mockApi = vi.mocked(discordConfigApi)

    // 重置所有mock
    vi.clearAllMocks()
  })

  describe('初始状态测试', () => {
    test('应该有正确的初始状态', () => {
      expect(store.configs).toEqual([])
      expect(store.loading).toBe(false)
      expect(store.error).toBe(null)
      expect(store.configCount).toBe(0)
      expect(store.enabledConfigs).toEqual([])
    })
  })

  describe('获取配置列表测试', () => {
    test('应该成功获取配置列表', async () => {
      // 准备mock数据
      const mockConfigs = [
        {
          id: '1',
          source_name: '测试配置1',
          enabled: true,
          has_token: true,
          server_ids: ['123456789'],
          channel_ids: ['111111111'],
          author_ids: ['333333333'],
          allowed_message_types: ['text'],
          created_at: '2023-01-01T00:00:00Z',
          updated_at: '2023-01-01T00:00:00Z'
        },
        {
          id: '2',
          source_name: '测试配置2',
          enabled: false,
          has_token: true,
          server_ids: ['987654321'],
          channel_ids: ['222222222'],
          author_ids: [],
          allowed_message_types: ['text'],
          created_at: '2023-01-02T00:00:00Z',
          updated_at: '2023-01-02T00:00:00Z'
        }
      ]

      mockApi.getDiscordConfigs.mockResolvedValue(mockConfigs)

      // 执行操作
      await store.fetchConfigs()

      // 验证结果
      expect(store.configs).toEqual(mockConfigs)
      expect(store.loading).toBe(false)
      expect(store.error).toBe(null)
      expect(store.configCount).toBe(2)
      expect(store.enabledConfigs).toHaveLength(1)
      expect(store.enabledConfigs[0].id).toBe('1')
      expect(mockApi.getDiscordConfigs).toHaveBeenCalledOnce()
    })

    test('应该处理获取配置列表失败', async () => {
      // 准备mock错误
      const mockError = new Error('网络错误')
      mockApi.getDiscordConfigs.mockRejectedValue(mockError)

      // 执行操作
      await store.fetchConfigs()

      // 验证结果
      expect(store.configs).toEqual([])
      expect(store.loading).toBe(false)
      expect(store.error).toBe('获取Discord配置失败: 网络错误')
      expect(mockApi.getDiscordConfigs).toHaveBeenCalledOnce()
    })

    test('应该在获取过程中设置loading状态', async () => {
      // 创建一个延迟的Promise
      let resolvePromise
      const delayedPromise = new Promise(resolve => {
        resolvePromise = resolve
      })
      mockApi.getDiscordConfigs.mockReturnValue(delayedPromise)

      // 开始获取
      const fetchPromise = store.fetchConfigs()
      
      // 验证loading状态
      expect(store.loading).toBe(true)
      
      // 完成Promise
      resolvePromise([])
      await fetchPromise
      
      // 验证loading状态重置
      expect(store.loading).toBe(false)
    })
  })

  describe('创建配置测试', () => {
    test('应该成功创建Discord配置', async () => {
      // 准备测试数据
      const configData = {
        source_name: '新建Discord配置',
        enabled: true,
        token: 'test_token',
        server_ids: ['123456789'],
        channel_ids: ['111111111'],
        author_ids: ['333333333'],
        allowed_message_types: ['text']
      }

      const mockCreatedConfig = {
        id: 'new-config-id',
        user_id: 'user-id',
        ...configData,
        has_token: true,
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-01T00:00:00Z'
      }

      mockApi.createDiscordConfig.mockResolvedValue(mockCreatedConfig)

      // 执行操作
      const result = await store.createConfig(configData)

      // 验证结果
      expect(result).toBe(true)
      expect(store.configs).toHaveLength(1)
      expect(store.configs[0]).toEqual(mockCreatedConfig)
      expect(store.error).toBe(null)
      expect(mockApi.createDiscordConfig).toHaveBeenCalledWith(configData)
    })

    test('应该处理创建配置失败', async () => {
      const configData = {
        source_name: '失败的配置',
        enabled: true,
        token: 'test_token',
        server_ids: [],
        channel_ids: [],
        author_ids: [],
        allowed_message_types: ['text']
      }

      const mockError = new Error('创建失败')
      mockApi.createDiscordConfig.mockRejectedValue(mockError)

      // 执行操作
      const result = await store.createConfig(configData)

      // 验证结果
      expect(result).toBe(false)
      expect(store.error).toBe('创建Discord配置失败: 创建失败')
      expect(store.configs).toEqual([])
    })
  })

  describe('更新配置测试', () => {
    test('应该成功更新Discord配置', async () => {
      // 准备初始配置
      const initialConfig = {
        id: 'config-1',
        source_name: '原始配置',
        enabled: false,
        has_token: true,
        server_ids: ['123456789'],
        channel_ids: ['111111111'],
        author_ids: [],
        allowed_message_types: ['text'],
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-01T00:00:00Z'
      }

      store.configs = [initialConfig]

      // 准备更新数据
      const updateData = {
        source_name: '更新后的配置',
        enabled: true,
        token: 'updated_token',
        server_ids: ['987654321'],
        channel_ids: ['222222222'],
        author_ids: ['333333333'],
        allowed_message_types: ['text', 'embed']
      }

      const mockUpdatedConfig = {
        ...initialConfig,
        ...updateData,
        has_token: true,
        updated_at: '2023-01-02T00:00:00Z'
      }

      mockApi.updateDiscordConfig.mockResolvedValue(mockUpdatedConfig)

      // 执行操作
      const result = await store.updateConfig('config-1', updateData)

      // 验证结果
      expect(result).toBe(true)
      expect(store.configs[0]).toEqual(mockUpdatedConfig)
      expect(store.error).toBe(null)
      expect(mockApi.updateDiscordConfig).toHaveBeenCalledWith('config-1', updateData)
    })

    test('应该处理更新不存在的配置', async () => {
      const updateData = {
        source_name: '不存在的配置',
        enabled: true,
        token: 'test_token',
        server_ids: [],
        channel_ids: [],
        author_ids: [],
        allowed_message_types: ['text']
      }

      const mockError = new Error('配置未找到')
      mockApi.updateDiscordConfig.mockRejectedValue(mockError)

      // 执行操作
      const result = await store.updateConfig('non-existent-id', updateData)

      // 验证结果
      expect(result).toBe(false)
      expect(store.error).toBe('更新Discord配置失败: 配置未找到')
    })
  })

  describe('删除配置测试', () => {
    test('应该成功删除Discord配置', async () => {
      // 准备初始配置
      const configs = [
        { id: 'config-1', source_name: '配置1' },
        { id: 'config-2', source_name: '配置2' }
      ]
      store.configs = [...configs]

      mockApi.deleteDiscordConfig.mockResolvedValue({ message: '删除成功' })

      // 执行操作
      const result = await store.deleteConfig('config-1')

      // 验证结果
      expect(result).toBe(true)
      expect(store.configs).toHaveLength(1)
      expect(store.configs[0].id).toBe('config-2')
      expect(store.error).toBe(null)
      expect(mockApi.deleteDiscordConfig).toHaveBeenCalledWith('config-1')
    })

    test('应该处理删除配置失败', async () => {
      store.configs = [{ id: 'config-1', source_name: '配置1' }]

      const mockError = new Error('删除失败')
      mockApi.deleteDiscordConfig.mockRejectedValue(mockError)

      // 执行操作
      const result = await store.deleteConfig('config-1')

      // 验证结果
      expect(result).toBe(false)
      expect(store.configs).toHaveLength(1) // 配置应该仍然存在
      expect(store.error).toBe('删除Discord配置失败: 删除失败')
    })
  })

  describe('工具方法测试', () => {
    test('应该正确获取指定ID的配置', () => {
      const configs = [
        { id: 'config-1', source_name: '配置1' },
        { id: 'config-2', source_name: '配置2' }
      ]
      store.configs = configs

      const config = store.getConfigById('config-1')
      expect(config).toEqual(configs[0])

      const nonExistentConfig = store.getConfigById('non-existent')
      expect(nonExistentConfig).toBeUndefined()
    })

    test('应该正确清除错误信息', () => {
      store.error = '测试错误信息'
      store.clearError()
      expect(store.error).toBe(null)
    })

    test('应该正确计算启用的配置数量', () => {
      store.configs = [
        { id: '1', enabled: true },
        { id: '2', enabled: false },
        { id: '3', enabled: true },
        { id: '4', enabled: false }
      ]

      expect(store.enabledConfigs).toHaveLength(2)
      expect(store.enabledConfigs.map(c => c.id)).toEqual(['1', '3'])
    })
  })

  describe('错误处理测试', () => {
    test('应该处理API响应错误', async () => {
      const apiError = {
        response: {
          data: {
            detail: 'API详细错误信息'
          }
        }
      }

      mockApi.getDiscordConfigs.mockRejectedValue(apiError)

      await store.fetchConfigs()

      expect(store.error).toBe('获取Discord配置失败: API详细错误信息')
    })

    test('应该处理网络错误', async () => {
      const networkError = new Error('Network Error')
      networkError.code = 'NETWORK_ERROR'

      mockApi.getDiscordConfigs.mockRejectedValue(networkError)

      await store.fetchConfigs()

      expect(store.error).toBe('获取Discord配置失败: Network Error')
    })
  })
})
