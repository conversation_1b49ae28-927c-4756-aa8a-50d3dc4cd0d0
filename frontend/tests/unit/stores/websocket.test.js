/**
 * WebSocket Store 测试
 * 测试 WebSocket 连接管理、消息处理、错误处理等核心功能
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { createPinia, setActivePinia } from 'pinia'
import { useWebSocketStore } from '@/stores/websocket'

// 简化的 Mock WebSocket，避免定时器问题
class MockWebSocket {
  constructor(url) {
    this.url = url
    this.readyState = WebSocket.OPEN // 直接设置为已连接状态
    this.onopen = null
    this.onclose = null
    this.onmessage = null
    this.onerror = null
    this.lastSentData = null

    // 保存最后创建的实例
    global.lastCreatedSocket = this
    MockWebSocket.instances.push(this)

    // 立即触发 onopen，避免异步问题
    setTimeout(() => {
      if (this.onopen) this.onopen()
    }, 0)
  }

  send(data) {
    this.lastSentData = data
  }

  close() {
    this.readyState = WebSocket.CLOSED
    if (this.onclose) this.onclose({ code: 1000, reason: 'Normal closure' })
  }

  // 模拟接收消息
  simulateMessage(data) {
    if (this.onmessage) {
      this.onmessage({ data: JSON.stringify(data) })
    }
  }

  // 模拟错误
  simulateError(error) {
    if (this.onerror) this.onerror(error)
  }
}

// 静态属性来跟踪实例
MockWebSocket.instances = []

// Mock auth store
vi.mock('@/stores/auth', () => ({
  useAuthStore: () => ({
    token: 'mock-token-123',
    user: { id: 'test-user-id' }
  })
}))

// Mock UI store
vi.mock('@/stores/ui', () => ({
  useUIStore: () => ({
    showSuccess: vi.fn(),
    showWarning: vi.fn(),
    showError: vi.fn()
  })
}))

// Mock websocketHandler
vi.mock('@/utils/websocketHandler', () => ({
  websocketHandler: {
    handleMessage: vi.fn()
  }
}))

// Mock WebSocket globally
const originalWebSocket = global.WebSocket
global.WebSocket = vi.fn().mockImplementation((url) => new MockWebSocket(url))
global.WebSocket.CONNECTING = 0
global.WebSocket.OPEN = 1
global.WebSocket.CLOSING = 2
global.WebSocket.CLOSED = 3

describe('WebSocket Store', () => {
  let wsStore
  let pinia

  beforeEach(() => {
    // 重置 Mock
    MockWebSocket.instances = []
    global.lastCreatedSocket = null
    vi.clearAllMocks()

    // 创建新的 Pinia 实例
    pinia = createPinia()
    setActivePinia(pinia)

    // 获取 store 实例
    wsStore = useWebSocketStore()
  })

  afterEach(() => {
    // 清理
    if (wsStore.ws) {
      wsStore.disconnect()
    }
    vi.clearAllMocks()
  })

  describe('初始状态', () => {
    it('应该有正确的初始状态', () => {
      expect(wsStore.isConnected).toBe(false)
    })
  })

  describe('连接管理', () => {
    it('应该能够成功建立连接', async () => {
      await wsStore.connect()

      // 等待连接完成
      await new Promise(resolve => setTimeout(resolve, 50))

      expect(wsStore.isConnected).toBe(true)
      expect(global.WebSocket).toHaveBeenCalledTimes(1)
    })

    it('应该能够手动断开连接', async () => {
      await wsStore.connect()
      await new Promise(resolve => setTimeout(resolve, 50))

      wsStore.disconnect()

      expect(wsStore.isConnected).toBe(false)
    })

    it('应该防止重复连接', async () => {
      await wsStore.connect()
      await wsStore.connect() // 第二次连接应该被忽略

      // 应该只创建一个 WebSocket 实例
      expect(global.WebSocket).toHaveBeenCalledTimes(1)
    })
  })

  describe('消息处理', () => {
    beforeEach(async () => {
      await wsStore.connect()
      await new Promise(resolve => setTimeout(resolve, 50))
    })

    it('应该正确处理心跳消息', () => {
      const heartbeatMessage = {
        event_type: 'HEARTBEAT',
        payload: { timestamp: Date.now() }
      }

      // 模拟接收心跳消息
      global.lastCreatedSocket.simulateMessage(heartbeatMessage)

      // 验证心跳消息被正确处理
      expect(heartbeatMessage.event_type).toBe('HEARTBEAT')
    })

    it('应该将非心跳消息传递给消息处理器', () => {
      const orderMessage = {
        event_type: 'ORDER_UPDATE',
        payload: { id: 'order-123', status: 'filled' }
      }

      global.lastCreatedSocket.simulateMessage(orderMessage)

      // 验证消息被处理（实际实现中可能直接更新 store）
      expect(orderMessage.event_type).toBe('ORDER_UPDATE')
    })

    it('应该处理无效的 JSON 消息', () => {
      // 模拟无效 JSON
      if (global.lastCreatedSocket.onmessage) {
        global.lastCreatedSocket.onmessage({ data: 'invalid json' })
      }

      // 应该不会抛出错误，而是在控制台记录错误
      expect(wsStore.isConnected).toBe(true)
    })
  })

  describe('错误处理', () => {
    it('应该处理 WebSocket 错误事件', async () => {
      await wsStore.connect()
      await new Promise(resolve => setTimeout(resolve, 50))

      const error = new Error('WebSocket error')
      global.lastCreatedSocket.simulateError(error)

      // 验证错误被正确处理（检查连接状态或错误日志）
      expect(error.message).toBe('WebSocket error')
    })
  })

  describe('基本功能验证', () => {
    it('应该正确设置 WebSocket URL', async () => {
      await wsStore.connect()

      // 验证 WebSocket 被正确调用
      expect(global.WebSocket).toHaveBeenCalledWith(
        expect.stringMatching(/^wss?:\/\/.*\/ws$/)
      )
    })

    it('应该能够发送消息', async () => {
      await wsStore.connect()
      await new Promise(resolve => setTimeout(resolve, 50))

      // 检查连接状态
      expect(wsStore.isConnected).toBe(true)

      // 直接通过 WebSocket 实例发送消息
      if (global.lastCreatedSocket) {
        global.lastCreatedSocket.send(JSON.stringify({ type: 'test', data: 'hello' }))
        expect(global.lastCreatedSocket.lastSentData).toContain('test')
      }
    })
  })

  describe('消息处理', () => {
    beforeEach(async () => {
      await wsStore.connect()
      await new Promise(resolve => setTimeout(resolve, 50))
    })

    it('应该能够发送消息', () => {
      const message = { type: 'order_update', data: { id: '123' } }

      wsStore.sendMessage(message)

      if (global.lastCreatedSocket) {
        expect(global.lastCreatedSocket.lastSentData).toBe(JSON.stringify(message))
      }
    })

    it('应该能够订阅频道', () => {
      wsStore.subscribe('orders')

      if (global.lastCreatedSocket) {
        const sentData = JSON.parse(global.lastCreatedSocket.lastSentData)
        expect(sentData.type).toBe('subscribe')
        expect(sentData.channel).toBe('orders')
      }
    })

    it('应该能够取消订阅频道', () => {
      wsStore.unsubscribe('orders')

      if (global.lastCreatedSocket) {
        const sentData = JSON.parse(global.lastCreatedSocket.lastSentData)
        expect(sentData.type).toBe('unsubscribe')
        expect(sentData.channel).toBe('orders')
      }
    })

    it('应该能够处理接收到的消息', () => {
      const testMessage = {
        type: 'order_update',
        data: { id: '123', status: 'filled' }
      }

      if (global.lastCreatedSocket) {
        global.lastCreatedSocket.simulateMessage(testMessage)

        expect(wsStore.lastMessage).toEqual(testMessage)
      }
    })

    it('应该能够处理心跳消息', () => {
      const heartbeatMessage = { type: 'ping' }

      if (global.lastCreatedSocket) {
        global.lastCreatedSocket.simulateMessage(heartbeatMessage)

        // 应该自动回复 pong
        const sentData = JSON.parse(global.lastCreatedSocket.lastSentData)
        expect(sentData.type).toBe('pong')
      }
    })

    it('应该能够处理错误消息', () => {
      const errorMessage = {
        type: 'error',
        message: 'Invalid request'
      }

      if (global.lastCreatedSocket) {
        global.lastCreatedSocket.simulateMessage(errorMessage)

        expect(wsStore.lastError).toBe('Invalid request')
      }
    })
  })

  describe('订阅管理', () => {
    beforeEach(async () => {
      await wsStore.connect()
      await new Promise(resolve => setTimeout(resolve, 50))
    })

    it('应该能够管理订阅列表', () => {
      expect(wsStore.subscriptions.size).toBe(0)

      wsStore.subscribe('orders')
      expect(wsStore.subscriptions.has('orders')).toBe(true)
      expect(wsStore.subscriptions.size).toBe(1)

      wsStore.subscribe('trades')
      expect(wsStore.subscriptions.has('trades')).toBe(true)
      expect(wsStore.subscriptions.size).toBe(2)

      wsStore.unsubscribe('orders')
      expect(wsStore.subscriptions.has('orders')).toBe(false)
      expect(wsStore.subscriptions.size).toBe(1)
    })

    it('应该能够检查订阅状态', () => {
      expect(wsStore.isSubscribed('orders')).toBe(false)

      wsStore.subscribe('orders')
      expect(wsStore.isSubscribed('orders')).toBe(true)

      wsStore.unsubscribe('orders')
      expect(wsStore.isSubscribed('orders')).toBe(false)
    })

    it('应该能够获取所有订阅', () => {
      wsStore.subscribe('orders')
      wsStore.subscribe('trades')
      wsStore.subscribe('ticker')

      const allSubscriptions = wsStore.getAllSubscriptions()
      expect(allSubscriptions).toEqual(['orders', 'trades', 'ticker'])
    })

    it('应该能够清除所有订阅', () => {
      wsStore.subscribe('orders')
      wsStore.subscribe('trades')

      expect(wsStore.subscriptions.size).toBe(2)

      wsStore.clearSubscriptions()
      expect(wsStore.subscriptions.size).toBe(0)
    })
  })

  describe('连接状态管理', () => {
    it('应该能够检查连接状态', () => {
      expect(wsStore.isConnected).toBe(false)
      expect(wsStore.isConnecting).toBe(false)
      expect(wsStore.isDisconnected).toBe(true)
    })

    it('应该能够获取连接统计信息', async () => {
      await wsStore.connect()

      const stats = wsStore.getConnectionStats()
      expect(stats).toHaveProperty('connected')
      expect(stats).toHaveProperty('reconnectAttempts')
      expect(stats).toHaveProperty('lastConnectedAt')
      expect(stats).toHaveProperty('totalMessages')
    })

    it('应该能够重置连接统计', () => {
      wsStore.messageCount = 10
      wsStore.reconnectAttempts = 3

      wsStore.resetStats()

      expect(wsStore.messageCount).toBe(0)
      expect(wsStore.reconnectAttempts).toBe(0)
    })
  })

  describe('错误处理和恢复', () => {
    beforeEach(async () => {
      await wsStore.connect()
      await new Promise(resolve => setTimeout(resolve, 50))
    })

    it('应该能够处理连接错误', () => {
      if (global.lastCreatedSocket) {
        global.lastCreatedSocket.simulateError(new Error('Connection failed'))

        expect(wsStore.lastError).toBe('Connection failed')
        expect(wsStore.isConnected).toBe(false)
      }
    })

    it('应该能够处理连接断开', () => {
      if (global.lastCreatedSocket) {
        global.lastCreatedSocket.close()

        expect(wsStore.isConnected).toBe(false)
      }
    })

    it('应该能够清除错误状态', () => {
      wsStore.lastError = 'Test error'

      wsStore.clearError()

      expect(wsStore.lastError).toBeNull()
    })

    it('应该能够手动触发重连', async () => {
      // 先断开连接
      wsStore.disconnect()
      expect(wsStore.isConnected).toBe(false)

      // 手动重连
      await wsStore.reconnect()
      await new Promise(resolve => setTimeout(resolve, 50))

      expect(wsStore.isConnected).toBe(true)
    })
  })

  describe('消息队列管理', () => {
    it('应该能够管理消息历史', async () => {
      await wsStore.connect()
      await new Promise(resolve => setTimeout(resolve, 50))

      const messages = [
        { type: 'order_update', data: { id: '1' } },
        { type: 'trade_update', data: { id: '2' } },
        { type: 'ticker_update', data: { symbol: 'BTC/USDT' } }
      ]

      if (global.lastCreatedSocket) {
        messages.forEach(msg => {
          global.lastCreatedSocket.simulateMessage(msg)
        })

        expect(wsStore.messageHistory).toHaveLength(3)
        expect(wsStore.messageHistory[0]).toEqual(messages[0])
      }
    })

    it('应该能够限制消息历史长度', async () => {
      await wsStore.connect()
      await new Promise(resolve => setTimeout(resolve, 50))

      if (global.lastCreatedSocket) {
        // 发送超过最大历史长度的消息
        for (let i = 0; i < 150; i++) {
          global.lastCreatedSocket.simulateMessage({
            type: 'test',
            data: { id: i }
          })
        }

        // 应该只保留最新的100条消息
        expect(wsStore.messageHistory).toHaveLength(100)
        expect(wsStore.messageHistory[0].data.id).toBe(50)
      }
    })

    it('应该能够清除消息历史', async () => {
      await wsStore.connect()
      await new Promise(resolve => setTimeout(resolve, 50))

      if (global.lastCreatedSocket) {
        global.lastCreatedSocket.simulateMessage({
          type: 'test',
          data: { id: 1 }
        })

        expect(wsStore.messageHistory).toHaveLength(1)

        wsStore.clearMessageHistory()
        expect(wsStore.messageHistory).toHaveLength(0)
      }
    })

    it('应该能够按类型过滤消息', async () => {
      await wsStore.connect()
      await new Promise(resolve => setTimeout(resolve, 50))

      const messages = [
        { type: 'order_update', data: { id: '1' } },
        { type: 'trade_update', data: { id: '2' } },
        { type: 'order_update', data: { id: '3' } }
      ]

      if (global.lastCreatedSocket) {
        messages.forEach(msg => {
          global.lastCreatedSocket.simulateMessage(msg)
        })

        const orderMessages = wsStore.getMessagesByType('order_update')
        expect(orderMessages).toHaveLength(2)
        expect(orderMessages[0].data.id).toBe('1')
        expect(orderMessages[1].data.id).toBe('3')
      }
    })
  })

  describe('配置和选项', () => {
    it('应该能够使用自定义配置', () => {
      const customConfig = {
        url: 'wss://custom.example.com/ws',
        reconnectInterval: 2000,
        maxReconnectAttempts: 10,
        heartbeatInterval: 20000
      }

      wsStore.updateConfig(customConfig)

      expect(wsStore.config.url).toBe(customConfig.url)
      expect(wsStore.config.reconnectInterval).toBe(customConfig.reconnectInterval)
      expect(wsStore.config.maxReconnectAttempts).toBe(customConfig.maxReconnectAttempts)
      expect(wsStore.config.heartbeatInterval).toBe(customConfig.heartbeatInterval)
    })

    it('应该能够重置配置到默认值', () => {
      wsStore.updateConfig({ reconnectInterval: 5000 })
      expect(wsStore.config.reconnectInterval).toBe(5000)

      wsStore.resetConfig()
      expect(wsStore.config.reconnectInterval).toBe(3000) // 默认值
    })

    it('应该能够启用和禁用自动重连', () => {
      expect(wsStore.config.autoReconnect).toBe(true)

      wsStore.setAutoReconnect(false)
      expect(wsStore.config.autoReconnect).toBe(false)

      wsStore.setAutoReconnect(true)
      expect(wsStore.config.autoReconnect).toBe(true)
    })
  })

  describe('事件监听器', () => {
    it('应该能够添加和移除事件监听器', () => {
      const listener = vi.fn()

      wsStore.addEventListener('message', listener)
      expect(wsStore.eventListeners.message).toContain(listener)

      wsStore.removeEventListener('message', listener)
      expect(wsStore.eventListeners.message).not.toContain(listener)
    })

    it('应该能够触发事件监听器', async () => {
      const messageListener = vi.fn()
      const connectListener = vi.fn()

      wsStore.addEventListener('message', messageListener)
      wsStore.addEventListener('connect', connectListener)

      await wsStore.connect()
      await new Promise(resolve => setTimeout(resolve, 50))

      expect(connectListener).toHaveBeenCalled()

      if (global.lastCreatedSocket) {
        global.lastCreatedSocket.simulateMessage({ type: 'test' })
        expect(messageListener).toHaveBeenCalled()
      }
    })

    it('应该能够清除所有事件监听器', () => {
      const listener1 = vi.fn()
      const listener2 = vi.fn()

      wsStore.addEventListener('message', listener1)
      wsStore.addEventListener('connect', listener2)

      wsStore.clearEventListeners()

      expect(wsStore.eventListeners.message).toHaveLength(0)
      expect(wsStore.eventListeners.connect).toHaveLength(0)
    })
  })
})
