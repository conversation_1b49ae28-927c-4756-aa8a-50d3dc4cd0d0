/**
 * 基础测试 - 验证测试环境配置
 */

import { describe, it, expect } from 'vitest'

describe('基础测试环境', () => {
  it('应该能够运行基本测试', () => {
    expect(1 + 1).toBe(2)
  })

  it('应该支持异步测试', async () => {
    const result = await Promise.resolve('success')
    expect(result).toBe('success')
  })

  it('应该支持 Mock 功能', () => {
    const mockFn = vi.fn()
    mockFn('test')
    expect(mockFn).toHaveBeenCalledWith('test')
  })
})
