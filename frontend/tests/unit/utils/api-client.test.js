/**
 * API客户端测试
 * 测试 src/api/client.ts 中的HTTP状态码常量
 */

import { describe, it, expect } from 'vitest'
import { HTTP_STATUS } from '@/api/client'

describe('API客户端', () => {
  describe('HTTP状态码常量', () => {
    it('应该导出正确的HTTP状态码', () => {
      expect(HTTP_STATUS.OK).toBe(200)
      expect(HTTP_STATUS.CREATED).toBe(201)
      expect(HTTP_STATUS.BAD_REQUEST).toBe(400)
      expect(HTTP_STATUS.UNAUTHORIZED).toBe(401)
      expect(HTTP_STATUS.NOT_FOUND).toBe(404)
      expect(HTTP_STATUS.INTERNAL_SERVER_ERROR).toBe(500)
    })
  })
})
