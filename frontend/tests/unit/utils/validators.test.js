/**
 * 表单验证测试
 * 测试 src/composables/useForm.js 和 src/utils/validation.ts 中的验证功能
 */

import { describe, it, expect, beforeEach } from 'vitest'
import { createPinia, setActivePinia } from 'pinia'
import { useForm, validationRules } from '@/composables/useForm'
import {
  validateOrder,
  validateOrderArray,
  validateOrderStats,
  safeValidateOrder,
  safeValidateOrderArray,
  OrderSchema,
  OrderStatusSchema,
  OrderSideSchema,
  OrderTypeSchema
} from '@/utils/validation'

describe('表单验证', () => {
  beforeEach(() => {
    // 设置Pinia
    const pinia = createPinia()
    setActivePinia(pinia)
  })

  describe('useForm组合式函数', () => {
    let form

    beforeEach(() => {
      form = useForm({
        email: '',
        password: '',
        amount: 0,
        symbol: ''
      })
    })

    it('应该初始化表单状态', () => {
      expect(form.formData).toEqual({
        email: '',
        password: '',
        amount: 0,
        symbol: ''
      })
      expect(form.errors.value).toEqual({})
      expect(form.isValid.value).toBe(true)
    })

    it('应该更新表单数据', () => {
      form.setFieldValue('email', '<EMAIL>')
      expect(form.formData.email).toBe('<EMAIL>')
    })

    it('应该设置字段错误', () => {
      form.setFieldError('email', '邮箱格式不正确')
      expect(form.errors.value.email).toEqual(['邮箱格式不正确'])
      expect(form.isValid.value).toBe(false)
    })

    it('应该清除字段错误', () => {
      form.setFieldError('email', '邮箱格式不正确')
      expect(form.errors.value.email).toEqual(['邮箱格式不正确'])

      form.clearFieldError('email')
      expect(form.errors.value.email).toBeUndefined()
      expect(form.isValid.value).toBe(true)
    })

    it('应该清除所有错误', () => {
      form.setFieldError('email', '邮箱格式不正确')
      form.setFieldError('password', '密码太短')
      expect(Object.keys(form.errors.value)).toHaveLength(2)

      form.clearErrors()
      expect(form.errors.value).toEqual({})
      expect(form.isValid.value).toBe(true)
    })

    it('应该重置表单', () => {
      form.setFieldValue('email', '<EMAIL>')
      form.setFieldError('email', '测试错误')

      form.resetForm()
      expect(form.formData.email).toBe('')
      expect(form.errors.value).toEqual({})
    })
  })

  describe('验证规则', () => {
    it('required规则应该正确工作', () => {
      const rule = validationRules.required('字段是必填的')

      expect(rule('')).toBe('字段是必填的')
      expect(rule(null)).toBe('字段是必填的')
      expect(rule(undefined)).toBe('字段是必填的')
      expect(rule('valid')).toBe(true)
    })

    it('email规则应该正确工作', () => {
      const rule = validationRules.email('邮箱格式不正确')
      
      expect(rule('invalid')).toBe('邮箱格式不正确')
      expect(rule('test@')).toBe('邮箱格式不正确')
      expect(rule('@example.com')).toBe('邮箱格式不正确')
      expect(rule('<EMAIL>')).toBe(true)
      expect(rule('<EMAIL>')).toBe(true)
    })

    it('minLength规则应该正确工作', () => {
      const rule = validationRules.minLength(5, '至少需要5个字符')
      
      expect(rule('1234')).toBe('至少需要5个字符')
      expect(rule('12345')).toBe(true)
      expect(rule('123456')).toBe(true)
    })

    it('maxLength规则应该正确工作', () => {
      const rule = validationRules.maxLength(5, '不能超过5个字符')
      
      expect(rule('123456')).toBe('不能超过5个字符')
      expect(rule('12345')).toBe(true)
      expect(rule('1234')).toBe(true)
    })

    it('min规则应该正确工作', () => {
      const rule = validationRules.min(10, '值不能小于10')
      
      expect(rule(5)).toBe('值不能小于10')
      expect(rule(10)).toBe(true)
      expect(rule(15)).toBe(true)
    })

    it('max规则应该正确工作', () => {
      const rule = validationRules.max(100, '值不能大于100')
      
      expect(rule(150)).toBe('值不能大于100')
      expect(rule(100)).toBe(true)
      expect(rule(50)).toBe(true)
    })

    it('pattern规则应该正确工作', () => {
      const rule = validationRules.pattern(/^\d{3}-\d{3}-\d{4}$/, '电话号码格式不正确')
      
      expect(rule('123-456-789')).toBe('电话号码格式不正确')
      expect(rule('123-456-7890')).toBe(true)
    })


  })

  describe('运行时类型验证', () => {
    const validOrder = {
      id: '123e4567-e89b-12d3-a456-426614174000',
      symbol: 'BTC/USDT',
      side: 'buy',
      status: 'pending',
      quantity: '1.5',
      price: '50000.00',
      created_at: '2024-01-15T10:30:00Z'
    }

    it('应该验证有效的订单对象', () => {
      expect(() => validateOrder(validOrder)).not.toThrow()
    })

    it('应该拒绝无效的订单对象', () => {
      const invalidOrder = { ...validOrder, side: 'invalid_side' }
      expect(() => validateOrder(invalidOrder)).toThrow()
    })

    it('应该验证订单数组', () => {
      const orders = [validOrder, { ...validOrder, id: '123e4567-e89b-12d3-a456-426614174001' }]
      expect(() => validateOrderArray(orders)).not.toThrow()
    })

    it('应该安全验证订单', () => {
      const result = safeValidateOrder(validOrder)
      expect(result.success).toBe(true)
      expect(result.data).toEqual(validOrder)
    })

    it('应该安全处理验证失败', () => {
      const invalidOrder = { ...validOrder, side: 'invalid_side' }
      const result = safeValidateOrder(invalidOrder)
      expect(result.success).toBe(false)
      expect(result.error).toBeDefined()
    })

    it('应该安全验证订单数组', () => {
      const orders = [validOrder]
      const result = safeValidateOrderArray(orders)
      expect(result.success).toBe(true)
      expect(result.data).toEqual(orders)
    })
  })

  describe('Schema验证', () => {
    it('应该验证订单状态枚举', () => {
      expect(() => OrderStatusSchema.parse('pending')).not.toThrow()
      expect(() => OrderStatusSchema.parse('filled')).not.toThrow()
      expect(() => OrderStatusSchema.parse('cancelled')).not.toThrow()
      expect(() => OrderStatusSchema.parse('invalid')).toThrow()
    })

    it('应该验证订单方向枚举', () => {
      expect(() => OrderSideSchema.parse('buy')).not.toThrow()
      expect(() => OrderSideSchema.parse('sell')).not.toThrow()
      expect(() => OrderSideSchema.parse('invalid')).toThrow()
    })

    it('应该验证订单类型枚举', () => {
      expect(() => OrderTypeSchema.parse('market')).not.toThrow()
      expect(() => OrderTypeSchema.parse('limit')).not.toThrow()
      expect(() => OrderTypeSchema.parse('stop')).not.toThrow()
      expect(() => OrderTypeSchema.parse('invalid')).toThrow()
    })
  })

  describe('边界条件测试', () => {
    it('应该处理空字符串', () => {
      const rule = validationRules.minLength(1, '不能为空')
      expect(rule('')).toBe(true) // 空字符串在minLength中返回true
    })

    it('应该处理null和undefined', () => {
      const rule = validationRules.required('必填')
      expect(rule(null)).toBe('必填')
      expect(rule(undefined)).toBe('必填')
    })

    it('应该处理数字0', () => {
      const rule = validationRules.min(1, '必须大于0')
      expect(rule(0)).toBe('必须大于0')
    })

    it('应该处理布尔值', () => {
      const rule = validationRules.required('必填')
      expect(rule(false)).toBe(true) // false是有效值
      expect(rule(true)).toBe(true)
    })
  })
})
