/**
 * Real-time Features E2E Tests
 * 测试实时功能，包括日志流、WebSocket连接、数据自动刷新等
 */

import { test, expect } from '@playwright/test'
import { API_ENDPOINTS, SELECTORS, TIMEOUTS } from '../fixtures/test-data.js'
import { AuthHelpers, NavigationHelpers, UIHelpers, MockHelpers } from '../fixtures/test-helpers.js'

test.describe('Real-time Features Tests', () => {
  test.beforeEach(async ({ page }) => {
    await AuthHelpers.loginViaUI(page)
  })

  test('should handle real-time log stream', async ({ page }) => {
    await test.step('测试实时日志流功能', async () => {
      await NavigationHelpers.goToDashboard(page)
      
      // 查找日志流容器
      const logStreamSelectors = [
        '.log-stream',
        '.logs-container',
        '[data-testid="log-stream"]',
        '.live-logs',
        '.real-time-logs'
      ]
      
      let logStreamFound = false
      let logContainer = null
      
      for (const selector of logStreamSelectors) {
        const element = page.locator(selector)
        if (await element.count() > 0) {
          logContainer = element.first()
          logStreamFound = true
          console.log('✓ 找到日志流容器:', selector)
          break
        }
      }
      
      if (logStreamFound) {
        // 检查日志内容是否存在
        const logContent = await logContainer.textContent()
        console.log('日志内容长度:', logContent.length)
        
        // 测试日志自动滚动（如果有新日志）
        const initialScrollTop = await logContainer.evaluate(el => el.scrollTop).catch(() => 0)
        
        // 等待一段时间看是否有新日志
        await page.waitForTimeout(3000)
        
        const newLogContent = await logContainer.textContent()
        if (newLogContent.length > logContent.length) {
          console.log('✓ 检测到新日志内容')
          
          // 检查是否自动滚动到底部
          const currentScrollTop = await logContainer.evaluate(el => el.scrollTop).catch(() => 0)
          if (currentScrollTop > initialScrollTop) {
            console.log('✓ 日志自动滚动正常')
          }
        } else {
          console.log('ℹ 在测试期间未检测到新日志')
        }
        
        // 测试日志控制按钮
        await testLogControls(page, logContainer)
      } else {
        console.log('⚠ 未找到日志流容器')
      }
    })
  })
})

async function testLogControls(page, logContainer) {
    // 测试清空日志按钮
    const clearLogButton = page.locator('button:has-text("清空日志"), button:has-text("清除日志"), [data-testid="clear-logs"]')
    if (await clearLogButton.count() > 0) {
      console.log('✓ 找到清空日志按钮')
      
      const beforeClearContent = await logContainer.textContent()
      await clearLogButton.first().click()
      await page.waitForTimeout(1000)
      
      const afterClearContent = await logContainer.textContent()
      if (afterClearContent.length < beforeClearContent.length || afterClearContent.includes('无日志') || afterClearContent.trim() === '') {
        console.log('✓ 清空日志功能正常')
      }
    }
    
    // 测试暂停/恢复日志按钮
    const pauseLogButton = page.locator('button:has-text("暂停日志"), button:has-text("停止日志"), [data-testid="pause-logs"]')
    if (await pauseLogButton.count() > 0) {
      console.log('✓ 找到暂停日志按钮')
      
      await pauseLogButton.first().click()
      await page.waitForTimeout(1000)
      
      // 检查按钮文本是否改变
      const buttonText = await pauseLogButton.first().textContent()
      if (buttonText.includes('恢复') || buttonText.includes('开始')) {
        console.log('✓ 暂停日志功能正常，按钮状态已改变')
        
        // 恢复日志
        await pauseLogButton.first().click()
        await page.waitForTimeout(1000)
      }
    }
    
    // 测试导出日志按钮
    const exportLogButton = page.locator('button:has-text("导出日志"), button:has-text("下载日志"), [data-testid="export-logs"]')
    if (await exportLogButton.count() > 0) {
      console.log('✓ 找到导出日志按钮')
      
      const downloadPromise = page.waitForEvent('download', { timeout: 5000 }).catch(() => null)
      await exportLogButton.first().click()
      
      const download = await downloadPromise
      if (download) {
        console.log('✓ 日志导出下载已开始')
        console.log('导出文件名:', download.suggestedFilename())
      } else {
        console.log('ℹ 日志导出可能使用了不同的方式')
      }
    }
  }

test.describe('WebSocket and Connection Tests', () => {
  test.beforeEach(async ({ page }) => {
    await AuthHelpers.loginViaUI(page)
  })

  test('should handle WebSocket connection status', async ({ page }) => {
    await test.step('测试WebSocket连接状态', async () => {
      await NavigationHelpers.goToDashboard(page)
      
      // 监听WebSocket连接
      let wsConnected = false
      let wsMessages = 0
      
      page.on('websocket', ws => {
        console.log('✓ 检测到WebSocket连接:', ws.url())
        wsConnected = true
        
        ws.on('framereceived', event => {
          wsMessages++
          console.log('收到WebSocket消息:', wsMessages)
        })
        
        ws.on('framesent', event => {
          console.log('发送WebSocket消息')
        })
        
        ws.on('close', () => {
          console.log('WebSocket连接已关闭')
        })
      })
      
      // 等待WebSocket连接建立
      await page.waitForTimeout(5000)
      
      if (wsConnected) {
        console.log('✓ WebSocket连接已建立')
        
        // 检查连接状态指示器
        const connectionIndicators = [
          '.connection-status',
          '.ws-status',
          '.online-indicator',
          '[data-testid="connection-status"]'
        ]
        
        for (const indicator of connectionIndicators) {
          const element = page.locator(indicator)
          if (await element.count() > 0) {
            console.log('✓ 找到连接状态指示器:', indicator)
            
            const statusText = await element.first().textContent()
            if (statusText.includes('在线') || statusText.includes('连接') || statusText.includes('online')) {
              console.log('✓ 连接状态显示正常')
            }
            break
          }
        }
        
        // 等待接收消息
        await page.waitForTimeout(3000)
        
        if (wsMessages > 0) {
          console.log(`✓ 收到 ${wsMessages} 条WebSocket消息`)
        } else {
          console.log('ℹ 在测试期间未收到WebSocket消息')
        }
      } else {
        console.log('⚠ 未检测到WebSocket连接')
      }
    })
  })

  test('should handle data auto-refresh', async ({ page }) => {
    await test.step('测试数据自动刷新功能', async () => {
      await NavigationHelpers.goToDashboard(page)
      
      // 记录初始数据状态
      const dataElements = [
        '.stats-card',
        '.data-value',
        '.price-display',
        '[data-testid="stats"]'
      ]
      
      let initialData = {}
      
      for (const selector of dataElements) {
        const elements = page.locator(selector)
        const count = await elements.count()
        if (count > 0) {
          for (let i = 0; i < Math.min(count, 3); i++) {
            const text = await elements.nth(i).textContent()
            initialData[`${selector}-${i}`] = text
          }
        }
      }
      
      console.log('记录了初始数据状态')
      
      // 等待自动刷新周期
      await page.waitForTimeout(10000)
      
      // 检查数据是否更新
      let dataUpdated = false
      
      for (const key in initialData) {
        const [selector, index] = key.split('-')
        const elements = page.locator(selector)
        if (await elements.count() > parseInt(index)) {
          const newText = await elements.nth(parseInt(index)).textContent()
          if (newText !== initialData[key]) {
            console.log('✓ 检测到数据更新:', selector)
            dataUpdated = true
            break
          }
        }
      }
      
      if (dataUpdated) {
        console.log('✓ 数据自动刷新功能正常')
      } else {
        console.log('ℹ 在测试期间未检测到数据更新，可能刷新周期较长')
      }
      
      // 测试手动刷新按钮
      const refreshButton = page.locator('button:has-text("刷新"), .refresh-btn, [data-testid="refresh"]')
      if (await refreshButton.count() > 0) {
        console.log('✓ 找到手动刷新按钮')
        
        await refreshButton.first().click()
        
        // 检查加载状态
        const loadingIndicators = [
          '.loading',
          '.v-progress-circular',
          '.v-progress-linear',
          '.refreshing'
        ]
        
        let loadingFound = false
        for (const indicator of loadingIndicators) {
          if (await page.locator(indicator).isVisible({ timeout: 2000 }).catch(() => false)) {
            console.log('✓ 找到加载指示器:', indicator)
            loadingFound = true
            break
          }
        }
        
        if (loadingFound) {
          console.log('✓ 手动刷新功能正常')
        }
      }
    })
  })

  test('should handle connection interruption and recovery', async ({ page }) => {
    await test.step('测试连接中断和恢复', async () => {
      await NavigationHelpers.goToDashboard(page)
      
      // 模拟网络中断
      await page.route('**/api/v1/**', route => {
        route.abort('failed')
      })
      
      console.log('模拟网络中断')
      
      // 等待错误检测
      await page.waitForTimeout(3000)
      
      // 检查错误状态指示器
      const errorIndicators = [
        '.connection-error',
        '.offline-indicator',
        '.network-error',
        'text=连接失败',
        'text=网络错误'
      ]
      
      let errorFound = false
      for (const indicator of errorIndicators) {
        if (await page.locator(indicator).isVisible({ timeout: 3000 }).catch(() => false)) {
          console.log('✓ 找到错误状态指示器:', indicator)
          errorFound = true
          break
        }
      }
      
      // 恢复网络连接
      await page.unroute('**/api/v1/**')
      console.log('恢复网络连接')
      
      // 等待连接恢复
      await page.waitForTimeout(5000)
      
      // 检查恢复状态
      const recoveryIndicators = [
        '.connection-restored',
        '.online-indicator',
        'text=连接已恢复',
        'text=在线'
      ]
      
      let recoveryFound = false
      for (const indicator of recoveryIndicators) {
        if (await page.locator(indicator).isVisible({ timeout: 5000 }).catch(() => false)) {
          console.log('✓ 找到恢复状态指示器:', indicator)
          recoveryFound = true
          break
        }
      }
      
      if (errorFound || recoveryFound) {
        console.log('✓ 连接中断和恢复处理正常')
      } else {
        console.log('ℹ 未找到明显的连接状态指示器')
      }
    })
  })
})
