/**
 * Dashboard Navigation E2E Tests
 * 测试仪表盘页面的导航和基础功能
 */

import { test, expect } from '@playwright/test'
import { API_ENDPOINTS, DEMO_CREDENTIALS, SELECTORS, TIMEOUTS } from '../fixtures/test-data.js'
import { AuthHelpers, NavigationHelpers, UIHelpers, AssertionHelpers } from '../fixtures/test-helpers.js'

test.describe('Dashboard Navigation Tests', () => {
  test.beforeEach(async ({ page }) => {
    // 使用更可靠的登录方式
    try {
      await page.goto(API_ENDPOINTS.FRONTEND_URL, {
        waitUntil: 'networkidle',
        timeout: 30000
      })
      await UIHelpers.waitForPageReady(page)

      // 检查是否已经在登录页面
      const isLoginPage = await page.locator('h1:has-text("AI Agent 智能跟单系统")').isVisible({ timeout: 5000 }).catch(() => false)
      if (isLoginPage) {
        // 尝试登录
        await AuthHelpers.loginViaUI(page)
      }
    } catch (error) {
      console.log(`⚠️ 页面导航失败: ${error.message}`)
      // 尝试使用更简单的导航方式
      await UIHelpers.navigateWithRetry(page, API_ENDPOINTS.FRONTEND_URL)
    }
  })

  test('should display dashboard main components', async ({ page }) => {
    // 等待页面加载完成
    await UIHelpers.waitForPageReady(page)
    
    // 检查是否在仪表盘页面（通过URL或页面内容）
    const currentUrl = page.url()
    if (!currentUrl.includes('dashboard')) {
      // 如果不在仪表盘，尝试导航到仪表盘
      await NavigationHelpers.goToDashboard(page)
    }

    // 验证仪表盘核心组件
    await test.step('验证统计卡片', async () => {
      // 查找多种可能的统计卡片选择器
      const statsSelectors = [
        '.stats-card',
        '.v-card',
        '.dashboard-stats',
        '.stat-card',
        '[data-testid="stats-card"]'
      ]

      let foundStats = false
      for (const selector of statsSelectors) {
        const statsElements = page.locator(selector)
        if (await statsElements.count() > 0) {
          await expect(statsElements.first()).toBeVisible()
          console.log('找到统计卡片:', selector)
          foundStats = true
          break
        }
      }

      if (!foundStats) {
        console.log('未找到统计卡片，可能页面结构不同')
        // 验证页面至少加载了基本内容
        const pageContent = page.locator('.v-main, .main-content')
        await expect(pageContent.first()).toBeVisible()
      }
    })

    await test.step('验证导航菜单', async () => {
      // 检查主导航菜单
      const navItems = ['仪表盘', '订单管理', '条件订单', '配置管理']
      
      for (const item of navItems) {
        const navLink = page.locator(`nav a:has-text("${item}"), .v-navigation-drawer a:has-text("${item}")`)
        if (await navLink.count() > 0) {
          await expect(navLink.first()).toBeVisible()
        }
      }
    })

    await test.step('验证页面标题', async () => {
      // 检查页面标题或主要内容
      const pageTitle = page.locator('h1, .page-title, [data-testid="page-title"]')
      if (await pageTitle.count() > 0) {
        await expect(pageTitle.first()).toBeVisible()
      }
    })
  })

  test('should navigate between main sections', async ({ page }) => {
    await UIHelpers.waitForPageReady(page)

    // 测试导航到订单管理
    await test.step('导航到订单管理', async () => {
      await NavigationHelpers.goToOrders(page)
      
      // 验证订单页面加载
      await page.waitForTimeout(TIMEOUTS.SHORT)
      const ordersContent = page.locator('text=订单管理, .orders-view, [data-testid="orders-page"]')
      if (await ordersContent.count() > 0) {
        await expect(ordersContent.first()).toBeVisible()
      }
    })

    // 测试导航到配置管理
    await test.step('导航到配置管理', async () => {
      await NavigationHelpers.goToConfigs(page)
      
      // 验证配置页面加载
      await page.waitForTimeout(TIMEOUTS.SHORT)
      const configsContent = page.locator('text=配置管理, .configs-view, [data-testid="configs-page"]')
      if (await configsContent.count() > 0) {
        await expect(configsContent.first()).toBeVisible()
      }
    })

    // 返回仪表盘
    await test.step('返回仪表盘', async () => {
      await NavigationHelpers.goToDashboard(page)
      
      // 验证回到仪表盘
      await page.waitForTimeout(TIMEOUTS.SHORT)
      const dashboardContent = page.locator('text=仪表盘, .dashboard-view, [data-testid="dashboard-page"]')
      if (await dashboardContent.count() > 0) {
        await expect(dashboardContent.first()).toBeVisible()
      }
    })
  })

  test('should handle responsive navigation', async ({ page }) => {
    // 测试移动端导航
    await page.setViewportSize({ width: 375, height: 667 })
    await UIHelpers.waitForPageReady(page)

    await test.step('验证移动端导航菜单', async () => {
      // 查找移动端菜单按钮
      const menuButton = page.locator('.v-app-bar__nav-icon, [data-testid="mobile-menu"], .mobile-menu-button')
      
      if (await menuButton.count() > 0 && await menuButton.first().isVisible()) {
        await menuButton.first().click()
        
        // 验证导航抽屉打开
        const navDrawer = page.locator('.v-navigation-drawer, .mobile-nav')
        await expect(navDrawer.first()).toBeVisible()
      }
    })

    // 恢复桌面端视口
    await page.setViewportSize({ width: 1280, height: 720 })
  })

  test('should display real-time data updates', async ({ page }) => {
    await UIHelpers.waitForPageReady(page)

    await test.step('验证WebSocket连接状态', async () => {
      // 等待WebSocket连接建立
      await page.waitForTimeout(TIMEOUTS.MEDIUM)
      
      // 检查连接状态指示器
      const connectionStatus = page.locator('.connection-status, [data-testid="ws-status"], .ws-indicator')
      if (await connectionStatus.count() > 0) {
        await expect(connectionStatus.first()).toBeVisible()
      }
    })

    await test.step('验证实时日志流', async () => {
      // 检查实时日志组件
      const logStream = page.locator('.log-stream, [data-testid="live-logs"], .live-log-stream')
      if (await logStream.count() > 0) {
        await expect(logStream.first()).toBeVisible()
      }
    })
  })

  test('should handle error states gracefully', async ({ page }) => {
    await UIHelpers.waitForPageReady(page)

    await test.step('测试网络错误处理', async () => {
      // 模拟网络错误
      await page.route('**/api/v1/**', route => {
        route.abort('failed')
      })

      // 触发数据刷新
      const refreshButton = page.locator('[data-testid="refresh-button"], .refresh-btn, button:has-text("刷新")')
      if (await refreshButton.count() > 0) {
        await refreshButton.first().click()
      }

      // 验证错误提示显示
      await page.waitForTimeout(TIMEOUTS.SHORT)
      const errorMessage = page.locator('.error-message, .v-alert--error, [data-testid="error-alert"]')
      if (await errorMessage.count() > 0) {
        await expect(errorMessage.first()).toBeVisible()
      }
    })
  })

  test('should support keyboard navigation', async ({ page }) => {
    await UIHelpers.waitForPageReady(page)

    await test.step('测试Tab键导航', async () => {
      // 使用Tab键导航
      await page.keyboard.press('Tab')
      await page.keyboard.press('Tab')
      
      // 验证焦点可见性
      const focusedElement = page.locator(':focus')
      if (await focusedElement.count() > 0) {
        await expect(focusedElement).toBeVisible()
      }
    })

    await test.step('测试快捷键', async () => {
      // 测试常用快捷键（如果有实现）
      await page.keyboard.press('Alt+1') // 可能的仪表盘快捷键
      await page.waitForTimeout(TIMEOUTS.SHORT)
      
      await page.keyboard.press('Alt+2') // 可能的订单管理快捷键
      await page.waitForTimeout(TIMEOUTS.SHORT)
    })
  })

  test('should maintain state across page refreshes', async ({ page }) => {
    await UIHelpers.waitForPageReady(page)

    await test.step('设置页面状态', async () => {
      // 导航到特定页面
      await NavigationHelpers.goToOrders(page)
      await page.waitForTimeout(TIMEOUTS.SHORT)
    })

    await test.step('刷新页面并验证状态', async () => {
      // 刷新页面
      await page.reload()
      await UIHelpers.waitForPageReady(page)
      
      // 验证用户仍然登录且在正确页面
      const currentUrl = page.url()
      expect(currentUrl).toContain('orders')
    })
  })

  test('should handle loading states properly', async ({ page }) => {
    await UIHelpers.waitForPageReady(page)

    await test.step('验证加载指示器', async () => {
      // 触发数据加载
      const refreshButton = page.locator('[data-testid="refresh-button"], .refresh-btn, button:has-text("刷新")')
      if (await refreshButton.count() > 0) {
        await refreshButton.first().click()
        
        // 验证加载状态显示
        const loadingIndicator = page.locator('.v-progress-circular, .loading, [data-testid="loading"]')
        if (await loadingIndicator.count() > 0) {
          await expect(loadingIndicator.first()).toBeVisible()
        }
        
        // 等待加载完成
        await UIHelpers.waitForLoadingComplete(page)
      }
    })
  })
})
