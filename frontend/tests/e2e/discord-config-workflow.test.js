/**
 * Discord配置完整工作流程E2E测试
 * 测试从前端到后端的完整Discord配置管理流程
 *
 * 按照项目测试规范：
 * - 测试完整用户旅程
 * - 使用真实前后端交互
 * - 验证UI和数据一致性
 * - 使用中文描述测试用例
 */
import { test, expect } from '@playwright/test'

// 环境检测和配置
const FRONTEND_URL = process.env.FRONTEND_URL || 'http://localhost:3000'
const BACKEND_URL = process.env.API_BASE_URL || 'http://localhost:8000'

// 全局环境检测
test.beforeAll(async ({ request }) => {
  try {
    // 检测前端是否可用
    const frontendResponse = await request.get(FRONTEND_URL)
    if (!frontendResponse.ok()) {
      throw new Error(`Frontend not available at ${FRONTEND_URL}`)
    }

    // 检测后端是否可用
    const backendResponse = await request.get(`${BACKEND_URL}/health`)
    if (!backendResponse.ok()) {
      throw new Error(`Backend not available at ${BACKEND_URL}`)
    }

    console.log(`✅ E2E测试环境检测通过`)
    console.log(`📱 前端地址: ${FRONTEND_URL}`)
    console.log(`🔧 后端地址: ${BACKEND_URL}`)
  } catch (error) {
    console.error(`❌ E2E测试环境不可用: ${error.message}`)
    console.log(`💡 E2E测试需要完整的前后端环境:`)
    console.log(`   - 前端应用运行在: ${FRONTEND_URL}`)
    console.log(`   - 后端API运行在: ${BACKEND_URL}`)
    console.log(`   - 可以通过环境变量 FRONTEND_URL 和 API_BASE_URL 配置地址`)
    test.skip()
  }
})

test.describe('Discord配置管理完整工作流程', () => {
  let testUser = {
    username: `discord_e2e_${Date.now()}`,
    email: `discord_e2e_${Date.now()}@example.com`,
    password: 'test_password_123'
  }

  test.beforeEach(async ({ page }) => {
    // 注册测试用户
    await page.goto(`${FRONTEND_URL}/login`)
    await page.click('text=注册新账户')
    
    await page.fill('[data-testid="register-username"]', testUser.username)
    await page.fill('[data-testid="register-email"]', testUser.email)
    await page.fill('[data-testid="register-password"]', testUser.password)
    await page.fill('[data-testid="register-confirm-password"]', testUser.password)
    
    await page.click('[data-testid="register-submit"]')
    
    // 等待注册成功并自动登录
    await expect(page).toHaveURL('/dashboard')
    
    // 导航到配置页面
    await page.click('text=配置管理')
    await expect(page).toHaveURL('/configs')
    
    // 切换到Discord配置标签
    await page.click('text=信号源配置')
    await page.waitForSelector('[data-testid="discord-config-panel"]')
  })

  test('应该完成Discord配置的完整CRUD工作流程', async ({ page }) => {
    // 1. 验证初始空状态
    await expect(page.locator('text=暂无Discord配置')).toBeVisible()
    await expect(page.locator('text=创建第一个Discord配置来开始监听交易信号')).toBeVisible()

    // 2. 创建新的Discord配置
    await page.click('text=新建配置')
    
    // 等待对话框打开
    await expect(page.locator('[data-testid="discord-config-dialog"]')).toBeVisible()
    await expect(page.locator('text=添加Discord配置')).toBeVisible()

    // 填写配置信息
    const configData = {
      sourceName: 'E2E测试Discord配置',
      token: 'test_discord_token_e2e_123',
      serverIds: '123456789012345678\n987654321098765432',
      channelIds: '111111111111111111\n222222222222222222',
      authorIds: '333333333333333333'
    }

    await page.fill('[data-testid="config-source-name"]', configData.sourceName)
    await page.fill('[data-testid="config-token"]', configData.token)
    await page.fill('[data-testid="config-server-ids"]', configData.serverIds)
    await page.fill('[data-testid="config-channel-ids"]', configData.channelIds)
    await page.fill('[data-testid="config-author-ids"]', configData.authorIds)
    
    // 启用配置
    await page.check('[data-testid="config-enabled"]')
    
    // 保存配置
    await page.click('[data-testid="save-config"]')
    
    // 等待对话框关闭
    await expect(page.locator('[data-testid="discord-config-dialog"]')).not.toBeVisible()

    // 3. 验证配置已创建并显示在列表中
    await expect(page.locator('text=暂无Discord配置')).not.toBeVisible()
    await expect(page.locator(`text=${configData.sourceName}`)).toBeVisible()
    
    // 验证统计信息更新
    await expect(page.locator('[data-testid="total-configs"]')).toContainText('1')
    await expect(page.locator('[data-testid="enabled-configs"]')).toContainText('1')
    await expect(page.locator('[data-testid="disabled-configs"]')).toContainText('0')

    // 4. 编辑配置
    await page.click('[data-testid="edit-config-btn"]')
    
    // 等待编辑对话框打开
    await expect(page.locator('[data-testid="discord-config-dialog"]')).toBeVisible()
    await expect(page.locator('text=编辑Discord配置')).toBeVisible()
    
    // 验证现有数据已填充
    await expect(page.locator('[data-testid="config-source-name"]')).toHaveValue(configData.sourceName)
    
    // 修改配置
    const updatedName = 'E2E测试Discord配置 - 已更新'
    await page.fill('[data-testid="config-source-name"]', updatedName)
    await page.uncheck('[data-testid="config-enabled"]') // 禁用配置
    
    // 保存更新
    await page.click('[data-testid="save-config"]')
    
    // 等待对话框关闭
    await expect(page.locator('[data-testid="discord-config-dialog"]')).not.toBeVisible()

    // 5. 验证配置已更新
    await expect(page.locator(`text=${updatedName}`)).toBeVisible()
    await expect(page.locator(`text=${configData.sourceName}`)).not.toBeVisible()
    
    // 验证统计信息更新
    await expect(page.locator('[data-testid="enabled-configs"]')).toContainText('0')
    await expect(page.locator('[data-testid="disabled-configs"]')).toContainText('1')
    
    // 验证配置卡片显示为禁用状态
    await expect(page.locator('[data-testid="config-status"]')).toContainText('已禁用')

    // 6. 删除配置
    await page.click('[data-testid="delete-config-btn"]')
    
    // 确认删除对话框
    await expect(page.locator('text=确认删除')).toBeVisible()
    await expect(page.locator('text=确定要删除这个Discord配置吗？')).toBeVisible()
    
    await page.click('[data-testid="confirm-delete"]')
    
    // 7. 验证配置已删除
    await expect(page.locator(`text=${updatedName}`)).not.toBeVisible()
    await expect(page.locator('text=暂无Discord配置')).toBeVisible()
    
    // 验证统计信息重置
    await expect(page.locator('[data-testid="total-configs"]')).toContainText('0')
    await expect(page.locator('[data-testid="enabled-configs"]')).toContainText('0')
    await expect(page.locator('[data-testid="disabled-configs"]')).toContainText('0')
  })

  test('应该正确处理表单验证错误', async ({ page }) => {
    // 打开创建对话框
    await page.click('text=新建配置')
    await expect(page.locator('[data-testid="discord-config-dialog"]')).toBeVisible()

    // 尝试保存空表单
    await page.click('[data-testid="save-config"]')
    
    // 验证必需字段错误
    await expect(page.locator('text=配置名称不能为空')).toBeVisible()
    await expect(page.locator('text=Discord Token不能为空')).toBeVisible()

    // 填写过长的配置名称
    const longName = 'a'.repeat(101) // 超过100字符限制
    await page.fill('[data-testid="config-source-name"]', longName)
    
    // 验证长度限制错误
    await expect(page.locator('text=配置名称不能超过100个字符')).toBeVisible()

    // 取消对话框
    await page.click('[data-testid="cancel-config"]')
    await expect(page.locator('[data-testid="discord-config-dialog"]')).not.toBeVisible()
  })

  test('应该正确处理网络错误', async ({ page }) => {
    // 模拟网络错误 - 断开网络连接
    await page.context().setOffline(true)

    // 尝试创建配置
    await page.click('text=新建配置')
    await page.fill('[data-testid="config-source-name"]', '网络错误测试配置')
    await page.fill('[data-testid="config-token"]', 'test_token')
    await page.click('[data-testid="save-config"]')

    // 验证错误提示
    await expect(page.locator('text=网络连接失败')).toBeVisible()
    
    // 恢复网络连接
    await page.context().setOffline(false)
    
    // 关闭错误提示
    await page.click('[data-testid="close-error"]')
    await expect(page.locator('text=网络连接失败')).not.toBeVisible()
  })

  test('应该支持多个配置的管理', async ({ page }) => {
    // 创建多个配置
    const configs = [
      {
        name: '主要交易信号',
        token: 'main_trading_token',
        enabled: true
      },
      {
        name: '备用信号源',
        token: 'backup_signal_token',
        enabled: false
      },
      {
        name: '测试环境配置',
        token: 'test_env_token',
        enabled: true
      }
    ]

    for (const config of configs) {
      // 创建配置
      await page.click('text=新建配置')
      await page.fill('[data-testid="config-source-name"]', config.name)
      await page.fill('[data-testid="config-token"]', config.token)
      
      if (config.enabled) {
        await page.check('[data-testid="config-enabled"]')
      }
      
      await page.click('[data-testid="save-config"]')
      await expect(page.locator('[data-testid="discord-config-dialog"]')).not.toBeVisible()
    }

    // 验证所有配置都显示
    for (const config of configs) {
      await expect(page.locator(`text=${config.name}`)).toBeVisible()
    }

    // 验证统计信息
    await expect(page.locator('[data-testid="total-configs"]')).toContainText('3')
    await expect(page.locator('[data-testid="enabled-configs"]')).toContainText('2')
    await expect(page.locator('[data-testid="disabled-configs"]')).toContainText('1')

    // 测试刷新功能
    await page.click('[data-testid="refresh-configs"]')
    
    // 验证配置仍然存在
    for (const config of configs) {
      await expect(page.locator(`text=${config.name}`)).toBeVisible()
    }
  })

  test('应该正确处理配置状态切换', async ({ page }) => {
    // 创建一个配置
    await page.click('text=新建配置')
    await page.fill('[data-testid="config-source-name"]', '状态切换测试配置')
    await page.fill('[data-testid="config-token"]', 'toggle_test_token')
    await page.check('[data-testid="config-enabled"]')
    await page.click('[data-testid="save-config"]')

    // 验证配置已启用
    await expect(page.locator('[data-testid="config-status"]')).toContainText('已启用')
    await expect(page.locator('[data-testid="enabled-configs"]')).toContainText('1')

    // 切换配置状态（这会打开编辑对话框，因为需要重新输入token）
    await page.click('[data-testid="toggle-config"]')
    
    // 验证编辑对话框打开
    await expect(page.locator('[data-testid="discord-config-dialog"]')).toBeVisible()
    await expect(page.locator('text=编辑Discord配置')).toBeVisible()
    
    // 禁用配置
    await page.uncheck('[data-testid="config-enabled"]')
    await page.click('[data-testid="save-config"]')

    // 验证配置已禁用
    await expect(page.locator('[data-testid="config-status"]')).toContainText('已禁用')
    await expect(page.locator('[data-testid="enabled-configs"]')).toContainText('0')
    await expect(page.locator('[data-testid="disabled-configs"]')).toContainText('1')
  })

  test('应该保持数据在页面刷新后的持久性', async ({ page }) => {
    // 创建配置
    const configName = '持久性测试配置'
    await page.click('text=新建配置')
    await page.fill('[data-testid="config-source-name"]', configName)
    await page.fill('[data-testid="config-token"]', 'persistence_test_token')
    await page.click('[data-testid="save-config"]')

    // 验证配置已创建
    await expect(page.locator(`text=${configName}`)).toBeVisible()

    // 刷新页面
    await page.reload()
    
    // 等待页面加载
    await page.waitForSelector('[data-testid="discord-config-panel"]')

    // 验证配置仍然存在
    await expect(page.locator(`text=${configName}`)).toBeVisible()
    await expect(page.locator('[data-testid="total-configs"]')).toContainText('1')
  })
})
