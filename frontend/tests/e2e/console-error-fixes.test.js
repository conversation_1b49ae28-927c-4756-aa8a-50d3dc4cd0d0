/**
 * Console Error Fixes E2E Tests
 * 针对发现的Console异常问题创建的修复验证测试
 */

import { test, expect } from '@playwright/test'
import { API_ENDPOINTS, SELECTORS, TIMEOUTS } from '../fixtures/test-data.js'
import { AuthHelpers, NavigationHelpers, UIHelpers } from '../fixtures/test-helpers.js'

test.describe('Console Error Fixes Tests', () => {
  test('should handle Material Design Icons loading failure gracefully', async ({ page }) => {
    await test.step('测试Material Design Icons加载失败的优雅处理', async () => {
      let mdiLoadError = false
      let fallbackMechanism = false

      // 监听MDI CSS加载失败
      page.on('response', response => {
        if (response.url().includes('materialdesignicons') && response.status() >= 400) {
          mdiLoadError = true
          console.log('🔴 MDI CSS加载失败:', response.url(), response.status())
        }
      })

      page.on('requestfailed', request => {
        if (request.url().includes('materialdesignicons')) {
          mdiLoadError = true
          console.log('🔴 MDI CSS请求失败:', request.url())
        }
      })

      // 访问应用
      await page.goto(API_ENDPOINTS.FRONTEND_URL)
      await UIHelpers.waitForPageReady(page)

      // 检查是否有备用图标方案
      const iconElements = page.locator('.v-icon, .mdi, [class*="icon"]')
      const iconCount = await iconElements.count()

      if (iconCount > 0) {
        console.log(`✅ 找到 ${iconCount} 个图标元素`)
        
        // 检查图标是否正常显示（即使MDI CSS加载失败）
        const visibleIcons = await iconElements.filter({ hasText: /.+/ }).count()
        const emptyIcons = iconCount - visibleIcons

        if (emptyIcons > 0) {
          console.log(`⚠️ 有 ${emptyIcons} 个图标可能因为MDI CSS加载失败而不显示`)
          
          // 检查是否有备用图标系统
          const fallbackIcons = page.locator('.fallback-icon, .backup-icon, .text-icon')
          const fallbackCount = await fallbackIcons.count()
          
          if (fallbackCount > 0) {
            fallbackMechanism = true
            console.log(`✅ 发现 ${fallbackCount} 个备用图标`)
          }
        } else {
          console.log('✅ 所有图标都正常显示')
        }
      }

      // 建议修复方案
      if (mdiLoadError && !fallbackMechanism) {
        console.log('\n💡 建议的修复方案:')
        console.log('1. 添加本地MDI字体文件作为备用')
        console.log('2. 实现图标加载失败的降级方案')
        console.log('3. 使用SVG图标替代字体图标')
        console.log('4. 添加图标加载状态检测')
      }
    })
  })

  test('should handle WebSocket unknown event types properly', async ({ page }) => {
    await test.step('测试WebSocket未知事件类型的正确处理', async () => {
      let wsErrors = []
      let wsWarnings = []

      // 监听WebSocket相关的Console消息
      page.on('console', msg => {
        const text = msg.text()
        if (text.includes('WebSocket') || text.includes('Unknown event type')) {
          if (msg.type() === 'error') {
            wsErrors.push(text)
            console.log('🔴 WebSocket错误:', text)
          } else if (msg.type() === 'warning') {
            wsWarnings.push(text)
            console.log('🟡 WebSocket警告:', text)
          }
        }
      })

      // 登录并等待WebSocket连接
      await AuthHelpers.loginViaUI(page)
      await NavigationHelpers.goToDashboard(page)
      
      // 等待WebSocket连接和消息
      await page.waitForTimeout(5000)

      // 分析WebSocket错误
      if (wsErrors.length > 0) {
        console.log(`\n⚠️ 发现 ${wsErrors.length} 个WebSocket错误`)
        
        // 检查是否有错误处理机制
        const hasErrorHandler = wsWarnings.some(warning => 
          warning.includes('这通常不影响功能') || 
          warning.includes('已忽略') ||
          warning.includes('fallback')
        )

        if (hasErrorHandler) {
          console.log('✅ 发现WebSocket错误处理机制')
        } else {
          console.log('⚠️ 缺少WebSocket错误处理机制')
          
          console.log('\n💡 建议的修复方案:')
          console.log('1. 在WebSocket处理器中添加未知事件类型的过滤')
          console.log('2. 改进后端WebSocket事件格式验证')
          console.log('3. 添加WebSocket事件类型白名单')
          console.log('4. 实现优雅的错误降级处理')
        }
      }

      // 验证WebSocket功能是否正常工作
      const realTimeElements = page.locator('.live-data, .real-time, [data-testid="live"]')
      const realTimeCount = await realTimeElements.count()
      
      if (realTimeCount > 0) {
        console.log('✅ 实时功能组件正常显示，WebSocket错误未影响核心功能')
      }
    })
  })

  test('should handle API data format inconsistencies', async ({ page }) => {
    await test.step('测试API数据格式不一致的处理', async () => {
      let apiWarnings = []

      // 监听API数据格式警告
      page.on('console', msg => {
        const text = msg.text()
        if (text.includes('API返回的数据格式不正确') || text.includes('期望数组')) {
          apiWarnings.push(text)
          console.log('🟡 API数据格式警告:', text)
        }
      })

      await AuthHelpers.loginViaUI(page)
      
      // 访问可能触发API调用的页面
      await page.goto(API_ENDPOINTS.FRONTEND_URL + '/orders')
      await UIHelpers.waitForPageReady(page)
      await page.waitForTimeout(3000)

      if (apiWarnings.length > 0) {
        console.log(`\n⚠️ 发现 ${apiWarnings.length} 个API数据格式警告`)
        
        // 检查页面是否仍然正常显示数据
        const dataElements = page.locator('.v-data-table, .order-list, .data-container')
        const hasData = await dataElements.count() > 0

        if (hasData) {
          console.log('✅ 尽管有数据格式警告，页面仍正常显示数据')
          
          // 检查是否有空状态处理
          const emptyState = page.locator('.empty-state, .no-data, text=暂无数据')
          const hasEmptyState = await emptyState.count() > 0
          
          if (hasEmptyState) {
            console.log('✅ 发现空状态处理机制')
          }
        } else {
          console.log('❌ 数据格式问题可能影响了页面显示')
        }

        console.log('\n💡 建议的修复方案:')
        console.log('1. 统一后端API响应格式规范')
        console.log('2. 在前端添加数据格式适配器')
        console.log('3. 改进数据验证和错误处理')
        console.log('4. 添加数据格式兼容性处理')
      } else {
        console.log('✅ 未发现API数据格式问题')
      }
    })
  })

  test('should verify error boundary and fallback mechanisms', async ({ page }) => {
    await test.step('验证错误边界和降级机制', async () => {
      let jsErrors = []

      // 监听JavaScript错误
      page.on('pageerror', error => {
        jsErrors.push(error.message)
        console.log('🔴 JavaScript错误:', error.message)
      })

      await AuthHelpers.loginViaUI(page)

      // 模拟一些可能导致错误的操作
      const errorProneActions = [
        {
          name: '快速页面切换',
          action: async () => {
            const pages = ['/dashboard', '/orders', '/configs', '/signals']
            for (const pagePath of pages) {
              await page.goto(API_ENDPOINTS.FRONTEND_URL + pagePath)
              await page.waitForTimeout(500) // 快速切换
            }
          }
        },
        {
          name: '重复点击按钮',
          action: async () => {
            const buttons = page.locator('button:visible')
            const buttonCount = await buttons.count()
            if (buttonCount > 0) {
              const button = buttons.first()
              for (let i = 0; i < 5; i++) {
                await button.click({ timeout: 1000 }).catch(() => {})
                await page.waitForTimeout(100)
              }
            }
          }
        }
      ]

      for (const action of errorProneActions) {
        console.log(`🧪 执行压力测试: ${action.name}`)
        
        const beforeErrors = jsErrors.length
        
        try {
          await action.action()
        } catch (error) {
          console.log(`⚠️ 压力测试中捕获异常: ${error.message}`)
        }
        
        const newErrors = jsErrors.length - beforeErrors
        if (newErrors > 0) {
          console.log(`❌ ${action.name}产生了 ${newErrors} 个JavaScript错误`)
        } else {
          console.log(`✅ ${action.name}没有产生JavaScript错误`)
        }
      }

      // 检查错误边界组件
      const errorBoundaries = page.locator('.error-boundary, .error-fallback, .error-page')
      const boundaryCount = await errorBoundaries.count()

      if (boundaryCount > 0) {
        console.log(`✅ 发现 ${boundaryCount} 个错误边界组件`)
      } else {
        console.log('⚠️ 未发现错误边界组件')
        console.log('\n💡 建议添加错误边界组件来提高应用稳定性')
      }

      // 检查应用整体稳定性
      const isAppResponsive = await page.locator('body').isVisible()
      if (isAppResponsive) {
        console.log('✅ 应用在压力测试后仍保持响应')
      } else {
        console.log('❌ 应用在压力测试后失去响应')
      }
    })
  })

  test('should provide console error fix recommendations', async ({ page }) => {
    await test.step('提供Console错误修复建议', async () => {
      console.log('\n📋 Console错误修复建议总结:')
      
      console.log('\n1. 🎨 图标系统优化:')
      console.log('   - 将MDI字体文件本地化，避免CDN依赖')
      console.log('   - 实现图标加载失败的降级方案')
      console.log('   - 考虑使用SVG图标系统替代字体图标')
      
      console.log('\n2. 🔌 WebSocket错误处理:')
      console.log('   - 在websocketHandler.ts中添加未知事件类型过滤')
      console.log('   - 改进后端WebSocket事件格式验证')
      console.log('   - 添加WebSocket重连机制')
      
      console.log('\n3. 📊 API数据格式统一:')
      console.log('   - 制定统一的API响应格式规范')
      console.log('   - 在前端添加数据适配器层')
      console.log('   - 改进数据验证和类型检查')
      
      console.log('\n4. 🛡️ 错误边界和容错:')
      console.log('   - 添加React/Vue错误边界组件')
      console.log('   - 实现优雅的错误降级处理')
      console.log('   - 添加用户友好的错误提示')
      
      console.log('\n5. 📈 监控和日志:')
      console.log('   - 集成前端错误监控系统')
      console.log('   - 添加性能监控和用户体验追踪')
      console.log('   - 建立错误报告和分析机制')

      // 验证当前应用状态
      await page.goto(API_ENDPOINTS.FRONTEND_URL)
      await UIHelpers.waitForPageReady(page)
      
      const isWorking = await page.locator('text=AI Agent, text=智能跟单').count() > 0
      if (isWorking) {
        console.log('\n✅ 尽管存在Console错误，应用核心功能正常运行')
        console.log('   这些错误主要影响用户体验和开发调试，建议优先修复')
      }
    })
  })
})
