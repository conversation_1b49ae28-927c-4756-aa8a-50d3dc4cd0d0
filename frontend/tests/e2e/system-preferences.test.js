/**
 * 系统设置和偏好E2E测试
 * 测试用户偏好设置、系统配置持久化、本地存储管理等功能
 * 
 * @fileoverview 按照《0. 项目规范.md》编写的系统偏好测试
 * <AUTHOR> Test Suite
 * @version 1.0.0
 */

import { test, expect } from '@playwright/test'
import { UIHelpers, AuthHelpers } from '../fixtures/test-helpers'
import { SELECTORS, TIMEOUTS, DEMO_CREDENTIALS } from '../fixtures/test-data'
import { TestDataFactory } from '../api-unified/test-data-factory'

test.describe('系统设置和偏好测试', () => {
  let testData

  test.beforeEach(async ({ page }) => {
    console.log(`⚙️ 系统偏好测试初始化`)
    
    // 创建测试数据
    testData = await TestDataFactory.createTestScenario()
    
    // 导航到应用
    await UIHelpers.navigateWithRetry(page, 'http://localhost:5173')
    await UIHelpers.waitForPageReady(page)
    
    // 登录
    await AuthHelpers.loginViaUI(page)
    await page.waitForURL('**/dashboard', { timeout: TIMEOUTS.NAVIGATION })
  })

  test.afterEach(async () => {
    if (testData) {
      await TestDataFactory.cleanup(testData.id)
    }
  })

  test.describe('主题和外观设置', () => {
    test('应该支持主题切换和持久化', async ({ page }) => {
      console.log(`🎨 测试主题切换和持久化`)
      
      // 获取初始主题
      const initialTheme = await page.evaluate(() => {
        return localStorage.getItem('theme') || 'dark'
      })
      
      console.log(`📊 初始主题: ${initialTheme}`)
      
      // 查找主题切换控件
      const themeToggleSelectors = [
        '[data-testid="theme-toggle"]',
        'text=浅色主题',
        'text=深色主题',
        'text=Light Theme',
        'text=Dark Theme',
        '.theme-toggle'
      ]
      
      // 先尝试打开用户菜单
      const userMenuButton = page.locator('[data-testid="user-menu"], .user-menu').first()
      if (await userMenuButton.isVisible({ timeout: 3000 }).catch(() => false)) {
        await userMenuButton.click()
        await page.waitForTimeout(1000)
      }
      
      let themeToggled = false
      for (const selector of themeToggleSelectors) {
        if (await page.locator(selector).isVisible({ timeout: 3000 }).catch(() => false)) {
          await page.locator(selector).first().click()
          await page.waitForTimeout(1000)
          
          // 验证主题是否改变
          const newTheme = await page.evaluate(() => {
            return localStorage.getItem('theme') || 'dark'
          })
          
          console.log(`📊 切换后主题: ${newTheme}`)
          
          if (newTheme !== initialTheme) {
            console.log(`✅ 主题切换成功`)
            themeToggled = true
          }
          break
        }
      }
      
      if (themeToggled) {
        // 测试主题持久化
        await page.reload()
        await UIHelpers.waitForPageReady(page)
        
        const persistedTheme = await page.evaluate(() => {
          return localStorage.getItem('theme') || 'dark'
        })
        
        console.log(`📊 刷新后主题: ${persistedTheme}`)
        expect(persistedTheme).toBe(await page.evaluate(() => localStorage.getItem('theme')))
      }
      
      expect(true).toBeTruthy()
    })

    test('应该支持自定义外观设置', async ({ page }) => {
      console.log(`🎛️ 测试自定义外观设置`)
      
      // 查找外观设置选项
      const appearanceSelectors = [
        '[data-testid="appearance-settings"]',
        'text=外观',
        'text=Appearance',
        'text=显示',
        '.appearance-settings'
      ]
      
      let appearanceSettingsFound = false
      for (const selector of appearanceSelectors) {
        if (await page.locator(selector).isVisible({ timeout: 3000 }).catch(() => false)) {
          await page.locator(selector).first().click()
          await page.waitForTimeout(2000)
          
          console.log(`✅ 找到外观设置: ${selector}`)
          appearanceSettingsFound = true
          break
        }
      }
      
      if (appearanceSettingsFound) {
        // 查找具体的外观选项
        const appearanceOptions = [
          'text=字体大小',
          'text=Font Size',
          'text=密度',
          'text=Density',
          'text=紧凑',
          'text=Compact'
        ]
        
        for (const option of appearanceOptions) {
          if (await page.locator(option).isVisible({ timeout: 3000 }).catch(() => false)) {
            console.log(`✅ 找到外观选项: ${option}`)
            break
          }
        }
      } else {
        console.log(`ℹ️ 外观设置可能在系统设置中`)
      }
      
      expect(true).toBeTruthy()
    })
  })

  test.describe('通知和提醒设置', () => {
    test('应该支持通知偏好设置', async ({ page }) => {
      console.log(`🔔 测试通知偏好设置`)
      
      // 查找通知设置
      const notificationSelectors = [
        '[data-testid="notification-settings"]',
        'text=通知设置',
        'text=Notification Settings',
        'text=提醒',
        '.notification-settings'
      ]
      
      let notificationSettingsFound = false
      for (const selector of notificationSelectors) {
        if (await page.locator(selector).isVisible({ timeout: 3000 }).catch(() => false)) {
          await page.locator(selector).first().click()
          await page.waitForTimeout(2000)
          
          console.log(`✅ 找到通知设置: ${selector}`)
          notificationSettingsFound = true
          break
        }
      }
      
      if (notificationSettingsFound) {
        // 查找通知选项
        const notificationOptions = [
          'text=桌面通知',
          'text=Desktop Notifications',
          'text=声音提醒',
          'text=Sound Alerts',
          'text=邮件通知',
          'text=Email Notifications'
        ]
        
        for (const option of notificationOptions) {
          if (await page.locator(option).isVisible({ timeout: 3000 }).catch(() => false)) {
            console.log(`✅ 找到通知选项: ${option}`)
            
            // 尝试切换通知选项
            const toggle = page.locator(`${option} ~ .v-switch, ${option} ~ input[type="checkbox"]`).first()
            if (await toggle.isVisible({ timeout: 2000 }).catch(() => false)) {
              await toggle.click()
              await page.waitForTimeout(1000)
              console.log(`✅ 切换通知选项: ${option}`)
            }
            break
          }
        }
      } else {
        console.log(`ℹ️ 通知设置可能在用户菜单或系统设置中`)
      }
      
      expect(true).toBeTruthy()
    })

    test('应该支持交易提醒设置', async ({ page }) => {
      console.log(`📈 测试交易提醒设置`)
      
      // 查找交易提醒设置
      const tradingAlertSelectors = [
        '[data-testid="trading-alerts"]',
        'text=交易提醒',
        'text=Trading Alerts',
        'text=价格提醒',
        'text=Price Alerts'
      ]
      
      let tradingAlertsFound = false
      for (const selector of tradingAlertSelectors) {
        if (await page.locator(selector).isVisible({ timeout: 3000 }).catch(() => false)) {
          console.log(`✅ 找到交易提醒设置: ${selector}`)
          tradingAlertsFound = true
          break
        }
      }
      
      if (!tradingAlertsFound) {
        console.log(`ℹ️ 交易提醒设置可能在配置管理页面`)
      }
      
      expect(true).toBeTruthy()
    })
  })

  test.describe('数据和隐私设置', () => {
    test('应该支持数据存储偏好', async ({ page }) => {
      console.log(`💾 测试数据存储偏好`)
      
      // 检查本地存储的使用情况
      const storageInfo = await page.evaluate(() => {
        const localStorage = window.localStorage
        const sessionStorage = window.sessionStorage
        
        return {
          localStorageKeys: Object.keys(localStorage),
          sessionStorageKeys: Object.keys(sessionStorage),
          localStorageSize: JSON.stringify(localStorage).length,
          sessionStorageSize: JSON.stringify(sessionStorage).length
        }
      })
      
      console.log(`📊 存储信息:`, storageInfo)
      
      // 查找数据管理选项
      const dataManagementSelectors = [
        '[data-testid="data-management"]',
        'text=数据管理',
        'text=Data Management',
        'text=清除数据',
        'text=Clear Data'
      ]
      
      let dataManagementFound = false
      for (const selector of dataManagementSelectors) {
        if (await page.locator(selector).isVisible({ timeout: 3000 }).catch(() => false)) {
          console.log(`✅ 找到数据管理选项: ${selector}`)
          dataManagementFound = true
          break
        }
      }
      
      if (!dataManagementFound) {
        console.log(`ℹ️ 数据管理选项可能在高级设置中`)
      }
      
      expect(true).toBeTruthy()
    })

    test('应该支持缓存管理', async ({ page }) => {
      console.log(`🗂️ 测试缓存管理`)
      
      // 查找缓存清理选项
      const cacheManagementSelectors = [
        '[data-testid="clear-cache"]',
        'text=清除缓存',
        'text=Clear Cache',
        'text=重置数据',
        'text=Reset Data'
      ]
      
      let cacheManagementFound = false
      for (const selector of cacheManagementSelectors) {
        if (await page.locator(selector).isVisible({ timeout: 3000 }).catch(() => false)) {
          console.log(`✅ 找到缓存管理选项: ${selector}`)
          cacheManagementFound = true
          
          // 不实际执行清除操作，避免影响其他测试
          console.log(`ℹ️ 跳过实际清除操作以保护测试环境`)
          break
        }
      }
      
      if (!cacheManagementFound) {
        console.log(`ℹ️ 缓存管理功能可能在开发者工具或高级设置中`)
      }
      
      expect(true).toBeTruthy()
    })
  })

  test.describe('语言和地区设置', () => {
    test('应该支持语言切换', async ({ page }) => {
      console.log(`🌐 测试语言切换`)
      
      // 查找语言设置
      const languageSelectors = [
        '[data-testid="language-selector"]',
        'text=语言',
        'text=Language',
        'text=中文',
        'text=English',
        '.language-selector'
      ]
      
      let languageSettingFound = false
      for (const selector of languageSelectors) {
        if (await page.locator(selector).isVisible({ timeout: 3000 }).catch(() => false)) {
          await page.locator(selector).first().click()
          await page.waitForTimeout(1000)
          
          console.log(`✅ 找到语言设置: ${selector}`)
          languageSettingFound = true
          
          // 查找语言选项
          const languageOptions = [
            'text=中文',
            'text=English',
            'text=简体中文',
            'text=繁體中文'
          ]
          
          for (const option of languageOptions) {
            if (await page.locator(option).isVisible({ timeout: 2000 }).catch(() => false)) {
              console.log(`✅ 找到语言选项: ${option}`)
              // 不实际切换语言，避免影响其他测试
              break
            }
          }
          break
        }
      }
      
      if (!languageSettingFound) {
        console.log(`ℹ️ 语言设置可能在系统设置或用户菜单中`)
      }
      
      expect(true).toBeTruthy()
    })

    test('应该支持时区设置', async ({ page }) => {
      console.log(`🕐 测试时区设置`)
      
      // 查找时区设置
      const timezoneSelectors = [
        '[data-testid="timezone-selector"]',
        'text=时区',
        'text=Timezone',
        'text=时间',
        'text=Time'
      ]
      
      let timezoneSettingFound = false
      for (const selector of timezoneSelectors) {
        if (await page.locator(selector).isVisible({ timeout: 3000 }).catch(() => false)) {
          console.log(`✅ 找到时区设置: ${selector}`)
          timezoneSettingFound = true
          break
        }
      }
      
      if (!timezoneSettingFound) {
        console.log(`ℹ️ 时区设置可能在高级设置中或自动检测`)
      }
      
      expect(true).toBeTruthy()
    })
  })

  test.describe('性能和显示设置', () => {
    test('应该支持性能优化设置', async ({ page }) => {
      console.log(`⚡ 测试性能优化设置`)
      
      // 查找性能设置
      const performanceSelectors = [
        '[data-testid="performance-settings"]',
        'text=性能',
        'text=Performance',
        'text=优化',
        'text=Optimization'
      ]
      
      let performanceSettingFound = false
      for (const selector of performanceSelectors) {
        if (await page.locator(selector).isVisible({ timeout: 3000 }).catch(() => false)) {
          console.log(`✅ 找到性能设置: ${selector}`)
          performanceSettingFound = true
          break
        }
      }
      
      if (!performanceSettingFound) {
        console.log(`ℹ️ 性能设置可能在高级选项中`)
      }
      
      expect(true).toBeTruthy()
    })

    test('应该支持显示密度设置', async ({ page }) => {
      console.log(`📏 测试显示密度设置`)
      
      // 查找显示密度设置
      const densitySelectors = [
        '[data-testid="display-density"]',
        'text=显示密度',
        'text=Display Density',
        'text=紧凑',
        'text=Compact',
        'text=舒适',
        'text=Comfortable'
      ]
      
      let densitySettingFound = false
      for (const selector of densitySelectors) {
        if (await page.locator(selector).isVisible({ timeout: 3000 }).catch(() => false)) {
          console.log(`✅ 找到显示密度设置: ${selector}`)
          densitySettingFound = true
          break
        }
      }
      
      if (!densitySettingFound) {
        console.log(`ℹ️ 显示密度设置可能在外观设置中`)
      }
      
      expect(true).toBeTruthy()
    })
  })

  test.describe('设置持久化验证', () => {
    test('应该在页面刷新后保持设置', async ({ page }) => {
      console.log(`🔄 测试设置持久化`)
      
      // 记录当前设置
      const currentSettings = await page.evaluate(() => {
        return {
          theme: localStorage.getItem('theme'),
          language: localStorage.getItem('language'),
          notifications: localStorage.getItem('notifications'),
          sidebarOpen: localStorage.getItem('sidebarOpen')
        }
      })
      
      console.log(`📊 当前设置:`, currentSettings)
      
      // 刷新页面
      await page.reload()
      await UIHelpers.waitForPageReady(page)
      
      // 验证设置是否保持
      const persistedSettings = await page.evaluate(() => {
        return {
          theme: localStorage.getItem('theme'),
          language: localStorage.getItem('language'),
          notifications: localStorage.getItem('notifications'),
          sidebarOpen: localStorage.getItem('sidebarOpen')
        }
      })
      
      console.log(`📊 刷新后设置:`, persistedSettings)
      
      // 验证关键设置是否保持
      expect(persistedSettings.theme).toBe(currentSettings.theme)
      
      console.log(`✅ 设置持久化验证完成`)
    })
  })
})
