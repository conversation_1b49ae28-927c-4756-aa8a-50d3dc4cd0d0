/**
 * Console Error Analysis E2E Tests
 * 专门用于捕获、分析和验证前端Console异常的测试套件
 */

import { test, expect } from '@playwright/test'
import { API_ENDPOINTS, SELECTORS, TIMEOUTS } from '../fixtures/test-data.js'
import { AuthHelpers, NavigationHelpers, UIHelpers } from '../fixtures/test-helpers.js'

test.describe('Console Error Analysis Tests', () => {
  let consoleErrors = []
  let consoleWarnings = []
  let networkErrors = []
  let jsErrors = []

  test.beforeEach(async ({ page }) => {
    // 重置错误收集器
    consoleErrors = []
    consoleWarnings = []
    networkErrors = []
    jsErrors = []

    // 监听Console错误
    page.on('console', msg => {
      const type = msg.type()
      const text = msg.text()
      const location = msg.location()
      
      const errorInfo = {
        type,
        text,
        location,
        timestamp: new Date().toISOString()
      }

      if (type === 'error') {
        consoleErrors.push(errorInfo)
        console.log('🔴 Console Error:', text)
        if (location.url) {
          console.log('   📍 Location:', `${location.url}:${location.lineNumber}:${location.columnNumber}`)
        }
      } else if (type === 'warning') {
        consoleWarnings.push(errorInfo)
        console.log('🟡 Console Warning:', text)
      }
    })

    // 监听页面错误
    page.on('pageerror', error => {
      const errorInfo = {
        message: error.message,
        stack: error.stack,
        name: error.name,
        timestamp: new Date().toISOString()
      }
      jsErrors.push(errorInfo)
      console.log('🔴 Page Error:', error.message)
      console.log('   📍 Stack:', error.stack)
    })

    // 监听网络请求失败
    page.on('requestfailed', request => {
      const errorInfo = {
        url: request.url(),
        method: request.method(),
        failure: request.failure(),
        timestamp: new Date().toISOString()
      }
      networkErrors.push(errorInfo)
      console.log('🔴 Network Error:', request.url(), request.failure()?.errorText)
    })

    // 监听响应错误
    page.on('response', response => {
      if (response.status() >= 400) {
        const errorInfo = {
          url: response.url(),
          status: response.status(),
          statusText: response.statusText(),
          timestamp: new Date().toISOString()
        }
        networkErrors.push(errorInfo)
        console.log('🔴 HTTP Error:', response.status(), response.url())
      }
    })
  })

  test.afterEach(async ({ page }) => {
    // 输出错误统计
    console.log('\n📊 Error Summary:')
    console.log(`   Console Errors: ${consoleErrors.length}`)
    console.log(`   Console Warnings: ${consoleWarnings.length}`)
    console.log(`   JavaScript Errors: ${jsErrors.length}`)
    console.log(`   Network Errors: ${networkErrors.length}`)

    // 如果有错误，输出详细信息
    if (consoleErrors.length > 0) {
      console.log('\n🔍 Console Errors Details:')
      consoleErrors.forEach((error, index) => {
        console.log(`   ${index + 1}. ${error.text}`)
        if (error.location.url) {
          console.log(`      Location: ${error.location.url}:${error.location.lineNumber}`)
        }
      })
    }

    if (jsErrors.length > 0) {
      console.log('\n🔍 JavaScript Errors Details:')
      jsErrors.forEach((error, index) => {
        console.log(`   ${index + 1}. ${error.message}`)
        if (error.stack) {
          console.log(`      Stack: ${error.stack.split('\n')[0]}`)
        }
      })
    }
  })

  test('should analyze console errors during login flow', async ({ page }) => {
    await test.step('登录流程Console错误分析', async () => {
      console.log('🔍 开始分析登录流程中的Console错误...')
      
      // 访问登录页面
      await page.goto(API_ENDPOINTS.FRONTEND_URL)
      await UIHelpers.waitForPageReady(page)
      
      // 等待一段时间让所有资源加载
      await page.waitForTimeout(3000)
      
      // 执行登录操作
      await AuthHelpers.loginViaUI(page)
      
      // 等待登录完成
      await page.waitForTimeout(3000)
      
      // 分析收集到的错误
      const criticalErrors = consoleErrors.filter(error => 
        error.text.includes('Failed to') || 
        error.text.includes('Cannot read') ||
        error.text.includes('undefined') ||
        error.text.includes('null')
      )
      
      if (criticalErrors.length > 0) {
        console.log('⚠️ 发现关键错误:', criticalErrors.length)
        criticalErrors.forEach(error => {
          console.log(`   - ${error.text}`)
        })
      }
      
      // 验证登录是否成功（即使有Console错误）
      const isLoggedIn = await page.locator('text=仪表盘, text=订单管理, text=配置管理').count() > 0
      if (isLoggedIn) {
        console.log('✅ 登录成功，但存在Console错误')
      } else {
        console.log('❌ 登录失败，可能与Console错误相关')
      }
    })
  })

  test('should analyze console errors during navigation', async ({ page }) => {
    await test.step('页面导航Console错误分析', async () => {
      console.log('🔍 开始分析页面导航中的Console错误...')
      
      await AuthHelpers.loginViaUI(page)
      
      // 测试各个页面的导航
      const pages = [
        { name: '仪表盘', url: '/dashboard' },
        { name: '订单管理', url: '/orders' },
        { name: '条件订单', url: '/conditional-orders' },
        { name: '信号管理', url: '/signals' },
        { name: '配置管理', url: '/configs' }
      ]
      
      for (const pageInfo of pages) {
        console.log(`📄 导航到${pageInfo.name}页面...`)
        
        const beforeErrorCount = consoleErrors.length
        
        await page.goto(API_ENDPOINTS.FRONTEND_URL + pageInfo.url)
        await UIHelpers.waitForPageReady(page)
        await page.waitForTimeout(2000)
        
        const afterErrorCount = consoleErrors.length
        const newErrors = afterErrorCount - beforeErrorCount
        
        if (newErrors > 0) {
          console.log(`⚠️ ${pageInfo.name}页面产生了 ${newErrors} 个新的Console错误`)
          const pageErrors = consoleErrors.slice(beforeErrorCount)
          pageErrors.forEach(error => {
            console.log(`   - ${error.text}`)
          })
        } else {
          console.log(`✅ ${pageInfo.name}页面没有产生Console错误`)
        }
      }
    })
  })

  test('should analyze console errors during user interactions', async ({ page }) => {
    await test.step('用户交互Console错误分析', async () => {
      console.log('🔍 开始分析用户交互中的Console错误...')
      
      await AuthHelpers.loginViaUI(page)
      await NavigationHelpers.goToDashboard(page)
      
      // 测试各种用户交互
      const interactions = [
        {
          name: '点击刷新按钮',
          action: async () => {
            const refreshBtn = page.locator('button:has-text("刷新"), .refresh-btn')
            if (await refreshBtn.count() > 0) {
              await refreshBtn.first().click()
              await page.waitForTimeout(2000)
            }
          }
        },
        {
          name: '打开订单详情',
          action: async () => {
            await page.goto(API_ENDPOINTS.FRONTEND_URL + '/orders')
            await UIHelpers.waitForPageReady(page)
            const detailsBtn = page.locator('button:has-text("详情"), .details-button')
            if (await detailsBtn.count() > 0) {
              await detailsBtn.first().click()
              await page.waitForTimeout(2000)
            }
          }
        },
        {
          name: '切换配置选项卡',
          action: async () => {
            await page.goto(API_ENDPOINTS.FRONTEND_URL + '/configs')
            await UIHelpers.waitForPageReady(page)
            const tabs = page.locator('.v-tab, [role="tab"]')
            const tabCount = await tabs.count()
            if (tabCount > 1) {
              await tabs.nth(1).click()
              await page.waitForTimeout(2000)
            }
          }
        }
      ]
      
      for (const interaction of interactions) {
        console.log(`🖱️ 执行交互: ${interaction.name}`)
        
        const beforeErrorCount = consoleErrors.length
        
        try {
          await interaction.action()
        } catch (error) {
          console.log(`❌ 交互执行失败: ${error.message}`)
        }
        
        const afterErrorCount = consoleErrors.length
        const newErrors = afterErrorCount - beforeErrorCount
        
        if (newErrors > 0) {
          console.log(`⚠️ ${interaction.name}产生了 ${newErrors} 个Console错误`)
          const interactionErrors = consoleErrors.slice(beforeErrorCount)
          interactionErrors.forEach(error => {
            console.log(`   - ${error.text}`)
          })
        } else {
          console.log(`✅ ${interaction.name}没有产生Console错误`)
        }
      }
    })
  })

  test('should analyze specific error patterns', async ({ page }) => {
    await test.step('特定错误模式分析', async () => {
      console.log('🔍 开始分析特定的错误模式...')
      
      await AuthHelpers.loginViaUI(page)
      
      // 等待收集足够的错误信息
      await page.waitForTimeout(5000)
      
      // 分析常见错误模式
      const errorPatterns = {
        'API调用错误': consoleErrors.filter(e => 
          e.text.includes('fetch') || 
          e.text.includes('XMLHttpRequest') ||
          e.text.includes('api')
        ),
        'Vue组件错误': consoleErrors.filter(e => 
          e.text.includes('Vue') || 
          e.text.includes('component') ||
          e.text.includes('reactive')
        ),
        '资源加载错误': consoleErrors.filter(e => 
          e.text.includes('Failed to load') || 
          e.text.includes('404') ||
          e.text.includes('net::ERR')
        ),
        'JavaScript运行时错误': consoleErrors.filter(e => 
          e.text.includes('Cannot read') || 
          e.text.includes('undefined') ||
          e.text.includes('null')
        ),
        'WebSocket错误': consoleErrors.filter(e => 
          e.text.includes('WebSocket') || 
          e.text.includes('ws://') ||
          e.text.includes('wss://')
        )
      }
      
      console.log('\n📊 错误模式分析:')
      Object.entries(errorPatterns).forEach(([pattern, errors]) => {
        if (errors.length > 0) {
          console.log(`   ${pattern}: ${errors.length} 个错误`)
          errors.forEach(error => {
            console.log(`     - ${error.text}`)
          })
        }
      })
      
      // 检查是否有重复的错误
      const errorCounts = {}
      consoleErrors.forEach(error => {
        const key = error.text
        errorCounts[key] = (errorCounts[key] || 0) + 1
      })
      
      const repeatedErrors = Object.entries(errorCounts).filter(([, count]) => count > 1)
      if (repeatedErrors.length > 0) {
        console.log('\n🔄 重复出现的错误:')
        repeatedErrors.forEach(([error, count]) => {
          console.log(`   ${error} (出现 ${count} 次)`)
        })
      }
    })
  })

  test('should test error recovery mechanisms', async ({ page }) => {
    await test.step('错误恢复机制测试', async () => {
      console.log('🔍 测试应用的错误恢复机制...')
      
      await AuthHelpers.loginViaUI(page)
      
      // 模拟网络中断
      console.log('🌐 模拟网络中断...')
      await page.route('**/api/v1/**', route => {
        route.abort('failed')
      })
      
      // 尝试执行一些操作
      await page.goto(API_ENDPOINTS.FRONTEND_URL + '/orders')
      await page.waitForTimeout(3000)
      
      // 恢复网络
      console.log('🌐 恢复网络连接...')
      await page.unroute('**/api/v1/**')
      
      // 检查应用是否能够恢复
      await page.reload()
      await UIHelpers.waitForPageReady(page)
      
      // 验证错误恢复
      const errorBoundarySelectors = [
        '.error-boundary',
        '.error-fallback',
        'text=出错了',
        'text=Error',
        '.error-message'
      ]

      let hasErrorBoundary = false
      for (const selector of errorBoundarySelectors) {
        if (await page.locator(selector).count() > 0) {
          hasErrorBoundary = true
          break
        }
      }

      const retrySelectors = [
        'button:has-text("重试")',
        'button:has-text("刷新")',
        'button:has-text("Retry")',
        'button:has-text("Refresh")'
      ]

      let hasRetryMechanism = false
      for (const selector of retrySelectors) {
        if (await page.locator(selector).count() > 0) {
          hasRetryMechanism = true
          break
        }
      }
      
      if (hasErrorBoundary) {
        console.log('✅ 发现错误边界组件')
      }
      
      if (hasRetryMechanism) {
        console.log('✅ 发现重试机制')
      }
      
      if (!hasErrorBoundary && !hasRetryMechanism) {
        console.log('⚠️ 未发现明显的错误恢复机制')
      }
    })
  })
})
