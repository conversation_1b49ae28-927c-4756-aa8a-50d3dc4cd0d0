/**
 * Core Functionality E2E Tests
 * 测试应用的核心功能，不依赖特定的登录流程
 */

import { test, expect } from '@playwright/test'
import { API_ENDPOINTS, TIMEOUTS } from '../fixtures/test-data.js'
import { UIHelpers } from '../fixtures/test-helpers.js'

test.describe('Core Functionality Tests', () => {
  test.beforeEach(async ({ page }) => {
    // 设置更长的超时时间
    test.setTimeout(60000)
    
    // 访问应用首页
    await page.goto(API_ENDPOINTS.FRONTEND_URL)
    await UIHelpers.waitForPageReady(page)
  })

  test('should load application and display main interface', async ({ page }) => {
    await test.step('验证应用基础加载', async () => {
      // 检查页面标题
      const title = await page.title()
      expect(title).toBeTruthy()
      console.log('页面标题:', title)
      
      // 检查Vue应用是否加载
      const vueApp = page.locator('#app')
      await expect(vueApp).toBeVisible()
      
      // 检查是否有JavaScript错误
      const errors = []
      page.on('pageerror', error => {
        errors.push(error.message)
      })
      
      await page.waitForTimeout(TIMEOUTS.SHORT)
      
      if (errors.length > 0) {
        console.log('页面JavaScript错误:', errors)
      }
    })

    await test.step('验证基础UI组件', async () => {
      // 检查是否有Vuetify组件
      const vuetifyElements = [
        '.v-application',
        '.v-main',
        '.v-app-bar',
        '.v-navigation-drawer'
      ]
      
      for (const selector of vuetifyElements) {
        const element = page.locator(selector)
        if (await element.count() > 0) {
          console.log('找到Vuetify组件:', selector)
          await expect(element.first()).toBeVisible()
        }
      }
    })
  })

  test('should handle responsive design', async ({ page }) => {
    await test.step('测试桌面端布局', async () => {
      await page.setViewportSize({ width: 1280, height: 720 })
      await page.waitForTimeout(TIMEOUTS.SHORT)
      
      // 验证桌面端布局
      const desktopElements = page.locator('.v-navigation-drawer, .sidebar, .desktop-nav')
      if (await desktopElements.count() > 0) {
        await expect(desktopElements.first()).toBeVisible()
      }
    })

    await test.step('测试平板端布局', async () => {
      await page.setViewportSize({ width: 768, height: 1024 })
      await page.waitForTimeout(TIMEOUTS.SHORT)
      
      // 验证平板端适配
      const content = page.locator('.v-main, .main-content')
      if (await content.count() > 0) {
        await expect(content.first()).toBeVisible()
      }
    })

    await test.step('测试移动端布局', async () => {
      await page.setViewportSize({ width: 375, height: 667 })
      await page.waitForTimeout(TIMEOUTS.SHORT)
      
      // 验证移动端菜单
      const mobileMenu = page.locator('.v-app-bar__nav-icon, .mobile-menu-btn')
      if (await mobileMenu.count() > 0) {
        await expect(mobileMenu.first()).toBeVisible()
      }
    })
  })

  test('should handle API connectivity', async ({ page }) => {
    await test.step('测试API连接状态', async () => {
      // 检查网络请求
      const requests = []
      page.on('request', request => {
        if (request.url().includes('/api/')) {
          requests.push(request.url())
        }
      })
      
      // 触发可能的API调用
      await page.reload()
      await page.waitForTimeout(TIMEOUTS.MEDIUM)
      
      console.log('检测到的API请求:', requests)
    })

    await test.step('测试错误处理', async () => {
      // 模拟网络错误
      await page.route('**/api/v1/**', route => {
        route.abort('failed')
      })
      
      // 刷新页面触发网络请求
      await page.reload()
      await page.waitForTimeout(TIMEOUTS.SHORT)
      
      // 检查是否有错误提示
      const errorElements = [
        '.error-message',
        '.v-alert--error',
        'text=网络错误',
        'text=连接失败'
      ]
      
      for (const selector of errorElements) {
        const element = page.locator(selector)
        if (await element.count() > 0) {
          console.log('发现错误提示:', selector)
          break
        }
      }
    })
  })

  test('should support keyboard navigation', async ({ page }) => {
    await test.step('测试Tab键导航', async () => {
      // 使用Tab键导航
      await page.keyboard.press('Tab')
      await page.keyboard.press('Tab')
      await page.keyboard.press('Tab')
      
      // 检查焦点是否可见
      const focusedElement = page.locator(':focus')
      if (await focusedElement.count() > 0) {
        await expect(focusedElement).toBeVisible()
        console.log('键盘导航正常工作')
      }
    })

    await test.step('测试Escape键功能', async () => {
      // 按Escape键
      await page.keyboard.press('Escape')
      await page.waitForTimeout(TIMEOUTS.SHORT)
      
      // 验证可能的对话框关闭等行为
      console.log('Escape键功能测试完成')
    })
  })

  test('should handle browser back/forward navigation', async ({ page }) => {
    await test.step('测试浏览器导航', async () => {
      const initialUrl = page.url()
      
      // 尝试导航到不同页面（如果有路由）
      const possibleRoutes = ['/orders', '/config', '/dashboard']
      
      for (const route of possibleRoutes) {
        try {
          await page.goto(API_ENDPOINTS.FRONTEND_URL + route)
          await page.waitForTimeout(TIMEOUTS.SHORT)
          
          // 使用浏览器后退
          await page.goBack()
          await page.waitForTimeout(TIMEOUTS.SHORT)
          
          // 使用浏览器前进
          await page.goForward()
          await page.waitForTimeout(TIMEOUTS.SHORT)
          
          console.log('浏览器导航测试完成:', route)
          break
        } catch (error) {
          console.log('路由不存在或导航失败:', route)
        }
      }
    })
  })

  test('should handle page refresh and state persistence', async ({ page }) => {
    await test.step('测试页面刷新', async () => {
      // 记录初始状态
      const initialUrl = page.url()
      
      // 刷新页面
      await page.reload()
      await UIHelpers.waitForPageReady(page)
      
      // 验证页面重新加载
      const currentUrl = page.url()
      expect(currentUrl).toBe(initialUrl)
      
      // 检查应用是否正常重新初始化
      const vueApp = page.locator('#app')
      await expect(vueApp).toBeVisible()
    })

    await test.step('测试本地存储', async () => {
      // 检查localStorage使用
      const localStorageData = await page.evaluate(() => {
        const data = {}
        for (let i = 0; i < localStorage.length; i++) {
          const key = localStorage.key(i)
          data[key] = localStorage.getItem(key)
        }
        return data
      })
      
      console.log('LocalStorage数据:', Object.keys(localStorageData))
    })
  })

  test('should display appropriate loading states', async ({ page }) => {
    await test.step('检查加载指示器', async () => {
      // 刷新页面观察加载状态
      await page.reload()
      
      // 在页面加载过程中查找加载指示器
      const loadingSelectors = [
        '.v-progress-circular',
        '.v-progress-linear',
        '.loading',
        '.spinner',
        '[data-testid="loading"]'
      ]
      
      for (const selector of loadingSelectors) {
        const loadingElement = page.locator(selector)
        if (await loadingElement.count() > 0) {
          console.log('发现加载指示器:', selector)
          break
        }
      }
      
      // 等待页面完全加载
      await UIHelpers.waitForPageReady(page)
    })
  })

  test('should handle different screen orientations', async ({ page }) => {
    await test.step('测试横屏模式', async () => {
      await page.setViewportSize({ width: 667, height: 375 })
      await page.waitForTimeout(TIMEOUTS.SHORT)
      
      // 验证横屏布局
      const content = page.locator('.v-main, .main-content')
      if (await content.count() > 0) {
        await expect(content.first()).toBeVisible()
      }
    })

    await test.step('测试竖屏模式', async () => {
      await page.setViewportSize({ width: 375, height: 667 })
      await page.waitForTimeout(TIMEOUTS.SHORT)
      
      // 验证竖屏布局
      const content = page.locator('.v-main, .main-content')
      if (await content.count() > 0) {
        await expect(content.first()).toBeVisible()
      }
    })
  })

  test('should support accessibility features', async ({ page }) => {
    await test.step('检查基础无障碍性', async () => {
      // 检查页面是否有适当的语义结构
      const semanticElements = [
        'main',
        'nav',
        'header',
        'section',
        'article'
      ]
      
      for (const tag of semanticElements) {
        const elements = page.locator(tag)
        if (await elements.count() > 0) {
          console.log('发现语义化元素:', tag)
        }
      }
      
      // 检查是否有alt属性的图片
      const images = page.locator('img')
      const imageCount = await images.count()
      if (imageCount > 0) {
        console.log('页面包含', imageCount, '个图片元素')
      }
    })
  })
})
