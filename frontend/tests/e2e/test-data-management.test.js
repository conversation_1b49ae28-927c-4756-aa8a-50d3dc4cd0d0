/**
 * Test Data Management E2E Tests
 * 测试数据管理和清理，确保测试环境的一致性和可重复性
 */

import { test, expect } from '@playwright/test'
import { API_ENDPOINTS, SELECTORS, TIMEOUTS } from '../fixtures/test-data.js'
import { AuthHelpers, NavigationHelpers, UIHelpers } from '../fixtures/test-helpers.js'

test.describe('Test Data Management', () => {
  
  test('should verify test data consistency', async ({ page }) => {
    await test.step('验证测试数据一致性', async () => {
      console.log('🔍 开始验证测试数据一致性...')
      
      await AuthHelpers.loginViaUI(page)
      
      // 检查各个页面的数据状态
      const dataChecks = [
        {
          name: '订单数据',
          url: '/orders',
          selector: '.v-data-table tbody tr, .order-row',
          expectedMin: 0,
          expectedMax: 1000
        },
        {
          name: '条件订单数据',
          url: '/conditional-orders',
          selector: '.conditional-order-item, .order-row',
          expectedMin: 0,
          expectedMax: 100
        },
        {
          name: '信号数据',
          url: '/signals',
          selector: '.signal-item, .signal-row',
          expectedMin: 0,
          expectedMax: 500
        },
        {
          name: '配置数据',
          url: '/configs',
          selector: '.config-item, .v-tab',
          expectedMin: 1,
          expectedMax: 10
        }
      ]

      for (const check of dataChecks) {
        await page.goto(API_ENDPOINTS.FRONTEND_URL + check.url)
        await UIHelpers.waitForPageReady(page)
        await page.waitForTimeout(2000)

        const count = await page.locator(check.selector).count()
        
        console.log(`📊 ${check.name}: ${count} 条记录`)
        
        if (count >= check.expectedMin && count <= check.expectedMax) {
          console.log(`✅ ${check.name}数量正常 (${check.expectedMin}-${check.expectedMax})`)
        } else {
          console.log(`⚠️ ${check.name}数量异常: ${count} (期望: ${check.expectedMin}-${check.expectedMax})`)
        }
      }
    })
  })

  test('should handle test data isolation', async ({ page }) => {
    await test.step('测试数据隔离验证', async () => {
      console.log('🔒 验证测试数据隔离...')
      
      await AuthHelpers.loginViaUI(page)
      
      // 创建测试数据
      const testDataMarker = `test_${Date.now()}`
      console.log(`🏷️ 使用测试标记: ${testDataMarker}`)
      
      // 尝试在信号管理页面创建测试数据
      await page.goto(API_ENDPOINTS.FRONTEND_URL + '/signals')
      await UIHelpers.waitForPageReady(page)
      
      const addButton = page.locator('button:has-text("手动添加信号"), button:has-text("添加信号")')
      if (await addButton.count() > 0) {
        await addButton.first().click()
        
        const dialog = page.locator('.v-dialog, .create-signal-dialog')
        if (await dialog.first().isVisible({ timeout: 3000 }).catch(() => false)) {
          // 填写测试数据
          const noteInput = dialog.first().locator('textarea, input[name="note"]')
          if (await noteInput.count() > 0) {
            await noteInput.first().fill(`测试数据 - ${testDataMarker}`)
          }
          
          // 取消创建（避免实际创建测试数据）
          const cancelButton = dialog.first().locator('button:has-text("取消"), button:has-text("关闭")')
          if (await cancelButton.count() > 0) {
            await cancelButton.first().click()
            console.log('✅ 测试数据创建流程验证完成（已取消实际创建）')
          }
        }
      }
      
      // 验证测试数据不会影响其他测试
      await page.reload()
      await UIHelpers.waitForPageReady(page)
      
      const existingData = page.locator(`text=${testDataMarker}`)
      const hasTestData = await existingData.count() > 0
      
      if (!hasTestData) {
        console.log('✅ 测试数据隔离正常，未发现残留数据')
      } else {
        console.log('⚠️ 发现测试数据残留，可能存在数据隔离问题')
      }
    })
  })

  test('should verify test environment reset capability', async ({ page }) => {
    await test.step('验证测试环境重置能力', async () => {
      console.log('🔄 验证测试环境重置能力...')
      
      // 检查是否有环境重置功能
      const resetMethods = [
        {
          name: 'API重置端点',
          check: async () => {
            const response = await page.request.get(`${API_ENDPOINTS.BACKEND_URL}/api/v1/test/reset`, {
              ignoreHTTPSErrors: true
            }).catch(() => null)
            return response && response.status() < 500
          }
        },
        {
          name: '数据库清理脚本',
          check: async () => {
            // 检查是否有清理脚本的迹象
            const response = await page.request.get(`${API_ENDPOINTS.BACKEND_URL}/api/v1/health`, {
              ignoreHTTPSErrors: true
            }).catch(() => null)
            return response && response.status() === 200
          }
        }
      ]

      for (const method of resetMethods) {
        const available = await method.check()
        if (available) {
          console.log(`✅ ${method.name}: 可用`)
        } else {
          console.log(`⚠️ ${method.name}: 不可用`)
        }
      }

      // 建议实现测试环境重置机制
      console.log('\n💡 测试环境重置建议:')
      console.log('   1. 实现 /api/v1/test/reset 端点用于重置测试数据')
      console.log('   2. 创建数据库种子文件用于恢复初始状态')
      console.log('   3. 添加测试数据标记机制便于清理')
      console.log('   4. 实现测试用户和生产用户的数据隔离')
    })
  })

  test('should validate test data quality', async ({ page }) => {
    await test.step('验证测试数据质量', async () => {
      console.log('🎯 验证测试数据质量...')
      
      await AuthHelpers.loginViaUI(page)
      
      // 检查数据完整性
      const dataQualityChecks = [
        {
          name: '订单数据完整性',
          url: '/orders',
          checks: [
            {
              name: '订单ID格式',
              selector: '[data-testid*="order-"], .order-id',
              validate: (text) => /^(order_|ORDER_|\d+)/.test(text)
            },
            {
              name: '价格格式',
              selector: '.price, .amount',
              validate: (text) => /^\d+(\.\d+)?$/.test(text.replace(/[,$\s]/g, ''))
            }
          ]
        },
        {
          name: '用户界面数据',
          url: '/dashboard',
          checks: [
            {
              name: '统计数字格式',
              selector: '.stats-value, .metric-value',
              validate: (text) => /^\d+(\.\d+)?[%]?$/.test(text.replace(/[,$\s]/g, ''))
            }
          ]
        }
      ]

      for (const dataCheck of dataQualityChecks) {
        await page.goto(API_ENDPOINTS.FRONTEND_URL + dataCheck.url)
        await UIHelpers.waitForPageReady(page)
        
        console.log(`🔍 检查 ${dataCheck.name}...`)
        
        for (const check of dataCheck.checks) {
          const elements = page.locator(check.selector)
          const count = await elements.count()
          
          if (count > 0) {
            let validCount = 0
            
            for (let i = 0; i < Math.min(count, 5); i++) {
              const text = await elements.nth(i).textContent()
              if (text && check.validate(text.trim())) {
                validCount++
              }
            }
            
            const validPercentage = Math.round((validCount / Math.min(count, 5)) * 100)
            console.log(`   ${check.name}: ${validPercentage}% 有效 (${validCount}/${Math.min(count, 5)})`)
            
            if (validPercentage >= 80) {
              console.log(`   ✅ ${check.name}质量良好`)
            } else {
              console.log(`   ⚠️ ${check.name}质量需要改进`)
            }
          } else {
            console.log(`   ℹ️ ${check.name}: 无数据可检查`)
          }
        }
      }
    })
  })

  test('should test data synchronization', async ({ page }) => {
    await test.step('测试数据同步机制', async () => {
      console.log('🔄 测试数据同步机制...')
      
      await AuthHelpers.loginViaUI(page)
      
      // 测试实时数据同步
      await page.goto(API_ENDPOINTS.FRONTEND_URL + '/dashboard')
      await UIHelpers.waitForPageReady(page)
      
      // 记录初始数据状态
      const initialData = {}
      const dataElements = [
        '.stats-value',
        '.price-display',
        '.balance-amount'
      ]
      
      for (const selector of dataElements) {
        const elements = page.locator(selector)
        const count = await elements.count()
        if (count > 0) {
          const text = await elements.first().textContent()
          initialData[selector] = text
        }
      }
      
      console.log('📊 记录初始数据状态')
      
      // 等待数据更新
      await page.waitForTimeout(5000)
      
      // 检查数据是否有更新
      let dataUpdated = false
      for (const selector of dataElements) {
        const elements = page.locator(selector)
        const count = await elements.count()
        if (count > 0) {
          const newText = await elements.first().textContent()
          if (newText !== initialData[selector]) {
            console.log(`✅ 检测到数据更新: ${selector}`)
            dataUpdated = true
          }
        }
      }
      
      if (dataUpdated) {
        console.log('✅ 数据同步机制正常工作')
      } else {
        console.log('ℹ️ 在测试期间未检测到数据更新（可能是正常的）')
      }
      
      // 测试手动刷新
      const refreshButton = page.locator('button:has-text("刷新"), .refresh-btn')
      if (await refreshButton.count() > 0) {
        await refreshButton.first().click()
        await page.waitForTimeout(2000)
        console.log('✅ 手动刷新功能正常')
      }
    })
  })

  test('should provide data management recommendations', async ({ page }) => {
    await test.step('提供数据管理建议', async () => {
      console.log('\n📋 测试数据管理建议总结:')
      
      console.log('\n1. 🗄️ 数据隔离和清理:')
      console.log('   - 实现测试数据和生产数据的完全隔离')
      console.log('   - 添加测试数据标记和自动清理机制')
      console.log('   - 创建测试环境重置API端点')
      console.log('   - 实现数据库种子文件管理')
      
      console.log('\n2. 📊 数据质量保证:')
      console.log('   - 建立数据格式验证规则')
      console.log('   - 实现数据完整性检查')
      console.log('   - 添加数据一致性监控')
      console.log('   - 创建数据质量报告')
      
      console.log('\n3. 🔄 数据同步优化:')
      console.log('   - 优化实时数据更新机制')
      console.log('   - 实现增量数据同步')
      console.log('   - 添加数据冲突解决策略')
      console.log('   - 建立数据版本控制')
      
      console.log('\n4. 🧪 测试数据策略:')
      console.log('   - 创建多样化的测试数据集')
      console.log('   - 实现边界值和异常数据测试')
      console.log('   - 建立数据驱动的测试框架')
      console.log('   - 添加性能测试数据生成器')
      
      console.log('\n5. 🔒 数据安全和隐私:')
      console.log('   - 确保测试数据不包含敏感信息')
      console.log('   - 实现数据脱敏机制')
      console.log('   - 建立数据访问控制')
      console.log('   - 添加数据审计日志')
    })
  })
})
