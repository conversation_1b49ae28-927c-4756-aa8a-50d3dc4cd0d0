/**
 * Performance and Load E2E Tests
 * 专门测试应用性能、加载速度和大数据量处理能力
 */

import { test, expect } from '@playwright/test'
import { API_ENDPOINTS, SELECTORS, TIMEOUTS } from '../fixtures/test-data.js'
import { AuthHelpers, NavigationHelpers, UIHelpers } from '../fixtures/test-helpers.js'

test.describe('Performance and Load Tests', () => {
  
  test('should measure page load performance', async ({ page }) => {
    await test.step('测试页面加载性能', async () => {
      const performanceMetrics = {}
      
      // 监听性能指标
      await page.addInitScript(() => {
        window.performanceMetrics = {
          navigationStart: performance.timeOrigin,
          loadEventEnd: 0,
          domContentLoaded: 0,
          firstPaint: 0,
          firstContentfulPaint: 0
        }
        
        // 监听DOMContentLoaded
        document.addEventListener('DOMContentLoaded', () => {
          window.performanceMetrics.domContentLoaded = performance.now()
        })
        
        // 监听load事件
        window.addEventListener('load', () => {
          window.performanceMetrics.loadEventEnd = performance.now()
        })
        
        // 监听First Paint和First Contentful Paint
        if ('PerformanceObserver' in window) {
          const observer = new PerformanceObserver((list) => {
            for (const entry of list.getEntries()) {
              if (entry.name === 'first-paint') {
                window.performanceMetrics.firstPaint = entry.startTime
              }
              if (entry.name === 'first-contentful-paint') {
                window.performanceMetrics.firstContentfulPaint = entry.startTime
              }
            }
          })
          observer.observe({ entryTypes: ['paint'] })
        }
      })

      const startTime = Date.now()
      await page.goto(API_ENDPOINTS.FRONTEND_URL)
      await UIHelpers.waitForPageReady(page)
      const endTime = Date.now()

      // 获取性能指标
      const metrics = await page.evaluate(() => window.performanceMetrics)
      const totalLoadTime = endTime - startTime

      console.log('\n📊 页面加载性能指标:')
      console.log(`   总加载时间: ${totalLoadTime}ms`)
      console.log(`   DOM内容加载: ${Math.round(metrics.domContentLoaded)}ms`)
      console.log(`   页面完全加载: ${Math.round(metrics.loadEventEnd)}ms`)
      
      if (metrics.firstPaint > 0) {
        console.log(`   首次绘制: ${Math.round(metrics.firstPaint)}ms`)
      }
      if (metrics.firstContentfulPaint > 0) {
        console.log(`   首次内容绘制: ${Math.round(metrics.firstContentfulPaint)}ms`)
      }

      // 性能基准检查
      const benchmarks = {
        totalLoad: 5000,    // 总加载时间 < 5秒
        domReady: 2000,     // DOM准备 < 2秒
        firstPaint: 1500,   // 首次绘制 < 1.5秒
        fcp: 2000          // 首次内容绘制 < 2秒
      }

      const results = {
        totalLoad: totalLoadTime <= benchmarks.totalLoad,
        domReady: metrics.domContentLoaded <= benchmarks.domReady,
        firstPaint: metrics.firstPaint <= benchmarks.firstPaint || metrics.firstPaint === 0,
        fcp: metrics.firstContentfulPaint <= benchmarks.fcp || metrics.firstContentfulPaint === 0
      }

      console.log('\n✅ 性能基准检查:')
      Object.entries(results).forEach(([metric, passed]) => {
        const status = passed ? '✅' : '❌'
        console.log(`   ${status} ${metric}: ${passed ? '通过' : '未通过'}`)
      })

      // 获取资源加载信息
      const resourceTiming = await page.evaluate(() => {
        return performance.getEntriesByType('resource').map(entry => ({
          name: entry.name,
          duration: Math.round(entry.duration),
          size: entry.transferSize || 0,
          type: entry.initiatorType
        })).sort((a, b) => b.duration - a.duration).slice(0, 10)
      })

      console.log('\n📦 最慢的10个资源:')
      resourceTiming.forEach((resource, index) => {
        const sizeKB = Math.round(resource.size / 1024)
        console.log(`   ${index + 1}. ${resource.name.split('/').pop()} (${resource.duration}ms, ${sizeKB}KB)`)
      })
    })
  })

  test('should handle large data sets efficiently', async ({ page }) => {
    await test.step('测试大数据量处理性能', async () => {
      await AuthHelpers.loginViaUI(page)
      
      // 模拟大量数据的API响应
      await page.route('**/api/v1/orders*', route => {
        const largeDataSet = {
          success: true,
          data: {
            orders: Array.from({ length: 1000 }, (_, i) => ({
              id: `order_${i}`,
              symbol: i % 2 === 0 ? 'BTC/USDT' : 'ETH/USDT',
              side: i % 2 === 0 ? 'buy' : 'sell',
              amount: (Math.random() * 10).toFixed(4),
              price: (Math.random() * 50000 + 30000).toFixed(2),
              status: ['pending', 'filled', 'cancelled'][i % 3],
              created_at: new Date(Date.now() - Math.random() * 86400000).toISOString()
            })),
            total: 1000,
            page: 1,
            per_page: 100
          }
        }
        route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify(largeDataSet)
        })
      })

      const startTime = Date.now()
      await page.goto(API_ENDPOINTS.FRONTEND_URL + '/orders')
      await UIHelpers.waitForPageReady(page)
      
      // 等待数据加载完成
      await page.waitForSelector('.v-data-table tbody tr, .order-row', { timeout: 10000 })
      const loadTime = Date.now() - startTime

      console.log(`📊 大数据量加载时间: ${loadTime}ms`)

      // 检查渲染的行数
      const renderedRows = await page.locator('.v-data-table tbody tr, .order-row').count()
      console.log(`📋 渲染的数据行数: ${renderedRows}`)

      // 测试滚动性能
      const scrollStartTime = Date.now()
      await page.evaluate(() => {
        const container = document.querySelector('.v-data-table__wrapper, .table-container') || window
        if (container.scrollTo) {
          container.scrollTo(0, container.scrollHeight || document.body.scrollHeight)
        } else {
          window.scrollTo(0, document.body.scrollHeight)
        }
      })
      await page.waitForTimeout(500)
      const scrollTime = Date.now() - scrollStartTime

      console.log(`🖱️ 滚动到底部耗时: ${scrollTime}ms`)

      // 检查是否实现了虚拟滚动或分页
      const hasVirtualScroll = await page.locator('.virtual-scroll, .v-virtual-scroll').count() > 0
      const hasPagination = await page.locator('.v-pagination, .pagination').count() > 0

      if (hasVirtualScroll) {
        console.log('✅ 检测到虚拟滚动实现')
      } else if (hasPagination) {
        console.log('✅ 检测到分页实现')
      } else {
        console.log('⚠️ 建议实现虚拟滚动或分页来优化大数据量性能')
      }

      // 性能基准
      if (loadTime <= 3000) {
        console.log('✅ 大数据量加载性能良好')
      } else {
        console.log('⚠️ 大数据量加载性能需要优化')
      }
    })
  })

  test('should detect memory leaks', async ({ page }) => {
    await test.step('检测内存泄漏', async () => {
      await AuthHelpers.loginViaUI(page)

      // 获取初始内存使用情况
      const getMemoryUsage = async () => {
        return await page.evaluate(() => {
          if (performance.memory) {
            return {
              usedJSHeapSize: performance.memory.usedJSHeapSize,
              totalJSHeapSize: performance.memory.totalJSHeapSize,
              jsHeapSizeLimit: performance.memory.jsHeapSizeLimit
            }
          }
          return null
        })
      }

      const initialMemory = await getMemoryUsage()
      if (!initialMemory) {
        console.log('⚠️ 浏览器不支持内存监控API')
        return
      }

      console.log(`📊 初始内存使用: ${Math.round(initialMemory.usedJSHeapSize / 1024 / 1024)}MB`)

      // 执行一系列操作来测试内存泄漏
      const operations = [
        () => page.goto(API_ENDPOINTS.FRONTEND_URL + '/dashboard'),
        () => page.goto(API_ENDPOINTS.FRONTEND_URL + '/orders'),
        () => page.goto(API_ENDPOINTS.FRONTEND_URL + '/configs'),
        () => page.goto(API_ENDPOINTS.FRONTEND_URL + '/signals'),
      ]

      const memorySnapshots = [initialMemory]

      for (let cycle = 0; cycle < 3; cycle++) {
        console.log(`🔄 执行内存测试周期 ${cycle + 1}/3`)
        
        for (const operation of operations) {
          await operation()
          await UIHelpers.waitForPageReady(page)
          await page.waitForTimeout(1000)
        }

        // 强制垃圾回收（如果支持）
        await page.evaluate(() => {
          if (window.gc) {
            window.gc()
          }
        })

        const currentMemory = await getMemoryUsage()
        memorySnapshots.push(currentMemory)
        console.log(`   内存使用: ${Math.round(currentMemory.usedJSHeapSize / 1024 / 1024)}MB`)
      }

      // 分析内存趋势
      const memoryGrowth = memorySnapshots.map((snapshot, index) => {
        if (index === 0) return 0
        return snapshot.usedJSHeapSize - memorySnapshots[0].usedJSHeapSize
      })

      const finalGrowth = memoryGrowth[memoryGrowth.length - 1]
      const growthMB = Math.round(finalGrowth / 1024 / 1024)

      console.log(`📈 总内存增长: ${growthMB}MB`)

      // 内存泄漏检测
      const memoryLeakThreshold = 50 * 1024 * 1024 // 50MB
      if (finalGrowth > memoryLeakThreshold) {
        console.log('⚠️ 可能存在内存泄漏')
        console.log('💡 建议检查:')
        console.log('   - 事件监听器是否正确移除')
        console.log('   - 定时器是否正确清理')
        console.log('   - DOM引用是否正确释放')
        console.log('   - WebSocket连接是否正确关闭')
      } else {
        console.log('✅ 内存使用正常，未检测到明显泄漏')
      }
    })
  })

  test('should measure network performance', async ({ page }) => {
    await test.step('测试网络性能', async () => {
      const networkMetrics = {
        requests: [],
        totalSize: 0,
        totalTime: 0,
        failedRequests: 0
      }

      // 监听网络请求
      page.on('request', request => {
        networkMetrics.requests.push({
          url: request.url(),
          method: request.method(),
          startTime: Date.now()
        })
      })

      page.on('response', response => {
        const request = networkMetrics.requests.find(req => req.url === response.url())
        if (request) {
          const endTime = Date.now()
          const duration = endTime - request.startTime
          
          request.status = response.status()
          request.duration = duration
          request.size = response.headers()['content-length'] || 0

          networkMetrics.totalTime += duration
          networkMetrics.totalSize += parseInt(request.size) || 0

          if (response.status() >= 400) {
            networkMetrics.failedRequests++
          }
        }
      })

      await AuthHelpers.loginViaUI(page)
      await page.goto(API_ENDPOINTS.FRONTEND_URL + '/dashboard')
      await UIHelpers.waitForPageReady(page)
      
      // 等待所有网络请求完成
      await page.waitForTimeout(3000)

      console.log('\n🌐 网络性能统计:')
      console.log(`   总请求数: ${networkMetrics.requests.length}`)
      console.log(`   失败请求: ${networkMetrics.failedRequests}`)
      console.log(`   总传输大小: ${Math.round(networkMetrics.totalSize / 1024)}KB`)
      console.log(`   平均请求时间: ${Math.round(networkMetrics.totalTime / networkMetrics.requests.length)}ms`)

      // 分析慢请求
      const slowRequests = networkMetrics.requests
        .filter(req => req.duration > 1000)
        .sort((a, b) => b.duration - a.duration)

      if (slowRequests.length > 0) {
        console.log('\n🐌 慢请求 (>1秒):')
        slowRequests.slice(0, 5).forEach((req, index) => {
          const url = req.url.split('/').pop() || req.url
          console.log(`   ${index + 1}. ${url} (${req.duration}ms)`)
        })
      }

      // 分析大文件
      const largeRequests = networkMetrics.requests
        .filter(req => parseInt(req.size) > 100 * 1024) // >100KB
        .sort((a, b) => parseInt(b.size) - parseInt(a.size))

      if (largeRequests.length > 0) {
        console.log('\n📦 大文件请求 (>100KB):')
        largeRequests.slice(0, 5).forEach((req, index) => {
          const url = req.url.split('/').pop() || req.url
          const sizeKB = Math.round(parseInt(req.size) / 1024)
          console.log(`   ${index + 1}. ${url} (${sizeKB}KB)`)
        })
      }

      // 网络性能建议
      const avgRequestTime = networkMetrics.totalTime / networkMetrics.requests.length
      if (avgRequestTime > 500) {
        console.log('\n💡 网络性能优化建议:')
        console.log('   - 启用HTTP/2或HTTP/3')
        console.log('   - 实现请求合并和批处理')
        console.log('   - 添加CDN加速')
        console.log('   - 优化API响应大小')
      }
    })
  })

  test('should test concurrent user simulation', async ({ page, context }) => {
    await test.step('模拟并发用户测试', async () => {
      console.log('🔄 开始并发用户模拟测试...')

      // 创建多个页面模拟并发用户
      const concurrentPages = []
      const userCount = 3

      try {
        for (let i = 0; i < userCount; i++) {
          const newPage = await context.newPage()
          concurrentPages.push(newPage)
          console.log(`👤 创建用户 ${i + 1}`)
        }

        // 并发执行用户操作
        const userOperations = concurrentPages.map(async (userPage, index) => {
          const startTime = Date.now()
          
          try {
            await userPage.goto(API_ENDPOINTS.FRONTEND_URL)
            await UIHelpers.waitForPageReady(userPage)
            
            // 模拟不同的用户行为
            const operations = [
              () => userPage.goto(API_ENDPOINTS.FRONTEND_URL + '/dashboard'),
              () => userPage.goto(API_ENDPOINTS.FRONTEND_URL + '/orders'),
              () => userPage.goto(API_ENDPOINTS.FRONTEND_URL + '/configs')
            ]

            for (const operation of operations) {
              await operation()
              await userPage.waitForTimeout(1000 + Math.random() * 2000) // 随机等待
            }

            const endTime = Date.now()
            console.log(`✅ 用户 ${index + 1} 完成操作 (${endTime - startTime}ms)`)
            
            return { success: true, duration: endTime - startTime }
          } catch (error) {
            console.log(`❌ 用户 ${index + 1} 操作失败: ${error.message}`)
            return { success: false, error: error.message }
          }
        })

        const results = await Promise.all(userOperations)
        
        const successCount = results.filter(r => r.success).length
        const avgDuration = results
          .filter(r => r.success)
          .reduce((sum, r) => sum + r.duration, 0) / successCount

        console.log(`\n📊 并发测试结果:`)
        console.log(`   成功用户: ${successCount}/${userCount}`)
        console.log(`   平均完成时间: ${Math.round(avgDuration)}ms`)

        if (successCount === userCount) {
          console.log('✅ 并发测试通过，系统处理多用户访问正常')
        } else {
          console.log('⚠️ 部分用户操作失败，可能存在并发处理问题')
        }

      } finally {
        // 清理并发页面
        for (const userPage of concurrentPages) {
          await userPage.close()
        }
      }
    })
  })

  test('should provide performance optimization recommendations', async ({ page }) => {
    await test.step('提供性能优化建议', async () => {
      console.log('\n🚀 性能优化建议总结:')
      
      console.log('\n1. 📦 资源优化:')
      console.log('   - 实现代码分割和懒加载')
      console.log('   - 压缩和优化图片资源')
      console.log('   - 启用Gzip/Brotli压缩')
      console.log('   - 使用CDN加速静态资源')
      
      console.log('\n2. 🎯 渲染优化:')
      console.log('   - 实现虚拟滚动处理大列表')
      console.log('   - 优化组件重渲染')
      console.log('   - 使用Web Workers处理重计算')
      console.log('   - 实现骨架屏提升感知性能')
      
      console.log('\n3. 🌐 网络优化:')
      console.log('   - 实现API请求缓存')
      console.log('   - 使用HTTP/2多路复用')
      console.log('   - 优化API响应大小')
      console.log('   - 实现离线缓存策略')
      
      console.log('\n4. 💾 内存优化:')
      console.log('   - 及时清理事件监听器')
      console.log('   - 优化组件生命周期管理')
      console.log('   - 实现内存泄漏监控')
      console.log('   - 优化大数据结构处理')
    })
  })
})
