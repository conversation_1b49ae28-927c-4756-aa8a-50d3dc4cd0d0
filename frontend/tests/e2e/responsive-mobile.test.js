/**
 * Responsive and Mobile E2E Tests
 * 专门测试移动端和响应式设计的用户体验
 */

import { test, expect } from '@playwright/test'
import { API_ENDPOINTS, SELECTORS, TIMEOUTS } from '../fixtures/test-data.js'
import { AuthHelpers, NavigationHelpers, UIHelpers } from '../fixtures/test-helpers.js'

// 定义测试设备配置
const DEVICE_CONFIGS = {
  mobile: {
    name: 'iPhone 12',
    viewport: { width: 390, height: 844 },
    userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15'
  },
  tablet: {
    name: 'iPad',
    viewport: { width: 768, height: 1024 },
    userAgent: 'Mozilla/5.0 (iPad; CPU OS 14_0 like Mac OS X) AppleWebKit/605.1.15'
  },
  desktop: {
    name: 'Desktop',
    viewport: { width: 1280, height: 720 },
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
  },
  smallMobile: {
    name: 'iPhone SE',
    viewport: { width: 375, height: 667 },
    userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15'
  }
}

test.describe('Responsive and Mobile Tests', () => {
  
  Object.entries(DEVICE_CONFIGS).forEach(([deviceType, config]) => {
    test.describe(`${config.name} (${deviceType})`, () => {
      
      test.beforeEach(async ({ page }) => {
        // 设置设备视口
        await page.setViewportSize(config.viewport)

        // 设置用户代理 (通过 HTTP 头)
        await page.setExtraHTTPHeaders({
          'User-Agent': config.userAgent
        })

        console.log(`📱 设置设备: ${config.name} (${config.viewport.width}x${config.viewport.height})`)
      })

      test('should display responsive layout correctly', async ({ page }) => {
        await test.step(`测试${config.name}的响应式布局`, async () => {
          await page.goto(API_ENDPOINTS.FRONTEND_URL)
          await UIHelpers.waitForPageReady(page)

          // 检查基本布局元素
          const layoutElements = {
            header: page.locator('header, .v-app-bar, .app-header'),
            navigation: page.locator('nav, .v-navigation-drawer, .sidebar'),
            main: page.locator('main, .v-main, .main-content'),
            footer: page.locator('footer, .app-footer')
          }

          for (const [elementName, locator] of Object.entries(layoutElements)) {
            const isVisible = await locator.isVisible().catch(() => false)
            if (isVisible) {
              const boundingBox = await locator.boundingBox()
              console.log(`✅ ${elementName}: ${boundingBox.width}x${boundingBox.height}`)
              
              // 检查元素是否在视口内
              if (boundingBox.width > config.viewport.width) {
                console.log(`⚠️ ${elementName}宽度超出视口: ${boundingBox.width} > ${config.viewport.width}`)
              }
            } else {
              console.log(`ℹ️ ${elementName}: 不可见或不存在`)
            }
          }

          // 检查是否有水平滚动条
          const bodyWidth = await page.evaluate(() => document.body.scrollWidth)
          if (bodyWidth > config.viewport.width) {
            console.log(`⚠️ 页面出现水平滚动: ${bodyWidth} > ${config.viewport.width}`)
          } else {
            console.log(`✅ 页面宽度适配正确: ${bodyWidth} <= ${config.viewport.width}`)
          }
        })
      })

      test('should handle mobile navigation properly', async ({ page }) => {
        await test.step(`测试${config.name}的移动端导航`, async () => {
          await AuthHelpers.loginViaUI(page)

          if (deviceType === 'mobile' || deviceType === 'smallMobile') {
            // 移动端导航测试
            const mobileMenuSelectors = [
              '.v-app-bar__nav-icon',
              '.mobile-menu-btn',
              '.hamburger-menu',
              '[data-testid="mobile-menu"]',
              'button[aria-label="menu"]'
            ]

            let menuFound = false
            for (const selector of mobileMenuSelectors) {
              const menuButton = page.locator(selector)
              if (await menuButton.isVisible()) {
                console.log(`✅ 找到移动端菜单按钮: ${selector}`)
                
                // 点击菜单按钮
                await menuButton.click()
                await page.waitForTimeout(1000)
                
                // 检查导航菜单是否打开
                const navigationMenu = page.locator('.v-navigation-drawer, .mobile-menu, .sidebar-menu')
                const isMenuOpen = await navigationMenu.isVisible()
                
                if (isMenuOpen) {
                  console.log('✅ 移动端导航菜单成功打开')
                  
                  // 测试菜单项点击
                  const menuItems = navigationMenu.locator('a, button').filter({ hasText: /仪表盘|订单|配置/ })
                  const itemCount = await menuItems.count()
                  
                  if (itemCount > 0) {
                    console.log(`✅ 找到 ${itemCount} 个导航菜单项`)
                    
                    // 点击第一个菜单项
                    await menuItems.first().click()
                    await page.waitForTimeout(1000)
                    
                    // 检查菜单是否自动关闭
                    const isMenuClosed = !await navigationMenu.isVisible()
                    if (isMenuClosed) {
                      console.log('✅ 菜单项点击后自动关闭')
                    }
                  }
                } else {
                  console.log('⚠️ 移动端导航菜单未打开')
                }
                
                menuFound = true
                break
              }
            }

            if (!menuFound) {
              console.log('⚠️ 未找到移动端菜单按钮')
            }
          } else {
            // 桌面端/平板端导航测试
            const desktopNavigation = page.locator('.v-navigation-drawer, .sidebar, nav')
            const isNavVisible = await desktopNavigation.isVisible()
            
            if (isNavVisible) {
              console.log('✅ 桌面端导航栏正常显示')
              
              // 测试导航链接
              const navLinks = desktopNavigation.locator('a, button').filter({ hasText: /仪表盘|订单|配置/ })
              const linkCount = await navLinks.count()
              console.log(`✅ 找到 ${linkCount} 个导航链接`)
            } else {
              console.log('⚠️ 桌面端导航栏不可见')
            }
          }
        })
      })

      test('should handle touch interactions on mobile', async ({ page }) => {
        if (deviceType !== 'mobile' && deviceType !== 'smallMobile') {
          test.skip('跳过非移动端设备的触摸测试')
        }

        await test.step(`测试${config.name}的触摸交互`, async () => {
          await AuthHelpers.loginViaUI(page)
          await NavigationHelpers.goToDashboard(page)

          // 测试滑动操作
          const scrollableElements = page.locator('.v-data-table, .scroll-container, .overflow-auto')
          const scrollableCount = await scrollableElements.count()

          if (scrollableCount > 0) {
            console.log(`✅ 找到 ${scrollableCount} 个可滚动元素`)
            
            const firstScrollable = scrollableElements.first()
            const boundingBox = await firstScrollable.boundingBox()
            
            if (boundingBox) {
              // 模拟垂直滑动
              await page.touchscreen.tap(boundingBox.x + boundingBox.width / 2, boundingBox.y + boundingBox.height / 2)
              await page.touchscreen.tap(boundingBox.x + boundingBox.width / 2, boundingBox.y + 50)
              
              console.log('✅ 执行了触摸滑动操作')
            }
          }

          // 测试按钮点击区域
          const buttons = page.locator('button:visible')
          const buttonCount = await buttons.count()

          if (buttonCount > 0) {
            const firstButton = buttons.first()
            const buttonBox = await firstButton.boundingBox()
            
            if (buttonBox) {
              // 检查按钮是否足够大（至少44px，符合移动端可用性标准）
              const minTouchTarget = 44
              if (buttonBox.width < minTouchTarget || buttonBox.height < minTouchTarget) {
                console.log(`⚠️ 按钮触摸目标过小: ${buttonBox.width}x${buttonBox.height} (建议至少 ${minTouchTarget}x${minTouchTarget})`)
              } else {
                console.log(`✅ 按钮触摸目标大小合适: ${buttonBox.width}x${buttonBox.height}`)
              }
            }
          }
        })
      })

      test('should display data tables responsively', async ({ page }) => {
        await test.step(`测试${config.name}的数据表格响应式显示`, async () => {
          await AuthHelpers.loginViaUI(page)
          await page.goto(API_ENDPOINTS.FRONTEND_URL + '/orders')
          await UIHelpers.waitForPageReady(page)

          const dataTables = page.locator('.v-data-table, .data-table, table')
          const tableCount = await dataTables.count()

          if (tableCount > 0) {
            console.log(`✅ 找到 ${tableCount} 个数据表格`)
            
            const firstTable = dataTables.first()
            const tableBox = await firstTable.boundingBox()
            
            if (tableBox) {
              console.log(`表格尺寸: ${tableBox.width}x${tableBox.height}`)
              
              if (deviceType === 'mobile' || deviceType === 'smallMobile') {
                // 移动端表格检查
                if (tableBox.width > config.viewport.width) {
                  console.log('⚠️ 表格宽度超出移动端视口，检查是否有水平滚动')
                  
                  // 检查是否有水平滚动容器
                  const scrollContainer = page.locator('.table-container, .overflow-x-auto, .v-data-table__wrapper')
                  const hasScrollContainer = await scrollContainer.count() > 0
                  
                  if (hasScrollContainer) {
                    console.log('✅ 发现表格滚动容器')
                  } else {
                    console.log('⚠️ 建议添加表格水平滚动容器')
                  }
                }

                // 检查是否有移动端优化（如卡片视图）
                const mobileOptimized = page.locator('.mobile-card, .card-view, .list-view')
                const hasMobileView = await mobileOptimized.count() > 0
                
                if (hasMobileView) {
                  console.log('✅ 发现移动端优化视图')
                } else {
                  console.log('💡 建议为移动端添加卡片视图或列表视图')
                }
              }

              // 检查表格列的可见性
              const tableHeaders = firstTable.locator('th')
              const headerCount = await tableHeaders.count()
              console.log(`表格列数: ${headerCount}`)

              if (deviceType === 'mobile' && headerCount > 4) {
                console.log('💡 移动端建议隐藏部分非关键列或使用响应式列显示')
              }
            }
          } else {
            console.log('ℹ️ 当前页面没有数据表格')
          }
        })
      })

      test('should handle form inputs on different screen sizes', async ({ page }) => {
        await test.step(`测试${config.name}的表单输入体验`, async () => {
          await page.goto(API_ENDPOINTS.FRONTEND_URL)
          await UIHelpers.waitForPageReady(page)

          // 测试登录表单
          const formInputs = page.locator('input, textarea, select')
          const inputCount = await formInputs.count()

          if (inputCount > 0) {
            console.log(`✅ 找到 ${inputCount} 个表单输入元素`)

            for (let i = 0; i < Math.min(inputCount, 3); i++) {
              const input = formInputs.nth(i)
              const inputBox = await input.boundingBox()
              
              if (inputBox) {
                console.log(`输入框 ${i + 1} 尺寸: ${inputBox.width}x${inputBox.height}`)
                
                // 检查输入框高度是否适合触摸
                const minTouchHeight = 44
                if (deviceType === 'mobile' && inputBox.height < minTouchHeight) {
                  console.log(`⚠️ 移动端输入框高度过小: ${inputBox.height}px (建议至少 ${minTouchHeight}px)`)
                }

                // 测试输入框聚焦
                await input.click()
                await page.waitForTimeout(500)
                
                const isFocused = await input.evaluate(el => el === document.activeElement)
                if (isFocused) {
                  console.log(`✅ 输入框 ${i + 1} 聚焦成功`)
                  
                  // 在移动端检查虚拟键盘是否影响布局
                  if (deviceType === 'mobile') {
                    const viewportHeight = await page.evaluate(() => window.visualViewport?.height || window.innerHeight)
                    if (viewportHeight < config.viewport.height * 0.7) {
                      console.log('✅ 检测到虚拟键盘，视口高度调整正常')
                    }
                  }
                } else {
                  console.log(`⚠️ 输入框 ${i + 1} 聚焦失败`)
                }
              }
            }
          }
        })
      })

      test('should maintain performance on different devices', async ({ page }) => {
        await test.step(`测试${config.name}的性能表现`, async () => {
          const startTime = Date.now()
          
          await page.goto(API_ENDPOINTS.FRONTEND_URL)
          await UIHelpers.waitForPageReady(page)
          
          const loadTime = Date.now() - startTime
          console.log(`页面加载时间: ${loadTime}ms`)

          // 设备性能基准
          const performanceBenchmarks = {
            mobile: 5000,      // 移动端5秒
            smallMobile: 6000, // 小屏移动端6秒
            tablet: 4000,      // 平板4秒
            desktop: 3000      // 桌面3秒
          }

          const benchmark = performanceBenchmarks[deviceType]
          if (loadTime <= benchmark) {
            console.log(`✅ 页面加载性能良好 (${loadTime}ms <= ${benchmark}ms)`)
          } else {
            console.log(`⚠️ 页面加载较慢 (${loadTime}ms > ${benchmark}ms)`)
          }

          // 测试滚动性能
          const scrollStartTime = Date.now()
          await page.evaluate(() => {
            window.scrollTo(0, document.body.scrollHeight)
          })
          await page.waitForTimeout(100)
          const scrollTime = Date.now() - scrollStartTime
          
          if (scrollTime < 100) {
            console.log(`✅ 滚动性能良好 (${scrollTime}ms)`)
          } else {
            console.log(`⚠️ 滚动可能存在性能问题 (${scrollTime}ms)`)
          }
        })
      })
    })
  })

  test('should provide responsive design recommendations', async ({ page }) => {
    await test.step('提供响应式设计改进建议', async () => {
      console.log('\n📱 响应式设计改进建议:')
      
      console.log('\n1. 🎯 移动端优化:')
      console.log('   - 确保所有触摸目标至少44x44px')
      console.log('   - 优化表格在小屏幕上的显示（卡片视图/列表视图）')
      console.log('   - 添加移动端专用的导航模式')
      
      console.log('\n2. 📊 数据展示优化:')
      console.log('   - 实现响应式表格列隐藏/显示')
      console.log('   - 添加数据的移动端友好展示方式')
      console.log('   - 优化图表在小屏幕上的可读性')
      
      console.log('\n3. 🚀 性能优化:')
      console.log('   - 针对移动端进行资源优化')
      console.log('   - 实现懒加载和虚拟滚动')
      console.log('   - 优化图片和字体加载')
      
      console.log('\n4. 💡 用户体验提升:')
      console.log('   - 添加触摸手势支持')
      console.log('   - 优化表单输入体验')
      console.log('   - 实现更好的加载状态指示')
    })
  })
})
