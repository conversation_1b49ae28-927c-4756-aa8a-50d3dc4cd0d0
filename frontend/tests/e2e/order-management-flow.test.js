/**
 * Order Management Flow E2E Tests
 * 测试订单管理的完整流程，包括查看、筛选、操作等功能
 */

import { test, expect } from '@playwright/test'
import { API_ENDPOINTS, TIMEOUTS } from '../fixtures/test-data.js'
import { UIHelpers, NavigationHelpers, AuthHelpers } from '../fixtures/test-helpers.js'

test.describe('Order Management Flow Tests', () => {
  test.beforeEach(async ({ page }) => {
    test.setTimeout(60000)
    await page.goto(API_ENDPOINTS.FRONTEND_URL)
    await UIHelpers.waitForPageReady(page)
  })

  test('should navigate to orders page and display order interface', async ({ page }) => {
    await test.step('导航到订单页面', async () => {
      // 尝试多种方式导航到订单页面
      const orderNavSelectors = [
        'a:has-text("订单管理")',
        'a:has-text("订单")',
        'a[href*="orders"]',
        '.nav-orders',
        '[data-testid="nav-orders"]'
      ]
      
      let navigated = false
      for (const selector of orderNavSelectors) {
        const navElement = page.locator(selector)
        if (await navElement.count() > 0 && await navElement.first().isVisible()) {
          await navElement.first().click()
          await page.waitForTimeout(TIMEOUTS.SHORT)
          navigated = true
          console.log('通过导航元素跳转到订单页面:', selector)
          break
        }
      }
      
      if (!navigated) {
        // 直接导航到订单页面
        await page.goto(API_ENDPOINTS.FRONTEND_URL + '/orders')
        await page.waitForTimeout(TIMEOUTS.SHORT)
        console.log('直接导航到订单页面')
      }
    })

    await test.step('验证订单页面界面', async () => {
      await UIHelpers.waitForPageReady(page)
      
      // 检查页面标题或标识
      const pageTitleSelectors = [
        'h1:has-text("订单管理")',
        'h1:has-text("订单")',
        '.page-title:has-text("订单")',
        '[data-testid="orders-title"]'
      ]
      
      for (const selector of pageTitleSelectors) {
        const titleElement = page.locator(selector)
        if (await titleElement.count() > 0) {
          await expect(titleElement.first()).toBeVisible()
          console.log('找到订单页面标题:', selector)
          break
        }
      }
      
      // 检查订单列表容器
      const orderListSelectors = [
        '.v-data-table',
        '.orders-table',
        '.order-list',
        '.v-card',
        '[data-testid="orders-table"]'
      ]
      
      for (const selector of orderListSelectors) {
        const listElement = page.locator(selector)
        if (await listElement.count() > 0) {
          await expect(listElement.first()).toBeVisible()
          console.log('找到订单列表容器:', selector)
          break
        }
      }
    })
  })

  test('should display order statistics and controls', async ({ page }) => {
    // 导航到订单页面
    await page.goto(API_ENDPOINTS.FRONTEND_URL + '/orders')
    await UIHelpers.waitForPageReady(page)

    await test.step('验证统计卡片', async () => {
      // 查找统计卡片
      const statsSelectors = [
        '.stats-card',
        '.v-card:has-text("总订单")',
        '.v-card:has-text("活跃订单")',
        '.order-stats',
        '[data-testid="order-stats"]'
      ]
      
      for (const selector of statsSelectors) {
        const statsElement = page.locator(selector)
        if (await statsElement.count() > 0) {
          await expect(statsElement.first()).toBeVisible()
          console.log('找到统计卡片:', selector)
          break
        }
      }
    })

    await test.step('验证操作按钮', async () => {
      // 查找操作按钮
      const actionButtonSelectors = [
        'button:has-text("刷新")',
        'button:has-text("创建订单")',
        'button:has-text("一键清仓")',
        '.refresh-btn',
        '.create-order-btn',
        '[data-testid="refresh-button"]'
      ]
      
      for (const selector of actionButtonSelectors) {
        const buttonElement = page.locator(selector)
        if (await buttonElement.count() > 0 && await buttonElement.first().isVisible()) {
          console.log('找到操作按钮:', selector)
        }
      }
    })
  })

  test('should handle order filtering and search', async ({ page }) => {
    await page.goto(API_ENDPOINTS.FRONTEND_URL + '/orders')
    await UIHelpers.waitForPageReady(page)

    await test.step('测试状态筛选', async () => {
      // 查找状态筛选器
      const filterSelectors = [
        '.status-filter',
        '.v-select:has-text("状态")',
        '.filter-dropdown',
        '[data-testid="status-filter"]'
      ]
      
      for (const selector of filterSelectors) {
        const filterElement = page.locator(selector)
        if (await filterElement.count() > 0 && await filterElement.first().isVisible()) {
          await filterElement.first().click()
          await page.waitForTimeout(TIMEOUTS.SHORT)
          console.log('找到状态筛选器:', selector)
          
          // 尝试选择筛选选项
          const filterOptions = page.locator('.v-list-item, .v-menu .v-list-item')
          if (await filterOptions.count() > 0) {
            await filterOptions.first().click()
            await page.waitForTimeout(TIMEOUTS.SHORT)
            console.log('应用了筛选条件')
          }
          break
        }
      }
    })

    await test.step('测试搜索功能', async () => {
      // 查找搜索框
      const searchSelectors = [
        'input[placeholder*="搜索"]',
        'input[placeholder*="交易对"]',
        '.search-input',
        '[data-testid="search-input"]'
      ]
      
      for (const selector of searchSelectors) {
        const searchElement = page.locator(selector)
        if (await searchElement.count() > 0 && await searchElement.first().isVisible()) {
          await searchElement.first().fill('BTC')
          await page.waitForTimeout(TIMEOUTS.SHORT)
          console.log('执行了搜索操作:', selector)
          
          // 清空搜索
          await searchElement.first().fill('')
          await page.waitForTimeout(TIMEOUTS.SHORT)
          break
        }
      }
    })
  })

  test('should handle order list interactions', async ({ page }) => {
    await page.goto(API_ENDPOINTS.FRONTEND_URL + '/orders')
    await UIHelpers.waitForPageReady(page)

    await test.step('测试订单列表显示', async () => {
      // 检查是否有订单数据
      const orderRowSelectors = [
        '.v-data-table tbody tr',
        '.order-row',
        '.order-item',
        '[data-testid="order-row"]'
      ]
      
      let hasOrders = false
      for (const selector of orderRowSelectors) {
        const orderRows = page.locator(selector)
        const rowCount = await orderRows.count()
        if (rowCount > 0) {
          console.log('找到订单行数:', rowCount)
          hasOrders = true
          
          // 测试点击订单行
          await orderRows.first().click()
          await page.waitForTimeout(TIMEOUTS.SHORT)
          console.log('点击了第一个订单行')
          break
        }
      }
      
      if (!hasOrders) {
        // 检查空状态
        const emptyStateSelectors = [
          'text=暂无订单',
          'text=暂无数据',
          '.empty-state',
          '[data-testid="empty-orders"]'
        ]
        
        for (const selector of emptyStateSelectors) {
          const emptyElement = page.locator(selector)
          if (await emptyElement.count() > 0) {
            await expect(emptyElement.first()).toBeVisible()
            console.log('显示空状态:', selector)
            break
          }
        }
      }
    })

    await test.step('测试订单操作按钮', async () => {
      // 查找订单操作按钮
      const actionSelectors = [
        'button:has-text("取消")',
        'button:has-text("详情")',
        'button:has-text("编辑")',
        '.order-action-btn',
        '[data-testid="order-action"]'
      ]
      
      for (const selector of actionSelectors) {
        const actionButtons = page.locator(selector)
        if (await actionButtons.count() > 0) {
          const visibleButtons = await actionButtons.filter({ hasText: /.+/ }).count()
          if (visibleButtons > 0) {
            console.log('找到订单操作按钮:', selector, '数量:', visibleButtons)
          }
        }
      }
    })
  })

  test('should handle order creation dialog', async ({ page }) => {
    await page.goto(API_ENDPOINTS.FRONTEND_URL + '/orders')
    await UIHelpers.waitForPageReady(page)

    await test.step('打开创建订单对话框', async () => {
      // 查找创建订单按钮
      const createButtonSelectors = [
        'button:has-text("创建订单")',
        'button:has-text("新建订单")',
        'button:has-text("下单")',
        '.create-order-btn',
        '[data-testid="create-order"]'
      ]
      
      for (const selector of createButtonSelectors) {
        const createButton = page.locator(selector)
        if (await createButton.count() > 0 && await createButton.first().isVisible()) {
          await createButton.first().click()
          await page.waitForTimeout(TIMEOUTS.SHORT)
          console.log('点击了创建订单按钮:', selector)
          break
        }
      }
    })

    await test.step('验证订单创建表单', async () => {
      // 查找订单创建对话框
      const dialogSelectors = [
        '.v-dialog',
        '.create-order-dialog',
        '.order-form',
        '[data-testid="order-dialog"]'
      ]
      
      for (const selector of dialogSelectors) {
        const dialogElement = page.locator(selector)
        if (await dialogElement.count() > 0 && await dialogElement.first().isVisible()) {
          await expect(dialogElement.first()).toBeVisible()
          console.log('订单创建对话框已打开:', selector)
          
          // 查找表单字段
          const formFieldSelectors = [
            'input[placeholder*="交易对"]',
            'input[placeholder*="数量"]',
            'input[placeholder*="价格"]',
            '.v-select',
            '.v-text-field'
          ]
          
          for (const fieldSelector of formFieldSelectors) {
            const formFields = page.locator(fieldSelector)
            if (await formFields.count() > 0) {
              console.log('找到表单字段:', fieldSelector)
            }
          }
          
          // 关闭对话框
          const closeSelectors = [
            'button:has-text("取消")',
            'button:has-text("关闭")',
            '.v-dialog .v-btn--icon',
            '[data-testid="close-dialog"]'
          ]
          
          for (const closeSelector of closeSelectors) {
            const closeButton = page.locator(closeSelector)
            if (await closeButton.count() > 0 && await closeButton.first().isVisible()) {
              await closeButton.first().click()
              await page.waitForTimeout(TIMEOUTS.SHORT)
              console.log('关闭了对话框')
              break
            }
          }
          break
        }
      }
    })
  })

  test('should handle refresh and data loading', async ({ page }) => {
    await page.goto(API_ENDPOINTS.FRONTEND_URL + '/orders')
    await UIHelpers.waitForPageReady(page)

    await test.step('测试数据刷新', async () => {
      // 查找刷新按钮
      const refreshSelectors = [
        'button:has-text("刷新")',
        '.refresh-btn',
        '.v-btn--icon[title*="刷新"]',
        '[data-testid="refresh-button"]'
      ]
      
      for (const selector of refreshSelectors) {
        const refreshButton = page.locator(selector)
        if (await refreshButton.count() > 0 && await refreshButton.first().isVisible()) {
          await refreshButton.first().click()
          await page.waitForTimeout(TIMEOUTS.SHORT)
          console.log('执行了数据刷新:', selector)
          
          // 检查加载状态
          const loadingSelectors = [
            '.v-progress-circular',
            '.v-progress-linear',
            '.loading',
            '[data-testid="loading"]'
          ]
          
          for (const loadingSelector of loadingSelectors) {
            const loadingElement = page.locator(loadingSelector)
            if (await loadingElement.count() > 0) {
              console.log('显示了加载状态:', loadingSelector)
              break
            }
          }
          break
        }
      }
    })
  })

  test('should support responsive order management', async ({ page }) => {
    await page.goto(API_ENDPOINTS.FRONTEND_URL + '/orders')
    await UIHelpers.waitForPageReady(page)

    await test.step('测试移动端订单管理', async () => {
      // 切换到移动端视口
      await page.setViewportSize({ width: 375, height: 667 })
      await page.waitForTimeout(TIMEOUTS.SHORT)
      
      // 验证移动端布局
      const mobileElements = [
        '.v-data-table',
        '.order-list',
        '.mobile-order-card'
      ]
      
      for (const selector of mobileElements) {
        const element = page.locator(selector)
        if (await element.count() > 0) {
          await expect(element.first()).toBeVisible()
          console.log('移动端订单界面正常显示:', selector)
          break
        }
      }
      
      // 恢复桌面端视口
      await page.setViewportSize({ width: 1280, height: 720 })
    })
  })

  test('should handle individual order close operations', async ({ page }) => {
    await test.step('测试单个订单平仓功能', async () => {
      await NavigationHelpers.goToOrders(page)

      const orderRows = page.locator('tbody tr, .order-row')
      const rowCount = await orderRows.count()

      if (rowCount > 0) {
        const firstOrder = orderRows.first()

        // 查找单个订单的平仓按钮 (data-testid="close-order-{id}")
        const closeOrderButton = firstOrder.locator('[data-testid^="close-order-"], button:has-text("平仓"), .close-order-btn')

        if (await closeOrderButton.count() > 0) {
          console.log('✓ 找到单个订单平仓按钮')

          // 点击平仓按钮
          await closeOrderButton.first().click()

          // 应该显示确认对话框
          const confirmDialog = page.locator('.v-dialog, .confirmation-dialog, .close-order-dialog')
          const dialogVisible = await confirmDialog.first().isVisible({ timeout: 3000 }).catch(() => false)

          if (dialogVisible) {
            console.log('✓ 平仓确认对话框已显示')

            const dialogContent = confirmDialog.first()

            // 检查确认信息
            const confirmationTexts = [
              'text=确认平仓',
              'text=关闭订单',
              'text=平仓操作',
              'text=确认'
            ]

            let confirmationFound = false
            for (const text of confirmationTexts) {
              if (await dialogContent.locator(text).count() > 0) {
                console.log('✓ 找到确认信息:', text)
                confirmationFound = true
                break
              }
            }

            // 为了安全，我们只测试取消操作
            const cancelButton = dialogContent.locator('button:has-text("取消"), button:has-text("否")')
            if (await cancelButton.count() > 0) {
              await cancelButton.first().click()
              console.log('✓ 安全取消平仓操作')

              // 验证对话框已关闭
              await expect(confirmDialog.first()).not.toBeVisible()
            } else {
              await page.keyboard.press('Escape')
              console.log('✓ 通过ESC键关闭确认对话框')
            }

            expect(confirmationFound).toBeTruthy()
          } else {
            console.log('⚠ 平仓确认对话框未显示')
            // 检查是否直接执行了操作
            await page.waitForTimeout(TIMEOUTS.SHORT)
            const feedback = page.locator('.v-alert, .notification, .snackbar')
            if (await feedback.count() > 0) {
              console.log('✓ 找到操作反馈')
            }
          }
        } else {
          console.log('⚠ 未找到单个订单平仓按钮')
        }
      } else {
        console.log('ℹ 没有订单数据可供测试平仓功能')
        test.skip()
      }
    })
  })

  test('should handle order chart functionality', async ({ page }) => {
    await test.step('测试订单图表功能', async () => {
      await NavigationHelpers.goToOrders(page)

      const orderRows = page.locator('tbody tr, .order-row')
      const rowCount = await orderRows.count()

      if (rowCount > 0) {
        const firstOrder = orderRows.first()

        // 查找图表按钮
        const chartButton = firstOrder.locator('button:has-text("图表"), .chart-button, [data-testid^="chart-"], .view-chart')

        if (await chartButton.count() > 0) {
          console.log('✓ 找到订单图表按钮')

          // 点击图表按钮
          await chartButton.first().click()

          // 检查图表是否打开
          const chartModal = page.locator('.v-dialog, .chart-modal, .chart-dialog')
          const chartVisible = await chartModal.first().isVisible({ timeout: 5000 }).catch(() => false)

          if (chartVisible) {
            console.log('✓ 图表对话框已打开')

            // 检查图表内容
            const chartContent = chartModal.first()
            const chartElements = [
              '.chart-container',
              '.trading-chart',
              'canvas',
              '.chart-wrapper',
              '[data-testid="chart"]'
            ]

            let chartFound = false
            for (const element of chartElements) {
              if (await chartContent.locator(element).count() > 0) {
                console.log('✓ 找到图表元素:', element)
                chartFound = true
                break
              }
            }

            // 关闭图表
            const closeButton = chartContent.locator('button:has-text("关闭"), .close-button, .v-dialog__close')
            if (await closeButton.count() > 0) {
              await closeButton.first().click()
              console.log('✓ 已关闭图表对话框')
            } else {
              await page.keyboard.press('Escape')
              console.log('✓ 通过ESC键关闭图表')
            }

            expect(chartFound).toBeTruthy()
          } else {
            console.log('⚠ 图表对话框未打开，可能在新窗口或标签页中打开')
            // 检查是否有新窗口打开
            const pages = page.context().pages()
            if (pages.length > 1) {
              console.log('✓ 检测到新窗口，图表可能在新窗口中打开')
              // 关闭新窗口
              const newPage = pages[pages.length - 1]
              await newPage.close()
            }
          }
        } else {
          console.log('⚠ 未找到订单图表按钮')
        }
      } else {
        console.log('ℹ 没有订单数据可供测试图表功能')
        test.skip()
      }
    })
  })

  test('should handle advanced order filtering', async ({ page }) => {
    await test.step('测试高级订单筛选功能', async () => {
      await NavigationHelpers.goToOrders(page)

      // 测试方向筛选下拉框
      const directionFilter = page.locator('select[name="direction"], .direction-filter, .side-filter')
      if (await directionFilter.count() > 0) {
        await directionFilter.first().selectOption('buy')
        await page.waitForTimeout(TIMEOUTS.SHORT)
        console.log('✓ 已应用方向筛选')

        // 验证筛选结果
        const orderRows = page.locator('tbody tr, .order-row')
        const visibleRows = await orderRows.count()
        if (visibleRows > 0) {
          console.log(`✓ 筛选后显示 ${visibleRows} 个订单`)
        }
      }

      // 测试日期范围选择器
      const dateFromInput = page.locator('input[name="date_from"], .date-from, [data-testid="date-from"]')
      if (await dateFromInput.count() > 0) {
        const today = new Date()
        const yesterday = new Date(today)
        yesterday.setDate(yesterday.getDate() - 1)
        const dateString = yesterday.toISOString().split('T')[0]

        await dateFromInput.first().fill(dateString)
        await page.waitForTimeout(TIMEOUTS.SHORT)
        console.log('✓ 已设置开始日期筛选')
      }

      const dateToInput = page.locator('input[name="date_to"], .date-to, [data-testid="date-to"]')
      if (await dateToInput.count() > 0) {
        const today = new Date().toISOString().split('T')[0]
        await dateToInput.first().fill(today)
        await page.waitForTimeout(TIMEOUTS.SHORT)
        console.log('✓ 已设置结束日期筛选')
      }

      // 测试清除筛选按钮
      const clearFiltersButton = page.locator('button:has-text("清除筛选"), button:has-text("重置筛选"), .clear-filters')
      if (await clearFiltersButton.count() > 0) {
        await clearFiltersButton.first().click()
        await page.waitForTimeout(TIMEOUTS.SHORT)
        console.log('✓ 已清除所有筛选')

        // 验证筛选已清除
        const allOrderRows = page.locator('tbody tr, .order-row')
        const totalRows = await allOrderRows.count()
        console.log(`✓ 清除筛选后显示 ${totalRows} 个订单`)
      }
    })
  })
})
