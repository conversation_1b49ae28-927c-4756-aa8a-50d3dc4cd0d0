/**
 * 端到端交易流程测试
 * 测试完整的用户交易场景，从登录到订单执行
 */

import { test, expect } from '@playwright/test'
import { createTestDataFactory } from '../api-unified/test-data-factory.js'

test.describe('完整交易流程测试', () => {
  let testDataFactory

  test.beforeEach(async ({ request }) => {
    testDataFactory = createTestDataFactory(request)
  })

  test.afterEach(async () => {
    if (testDataFactory) {
      await testDataFactory.cleanup()
    }
  })

  test('用户注册、登录和基本交易流程', async ({ page, request }) => {
    // 创建测试场景
    const scenario = await testDataFactory.createTestScenario('trading_flow', request)
    
    // 1. 访问登录页面
    await page.goto('/login')
    await expect(page).toHaveTitle(/Crypto Trader/)

    // 2. 用户登录
    await page.fill('[data-testid="username-input"]', scenario.user.username)
    await page.fill('[data-testid="password-input"]', scenario.user.password)
    await page.click('[data-testid="login-button"]')

    // 3. 验证登录成功，跳转到仪表盘
    await expect(page).toHaveURL('/dashboard')
    await expect(page.locator('[data-testid="user-welcome"]')).toContainText(scenario.user.username)

    // 4. 检查仪表盘组件加载
    await expect(page.locator('[data-testid="stats-cards"]')).toBeVisible()
    await expect(page.locator('[data-testid="live-log-stream"]')).toBeVisible()
    await expect(page.locator('[data-testid="pending-actions-list"]')).toBeVisible()

    // 5. 导航到订单页面
    await page.click('[data-testid="nav-orders"]')
    await expect(page).toHaveURL('/orders')

    // 6. 验证订单列表显示
    await expect(page.locator('[data-testid="orders-table"]')).toBeVisible()
    
    // 检查是否显示了测试场景中创建的订单
    for (const order of scenario.orders) {
      await expect(page.locator(`[data-testid="order-${order.symbol}"]`)).toBeVisible()
    }

    // 7. 测试创建新订单
    await page.click('[data-testid="create-order-button"]')
    await expect(page.locator('[data-testid="order-form-dialog"]')).toBeVisible()

    // 填写订单表单
    await page.selectOption('[data-testid="symbol-select"]', 'BTC/USDT')
    await page.selectOption('[data-testid="side-select"]', 'BUY')
    await page.fill('[data-testid="quantity-input"]', '0.001')
    await page.selectOption('[data-testid="order-type-select"]', 'MARKET')

    // 提交订单
    await page.click('[data-testid="submit-order-button"]')

    // 8. 验证订单创建成功
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible()
    await expect(page.locator('[data-testid="success-message"]')).toContainText('订单创建成功')

    // 9. 检查订单列表更新
    await page.waitForTimeout(1000) // 等待列表刷新
    const orderRows = page.locator('[data-testid="order-row"]')
    await expect(orderRows).toHaveCount(scenario.orders.length + 1)
  })

  test('WebSocket实时通信测试', async ({ page, request }) => {
    const scenario = await testDataFactory.createTestScenario('default', request)
    
    // 登录
    await page.goto('/login')
    await UIHelpers.fillVuetifyInput(page, '[data-testid="username-input"]', scenario.user.username)
    await UIHelpers.fillVuetifyInput(page, '[data-testid="password-input"]', scenario.user.password)
    await UIHelpers.clickWithFallback(page, '[data-testid="login-button"]')
    
    await expect(page).toHaveURL('/dashboard')

    // 检查WebSocket连接状态
    await expect(page.locator('[data-testid="ws-status"]')).toContainText('已连接')

    // 模拟订单状态更新
    await page.evaluate(() => {
      // 模拟WebSocket消息
      window.mockWebSocketMessage({
        event_type: 'ORDER_UPDATE',
        payload: {
          id: 'order_123',
          status: 'FILLED',
          filled_quantity: 0.001
        }
      })
    })

    // 验证实时日志更新
    await expect(page.locator('[data-testid="log-item"]').first()).toContainText('订单状态更新')

    // 验证统计数据更新
    const statsCards = page.locator('[data-testid="stats-card"]')
    await expect(statsCards).toHaveCount(4)
  })

  test('Agent交互流程测试', async ({ page, request }) => {
    const scenario = await testDataFactory.createTestScenario('default', request)
    
    // 登录
    await page.goto('/login')
    await page.fill('[data-testid="username-input"]', scenario.user.username)
    await page.fill('[data-testid="password-input"]', scenario.user.password)
    await page.click('[data-testid="login-button"]')
    
    await expect(page).toHaveURL('/dashboard')

    // 模拟Agent需要用户确认的动作
    await page.evaluate(() => {
      window.mockWebSocketMessage({
        event_type: 'PENDING_ACTION',
        payload: {
          id: 'action_123',
          type: 'ORDER_CONFIRMATION',
          message: '确认执行BTC买入订单？',
          data: {
            symbol: 'BTC/USDT',
            side: 'BUY',
            quantity: 0.001,
            estimated_price: 50000
          }
        }
      })
    })

    // 验证待处理动作显示
    await expect(page.locator('[data-testid="pending-action-item"]')).toBeVisible()
    await expect(page.locator('[data-testid="action-message"]')).toContainText('确认执行BTC买入订单？')

    // 点击确认按钮
    await page.click('[data-testid="confirm-action-button"]')

    // 验证确认对话框
    await expect(page.locator('[data-testid="confirmation-dialog"]')).toBeVisible()
    await page.click('[data-testid="final-confirm-button"]')

    // 验证动作被确认
    await expect(page.locator('[data-testid="success-message"]')).toContainText('动作已确认')
  })

  test('错误处理和恢复测试', async ({ page, request }) => {
    const scenario = await testDataFactory.createTestScenario('default', request)
    
    // 登录
    await page.goto('/login')
    await page.fill('[data-testid="username-input"]', scenario.user.username)
    await page.fill('[data-testid="password-input"]', scenario.user.password)
    await page.click('[data-testid="login-button"]')
    
    await expect(page).toHaveURL('/dashboard')

    // 模拟网络错误
    await page.route('**/api/v1/orders', route => {
      route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({ error: 'Internal Server Error' })
      })
    })

    // 尝试创建订单
    await page.click('[data-testid="nav-orders"]')
    await page.click('[data-testid="create-order-button"]')
    
    await page.selectOption('[data-testid="symbol-select"]', 'BTC/USDT')
    await page.selectOption('[data-testid="side-select"]', 'BUY')
    await page.fill('[data-testid="quantity-input"]', '0.001')
    await page.click('[data-testid="submit-order-button"]')

    // 验证错误消息显示
    await expect(page.locator('[data-testid="error-message"]')).toBeVisible()
    await expect(page.locator('[data-testid="error-message"]')).toContainText('订单创建失败')

    // 恢复网络连接
    await page.unroute('**/api/v1/orders')

    // 重试订单创建
    await page.click('[data-testid="retry-button"]')
    
    // 验证重试成功
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible()
  })

  test('配置管理流程测试', async ({ page, request }) => {
    const scenario = await testDataFactory.createTestScenario('default', request)
    
    // 登录
    await page.goto('/login')
    await page.fill('[data-testid="username-input"]', scenario.user.username)
    await page.fill('[data-testid="password-input"]', scenario.user.password)
    await page.click('[data-testid="login-button"]')
    
    await expect(page).toHaveURL('/dashboard')

    // 导航到配置页面
    await page.click('[data-testid="nav-configs"]')
    await expect(page).toHaveURL('/configs')

    // 测试交易所配置
    await page.click('[data-testid="exchange-config-tab"]')
    
    // 填写交易所配置
    await page.selectOption('[data-testid="exchange-select"]', 'binance')
    await page.fill('[data-testid="api-key-input"]', 'test_api_key')
    await page.fill('[data-testid="api-secret-input"]', 'test_api_secret')
    await page.check('[data-testid="testnet-checkbox"]')

    // 保存配置
    await page.click('[data-testid="save-config-button"]')

    // 验证配置保存成功
    await expect(page.locator('[data-testid="success-message"]')).toContainText('配置保存成功')

    // 测试风控配置
    await page.click('[data-testid="risk-config-tab"]')
    
    await page.fill('[data-testid="max-position-input"]', '1000')
    await page.fill('[data-testid="max-daily-loss-input"]', '100')
    await page.check('[data-testid="enable-stop-loss-checkbox"]')

    await page.click('[data-testid="save-risk-config-button"]')
    await expect(page.locator('[data-testid="success-message"]')).toContainText('风控配置保存成功')
  })

  test('响应式设计测试', async ({ page, request }) => {
    const scenario = await testDataFactory.createTestScenario('default', request)
    
    // 测试桌面端
    await page.setViewportSize({ width: 1920, height: 1080 })
    await page.goto('/login')
    await page.fill('[data-testid="username-input"]', scenario.user.username)
    await page.fill('[data-testid="password-input"]', scenario.user.password)
    await page.click('[data-testid="login-button"]')
    
    await expect(page).toHaveURL('/dashboard')
    
    // 验证桌面端布局
    await expect(page.locator('[data-testid="sidebar"]')).toBeVisible()
    await expect(page.locator('[data-testid="main-content"]')).toHaveCSS('margin-left', '256px')

    // 测试平板端
    await page.setViewportSize({ width: 768, height: 1024 })
    await page.waitForTimeout(500)
    
    // 验证平板端布局
    await expect(page.locator('[data-testid="mobile-menu-button"]')).toBeVisible()
    
    // 测试移动端
    await page.setViewportSize({ width: 375, height: 667 })
    await page.waitForTimeout(500)
    
    // 验证移动端布局
    await expect(page.locator('[data-testid="mobile-nav"]')).toBeVisible()
    
    // 测试移动端导航
    await page.click('[data-testid="mobile-menu-button"]')
    await expect(page.locator('[data-testid="mobile-nav-drawer"]')).toBeVisible()
    
    await page.click('[data-testid="mobile-nav-orders"]')
    await expect(page).toHaveURL('/orders')
  })

  test('性能和加载测试', async ({ page, request }) => {
    const scenario = await testDataFactory.createTestScenario('default', request)
    
    // 开始性能监控
    await page.goto('/login')
    
    const startTime = Date.now()
    
    // 登录
    await page.fill('[data-testid="username-input"]', scenario.user.username)
    await page.fill('[data-testid="password-input"]', scenario.user.password)
    await page.click('[data-testid="login-button"]')
    
    // 等待仪表盘完全加载
    await expect(page.locator('[data-testid="dashboard-container"]')).toBeVisible()
    await page.waitForLoadState('networkidle')
    
    const loadTime = Date.now() - startTime
    
    // 验证加载时间在合理范围内（5秒内）
    expect(loadTime).toBeLessThan(5000)
    
    // 验证关键元素都已加载
    await expect(page.locator('[data-testid="stats-cards"]')).toBeVisible()
    await expect(page.locator('[data-testid="live-log-stream"]')).toBeVisible()
    await expect(page.locator('[data-testid="pending-actions-list"]')).toBeVisible()
    
    // 测试页面切换性能
    const navigationStart = Date.now()
    await page.click('[data-testid="nav-orders"]')
    await expect(page.locator('[data-testid="orders-table"]')).toBeVisible()
    const navigationTime = Date.now() - navigationStart
    
    // 验证页面切换时间在合理范围内（2秒内）
    expect(navigationTime).toBeLessThan(2000)
  })
})
