# Crypto Trader Frontend

基于 Vue 3 + Vuetify 的加密货币交易系统前端应用。

## 🚀 技术栈

- **Vue 3** - 渐进式 JavaScript 框架
- **Vuetify 3** - Material Design 组件库
- **Pinia** - 状态管理
- **Vue Router** - 路由管理
- **Vite** - 构建工具

## 📁 项目结构

```
frontend/
├── src/
│   ├── api/                    # API 接口层
│   │   ├── client.js          # HTTP 客户端基础配置
│   │   ├── auth.js            # 认证相关 API
│   │   ├── orders.js          # 订单相关 API
│   │   ├── configs.js         # 配置相关 API
│   │   ├── agent.js           # Agent 相关 API
│   │   └── index.js           # API 统一导出
│   ├── assets/                 # 静态资源
│   │   ├── styles/            # 样式文件
│   │   │   ├── global.css     # 全局样式和CSS变量
│   │   │   ├── components.css # 组件样式库
│   │   │   └── index.css      # 样式统一入口
│   │   └── icons/             # SVG 图标
│   ├── components/             # 可复用组件
│   │   ├── common/            # 通用组件
│   │   ├── forms/             # 表单组件
│   │   └── layout/            # 布局组件
│   ├── composables/            # 组合式函数
│   │   ├── useAsyncData.js    # 异步数据处理
│   │   ├── useForm.js         # 表单处理
│   │   ├── useUtils.js        # 工具函数
│   │   └── index.js           # Composables 统一导出
│   ├── router/                 # 路由配置
│   │   └── index.js           # 路由定义
│   ├── stores/                 # Pinia 状态管理
│   │   ├── auth.js            # 认证状态
│   │   ├── order.js           # 订单状态
│   │   ├── config.js          # 配置状态
│   │   ├── agent.js           # Agent 状态
│   │   ├── websocket.js       # WebSocket 连接
│   │   └── ui.js              # UI 状态
│   ├── views/                  # 页面组件
│   │   ├── DashboardView.vue  # 仪表板
│   │   ├── OrdersView.vue     # 订单管理
│   │   ├── ConfigsView.vue    # 配置管理
│   │   ├── LoginView.vue      # 登录页面
│   │   └── NotFoundView.vue   # 404 页面
│   ├── App.vue                 # 根组件
│   └── main.js                 # 应用入口
├── public/                     # 公共静态文件
├── package.json               # 项目依赖
└── vite.config.js             # Vite 配置
```

## 🏗️ 架构设计

### 状态管理架构

采用 Pinia 进行状态管理，按功能模块划分：

- **auth.js** - 用户认证、权限管理
- **order.js** - 订单数据、交易状态
- **config.js** - 系统配置、交易所配置
- **agent.js** - AI Agent 状态、任务管理
- **websocket.js** - WebSocket 连接管理
- **ui.js** - UI 状态、通知、主题等

### API 接口层

统一的 HTTP 客户端配置，包含：

- 请求/响应拦截器
- 错误处理机制
- 认证 Token 管理
- 请求重试逻辑

### 组件化设计

- **原子组件** - 最小可复用单元（按钮、输入框等）
- **分子组件** - 组合原子组件（表单组、卡片等）
- **有机体组件** - 复杂业务组件（订单表格、配置面板等）
- **模板组件** - 页面布局模板
- **页面组件** - 完整的页面视图

### 样式系统

- **CSS 变量** - 主题色彩、间距、字体等设计令牌
- **工具类** - 常用的原子样式类
- **组件样式** - 可复用的组件样式库
- **响应式设计** - 移动端适配

## 🔧 开发指南

### 环境要求

- Node.js >= 16
- npm >= 8

### 安装依赖

```bash
npm install
```

### 开发服务器

```bash
npm run dev
```

### 构建生产版本

```bash
npm run build
```

### 代码检查

```bash
npm run lint
```

## 📋 功能特性

### 已实现功能

- ✅ 用户认证系统
- ✅ 响应式布局
- ✅ 主题切换（深色/浅色）
- ✅ WebSocket 实时连接
- ✅ 通知系统
- ✅ 路由守卫
- ✅ 状态持久化

### 核心页面

- **仪表板** - 交易概览、实时数据
- **订单管理** - 订单列表、交易历史
- **配置管理** - 交易所配置、风控设置
- **Agent 控制** - AI Agent 状态监控

### 组件库

- **表单组件** - 输入框、选择器、验证
- **数据展示** - 表格、图表、统计卡片
- **反馈组件** - 通知、对话框、加载状态
- **导航组件** - 菜单、面包屑、分页

## 🎨 设计系统

### 色彩规范

- **主色调** - #1976d2 (蓝色)
- **成功色** - #4caf50 (绿色)
- **警告色** - #ff9800 (橙色)
- **错误色** - #f44336 (红色)
- **信息色** - #2196f3 (浅蓝)

### 间距系统

- **xs** - 4px
- **sm** - 8px
- **md** - 16px
- **lg** - 24px
- **xl** - 32px

### 字体规范

- **主字体** - Roboto, Helvetica Neue, Arial
- **等宽字体** - Roboto Mono, Courier New

## 🔌 API 集成

### 认证流程

1. 用户登录获取 JWT Token
2. Token 存储在 localStorage
3. 请求拦截器自动添加 Authorization 头
4. Token 过期自动刷新

### WebSocket 连接

- 自动重连机制
- 心跳检测
- 消息分发到对应 Store
- 连接状态监控

### 错误处理

- 统一错误拦截
- 用户友好的错误提示
- 网络错误重试
- 业务错误处理

## 🧪 测试策略

### 单元测试

- 组件测试 (Vue Test Utils)
- Store 测试 (Pinia Testing)
- 工具函数测试

### 集成测试

- API 接口测试
- 路由测试
- 端到端测试

## 📦 部署

### 构建优化

- 代码分割
- 资源压缩
- Tree Shaking
- 缓存策略

### 环境配置

- 开发环境 - 热重载、调试工具
- 测试环境 - Mock 数据、测试配置
- 生产环境 - 性能优化、错误监控

## 🤝 贡献指南

### 代码规范

- ESLint + Prettier 代码格式化
- Vue 3 Composition API 优先
- TypeScript 类型检查（可选）
- Git Commit 规范

### 开发流程

1. 创建功能分支
2. 编写代码和测试
3. 提交 Pull Request
4. 代码审查
5. 合并到主分支

## 📄 许可证

MIT License

## 🔗 相关链接

- [Vue 3 文档](https://vuejs.org/)
- [Vuetify 3 文档](https://vuetifyjs.com/)
- [Pinia 文档](https://pinia.vuejs.org/)
- [Vite 文档](https://vitejs.dev/)

---

**注意**: 这是一个加密货币交易系统，请确保在生产环境中采取适当的安全措施。