import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { VitePWA } from 'vite-plugin-pwa'
import path from 'path'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    VitePWA({
      registerType: 'autoUpdate',
      workbox: {
        globPatterns: ['**/*.{js,css,html,ico,png,svg}'],
        runtimeCaching: [
          {
            urlPattern: /^https:\/\/api\./,
            handler: 'NetworkFirst',
            options: {
              cacheName: 'api-cache',
              expiration: {
                maxEntries: 100,
                maxAgeSeconds: 60 * 60 * 24 // 24 hours
              }
            }
          }
        ]
      },
      manifest: {
        name: 'Crypto Trader - AI Agent 驱动的加密货币智能跟单系统',
        short_name: 'Crypto Trader',
        description: 'AI-powered cryptocurrency trading platform',
        theme_color: '#1976d2',
        background_color: '#ffffff',
        display: 'standalone',
        orientation: 'portrait',
        scope: '/',
        start_url: '/',
        icons: [
        {
          src: '/icon.svg',
          sizes: '192x192',
          type: 'image/svg+xml',
          purpose: 'any maskable'
        },
        {
          src: '/icon.svg',
          sizes: '512x512',
          type: 'image/svg+xml',
          purpose: 'any maskable'
        }
      ]
      }
    })
  ],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src')
    }
  },
  server: {
    host: '0.0.0.0', // 允许外部访问（Docker环境需要）
    port: 5173,
    // 让HMR使用默认配置，避免端口冲突
    hmr: true,
    watch: {
      usePolling: process.env.VITE_USE_POLLING === 'true' || process.env.CHOKIDAR_USEPOLLING === 'true',
      interval: process.env.VITE_POLLING_INTERVAL ? parseInt(process.env.VITE_POLLING_INTERVAL) : 1000,
      // 忽略node_modules以提高性能
      ignored: ['**/node_modules/**', '**/dist/**']
    },
    proxy: {
      '/api': {
        // 在Docker环境中使用服务名称，否则使用localhost
        target: process.env.DOCKER_ENV === 'true' ? 'http://backend-dev:8000' : (process.env.VITE_API_BASE_URL || 'http://localhost:8000'),
        changeOrigin: true
      },
      '/ws': {
        // 在Docker环境中使用服务名称，否则使用localhost
        target: process.env.DOCKER_ENV === 'true' ? 'ws://backend-dev:8000' : (process.env.VITE_API_BASE_URL?.replace('http', 'ws') || 'ws://localhost:8000'),
        ws: true
      }
    }
  },
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          // 将Vue相关库分离到单独的chunk
          'vue-vendor': ['vue', 'vue-router', 'pinia'],
          // 将Vuetify分离到单独的chunk
          'vuetify-vendor': ['vuetify']
        }
      }
    },
    // 启用gzip压缩
    reportCompressedSize: true,
    // 设置chunk大小警告限制
    chunkSizeWarningLimit: 1000
  },
  // 优化依赖预构建
  optimizeDeps: {
    include: [
      'vue',
      'vue-router',
      'pinia',
      'vuetify'
    ]
  }
})
