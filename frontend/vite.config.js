import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { VitePWA } from 'vite-plugin-pwa'
import path from 'path'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    VitePWA({
      registerType: 'autoUpdate',
      workbox: {
        globPatterns: ['**/*.{js,css,html,ico,png,svg}'],
        runtimeCaching: [
          {
            urlPattern: /^https:\/\/api\./,
            handler: 'NetworkFirst',
            options: {
              cacheName: 'api-cache',
              expiration: {
                maxEntries: 100,
                maxAgeSeconds: 60 * 60 * 24 // 24 hours
              }
            }
          }
        ]
      },
      manifest: {
        name: 'Crypto Trader - AI Agent 驱动的加密货币智能跟单系统',
        short_name: 'Crypto Trader',
        description: 'AI-powered cryptocurrency trading platform',
        theme_color: '#1976d2',
        background_color: '#ffffff',
        display: 'standalone',
        orientation: 'portrait',
        scope: '/',
        start_url: '/',
        icons: [
        {
          src: '/icon.svg',
          sizes: '192x192',
          type: 'image/svg+xml',
          purpose: 'any maskable'
        },
        {
          src: '/icon.svg',
          sizes: '512x512',
          type: 'image/svg+xml',
          purpose: 'any maskable'
        }
      ]
      }
    })
  ],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src')
    }
  },
  server: {
    host: '0.0.0.0', // 允许外部访问（Docker环境需要）
    port: 5173,
    // Docker环境下的HMR配置
    hmr: {
      port: process.env.VITE_HMR_PORT ? parseInt(process.env.VITE_HMR_PORT) : 24678,
      host: process.env.VITE_HMR_HOST || '0.0.0.0',
      protocol: process.env.VITE_HMR_PROTOCOL || 'ws'
    },
    watch: {
      // 在Docker环境中使用轮询，否则使用原生文件监听
      usePolling: process.env.DOCKER_ENV === 'true' && (process.env.VITE_USE_POLLING === 'true' || process.env.CHOKIDAR_USEPOLLING === 'true'),
      interval: process.env.VITE_POLLING_INTERVAL ? parseInt(process.env.VITE_POLLING_INTERVAL) : 3000,
      // 扩展忽略列表以避免无限重启
      ignored: [
        '**/node_modules/**',
        '**/dist/**',
        '**/.git/**',
        '**/coverage/**',
        '**/test-results/**',
        '**/*.log',
        '**/temp/**',
        // 在Docker环境中忽略配置文件的变更检测
        ...(process.env.DOCKER_ENV === 'true' ? ['**/vite.config.js', '**/package*.json'] : [])
      ],
      // 添加文件监听选项以提高稳定性
      followSymlinks: false,
      disableGlobbing: false
    },
    proxy: {
      '/api': {
        target: process.env.VITE_API_BASE_URL || 'http://localhost:8000',
        changeOrigin: true
      },
      '/ws': {
        target: process.env.VITE_API_BASE_URL?.replace('http', 'ws') || 'ws://localhost:8000',
        ws: true
      }
    }
  },
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          // 将Vue相关库分离到单独的chunk
          'vue-vendor': ['vue', 'vue-router', 'pinia'],
          // 将Vuetify分离到单独的chunk
          'vuetify-vendor': ['vuetify']
        }
      }
    },
    // 启用gzip压缩
    reportCompressedSize: true,
    // 设置chunk大小警告限制
    chunkSizeWarningLimit: 1000
  },
  // 优化依赖预构建
  optimizeDeps: {
    include: [
      'vue',
      'vue-router',
      'pinia',
      'vuetify'
    ]
  }
})
