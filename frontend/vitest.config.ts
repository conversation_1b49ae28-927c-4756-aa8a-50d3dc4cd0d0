import { defineConfig } from 'vitest/config'
import vue from '@vitejs/plugin-vue'
import path from 'path'

export default defineConfig({
  plugins: [vue()],
  test: {
    globals: true,
    environment: 'jsdom',
    setupFiles: ['./tests/setup/vitest-setup.js'],
    include: [
      'tests/unit/**/*.{test,spec}.{js,ts,vue}',
      'tests/components/**/*.{test,spec}.{js,ts,vue}' // 包含组件测试
    ],
    exclude: [
      'node_modules/**/*',
      // 排除Playwright测试目录
      'tests/api-unified/**/*',
      'tests/e2e/**/*',
      'tests/fixtures/**/*',
      'tests/setup/**/*'
    ],
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      reportsDirectory: '../temp/frontend/vitest/coverage',
      exclude: [
        'node_modules/',
        'tests/e2e/',
        'tests/api-unified/',
        'tests/fixtures/',
        'tests/setup/',
        '**/*.d.ts'
      ]
    },
    // 统一输出目录
    outputFile: {
      json: '../temp/frontend/vitest/results.json',
      junit: '../temp/frontend/vitest/junit.xml'
    },
    testTimeout: 30000, // 30秒超时
    hookTimeout: 10000,
    // 确保在CI环境中不使用watch模式
    watch: false,
    // 处理静态资源
    server: {
      deps: {
        inline: ['vuetify']
      }
    }
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src')
    }
  }
})
