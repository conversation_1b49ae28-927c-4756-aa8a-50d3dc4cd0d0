<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 192 192" width="192" height="192">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1976d2;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1565c0;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="192" height="192" fill="url(#bgGradient)" rx="24"/>
  
  <!-- Main content -->
  <g fill="white">
    <!-- Robot head -->
    <rect x="64" y="48" width="64" height="48" rx="8" stroke="white" stroke-width="2"/>
    
    <!-- Eyes -->
    <circle cx="80" cy="68" r="6" fill="#4fc3f7"/>
    <circle cx="112" cy="68" r="6" fill="#4fc3f7"/>
    <circle cx="80" cy="68" r="3" fill="white"/>
    <circle cx="112" cy="68" r="3" fill="white"/>
    
    <!-- Mouth -->
    <rect x="88" y="80" width="16" height="4" rx="2" fill="#4fc3f7"/>
    
    <!-- Body -->
    <rect x="72" y="104" width="48" height="40" rx="6" stroke="white" stroke-width="2"/>
    
    <!-- Arms -->
    <rect x="48" y="112" width="16" height="24" rx="4" stroke="white" stroke-width="1"/>
    <rect x="128" y="112" width="16" height="24" rx="4" stroke="white" stroke-width="1"/>
    
    <!-- Crypto symbol -->
    <text x="96" y="130" text-anchor="middle" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="#ffd54f">₿</text>
    
    <!-- Trading chart lines -->
    <polyline points="76,120 84,116 92,122 100,118 108,124 116,120" 
              stroke="#4fc3f7" stroke-width="2" fill="none"/>
  </g>
</svg>
