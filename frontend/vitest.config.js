import { defineConfig } from 'vitest/config'
import vue from '@vitejs/plugin-vue'
import path from 'path'

export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src')
    }
  },
  test: {
    environment: 'jsdom',
    setupFiles: ['./tests/setup/vitest-setup.js'],
    globals: true,
    include: [
      'tests/unit/**/*.{test,spec}.{js,ts,vue}',
      'tests/components/**/*.{test,spec}.{js,ts,vue}',
      'tests/api-unified/**/*.{test,spec}.{js,ts,vue}'
    ],
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      exclude: [
        'node_modules/',
        'tests/',
        '**/*.d.ts',
        '**/*.config.js',
        '**/*.config.ts'
      ]
    }
  }
})
