{"name": "crypto-trader-frontend", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore", "test": "vitest", "test:run": "vitest run", "test:coverage": "vitest --coverage", "test:ui": "vitest --ui", "type-check": "vue-tsc --noEmit", "test:e2e": "npm run test:parallel:fast", "test:e2e:ui": "npm run test:parallel:headed", "test:e2e:debug": "npm run test:parallel:debug", "test:e2e:report": "npx playwright show-report ../temp/frontend/playwright/parallel-report", "test:all": "npm run test:run && npm run test:e2e", "test:ci": "npm run test:coverage && npm run test:parallel:standard", "test:quick": "npm run test:parallel:fast", "test:smoke": "npm run test:parallel:fast", "test:critical": "npm run test:parallel:standard", "test:clean": "rm -rf test-results coverage .nyc_output ../temp/frontend/playwright", "test:setup": "npm run install:playwright && npm run test:clean", "install:playwright": "playwright install", "services:start": "docker-compose -f ../docker-compose.test.yml up -d", "services:stop": "docker-compose -f ../docker-compose.test.yml down", "services:restart": "npm run services:stop && npm run services:start", "test:e2e:full": "npm run services:start && npm run test:parallel:full && npm run services:stop", "test:e2e:dev": "npm run test:parallel:fast", "test:e2e:ci": "CI=true npm run test:parallel:standard", "test:parallel": "node run-parallel-tests.js", "test:parallel:fast": "node run-parallel-tests.js fast", "test:parallel:standard": "node run-parallel-tests.js standard", "test:parallel:full": "node run-parallel-tests.js full", "test:parallel:shard": "node run-parallel-tests.js shard", "test:parallel:debug": "node run-parallel-tests.js standard --debug", "test:parallel:headed": "node run-parallel-tests.js fast --headed", "test:parallel:help": "node run-parallel-tests.js --help"}, "dependencies": {"axios": "^1.6.0", "decimal.js": "^10.6.0", "lodash-es": "^4.17.21", "pinia": "^2.1.6", "vue": "^3.3.4", "vue-router": "^4.2.5", "vuetify": "^3.3.15", "zod": "^4.0.10"}, "devDependencies": {"@playwright/test": "^1.54.1", "@types/decimal.js": "^0.0.32", "@types/node": "^24.0.14", "@vitejs/plugin-vue": "^4.4.0", "@vitest/coverage-v8": "^3.2.4", "@vitest/ui": "^3.2.4", "@vue/test-utils": "^2.4.6", "allure-playwright": "^2.15.1", "eslint": "^8.49.0", "eslint-plugin-vue": "^9.17.0", "jsdom": "^26.1.0", "playwright": "^1.54.1", "typescript": "^5.8.3", "vite": "^4.4.11", "vite-plugin-pwa": "^1.0.1", "vitest": "^3.2.4", "vue-tsc": "^3.0.3"}}