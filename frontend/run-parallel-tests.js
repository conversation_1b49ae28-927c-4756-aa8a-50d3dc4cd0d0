#!/usr/bin/env node
/**
 * 并行测试执行脚本
 * 提供多种并行执行策略和优化选项
 */

import { spawn } from 'child_process'
import { fileURLToPath } from 'url'
import { dirname, join } from 'path'
import fs from 'fs'
import os from 'os'

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

// 并行执行策略
const PARALLEL_STRATEGIES = {
  // 快速并行：只运行核心测试
  fast: {
    projects: ['api-fast', 'core-features'],
    workers: Math.min(os.cpus().length, 4),
    description: '快速并行执行核心测试（约5-10分钟）'
  },
  
  // 标准并行：运行大部分测试
  standard: {
    projects: ['api-fast', 'core-features', 'business-flows', 'system-tests', 'error-handling'],
    workers: Math.min(os.cpus().length, 6),
    description: '标准并行执行主要测试（约15-25分钟）'
  },

  // 完整并行：运行所有测试
  full: {
    projects: ['api-fast', 'core-features', 'business-flows', 'advanced-features', 'system-tests', 'error-handling', 'ui-ux-tests', 'test-management'],
    workers: Math.min(os.cpus().length, 8),
    description: '完整并行执行所有测试（约30-45分钟）'
  },
  
  // 分片执行：用于CI/CD
  shard: {
    projects: null, // 使用所有项目
    workers: 4,
    description: '分片并行执行（用于CI/CD管道）'
  }
}

class ParallelTestRunner {
  constructor() {
    this.configFile = join(__dirname, 'playwright-parallel.config.js')
    this.logFile = join(__dirname, '../temp/frontend/playwright/parallel-execution.log')
    this.startTime = Date.now()
  }

  async run(strategy = 'standard', options = {}) {
    console.log('🚀 启动并行测试执行器...')
    console.log(`📋 执行策略: ${strategy} - ${PARALLEL_STRATEGIES[strategy]?.description || '自定义策略'}`)
    
    // 确保日志目录存在
    this.ensureLogDirectory()
    
    // 检查环境
    await this.checkEnvironment()
    
    // 构建执行命令
    const command = this.buildCommand(strategy, options)
    
    console.log(`🔧 执行命令: ${command.join(' ')}`)
    console.log(`📊 Worker数量: ${this.getWorkerCount(strategy)}`)
    console.log(`💾 日志文件: ${this.logFile}`)
    console.log('')
    
    // 执行测试
    return this.executeTests(command)
  }

  ensureLogDirectory() {
    const logDir = dirname(this.logFile)
    if (!fs.existsSync(logDir)) {
      fs.mkdirSync(logDir, { recursive: true })
    }
  }

  async checkEnvironment() {
    console.log('🔍 检查执行环境...')
    
    // 检查配置文件
    if (!fs.existsSync(this.configFile)) {
      throw new Error(`配置文件不存在: ${this.configFile}`)
    }
    
    // 检查系统资源
    const totalMem = Math.floor(os.totalmem() / (1024 * 1024 * 1024))
    const freeMem = Math.floor(os.freemem() / (1024 * 1024 * 1024))
    const cpuCount = os.cpus().length
    
    console.log(`💻 系统资源:`)
    console.log(`   - CPU核心: ${cpuCount}`)
    console.log(`   - 总内存: ${totalMem}GB`)
    console.log(`   - 可用内存: ${freeMem}GB`)
    
    // 内存警告
    if (freeMem < 2) {
      console.warn('⚠️ 可用内存不足2GB，建议减少并行worker数量')
    }
    
    // 检查服务状态（可选）
    if (process.env.CHECK_SERVICES !== 'false') {
      await this.checkServices()
    }
    
    console.log('✅ 环境检查完成')
  }

  async checkServices() {
    console.log('🔍 检查服务状态...')
    
    try {
      // 这里可以添加服务检查逻辑
      // 例如：ping后端API、检查数据库连接等
      console.log('✅ 服务状态正常')
    } catch (error) {
      console.warn('⚠️ 服务检查失败:', error.message)
      console.warn('💡 提示: 使用 CHECK_SERVICES=false 跳过服务检查')
    }
  }

  buildCommand(strategy, options) {
    const command = ['npx', 'playwright', 'test']
    
    // 使用并行配置文件
    command.push('--config', this.configFile)
    
    // 设置worker数量
    const workers = options.workers || this.getWorkerCount(strategy)
    command.push('--workers', workers.toString())
    
    // 项目选择
    const strategyConfig = PARALLEL_STRATEGIES[strategy]
    if (strategyConfig?.projects) {
      strategyConfig.projects.forEach(project => {
        command.push('--project', project)
      })
    }
    
    // 分片支持
    if (strategy === 'shard' || options.shard) {
      const shardTotal = options.shardTotal || process.env.SHARD_TOTAL || 4
      const shardIndex = options.shardIndex || process.env.SHARD_INDEX || 1
      command.push('--shard', `${shardIndex}/${shardTotal}`)
    }
    
    // 报告器
    if (options.reporter) {
      command.push('--reporter', options.reporter)
    } else {
      command.push('--reporter', 'line,html,json')
    }
    
    // 其他选项
    if (options.headed) {
      command.push('--headed')
    }
    
    if (options.debug) {
      command.push('--debug')
    }
    
    if (options.grep) {
      command.push('--grep', options.grep)
    }
    
    if (options.timeout) {
      command.push('--timeout', options.timeout.toString())
    }
    
    // 环境变量
    if (options.maxFailures) {
      command.push('--max-failures', options.maxFailures.toString())
    }
    
    return command
  }

  getWorkerCount(strategy) {
    const strategyConfig = PARALLEL_STRATEGIES[strategy]
    return strategyConfig?.workers || Math.min(os.cpus().length, 4)
  }

  async executeTests(command) {
    return new Promise((resolve, reject) => {
      console.log('🎬 开始执行测试...')
      console.log('=' * 50)
      
      const process = spawn(command[0], command.slice(1), {
        stdio: ['inherit', 'pipe', 'pipe'],
        cwd: __dirname
      })
      
      // 创建日志写入流
      const logStream = fs.createWriteStream(this.logFile, { flags: 'w' })
      
      // 处理输出
      process.stdout.on('data', (data) => {
        const output = data.toString()
        console.log(output)
        logStream.write(`[STDOUT] ${output}`)
      })
      
      process.stderr.on('data', (data) => {
        const output = data.toString()
        console.error(output)
        logStream.write(`[STDERR] ${output}`)
      })
      
      process.on('close', (code) => {
        logStream.end()
        
        const duration = Date.now() - this.startTime
        const durationMin = Math.round(duration / 60000)
        
        console.log('=' * 50)
        console.log(`🏁 测试执行完成`)
        console.log(`⏱️ 总耗时: ${durationMin}分钟`)
        console.log(`📊 退出码: ${code}`)
        console.log(`📝 详细日志: ${this.logFile}`)
        
        if (code === 0) {
          console.log('✅ 所有测试通过！')
          resolve({ success: true, duration, code })
        } else {
          console.log('❌ 部分测试失败')
          resolve({ success: false, duration, code })
        }
      })
      
      process.on('error', (error) => {
        logStream.end()
        console.error('❌ 测试执行出错:', error.message)
        reject(error)
      })
    })
  }
}

// CLI接口
async function main() {
  const args = process.argv.slice(2)
  const strategy = args[0] || 'standard'
  
  // 解析选项
  const options = {}
  for (let i = 1; i < args.length; i += 2) {
    const key = args[i]?.replace('--', '')
    const value = args[i + 1]
    
    if (key && value) {
      // 转换数字类型
      if (['workers', 'timeout', 'maxFailures', 'shardTotal', 'shardIndex'].includes(key)) {
        options[key] = parseInt(value)
      } else if (['headed', 'debug'].includes(key)) {
        options[key] = true
        i-- // 布尔选项不需要值
      } else {
        options[key] = value
      }
    }
  }
  
  // 显示帮助
  if (args.includes('--help') || args.includes('-h')) {
    showHelp()
    return
  }
  
  // 显示策略列表
  if (args.includes('--list-strategies')) {
    showStrategies()
    return
  }
  
  try {
    const runner = new ParallelTestRunner()
    const result = await runner.run(strategy, options)
    
    process.exit(result.success ? 0 : 1)
  } catch (error) {
    console.error('❌ 执行失败:', error.message)
    process.exit(1)
  }
}

function showHelp() {
  console.log(`
🧪 并行测试执行器

用法:
  node run-parallel-tests.js [策略] [选项]

策略:
  fast      - 快速并行执行核心测试
  standard  - 标准并行执行主要测试 (默认)
  full      - 完整并行执行所有测试
  shard     - 分片并行执行

选项:
  --workers <数量>        Worker数量
  --reporter <类型>       报告器类型
  --grep <模式>          测试过滤模式
  --timeout <毫秒>       测试超时时间
  --max-failures <数量>   最大失败数
  --headed               显示浏览器界面
  --debug                调试模式
  --shard-total <数量>    分片总数
  --shard-index <索引>    分片索引

示例:
  node run-parallel-tests.js fast
  node run-parallel-tests.js standard --workers 6
  node run-parallel-tests.js full --grep "auth"
  node run-parallel-tests.js shard --shard-total 4 --shard-index 1

环境变量:
  CHECK_SERVICES=false   跳过服务检查
  SHARD_TOTAL=4         分片总数
  SHARD_INDEX=1         分片索引
`)
}

function showStrategies() {
  console.log('📋 可用的并行执行策略:')
  console.log('')
  
  Object.entries(PARALLEL_STRATEGIES).forEach(([name, config]) => {
    console.log(`${name}:`)
    console.log(`  描述: ${config.description}`)
    console.log(`  Worker数量: ${config.workers}`)
    if (config.projects) {
      console.log(`  项目: ${config.projects.join(', ')}`)
    }
    console.log('')
  })
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error)
}

export { ParallelTestRunner, PARALLEL_STRATEGIES }