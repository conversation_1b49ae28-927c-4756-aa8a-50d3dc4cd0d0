# 应用基础配置
APP_NAME=AI Crypto Trading Agent
APP_VERSION=1.0.0
DEBUG=false
LOG_LEVEL=INFO

# 日志配置
LOG_TO_FILE=true
LOG_FILE_PATH=logs
LOG_FILE_MAX_SIZE=10
LOG_FILE_BACKUP_COUNT=5
LOG_RETENTION_DAYS=30

# 数据库配置
DATABASE_URL=postgresql+asyncpg://postgres:password@localhost:5432/crypto_trader
DB_POOL_SIZE=10
DB_MAX_OVERFLOW=20
DB_POOL_TIMEOUT=30
DB_POOL_RECYCLE=1800
DB_ECHO=false

# 安全配置
APP_SECRET_KEY=your-very-secret-key-here-min-32-chars-long
JWT_SECRET_KEY=your-jwt-secret-key-here-min-32-chars-long
JWT_ALGORITHM=HS256
JWT_EXPIRE_MINUTES=60
PASSWORD_HASH_ROUNDS=12

# API配置
API_HOST=0.0.0.0
API_PORT=8000
API_PREFIX=/api/v1
API_URL=http://localhost:8000
CORS_ORIGINS=http://localhost:3000,http://localhost:8080

# LLM配置
OPENAI_API_KEY=sk-your-key-here
ANTHROPIC_API_KEY=your-anthropic-key-here
DEFAULT_LLM_PROVIDER=openai
DEFAULT_LLM_MODEL=gpt-4
LLM_MAX_RETRIES=3
LLM_REQUEST_TIMEOUT=60
LLM_MAX_TOKENS=4096

# Discord配置
DISCORD_TOKEN=your-discord-token

# Discord过滤配置 - 简化的统一配置格式
DISCORD_FILTER_CONFIG={"enabled":false,"server_ids":[],"channel_ids":[],"author_ids":[],"allowed_message_types":["text"]}

# 默认用户ID - 用于存储所有捕获的Discord消息
DEFAULT_USER_ID=708db973-fc1f-4be0-9c52-a9736a10372c

# Discord 代理设置（可选）- 支持 http/https/socks5 代理
# Discord Proxy Settings (Optional) - Supports http/https/socks5 proxies
# DISCORD_PROXY=http://127.0.0.1:7890
# DISCORD_PROXY=https://127.0.0.1:7890
# DISCORD_PROXY=socks5://127.0.0.1:1080
# DISCORD_PROXY=***************************************

# Discord 其他配置
DISCORD_AUTO_START=true
DISCORD_RECONNECT_ATTEMPTS=5
DISCORD_RECONNECT_DELAY=30
DISCORD_MESSAGE_CACHE_SIZE=1000
DISCORD_DEDUPLICATION_WINDOW=5

# 交易配置
DEFAULT_EXCHANGE=binance
SIMULATION_MODE=true
PRICE_CHECK_INTERVAL=60

# ========================================
# 前端配置 (Frontend Configuration)
# ========================================

# 前端API配置 (Frontend API Configuration)
VITE_API_BASE_URL=http://localhost:8000
VITE_WS_URL=ws://localhost:8000

# 前端应用配置 (Frontend Application Configuration)
VITE_APP_TITLE=AI Crypto Trading Agent
VITE_APP_VERSION=0.1.0
VITE_DEBUG=false

# 前端测试配置 (Frontend Test Configuration - 仅测试环境需要)
# NODE_ENV=test
# FRONTEND_URL=http://localhost:5173
# TEST_USERNAME=demo
# TEST_PASSWORD=password123
# TEST_TIMEOUT=60000
# API_TIMEOUT=30000
# NAVIGATION_TIMEOUT=10000
# HEADLESS=true
# SLOW_MO=0
# USE_MOCK_DATA=false
# CLEANUP_TEST_DATA=true
# DEBUG_TESTS=false
# SAVE_SCREENSHOTS=true
# SAVE_VIDEOS=false
# SAVE_TRACES=false